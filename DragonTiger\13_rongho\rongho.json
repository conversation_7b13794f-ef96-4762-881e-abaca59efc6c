{"skins": {"default": {"Mat": {"Mat": {"scaleX": 0.991, "scaleY": 0.991, "rotation": 49.47, "x": 0.79, "width": 17, "y": 0.89, "height": 17}}, "longho2": {"longho": {"x": 4.28, "width": 171, "y": 10.54, "height": 71}}, "Rau2": {"Rau2": {"triangles": [8, 9, 14, 14, 9, 13, 9, 10, 13, 10, 11, 13, 13, 11, 12, 7, 8, 15, 8, 14, 15, 6, 16, 17, 6, 7, 16, 7, 15, 16, 5, 18, 4, 4, 18, 19, 5, 17, 18, 5, 6, 17, 2, 3, 20, 3, 19, 20, 3, 4, 19, 22, 2, 21, 2, 20, 21, 24, 1, 23, 24, 0, 1, 23, 2, 22, 23, 1, 2], "uvs": [0.43987, 0.92054, 0.59594, 0.90052, 0.76871, 0.8603, 0.74814, 0.77676, 0.60005, 0.66847, 0.40122, 0.57652, 0.23668, 0.49762, 0.08447, 0.39707, 0.00631, 0.28259, 0, 0.12943, 0.05156, 0.02424, 0.14206, 0, 0.24902, 0, 0.14618, 0.08766, 0.13383, 0.20214, 0.18731, 0.29496, 0.26547, 0.4048, 0.40945, 0.50071, 0.58222, 0.57807, 0.7899, 0.66827, 0.95033, 0.76109, 0.97913, 0.8864, 0.85983, 0.95602, 0.68706, 0.98696, 0.44436, 0.99779], "vertices": [1, 34, -2.28, 5.3, 1, 2, 34, 4.84, 3.89, 0.9991, 35, -2.75, 6.68, 0.0009, 3, 34, 13.78, 4.17, 0.01133, 35, 4.73, 1.78, 0.98561, 36, -3.1, 6.87, 0.00306, 2, 35, 13.2, 6.74, 0.03623, 36, 6.02, 3.23, 0.96377, 2, 36, 20.27, 3.28, 0.34298, 37, 1.06, 3.11, 0.65702, 1, 37, 14.92, 2.5, 1, 1, 38, 4.39, 2.46, 1, 2, 38, 17.9, 1.66, 0.87324, 39, -1.15, 1.42, 0.12676, 1, 39, 12.63, 0.28, 1, 1, 40, 11.95, 1.25, 1, 1, 40, 24.46, 1.85, 1, 1, 40, 28.12, -1.38, 1, 1, 40, 29.2, -5.97, 1, 1, 40, 18.18, -3.9, 1, 2, 39, 19.68, -8.11, 0.28581, 40, 5.01, -6.42, 0.71419, 3, 38, 25.45, -8.65, 0.00016, 39, 8.65, -6.77, 0.98578, 40, -5.03, -11.19, 0.01405, 2, 38, 12.8, -4.52, 0.95133, 39, -4.62, -5.81, 0.04867, 2, 37, 21.3, -3.66, 0.57943, 38, -0.06, -3.71, 0.42057, 1, 37, 9.49, -3.33, 1, 2, 36, 16.49, -4.17, 0.99663, 37, -4.47, -3.15, 0.00337, 1, 36, 3.61, -5.52, 1, 2, 34, 20.27, -3.1, 0.006, 35, 5.88, -7.9, 0.994, 2, 34, 11.65, -7.53, 0.6756, 35, -3.73, -6.59, 0.3244, 1, 34, 3.26, -6.87, 1, 1, 34, -6.62, -2.63, 1], "width": 44, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48], "type": "mesh", "hull": 25, "height": 117}}, "tranh": {"tranh": {"x": 1.47, "width": 100, "y": 11.67, "height": 55}}, "Duoi": {"Duoi": {"triangles": [39, 8, 7, 8, 39, 9, 39, 40, 30, 9, 39, 10, 39, 11, 10, 30, 37, 39, 11, 37, 12, 12, 37, 38, 37, 11, 39, 30, 51, 37, 37, 51, 38, 38, 13, 12, 38, 51, 14, 38, 14, 13, 14, 51, 15, 15, 51, 16, 7, 6, 40, 39, 7, 40, 40, 36, 30, 6, 5, 40, 40, 41, 35, 41, 29, 35, 40, 35, 36, 35, 29, 50, 36, 35, 50, 30, 36, 51, 51, 36, 50, 51, 50, 16, 50, 17, 16, 50, 18, 17, 5, 41, 40, 5, 4, 41, 4, 42, 41, 41, 33, 34, 33, 41, 42, 41, 34, 29, 42, 28, 33, 50, 29, 34, 49, 50, 34, 34, 33, 49, 33, 28, 49, 28, 48, 49, 48, 19, 49, 48, 20, 19, 49, 18, 50, 49, 19, 18, 2, 44, 3, 3, 44, 43, 3, 43, 4, 43, 42, 4, 27, 32, 43, 43, 32, 42, 46, 47, 32, 32, 28, 42, 32, 47, 28, 47, 48, 28, 23, 22, 47, 47, 22, 48, 22, 21, 48, 21, 20, 48, 0, 44, 1, 44, 2, 1, 26, 45, 0, 45, 31, 0, 0, 31, 44, 26, 25, 45, 45, 25, 31, 25, 27, 31, 44, 31, 43, 31, 27, 43, 25, 46, 27, 25, 24, 46, 27, 46, 32, 24, 23, 46, 46, 23, 47], "uvs": [0.15182, 1, 0.29874, 1, 0.29532, 0.86772, 0.31753, 0.76381, 0.38074, 0.7273, 0.46786, 0.75819, 0.58061, 0.83402, 0.71045, 0.8958, 0.82491, 0.89018, 0.93936, 0.8284, 1, 0.75258, 1, 0.59813, 0.97524, 0.40998, 0.94475, 0.32227, 0.91374, 0.23306, 0.80099, 0.1797, 0.72582, 0.10388, 0.62845, 0.00278, 0.4542, 0, 0.28507, 0, 0.16378, 0.02806, 0.07666, 0.13196, 0.03566, 0.29765, 0.0442, 0.46052, 0, 0.63183, 0, 0.83402, 0, 1, 0.1467, 0.6908, 0.29874, 0.46614, 0.53791, 0.47176, 0.74461, 0.46052, 0.1493, 0.84809, 0.21516, 0.58963, 0.36901, 0.46779, 0.45263, 0.46975, 0.59587, 0.46861, 0.69496, 0.46322, 0.8097, 0.41557, 0.86601, 0.37667, 0.84882, 0.65429, 0.66432, 0.65429, 0.48666, 0.59813, 0.32095, 0.59532, 0.24407, 0.6908, 0.21503, 0.86772, 0.08178, 0.8958, 0.08691, 0.64587, 0.13986, 0.46052, 0.21161, 0.28641, 0.38586, 0.21902, 0.60282, 0.26395, 0.74974, 0.28361], "vertices": [2, 2, -2.37, -0.95, 0.99738, 3, -30.61, -31.2, 0.00262, 3, 2, -2.37, -36.21, 0.70036, 3, -5.68, -56.14, 0.29629, 4, 3.13, -77.59, 0.00335, 3, 2, 16.94, -35.39, 0.46211, 3, 7.4, -41.9, 0.49384, 4, 1.97, -58.29, 0.04404, 4, 2, 32.11, -40.72, 0.10246, 3, 21.89, -34.94, 0.65848, 4, 7.03, -43.03, 0.2381, 5, -54.52, -40.91, 0.00096, 4, 2, 37.44, -55.89, 0.00454, 3, 36.39, -41.9, 0.33222, 4, 22.1, -37.43, 0.62828, 5, -39.24, -35.91, 0.03496, 4, 3, 47.98, -59.87, 0.06291, 4, 43.09, -41.57, 0.65565, 5, -18.44, -40.88, 0.28116, 6, -78.46, -5.68, 0.00028, 4, 3, 59.29, -86.84, 0.00148, 4, 70.34, -52.16, 0.22239, 5, 8.38, -52.53, 0.72369, 6, -59.92, -28.29, 0.05243, 3, 4, 101.66, -60.63, 0.02361, 5, 39.33, -62.23, 0.66636, 6, -36.81, -51.05, 0.31004, 3, 4, 129.11, -59.32, 0.00017, 5, 66.82, -62, 0.38294, 6, -12.26, -63.4, 0.61689, 2, 5, 94.47, -53.58, 0.12957, 6, 16.2, -68.54, 0.87043, 2, 5, 109.26, -42.83, 0.05872, 6, 34.26, -65.73, 0.94128, 2, 5, 109.75, -20.29, 0.01429, 6, 44.99, -45.89, 0.98571, 1, 6, 52.84, -18.9, 1, 1, 6, 52.49, -4.16, 1, 2, 5, 90.22, 33.45, 0.00301, 6, 52.15, 10.84, 0.99699, 3, 4, 121.53, 44.29, 0.0225, 5, 63.33, 41.83, 0.20496, 6, 32.05, 30.56, 0.77254, 3, 4, 103.3, 55.04, 0.12297, 5, 45.54, 53.29, 0.473, 6, 21.45, 48.89, 0.40402, 3, 4, 79.67, 69.38, 0.33897, 5, 22.49, 68.55, 0.50793, 6, 7.92, 72.99, 0.1531, 5, 2, 143.63, -73.52, 0.00082, 3, 123.94, 20.72, 0.02723, 4, 37.85, 69.05, 0.73092, 5, -19.31, 69.87, 0.22344, 6, -28.67, 93.24, 0.01759, 4, 2, 143.63, -32.93, 0.03638, 3, 95.24, 49.42, 0.25944, 4, -2.73, 68.33, 0.67524, 5, -59.89, 70.75, 0.02894, 4, 2, 139.53, -3.82, 0.10618, 3, 71.76, 67.11, 0.47839, 4, -31.77, 63.72, 0.41385, 5, -89.08, 67.29, 0.00158, 3, 2, 124.36, 17.09, 0.19074, 3, 46.25, 71.17, 0.58443, 4, -52.4, 48.18, 0.22483, 3, 2, 100.17, 26.93, 0.32501, 3, 22.18, 61.02, 0.59106, 4, -61.81, 23.82, 0.08393, 3, 2, 76.39, 24.88, 0.60381, 3, 6.82, 42.76, 0.38679, 4, -59.34, 0.08, 0.0094, 2, 2, 51.38, 35.49, 0.98233, 3, -18.37, 32.57, 0.01767, 1, 2, 21.86, 35.49, 1, 1, 2, -2.37, 35.49, 1, 2, 2, 42.77, 0.28, 0.87003, 3, 0.44, 1.59, 0.12997, 3, 2, 75.57, -36.21, 7e-05, 3, 49.43, -1.02, 0.18924, 4, 1.75, 0.34, 0.81069, 3, 4, 59.16, 0.54, 0.61679, 5, -0.72, 0.57, 0.38316, 6, -43.77, 23.1, 5e-05, 3, 4, 108.73, 3.06, 3e-05, 5, 48.91, 1.13, 0.41795, 6, 0.64, 0.94, 0.58201, 3, 2, 19.81, -0.34, 0.99529, 3, -15.36, -15.09, 0.00465, 4, -33.12, -56.05, 5e-05, 2, 2, 57.54, -16.15, 0.00251, 3, 22.5, 0.41, 0.99749, 2, 4, 18.62, 0.4, 0.99998, 5, -41.23, 2.03, 2e-05, 2, 4, 38.69, 0.47, 0.99983, 5, -21.18, 1.31, 0.00017, 3, 4, 73.06, 1.25, 0.00075, 5, 13.2, 0.73, 0.99898, 6, -31.32, 16.89, 0.00027, 3, 4, 96.82, 2.45, 0.00014, 5, 36.99, 1, 0.99451, 6, -10.03, 6.27, 0.00534, 1, 6, 17.5, -0.72, 1, 1, 6, 32.09, -2.15, 1, 2, 5, 73.3, -27.7, 0.17016, 6, 9.18, -35.84, 0.82984, 3, 4, 89.96, -25.57, 0.03726, 5, 29.03, -26.73, 0.8664, 6, -29.77, -14.77, 0.09634, 3, 3, 67.7, -46.54, 0.02246, 4, 47.19, -18.12, 0.79998, 5, -13.42, -17.61, 0.17756, 4, 2, 56.71, -41.54, 0.00028, 3, 39.87, -18.13, 0.51582, 4, 7.41, -18.42, 0.48375, 5, -53.17, -16.33, 0.00016, 3, 2, 42.77, -23.09, 0.03129, 3, 16.96, -14.94, 0.90284, 4, -10.79, -32.68, 0.06587, 3, 2, 16.94, -16.12, 0.67883, 3, -6.23, -28.27, 0.31154, 4, -17.3, -58.63, 0.00964, 1, 2, 12.84, 15.86, 1, 2, 2, 49.33, 14.63, 0.9647, 3, -5.07, 16.37, 0.0353, 3, 2, 76.39, 1.92, 0.35016, 3, 23.05, 26.52, 0.63697, 4, -36.39, 0.49, 0.01287, 4, 2, 101.81, -15.3, 0.10538, 3, 53.2, 32.32, 0.56536, 4, -19.62, 26.21, 0.32885, 5, -78.42, 29.33, 0.0004, 5, 2, 111.65, -57.12, 0.00389, 3, 89.73, 9.71, 0.0538, 4, 22.02, 36.79, 0.87128, 5, -36.4, 38.25, 0.07004, 6, -58.31, 72.92, 0.00099, 3, 4, 74.2, 31.15, 0.25365, 5, 15.52, 30.56, 0.65263, 6, -15.63, 42.38, 0.09372, 3, 4, 109.5, 28.91, 0.02363, 5, 50.7, 26.93, 0.33086, 6, 14.01, 23.07, 0.64551], "width": 240, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 2, 0, 0, 52, 24, 26, 26, 28, 48, 50, 50, 52, 0, 62, 62, 54, 54, 64, 64, 56, 56, 66, 58, 68, 68, 66, 58, 70, 60, 72, 72, 70, 74, 60, 26, 76, 76, 74], "type": "mesh", "hull": 27, "height": 146}}, "mongtay2": {"mongtay2": {"triangles": [8, 11, 10, 3, 6, 5, 3, 5, 4, 7, 6, 3, 9, 8, 10, 11, 18, 12, 7, 11, 8, 18, 11, 7, 18, 7, 3, 2, 18, 3, 13, 12, 18, 18, 15, 13, 15, 14, 13, 18, 17, 16, 1, 18, 2, 15, 18, 16, 1, 0, 17, 17, 18, 1], "uvs": [0.99535, 0.91417, 0.75617, 0.78441, 0.61701, 0.65084, 0.61701, 0.52872, 0.77791, 0.36844, 0.69094, 0.12801, 0.50394, 0.00589, 0.33869, 0.00589, 0.24301, 0.00589, 0.04296, 0.10893, 0, 0.28829, 0.00382, 0.49819, 0.0821, 0.66992, 0.16473, 0.74243, 0.16908, 0.89127, 0.36478, 0.95233, 0.4648, 1, 1, 1, 0.36913, 0.67374], "vertices": [1, 73, 28.73, 3.25, 1, 2, 73, 16.65, 3.78, 0.99975, 72, 32.07, 16.07, 0.00025, 2, 73, 8.23, 6.57, 0.73578, 72, 25.19, 10.47, 0.26422, 2, 73, 5.31, 11.8, 0.20036, 72, 19.22, 10.82, 0.79964, 2, 73, 7.52, 22.03, 0.00777, 72, 11.78, 18.18, 0.99223, 1, 72, -0.2, 15.12, 1, 1, 72, -6.64, 7.43, 1, 1, 72, -7.04, 0.34, 1, 1, 72, -7.28, -3.77, 1, 1, 72, -2.73, -12.65, 1, 1, 72, 5.94, -14.99, 1, 1, 72, 16.21, -15.42, 1, 2, 73, -11.39, -5.46, 0.06338, 72, 24.81, -12.54, 0.93662, 2, 73, -6.56, -6.83, 0.35303, 72, 28.56, -9.2, 0.64697, 2, 73, -2.83, -13.11, 0.79884, 72, 35.85, -9.43, 0.20116, 2, 73, 5.97, -11.61, 0.98732, 72, 39.32, -1.2, 0.01268, 1, 73, 10.86, -11.55, 1, 1, 73, 30.95, -0.33, 1, 2, 73, -0.53, 0.39, 0.02639, 72, 25.7, -0.23, 0.97361], "width": 43, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 36, 34, 12, 14, 14, 16, 36, 14], "type": "mesh", "hull": 18, "height": 49}}, "Rau1": {"Rau1": {"triangles": [17, 14, 13, 14, 17, 15, 17, 16, 15, 18, 12, 19, 17, 13, 18, 18, 13, 12, 19, 11, 20, 20, 11, 10, 19, 12, 11, 22, 21, 10, 21, 20, 10, 22, 9, 23, 8, 23, 9, 9, 22, 10, 25, 24, 7, 24, 8, 7, 24, 23, 8, 26, 6, 27, 26, 25, 6, 25, 7, 6, 27, 5, 28, 27, 6, 5, 28, 4, 29, 3, 29, 4, 4, 28, 5, 30, 3, 2, 30, 29, 3, 32, 0, 33, 31, 1, 32, 32, 1, 0, 31, 2, 1, 31, 30, 2], "uvs": [1, 0.93399, 0.95319, 0.92414, 0.87866, 0.91553, 0.81241, 0.8774, 0.7414, 0.77178, 0.67515, 0.6537, 0.61618, 0.55033, 0.54638, 0.41625, 0.49257, 0.31496, 0.42337, 0.22762, 0.35002, 0.17473, 0.25162, 0.12177, 0.17828, 0.08241, 0.10967, 0.02829, 0.06353, 0, 0, 0.01968, 0, 0.05904, 0.04756, 0.05904, 0.11972, 0.09348, 0.19188, 0.1513, 0.25458, 0.20542, 0.31491, 0.22756, 0.37202, 0.25864, 0.42998, 0.30292, 0.46843, 0.3595, 0.53354, 0.46175, 0.57613, 0.55401, 0.62699, 0.65611, 0.68749, 0.76402, 0.75314, 0.87227, 0.80992, 0.94484, 0.87686, 0.99588, 0.95612, 1, 1, 1], "vertices": [1, 23, -5.63, -3.99, 1, 1, 23, 5.42, -4.24, 1, 2, 23, 22.96, -3.85, 0.81896, 24, -1.57, -4.69, 0.18104, 1, 24, 14.37, -2.06, 1, 2, 25, 11.52, -3.05, 0.99996, 26, -12.65, -3.26, 4e-05, 1, 26, 7.85, -3.3, 1, 2, 26, 25.98, -3.18, 0.42127, 27, 0.6, -3.14, 0.57873, 2, 27, 22.91, -2.25, 0.2618, 28, 0.84, -2.36, 0.7382, 1, 28, 17.87, -3.35, 1, 1, 29, 12.07, -4.22, 1, 1, 30, 7.11, -4.15, 1, 1, 31, 8.78, -3.87, 1, 2, 31, 26.54, -2.73, 0.18031, 32, 1.74, -2.74, 0.81969, 2, 32, 18.96, -3.65, 0.87157, 33, -2.8, -3.32, 0.12843, 1, 33, 8.44, -4.51, 1, 1, 33, 22.73, 0.38, 1, 1, 33, 21.92, 4.75, 1, 1, 33, 10.93, 2.73, 1, 1, 32, 14.47, 2.65, 1, 1, 31, 21.08, 3.67, 1, 1, 31, 5.17, 4.9, 1, 1, 30, 13.73, 3.58, 1, 2, 29, 21.76, 3.78, 0.53311, 30, -0.14, 3.86, 0.46689, 2, 28, 30.18, 4.81, 0.00125, 29, 7.27, 2.97, 0.99875, 2, 28, 19.13, 4.13, 0.91893, 29, -3.56, 5.27, 0.08107, 2, 27, 21.4, 3.52, 0.59872, 28, -0.03, 3.54, 0.40128, 1, 27, 6.96, 3.81, 1, 1, 26, 16.28, 4.26, 1, 2, 25, 21.85, 4.34, 0.8814, 26, -2.45, 4.3, 0.1186, 2, 24, 27.25, 3.27, 0.15027, 25, 2.16, 3.94, 0.84973, 1, 24, 11.7, 5.1, 1, 2, 23, 22.67, 5.23, 0.96163, 24, -5, 3.73, 0.03837, 1, 23, 4.07, 4.25, 1, 1, 23, -6.21, 3.45, 1], "width": 235, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66], "type": "mesh", "hull": 34, "height": 113}}, "Toc2": {"Toc2": {"triangles": [44, 15, 14, 16, 15, 44, 17, 16, 44, 45, 44, 14, 43, 45, 14, 45, 17, 44, 18, 17, 45, 13, 43, 14, 18, 45, 43, 19, 18, 43, 12, 59, 13, 46, 43, 13, 46, 13, 59, 19, 43, 46, 58, 19, 46, 60, 59, 12, 21, 19, 58, 20, 19, 21, 42, 59, 60, 46, 59, 42, 60, 12, 11, 42, 58, 46, 57, 58, 42, 21, 58, 57, 22, 21, 57, 47, 42, 60, 57, 42, 47, 25, 23, 22, 24, 23, 25, 56, 57, 47, 22, 57, 56, 26, 25, 22, 11, 47, 60, 61, 47, 11, 10, 61, 11, 56, 26, 22, 41, 47, 61, 56, 47, 41, 8, 10, 9, 28, 26, 56, 27, 26, 28, 10, 8, 61, 55, 28, 56, 41, 55, 56, 48, 41, 62, 55, 41, 48, 61, 8, 41, 8, 62, 41, 62, 8, 7, 29, 28, 55, 40, 48, 62, 55, 54, 29, 40, 54, 55, 40, 55, 48, 62, 6, 40, 6, 62, 7, 54, 30, 29, 63, 40, 6, 32, 31, 30, 32, 30, 54, 49, 40, 63, 54, 40, 49, 63, 6, 5, 64, 63, 5, 39, 49, 63, 53, 54, 49, 32, 54, 53, 33, 32, 53, 64, 39, 63, 53, 49, 39, 34, 33, 53, 52, 53, 39, 35, 34, 53, 50, 64, 5, 50, 5, 4, 39, 64, 50, 65, 4, 3, 52, 39, 50, 51, 4, 65, 50, 4, 51, 37, 50, 51, 36, 52, 50, 36, 50, 37, 35, 53, 52, 35, 52, 36, 51, 2, 1, 2, 51, 3, 38, 51, 1, 37, 51, 38, 3, 51, 65, 38, 1, 0], "uvs": [1, 1, 0.98066, 0.96104, 0.96162, 0.92269, 0.86821, 0.85623, 0.7674, 0.81503, 0.65636, 0.75617, 0.57444, 0.64965, 0.55252, 0.56039, 0.53499, 0.4927, 0.56567, 0.43188, 0.53207, 0.4309, 0.53918, 0.31351, 0.4749, 0.24092, 0.33428, 0.12781, 0.19403, 0.04345, 0.08883, 0, 0.0041, 0, 0.07569, 0.09544, 0.1502, 0.15429, 0.23785, 0.23767, 0.16531, 0.24603, 0.25443, 0.30587, 0.30556, 0.3608, 0.22959, 0.34903, 0.16677, 0.36669, 0.25151, 0.41377, 0.28803, 0.4432, 0.26027, 0.4638, 0.3119, 0.50999, 0.30898, 0.60318, 0.3119, 0.65223, 0.22862, 0.65125, 0.29875, 0.72384, 0.29729, 0.79545, 0.32649, 0.8715, 0.42292, 0.93723, 0.5471, 0.93134, 0.69174, 0.92251, 0.84238, 0.94213, 0.52935, 0.82055, 0.43882, 0.62998, 0.42414, 0.47226, 0.41191, 0.30798, 0.23832, 0.15472, 0.10147, 0.06432, 0.17174, 0.11074, 0.30709, 0.21544, 0.41745, 0.38249, 0.43236, 0.56058, 0.48549, 0.72821, 0.64369, 0.85614, 0.77082, 0.89572, 0.51977, 0.8817, 0.42434, 0.80941, 0.37051, 0.6747, 0.37541, 0.54149, 0.35828, 0.42484, 0.33381, 0.33285, 0.29221, 0.25727, 0.37541, 0.21456, 0.44881, 0.29177, 0.48062, 0.42649, 0.48796, 0.5612, 0.51243, 0.70084, 0.60296, 0.80434, 0.80115, 0.8602], "vertices": [1, 44, -11.91, 6.71, 1, 1, 44, -6.1, 0.33, 1, 1, 44, -0.38, -5.94, 1, 1, 44, 17.32, -13.36, 1, 2, 44, 33.82, -15.51, 0.97901, 45, -8.51, -32, 0.02099, 2, 44, 53.14, -20.46, 0.48728, 45, 8.25, -21.2, 0.51272, 3, 44, 72.77, -36.23, 0.00355, 45, 33.16, -17.52, 0.77737, 46, -3.53, -19.29, 0.21908, 3, 45, 51.89, -20.67, 0.0686, 46, 15.33, -17.04, 0.93033, 47, -20.94, -17.3, 0.00107, 2, 46, 29.64, -15.21, 0.76507, 47, -6.65, -15.29, 0.23493, 2, 46, 42.21, -20.1, 0.30991, 47, 5.97, -20.03, 0.69009, 2, 46, 42.63, -15.38, 0.2326, 47, 6.33, -15.3, 0.7674, 2, 47, 30.94, -17.09, 0.85002, 48, -12.22, -12.28, 0.14998, 2, 47, 46.47, -8.53, 0.07899, 48, 5.4, -14.33, 0.92101, 2, 48, 36.31, -12.99, 0.68606, 49, -3.05, -12.7, 0.31394, 1, 49, 23.46, -11.24, 1, 1, 49, 40.4, -7.21, 1, 1, 49, 48.85, 1.24, 1, 1, 49, 27.54, 8.28, 1, 2, 48, 47.65, 11.01, 0.03433, 49, 11.37, 9.59, 0.96567, 3, 47, 48.23, 24.86, 0.00458, 48, 26.23, 11.82, 0.98111, 49, -9.75, 13.23, 0.01431, 1, 48, 31.04, 21.01, 1, 2, 47, 33.84, 22.98, 0.16644, 48, 13.43, 18.65, 0.83356, 3, 46, 58.76, 15.87, 0.00154, 47, 22.08, 16.15, 0.83101, 48, -0.12, 19.92, 0.16744, 2, 47, 24.89, 26.78, 0.98803, 48, 8.35, 26.94, 0.01197, 1, 47, 21.47, 35.75, 1, 3, 46, 47.99, 23.98, 0.04219, 47, 11.2, 24.13, 0.94838, 48, -4.33, 32.73, 0.00942, 3, 46, 41.58, 19.11, 0.2437, 47, 4.86, 19.18, 0.75512, 48, -12.37, 32.39, 0.00118, 2, 46, 37.44, 23.22, 0.39505, 47, 0.66, 23.23, 0.60495, 2, 46, 27.42, 16.38, 0.79869, 47, -9.27, 16.27, 0.20131, 2, 45, 54.52, 14.73, 0.13537, 46, 7.89, 17.67, 0.86463, 2, 45, 44.64, 17.68, 0.6799, 46, -2.42, 17.72, 0.3201, 2, 45, 48.64, 28.72, 0.84119, 46, -1.69, 29.44, 0.15881, 2, 45, 31.01, 24.31, 0.95826, 46, -17.36, 20.24, 0.04174, 2, 44, 96.08, 7.6, 0.0235, 45, 16.85, 29.37, 0.9765, 2, 44, 85.84, 20.53, 0.21504, 45, 0.41, 30.65, 0.78496, 2, 44, 67.82, 27.64, 0.63375, 45, -17.05, 22.26, 0.36625, 2, 44, 52.31, 19.41, 0.9595, 45, -21.56, 5.3, 0.0405, 1, 44, 34.42, 9.45, 1, 1, 44, 13.33, 4.61, 1, 2, 44, 64.03, -0.84, 0.24948, 45, 1.27, 0.13, 0.75052, 2, 45, 43.26, -0.76, 0.22019, 46, 1.45, -0.37, 0.77981, 2, 46, 34.63, 0.22, 0.87693, 47, -1.86, 0.19, 0.12307, 2, 47, 32.68, 0.8, 0.6715, 48, -0.4, 1.27, 0.3285, 2, 48, 40.03, 1.19, 0.06279, 49, 2.52, 0.87, 0.93721, 1, 49, 29.59, 1.09, 1, 1, 49, 15.69, 0.97, 1, 2, 47, 52.58, 14.95, 6e-05, 48, 24.01, 1.23, 0.99994, 2, 47, 17.02, 0.53, 0.99907, 48, -13.31, 10.15, 0.00093, 2, 45, 57.35, -4.62, 7e-05, 46, 16.05, -0.11, 0.99993, 2, 44, 77.55, -16.06, 0.00027, 45, 21.62, -0.3, 0.99973, 2, 44, 46.26, -0.54, 0.99821, 45, -11.03, -12.7, 0.00179, 1, 44, 26.51, -0.21, 1, 2, 44, 60.06, 11.44, 0.88482, 45, -10.44, 5.57, 0.11518, 2, 44, 78.52, 3.02, 0.05427, 45, 8.28, 13.38, 0.94573, 2, 45, 37.5, 11.39, 0.86073, 46, -7.5, 9.67, 0.13927, 2, 46, 20.41, 7.73, 0.99459, 47, -16.17, 7.53, 0.00541, 3, 46, 44.99, 9.05, 0.07444, 47, 8.39, 9.15, 0.92292, 48, -15.32, 22.18, 0.00264, 2, 47, 27.82, 11.98, 0.59909, 48, 2.13, 13.19, 0.40091, 2, 47, 43.87, 17.33, 0.02532, 48, 18.31, 8.22, 0.97468, 1, 48, 18.32, -6.54, 1, 2, 47, 35.91, -4.51, 0.42887, 48, -0.85, -4.93, 0.57113, 2, 46, 43.88, -8.17, 0.10074, 47, 7.49, -8.08, 0.89926, 2, 45, 54.68, -12, 0.02091, 46, 15.57, -7.94, 0.97909, 3, 44, 76.4, -22.86, 0.00579, 45, 25.82, -5.76, 0.98417, 46, -13.88, -10.07, 0.01003, 2, 44, 55.92, -8.16, 0.6661, 45, 1.12, -10.79, 0.3339, 2, 44, 25.62, -8.76, 0.99984, 45, -19.03, -33.43, 0.00016], "width": 141, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 0, 76, 0, 2, 2, 4, 32, 88, 86, 90, 90, 88, 84, 92, 92, 86, 82, 94, 94, 84, 80, 96, 96, 82, 78, 98, 98, 80, 100, 78, 2, 102, 102, 100], "type": "mesh", "hull": 39, "height": 210}}, "Toc1": {"Toc1": {"triangles": [8, 23, 7, 9, 10, 23, 9, 23, 8, 10, 11, 23, 33, 23, 11, 12, 33, 11, 24, 33, 12, 6, 7, 23, 6, 23, 33, 13, 24, 12, 34, 13, 14, 24, 6, 33, 43, 6, 24, 32, 24, 13, 32, 13, 34, 43, 24, 32, 32, 34, 25, 5, 43, 32, 6, 43, 5, 42, 32, 25, 5, 32, 42, 5, 42, 4, 25, 34, 14, 35, 25, 14, 15, 35, 14, 31, 25, 35, 4, 42, 25, 4, 25, 31, 36, 15, 16, 35, 15, 36, 31, 35, 36, 26, 31, 36, 41, 31, 26, 41, 4, 31, 3, 4, 41, 41, 26, 3, 30, 36, 16, 37, 30, 16, 26, 36, 30, 17, 37, 16, 38, 37, 17, 27, 30, 37, 18, 38, 17, 27, 37, 38, 40, 30, 27, 3, 26, 30, 40, 2, 30, 2, 3, 30, 29, 40, 27, 38, 18, 19, 39, 27, 38, 20, 39, 38, 29, 27, 39, 19, 20, 38, 29, 2, 40, 28, 29, 39, 28, 2, 29, 1, 2, 28, 21, 39, 20, 28, 39, 21, 0, 1, 28, 22, 28, 21, 0, 28, 22], "uvs": [0.63865, 1, 0.63865, 0.89151, 0.64182, 0.7839, 0.56254, 0.65542, 0.37863, 0.52051, 0.24545, 0.44021, 0.10276, 0.2796, 0.0245, 0.18342, 0.01499, 0.0469, 0.04036, 0, 0.1228, 0.00515, 0.16402, 0.09187, 0.27184, 0.14969, 0.32891, 0.22839, 0.44307, 0.29424, 0.60795, 0.38418, 0.73796, 0.49339, 0.88383, 0.60421, 0.99481, 0.72305, 1, 0.81621, 0.90561, 0.86645, 0.85289, 0.93543, 0.7211, 1, 0.12264, 0.18372, 0.23838, 0.29463, 0.41043, 0.42931, 0.64192, 0.57982, 0.79207, 0.73985, 0.70665, 0.88469, 0.75786, 0.79787, 0.70368, 0.64565, 0.5389, 0.51284, 0.32222, 0.36026, 0.17234, 0.23135, 0.4073, 0.32315, 0.54181, 0.4404, 0.68571, 0.5418, 0.82022, 0.6638, 0.88279, 0.74302, 0.82648, 0.84125, 0.6951, 0.74777, 0.56371, 0.58933, 0.35412, 0.44357, 0.22587, 0.34533], "vertices": [1, 43, -8.55, 3.5, 1, 1, 43, 15.81, 9.4, 1, 2, 43, 40.06, 14.9, 0.84147, 50, -2.12, 21.3, 0.15853, 3, 43, 66.71, 30.91, 0.00858, 50, 28.58, 16.32, 0.59786, 51, -7.17, 17.65, 0.39356, 2, 51, 30.62, 15.26, 0.70528, 52, -15.96, 13.52, 0.29472, 1, 52, 8.08, 16.49, 1, 2, 52, 48.29, 10.3, 0.39531, 53, 10.04, 12.24, 0.60469, 2, 53, 34.05, 11.09, 0.31662, 54, 2.25, 11.79, 0.68338, 1, 54, 33.19, 5.55, 1, 1, 54, 43.03, 0.14, 1, 1, 54, 39.64, -8.96, 1, 2, 53, 46.25, -12.68, 0.00774, 54, 19.03, -9, 0.99226, 2, 53, 28.8, -18.42, 0.55048, 54, 3.11, -18.17, 0.44952, 4, 51, 87.23, -21.93, 0.00607, 52, 43.81, -18.34, 0.03897, 53, 9.51, -16.74, 0.94815, 54, -16.12, -20.44, 0.00681, 3, 51, 67.02, -23.01, 0.22402, 52, 23.78, -21.26, 0.50712, 53, -9.93, -22.37, 0.26886, 4, 50, 82.24, -16.46, 0.0107, 51, 38.76, -25.3, 0.97657, 52, -4.15, -26.13, 0.01157, 53, -36.94, -31, 0.00117, 2, 50, 52.88, -18.78, 0.46397, 51, 9.53, -21.64, 0.53603, 1, 50, 22.35, -22.59, 1, 2, 43, 63.45, -21.93, 0.50549, 50, -8.02, -21.93, 0.49451, 2, 43, 42.68, -27.59, 0.96457, 50, -27.53, -12.85, 0.03543, 1, 43, 28.8, -19.59, 1, 1, 43, 11.86, -17.35, 1, 1, 43, -6.27, -5.88, 1, 2, 53, 29.11, 0.72, 0.58746, 54, -0.48, 0.64, 0.41254, 2, 51, 81.77, -4.14, 0.00013, 53, 0.16, -0.65, 0.99987, 1, 51, 44.87, -0.7, 1, 1, 51, 0.8, -0.46, 1, 2, 43, 54.09, 0.21, 0.77656, 50, -0.88, 1.02, 0.22344, 1, 43, 19.22, 2.04, 1, 2, 43, 40.12, 0.95, 0.99737, 50, -11.07, 10.59, 0.00263, 3, 43, 72.8, 15.39, 0.00019, 50, 23.21, 0.54, 0.99661, 51, -15.62, 3.28, 0.0032, 2, 50, 59.27, 4.06, 0.0019, 51, 20.41, -0.56, 0.9981, 3, 51, 63.79, -2.46, 0.00954, 52, 18.68, -1.1, 0.98637, 53, -17.73, -3.09, 0.00408, 2, 52, 53.21, -2.6, 0.00098, 53, 16.68, 0.13, 0.99902, 3, 51, 64.36, -15.59, 0.23545, 52, 20.45, -14.11, 0.60075, 53, -14.2, -15.74, 0.1638, 2, 50, 74.09, -3.73, 0.0141, 51, 33.35, -11.19, 0.9859, 2, 50, 45.61, -8.31, 0.59007, 51, 4.53, -9.92, 0.40993, 1, 50, 13.36, -9.78, 1, 2, 43, 55.88, -10.27, 0.64955, 50, -6.28, -8.15, 0.35045, 1, 43, 32.27, -9.22, 1, 3, 43, 49.64, 10.81, 0.59119, 50, 2.56, 11.99, 0.40822, 51, -33.53, 18.67, 0.00058, 2, 50, 42.17, 9.37, 0.02072, 51, 4.74, 8.09, 0.97928, 1, 52, 0.51, 6.25, 1, 1, 52, 27.71, 6.48, 1], "width": 117, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 18, 46, 0, 56, 54, 58, 58, 56, 52, 60, 60, 54, 50, 62, 62, 52, 48, 64, 64, 50, 46, 66, 66, 48], "type": "mesh", "hull": 23, "height": 231}}, "Rong": {"Rong": {"triangles": [130, 103, 104, 130, 104, 105, 130, 111, 112, 108, 130, 107, 130, 108, 111, 106, 130, 105, 107, 130, 106, 110, 111, 108, 109, 110, 108, 128, 101, 129, 114, 128, 129, 113, 114, 129, 112, 113, 129, 102, 103, 130, 130, 129, 101, 130, 101, 102, 129, 130, 112, 128, 100, 101, 128, 127, 99, 128, 99, 100, 116, 117, 128, 114, 116, 128, 115, 116, 114, 94, 92, 93, 95, 92, 94, 86, 92, 95, 134, 86, 95, 126, 98, 127, 117, 126, 127, 118, 126, 117, 95, 96, 97, 98, 133, 95, 98, 95, 97, 119, 126, 118, 119, 133, 126, 133, 98, 126, 120, 0, 119, 98, 99, 127, 117, 127, 128, 66, 154, 65, 142, 125, 154, 74, 75, 125, 66, 142, 154, 74, 125, 142, 73, 74, 142, 72, 73, 142, 69, 67, 68, 142, 66, 67, 69, 142, 67, 70, 142, 69, 72, 142, 70, 71, 72, 70, 36, 37, 38, 39, 35, 36, 39, 36, 38, 35, 39, 40, 150, 42, 43, 150, 43, 151, 32, 33, 34, 31, 32, 34, 123, 150, 151, 40, 34, 35, 29, 30, 34, 31, 34, 30, 34, 150, 29, 144, 150, 123, 144, 123, 138, 29, 149, 24, 28, 29, 24, 150, 40, 41, 150, 41, 42, 40, 150, 34, 150, 149, 29, 149, 150, 144, 25, 28, 24, 27, 28, 25, 26, 27, 25, 139, 80, 138, 137, 144, 138, 80, 81, 138, 137, 138, 81, 122, 149, 144, 122, 144, 137, 82, 137, 81, 18, 23, 24, 149, 18, 24, 153, 60, 61, 153, 61, 62, 153, 62, 63, 154, 153, 63, 154, 63, 64, 153, 124, 152, 141, 153, 154, 141, 124, 153, 65, 154, 64, 140, 124, 141, 139, 124, 140, 140, 78, 139, 125, 141, 154, 77, 78, 140, 76, 140, 141, 75, 76, 141, 77, 140, 76, 125, 75, 141, 22, 23, 18, 21, 22, 18, 87, 88, 89, 19, 21, 18, 20, 21, 19, 145, 122, 137, 145, 137, 82, 136, 145, 82, 136, 82, 83, 136, 83, 84, 148, 149, 122, 148, 122, 145, 90, 86, 87, 90, 87, 89, 148, 18, 149, 148, 13, 18, 17, 18, 13, 16, 17, 13, 14, 16, 13, 15, 16, 14, 146, 145, 136, 86, 135, 84, 86, 84, 85, 136, 84, 135, 146, 136, 135, 11, 13, 148, 12, 13, 11, 9, 10, 11, 148, 9, 11, 121, 146, 135, 8, 9, 148, 134, 135, 86, 86, 90, 91, 86, 91, 92, 146, 7, 8, 146, 148, 145, 121, 7, 146, 148, 146, 8, 44, 46, 47, 45, 46, 44, 48, 49, 50, 53, 54, 55, 43, 44, 47, 57, 55, 56, 51, 48, 50, 151, 51, 52, 151, 52, 53, 151, 48, 51, 152, 55, 57, 43, 47, 48, 43, 48, 151, 152, 151, 53, 152, 53, 55, 143, 151, 152, 123, 151, 143, 124, 143, 152, 57, 58, 59, 152, 57, 59, 138, 123, 143, 139, 143, 124, 138, 143, 139, 152, 59, 60, 152, 60, 153, 78, 79, 139, 139, 79, 80, 131, 121, 135, 134, 131, 135, 147, 121, 131, 121, 147, 7, 6, 7, 147, 132, 131, 134, 5, 6, 147, 133, 134, 95, 132, 134, 133, 4, 5, 147, 3, 4, 147, 2, 147, 131, 2, 131, 132, 3, 147, 2, 0, 132, 133, 2, 132, 0, 1, 2, 0, 119, 0, 133], "uvs": [0.6283, 1, 0.43874, 1, 0.44604, 0.97869, 0.41351, 0.97869, 0.38363, 0.96531, 0.36504, 0.92623, 0.31657, 0.91125, 0.27807, 0.87592, 0.24885, 0.82668, 0.23225, 0.79349, 0.18909, 0.78429, 0.16386, 0.74896, 0.15456, 0.71845, 0.16054, 0.6949, 0.1153, 0.71695, 0.07348, 0.7132, 0.09937, 0.68751, 0.11265, 0.65218, 0.13323, 0.61472, 0.09007, 0.64201, 0.04825, 0.64629, 0.07746, 0.61953, 0.0984, 0.58466, 0.11234, 0.54292, 0.14621, 0.52365, 0.08778, 0.52579, 0.0267, 0.50384, 0.05458, 0.4819, 0.08778, 0.47655, 0.1223, 0.45192, 0.05481, 0.43594, 0.02361, 0.41988, 0, 0.39152, 0, 0.33103, 0.03025, 0.31094, 0.03091, 0.27615, 0.01962, 0.21941, 0.01431, 0.1662, 0.0588, 0.14425, 0.11125, 0.15014, 0.16635, 0.16941, 0.22212, 0.16727, 0.28918, 0.16834, 0.33765, 0.17262, 0.29781, 0.15121, 0.24271, 0.13836, 0.27922, 0.13034, 0.35358, 0.148, 0.3901, 0.15603, 0.36753, 0.12284, 0.41214, 0.10625, 0.48119, 0.12445, 0.5387, 0.13938, 0.57875, 0.16244, 0.58257, 0.14323, 0.62834, 0.16936, 0.64836, 0.14476, 0.67125, 0.19012, 0.6989, 0.18935, 0.70462, 0.21241, 0.77328, 0.247, 0.85258, 0.30133, 0.89236, 0.36203, 0.91651, 0.43763, 0.9293, 0.48688, 0.92504, 0.54644, 0.91083, 0.60829, 0.93924, 0.64723, 0.96198, 0.69534, 0.94635, 0.73543, 0.90373, 0.75032, 0.82274, 0.74001, 0.82842, 0.69648, 0.81099, 0.67009, 0.77998, 0.6352, 0.75762, 0.60671, 0.70785, 0.59683, 0.6588, 0.59101, 0.60326, 0.5724, 0.56863, 0.56891, 0.52608, 0.57938, 0.48785, 0.57705, 0.48352, 0.60264, 0.50299, 0.63055, 0.52535, 0.65963, 0.54267, 0.63927, 0.59893, 0.68812, 0.5744, 0.6416, 0.57296, 0.59741, 0.61191, 0.6195, 0.62922, 0.64393, 0.65952, 0.64683, 0.69847, 0.67649, 0.72876, 0.71661, 0.72372, 0.7621, 0.71001, 0.78361, 0.73598, 0.77431, 0.75185, 0.80397, 0.7605, 0.83944, 0.79224, 0.80455, 0.82181, 0.78885, 0.87086, 0.79234, 0.89034, 0.77024, 0.91716, 0.75572, 0.9342, 0.7579, 0.95599, 0.77866, 0.97755, 0.79568, 0.99426, 0.8205, 0.99514, 0.85703, 0.98018, 0.88753, 0.95995, 0.86838, 0.93576, 0.84604, 0.92124, 0.83398, 0.89881, 0.86022, 0.88561, 0.85916, 0.87989, 0.87334, 0.85042, 0.87689, 0.82578, 0.88043, 0.81717, 0.92093, 0.80673, 0.95767, 0.80981, 1, 0.46135, 0.80102, 0.38081, 0.58496, 0.45696, 0.43149, 0.65174, 0.46927, 0.79818, 0.5767, 0.76234, 0.90057, 0.80358, 0.85959, 0.84769, 0.83253, 0.88797, 0.82867, 0.92058, 0.79001, 0.50699, 0.85541, 0.56433, 0.92375, 0.6863, 0.94635, 0.59675, 0.82812, 0.52667, 0.73814, 0.46053, 0.66603, 0.44625, 0.56978, 0.48648, 0.5206, 0.58252, 0.52374, 0.66039, 0.54781, 0.73277, 0.52872, 0.85733, 0.64006, 0.55398, 0.4503, 0.4162, 0.51363, 0.404, 0.64717, 0.43115, 0.72001, 0.4138, 0.88679, 0.2957, 0.68487, 0.24639, 0.52479, 0.30089, 0.3689, 0.4748, 0.27892, 0.6539, 0.34169, 0.78757, 0.41807, 0.85376, 0.5227], "vertices": [1, 1, -1.97, 2.99, 1, 2, 1, 37.84, 59.81, 0.99824, 7, -82.34, 38.01, 0.00176, 2, 1, 44.23, 52.07, 0.99035, 7, -74.02, 32.4, 0.00965, 2, 1, 51.06, 61.82, 0.96353, 7, -70.23, 43.69, 0.03647, 2, 1, 62.31, 67.29, 0.93377, 7, -61, 52.12, 0.06623, 2, 1, 80.74, 62.69, 0.7987, 7, -42.02, 52.94, 0.2013, 2, 1, 96.5, 73.31, 0.59308, 7, -29.93, 67.59, 0.40692, 2, 1, 117.72, 75.65, 0.40406, 7, -10.24, 75.86, 0.59594, 3, 1, 142.16, 71.58, 0.1914, 7, 14.35, 78.89, 0.80844, 8, -120.86, -6.59, 0.00016, 3, 1, 157.99, 67.91, 0.07654, 7, 30.57, 79.87, 0.91873, 8, -109.83, 5.34, 0.00473, 3, 1, 170.48, 78.45, 0.02895, 7, 39.55, 93.52, 0.95517, 8, -112.79, 21.41, 0.01588, 3, 1, 188.91, 76.81, 0.00957, 7, 57.69, 97.18, 0.95136, 8, -102.23, 36.61, 0.03907, 3, 1, 202.21, 71.65, 0.00269, 7, 71.91, 96, 0.92339, 8, -91.16, 45.6, 0.07392, 3, 1, 209.71, 63.72, 0.00021, 7, 81.35, 90.53, 0.87221, 8, -80.56, 48.18, 0.12759, 2, 7, 77.12, 109.41, 0.85086, 8, -96.67, 58.88, 0.14914, 2, 7, 83.6, 123.38, 0.85179, 8, -101.67, 73.45, 0.14821, 2, 7, 91.65, 110.69, 0.84168, 8, -87.08, 69.86, 0.15832, 2, 7, 105.31, 100.99, 0.78496, 8, -70.5, 72.31, 0.21504, 2, 7, 119.04, 88.44, 0.62739, 8, -51.9, 72.76, 0.37261, 2, 7, 112.31, 107.35, 0.58862, 8, -69.85, 81.75, 0.41138, 2, 7, 115.33, 122.49, 0.58976, 8, -78.14, 94.77, 0.41024, 2, 7, 123.46, 108.49, 0.58674, 8, -62.59, 90.28, 0.41326, 2, 7, 136.03, 96.19, 0.53887, 8, -45, 90.11, 0.46113, 2, 7, 152.38, 85.33, 0.41797, 8, -25.68, 93.59, 0.58203, 2, 7, 156.74, 70.8, 0.26347, 8, -12.48, 86.11, 0.73653, 2, 7, 162.61, 91.38, 0.14853, 8, -22.49, 105.04, 0.85147, 2, 7, 179.16, 109.41, 0.13217, 8, -23.02, 129.5, 0.86783, 2, 7, 185.36, 96.57, 0.13273, 8, -9.66, 124.53, 0.86727, 3, 9, -127.71, -54.89, 2e-05, 7, 183.81, 84.28, 0.13065, 8, -2.27, 114.58, 0.86933, 3, 9, -118.17, -40.98, 0.00266, 7, 190.39, 68.74, 0.07371, 8, 13.23, 107.92, 0.92363, 3, 9, -143.89, -39.93, 0.00382, 7, 205.12, 89.86, 0.01493, 8, 9.25, 133.36, 0.98125, 3, 9, -156.74, -35.63, 0.00492, 7, 215.66, 98.37, 0.00697, 8, 10.97, 146.8, 0.98812, 3, 9, -168.24, -25.23, 0.00813, 7, 230.62, 102.47, 0.0027, 8, 18.92, 160.11, 0.98917, 3, 9, -174.9, 1.41, 0.02219, 7, 256.65, 93.74, 1e-05, 8, 43.76, 171.83, 0.9778, 2, 9, -166.38, 12.94, 0.03801, 8, 56.73, 165.71, 0.96199, 2, 9, -169.97, 28.33, 0.06192, 8, 71.12, 172.23, 0.93808, 2, 9, -180.23, 52.31, 0.08268, 8, 92.65, 186.96, 0.91732, 2, 9, -187.97, 75.28, 0.08979, 8, 113.67, 199.04, 0.91021, 2, 9, -174.59, 88.9, 0.09619, 8, 129.63, 188.56, 0.90381, 2, 9, -155.32, 90.96, 0.11352, 8, 135.4, 170.06, 0.88648, 2, 9, -133.63, 87.36, 0.16279, 8, 136.1, 148.09, 0.83721, 2, 9, -114.07, 93.26, 0.24814, 8, 145.69, 130.05, 0.75186, 2, 9, -90.14, 98.74, 0.38309, 8, 155.73, 107.64, 0.61691, 2, 9, -72.46, 101.15, 0.53177, 8, 161.54, 90.77, 0.46823, 2, 9, -88.96, 107.05, 0.56691, 8, 164.11, 108.11, 0.43309, 2, 9, -109.94, 107.81, 0.56985, 8, 160.78, 128.83, 0.43015, 2, 9, -97.86, 114.59, 0.5689, 8, 169.78, 118.3, 0.4311, 2, 9, -69.51, 113.41, 0.57798, 8, 174.14, 90.27, 0.42202, 3, 9, -55.66, 113.12, 0.66783, 10, -167.37, 36.42, 0.00047, 8, 176.55, 76.63, 0.33171, 2, 9, -67.33, 125.73, 0.71212, 8, 186.65, 90.53, 0.28788, 3, 9, -53.32, 137, 0.72799, 10, -176.92, 58.43, 0.00077, 8, 200.43, 78.98, 0.27125, 3, 9, -26.8, 135.11, 0.77802, 10, -152.82, 69.66, 0.00889, 8, 203.75, 52.6, 0.21308, 3, 9, -4.73, 133.64, 0.8276, 10, -132.82, 79.09, 0.02701, 8, 206.6, 30.67, 0.1454, 3, 9, 12.03, 127.04, 0.85658, 10, -114.96, 81.46, 0.05992, 8, 203.39, 12.94, 0.08349, 3, 9, 11.27, 135.84, 0.86067, 10, -119.9, 88.79, 0.06585, 8, 211.87, 15.4, 0.07348, 3, 9, 30.4, 128.39, 0.8572, 10, -99.56, 91.57, 0.10072, 8, 208.29, -4.81, 0.04209, 3, 9, 34.8, 141.01, 0.85759, 10, -101.84, 104.73, 0.10958, 8, 221.52, -6.67, 0.03283, 3, 9, 47.92, 123.06, 0.82964, 10, -81.65, 95.41, 0.15019, 8, 206.48, -23.04, 0.02017, 3, 9, 57.66, 125.85, 0.81022, 10, -74.5, 102.58, 0.17679, 8, 211.11, -32.04, 0.01299, 3, 9, 62.23, 116.2, 0.78111, 10, -65.82, 96.37, 0.21042, 8, 202.54, -38.4, 0.00848, 2, 9, 90.41, 107.06, 0.59937, 10, -36.74, 102.06, 0.40063, 3, 9, 124.55, 90.17, 0.35593, 10, 1.3, 103.88, 0.64377, 22, -88.48, 87.98, 0.0003, 3, 9, 145.36, 66.97, 0.18562, 10, 30.77, 93.7, 0.79811, 22, -57.58, 83.89, 0.01626, 3, 9, 162.26, 35.82, 0.04197, 10, 60.67, 74.68, 0.8287, 22, -24.48, 71.22, 0.12933, 3, 9, 172.22, 15.26, 0.00588, 10, 79.36, 61.55, 0.67953, 22, -3.54, 62.09, 0.31459, 2, 10, 96.69, 40.73, 0.33142, 22, 17.6, 45.16, 0.66858, 2, 10, 112.08, 16.68, 0.01858, 22, 37.48, 24.66, 0.98142, 1, 22, 57.92, 22.9, 1, 1, 22, 80.54, 17.04, 1, 1, 22, 92.07, 1.83, 1, 1, 22, 88.55, -14.8, 1, 1, 22, 67.57, -36.25, 1, 1, 22, 52.66, -23.11, 1, 1, 22, 39.2, -21.38, 1, 2, 10, 85.45, -24.97, 0.05438, 22, 19.71, -21.46, 0.94562, 4, 9, 124.46, -52.76, 0.00101, 10, 70.64, -21.11, 0.49626, 8, 48.93, -132.35, 0.00057, 22, 4.43, -20.64, 0.50216, 4, 9, 105.7, -52.82, 0.02811, 10, 54.27, -30.28, 0.89322, 8, 45.21, -113.96, 0.00869, 22, -9.78, -32.89, 0.06998, 5, 9, 87.64, -54.62, 0.11562, 7, 68.12, -97.37, 0.00052, 10, 39.36, -40.61, 0.84768, 8, 39.94, -96.6, 0.03397, 22, -22.33, -46, 0.0022, 4, 9, 65.87, -51.35, 0.32472, 7, 82.59, -80.78, 0.01129, 10, 18.74, -48.33, 0.53602, 8, 38.9, -74.61, 0.12797, 4, 9, 53.19, -52.89, 0.40747, 7, 88.12, -69.27, 0.03925, 10, 8.4, -55.83, 0.29644, 8, 34.92, -62.48, 0.25683, 5, 9, 39.24, -61.27, 0.32329, 7, 88.56, -52.99, 0.12406, 10, 0.28, -69.94, 0.13228, 8, 23.98, -50.42, 0.42033, 18, 70.17, 155.2, 4e-05, 5, 9, 25.41, -63.64, 0.16082, 7, 94.01, -40.06, 0.30223, 10, -10.66, -78.73, 0.04561, 8, 18.96, -37.32, 0.49052, 18, 62.98, 167.24, 0.00082, 6, 1, 176.18, -57.13, 0.00061, 9, 26.69, -75.3, 0.04867, 7, 83.5, -34.86, 0.67462, 10, -3.88, -88.29, 0.0115, 8, 7.78, -40.84, 0.25737, 18, 52.57, 161.85, 0.00723, 6, 1, 161.71, -55.69, 0.01091, 9, 36.68, -85.86, 0.01067, 7, 69.22, -37.6, 0.87723, 10, 9.98, -92.68, 0.00194, 8, -0.64, -52.7, 0.06873, 18, 46.31, 148.73, 0.03052, 6, 1, 146.21, -54.82, 0.05629, 9, 47.82, -96.68, 0.00083, 7, 54.1, -41.16, 0.83906, 10, 24.97, -96.73, 8e-05, 8, -9.08, -65.73, 0.00671, 18, 40.23, 134.44, 0.09702, 4, 1, 150.14, -65.31, 0.07447, 7, 60.85, -50.1, 0.79828, 8, 1.98, -67.52, 0.00048, 18, 51.43, 134.58, 0.12677, 3, 1, 120.16, -69.45, 0.18136, 7, 33.28, -62.58, 0.52651, 18, 45.16, 104.97, 0.29213, 3, 1, 142.61, -74.22, 0.16431, 7, 56.16, -60.78, 0.48678, 18, 57.26, 124.47, 0.34892, 3, 1, 159.34, -85.3, 0.15939, 7, 75.35, -66.66, 0.48785, 18, 73.36, 136.45, 0.35275, 3, 1, 142.95, -91.22, 0.16359, 7, 61.31, -76.99, 0.48429, 18, 73.36, 119.02, 0.35212, 3, 1, 130.23, -90.05, 0.17555, 7, 48.78, -79.47, 0.46967, 18, 67.95, 107.46, 0.35479, 3, 1, 122.79, -98.37, 0.18796, 7, 44, -89.56, 0.44181, 18, 73.25, 97.63, 0.37023, 3, 1, 103.58, -102.32, 0.20915, 7, 26.71, -98.8, 0.38599, 18, 70.45, 78.22, 0.40486, 3, 1, 82.3, -100.95, 0.2324, 7, 5.91, -103.53, 0.31319, 18, 61.94, 58.67, 0.45441, 3, 1, 66.45, -87.59, 0.25151, 7, -13.08, -95.21, 0.23004, 18, 43.99, 48.29, 0.51844, 3, 1, 61.32, -77.87, 0.25537, 7, -20.75, -87.35, 0.15603, 18, 33.12, 46.77, 0.58861, 3, 1, 59.33, -88.08, 0.22281, 7, -19.76, -97.71, 0.1148, 18, 42.05, 41.43, 0.66239, 5, 1, 44.97, -85.11, 0.19498, 7, -34.37, -98.93, 0.08549, 18, 34.38, 28.93, 0.71241, 19, -4.99, 29.64, 0.0061, 72, -12.24, -32.8, 0.00102, 5, 1, 29.96, -78.47, 0.07807, 7, -50.65, -96.82, 0.02229, 18, 23.04, 17.07, 0.76456, 19, -10.18, 14.07, 0.09626, 72, 4.02, -30.57, 0.03882, 6, 1, 36.27, -97.07, 0.00509, 7, -39.32, -112.87, 0.00171, 18, 42.68, 16.69, 0.06145, 19, 7.74, 22.12, 0.37998, 72, -11.13, -18.06, 0.54616, 41, -10.77, 34.86, 0.00561, 3, 19, 20.67, 22.99, 0.0233, 72, -17.63, -6.84, 0.88891, 41, 1.17, 29.81, 0.08779, 4, 19, 35.51, 12.76, 0.01438, 72, -15.01, 10.99, 0.20212, 41, 9.81, 13.99, 0.52679, 20, 9.8, 16.8, 0.25671, 5, 19, 46.66, 17.97, 0.00267, 72, -24.62, 18.68, 0.01994, 41, 22.11, 13.62, 0.72662, 20, 18.68, 25.32, 0.04076, 42, -13.05, -3.94, 0.21002, 2, 41, 33.04, 9.11, 0.46686, 42, -12.18, 7.85, 0.53314, 2, 41, 35.68, 3.38, 0.35565, 42, -7.55, 12.15, 0.64435, 2, 41, 32.26, -8.49, 0.07307, 42, 4.79, 12.61, 0.92693, 1, 42, 15.74, 14.05, 1, 1, 42, 28.38, 11.91, 1, 1, 42, 41.63, 1.93, 1, 2, 20, 41.02, -33.14, 7e-05, 42, 49.15, -10.92, 0.99993, 2, 41, -0.83, -32.29, 0.01592, 42, 37.74, -11.38, 0.98408, 4, 72, 10.69, 33.3, 0.00076, 41, 2.7, -19.29, 0.22318, 20, 28.57, -11.59, 0.02, 42, 24.29, -12.09, 0.75606, 4, 72, 4.92, 28.31, 0.01915, 41, 4.31, -11.84, 0.46996, 20, 24.38, -5.22, 0.09155, 42, 16.7, -12.9, 0.41934, 4, 72, 16.34, 19.43, 0.37322, 41, -10.16, -11.61, 0.12375, 20, 14.08, -15.39, 0.48856, 42, 21.01, -26.71, 0.01448, 4, 72, 15.58, 14.63, 0.52306, 41, -12.44, -7.33, 0.04778, 20, 9.43, -14.01, 0.4269, 42, 17.65, -30.21, 0.00226, 3, 72, 21.89, 12.18, 0.7022, 41, -18.96, -9.16, 0.00586, 20, 6.16, -19.94, 0.29194, 2, 72, 22.88, 1.31, 0.99198, 20, -4.73, -19.51, 0.00802, 3, 18, 21.58, -13.19, 0.53288, 19, 1.44, -13.91, 0.06491, 72, 23.97, -7.78, 0.4022, 3, 1, -12.24, -74.22, 0.00603, 18, 4.73, -21.19, 0.95517, 72, 42.15, -11.98, 0.0388, 3, 1, -23.71, -61.52, 0.08415, 18, -11.11, -27.67, 0.9134, 72, 58.58, -16.75, 0.00245, 2, 1, -40.09, -51.42, 0.14132, 18, -26.17, -39.66, 0.85868, 2, 1, 107.08, 1.2, 0.44428, 7, 0.68, 1.45, 0.55572, 4, 9, -11.73, -76.63, 0.00083, 7, 103.05, -1.77, 0.67038, 10, -36.82, -108.12, 0.00016, 8, -1.01, -3.42, 0.32862, 2, 9, -1.59, -2.27, 0.32096, 8, 73.9, 1.12, 0.67904, 3, 9, 71.73, -1.62, 0.55708, 10, -0.29, -2.01, 0.44203, 8, 88.82, -70.67, 0.0009, 2, 10, 72.17, -1.02, 0.37968, 22, 1.92, -0.65, 0.62031, 3, 1, 6.85, -63.09, 0.00176, 7, -77.18, -88.64, 0.00015, 18, 0.73, 0.54, 0.99809, 3, 18, 24.63, -1.09, 0.61306, 19, -0.97, -1.67, 0.36114, 72, 14.06, -15.35, 0.0258, 3, 19, 19.13, 1.06, 0.30181, 72, 2.72, 1.47, 0.47345, 20, -1.94, 0.46, 0.22474, 4, 72, 1.81, 16.29, 0.01444, 41, -0.44, -0.37, 0.46339, 20, 12.87, -0.58, 0.51732, 42, 7.3, -20.99, 0.00485, 2, 41, 20.78, -0.56, 0.51531, 42, 0.85, -0.77, 0.48469, 1, 1, 77.27, 1.69, 1, 1, 1, 39.82, 2.3, 1, 2, 1, 5.8, -28.37, 0.57521, 18, -32.28, 11.32, 0.42479, 3, 1, 68.56, -32.33, 0.66107, 7, -26.73, -41.63, 0.08902, 18, -7.27, 69.03, 0.24991, 3, 1, 116.74, -34.76, 0.18305, 7, 20.15, -30.29, 0.70159, 18, 11.36, 113.52, 0.11536, 6, 1, 157.44, -33.72, 0.00601, 9, 25.5, -105.26, 0.00149, 7, 58.88, -17.74, 0.96279, 10, 9.63, -115.06, 0.00014, 8, -21.84, -45.52, 0.0107, 18, 24.19, 152.16, 0.01888, 5, 9, 9.84, -64.13, 0.06492, 7, 101.98, -26.67, 0.32838, 10, -24.04, -86.72, 0.01409, 8, 15.45, -22.14, 0.59226, 18, 56.91, 181.59, 0.00036, 4, 9, 18.71, -38.9, 0.27185, 7, 118.47, -47.73, 0.04892, 10, -28.54, -60.35, 0.03588, 8, 41.93, -25.92, 0.64335, 4, 9, 53.15, -31.76, 0.55086, 7, 105.95, -80.6, 0.01226, 10, -1.89, -37.38, 0.2755, 8, 55.64, -58.32, 0.16138, 4, 9, 83.45, -35.45, 0.14247, 7, 86.53, -104.15, 0.00056, 10, 26.38, -25.89, 0.82353, 8, 57.93, -88.76, 0.03344, 3, 9, 107.05, -20.61, 0.00132, 10, 39.81, -1.46, 0.99829, 8, 77.07, -109.01, 0.00039, 1, 22, 37.91, 0.34, 1, 4, 9, 34.93, -1.95, 0.98697, 7, 140.88, -81.29, 0.00014, 10, -32.3, -20.17, 0.00186, 8, 81.33, -34.64, 0.01104, 4, 9, -7.02, -42.07, 0.0049, 7, 129.64, -24.34, 0.00204, 10, -49.48, -75.62, 0.0005, 8, 33.81, -1.31, 0.99255, 5, 9, 3.35, -101.97, 0.00011, 7, 73.58, -0.84, 0.99904, 10, -11.33, -122.94, 2e-05, 8, -22.92, -23.15, 0.00071, 18, 19.28, 174.01, 0.00012, 1, 7, 39.06, 0.24, 1, 2, 1, 85.17, 37.79, 0.84082, 7, -30.71, 30.32, 0.15918, 3, 1, 185.05, 20.59, 0.00069, 7, 69.95, 42.18, 0.96814, 8, -55.32, 5.39, 0.03118, 2, 7, 144.59, 36.19, 0.21707, 8, 2.7, 52.73, 0.78293, 3, 9, -63.9, 11.44, 0.1358, 7, 205.36, -5.21, 0.00023, 8, 75.22, 64.9, 0.86397, 3, 9, -12.05, 66.51, 0.7694, 10, -106.62, 16.85, 0.00891, 8, 139.33, 24.78, 0.22169, 3, 9, 58.45, 54.76, 0.77155, 10, -39.28, 40.82, 0.22826, 8, 141.53, -46.67, 0.00018, 3, 9, 114.33, 32.99, 0.14302, 10, 20.14, 48.92, 0.84878, 22, -59.05, 37.89, 0.0082, 2, 10, 70.28, 30.78, 0.62064, 22, -6.29, 30.13, 0.37936], "width": 366, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 2, 0, 0, 240, 252, 254, 254, 256, 256, 258, 258, 260, 260, 216, 252, 238, 252, 196, 262, 242, 0, 264, 264, 262, 248, 282, 282, 250, 138, 284, 284, 250, 246, 286, 286, 248, 244, 288, 288, 246, 290, 244, 242, 292, 292, 290], "type": "mesh", "hull": 121, "height": 454}}, "Miengduoi": {"Miengduoi": {"triangles": [41, 30, 40, 41, 29, 30, 26, 41, 40, 26, 40, 25, 41, 28, 29, 27, 41, 26, 41, 27, 28, 39, 22, 23, 31, 32, 39, 40, 31, 39, 40, 39, 23, 24, 40, 23, 30, 31, 40, 25, 40, 24, 32, 33, 38, 39, 32, 38, 21, 39, 38, 22, 39, 21, 35, 36, 37, 38, 35, 37, 34, 35, 38, 33, 34, 38, 21, 38, 37, 20, 21, 37, 36, 0, 12, 36, 13, 14, 37, 36, 14, 15, 37, 14, 16, 37, 15, 18, 16, 17, 19, 20, 37, 16, 19, 37, 18, 19, 16, 6, 7, 8, 4, 5, 6, 3, 4, 6, 9, 6, 8, 2, 6, 9, 3, 6, 2, 1, 9, 10, 2, 9, 1, 11, 1, 10, 0, 11, 12, 0, 1, 11, 36, 12, 13], "uvs": [0.56968, 0.36178, 0.41459, 0.28787, 0.22527, 0.25252, 0.09436, 0.19789, 0, 0.10149, 0, 0.03883, 0.12658, 0.07096, 0.11248, 0, 0.19707, 0, 0.32799, 0.03079, 0.47703, 0.04525, 0.62406, 0.07739, 0.81338, 0.13202, 0.89395, 0.20271, 0.93221, 0.34089, 0.93221, 0.48229, 0.92818, 0.52888, 1, 0.5562, 0.94027, 0.62529, 0.84359, 0.67509, 0.73282, 0.65903, 0.68851, 0.66545, 0.70865, 0.71526, 0.73886, 0.79399, 0.72879, 0.8679, 0.69455, 0.94021, 0.59788, 0.97073, 0.52335, 0.99805, 0.473, 1, 0.52738, 0.94181, 0.58378, 0.88236, 0.60795, 0.79881, 0.54954, 0.73294, 0.51127, 0.65099, 0.54752, 0.56744, 0.58781, 0.51763, 0.62809, 0.44051, 0.70059, 0.50639, 0.62003, 0.61886, 0.6583, 0.75061, 0.6442, 0.8679, 0.57169, 0.94181], "vertices": [3, 10, 85.84, -43.79, 0.27733, 13, -11.25, -16.72, 0.70873, 14, -15.93, -26.98, 0.01394, 2, 10, 60.77, -49.23, 0.77299, 13, -30.77, -33.37, 0.22701, 2, 10, 36.84, -63, 0.9588, 13, -45.33, -56.83, 0.0412, 2, 10, 16.62, -68.6, 0.99294, 13, -60.5, -71.33, 0.00706, 2, 10, -4.89, -65.22, 0.99998, 13, -81.04, -78.53, 2e-05, 1, 10, -12.51, -57.08, 1, 2, 10, 4.53, -48.97, 0.99919, 13, -80.44, -59.76, 0.00081, 1, 10, -5.56, -41.12, 1, 1, 10, 3.21, -32.91, 1, 2, 10, 20.53, -24.21, 0.99444, 13, -78.06, -30.37, 0.00556, 2, 10, 37.74, -11.64, 0.98856, 13, -68.84, -11.15, 0.01144, 2, 10, 56.9, -1.55, 0.99785, 13, -56.74, 6.8, 0.00215, 2, 10, 83.17, 9.72, 0.83371, 13, -38.93, 29.16, 0.16629, 2, 10, 100.12, 8.34, 0.559, 13, -23.34, 35.97, 0.441, 2, 10, 120.9, -5.91, 0.15343, 13, 1.7, 33.24, 0.84657, 2, 10, 138.09, -24.29, 0.00429, 13, 25.54, 25.19, 0.99571, 2, 10, 143.34, -30.74, 3e-05, 13, 33.22, 21.99, 0.99997, 1, 13, 41.09, 30.1, 1, 1, 13, 50.02, 18.13, 1, 1, 13, 54.03, 2.28, 1, 3, 13, 46.29, -11.7, 0.58249, 14, 21.77, 16.78, 0.14903, 15, 9.9, 13.62, 0.26849, 3, 13, 45.36, -18.03, 0.14683, 14, 25.52, 11.6, 0.10344, 15, 9.36, 7.25, 0.74973, 4, 13, 54.67, -18.16, 0.00322, 14, 32.28, 18.01, 2e-05, 15, 18.67, 7.69, 0.8423, 16, -6.01, 5.8, 0.15447, 2, 15, 33.32, 8.18, 0.01375, 16, 7.28, 11.99, 0.98625, 2, 16, 20.51, 12.39, 0.91448, 17, -11.07, 7.05, 0.08552, 2, 16, 33.92, 9.35, 0.18407, 17, 1.35, 12.97, 0.81593, 1, 17, 15.02, 7.39, 1, 1, 17, 26.02, 3.58, 1, 1, 17, 31.42, -1.12, 1, 1, 17, 18.68, -3.25, 1, 2, 16, 25.9, -7.65, 0.19521, 17, 5.57, -5.34, 0.80479, 2, 15, 29.3, -9.99, 0.05939, 16, 10.7, -6.3, 0.94061, 2, 15, 15.81, -14.94, 0.82646, 16, 0.23, -16.14, 0.17354, 2, 14, 34.08, -12.21, 0.16414, 15, 0.31, -16.38, 0.83586, 2, 14, 18.44, -13.99, 0.86876, 15, -12.7, -7.53, 0.13124, 2, 13, 15.85, -23.16, 0.00352, 14, 7.98, -12.67, 0.99648, 3, 10, 101.47, -48.36, 0.00929, 13, 4.68, -13.34, 0.5807, 14, -6.87, -13.45, 0.41001, 2, 13, 19.08, -7.34, 0.084, 14, -0.75, 0.91, 0.916, 2, 14, 22.25, -0.75, 0.72091, 15, -1.18, 0.02, 0.27909, 2, 15, 22.88, -0.85, 0.27897, 16, 1.21, -0.41, 0.72103, 1, 16, 22.17, 0.49, 1, 1, 17, 14.14, 1.11, 1], "width": 142, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 0, 72, 74, 76, 76, 78, 78, 80, 80, 82, 82, 56], "type": "mesh", "hull": 37, "height": 178}}, "longho": {"longho": {"x": 4.28, "width": 171, "y": 0.05, "height": 71}}, "longho4": {"longho": {"x": 4.28, "width": 171, "y": 10.54, "height": 71}}, "Mongtay": {"Mongtay": {"rotation": 10.65, "x": 23.94, "width": 47, "y": -3.3, "height": 54}}, "dau": {"dau": {"x": 7.91, "width": 152, "y": 0.69, "height": 38}}, "longho3": {"longho": {"x": 4.28, "width": 171, "y": 10.54, "height": 71}}, "tt_taixiu_char2": {"tt_taixiu_char2": {"triangles": [26, 27, 63, 30, 29, 26, 61, 55, 60, 80, 50, 55, 81, 50, 80, 82, 81, 80, 62, 85, 55, 62, 55, 61, 56, 80, 55, 28, 85, 62, 27, 28, 62, 36, 37, 82, 85, 56, 55, 82, 80, 56, 28, 86, 85, 29, 28, 27, 86, 56, 85, 28, 29, 86, 35, 36, 82, 35, 82, 56, 83, 56, 86, 57, 35, 56, 83, 57, 56, 29, 83, 86, 30, 83, 29, 26, 29, 27, 63, 27, 62, 25, 64, 65, 66, 88, 67, 66, 67, 18, 19, 66, 18, 59, 77, 66, 19, 59, 66, 21, 20, 19, 59, 19, 20, 18, 21, 19, 58, 59, 20, 65, 59, 58, 65, 64, 59, 25, 65, 58, 64, 77, 59, 14, 2, 3, 15, 14, 3, 16, 15, 3, 16, 12, 15, 16, 70, 12, 17, 16, 3, 69, 16, 17, 69, 70, 16, 68, 69, 17, 79, 70, 69, 79, 69, 68, 17, 3, 18, 88, 79, 68, 71, 79, 60, 79, 71, 70, 72, 87, 79, 87, 60, 79, 18, 67, 68, 88, 68, 67, 3, 22, 18, 18, 68, 17, 79, 75, 72, 88, 75, 79, 61, 60, 87, 73, 87, 72, 76, 75, 88, 73, 61, 87, 74, 61, 73, 62, 61, 74, 78, 72, 75, 78, 75, 76, 73, 72, 78, 74, 73, 78, 76, 88, 66, 77, 78, 76, 63, 74, 78, 62, 74, 63, 77, 64, 63, 77, 63, 78, 26, 63, 64, 26, 64, 25, 77, 76, 66, 7, 6, 8, 51, 7, 8, 10, 5, 2, 9, 5, 10, 9, 10, 50, 8, 9, 50, 5, 9, 8, 51, 8, 50, 50, 81, 51, 37, 51, 81, 37, 81, 82, 37, 40, 51, 4, 2, 5, 6, 5, 8, 47, 4, 5, 6, 47, 5, 46, 4, 47, 48, 47, 6, 45, 46, 47, 4, 1, 2, 1, 4, 46, 1, 46, 45, 48, 6, 7, 45, 47, 48, 45, 48, 44, 48, 7, 49, 42, 45, 44, 42, 44, 43, 49, 7, 51, 38, 37, 36, 52, 49, 51, 51, 40, 52, 37, 38, 40, 39, 40, 38, 38, 36, 35, 39, 38, 35, 53, 49, 52, 53, 43, 49, 34, 39, 35, 34, 41, 40, 34, 40, 39, 42, 43, 53, 53, 52, 40, 53, 40, 41, 42, 53, 41, 42, 41, 33, 1, 42, 33, 43, 44, 49, 44, 48, 49, 1, 45, 42, 84, 57, 83, 34, 35, 57, 30, 31, 83, 84, 83, 31, 33, 41, 34, 32, 31, 30, 57, 84, 31, 32, 57, 31, 33, 34, 57, 32, 33, 57, 1, 33, 32, 22, 3, 0, 21, 18, 22, 23, 21, 22, 24, 25, 58, 30, 25, 24, 58, 20, 21, 58, 21, 23, 24, 58, 23, 32, 30, 24, 23, 22, 0, 24, 23, 0, 32, 24, 0, 0, 1, 32, 30, 26, 25, 13, 2, 14, 12, 13, 14, 12, 14, 15, 13, 10, 2, 11, 13, 12, 11, 10, 13, 71, 11, 12, 70, 71, 12, 54, 11, 71, 50, 10, 11, 50, 11, 54, 60, 54, 71, 55, 54, 60, 50, 54, 55], "uvs": [1, 1, 0, 1, 0, 0, 1, 0, 0.01901, 0.51062, 0.12752, 0.42074, 0.1824, 0.54893, 0.23603, 0.56956, 0.2697, 0.49884, 0.2697, 0.38096, 0.34952, 0.26308, 0.50916, 0.25277, 0.59023, 0.18941, 0.53909, 0.11132, 0.64261, 0.04943, 0.79601, 0.12458, 0.82844, 0.19088, 0.93196, 0.38833, 0.93694, 0.53272, 0.86336, 0.65355, 0.86461, 0.7007, 0.88207, 0.71543, 0.97561, 0.78469, 0.93196, 0.90109, 0.74862, 0.90551, 0.72243, 0.78026, 0.68875, 0.68596, 0.67899, 0.67036, 0.61886, 0.65615, 0.61569, 0.67933, 0.68406, 0.79823, 0.6062, 0.81992, 0.61443, 0.93882, 0.38213, 0.92236, 0.4391, 0.77879, 0.44923, 0.73317, 0.41948, 0.66214, 0.41188, 0.63895, 0.40176, 0.67186, 0.38783, 0.68382, 0.35935, 0.68382, 0.36315, 0.82964, 0.18271, 0.8446, 0.20676, 0.75561, 0.18334, 0.73691, 0.11372, 0.70476, 0.05485, 0.6225, 0.11688, 0.58736, 0.18018, 0.65765, 0.26436, 0.64568, 0.42515, 0.47593, 0.36639, 0.59506, 0.30605, 0.67761, 0.29414, 0.74046, 0.56728, 0.43559, 0.58633, 0.52283, 0.55219, 0.66542, 0.52297, 0.7675, 0.79755, 0.74311, 0.78803, 0.67276, 0.63984, 0.48213, 0.62914, 0.54234, 0.63858, 0.60404, 0.69458, 0.64418, 0.70716, 0.70068, 0.76882, 0.71406, 0.8632, 0.62114, 0.88711, 0.52748, 0.87076, 0.4405, 0.80217, 0.37955, 0.721, 0.39442, 0.66312, 0.40557, 0.72792, 0.53863, 0.70527, 0.56093, 0.68954, 0.59735, 0.76756, 0.54011, 0.78896, 0.58546, 0.77323, 0.63229, 0.74303, 0.61222, 0.74995, 0.45611, 0.51244, 0.58117, 0.43541, 0.58167, 0.47468, 0.63528, 0.58048, 0.74495, 0.56711, 0.77655, 0.60367, 0.64114, 0.59013, 0.68788, 0.6801, 0.52451, 0.80586, 0.52307], "vertices": [1, 63, 81.01, 2.47, 1, 4, 61, 90.52, -121.62, 0.00301, 59, 121.97, -23.13, 0.45133, 65, 49.06, 85.82, 0.48622, 66, -50.69, 74.32, 0.05944, 4, 57, 167.16, -93.39, 0.06236, 66, 144.63, -39.37, 0.46008, 58, -76.43, -119.11, 0.45568, 64, -31.97, -149.62, 0.02188, 3, 57, -99.58, -105.38, 0.20106, 56, -106.02, 64.32, 0.76705, 62, -147.82, 63.62, 0.03189, 3, 66, 42.34, 14.29, 0.94109, 58, 34.94, -88.43, 0.05343, 64, 42.79, -61.56, 0.00549, 4, 57, 128.87, 0.07, 0.00272, 66, 45.32, -20.96, 0.64242, 58, 8.67, -64.72, 0.22491, 64, 7.73, -56.75, 0.12995, 4, 65, 15.09, -21.93, 0.11307, 66, 12.91, -19.05, 0.49384, 58, 33.65, -43.97, 0.04451, 64, 16.69, -25.54, 0.34858, 4, 65, 0.26, -19.32, 0.08087, 66, 1.68, -29.08, 0.1317, 58, 35, -28.97, 0.02184, 64, 9.36, -12.39, 0.76559, 3, 66, 10.97, -44.89, 0.13482, 58, 17.41, -23.77, 0.35224, 64, -8.1, -17.99, 0.51294, 2, 57, 91.35, -10.62, 0.0683, 58, -8.56, -29.71, 0.9317, 3, 57, 71.26, -38.19, 0.44521, 58, -39.28, -14.88, 0.54981, 56, -39.08, -106.62, 0.00499, 4, 57, 28.78, -42.43, 0.81399, 66, 26.87, -128.12, 0.02326, 58, -51.05, 26.15, 0.09138, 56, -43.26, -64.13, 0.07137, 4, 57, 7.8, -57.71, 0.71937, 66, 28.35, -154.03, 0.01166, 58, -69.84, 44.06, 0.0452, 56, -58.5, -43.13, 0.22376, 4, 57, 22.23, -74.72, 0.69176, 66, 50.47, -151.11, 0.03283, 58, -84, 26.81, 0.10997, 56, -75.54, -57.54, 0.16545, 5, 57, -4.75, -89.94, 0.60166, 66, 48.66, -182.04, 0.0091, 58, -103.8, 50.64, 0.03896, 56, -90.71, -30.53, 0.3502, 62, -141.06, -32.22, 8e-05, 5, 57, -46.43, -74.81, 0.34383, 66, 13.37, -208.89, 3e-05, 58, -96.37, 94.35, 0.00143, 56, -75.53, 11.13, 0.63889, 62, -122.21, 7.91, 0.01582, 4, 57, -55.75, -60.23, 0.25703, 58, -83.7, 106.13, 7e-05, 56, -60.93, 20.43, 0.70784, 62, -106.84, 15.87, 0.03506, 4, 57, -85.37, -16.89, 0.02395, 63, -33.99, 81.3, 0.01678, 56, -17.55, 49.98, 0.70553, 62, -60.99, 41.42, 0.25374, 3, 63, -8.82, 60.49, 0.11576, 56, 14.99, 52.73, 0.37962, 62, -28.33, 41.25, 0.50462, 3, 63, -1.62, 27.63, 0.24425, 56, 43.13, 34.28, 0.05515, 62, -1.96, 20.36, 0.7006, 3, 63, 6.53, 20.76, 0.45179, 56, 53.76, 35.08, 0.00577, 62, 8.7, 20.21, 0.54244, 3, 63, 12.12, 22.01, 0.55234, 56, 56.88, 39.88, 0.00455, 62, 12.24, 24.71, 0.44311, 3, 63, 40.45, 30.13, 0.78845, 56, 71.43, 65.51, 0.00125, 62, 29.03, 48.94, 0.2103, 3, 63, 52.24, 3.89, 0.98524, 61, 4.68, 113.01, 0.0022, 62, 54.77, 36.08, 0.01256, 4, 63, 20.28, -33.2, 0.73129, 61, 18.28, 65.97, 0.22639, 56, 101.35, 6.15, 0.03582, 60, 60.03, 67.01, 0.0065, 5, 63, -5.45, -19.5, 0.58397, 61, -7.26, 51.91, 0.17198, 56, 73.37, -2.07, 0.008, 62, 24.91, -18.55, 0.14141, 60, 35.44, 51.34, 0.09464, 5, 63, -27.32, -11.95, 0.07804, 61, -25.53, 37.72, 0.07229, 56, 52.47, -11.97, 0.31367, 62, 3.21, -26.54, 0.05489, 60, 18.12, 36.01, 0.48111, 5, 63, -31.68, -11.54, 0.04558, 61, -28.26, 34.29, 0.05137, 56, 49.06, -14.73, 0.20749, 62, -0.43, -28.99, 0.01478, 60, 15.62, 32.41, 0.68077, 3, 63, -44.79, -21.34, 0.01072, 61, -27.22, 17.95, 0.01207, 60, 17.7, 16.17, 0.97721, 4, 63, -41.46, -25.47, 0.02405, 61, -21.94, 18.49, 0.03663, 62, 0.81, -45.96, 0.00066, 60, 22.94, 17.05, 0.93866, 5, 63, -9.27, -29.84, 0.44928, 61, -0.69, 43.06, 0.32794, 56, 77.87, -12.13, 0.008, 62, 28.5, -28.97, 0.05012, 60, 42.57, 42.93, 0.16465, 4, 63, -19.51, -48.58, 0.17195, 61, 9.41, 24.24, 0.66225, 62, 32.43, -49.96, 0.00234, 60, 53.85, 24.8, 0.16346, 3, 63, 1.95, -64.9, 0.27839, 61, 34.81, 33.3, 0.71686, 60, 78.61, 35.47, 0.00474, 3, 61, 47.23, -27.58, 0.57987, 59, 48.11, 49.42, 0.40206, 65, -49.52, 54.18, 0.01806, 4, 61, 11.95, -21.26, 0.58911, 59, 12.89, 42.8, 0.2891, 58, 69, 34.43, 0.03148, 60, 59.31, -20.45, 0.09031, 5, 57, 39.89, 66.75, 0.00196, 61, 1.29, -21.31, 0.37867, 59, 2.94, 38.97, 0.26192, 58, 58.35, 34.76, 0.08329, 60, 48.67, -21.18, 0.27416, 5, 57, 48.55, 51.07, 0.02075, 61, -12.17, -33.13, 0.08757, 59, -5.44, 23.14, 0.31962, 58, 44.47, 23.44, 0.3035, 60, 36, -33.84, 0.26857, 5, 57, 50.81, 45.93, 0.033, 61, -16.7, -36.44, 0.04894, 59, -8.51, 18.44, 0.26629, 58, 39.82, 20.3, 0.42368, 60, 31.68, -37.43, 0.22809, 5, 57, 53.18, 53.48, 0.01175, 61, -8.82, -37.13, 0.09988, 59, -0.9, 20.59, 0.43359, 58, 47.67, 19.32, 0.26778, 60, 39.59, -37.62, 0.187, 5, 57, 56.77, 56.34, 0.00519, 61, -5.25, -40.03, 0.1104, 59, 3.47, 19.15, 0.55327, 58, 51.13, 16.3, 0.19492, 60, 43.34, -40.28, 0.13622, 5, 57, 64.37, 56.69, 0.00133, 61, -3.29, -47.37, 0.07253, 59, 7.92, 12.98, 0.74876, 58, 52.83, 8.88, 0.11565, 60, 45.77, -47.49, 0.06173, 5, 61, 28.29, -37.88, 0.40658, 59, 34.07, 33.06, 0.57847, 65, -41.57, 34.14, 0.00623, 58, 84.73, 17.22, 0.00149, 60, 76.68, -35.99, 0.00722, 3, 61, 43.99, -83.55, 0.06845, 59, 64.96, -4.06, 0.9036, 66, -44.88, 14.49, 0.02795, 4, 61, 22.91, -82.54, 0.01379, 59, 44.89, -10.6, 0.98007, 66, -30.73, -1.18, 0.00385, 64, 43.68, 13.13, 0.00229, 3, 61, 20.44, -89.67, 0.00487, 59, 45.11, -18.15, 0.96035, 66, -23.93, 2.1, 0.03478, 3, 59, 50.08, -37.48, 0.13376, 65, 28.32, 15.5, 0.53884, 66, -8.3, 14.51, 0.3274, 3, 59, 44.17, -61.1, 0.02218, 65, 46.48, -0.71, 0.05559, 66, 15.67, 18.74, 0.92223, 2, 59, 28.05, -52.3, 0.0002, 66, 14.2, 0.44, 0.9998, 2, 59, 31.07, -29.3, 0.02299, 65, 12.24, 2.48, 0.97701, 1, 59, 15.74, -12.64, 1, 3, 57, 48.93, 8.96, 0.58084, 58, 3.11, 15.53, 0.37643, 60, -4.36, -45.85, 0.04273, 5, 57, 63.39, 36.56, 0.01517, 61, -23.15, -50.73, 0.00423, 59, -9.46, 2.78, 0.07277, 58, 32.86, 6.24, 0.86412, 60, 26.17, -52.11, 0.04371, 4, 61, -0.97, -61.48, 0.00103, 59, 15.09, 0.61, 0.99825, 58, 54.64, -5.3, 0.00029, 60, 48.99, -61.42, 0.00042, 3, 61, 13.58, -60.89, 0.04573, 59, 28.48, 6.33, 0.95143, 60, 63.47, -59.89, 0.00284, 2, 57, 11.43, -1.85, 0.99907, 56, -2.65, -46.84, 0.00093, 4, 57, 5.46, 17.62, 0.32656, 58, 3.85, 59.85, 0.00119, 56, 16.83, -40.9, 0.02075, 60, -8.07, -1.69, 0.6515, 4, 61, -20.6, 1.3, 1e-05, 59, -25.55, 52.33, 5e-05, 58, 37.29, 58.15, 6e-05, 60, 25.38, -0.03, 0.99988, 4, 61, 3.71, -0.28, 0.94312, 59, -2.26, 59.48, 0.00107, 58, 61.52, 55.69, 0.00037, 60, 49.73, -0.05, 0.05543, 2, 63, 1.7, 1.04, 0.66663, 62, 17.45, 1.88, 0.33337, 2, 56, 48.34, 14.38, 0.384, 62, 1.45, 0.07, 0.616, 3, 57, -8.4, 7.79, 0.5851, 56, 7.02, -27.02, 0.24105, 60, -21.35, 8.91, 0.17385, 3, 57, -6.16, 21.51, 0.17066, 56, 20.73, -29.29, 0.25546, 60, -7.54, 10.55, 0.57388, 3, 57, -9.3, 35.33, 0.00838, 56, 34.56, -26.16, 0.39551, 60, 4.86, 17.4, 0.59611, 4, 63, -33.3, -4.49, 0.00623, 61, -35.05, 36.79, 0.00978, 56, 42.97, -10.83, 0.6221, 60, 8.68, 34.46, 0.36189, 5, 63, -21.56, -10.52, 0.05462, 61, -23.58, 43.33, 0.03523, 56, 55.58, -6.92, 0.70085, 62, 6.76, -21.79, 0.08896, 60, 19.7, 41.73, 0.12034, 5, 63, -8.31, -0.29, 0.01261, 61, -24.91, 60.02, 0.00571, 56, 57.89, 9.66, 0.13096, 62, 10.54, -5.48, 0.84722, 60, 17.31, 58.29, 0.00351, 3, 63, -7.1, 32.49, 0.16336, 56, 35.81, 33.92, 0.14271, 62, -9.28, 20.66, 0.69394, 3, 63, -18.59, 51.38, 0.07464, 56, 14.39, 39.38, 0.45397, 62, -30.13, 28.01, 0.47139, 4, 57, -69.57, -4.38, 0.00855, 63, -36.13, 61.27, 0.01953, 56, -5.06, 34.16, 0.70964, 62, -49.97, 24.56, 0.26228, 4, 57, -50.66, -17.32, 0.08432, 63, -58.62, 56.84, 0.00081, 56, -18.03, 15.27, 0.83845, 62, -64.57, 6.9, 0.07642, 3, 57, -29.16, -12.99, 0.27174, 56, -13.73, -6.23, 0.72512, 62, -62.21, -14.91, 0.00314, 3, 57, -13.83, -9.78, 0.63981, 56, -10.54, -21.57, 0.3586, 60, -39.73, 9.27, 0.00159, 3, 57, -32.47, 19.49, 0.01255, 56, 18.75, -2.97, 0.96754, 60, -16.77, 35.28, 0.0199, 3, 57, -26.65, 24.79, 0.02721, 56, 24.05, -8.8, 0.86147, 60, -10.06, 31.16, 0.11132, 3, 57, -22.83, 33.21, 0.0104, 56, 32.45, -12.63, 0.77546, 60, -0.92, 29.81, 0.21414, 3, 63, -37.79, 25.72, 0.00148, 56, 18.63, 7.62, 0.89077, 62, -28.75, -4, 0.10775, 3, 63, -26.34, 23.13, 0.00515, 56, 28.62, 13.77, 0.7629, 62, -18.25, 1.23, 0.23195, 2, 56, 39.37, 10.03, 0.82809, 62, -7.87, -3.45, 0.17191, 2, 56, 35.19, 1.78, 0.96082, 62, -12.77, -11.3, 0.03918, 3, 63, -55.06, 34.91, 0.00012, 56, -0.14, 2.09, 0.99122, 62, -47.93, -7.83, 0.00866, 4, 57, 24.57, 31.67, 0.15779, 59, -34.8, 32.6, 0.01452, 58, 21.1, 43.56, 0.12768, 60, 10.73, -16.17, 0.70001, 5, 57, 45.12, 32.71, 0.14802, 61, -30.83, -33.71, 0.00655, 59, -22.69, 15.97, 0.06114, 58, 25.8, 23.53, 0.50074, 60, 17.41, -35.62, 0.28354, 5, 57, 34.1, 44.35, 0.06028, 61, -21.83, -20.46, 0.0255, 59, -18.98, 31.56, 0.09581, 58, 35.27, 36.45, 0.23029, 60, 25.54, -21.81, 0.58812, 4, 63, -36.71, -42.37, 0.04011, 61, -5.18, 13.23, 0.27016, 62, 15.19, -56.04, 0.00047, 60, 40, 12.88, 0.68926, 3, 63, -33.78, -49.8, 0.06433, 61, 2.64, 11.63, 0.93548, 62, 22.16, -59.93, 0.00018, 3, 63, -50.03, -22.09, 0.00325, 61, -29.45, 13.16, 0.00145, 60, 15.79, 11.24, 0.99529, 3, 63, -44.58, -31.84, 0.01438, 61, -18.31, 12.39, 0.02307, 60, 26.95, 11.2, 0.96255, 3, 57, -19.57, 16.87, 0.1542, 56, 16.12, -15.87, 0.65434, 60, -15.71, 22.16, 0.19146, 3, 63, -33.82, 35.91, 0.01246, 56, 14.33, 17.66, 0.73088, 62, -32.13, 6.39, 0.25665], "width": 267, "edges": [0, 2, 2, 4, 4, 6, 0, 6, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 8, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 118, 118, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 120, 144, 146, 146, 148, 144, 150, 150, 152, 152, 154, 110, 112, 166, 168, 170, 172], "type": "mesh", "hull": 4, "height": 226}}}}, "skeleton": {"images": "", "width": 464.69, "spine": "3.6.53", "hash": "I9IfUTfeOjlK54Rrjj4RTN7GptM", "height": 234.69}, "slots": [{"attachment": "mongtay2", "name": "mongtay2", "bone": "mongtay2"}, {"attachment": "Rau2", "name": "Rau2", "bone": "Rau12"}, {"attachment": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON><PERSON>"}, {"attachment": "Mongtay", "name": "Mongtay", "bone": "Mongtay"}, {"attachment": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "bone": "<PERSON><PERSON>"}, {"attachment": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "bone": "<PERSON><PERSON>"}, {"attachment": "Mat", "name": "Mat", "bone": "Mat"}, {"attachment": "Toc1", "name": "Toc1", "bone": "Toc1"}, {"attachment": "Toc2", "name": "Toc2", "bone": "Toc2"}, {"attachment": "Rau1", "name": "Rau1", "bone": "Rau1"}, {"attachment": "tt_taixiu_char2", "name": "tt_taixiu_char2", "bone": "bone"}, {"attachment": "longho", "name": "longho", "bone": "longho"}, {"attachment": "longho", "blend": "additive", "name": "longho2", "bone": "longho2"}, {"attachment": "longho", "blend": "additive", "name": "longho3", "bone": "longho3"}, {"attachment": "longho", "blend": "additive", "name": "longho4", "bone": "longho4"}, {"attachment": "dau", "name": "dau", "bone": "dau"}, {"attachment": "tranh", "name": "tranh", "bone": "tranh"}], "bones": [{"name": "root"}, {"scaleX": 0.4, "parent": "root", "scaleY": 0.5, "rotation": 139.66, "name": "<PERSON><PERSON>", "length": 106.01, "x": -109.92, "y": -72.12}, {"parent": "<PERSON><PERSON>", "rotation": -35.02, "name": "<PERSON><PERSON>", "length": 41.34, "x": 111.56, "y": 160.9}, {"parent": "<PERSON><PERSON>", "rotation": -45, "name": "Duoi2", "length": 47.97, "x": 41.34, "y": -0.53}, {"parent": "Duoi2", "rotation": -46.01, "name": "Duoi3", "length": 59.9, "x": 47.97}, {"parent": "Duoi3", "rotation": 2.26, "name": "Duoi4", "length": 48.77, "x": 59.9}, {"parent": "Duoi4", "rotation": 27.17, "name": "Duoi5", "length": 36.76, "x": 48.77}, {"parent": "<PERSON><PERSON>", "rotation": -16.49, "name": "Rong2", "length": 106.15, "x": 106.01}, {"parent": "Rong2", "rotation": -43.8, "name": "Rong3", "length": 76.44, "x": 106.15}, {"parent": "Rong3", "rotation": -78.77, "name": "Rong4", "length": 72.96, "x": 76.44}, {"parent": "Rong4", "rotation": -29.05, "name": "Rong5", "length": 70.42, "x": 72.96}, {"scaleX": 1.009, "parent": "Rong5", "scaleY": 1.009, "rotation": -6.38, "name": "Mat", "x": 67.96, "y": 3.73}, {"parent": "Rong5", "rotation": -25.19, "name": "<PERSON><PERSON><PERSON><PERSON>", "length": 37.47, "x": 69.76, "y": -18.44}, {"parent": "<PERSON><PERSON><PERSON><PERSON>", "rotation": -3.05, "name": "Miengduoi2", "length": 46.08, "x": 37.47}, {"parent": "Miengduoi2", "rotation": -44.28, "name": "Miengduoi3", "length": 23.15, "x": 18.99, "y": -8.52}, {"parent": "Miengduoi3", "rotation": 40.74, "name": "Miengduoi4", "length": 21.92, "x": 23.15}, {"parent": "Miengduoi4", "rotation": -23.07, "name": "Miengduoi5", "length": 24.83, "x": 21.92}, {"parent": "Miengduoi5", "rotation": -38.26, "name": "Miengduoi6", "length": 28.93, "x": 24.83}, {"parent": "<PERSON><PERSON>", "rotation": -70.17, "name": "Rong7", "length": 26.23, "x": 6.09, "y": -62.58}, {"parent": "Rong7", "rotation": -25.31, "name": "Rong8", "length": 20.83, "x": 26.23}, {"parent": "Rong8", "rotation": -18.75, "name": "Rong9", "length": 12.91, "x": 20.83}, {"parent": "Rong9", "rotation": -21.44, "name": "Mongtay", "length": 31.04, "x": 22.49, "y": 14.78}, {"parent": "Rong5", "rotation": -11.52, "name": "Rong6", "length": 72.58, "x": 70.42}, {"parent": "Rong6", "rotation": -129.85, "name": "Rau1", "length": 26.06, "x": 67.33, "y": -9.16}, {"parent": "Rau1", "rotation": -20.38, "name": "Rau2", "length": 24.16, "x": 26.06}, {"parent": "Rau2", "rotation": -14.72, "name": "Rau3", "length": 24.22, "x": 24.16}, {"parent": "Rau3", "rotation": -0.94, "name": "Rau4", "length": 25.63, "x": 24.22}, {"parent": "Rau4", "rotation": -4.5, "name": "Rau5", "length": 21.82, "x": 25.63}, {"parent": "Rau5", "rotation": 6.19, "name": "Rau6", "length": 23.97, "x": 21.82}, {"parent": "Rau6", "rotation": 15.48, "name": "Rau7", "length": 22.55, "x": 23.97}, {"parent": "Rau7", "rotation": 9.8, "name": "Rau8", "length": 22.55, "x": 22.55}, {"parent": "Rau8", "rotation": -4.6, "name": "Rau9", "length": 24.78, "x": 22.55}, {"parent": "Rau9", "rotation": 0.39, "name": "Rau10", "length": 21.31, "x": 24.78}, {"parent": "Rau10", "rotation": 7.34, "name": "Rau11", "length": 20.23, "x": 21.31}, {"parent": "Rong6", "rotation": 84.6, "name": "Rau12", "length": 10.93, "x": 78.2, "y": 11.21}, {"parent": "Rau12", "rotation": 35, "name": "Rau13", "length": 12.05, "x": 10.93}, {"parent": "Rau13", "rotation": 52.05, "name": "Rau14", "length": 20.02, "x": 12.05}, {"parent": "Rau14", "rotation": 14.6, "name": "Rau15", "length": 21.92, "x": 20.02}, {"parent": "Rau15", "rotation": -8.56, "name": "Rau16", "length": 18.68, "x": 21.92}, {"parent": "Rau16", "rotation": -13.94, "name": "Rau17", "length": 18.88, "x": 18.68}, {"parent": "Rau17", "rotation": -32.31, "name": "Rau18", "length": 17.57, "x": 18.88}, {"parent": "Rong9", "rotation": 45.53, "name": "Rong10", "length": 21.78, "x": 12.91}, {"parent": "Rong10", "rotation": -108.21, "name": "Rong11", "length": 33.77, "x": 21.78}, {"parent": "Rong5", "rotation": 119.46, "name": "Toc1", "length": 55.42, "x": 57.69, "y": 37.88}, {"parent": "Rong5", "rotation": -160.82, "name": "Toc2", "length": 63.08, "x": 24.68, "y": 40.52}, {"parent": "Toc2", "rotation": -47.18, "name": "Toc3", "length": 41.98, "x": 63.08}, {"parent": "Toc3", "rotation": -16.34, "name": "Toc4", "length": 36.48, "x": 41.98}, {"parent": "Toc4", "rotation": -0.72, "name": "Toc5", "length": 33.75, "x": 36.48}, {"parent": "Toc5", "rotation": 35.52, "name": "Toc6", "length": 37.65, "x": 33.75}, {"parent": "Toc6", "rotation": 7.63, "name": "Toc7", "length": 43.08, "x": 37.65}, {"parent": "Toc1", "rotation": 40.19, "name": "Toc8", "length": 38.93, "x": 55.42}, {"parent": "Toc8", "rotation": 11.67, "name": "Toc9", "length": 45.71, "x": 39.17, "y": 0.49}, {"parent": "Toc9", "rotation": -5.25, "name": "Toc10", "length": 36.97, "x": 45.28, "y": 0.34}, {"parent": "Toc10", "rotation": -7.84, "name": "Toc11", "length": 29.45, "x": 36.67, "y": -0.46}, {"parent": "Toc11", "rotation": -11.71, "name": "Toc12", "length": 37.47, "x": 29.45}, {"scaleX": 0.9, "parent": "root", "scaleY": 0.9, "name": "bone", "x": 137.48, "y": 28.05}, {"parent": "bone", "rotation": -92.49, "name": "bone2", "length": 47.52, "x": 35.39, "y": -1.92}, {"parent": "bone", "rotation": 177.43, "name": "bone3", "length": 45.97, "x": 0.04, "y": 0.41}, {"parent": "bone3", "rotation": 79.69, "name": "bone4", "length": 40.49, "x": 63.65, "y": 3.12}, {"parent": "bone4", "rotation": -22.87, "name": "bone5", "length": 31.79, "x": 40.49}, {"parent": "bone3", "rotation": 73.94, "name": "bone6", "length": 46.02, "x": 6.07, "y": 25.84}, {"parent": "bone6", "rotation": 3.68, "name": "bone7", "length": 30.98, "x": 46.02}, {"parent": "bone", "rotation": -87.36, "name": "bone8", "length": 16.79, "x": 47.51, "y": -49.39}, {"parent": "bone8", "rotation": 39.27, "name": "bone9", "length": 30.51, "x": 16.79}, {"parent": "bone3", "rotation": 45.42, "name": "bone10", "length": 24.19, "x": 83.03, "y": 34.39}, {"parent": "bone10", "rotation": -50.88, "name": "bone11", "length": 22.06, "x": 24.19}, {"parent": "bone11", "rotation": -51.76, "name": "bone12", "length": 19.92, "x": 22.06}, {"parent": "root", "name": "dau", "x": -47.79, "y": -29.1}, {"parent": "root", "name": "longho", "x": 13.14, "y": 6.56}, {"parent": "root", "name": "longho2", "x": 13.14, "y": -3.93}, {"parent": "root", "name": "longho3", "x": 13.14, "y": -3.93}, {"parent": "root", "name": "longho4", "x": 13.14, "y": -3.93}, {"parent": "Rong8", "rotation": -116.25, "name": "mongtay2", "length": 26.32, "x": 19.01, "y": 4.15}, {"parent": "mongtay2", "rotation": 57.51, "name": "mongtay3", "length": 23.52, "x": 26.32}, {"parent": "root", "name": "tranh", "x": 88.74, "y": -31.31}], "animations": {"animation": {"slots": {"longho2": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.4}, {"color": "ffffff89", "time": 0.5667}, {"color": "ffffff00", "time": 0.7333}]}, "longho": {"color": [{"color": "ffffff01", "curve": [0.652, 0, 0.75, 1], "time": 0}, {"color": "ffffffff", "curve": "stepped", "time": 0.4}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 3.5333}, {"color": "ffffff01", "time": 4}]}, "longho4": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.8}, {"color": "ffffff89", "time": 0.9667}, {"color": "ffffff00", "time": 1.1333}]}, "dau": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.182, 0.4, 0.686, 1], "time": 0.1}, {"color": "ffffffff", "curve": "stepped", "time": 0.2667}, {"color": "ffffffff", "curve": [0.254, 0.42, 0.75, 1], "time": 3.7}, {"color": "ffffff00", "time": 3.9}]}, "longho3": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.6}, {"color": "ffffff89", "time": 0.7667}, {"color": "ffffff00", "time": 0.9333}]}, "tranh": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.182, 0.4, 0.686, 1], "time": 0.1333}, {"color": "ffffffff", "curve": "stepped", "time": 0.3}, {"color": "ffffffff", "curve": [0.207, 0.39, 0.75, 1], "time": 3.6667}, {"color": "ffffff00", "time": 3.8667}]}}, "bones": {"tranh": {"translate": [{"curve": "stepped", "x": 87.08, "y": 0, "time": 0}, {"curve": [0.182, 0.4, 0.686, 1], "x": 87.08, "y": 0, "time": 0.1333}, {"curve": [0.323, 0, 0.835, 0.68], "x": -7.32, "y": 0, "time": 0.3}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.4333}, {"curve": [0.207, 0.39, 0.75, 1], "x": 0, "y": 0, "time": 3.6667}, {"x": 87.08, "y": 0, "time": 3.8667}]}, "Rau10": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -4.6, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.82, "time": 0.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": -3.87, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.66, "time": 1.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.6, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.82, "time": 2.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": -3.87, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.66, "time": 3.0667}, {"angle": -4.6, "time": 4}]}, "Rong9": {"rotate": [{"angle": -1.66, "time": 0}]}, "Toc10": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -2.88, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0.45, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.24, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.88, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 0.45, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.24, "time": 3}, {"angle": -2.88, "time": 4}]}, "Rong8": {"rotate": [{"angle": 2.52, "time": 0}]}, "Rau12": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 7.57, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -3.43, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": 7.57, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -3.43, "time": 3}, {"angle": 7.57, "time": 4}]}, "Rong6": {"rotate": [{"angle": -3.78, "time": 0}]}, "Rau11": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -3.9, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.82, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.66, "time": 1.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": -3.9, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.82, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.66, "time": 3.1667}, {"angle": -3.9, "time": 4}]}, "longho": {"scale": [{"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 1.05, "y": 1.05, "time": 0.0667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.1333}, {"curve": [0.25, 0, 0.75, 1], "x": 1.05, "y": 1.05, "time": 0.2}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.2667}, {"curve": [0.25, 0, 0.75, 1], "x": 1.05, "y": 1.05, "time": 0.3333}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.4}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 1.0667}, {"curve": [0.25, 0, 0.75, 1], "x": 1.068, "y": 1.068, "time": 1.5667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 2.0333}, {"curve": [0.25, 0, 0.75, 1], "x": 1.068, "y": 1.068, "time": 2.5333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 3.0333}, {"curve": [0.25, 0, 0.75, 1], "x": 1.068, "y": 1.068, "time": 3.5333}, {"x": 1, "y": 1, "time": 4}]}, "Rau14": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 5.37, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 7.57, "time": 0.2}, {"curve": [0.25, 0, 0.75, 1], "angle": -3.43, "time": 1.2}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.37, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 7.57, "time": 2.2}, {"curve": [0.25, 0, 0.75, 1], "angle": -3.43, "time": 3.2}, {"angle": 5.37, "time": 4}]}, "Miengduoi": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 1.01, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.27, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.01, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.27, "time": 3.3333}, {"angle": 1.01, "time": 4}], "scale": [{"x": 0.921, "y": 1, "time": 0}]}, "dau": {"translate": [{"curve": "stepped", "x": -101.11, "y": 0, "time": 0}, {"curve": [0.182, 0.4, 0.686, 1], "x": -101.11, "y": 0, "time": 0.1}, {"curve": [0.25, 0, 0.75, 1], "x": 3, "y": 0, "time": 0.2667}, {"curve": [0.25, 0, 0.75, 1], "x": -9.68, "y": 0, "time": 0.7333}, {"curve": [0.25, 0, 0.75, 1], "x": 3, "y": 0, "time": 1.3}, {"curve": [0.25, 0, 0.75, 1], "x": -9.68, "y": 0, "time": 1.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 3, "y": 0, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "x": -9.68, "y": 0, "time": 2.8667}, {"curve": [0.278, 0.3, 0.618, 0.64], "x": 3, "y": 0, "time": 3.3333}, {"curve": [0.284, 0, 0.865, 0.73], "x": -30.58, "y": 0, "time": 3.5}, {"curve": [0.254, 0.42, 0.75, 1], "x": 15.54, "y": 0, "time": 3.6333}, {"x": -101.11, "y": 0, "time": 3.8333}]}, "Rau13": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 7.57, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -3.43, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": 7.57, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -3.43, "time": 3}, {"angle": 7.57, "time": 4}]}, "Rau16": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 3.17, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 7.57, "time": 0.4}, {"curve": [0.25, 0, 0.75, 1], "angle": -3.43, "time": 1.4}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.17, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 7.57, "time": 2.4}, {"curve": [0.25, 0, 0.75, 1], "angle": -3.43, "time": 3.4}, {"angle": 3.17, "time": 4}]}, "Toc12": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -1.09, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.88, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.09, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.24, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.09, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.88, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.09, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.24, "time": 3.3333}, {"angle": -1.09, "time": 4}]}, "Rau15": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 4.27, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 7.57, "time": 0.3}, {"curve": [0.25, 0, 0.75, 1], "angle": -3.43, "time": 1.3}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.27, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 7.57, "time": 2.3}, {"curve": [0.25, 0, 0.75, 1], "angle": -3.43, "time": 3.3}, {"angle": 4.27, "time": 4}]}, "Toc11": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -1.97, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.88, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.27, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.24, "time": 1.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.97, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.88, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.27, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.24, "time": 3.1667}, {"angle": -1.97, "time": 4}]}, "Rau18": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0.97, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 7.57, "time": 0.6}, {"curve": [0.25, 0, 0.75, 1], "angle": -3.43, "time": 1.6}, {"curve": [0.25, 0, 0.75, 1], "angle": 0.97, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 7.57, "time": 2.6}, {"curve": [0.25, 0, 0.75, 1], "angle": -3.43, "time": 3.6}, {"angle": 0.97, "time": 4}]}, "Rau17": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 2.07, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 7.57, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "angle": -3.43, "time": 1.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.07, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 7.57, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": -3.43, "time": 3.5}, {"angle": 2.07, "time": 4}]}, "Rong5": {"rotate": [{"curve": [0.224, 0.34, 0.75, 1], "angle": -0.92, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.5}, {"curve": [0.25, 0, 0.754, 0.67], "angle": -1.84, "time": 1.5}, {"curve": [0.224, 0.34, 0.75, 1], "angle": -0.92, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.5}, {"curve": [0.25, 0, 0.754, 0.67], "angle": -1.84, "time": 3.5}, {"angle": -0.92, "time": 4}]}, "mongtay3": {"rotate": [{"angle": 12.18, "time": 0}]}, "Toc7": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -0.34, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.77, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.34, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.52, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.34, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.77, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.34, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.52, "time": 3.3333}, {"angle": -0.34, "time": 4}]}, "Rong4": {"rotate": [{"curve": [0.254, 0.36, 0.75, 1], "angle": 0.4, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.15, "time": 0.3333}, {"curve": [0.25, 0, 0.801, 0.7], "angle": -1.11, "time": 1.3333}, {"curve": [0.254, 0.36, 0.75, 1], "angle": 0.4, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.15, "time": 2.3333}, {"curve": [0.25, 0, 0.801, 0.7], "angle": -1.11, "time": 3.3333}, {"angle": 0.4, "time": 4}]}, "Toc6": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -1.04, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.77, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0.31, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.52, "time": 1.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.04, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.77, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0.31, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.52, "time": 3.1667}, {"angle": -1.04, "time": 4}]}, "Rong3": {"rotate": [{"curve": [0.186, 0.37, 0.75, 1], "angle": 0.77, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.15, "time": 0.1667}, {"curve": [0.25, 0, 0.806, 0.7], "angle": -1.11, "time": 1.1667}, {"curve": [0.186, 0.37, 0.75, 1], "angle": 0.77, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.15, "time": 2.1667}, {"curve": [0.25, 0, 0.806, 0.7], "angle": -1.11, "time": 3.1667}, {"angle": 0.77, "time": 4}]}, "Toc5": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -1.77, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0.89, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.52, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.77, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 0.89, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.52, "time": 3}, {"angle": -1.77, "time": 4}]}, "Rong2": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 1.15, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -3.09, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.15, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -3.09, "time": 3}, {"angle": 1.15, "time": 4}]}, "mongtay2": {"rotate": [{"angle": 5.49, "time": 0}]}, "Toc4": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -1.03, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.33, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.52, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.77, "time": 1.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.03, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.33, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.52, "time": 2.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.77, "time": 3.8333}, {"angle": -1.03, "time": 4}]}, "Toc3": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -0.34, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.52, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.77, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.34, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.52, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.77, "time": 3.6667}, {"angle": -0.34, "time": 4}]}, "Duoi2": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 2.94, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.51, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.3, "time": 1.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.94, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.51, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.3, "time": 3.1667}, {"angle": 2.94, "time": 4}]}, "Toc9": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -1.96, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 1, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.24, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.88, "time": 1.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.96, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 1, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.24, "time": 2.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.88, "time": 3.8333}, {"angle": -1.96, "time": 4}]}, "Toc8": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -1.09, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.24, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.88, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.09, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.24, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.88, "time": 3.6667}, {"angle": -1.09, "time": 4}]}, "Mongtay": {"rotate": [{"angle": -12.29, "time": 0}]}, "Duoi4": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -0.08, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.51, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.3, "time": 1.5}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.08, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.51, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.3, "time": 3.5}, {"angle": -0.08, "time": 4}]}, "Duoi3": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 1.64, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.51, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.3, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.64, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.51, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.3, "time": 3.3333}, {"angle": 1.64, "time": 4}]}, "Mat": {"scale": [{"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.7333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 0.044, "time": 0.8333}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.9}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 2.7333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 0.044, "time": 2.8333}, {"x": 1, "y": 1, "time": 2.9}]}, "longho2": {"scale": [{"x": 1, "y": 1, "time": 0.4}, {"x": 1.3, "y": 1.6, "time": 0.7333}]}, "Rau4": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0.68, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.82, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.66, "time": 0.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.82, "time": 1.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0.68, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.82, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.66, "time": 2.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.82, "time": 3.4667}, {"angle": 0.68, "time": 4}]}, "Rong10": {"rotate": [{"angle": -6.79, "time": 0}]}, "Rau5": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -0.35, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0.87, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.66, "time": 0.5667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.82, "time": 1.5667}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.35, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 0.87, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.66, "time": 2.5667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.82, "time": 3.5667}, {"angle": -0.35, "time": 4}]}, "Rau2": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 2.74, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.72, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.66, "time": 0.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.82, "time": 1.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.74, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.72, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.66, "time": 2.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.82, "time": 3.2667}, {"angle": 2.74, "time": 4}]}, "Rau3": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 1.71, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.77, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.66, "time": 0.3667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.82, "time": 1.3667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.71, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.77, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.66, "time": 2.3667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.82, "time": 3.3667}, {"angle": 1.71, "time": 4}]}, "Rong11": {"rotate": [{"angle": -10.59, "time": 0}]}, "Rau1": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 5.18, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.66, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 10.02, "time": 1.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.18, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.66, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 10.02, "time": 3.1667}, {"angle": 5.18, "time": 4}]}, "Rau8": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -3.6, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.97, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.66, "time": 0.8667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.82, "time": 1.8667}, {"curve": [0.25, 0, 0.75, 1], "angle": -3.6, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.97, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.66, "time": 2.8667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.82, "time": 3.8667}, {"angle": -3.6, "time": 4}]}, "Rau9": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -4.63, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.92, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.66, "time": 0.9667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.82, "time": 1.9667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.63, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.92, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.66, "time": 2.9667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.82, "time": 3.9667}, {"angle": -4.63, "time": 4}]}, "Rau6": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -1.42, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.08, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.66, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.82, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.42, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.08, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.66, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.82, "time": 3.6667}, {"angle": -1.42, "time": 4}]}, "Rau7": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -2.5, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.02, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.66, "time": 0.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.82, "time": 1.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.5, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.02, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.66, "time": 2.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.82, "time": 3.7667}, {"angle": -2.5, "time": 4}]}, "longho4": {"scale": [{"x": 1, "y": 1, "time": 0.8}, {"x": 1.3, "y": 1.6, "time": 1.1333}]}, "longho3": {"scale": [{"x": 1, "y": 1, "time": 0.6}, {"x": 1.3, "y": 1.6, "time": 0.9333}]}, "bone10": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -24.34, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -24.34, "time": 3}, {"angle": 0, "time": 4}]}, "Duoi": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 3.51, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.3, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.51, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.3, "time": 3}, {"angle": 3.51, "time": 4}]}, "bone11": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -24.34, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -24.34, "time": 3}, {"angle": 0, "time": 4}]}, "bone12": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -24.34, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -24.34, "time": 3}, {"angle": 0, "time": 4}]}, "Rong": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 16.65, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 7.93, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 16.65, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 7.93, "time": 3}, {"x": 0, "y": 16.65, "time": 4}]}, "bone2": {"rotate": [{"curve": [0.351, 0.4, 0.757, 1], "angle": -2.48, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -13.65, "time": 0.6667}, {"curve": [0.265, 0, 0.618, 0.43], "angle": 1.95, "time": 1.6667}, {"curve": [0.351, 0.4, 0.757, 1], "angle": -2.48, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -13.65, "time": 2.6667}, {"curve": [0.265, 0, 0.618, 0.43], "angle": 1.95, "time": 3.6667}, {"angle": -2.48, "time": 4}], "translate": [{"curve": [0.351, 0.4, 0.757, 1], "x": -0.35, "y": 2.89, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.6667}, {"curve": [0.265, 0, 0.618, 0.43], "x": -0.48, "y": 4.04, "time": 1.6667}, {"curve": [0.351, 0.4, 0.757, 1], "x": -0.35, "y": 2.89, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.6667}, {"curve": [0.265, 0, 0.618, 0.43], "x": -0.48, "y": 4.04, "time": 3.6667}, {"x": -0.35, "y": 2.89, "time": 4}]}}, "deform": {"default": {"tt_taixiu_char2": {"tt_taixiu_char2": [{"curve": [0.25, 0, 0.75, 1], "time": 0}, {"offset": 452, "vertices": [1.51208, -1.35742, 1.96315, 0.5246, 2.02991, -0.09189, 1.92531, 0.64944, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.96651, 4.53337, 4.60182, 0.5544, 4.08842, 2.1833, -0.86387, 2.24951, 2.31813, 0.65785, 1.92244, 1.45266, -0.63296, 2.76848, 2.81412, 0.38112, 2.48494, 1.37436, 2.91254, -3.98004, 4.77515, -1.23325, 4.77422, -1.23729, 3.28623, -0.90216, 3.11383, 1.3846, 3.11517, 1.38191, 1.79916, 0.42706, 1.79962, 0.42554, 2.01846, -0.3457, 2.01828, -0.34731, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.79545, -0.72701, -0.79565, -0.72679, -0.47859, -0.96553, -0.37798, 0.33932, -0.50748, 0.02287, -0.50737, 0.02351], "curve": [0.25, 0, 0.75, 1], "time": 1}, {"curve": [0.25, 0, 0.75, 1], "time": 2}, {"offset": 452, "vertices": [1.51208, -1.35742, 1.96315, 0.5246, 2.02991, -0.09189, 1.92531, 0.64944, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.96651, 4.53337, 4.60182, 0.5544, 4.08842, 2.1833, -0.86387, 2.24951, 2.31813, 0.65785, 1.92244, 1.45266, -0.63296, 2.76848, 2.81412, 0.38112, 2.48494, 1.37436, 2.91254, -3.98004, 4.77515, -1.23325, 4.77422, -1.23729, 3.28623, -0.90216, 3.11383, 1.3846, 3.11517, 1.38191, 1.79916, 0.42706, 1.79962, 0.42554, 2.01846, -0.3457, 2.01828, -0.34731, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.79545, -0.72701, -0.79565, -0.72679, -0.47859, -0.96553, -0.37798, 0.33932, -0.50748, 0.02287, -0.50737, 0.02351], "curve": [0.25, 0, 0.75, 1], "time": 3}, {"time": 4}]}}}}}}