{"skins": {"default": {"Sparkle 4": {"Sparkle": {"width": 53, "y": 0.5, "height": 54}}, "Sparkle 5": {"Sparkle": {"width": 53, "y": 0.5, "height": 54}}, "BG-che": {"BG-che": {"color": "ce3a3aff", "vertices": [-86.11, 206.8, -83.4, -203.38, 85.59, -205.15, 81.07, 202.79], "end": "BG", "type": "clipping", "vertexCount": 4}}, "Sparkle 2": {"Sparkle": {"width": 53, "y": 0.5, "height": 54}}, "Sparkle 3": {"Sparkle": {"width": 53, "y": 1.13, "height": 54}}, "Rock/Rock 4": {"Rock/Rock 4": {"width": 65, "height": 212}}, "eff_mask": {"eff_mask": {"color": "3ace4fff", "vertices": [-79.11, -100.3, -84.34, -107.76, -84.18, -144.12, -77.95, -162.01, 84.28, -162.89, 84.8, -108.7, 79.08, -99.78, 83.23, 186.02, 74.28, 226.14, 67.94, 226.04, 69.29, 229.87, 66.74, 229.8, 68.05, 233.74, 64.54, 236.12, 59.41, 236.01, 57.09, 239.87, 53.23, 240.09, 49.76, 243.39, 45.02, 243.04, 48.28, 235.61, 55.65, 232.06, -86.67, 182.86], "end": "set2", "type": "clipping", "vertexCount": 22}}, "Rock/Rock 3": {"Rock/Rock 3": {"rotation": 90, "width": 165, "height": 90}}, "Sparkle 1": {"Sparkle": {"width": 53, "y": 34.62, "height": 54}}, "thanh-ngangBright2": {"thanh-ngangBright": {"scaleY": -1, "width": 200, "y": -182.49, "height": 39}}, "Rock/Rock 2": {"Rock/Rock 2": {"rotation": -90, "width": 158, "height": 68}}, "Rock/Rock 1": {"Rock/Rock 1": {"width": 52, "height": 227}}, "hong-chenguoi": {"hong-chenguoi": {"color": "ce3a3aff", "vertices": [253.39, 45.57, 310.93, 5.71, 297.4, -61.44, 240.5, -85.52, 213.32, -122.13, 59.82, -147.71, -8.35, -102.75, -221.13, -33.16, -195.35, 150.13, 42.76, 191.26, 251.34, 98.42], "end": "set2", "type": "clipping", "vertexCount": 11}}, "Rock/Rock 6": {"Rock/Rock 6": {"width": 81, "height": 203}}, "Rock/Rock 5": {"Rock/Rock 5": {"width": 87, "height": 228}}, "long2": {"long2": {"x": 0.51, "width": 19, "y": 23.52, "height": 51}}, "long3": {"long3": {"x": 2.89, "width": 21, "y": 17.47, "height": 44}}, "long1": {"long1": {"x": 1.5, "width": 19, "y": 30.15, "height": 66}}, "chanphai": {"chanphai": {"triangles": [2, 0, 1, 2, 3, 0, 0, 3, 5, 3, 4, 5], "uvs": [0.99999, 0.707, 1, 1, 0, 1, 0, 0.70536, 0, 0, 1, 0], "vertices": [2, 14, 176, 31.02, 0.24462, 15, -3.28, 31.15, 0.75538, 1, 15, 76.91, 29.42, 1, 1, 15, 75.32, -40.56, 1, 2, 14, 171.39, -38.95, 0.39888, 15, -7.84, -38.82, 0.60112, 1, 14, -21.85, -34.64, 1, 1, 14, -20.21, 35.34, 1], "width": 70, "edges": [2, 4, 8, 10, 4, 6, 6, 8, 2, 0, 0, 10, 6, 0], "type": "mesh", "hull": 6, "height": 130}}, "than": {"than": {"triangles": [12, 0, 14, 12, 14, 15, 11, 0, 12, 9, 11, 12, 10, 11, 9, 3, 1, 2, 4, 1, 3, 16, 0, 1, 16, 1, 4, 14, 0, 16, 15, 14, 16, 13, 16, 4, 13, 4, 5, 15, 16, 13, 12, 15, 13, 13, 9, 12, 8, 9, 13, 13, 5, 6, 6, 7, 8, 6, 8, 13], "uvs": [0.28392, 0.21084, 0.5148, 0.15935, 0.55837, 0.03576, 0.7849, 0.09343, 0.75005, 0.25822, 1, 0.39622, 1, 1, 0.46906, 1, 0.46688, 0.87615, 0.31441, 0.65987, 0.17066, 0.83701, 1e-05, 0.6276, 0.35144, 0.60632, 0.52571, 0.69348, 0.42261, 0.44161, 0.42604, 0.51961, 0.49493, 0.39817], "vertices": [2, 4, 101.27, 55.33, 0.83, 7, -4.12, -25.49, 0.17, 1, 4, 97.46, 14.23, 1, 2, 4, 116.6, -0.2, 0.97058, 7, -59.16, -8.5, 0.02942, 1, 4, 93.99, -34.18, 1, 2, 3, 130.52, -2.99, 0.00051, 4, 67.18, -18.78, 0.99949, 2, 3, 100.88, -43.72, 0.19846, 4, 29.26, -51.92, 0.80154, 1, 3, -9.65, -32.56, 1, 2, 3, -0.37, 59.35, 0.76922, 4, -46.64, 71.02, 0.23078, 2, 3, 22.34, 57.44, 0.63818, 4, -24.92, 64.13, 0.36182, 3, 3, 64.6, 79.84, 0.0159, 4, 21.25, 76.61, 0.58, 7, 56.91, 30.46, 0.4041, 2, 4, -1.68, 110.7, 0.00076, 7, 97.99, 31.41, 0.99924, 1, 7, 86.59, -15.88, 1, 3, 3, 73.75, 72.44, 0.02713, 4, 28.54, 67.36, 0.2299, 7, 45.2, 29.3, 0.74297, 1, 4, 3.69, 43.72, 1, 3, 3, 102.66, 57.08, 0.00035, 4, 53.33, 45.97, 0.35256, 7, 13.82, 19.93, 0.64709, 3, 3, 88.32, 57.93, 0.04449, 4, 39.54, 49.98, 0.81551, 7, 24.63, 29.4, 0.14, 1, 4, 56.9, 31.5, 1], "width": 174, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 20, 18, 12, 14, 16, 14, 18, 24, 10, 12, 16, 18, 20, 22, 0, 32], "type": "mesh", "hull": 12, "height": 184}}, "dau": {"dau": {"triangles": [19, 2, 3, 20, 2, 19, 18, 19, 3, 21, 2, 20, 15, 20, 19, 15, 19, 18, 21, 20, 15, 14, 15, 18, 4, 21, 15, 13, 5, 14, 4, 15, 14, 5, 4, 14, 7, 4, 5, 18, 13, 14, 6, 5, 13, 8, 7, 5, 8, 5, 6, 17, 16, 18, 13, 18, 16, 3, 17, 18, 10, 16, 17, 22, 7, 8, 22, 8, 6, 9, 13, 16, 9, 16, 10, 11, 10, 17, 12, 9, 10, 12, 10, 11, 23, 9, 12, 23, 12, 11, 0, 17, 3, 11, 17, 0, 23, 11, 0, 1, 2, 21, 7, 1, 21, 7, 21, 4, 1, 7, 22, 23, 1, 22, 6, 13, 9, 6, 9, 23, 23, 22, 6, 0, 1, 23], "uvs": [0.99999, 1, 1e-05, 1, 0, 0, 1, 0, 0.55244, 0.56511, 0.63581, 0.5681, 0.6782, 0.62339, 0.55385, 0.59948, 0.61037, 0.63534, 0.75733, 0.65776, 0.79972, 0.6473, 0.82515, 0.69063, 0.77852, 0.70707, 0.6895, 0.60845, 0.61885, 0.54419, 0.56657, 0.52776, 0.77711, 0.63833, 0.8407, 0.63983, 0.76633, 0.32453, 0.68424, 0.23184, 0.48807, 0.27156, 0.38233, 0.39221, 0.60452, 0.65442, 0.77564, 0.72554], "vertices": [13.72, -45.29, -18.7, 40.81, 62.72, 71.46, 95.14, -14.64, 34.62, 6.57, 37.08, -0.7, 33.95, -6.04, 31.87, 5.4, 30.78, -0.57, 33.72, -13.91, 35.94, -17.24, 33.24, -20.75, 30.39, -17.24, 35.53, -6.56, 38.47, 1.5, 38.12, 6.5, 35.94, -15.01, 37.88, -20.54, 61.14, -4.47, 66.03, 5.44, 56.43, 21.11, 43.18, 26.52, 29.04, -0.65, 28.79, -17.56], "width": 92, "edges": [4, 6, 2, 4, 0, 6, 8, 10, 10, 12, 8, 14, 14, 16, 16, 12, 18, 20, 20, 22, 22, 24, 24, 18, 26, 28, 28, 30, 32, 34, 46, 18, 46, 22, 44, 12, 44, 14, 0, 2], "type": "mesh", "hull": 4, "height": 87}}, "Wild Bright": {"Wild Bright": {"x": 1.55, "width": 192, "y": 31.62, "height": 55}}, "long6": {"long6": {"x": -11.3, "width": 29, "y": 15.67, "height": 43}}, "longduoiST": {"longduoiST": {"triangles": [12, 13, 25, 20, 25, 26, 11, 12, 25, 24, 11, 25, 19, 24, 25, 19, 25, 20, 10, 11, 24, 9, 10, 24, 29, 19, 20, 29, 20, 30, 7, 29, 30, 7, 30, 6, 29, 9, 19, 19, 9, 24, 8, 29, 7, 29, 8, 9, 27, 26, 14, 13, 14, 26, 21, 26, 27, 25, 13, 26, 21, 22, 31, 20, 26, 21, 30, 20, 21, 30, 21, 31, 6, 30, 31, 31, 22, 32, 4, 32, 3, 5, 31, 32, 4, 5, 32, 6, 31, 5, 28, 17, 18, 0, 28, 18, 27, 15, 16, 28, 27, 16, 28, 16, 17, 23, 28, 0, 22, 27, 28, 23, 0, 1, 23, 22, 28, 27, 14, 15, 21, 27, 22, 23, 1, 2, 32, 22, 23, 32, 23, 2, 32, 2, 3], "uvs": [1, 0.19676, 1, 0.33128, 1, 0.54788, 1, 0.68639, 0.9093, 0.75527, 0.72747, 0.78807, 0.49217, 0.80775, 0.32104, 0.88647, 0.14991, 0.94551, 0.06078, 1, 0, 0.87335, 0, 0.70935, 0.0786, 0.55191, 0.15704, 0.40759, 0.25687, 0.26983, 0.35669, 0.16487, 0.56347, 0.05663, 0.82017, 0, 1, 0, 0.1089, 0.82604, 0.25759, 0.66416, 0.44346, 0.50228, 0.6442, 0.386, 0.88955, 0.32672, 0.05438, 0.77588, 0.17086, 0.60716, 0.32946, 0.45896, 0.52772, 0.31076, 0.86725, 0.19904, 0.17333, 0.88076, 0.38894, 0.74396, 0.6442, 0.64136, 0.85486, 0.56612], "vertices": [3, 93, -9.55, -22.93, 0.99975, 94, -113.8, -9.24, 0.00024, 95, -172.01, 20.36, 1e-05, 3, 93, 3.59, 15.36, 0.97466, 94, -95, 26.62, 0.02408, 95, -147.39, 52.49, 0.00126, 3, 93, 24.76, 77.03, 0.71, 94, -64.75, 84.37, 0.25742, 95, -107.73, 104.24, 0.03258, 3, 93, 38.29, 116.46, 0.53691, 94, -45.4, 121.29, 0.43, 95, -82.38, 137.33, 0.03309, 3, 93, 68.78, 127.91, 0.14826, 94, -13.53, 128, 0.69147, 95, -49.83, 138.51, 0.16026, 4, 93, 119.6, 120.91, 0.03089, 94, 35.65, 113.38, 0.6367, 95, -3.86, 115.72, 0.32272, 96, -41.82, 132.9, 0.00968, 3, 94, 96.11, 88.38, 0.19833, 95, 51.46, 80.8, 0.57226, 96, 1.41, 83.79, 0.22941, 3, 94, 149.08, 87.37, 0.00945, 95, 103.48, 70.78, 0.18951, 96, 48.51, 59.52, 0.80104, 2, 95, 151.91, 56.06, 0.00372, 96, 90.82, 31.76, 0.99628, 1, 96, 118.63, 21.51, 1, 1, 96, 97.79, -14.57, 1, 2, 95, 141.62, -25.61, 0.00082, 96, 57.94, -43.7, 0.99918, 4, 93, 266.47, -4.65, 0.00128, 94, 161.81, -32.98, 0.00225, 95, 95.52, -49.98, 0.3523, 96, 6.84, -54.1, 0.64417, 4, 93, 231.83, -38.69, 0.05102, 94, 122.41, -61.37, 0.0742, 95, 51.86, -71.25, 0.78572, 96, -41.05, -62.2, 0.08906, 4, 93, 192.22, -68.93, 0.26562, 94, 78.68, -85.27, 0.22766, 95, 4.7, -87.35, 0.50647, 96, -90.83, -64.36, 0.00026, 3, 93, 155.82, -89.84, 0.56567, 94, 39.53, -100.42, 0.21201, 95, -36.45, -95.61, 0.22232, 3, 93, 91.09, -102.06, 0.9526, 94, -26.31, -102.7, 0.02742, 95, -101.72, -86.64, 0.01998, 3, 93, 18.32, -95.11, 0.99997, 94, -97.18, -84.81, 2e-05, 95, -168.51, -56.94, 0, 3, 93, -28.77, -78.94, 0.99985, 94, -141.28, -61.7, 0.00015, 95, -208.03, -26.65, 0, 2, 95, 139.05, 20.61, 0.00021, 96, 68.49, 1.37, 0.99979, 2, 95, 76.73, 6.98, 0.20122, 96, 4.86, 5.85, 0.79878, 2, 93, 166.06, 14.02, 6e-05, 95, 6.24, -0.39, 0.99994, 2, 93, 102.13, -1.04, 0.52, 94, -0.09, -4.52, 0.48, 3, 93, 32.08, 4.14, 0.99188, 94, -68.55, 11.21, 0.00772, 95, -123.94, 32.8, 0.0004, 1, 96, 65.22, -19.73, 1, 4, 93, 247.71, 19.37, 0.00021, 94, 146.9, -6.39, 0.00031, 95, 85.36, -21.24, 0.29234, 96, 5.19, -23.66, 0.70714, 4, 93, 191.69, -8.56, 0.04498, 94, 87.3, -25.52, 0.11741, 95, 23.37, -29.94, 0.83563, 96, -56.74, -14.54, 0.00198, 3, 93, 125.28, -32.93, 0.62868, 94, 17.96, -39.55, 0.31292, 95, -47.34, -31.95, 0.0584, 3, 93, 25.44, -34.21, 0.92, 94, -80.92, -25.7, 0.08, 95, -142.42, -1.46, 0, 2, 95, 134.9, 44.54, 0.01495, 96, 71.26, 25.49, 0.98505, 3, 94, 112.52, 58.11, 0.05806, 95, 62.47, 48.17, 0.56651, 96, 2.78, 49.39, 0.37544, 4, 93, 127.08, 71.66, 0.00983, 94, 35.58, 63.56, 0.63969, 95, -12.42, 66.65, 0.34081, 96, -63.86, 88.22, 0.00967, 3, 93, 64.55, 69.17, 0.30201, 94, -26.6, 70.57, 0.60666, 95, -72.49, 84.15, 0.09132], "width": 115, "edges": [34, 36, 20, 22, 6, 8, 30, 32, 32, 34, 26, 28, 28, 30, 22, 24, 24, 26, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 38, 18, 2, 0, 0, 36, 56, 54, 2, 4, 4, 6, 58, 60, 18, 58, 18, 48], "type": "mesh", "hull": 19, "height": 125}}, "long4": {"long4": {"x": -3.62, "width": 22, "y": 27.79, "height": 68}}, "chantrai": {"chantrai": {"triangles": [1, 3, 0, 2, 3, 1, 3, 4, 5, 3, 5, 0], "uvs": [0.99999, 0.63791, 0.62506, 1, 0, 1, 0.33166, 0.60998, 0, 0, 0.76567, 0], "vertices": [1, 16, 145.86, 23.99, 1, 2, 16, 229.07, -31.11, 1e-05, 17, 89.44, 35.31, 0.99999, 1, 17, 114.49, -15.75, 1, 2, 16, 124.61, -33.45, 0.09962, 17, 10.51, -33.16, 0.90038, 1, 16, -36.05, -25.86, 1, 1, 16, -19.76, 41.89, 1], "width": 91, "edges": [2, 4, 2, 0, 8, 10, 4, 6, 0, 6, 6, 8, 10, 0], "type": "mesh", "hull": 6, "height": 130}}, "long5": {"long5": {"x": -7.56, "width": 30, "y": 20.83, "height": 52}}, "riu": {"riu": {"rotation": -2.03, "x": 21.68, "width": 287, "y": -18.28, "height": 107}}, "set3": {"set2": {"scaleX": 3, "scaleY": 3, "x": 138.83, "width": 156, "y": -8.13, "height": 38}, "set1": {"scaleX": 3, "scaleY": 3, "x": 138.83, "width": 156, "y": -8.13, "height": 41}}, "set2": {"set2": {"scaleX": 2, "scaleY": 2, "x": 138.83, "width": 156, "y": -8.13, "height": 38}, "set1": {"scaleX": 2, "scaleY": 2, "x": 138.83, "width": 156, "y": -8.13, "height": 41}}, "thanh-ngangBright": {"thanh-ngangBright": {"width": 200, "y": 221.5, "height": 39}}, "thanh-ngang": {"thanh-ngang": {"width": 200, "y": -4.54, "height": 39}}, "hong": {"hong": {"triangles": [0, 12, 10, 0, 10, 11, 14, 12, 0, 9, 10, 12, 1, 14, 0, 8, 9, 12, 13, 12, 14, 8, 12, 13, 2, 14, 1, 4, 14, 2, 13, 14, 4, 5, 13, 4, 3, 4, 2, 8, 6, 7, 5, 8, 13, 5, 6, 8], "uvs": [0.7971, 0.21406, 0.86858, 0.40817, 0.86489, 0.64853, 0.86212, 0.95268, 0.7361, 0.72973, 0.35544, 0.81239, 0.08631, 1, 0, 1, 0.02581, 0.62041, 0.13055, 0.30668, 0.20754, 0, 0.74251, 0, 0.22644, 0.44932, 0.29878, 0.63629, 0.73081, 0.48204], "vertices": [21.26, -43.59, -12.7, -52.18, -52.67, -46.44, -103.3, -39.51, -63.3, -21.85, -68.5, 47.5, -93.69, 99.26, -91.75, 114.59, -29.07, 101.97, 20.86, 76.73, 70.23, 56.57, 58.17, -38.43, -5.08, 62.72, -37.87, 53.83, -21.9, -26.15], "width": 179, "edges": [12, 14, 12, 10, 20, 22, 22, 0, 18, 20, 4, 28, 0, 2, 14, 16, 16, 18, 2, 4, 4, 6, 6, 8, 8, 10], "type": "mesh", "hull": 12, "height": 168}}, "Frame Light Left": {"Frame Light": {"x": 0.5, "width": 31, "y": 0.5, "height": 225}}, "longtrenST": {"longtrenST": {"triangles": [4, 46, 3, 27, 46, 47, 29, 38, 28, 49, 48, 5, 5, 48, 4, 49, 29, 48, 29, 28, 48, 48, 47, 4, 48, 28, 47, 28, 27, 47, 4, 47, 46, 6, 49, 5, 6, 29, 49, 45, 44, 3, 44, 43, 2, 44, 2, 3, 26, 25, 45, 25, 44, 45, 35, 24, 25, 24, 43, 25, 25, 43, 44, 42, 20, 0, 33, 21, 22, 22, 41, 23, 41, 22, 19, 22, 21, 19, 41, 19, 20, 41, 20, 42, 9, 31, 8, 8, 31, 7, 9, 40, 31, 9, 10, 40, 31, 30, 7, 7, 30, 6, 31, 40, 30, 40, 39, 30, 40, 10, 39, 30, 29, 6, 30, 39, 29, 10, 11, 39, 39, 38, 29, 39, 11, 38, 11, 12, 38, 38, 37, 28, 38, 12, 37, 28, 37, 27, 12, 13, 37, 13, 36, 37, 27, 36, 26, 27, 37, 36, 13, 14, 36, 27, 26, 46, 26, 45, 46, 46, 45, 3, 14, 35, 36, 36, 25, 26, 36, 35, 25, 15, 34, 14, 14, 34, 35, 16, 33, 15, 15, 33, 34, 34, 23, 35, 35, 23, 24, 17, 32, 16, 16, 32, 33, 33, 22, 34, 34, 22, 23, 23, 42, 24, 24, 42, 43, 43, 1, 2, 17, 18, 32, 32, 21, 33, 23, 41, 42, 42, 0, 43, 43, 0, 1, 32, 18, 21, 18, 19, 21], "uvs": [0.71249, 0.05936, 0.81284, 0.12736, 0.92375, 0.22839, 1, 0.3333, 1, 0.68496, 0.91055, 0.80348, 0.81284, 0.84816, 0.73098, 0.94919, 0.68608, 1, 0.59102, 0.95113, 0.55669, 0.81513, 0.58309, 0.7141, 0.63063, 0.62862, 0.6095, 0.56839, 0.53292, 0.47125, 0.37975, 0.39353, 0.2662, 0.31388, 0.1817, 0.22839, 0.15529, 0.10405, 0.25036, 0, 0.56989, 0, 0.27287, 0.11978, 0.43054, 0.15168, 0.56259, 0.21403, 0.68675, 0.28363, 0.77347, 0.37063, 0.8188, 0.46633, 0.84048, 0.55913, 0.83062, 0.65483, 0.79318, 0.74328, 0.72814, 0.83173, 0.67098, 0.89263, 0.23345, 0.18068, 0.35565, 0.22563, 0.48572, 0.30103, 0.61186, 0.38513, 0.71434, 0.48953, 0.72617, 0.58523, 0.71237, 0.68093, 0.66507, 0.77518, 0.6158, 0.85493, 0.5074, 0.06033, 0.6493, 0.13428, 0.7577, 0.20678, 0.86413, 0.30393, 0.90946, 0.40398, 0.93114, 0.50258, 0.93311, 0.60263, 0.90946, 0.69978, 0.85822, 0.78823], "vertices": [1, 97, 24.66, -12.8, 1, 2, 92, 83.46, -62.48, 0.67, 97, 19.71, -25.96, 0.33, 2, 92, 69.08, -73.61, 0.32, 98, 23.89, 2.85, 0.68, 2, 92, 54.24, -81.16, 0.45, 98, 26.76, -13.55, 0.55, 2, 92, 5.01, -80.15, 0.37, 99, 25.3, -27.4, 0.63, 2, 92, -11.39, -70.59, 0.37, 99, 21.12, -45.91, 0.63, 2, 92, -17.43, -60.4, 0.71, 99, 13.22, -54.75, 0.29, 2, 92, -31.4, -51.68, 0.71, 99, 9.11, -70.69, 0.29, 1, 92, -38.42, -46.91, 1, 1, 92, -31.38, -37.26, 1, 1, 92, -12.27, -34.12, 1, 1, 92, 1.82, -37.13, 1, 1, 92, 13.68, -42.27, 1, 1, 92, 22.16, -40.27, 1, 1, 92, 35.92, -32.66, 1, 1, 92, 47.12, -17.12, 1, 1, 92, 58.51, -5.65, 1, 1, 92, 70.66, 2.8, 1, 1, 92, 88.12, 5.16, 1, 2, 92, 102.48, -4.92, 0.66891, 97, 14.51, 34.44, 0.33109, 2, 92, 101.8, -37.83, 0.27265, 97, 26.86, 3.93, 0.72735, 2, 92, 85.66, -6.9, 0.4616, 97, -0.17, 26, 0.5384, 2, 92, 80.86, -23.04, 0.15, 97, 1.79, 9.27, 0.85, 2, 92, 71.86, -36.46, 0.78368, 97, -1.2, -6.61, 0.21632, 2, 92, 61.85, -49.05, 0.24, 98, -1.71, 3.14, 0.76, 2, 92, 49.49, -57.73, 0.23, 98, 2.96, -11.23, 0.77, 2, 92, 36, -62.12, 0.63, 98, 3.21, -25.41, 0.37, 2, 92, 22.96, -64.08, 0.53, 99, 4.58, -15.11, 0.47, 1, 99, 7.37, -28.26, 1, 2, 92, -2.71, -58.68, 0.37, 99, 7.15, -41.22, 0.63, 2, 92, -14.95, -51.73, 0.62, 99, 4.2, -54.99, 0.38, 2, 92, -23.36, -45.67, 0.71, 99, 0.95, -64.83, 0.29, 2, 92, 77.22, -2.66, 0.90645, 97, -9.59, 26.56, 0.09355, 2, 92, 70.67, -15.12, 0.72273, 97, -10.7, 12.54, 0.27727, 1, 92, 59.84, -28.29, 1, 1, 92, 47.8, -41.04, 1, 1, 92, 32.97, -51.29, 1, 1, 92, 19.55, -52.24, 1, 1, 92, 6.19, -50.54, 1, 1, 92, -6.9, -45.4, 1, 1, 92, -17.96, -40.09, 1, 2, 92, 93.49, -31.22, 0.15, 97, 16.61, 6.73, 0.85, 2, 92, 82.84, -45.62, 0.65674, 97, 12.5, -10.71, 0.34326, 2, 92, 72.46, -56.57, 0.76, 98, 8.59, 11.07, 0.24, 2, 92, 58.63, -67.25, 0.36, 98, 14.75, -5.27, 0.64, 3, 92, 44.53, -71.63, 0.5103, 98, 14.81, -20.04, 0.2997, 99, 5.29, 7.73, 0.19, 2, 92, 30.69, -73.58, 0.16, 99, 11.31, -4.89, 0.84, 2, 92, 16.68, -73.5, 0.37, 99, 15.45, -18.27, 0.63, 2, 92, 3.13, -70.78, 0.37, 99, 16.93, -32.01, 0.63, 2, 92, -9.14, -65.25, 0.37, 99, 15.35, -45.38, 0.63], "width": 103, "edges": [8, 6, 38, 40, 36, 38, 2, 4, 4, 6, 40, 0, 0, 2, 16, 18, 34, 36, 8, 10, 10, 12, 12, 14, 14, 16, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18], "type": "mesh", "hull": 21, "height": 140}}, "set1": {"set2": {"scaleX": 2, "scaleY": 2, "x": 138.83, "width": 156, "y": -8.13, "height": 38}, "set1": {"scaleX": 2, "scaleY": 2, "x": 138.83, "width": 156, "y": -8.13, "height": 41}}, "thanh-docBright": {"thanh-docBright": {"x": -84.21, "width": 23, "y": 20.59, "height": 381}}, "vaithatlung": {"vaithatlung": {"triangles": [8, 5, 6, 7, 8, 6, 9, 10, 3, 9, 3, 4, 8, 9, 4, 8, 4, 5, 10, 11, 2, 3, 10, 2, 11, 12, 1, 11, 1, 2, 13, 14, 15, 13, 15, 0, 12, 13, 0, 12, 0, 1], "uvs": [0.57077, 0.07405, 0.61798, 0.22809, 0.68741, 0.40893, 0.74953, 0.57836, 0.84089, 0.70869, 1, 0.8765, 1, 1, 0, 1, 0, 0.8941, 1e-05, 0.73639, 0, 0.57836, 1e-05, 0.41707, 0, 0.23298, 0, 0.08146, 0, 0, 0.55951, 0], "vertices": [1, 18, -11.2, -26.03, 1, 2, 19, 37.23, 29.15, 0.91677, 20, -1.5, 29.21, 0.08323, 4, 19, 80.98, 34.12, 0.09278, 20, 42.34, 33.33, 0.42877, 21, -1.38, 33.31, 0.46613, 22, -33.69, 37.63, 0.01232, 5, 19, 121.95, 38.47, 0.0002, 20, 83.39, 36.88, 0.00303, 21, 39.61, 37.53, 0.34419, 22, 7.51, 36.89, 0.63082, 23, -29.39, 36.63, 0.02175, 3, 21, 71.42, 45.42, 0.01033, 22, 40.03, 40.91, 0.58821, 23, 3.1, 40.94, 0.40146, 4, 22, 82.7, 50.46, 0.00942, 23, 45.68, 50.88, 0.01725, 45, -9.9, 48.18, 0.00019, 46, -2.7, 13.69, 0.97314, 2, 45, 19.31, 43.15, 0.14707, 46, 26.51, 8.66, 0.85293, 2, 22, 92.72, -60.12, 0.00075, 44, 10.2, -23.14, 0.99925, 3, 21, 110.47, -47.05, 0.00027, 22, 67.72, -55.57, 0.57995, 23, 31.66, -55.28, 0.41978, 3, 21, 72.69, -44.79, 0.09073, 22, 30.48, -48.8, 0.74282, 23, -5.64, -48.85, 0.16645, 4, 20, 77.32, -43.09, 0.02927, 21, 34.83, -42.53, 0.54648, 22, -6.84, -42.01, 0.42379, 23, -43.01, -42.41, 0.00047, 3, 20, 38.72, -40.16, 0.59257, 21, -3.81, -40.22, 0.39133, 22, -44.92, -35.09, 0.01611, 4, 18, -41.34, 39.36, 0.0255, 19, 34.68, -36.94, 0.0796, 20, -5.34, -36.81, 0.89321, 21, -47.92, -37.59, 0.0017, 1, 18, -5.27, 34.78, 1, 1, 18, 14.13, 32.32, 1, 1, 18, 6.59, -27.07, 1], "width": 107, "edges": [28, 30, 2, 4, 4, 6, 6, 8, 12, 10, 8, 18, 18, 20, 6, 20, 20, 22, 4, 22, 22, 24, 2, 24, 2, 0, 0, 30, 24, 26, 26, 28, 0, 26, 14, 16, 16, 18, 8, 10, 12, 14, 16, 10], "type": "mesh", "hull": 16, "height": 240}}, "Wild": {"Wild": {"x": 1.55, "width": 192, "y": 31.62, "height": 55}}, "BG": {"BG": {"scaleX": 1.02, "scaleY": 1.71, "width": 163, "y": 3, "height": 230}}, "Wild Mask": {"Wild Mask": {"color": "ffff00ff", "vertices": [9.22, 75.47, 9.36, -74.45, -4.15, -74.64, -3.49, 73.48, -5.99, 73.53, -6.24, -74.53, -9.54, -74.6, -9.03, 75.42], "end": "<PERSON>", "type": "clipping", "vertexCount": 8}}, "toc": {"toc": {"triangles": [11, 7, 0, 6, 7, 11, 12, 11, 0, 10, 11, 12, 6, 11, 10, 5, 6, 10, 9, 12, 13, 10, 12, 9, 19, 5, 10, 8, 13, 0, 9, 13, 8, 13, 12, 0, 14, 5, 19, 4, 5, 14, 14, 18, 4, 19, 18, 14, 4, 18, 3, 15, 10, 9, 15, 9, 16, 19, 10, 15, 18, 15, 16, 15, 18, 19, 17, 16, 9, 3, 16, 17, 18, 16, 3, 1, 8, 0, 17, 8, 1, 17, 9, 8, 2, 17, 1, 3, 17, 2], "uvs": [0.78013, 0.08682, 0.85272, 0.38799, 0.77045, 0.55292, 0.60512, 0.66904, 0, 1, 0, 0.53379, 0, 0.29238, 0, 0, 0.61559, 0.39994, 0.47524, 0.49316, 0.22601, 0.46926, 0.15342, 0.23741, 0.28408, 0.31867, 0.49218, 0.34019, 0.09292, 0.75848, 0.33006, 0.58638, 0.50186, 0.54814, 0.65672, 0.45242, 0.43591, 0.71467, 0.21632, 0.62501], "vertices": [3, 6, 11.63, 15.21, 0.09077, 24, -5.64, -1.16, 0.84866, 31, -14.19, 4.57, 0.06057, 3, 6, -9.41, 1.01, 0.40614, 27, -4.15, 7.19, 0.5791, 28, -18.17, 1.63, 0.01476, 4, 6, -24.42, 2.48, 0.07747, 27, 10.6, 10.32, 0.58906, 28, -5.13, 9.18, 0.32623, 30, -32.38, 20.98, 0.00724, 5, 6, -38.05, 11.66, 0.00499, 27, 26.37, 5.69, 0.04039, 28, 11.3, 9.68, 0.76536, 29, -6.18, 8.09, 0.11229, 30, -16.21, 18.05, 0.07697, 1, 30, 37.46, 1.97, 1, 4, 29, 38.54, -14.84, 0.18853, 30, 10.12, -24.75, 0.32237, 26, 41.36, 10.58, 0.2839, 33, 13.89, 31.49, 0.2052, 4, 29, 33.62, -34.01, 0.0458, 30, -4.04, -38.59, 0.03262, 26, 40.81, -9.2, 0.19204, 33, 23.07, 13.95, 0.72954, 2, 31, 27.15, -43.75, 0.00075, 33, 34.2, -7.29, 0.99925, 4, 27, 8.5, -7.29, 0.31186, 28, -1.65, -8.21, 0.09187, 24, 22.6, 5.09, 0.01093, 25, 6.43, 6.35, 0.58533, 4, 28, 12.05, -8.16, 0.31598, 29, 0.43, -8.49, 0.20467, 25, 20.06, 4.98, 0.0211, 26, 2.79, 8.34, 0.45826, 4, 29, 19.49, -15.41, 0.26095, 30, -6.46, -15.36, 0.06554, 26, 22.92, 5.81, 0.54339, 33, 0.13, 18.31, 0.13012, 5, 29, 20.46, -35.29, 0.00927, 30, -15.95, -32.85, 0.00548, 26, 28.26, -13.36, 0.08098, 31, 31.35, -21.04, 3e-05, 33, 14.16, 4.19, 0.90424, 5, 29, 11.86, -26.2, 0.00079, 30, -18.58, -20.62, 0.0015, 26, 17.87, -6.4, 0.34142, 32, 17.19, 3.81, 0.01754, 33, 1.69, 5.19, 0.63875, 4, 25, 11, -3.8, 0.43831, 26, 1.07, -4.16, 0.20795, 31, 17.04, 3.84, 0.01498, 32, 0.34, 5.57, 0.33876, 4, 29, 35.83, 4.88, 0.041, 30, 18.03, -6.49, 0.914, 26, 34.36, 29.21, 0.03271, 33, -1.33, 44.32, 0.0123, 3, 29, 13.72, -4.02, 0.85484, 26, 14.76, 15.65, 0.14382, 33, -11.8, 22.9, 0.00134, 4, 28, 12.76, -3.21, 0.6954, 29, -0.54, -3.59, 0.17437, 25, 21.28, 9.82, 0.01034, 26, 0.76, 12.9, 0.11989, 4, 27, 9.82, -2.01, 0.81047, 28, -2.03, -2.78, 0.0797, 24, 23.77, 10.4, 0.00262, 25, 6.62, 11.79, 0.1072, 3, 28, 24.78, 5.19, 0.09947, 29, 8.03, 8.31, 0.54251, 30, -3.96, 10.87, 0.35801, 4, 29, 23.43, -3.24, 0.36283, 30, 3.22, -6.99, 0.48504, 26, 24.06, 18.55, 0.12606, 33, -5.11, 29.98, 0.02607], "width": 81, "edges": [16, 18, 18, 20, 8, 10, 20, 10, 10, 12, 12, 14, 12, 22, 22, 24, 24, 26, 8, 28, 30, 32, 32, 34, 2, 4, 4, 6, 6, 36, 28, 38, 38, 30, 6, 8, 14, 0, 0, 2], "type": "mesh", "hull": 8, "height": 82}}, "vangnon": {"vang": {"rotation": -74.46, "x": 19.6, "width": 65, "y": 2.05, "height": 59}}, "Gem Spartkle 2": {"Gem Sparkle": {"x": 0.95, "width": 55, "y": 0.3, "height": 60}}, "Smoke 3": {"Smoke 3": {"scaleX": 2, "path": "Smoke", "scaleY": -2, "x": 150, "width": 257, "height": 129}}, "Smoke 2": {"Smoke 2": {"scaleX": 2, "path": "Smoke", "scaleY": 2, "x": 350, "width": 257, "height": 129}}, "Dust 1": {"Dust 1": {"path": "Dust", "width": 234, "height": 71}}, "Rock Mask": {"Rock Mask": {"color": "0000ffff", "vertices": [-75.89, -186.56, -87.8, -135.53, -88.07, 185.19, -73.79, 224.41, 71.47, 225.04, 88.08, 185.36, 86.96, -142.55, 79.72, -186.63], "end": "Rock/Rock 6", "type": "clipping", "vertexCount": 8}}, "Smoke 1": {"Smoke 1": {"scaleX": 2, "path": "Smoke", "scaleY": 2, "width": 257, "height": 129}}, "thanh-ngang2": {"thanh-ngang": {"scaleY": -1, "width": 200, "y": 3.49, "height": 39}}, "Axe Heat": {"Axe Heat": {"rotation": -1.61, "x": 80.95, "width": 167, "y": -41.29, "height": 55}}, "Dust 2": {"Dust 1": {"path": "Dust", "width": 234, "height": 71}}, "Dust 3": {"Dust 1": {"path": "Dust", "width": 234, "height": 71}}, "thanh-doc2": {"thanh-doc": {"scaleX": -1, "scaleY": -1, "x": 0.66, "width": 23, "y": -1.18, "height": 381}}, "Dust 4": {"Dust 1": {"path": "Dust", "width": 234, "height": 71}}, "Dust 5": {"Dust 1": {"path": "Dust", "width": 234, "height": 71}}, "Dust 6": {"Dust 1": {"path": "Dust", "width": 234, "height": 71}}, "Dust 7": {"Dust 1": {"path": "Dust", "width": 234, "height": 71}}, "Dust 9": {"Dust 1": {"path": "Dust", "width": 234, "height": 71}}, "Gem Sparkle 1": {"Gem Sparkle": {"x": 0.95, "width": 55, "y": 0.3, "height": 60}}, "riu2": {"riu2": {"rotation": -2.03, "x": 21.68, "width": 287, "y": -18.28, "height": 105}}, "Dust 15": {"Dust 1": {"path": "Dust", "width": 234, "height": 71}}, "taytrai": {"taytrai": {"triangles": [20, 6, 7, 8, 20, 7, 8, 17, 20, 16, 5, 6, 16, 6, 20, 8, 9, 17, 19, 17, 9, 4, 5, 16, 18, 4, 16, 3, 4, 18, 2, 3, 18, 17, 16, 20, 18, 16, 17, 19, 18, 17, 2, 18, 19, 15, 1, 2, 10, 15, 2, 19, 10, 2, 10, 19, 9, 11, 15, 10, 12, 0, 1, 12, 1, 15, 11, 12, 15, 0, 12, 13, 0, 13, 14], "uvs": [0.36977, 0.42292, 0.42021, 0.62696, 0.666, 0.67007, 0.66416, 0.51448, 0.70697, 0.49188, 0.7496, 0.45515, 0.84244, 0.4545, 1, 0.50035, 0.95497, 0.64868, 0.8777, 0.77024, 0.73243, 0.85016, 0.7479, 0.98337, 0.19214, 1, 0, 0.05632, 0.26521, 0, 0.64164, 0.76899, 0.78519, 0.60083, 0.8163, 0.68533, 0.72386, 0.63239, 0.7689, 0.73962, 0.89008, 0.60693], "vertices": [3, 10, 40.26, 20.91, 0.58324, 11, 8.67, 41.5, 0.41647, 12, -2.7, 53.15, 0.00029, 3, 10, 63.58, 20.4, 0.38217, 11, 14.15, 18.82, 0.6174, 12, -11.54, 31.57, 0.00043, 3, 11, 44.91, 12.88, 0.1266, 12, 9.91, 8.73, 0.65179, 36, 2.01, 12.26, 0.22161, 3, 12, 20.28, 22.34, 0.00154, 36, 16.34, 2.9, 0.96838, 38, 24.32, 12.85, 0.03008, 3, 36, 15.39, -2.97, 0.82997, 38, 22.95, 7.07, 0.16296, 41, 19.75, 18.06, 0.00707, 3, 36, 15.75, -9.68, 0.19065, 38, 22.82, 0.35, 0.58773, 41, 20.09, 11.34, 0.22162, 4, 36, 9.27, -19.42, 0.13964, 38, 15.66, -8.9, 0.43048, 40, 20.26, 7.52, 0.07744, 41, 13.61, 1.61, 0.35244, 2, 40, 12.68, -11.51, 0.28944, 41, -1.67, -12.03, 0.71056, 2, 39, 8.14, -4.46, 0.54219, 40, -2.76, -3.76, 0.45781, 4, 12, 24.1, -16.4, 0.03218, 35, -12.41, -6.51, 0.06569, 37, -6.53, -6.14, 0.63991, 39, -8.15, -7.28, 0.26221, 4, 11, 52.5, -7.24, 0.21528, 12, 4.27, -12.02, 0.71396, 35, -12.08, 13.79, 0.04157, 37, -19.78, 9.25, 0.02919, 4, 11, 53.88, -21.96, 0.42797, 12, -3.23, -24.76, 0.56438, 35, -26.06, 18.6, 0.00521, 37, -33.42, 3.55, 0.00244, 2, 10, 94.38, -19.11, 0.5, 11, -16.16, -21.06, 0.5, 1, 10, -11.96, -11.82, 1, 1, 10, -8.08, 21.95, 1, 3, 11, 41.42, 2.12, 0.40051, 12, 0.78, 2.06, 0.59613, 36, -5.3, 20.88, 0.00337, 4, 35, 9.47, -4.42, 0.31458, 36, -0.05, -4.45, 0.27921, 37, 8.43, 9.97, 0.05015, 38, 7.44, 6.7, 0.35605, 3, 35, -0.59, -3.77, 0.52413, 37, 0.48, 3.77, 0.46542, 38, -2.29, 9.35, 0.01046, 4, 11, 52.36, 16.74, 0.003, 12, 18.2, 7.5, 0.23821, 35, 9.82, 4.04, 0.18284, 36, 1.38, 3.9, 0.57595, 3, 12, 15.39, -5.29, 0.6465, 35, -3.26, 4.24, 0.22957, 37, -6.85, 7.98, 0.12394, 4, 37, 11.22, -2.96, 0.12925, 38, -1.24, -3.29, 0.29278, 39, 5.48, 4.52, 0.20373, 40, 2.85, 3.74, 0.37425], "width": 126, "edges": [2, 4, 24, 26, 4, 30, 20, 18, 20, 22, 8, 32, 32, 34, 4, 6, 8, 6, 6, 36, 16, 18, 22, 24, 4, 20, 30, 20, 36, 38, 8, 10, 10, 12, 12, 14, 14, 16, 32, 10, 40, 12, 26, 28, 2, 0, 0, 28, 24, 0], "type": "mesh", "hull": 15, "height": 110}}, "thanh-doc": {"thanh-doc": {"x": 3.26, "width": 23, "y": 3.27, "height": 381}}, "daychuyen": {"daychuyen": {"rotation": -91.18, "x": -23.16, "width": 63, "y": -3.37, "height": 56}}, "Hit": {"Hit": {"rotation": -32.28, "x": 14.65, "width": 200, "y": -15.35, "height": 198}}, "Dust 10": {"Dust 1": {"path": "Dust", "width": 234, "height": 71}}, "Dust 11": {"Dust 1": {"path": "Dust", "width": 234, "height": 71}}, "Frame Light Right": {"Frame Light": {"scaleX": -1, "x": -0.5, "width": 31, "y": 0.5, "height": 225}}, "taytrai2": {"taytrai": {"triangles": [19, 7, 8, 9, 19, 8, 9, 16, 19, 15, 6, 7, 15, 7, 19, 9, 10, 16, 18, 16, 10, 5, 6, 15, 17, 5, 15, 4, 5, 17, 3, 4, 17, 16, 15, 19, 17, 15, 16, 18, 17, 16, 3, 17, 18, 14, 2, 3, 11, 14, 3, 18, 11, 3, 11, 18, 10, 12, 13, 14, 12, 14, 11, 0, 1, 2, 13, 0, 2, 13, 2, 14], "uvs": [0.24819, 0.81791, 0.36977, 0.42292, 0.42021, 0.62696, 0.666, 0.67007, 0.66416, 0.51448, 0.70697, 0.49188, 0.7496, 0.45515, 0.84244, 0.4545, 1, 0.50035, 0.95497, 0.64868, 0.8777, 0.77024, 0.73243, 0.85016, 0.7479, 0.98337, 0.37981, 0.99439, 0.64164, 0.76899, 0.78519, 0.60083, 0.8163, 0.68533, 0.72386, 0.63239, 0.7689, 0.73962, 0.89008, 0.60693], "vertices": [3, 10, 77.31, -6.48, 0.52626, 11, -8.33, -1.32, 0.47364, 12, -41.56, 28.41, 9e-05, 3, 10, 40.26, 20.91, 0.58324, 11, 8.67, 41.5, 0.41647, 12, -2.7, 53.15, 0.00029, 3, 10, 63.58, 20.4, 0.38217, 11, 14.15, 18.82, 0.6174, 12, -11.54, 31.57, 0.00043, 3, 11, 44.91, 12.88, 0.1266, 12, 9.91, 8.73, 0.65179, 36, 2.01, 12.26, 0.22161, 3, 12, 20.28, 22.34, 0.00154, 36, 16.34, 2.9, 0.96838, 38, 24.32, 12.85, 0.03008, 3, 36, 15.39, -2.97, 0.82997, 38, 22.95, 7.07, 0.16296, 41, 19.75, 18.06, 0.00707, 3, 36, 15.75, -9.68, 0.19065, 38, 22.82, 0.35, 0.58773, 41, 20.09, 11.34, 0.22162, 4, 36, 9.27, -19.42, 0.13964, 38, 15.66, -8.9, 0.43048, 40, 20.26, 7.52, 0.07744, 41, 13.61, 1.61, 0.35244, 2, 40, 12.68, -11.51, 0.28944, 41, -1.67, -12.03, 0.71056, 2, 39, 8.14, -4.46, 0.54219, 40, -2.76, -3.76, 0.45781, 4, 12, 24.1, -16.4, 0.03218, 35, -12.41, -6.51, 0.06569, 37, -6.53, -6.14, 0.63991, 39, -8.15, -7.28, 0.26221, 4, 11, 52.5, -7.24, 0.21528, 12, 4.27, -12.02, 0.71396, 35, -12.08, 13.79, 0.04157, 37, -19.78, 9.25, 0.02919, 4, 11, 53.88, -21.96, 0.42797, 12, -3.23, -24.76, 0.56438, 35, -26.06, 18.6, 0.00521, 37, -33.42, 3.55, 0.00244, 5, 10, 100.73, 3.68, 0.33116, 11, 7.49, -21.37, 0.47568, 12, -40.48, 2.9, 0.19058, 35, -6.41, 60.63, 0.00176, 37, -46.67, 48.01, 0.00082, 3, 11, 41.42, 2.12, 0.40051, 12, 0.78, 2.06, 0.59613, 36, -5.3, 20.88, 0.00337, 4, 35, 9.47, -4.42, 0.31458, 36, -0.05, -4.45, 0.27921, 37, 8.43, 9.97, 0.05015, 38, 7.44, 6.7, 0.35605, 3, 35, -0.59, -3.77, 0.52413, 37, 0.48, 3.77, 0.46542, 38, -2.29, 9.35, 0.01046, 4, 11, 52.36, 16.74, 0.003, 12, 18.2, 7.5, 0.23821, 35, 9.82, 4.04, 0.18284, 36, 1.38, 3.9, 0.57595, 3, 12, 15.39, -5.29, 0.6465, 35, -3.26, 4.24, 0.22957, 37, -6.85, 7.98, 0.12394, 4, 37, 11.22, -2.96, 0.12925, 38, -1.24, -3.29, 0.29278, 39, 5.48, 4.52, 0.20373, 40, 2.85, 3.74, 0.37425], "width": 126, "edges": [4, 6, 6, 28, 22, 20, 22, 24, 10, 30, 30, 32, 6, 8, 10, 8, 8, 34, 18, 20, 6, 22, 28, 22, 34, 36, 10, 12, 12, 14, 14, 16, 16, 18, 30, 12, 38, 14, 4, 2, 2, 0, 24, 26, 0, 26], "type": "mesh", "hull": 14, "height": 110}}, "tayphai": {"tayphai": {"triangles": [1, 8, 0, 0, 8, 5, 5, 7, 6, 5, 8, 7, 6, 4, 5, 6, 3, 4, 2, 8, 1, 2, 7, 8, 2, 3, 7, 7, 3, 6], "uvs": [0.99999, 0.99999, 0.67076, 1, 0, 1, 0, 0, 0.80959, 0.04392, 0.99999, 0.26355, 0.68411, 0.2091, 0.57999, 0.48911, 0.588, 0.77462], "vertices": [1, 9, 46.05, -17.61, 1, 2, 8, 54.09, -29.28, 0.14813, 9, 11.39, -26.94, 0.85187, 1, 8, -18.64, -21.8, 1, 1, 8, -13.22, 30.93, 1, 1, 9, 12.83, 25.92, 1, 1, 9, 35.9, 20.08, 1, 2, 8, 59.82, 12.27, 0.04374, 9, 1.9, 13.91, 0.95626, 2, 8, 47.02, -1.33, 0.94664, 9, -5.2, -3.37, 0.05336, 2, 8, 46.34, -16.47, 0.40289, 9, -0.42, -17.75, 0.59711], "width": 109, "edges": [4, 6, 0, 10, 6, 8, 8, 10, 8, 12, 12, 14, 14, 16, 0, 2, 2, 4, 16, 2], "type": "mesh", "hull": 6, "height": 53}}, "thanh-docBright2": {"thanh-docBright": {"scaleX": -1, "scaleY": -1, "x": 84.66, "width": 23, "y": 17.88, "height": 381}}, "khung_mask": {"khung_mask": {"color": "ce3a3aff", "vertices": [78.44, -186.53, 86.32, -129.13, 88.21, 34.58, 274.86, 33.92, 273.74, 300.14, -177.48, 300.37, -179.31, 55.3, -88.12, 54.69, -87.11, -120.87, -76.01, -187.07], "end": "Smoke 3", "type": "clipping", "vertexCount": 10}}}}, "skeleton": {"images": "./images/", "width": 322.1, "spine": "3.7.94", "audio": "", "hash": "3m5mxVhKL0BSCFB59VWm+DOwj8w", "height": 504.98}, "slots": [{"attachment": "BG-che", "name": "BG-che", "bone": "BG"}, {"attachment": "BG", "name": "BG", "bone": "BG"}, {"attachment": "Rock Mask", "name": "Rock Mask", "bone": "Rock All"}, {"color": "ffb031ff", "attachment": "Dust 1", "name": "Dust 1", "bone": "Dust 1"}, {"color": "ffb031ff", "attachment": "Dust 1", "name": "Dust 9", "bone": "Dust 9"}, {"color": "ffb031ff", "attachment": "Dust 1", "name": "Dust 2", "bone": "Dust 2"}, {"color": "ffb031ff", "attachment": "Dust 1", "name": "Dust 10", "bone": "Dust 10"}, {"color": "ffb031ff", "attachment": "Dust 1", "name": "Dust 3", "bone": "Dust 3"}, {"color": "ffb031ff", "attachment": "Dust 1", "name": "Dust 11", "bone": "Dust 11"}, {"color": "ffb031ff", "attachment": "Dust 1", "name": "Dust 4", "bone": "Dust 4"}, {"color": "ffb031ff", "attachment": "Dust 1", "name": "Dust 5", "bone": "Dust 5"}, {"color": "ffb031ff", "attachment": "Dust 1", "name": "Dust 6", "bone": "Dust 6"}, {"color": "ffb031ff", "attachment": "Dust 1", "name": "Dust 7", "bone": "Dust 7"}, {"color": "ffb031ff", "attachment": "Dust 1", "name": "Dust 15", "bone": "Dust 15"}, {"attachment": "Rock/Rock 1", "name": "Rock/Rock 1", "bone": "Rock 1"}, {"attachment": "Rock/Rock 2", "name": "Rock/Rock 2", "bone": "Rock 2"}, {"attachment": "Rock/Rock 3", "name": "Rock/Rock 3", "bone": "Rock 3"}, {"attachment": "Rock/Rock 4", "name": "Rock/Rock 4", "bone": "Rock 4"}, {"attachment": "Rock/Rock 5", "name": "Rock/Rock 5", "bone": "Rock 5"}, {"attachment": "Rock/Rock 6", "name": "Rock/Rock 6", "bone": "Rock 6"}, {"attachment": "khung_mask", "name": "khung_mask", "bone": "all"}, {"name": "template", "bone": "all"}, {"attachment": "<PERSON>rai", "name": "<PERSON>rai", "bone": "<PERSON>rai"}, {"attachment": "chan<PERSON>i", "name": "chan<PERSON>i", "bone": "chan<PERSON>i"}, {"color": "ffaf4b7d", "attachment": "Smoke 1", "name": "Smoke 1", "bone": "Smoke Fly"}, {"color": "ffaf4b7d", "attachment": "Smoke 2", "name": "Smoke 2", "bone": "Smoke Fly"}, {"color": "ffaf4b7d", "attachment": "Smoke 3", "name": "Smoke 3", "bone": "Smoke Fly"}, {"attachment": "Frame Light", "name": "Frame Light Left", "bone": "Frame Light Left"}, {"attachment": "thanh-doc", "name": "thanh-doc", "bone": "doc<PERSON><PERSON>"}, {"attachment": "thanh-doc", "name": "thanh-doc2", "bone": "<PERSON><PERSON><PERSON><PERSON>"}, {"attachment": "<PERSON><PERSON>-<PERSON><PERSON>", "name": "<PERSON><PERSON>-<PERSON><PERSON>", "bone": "ngangtren"}, {"attachment": "<PERSON><PERSON>-<PERSON><PERSON>", "name": "thanh-ngang2", "bone": "<PERSON><PERSON><PERSON><PERSON>"}, {"attachment": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "bone": "Frame-New"}, {"attachment": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "name": "thanh-docBright2", "bone": "Frame-New"}, {"attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bone": "Frame-New"}, {"attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "thanh-ngangBright2", "bone": "Frame-New"}, {"attachment": "<PERSON><PERSON>", "name": "Gem Sparkle 1", "bone": "Gem Sparkle 1"}, {"attachment": "<PERSON><PERSON>", "name": "Gem Spartkle 2", "bone": "Gem Spartkle 2"}, {"attachment": "hong-cheng<PERSON>i", "name": "hong-cheng<PERSON>i", "bone": "hong"}, {"attachment": "taytrai", "name": "taytrai", "bone": "taytrai"}, {"attachment": "toc", "name": "toc", "bone": "toc"}, {"attachment": "longduoiST", "name": "longduoiST", "bone": "<PERSON><PERSON><PERSON>"}, {"attachment": "than", "name": "than", "bone": "than"}, {"attachment": "<PERSON><PERSON><PERSON>n", "name": "<PERSON><PERSON><PERSON>n", "bone": "<PERSON><PERSON><PERSON>n"}, {"attachment": "hong", "name": "hong", "bone": "hong"}, {"attachment": "Frame Light", "name": "Frame Light Right", "bone": "Frame Light Right"}, {"attachment": "taytrai", "name": "taytrai2", "bone": "taytrai"}, {"attachment": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON><PERSON>"}, {"attachment": "longtrenST", "name": "longtrenST", "bone": "longtren"}, {"attachment": "dau", "name": "dau", "bone": "dau"}, {"attachment": "riu", "name": "riu", "bone": "riu"}, {"color": "ffffff00", "attachment": "riu2", "name": "riu2", "bone": "riu"}, {"color": "ffffff00", "attachment": "Axe Heat", "name": "Axe Heat", "bone": "riu"}, {"attachment": "ta<PERSON>phai", "name": "ta<PERSON>phai", "bone": "ta<PERSON>phai"}, {"attachment": "eff_mask", "name": "eff_mask", "bone": "all"}, {"attachment": "set1", "name": "set1", "bone": "set1"}, {"attachment": "set1", "name": "set3", "bone": "set3"}, {"attachment": "set1", "name": "set2", "bone": "set2"}, {"attachment": "Wild", "name": "Wild", "bone": "Wild All"}, {"attachment": "Wild Mask", "name": "Wild Mask", "bone": "Wild Mask"}, {"attachment": "<PERSON>", "name": "<PERSON>", "bone": "Wild All"}, {"attachment": "Sparkle", "name": "Sparkle 1", "bone": "Sparkle 1"}, {"attachment": "Sparkle", "name": "Sparkle 2", "bone": "Sparkle 2"}, {"attachment": "Sparkle", "name": "Sparkle 3", "bone": "Sparkle 3"}, {"attachment": "Sparkle", "name": "Sparkle 4", "bone": "Sparkle 4"}, {"attachment": "Sparkle", "name": "Sparkle 5", "bone": "Sparkle 5"}, {"attachment": "Hit", "name": "Hit", "bone": "Hit"}, {"attachment": "long6", "name": "long6", "bone": "long6"}, {"attachment": "long4", "name": "long4", "bone": "long4"}, {"attachment": "long5", "name": "long5", "bone": "long5"}, {"attachment": "long3", "name": "long3", "bone": "long3"}, {"attachment": "long2", "name": "long2", "bone": "long2"}, {"attachment": "long1", "name": "long1", "bone": "long1"}, {"attachment": "vang", "name": "<PERSON><PERSON><PERSON>", "bone": "non"}], "bones": [{"name": "root"}, {"parent": "root", "name": "all", "y": -20}, {"scaleX": 0.95, "parent": "all", "scaleY": 0.95, "color": "ffb82fff", "rotation": 97.24, "name": "hong", "length": 34.61, "x": 33.18, "y": 11.76}, {"parent": "hong", "color": "ffb82fff", "rotation": -1.47, "name": "than", "length": 59.71, "x": 35.08, "y": -0.61}, {"parent": "than", "color": "ffb82fff", "rotation": 12.8, "name": "than2", "length": 75.31, "x": 60.85, "y": 0.43}, {"parent": "than2", "rotation": -39.2, "name": "dau", "length": 49.5, "x": 85.65, "y": -3.47}, {"parent": "dau", "name": "toc", "x": 26.64, "y": 15.85}, {"parent": "than2", "color": "ffb82fff", "rotation": 122.6, "name": "than3", "length": 90.79, "x": 77.57, "y": 45.06}, {"parent": "than3", "color": "37da42ff", "rotation": 134.71, "name": "ta<PERSON>phai", "length": 52.81, "x": 90.96, "y": 0.02}, {"parent": "ta<PERSON>phai", "color": "37da42ff", "rotation": -20.94, "name": "tayphai2", "length": 22.92, "x": 53.08, "y": -0.04}, {"parent": "than2", "color": "3463f7ff", "rotation": 178.49, "name": "taytrai", "length": 80.22, "x": 27.44, "y": -31.41}, {"parent": "taytrai", "color": "3463f7ff", "rotation": 75.18, "name": "taytrai2", "length": 42, "x": 78.16, "y": 1.91}, {"parent": "taytrai2", "color": "3463f7ff", "rotation": 35.86, "name": "taytrai3", "length": 15.98, "x": 42}, {"parent": "tayphai2", "color": "37da42ff", "rotation": 47.35, "name": "riu", "length": 97.98, "x": 20.28, "y": -1.01}, {"parent": "hong", "rotation": 159.85, "name": "chan<PERSON>i", "length": 165.82, "x": 6.77, "y": 40.34}, {"parent": "chan<PERSON>i", "rotation": -11, "name": "chanphai2", "length": 59.99, "x": 162.79, "y": -0.12}, {"parent": "hong", "rotation": -173.72, "name": "<PERSON>rai", "length": 140.84, "x": 4.5, "y": -16.5}, {"parent": "<PERSON>rai", "rotation": -39.66, "name": "chantrai2", "length": 84.62, "x": 137.68, "y": -1.21}, {"parent": "hong", "color": "fd359bff", "name": "<PERSON><PERSON><PERSON><PERSON>", "x": 36.96, "y": -7.44}, {"parent": "<PERSON><PERSON><PERSON><PERSON>", "color": "fd359bff", "rotation": 175.99, "name": "vaithatlung2", "length": 38.76, "x": -9.33, "y": 0.09}, {"parent": "vaithatlung2", "color": "fd359bff", "rotation": 1.12, "name": "vaithatlung3", "length": 43.18, "x": 39.3, "y": -0.03}, {"parent": "vaithatlung3", "color": "fd359bff", "rotation": -0.93, "name": "vait<PERSON>lung4", "length": 36.58, "x": 43.18}, {"parent": "vait<PERSON>lung4", "color": "fd359bff", "rotation": 6.89, "name": "vaithatlung5", "length": 36.56, "x": 36.58}, {"parent": "vaithatlung5", "color": "fd359bff", "rotation": -0.52, "name": "vaithatlung6", "length": 44.23, "x": 36.56}, {"parent": "toc", "rotation": 160.72, "name": "toc3", "length": 15.13, "x": 5.92, "y": 15.98}, {"parent": "toc3", "rotation": -10.41, "name": "toc4", "length": 12.89, "x": 15.13}, {"parent": "toc4", "rotation": -38.07, "name": "toc5", "length": 14.99, "x": 12.72, "y": 0.13}, {"parent": "toc", "rotation": 162.42, "name": "toc2", "length": 12.62, "x": -11.2, "y": 9.12}, {"parent": "toc2", "rotation": -18.1, "name": "toc6", "length": 14.45, "x": 12.62}, {"parent": "toc6", "rotation": -19.29, "name": "toc7", "length": 16.12, "x": 14.45}, {"parent": "toc7", "rotation": 31.26, "name": "toc8", "length": 12.98, "x": 17.05, "y": 1.07}, {"parent": "toc", "rotation": 153.66, "name": "toc9", "length": 12.99, "x": 0.94, "y": 25.6}, {"parent": "toc9", "rotation": -43.03, "name": "toc10", "length": 13.29, "x": 12.99}, {"parent": "toc10", "rotation": -27.65, "name": "toc11", "length": 10, "x": 13.29}, {"parent": "than2", "color": "6a3899ff", "rotation": -17.39, "name": "<PERSON><PERSON><PERSON>n", "x": 90.21, "y": -1.77}, {"parent": "taytrai3", "color": "3463f7ff", "rotation": 78.48, "name": "taytrai4", "length": 8.95, "x": 20.2, "y": -2.94}, {"parent": "taytrai4", "color": "3463f7ff", "rotation": 7.38, "name": "taytrai5", "length": 8.96, "x": 8.95}, {"parent": "taytrai3", "color": "3463f7ff", "rotation": 36.81, "name": "taytrai6", "length": 9.33, "x": 25.65, "y": -7.57}, {"parent": "taytrai6", "color": "3463f7ff", "rotation": 53.17, "name": "taytrai7", "length": 10.9, "x": 9.33}, {"parent": "taytrai3", "color": "3463f7ff", "rotation": 6.06, "name": "taytrai8", "length": 6.78, "x": 31.44, "y": -8.3}, {"parent": "taytrai8", "color": "3463f7ff", "rotation": 53.33, "name": "taytrai9", "length": 8.8, "x": 6.78}, {"parent": "taytrai9", "color": "2e5fd3ff", "rotation": 26.53, "name": "taytrai10", "length": 7.42, "x": 8.8}, {"scaleX": 0.014, "parent": "all", "rotation": 46.19, "name": "set1", "x": -152.3, "y": -103.96}, {"scaleX": 0.014, "parent": "all", "rotation": 46.19, "name": "set2", "x": 111.12, "y": -66.2}, {"parent": "vaithatlung6", "color": "fd359bff", "name": "vaithatlung7", "x": 46.51, "y": -36.46}, {"parent": "vaithatlung6", "color": "fd359bff", "name": "vaithatlung8", "x": 55.58, "y": 2.7}, {"parent": "vaithatlung6", "color": "fd359bff", "name": "vaithatlung9", "x": 48.38, "y": 37.19}, {"parent": "all", "name": "<PERSON>ame", "y": 20}, {"parent": "<PERSON>ame", "color": "ff1c1cff", "name": "Gem Sparkle 1", "x": -75.93, "y": 197.06}, {"parent": "<PERSON>ame", "color": "ff1c1cff", "name": "Gem Spartkle 2", "x": 75.42, "y": 197.06}, {"scaleX": 0.014, "parent": "all", "rotation": 46.19, "name": "set3", "x": 138.19, "y": 170.54}, {"parent": "all", "name": "Rock All"}, {"parent": "Rock All", "color": "0000ffff", "name": "Rock 1", "x": 15}, {"parent": "Rock All", "color": "0000ffff", "name": "Rock 2", "x": -15}, {"parent": "Rock All", "color": "0000ffff", "name": "Rock 3", "x": 30}, {"parent": "Rock All", "color": "0000ffff", "name": "Rock 4", "x": -30}, {"parent": "Rock All", "color": "0000ffff", "name": "Rock 5", "x": 45}, {"parent": "Rock All", "color": "0000ffff", "name": "Rock 6", "x": -45}, {"parent": "Rock All", "rotation": -20, "name": "Dust All"}, {"scaleX": 1.127, "parent": "Dust All", "scaleY": 1.417, "name": "Dust Fly"}, {"parent": "Dust Fly", "name": "Dust A", "y": -150}, {"parent": "Dust A", "name": "Dust 1"}, {"scaleX": -1, "parent": "Dust A", "name": "Dust 2", "y": 50}, {"parent": "Dust A", "name": "Dust 3", "y": 100}, {"scaleX": -1, "parent": "Dust A", "name": "Dust 4", "y": 150}, {"parent": "Dust A", "name": "Dust 5", "y": 200}, {"parent": "Dust A", "name": "Dust 6", "y": 250}, {"parent": "Dust A", "name": "Dust 7", "y": 300}, {"parent": "Dust Fly", "name": "Dust B", "x": 225, "y": -150}, {"parent": "Dust B", "name": "Dust 9"}, {"scaleX": -1, "parent": "Dust B", "name": "Dust 10", "y": 50}, {"parent": "Dust B", "name": "Dust 11", "y": 100}, {"parent": "Dust B", "name": "Dust 15", "y": 300}, {"parent": "all", "name": "Smoke All"}, {"parent": "Smoke All", "name": "Smoke Fly", "y": -200}, {"parent": "<PERSON>ame", "name": "Frame Light Left", "x": -85.75}, {"parent": "<PERSON>ame", "name": "Frame Light Right", "x": 85.75}, {"parent": "all", "name": "Wild All", "x": -2.35, "y": -189.93}, {"scaleX": 2, "parent": "Wild All", "rotation": -17.5, "name": "Wild Mask", "x": -120}, {"scaleX": 0.85, "parent": "Wild All", "scaleY": 0.85, "name": "Sparkle 1", "x": -63.14, "y": 18.72}, {"scaleX": 0.85, "parent": "Wild All", "scaleY": 0.85, "name": "Sparkle 2", "x": -33.75, "y": 16.77}, {"scaleX": 0.85, "parent": "Wild All", "scaleY": 0.85, "name": "Sparkle 3", "x": 10.52, "y": 32.48}, {"scaleX": 0.85, "parent": "Wild All", "scaleY": 0.85, "name": "Sparkle 4", "x": 35.71, "y": 3.11}, {"scaleX": 0.85, "parent": "Wild All", "scaleY": 0.85, "name": "Sparkle 5", "x": 64.52, "y": 25.52}, {"parent": "tayphai2", "color": "37da42ff", "rotation": 47.35, "name": "Hit", "x": 111.28, "y": 30.9}, {"parent": "dau", "color": "fb0000ff", "name": "non", "x": 49.5}, {"parent": "non", "color": "fb0000ff", "rotation": -75.22, "name": "long1", "x": 13.86, "y": -5.11}, {"parent": "non", "color": "fb0000ff", "rotation": -76.32, "name": "long2", "x": 13.32, "y": -16.41}, {"parent": "non", "color": "fb0000ff", "rotation": -74.47, "name": "long3", "x": 11.18, "y": -20.19}, {"parent": "non", "color": "fb0000ff", "rotation": -67.87, "name": "long4", "x": 12.71, "y": -1.37}, {"parent": "non", "color": "fb0000ff", "rotation": -74.73, "name": "long5", "x": 10.73, "y": 6.91}, {"parent": "non", "color": "fb0000ff", "rotation": -68.34, "name": "long6", "x": 10.66, "y": 8.57}, {"parent": "than", "color": "ff002aff", "rotation": -3.51, "name": "longtren", "x": 56.67, "y": 7.09}, {"scaleX": 0.415, "parent": "than", "scaleY": 0.415, "color": "fd0606ff", "rotation": 106.14, "name": "<PERSON><PERSON><PERSON>", "length": 100, "x": 13.71, "y": 34.85}, {"parent": "<PERSON><PERSON><PERSON>", "color": "fd0606ff", "rotation": 8.71, "name": "longduoi2", "length": 59.17, "x": 101.53, "y": 3.44}, {"parent": "longduoi2", "color": "fd0606ff", "rotation": 9.81, "name": "longduoi3", "length": 73.72, "x": 59.17}, {"parent": "longduoi3", "color": "fd0606ff", "rotation": 16.37, "name": "longduoi4", "length": 68.31, "x": 73.72}, {"parent": "longtren", "color": "ff002aff", "rotation": -23.21, "name": "longtren2", "length": 10.75, "x": 75.57, "y": -30.86}, {"parent": "longtren2", "color": "ff002aff", "rotation": -49.75, "name": "longtren3", "length": 9.06, "x": -6.73, "y": -25.46}, {"parent": "longtren3", "color": "ff002aff", "rotation": -34.55, "name": "longtren4", "length": 9.75, "x": 6.07, "y": -23.41}, {"parent": "all", "color": "ff0101ff", "name": "Frame-New"}, {"parent": "Frame-New", "color": "ff0101ff", "name": "ngangtren", "y": 226.04}, {"parent": "Frame-New", "color": "ff0101ff", "name": "<PERSON><PERSON><PERSON><PERSON>", "y": -185.98}, {"parent": "Frame-New", "color": "ff0101ff", "name": "doc<PERSON><PERSON>", "x": -87.47, "y": 17.32}, {"parent": "Frame-New", "color": "ff0101ff", "name": "<PERSON><PERSON><PERSON><PERSON>", "x": 84, "y": 19.05}, {"parent": "<PERSON>ame", "name": "BG"}], "animations": {"Wild-SonTinh": {"slots": {"riu2": {"color": [{"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.1333}, {"color": "ffffffff", "curve": "stepped", "time": 0.5}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 1.1667}, {"color": "ffffff00", "curve": "stepped", "time": 1.6667}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 1.8}, {"color": "ffffffff", "curve": "stepped", "time": 2.1667}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 2.8333}, {"color": "ffffff00", "curve": "stepped", "time": 3.3333}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 3.4667}, {"color": "ffffffff", "curve": "stepped", "time": 3.8333}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 4.5}, {"color": "ffffff00", "curve": "stepped", "time": 5}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 5.1333}, {"color": "ffffffff", "curve": "stepped", "time": 5.5}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 6.1667}, {"color": "ffffff00", "time": 6.6667}]}, "set3": {"attachment": [{"name": "set2", "time": 0.7}, {"name": "set1", "time": 0.7667}, {"name": "set2", "time": 0.8333}, {"name": "set1", "time": 0.9}, {"name": null, "time": 0.9667}, {"name": "set2", "time": 2.3667}, {"name": "set1", "time": 2.4333}, {"name": "set2", "time": 2.5}, {"name": "set1", "time": 2.5667}, {"name": null, "time": 2.6333}, {"name": "set2", "time": 4.0333}, {"name": "set1", "time": 4.1}, {"name": "set2", "time": 4.1667}, {"name": "set1", "time": 4.2333}, {"name": null, "time": 4.3}, {"name": "set2", "time": 5.7}, {"name": "set1", "time": 5.7667}, {"name": "set2", "time": 5.8333}, {"name": "set1", "time": 5.9}, {"name": null, "time": 5.9667}]}, "BG": {"color": [{"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 0.4667}, {"color": "4b4b4bff", "curve": "stepped", "time": 0.6667}, {"color": "4b4b4bff", "curve": [0.25, 0, 0.75, 1], "time": 1.0667}, {"color": "ffffffff", "curve": "stepped", "time": 1.2667}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 2.1333}, {"color": "4b4b4bff", "curve": "stepped", "time": 2.3333}, {"color": "4b4b4bff", "curve": [0.25, 0, 0.75, 1], "time": 2.7333}, {"color": "ffffffff", "curve": "stepped", "time": 2.9333}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 3.8}, {"color": "4b4b4bff", "curve": "stepped", "time": 4}, {"color": "4b4b4bff", "curve": [0.25, 0, 0.75, 1], "time": 4.4}, {"color": "ffffffff", "time": 4.6}]}, "set2": {"attachment": [{"name": "set2", "time": 0.5333}, {"name": "set1", "time": 0.6}, {"name": "set2", "time": 0.6667}, {"name": "set1", "time": 0.7333}, {"name": "set2", "time": 0.8}, {"name": "set1", "time": 0.8667}, {"name": "set2", "time": 0.9333}, {"name": null, "time": 1}, {"name": "set2", "time": 2.2}, {"name": "set1", "time": 2.2667}, {"name": "set2", "time": 2.3333}, {"name": "set1", "time": 2.4}, {"name": "set2", "time": 2.4667}, {"name": "set1", "time": 2.5333}, {"name": "set2", "time": 2.6}, {"name": null, "time": 2.6667}, {"name": "set2", "time": 3.8667}, {"name": "set1", "time": 3.9333}, {"name": "set2", "time": 4}, {"name": "set1", "time": 4.0667}, {"name": "set2", "time": 4.1333}, {"name": "set1", "time": 4.2}, {"name": "set2", "time": 4.2667}, {"name": null, "time": 4.3333}, {"name": "set2", "time": 5.5333}, {"name": "set1", "time": 5.6}, {"name": "set2", "time": 5.6667}, {"name": "set1", "time": 5.7333}, {"name": "set2", "time": 5.8}, {"name": "set1", "time": 5.8667}, {"name": "set2", "time": 5.9333}, {"name": null, "time": 6}]}, "BG-che": {"attachment": [{"name": null, "time": 0}]}, "thanh-ngangBright2": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.1667}, {"color": "ffffffff", "curve": "stepped", "time": 0.4667}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 1.2667}, {"color": "ffffff00", "curve": "stepped", "time": 1.6667}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 1.8333}, {"color": "ffffffff", "curve": "stepped", "time": 2.1333}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 2.9333}, {"color": "ffffff00", "curve": "stepped", "time": 3.3333}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 3.5}, {"color": "ffffffff", "curve": "stepped", "time": 3.8}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 4.6}, {"color": "ffffff00", "time": 5}]}, "thanh-ngangBright": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.1667}, {"color": "ffffffff", "curve": "stepped", "time": 0.4667}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 1.2667}, {"color": "ffffff00", "curve": "stepped", "time": 1.6667}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 1.8333}, {"color": "ffffffff", "curve": "stepped", "time": 2.1333}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 2.9333}, {"color": "ffffff00", "curve": "stepped", "time": 3.3333}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 3.5}, {"color": "ffffffff", "curve": "stepped", "time": 3.8}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 4.6}, {"color": "ffffff00", "time": 5}]}, "hong-chenguoi": {"attachment": [{"name": null, "time": 0}]}, "Smoke 3": {"color": [{"color": "ffaf4b00", "time": 0}, {"color": "ffaf4b7d", "curve": "stepped", "time": 0.6667}, {"color": "ffaf4b7d", "time": 0.8333}, {"color": "ffaf4b00", "time": 1.6}, {"color": "ffaf4b7d", "time": 1.6667}, {"color": "ffaf4b00", "curve": "stepped", "time": 2.4333}, {"color": "ffaf4b00", "time": 2.4667}, {"color": "ffaf4b7d", "curve": "stepped", "time": 3.1333}, {"color": "ffaf4b7d", "time": 4.1667}, {"color": "ffaf4b00", "time": 4.9333}, {"color": "ffaf4b7d", "time": 5.8333}, {"color": "ffaf4b00", "time": 6.6}]}, "Smoke 2": {"color": [{"color": "ffaf4b00", "time": 0}, {"color": "ffaf4b7d", "curve": "stepped", "time": 0.6667}, {"color": "ffaf4b7d", "time": 0.8333}, {"color": "ffaf4b00", "time": 1.6}, {"color": "ffaf4b7d", "time": 1.6667}, {"color": "ffaf4b00", "curve": "stepped", "time": 2.4333}, {"color": "ffaf4b00", "time": 2.4667}, {"color": "ffaf4b7d", "curve": "stepped", "time": 3.1333}, {"color": "ffaf4b7d", "time": 4.1667}, {"color": "ffaf4b00", "time": 4.9333}, {"color": "ffaf4b7d", "time": 5.8333}, {"color": "ffaf4b00", "time": 6.6}]}, "Smoke 1": {"color": [{"color": "ffaf4b00", "time": 0}, {"color": "ffaf4b7d", "curve": "stepped", "time": 0.6667}, {"color": "ffaf4b7d", "time": 0.8333}, {"color": "ffaf4b00", "time": 1.6}, {"color": "ffaf4b7d", "time": 1.6667}, {"color": "ffaf4b00", "curve": "stepped", "time": 2.4333}, {"color": "ffaf4b00", "time": 2.4667}, {"color": "ffaf4b7d", "curve": "stepped", "time": 3.1333}, {"color": "ffaf4b7d", "time": 4.1667}, {"color": "ffaf4b00", "time": 4.9333}, {"color": "ffaf4b7d", "time": 5.8333}, {"color": "ffaf4b00", "time": 6.6}]}, "Axe Heat": {"color": [{"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.6667}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 1}, {"color": "ffffff00", "curve": "stepped", "time": 1.6667}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 2.3333}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 2.6667}, {"color": "ffffff00", "curve": "stepped", "time": 3.3333}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 4}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 4.3333}, {"color": "ffffff00", "curve": "stepped", "time": 5}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 5.6667}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 6}, {"color": "ffffff00", "time": 6.6667}]}, "set1": {"attachment": [{"name": "set2", "time": 0.5667}, {"name": "set1", "time": 0.6333}, {"name": "set2", "time": 0.7}, {"name": "set1", "time": 0.7667}, {"name": "set2", "time": 0.8333}, {"name": null, "time": 0.9}, {"name": "set2", "time": 2.2333}, {"name": "set1", "time": 2.3}, {"name": "set2", "time": 2.3667}, {"name": "set1", "time": 2.4333}, {"name": "set2", "time": 2.5}, {"name": null, "time": 2.5667}, {"name": "set2", "time": 3.9}, {"name": "set1", "time": 3.9667}, {"name": "set2", "time": 4.0333}, {"name": "set1", "time": 4.1}, {"name": "set2", "time": 4.1667}, {"name": null, "time": 4.2333}, {"name": "set2", "time": 5.5667}, {"name": "set1", "time": 5.6333}, {"name": "set2", "time": 5.7}, {"name": "set1", "time": 5.7667}, {"name": "set2", "time": 5.8333}, {"name": null, "time": 5.9}]}, "thanh-docBright2": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.1667}, {"color": "ffffffff", "curve": "stepped", "time": 0.4667}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 1.2667}, {"color": "ffffff00", "curve": "stepped", "time": 1.6667}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 1.8333}, {"color": "ffffffff", "curve": "stepped", "time": 2.1333}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 2.9333}, {"color": "ffffff00", "curve": "stepped", "time": 3.3333}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 3.5}, {"color": "ffffffff", "curve": "stepped", "time": 3.8}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 4.6}, {"color": "ffffff00", "time": 5}]}, "thanh-docBright": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.1667}, {"color": "ffffffff", "curve": "stepped", "time": 0.4667}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 1.2667}, {"color": "ffffff00", "curve": "stepped", "time": 1.6667}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 1.8333}, {"color": "ffffffff", "curve": "stepped", "time": 2.1333}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 2.9333}, {"color": "ffffff00", "curve": "stepped", "time": 3.3333}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 3.5}, {"color": "ffffffff", "curve": "stepped", "time": 3.8}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 4.6}, {"color": "ffffff00", "time": 5}]}}, "bones": {"longduoi2": {"rotate": [{"angle": 0, "time": 0}, {"angle": -5.45, "time": 1.1667}, {"angle": 2.44, "time": 2.3333}, {"angle": 0, "time": 3.3333}, {"angle": -5.45, "time": 4.5}, {"angle": 2.44, "time": 5.6667}, {"angle": 0, "time": 6.6667}]}, "Sparkle 4": {"scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.2}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.5333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.8667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.4667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 2.8}, {"curve": "stepped", "x": 0, "y": 0, "time": 3.1333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 4.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 5}, {"x": 0, "y": 0, "time": 5.3333}]}, "longduoi4": {"rotate": [{"angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -38.15, "time": 1.1667}, {"angle": 17.33, "time": 2.3333}, {"angle": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -38.15, "time": 4.5}, {"angle": 17.33, "time": 5.6667}, {"angle": 0, "time": 6.6667}]}, "Sparkle 5": {"scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.2667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.6}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.9333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.5333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 2.8667}, {"curve": "stepped", "x": 0, "y": 0, "time": 3.2}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 4.7333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 5.0667}, {"x": 0, "y": 0, "time": 5.4}]}, "longduoi3": {"rotate": [{"angle": 0, "time": 0}, {"angle": -9.26, "time": 1.1667}, {"angle": 18.61, "time": 2.3333}, {"angle": 0, "time": 3.3333}, {"angle": -9.26, "time": 4.5}, {"angle": 18.61, "time": 5.6667}, {"angle": 0, "time": 6.6667}]}, "Sparkle 2": {"scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.0667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.4}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.7333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 2.6667}, {"curve": "stepped", "x": 0, "y": 0, "time": 3}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 4.5333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 4.8667}, {"x": 0, "y": 0, "time": 5.2}]}, "Sparkle 3": {"scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.1333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.4667}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.4}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 2.7333}, {"curve": "stepped", "x": 0, "y": 0, "time": 3.0667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 4.6}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 4.9333}, {"x": 0, "y": 0, "time": 5.2667}]}, "Sparkle 1": {"scale": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.2667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 2.6}, {"curve": "stepped", "x": 0, "y": 0, "time": 2.9333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 4.4667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 4.8}, {"x": 0, "y": 0, "time": 5.1333}]}, "long2": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.83, "time": 0.3333}, {"angle": 0, "time": 0.6667}, {"angle": 2.41, "time": 1.1}, {"curve": "stepped", "angle": 0, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.83, "time": 2.1667}, {"angle": 0, "time": 2.5}, {"angle": 2.41, "time": 2.9333}, {"curve": "stepped", "angle": 0, "time": 3.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.4}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.83, "time": 3.7333}, {"angle": 0, "time": 4.0667}, {"angle": 2.41, "time": 4.5}, {"curve": "stepped", "angle": 0, "time": 4.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.83, "time": 5.5}, {"angle": 0, "time": 5.8333}, {"angle": 2.41, "time": 6.2667}, {"angle": 0, "time": 6.5}]}, "long1": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.83, "time": 0.4333}, {"angle": 0, "time": 0.7333}, {"angle": 2.41, "time": 1}, {"curve": "stepped", "angle": 0, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.83, "time": 2.1667}, {"angle": 0, "time": 2.4667}, {"angle": 2.41, "time": 2.7333}, {"curve": "stepped", "angle": 0, "time": 3.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.83, "time": 3.9}, {"angle": 0, "time": 4.2}, {"angle": 2.41, "time": 4.4667}, {"curve": "stepped", "angle": 0, "time": 4.8}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.83, "time": 5.7667}, {"angle": 0, "time": 6.0667}, {"angle": 2.41, "time": 6.3333}, {"angle": 0, "time": 6.6667}]}, "chanphai": {"rotate": [{"curve": [0.249, 0, 0.627, 0.51], "angle": 0, "time": 0}, {"curve": "stepped", "angle": -0.82, "time": 0.4333}, {"curve": [0.377, 0.51, 0.749, 1], "angle": -0.82, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.56, "time": 1.1667}, {"angle": 0, "time": 1.6667}, {"curve": "stepped", "angle": -0.82, "time": 2.1}, {"curve": [0.377, 0.51, 0.749, 1], "angle": -0.82, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.56, "time": 2.8333}, {"curve": [0.249, 0, 0.627, 0.51], "angle": 0, "time": 3.3333}, {"curve": "stepped", "angle": -0.82, "time": 3.7667}, {"curve": [0.377, 0.51, 0.749, 1], "angle": -0.82, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.56, "time": 4.5}, {"curve": [0.249, 0, 0.627, 0.51], "angle": 0, "time": 5}, {"curve": "stepped", "angle": -0.82, "time": 5.4333}, {"curve": [0.377, 0.51, 0.749, 1], "angle": -0.82, "time": 5.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.56, "time": 6.1667}, {"angle": 0, "time": 6.6667}]}, "dau": {"rotate": [{"curve": [0.249, 0, 0.627, 0.51], "angle": 0, "time": 0}, {"curve": "stepped", "angle": 3.06, "time": 0.4333}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 3.06, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.82, "time": 1.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.6667}, {"curve": "stepped", "angle": 3.06, "time": 2.1}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 3.06, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.82, "time": 2.8333}, {"curve": [0.249, 0, 0.627, 0.51], "angle": 0, "time": 3.3333}, {"curve": "stepped", "angle": 3.06, "time": 3.7667}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 3.06, "time": 4}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.82, "time": 4.5}, {"curve": [0.249, 0, 0.627, 0.51], "angle": 0, "time": 5}, {"curve": "stepped", "angle": 3.06, "time": 5.4333}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 3.06, "time": 5.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.82, "time": 6.1667}, {"angle": 0, "time": 6.6667}]}, "Smoke Fly": {"translate": [{"x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": -253.59, "y": 0, "time": 1.6}, {"curve": "stepped", "x": -253.59, "y": 0, "time": 2.4333}, {"x": 0, "y": 0, "time": 2.4667}, {"x": -253.59, "y": 0, "time": 4.9333}]}, "Wild All": {"scale": [{"x": 1, "y": 1, "time": 0}, {"curve": [0.268, 0.39, 0.621, 1], "x": 1.671, "y": 1.671, "time": 0.1333}, {"x": 0.937, "y": 0.937, "time": 0.3}, {"x": 1.047, "y": 1.047, "time": 0.3667}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.4333}, {"x": 1, "y": 1, "time": 3.3333}, {"curve": [0.268, 0.39, 0.621, 1], "x": 1.671, "y": 1.671, "time": 3.4667}, {"x": 0.937, "y": 0.937, "time": 3.6333}, {"x": 1.047, "y": 1.047, "time": 3.7}, {"x": 1, "y": 1, "time": 3.7667}]}, "chantrai": {"rotate": [{"curve": [0.249, 0, 0.627, 0.51], "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0.69, "time": 0.4333}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 0.69, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.31, "time": 1.1667}, {"angle": 0, "time": 1.6667}, {"curve": "stepped", "angle": 0.69, "time": 2.1}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 0.69, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.31, "time": 2.8333}, {"curve": [0.249, 0, 0.627, 0.51], "angle": 0, "time": 3.3333}, {"curve": "stepped", "angle": 0.69, "time": 3.7667}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 0.69, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.31, "time": 4.5}, {"curve": [0.249, 0, 0.627, 0.51], "angle": 0, "time": 5}, {"curve": "stepped", "angle": 0.69, "time": 5.4333}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 0.69, "time": 5.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.31, "time": 6.1667}, {"angle": 0, "time": 6.6667}]}, "riu": {"scale": [{"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.1333}, {"curve": "stepped", "x": 1, "y": 0.747, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 0.747, "time": 1.1667}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.5}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 1.8}, {"curve": "stepped", "x": 1, "y": 0.747, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 0.747, "time": 2.8333}, {"curve": "stepped", "x": 1, "y": 1, "time": 3.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 3.4667}, {"curve": "stepped", "x": 1, "y": 0.747, "time": 3.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 0.747, "time": 4.5}, {"curve": "stepped", "x": 1, "y": 1, "time": 4.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 5.1333}, {"curve": "stepped", "x": 1, "y": 0.747, "time": 5.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 0.747, "time": 6.1667}, {"x": 1, "y": 1, "time": 6.5}]}, "long5": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.83, "time": 0.3667}, {"angle": 0, "time": 0.6333}, {"angle": 2.41, "time": 0.9333}, {"curve": "stepped", "angle": 0, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.6333}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.83, "time": 1.9667}, {"angle": 0, "time": 2.3}, {"angle": 2.41, "time": 2.7333}, {"curve": "stepped", "angle": 0, "time": 2.9667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.5333}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.83, "time": 3.8667}, {"angle": 0, "time": 4.2}, {"angle": 2.41, "time": 4.6333}, {"curve": "stepped", "angle": 0, "time": 4.8667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.83, "time": 5.6667}, {"angle": 0, "time": 6}, {"angle": 2.41, "time": 6.4333}, {"angle": 0, "time": 6.6667}]}, "than3": {"rotate": [{"curve": "stepped", "angle": -0.48, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.48, "time": 0.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": -15.21, "time": 0.4667}, {"curve": [0.286, 0, 0.626, 0.38], "angle": -12.28, "time": 0.6333}, {"curve": [0.318, 0.28, 0.757, 1], "angle": -14.44, "time": 0.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": -10.25, "time": 1.3333}, {"curve": "stepped", "angle": -0.48, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.48, "time": 1.8}, {"curve": [0.25, 0, 0.75, 1], "angle": -15.21, "time": 2.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": -12.28, "time": 2.3}, {"curve": [0.318, 0.28, 0.757, 1], "angle": -14.44, "time": 2.4333}, {"curve": [0.25, 0, 0.75, 1], "angle": -10.25, "time": 3}, {"curve": "stepped", "angle": -0.48, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.48, "time": 3.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": -15.21, "time": 3.8}, {"curve": [0.25, 0, 0.75, 1], "angle": -12.28, "time": 3.9667}, {"curve": [0.318, 0.28, 0.757, 1], "angle": -14.44, "time": 4.1}, {"curve": [0.25, 0, 0.75, 1], "angle": -15.21, "time": 4.6667}, {"curve": "stepped", "angle": -0.48, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.48, "time": 5.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": -15.21, "time": 5.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": -12.28, "time": 5.6333}, {"curve": [0.318, 0.28, 0.757, 1], "angle": -14.44, "time": 5.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": -10.25, "time": 6.3333}, {"angle": -0.48, "time": 6.6667}]}, "than2": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": -2.35, "y": 0.24, "time": 1}, {"x": 0, "y": 0, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": -2.35, "y": 0.24, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": -2.35, "y": 0.24, "time": 4.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": -2.35, "y": 0.24, "time": 6}, {"x": 0, "y": 0, "time": 6.6667}]}, "set3": {"rotate": [{"angle": -151.3, "time": 0}], "scale": [{"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.5667}, {"curve": [0.25, 0, 0.75, 1], "x": 52.668, "y": 0.688, "time": 0.6333}, {"curve": [0.25, 0, 0.75, 1], "x": 55.054, "y": 0.65, "time": 0.7}, {"curve": [0.25, 0, 0.75, 1], "x": 52.668, "y": 0.688, "time": 0.7667}, {"curve": [0.25, 0, 0.75, 1], "x": 55.054, "y": 0.65, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 52.668, "y": 0.688, "time": 0.9}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 2.2333}, {"curve": [0.25, 0, 0.75, 1], "x": 52.668, "y": 0.688, "time": 2.3}, {"curve": [0.25, 0, 0.75, 1], "x": 55.054, "y": 0.65, "time": 2.3667}, {"curve": [0.25, 0, 0.75, 1], "x": 52.668, "y": 0.688, "time": 2.4333}, {"curve": [0.25, 0, 0.75, 1], "x": 55.054, "y": 0.65, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "x": 52.668, "y": 0.688, "time": 2.5667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 3.9}, {"curve": [0.25, 0, 0.75, 1], "x": 52.668, "y": 0.688, "time": 3.9667}, {"curve": [0.25, 0, 0.75, 1], "x": 55.054, "y": 0.65, "time": 4.0333}, {"curve": [0.25, 0, 0.75, 1], "x": 52.668, "y": 0.688, "time": 4.1}, {"curve": [0.25, 0, 0.75, 1], "x": 55.054, "y": 0.65, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 52.668, "y": 0.688, "time": 4.2333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 5.5667}, {"curve": [0.25, 0, 0.75, 1], "x": 52.668, "y": 0.688, "time": 5.6333}, {"curve": [0.25, 0, 0.75, 1], "x": 55.054, "y": 0.65, "time": 5.7}, {"curve": [0.25, 0, 0.75, 1], "x": 52.668, "y": 0.688, "time": 5.7667}, {"curve": [0.25, 0, 0.75, 1], "x": 55.054, "y": 0.65, "time": 5.8333}, {"x": 52.668, "y": 0.688, "time": 5.9}], "translate": [{"x": -29.12, "y": 114.75, "time": 0}]}, "set2": {"rotate": [{"angle": 80.46, "time": 0}], "scale": [{"curve": "stepped", "x": 0.736, "y": 1, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0.736, "y": 1, "time": 0.4667}, {"curve": [0.25, 0, 0.75, 1], "x": 72.289, "y": 1, "time": 0.5333}, {"curve": [0.25, 0, 0.75, 1], "x": 72.264, "y": 1.372, "time": 0.6}, {"curve": [0.25, 0, 0.75, 1], "x": 72.289, "y": 1, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 74.409, "y": 1, "time": 0.7333}, {"curve": [0.25, 0, 0.75, 1], "x": 72.289, "y": 1, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "x": 74.409, "y": 1, "time": 0.8667}, {"curve": "stepped", "x": 72.289, "y": 1, "time": 0.9333}, {"curve": [0.25, 0, 0.75, 1], "x": 0.736, "y": 1, "time": 2.1333}, {"curve": [0.25, 0, 0.75, 1], "x": 72.289, "y": 1, "time": 2.2}, {"curve": [0.25, 0, 0.75, 1], "x": 72.264, "y": 1.372, "time": 2.2667}, {"curve": [0.25, 0, 0.75, 1], "x": 72.289, "y": 1, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 74.409, "y": 1, "time": 2.4}, {"curve": [0.25, 0, 0.75, 1], "x": 72.289, "y": 1, "time": 2.4667}, {"curve": [0.25, 0, 0.75, 1], "x": 74.409, "y": 1, "time": 2.5333}, {"curve": "stepped", "x": 72.289, "y": 1, "time": 2.6}, {"curve": [0.25, 0, 0.75, 1], "x": 0.736, "y": 1, "time": 3.8}, {"curve": [0.25, 0, 0.75, 1], "x": 72.289, "y": 1, "time": 3.8667}, {"curve": [0.25, 0, 0.75, 1], "x": 72.264, "y": 1.372, "time": 3.9333}, {"curve": [0.25, 0, 0.75, 1], "x": 72.289, "y": 1, "time": 4}, {"curve": [0.25, 0, 0.75, 1], "x": 74.409, "y": 1, "time": 4.0667}, {"curve": [0.25, 0, 0.75, 1], "x": 72.289, "y": 1, "time": 4.1333}, {"curve": [0.25, 0, 0.75, 1], "x": 74.409, "y": 1, "time": 4.2}, {"curve": "stepped", "x": 72.289, "y": 1, "time": 4.2667}, {"curve": [0.25, 0, 0.75, 1], "x": 0.736, "y": 1, "time": 5.4667}, {"curve": [0.25, 0, 0.75, 1], "x": 72.289, "y": 1, "time": 5.5333}, {"curve": [0.25, 0, 0.75, 1], "x": 72.264, "y": 1.372, "time": 5.6}, {"curve": [0.25, 0, 0.75, 1], "x": 72.289, "y": 1, "time": 5.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 74.409, "y": 1, "time": 5.7333}, {"curve": [0.25, 0, 0.75, 1], "x": 72.289, "y": 1, "time": 5.8}, {"curve": [0.25, 0, 0.75, 1], "x": 74.409, "y": 1, "time": 5.8667}, {"x": 72.289, "y": 1, "time": 5.9333}], "translate": [{"x": 69.53, "y": -158.64, "time": 0}]}, "longtren2": {"rotate": [{"angle": 0, "time": 0}, {"angle": -6.92, "time": 1.1667}, {"angle": 7.55, "time": 2.2667}, {"angle": 0, "time": 3.3333}, {"angle": -6.92, "time": 4.5}, {"angle": 7.55, "time": 5.6}, {"angle": 0, "time": 6.6667}]}, "vaithatlung8": {"rotate": [{"curve": [0.377, 0.51, 0.749, 1], "angle": -164.72, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 129.94, "time": 0.2}, {"curve": [0.249, 0, 0.627, 0.51], "angle": -92.09, "time": 0.6333}, {"curve": [0.377, 0.51, 0.749, 1], "angle": -164.72, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 129.94, "time": 1.0333}, {"curve": [0.249, 0, 0.627, 0.51], "angle": -92.09, "time": 1.4667}, {"curve": [0.377, 0.51, 0.749, 1], "angle": -164.72, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 129.94, "time": 1.8667}, {"curve": [0.249, 0, 0.627, 0.51], "angle": -92.09, "time": 2.3}, {"curve": [0.377, 0.51, 0.749, 1], "angle": -164.72, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 129.94, "time": 2.7}, {"curve": [0.249, 0, 0.627, 0.51], "angle": -92.09, "time": 3.1333}, {"curve": [0.377, 0.51, 0.749, 1], "angle": -164.72, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 129.94, "time": 3.5333}, {"curve": [0.249, 0, 0.627, 0.51], "angle": -92.09, "time": 3.9667}, {"curve": [0.377, 0.51, 0.749, 1], "angle": -164.72, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 129.94, "time": 4.3667}, {"curve": [0.249, 0, 0.627, 0.51], "angle": -92.09, "time": 4.8}, {"curve": [0.377, 0.51, 0.749, 1], "angle": -164.72, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 129.94, "time": 5.2}, {"curve": [0.249, 0, 0.627, 0.51], "angle": -92.09, "time": 5.6333}, {"curve": [0.377, 0.51, 0.749, 1], "angle": -164.72, "time": 5.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 129.94, "time": 6.0333}, {"curve": [0.249, 0, 0.627, 0.51], "angle": -92.09, "time": 6.4667}, {"angle": -164.72, "time": 6.6667}]}, "vaithatlung7": {"rotate": [{"curve": [0.381, 0.55, 0.742, 1], "angle": -16.51, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -36.75, "time": 0.1667}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 18.28, "time": 0.6}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -16.51, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": -36.75, "time": 1}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 18.28, "time": 1.4333}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -16.51, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -36.75, "time": 1.8333}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 18.28, "time": 2.2667}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -16.51, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": -36.75, "time": 2.6667}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 18.28, "time": 3.1}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -16.51, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -36.75, "time": 3.5}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 18.28, "time": 3.9333}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -16.51, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": -36.75, "time": 4.3333}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 18.28, "time": 4.7667}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -16.51, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": -36.75, "time": 5.1667}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 18.28, "time": 5.6}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -16.51, "time": 5.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": -36.75, "time": 6}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 18.28, "time": 6.4333}, {"angle": -16.51, "time": 6.6667}]}, "vaithatlung9": {"rotate": [{"curve": [0.363, 0.44, 0.755, 1], "angle": -8.76, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.79, "time": 0.2667}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -13.4, "time": 0.6667}, {"curve": [0.363, 0.44, 0.755, 1], "angle": -8.76, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.79, "time": 1.1}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -13.4, "time": 1.5}, {"curve": [0.363, 0.44, 0.755, 1], "angle": -8.76, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.79, "time": 1.9333}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -13.4, "time": 2.3333}, {"curve": [0.363, 0.44, 0.755, 1], "angle": -8.76, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.79, "time": 2.7667}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -13.4, "time": 3.1667}, {"curve": [0.363, 0.44, 0.755, 1], "angle": -8.76, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.79, "time": 3.6}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -13.4, "time": 4}, {"curve": [0.363, 0.44, 0.755, 1], "angle": -8.76, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.79, "time": 4.4333}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -13.4, "time": 4.8333}, {"curve": [0.363, 0.44, 0.755, 1], "angle": -8.76, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.79, "time": 5.2667}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -13.4, "time": 5.6667}, {"curve": [0.363, 0.44, 0.755, 1], "angle": -8.76, "time": 5.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.79, "time": 6.1}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -13.4, "time": 6.5}, {"angle": -8.76, "time": 6.6667}]}, "hong": {"translate": [{"curve": [0.249, 0, 0.627, 0.51], "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": -3.34, "time": 0.4333}, {"curve": [0.377, 0.51, 0.749, 1], "x": 0, "y": -3.34, "time": 0.6667}, {"curve": [0.267, 0.07, 0.752, 1], "x": 0, "y": -6.34, "time": 1.1667}, {"x": 0, "y": 0, "time": 1.6667}, {"curve": "stepped", "x": 0, "y": -3.34, "time": 2.1}, {"curve": [0.377, 0.51, 0.749, 1], "x": 0, "y": -3.34, "time": 2.3333}, {"curve": [0.267, 0.07, 0.752, 1], "x": 0, "y": -6.34, "time": 2.8333}, {"curve": [0.249, 0, 0.627, 0.51], "x": 0, "y": 0, "time": 3.3333}, {"curve": "stepped", "x": 0, "y": -3.34, "time": 3.7667}, {"curve": [0.377, 0.51, 0.749, 1], "x": 0, "y": -3.34, "time": 4}, {"curve": [0.267, 0.07, 0.752, 1], "x": 0, "y": -6.34, "time": 4.5}, {"curve": [0.249, 0, 0.627, 0.51], "x": 0, "y": 0, "time": 5}, {"curve": "stepped", "x": 0, "y": -3.34, "time": 5.4333}, {"curve": [0.377, 0.51, 0.749, 1], "x": 0, "y": -3.34, "time": 5.6667}, {"curve": [0.267, 0.07, 0.752, 1], "x": 0, "y": -6.34, "time": 6.1667}, {"x": 0, "y": 0, "time": 6.6667}]}, "vaithatlung4": {"rotate": [{"curve": [0.382, 0.58, 0.734, 1], "angle": -0.27, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 0.1333}, {"curve": [0.243, 0, 0.651, 0.61], "angle": -4.97, "time": 0.5667}, {"curve": [0.382, 0.58, 0.734, 1], "angle": -0.27, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 0.9667}, {"curve": [0.243, 0, 0.651, 0.61], "angle": -4.97, "time": 1.4}, {"curve": [0.382, 0.58, 0.734, 1], "angle": -0.27, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 1.8}, {"curve": [0.243, 0, 0.651, 0.61], "angle": -4.97, "time": 2.2333}, {"curve": [0.382, 0.58, 0.734, 1], "angle": -0.27, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 2.6333}, {"curve": [0.243, 0, 0.651, 0.61], "angle": -4.97, "time": 3.0667}, {"curve": [0.382, 0.58, 0.734, 1], "angle": -0.27, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 3.4667}, {"curve": [0.243, 0, 0.651, 0.61], "angle": -4.97, "time": 3.9}, {"curve": [0.382, 0.58, 0.734, 1], "angle": -0.27, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 4.3}, {"curve": [0.243, 0, 0.651, 0.61], "angle": -4.97, "time": 4.7333}, {"curve": [0.382, 0.58, 0.734, 1], "angle": -0.27, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 5.1333}, {"curve": [0.243, 0, 0.651, 0.61], "angle": -4.97, "time": 5.5667}, {"curve": [0.382, 0.58, 0.734, 1], "angle": -0.27, "time": 5.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 5.9667}, {"curve": [0.243, 0, 0.651, 0.61], "angle": -4.97, "time": 6.4}, {"angle": -0.27, "time": 6.6667}]}, "vaithatlung3": {"rotate": [{"curve": [0.375, 0.62, 0.716, 1], "angle": 0.61, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 0.1}, {"curve": [0.243, 0, 0.68, 0.71], "angle": -4.97, "time": 0.5}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 0.61, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 0.9333}, {"curve": [0.243, 0, 0.68, 0.71], "angle": -4.97, "time": 1.3333}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 0.61, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 1.7667}, {"curve": [0.243, 0, 0.68, 0.71], "angle": -4.97, "time": 2.1667}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 0.61, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 2.6}, {"curve": [0.243, 0, 0.68, 0.71], "angle": -4.97, "time": 3}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 0.61, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 3.4333}, {"curve": [0.243, 0, 0.68, 0.71], "angle": -4.97, "time": 3.8333}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 0.61, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 4.2667}, {"curve": [0.243, 0, 0.68, 0.71], "angle": -4.97, "time": 4.6667}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 0.61, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 5.1}, {"curve": [0.243, 0, 0.68, 0.71], "angle": -4.97, "time": 5.5}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 0.61, "time": 5.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 5.9333}, {"curve": [0.243, 0, 0.68, 0.71], "angle": -4.97, "time": 6.3333}, {"angle": 0.61, "time": 6.6667}]}, "vaithatlung6": {"rotate": [{"curve": [0.377, 0.51, 0.749, 1], "angle": -1.59, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 0.2}, {"curve": [0.249, 0, 0.627, 0.51], "angle": -4.97, "time": 0.6333}, {"curve": [0.377, 0.51, 0.749, 1], "angle": -1.59, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 1.0333}, {"curve": [0.249, 0, 0.627, 0.51], "angle": -4.97, "time": 1.4667}, {"curve": [0.377, 0.51, 0.749, 1], "angle": -1.59, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 1.8667}, {"curve": [0.249, 0, 0.627, 0.51], "angle": -4.97, "time": 2.3}, {"curve": [0.377, 0.51, 0.749, 1], "angle": -1.59, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 2.7}, {"curve": [0.249, 0, 0.627, 0.51], "angle": -4.97, "time": 3.1333}, {"curve": [0.377, 0.51, 0.749, 1], "angle": -1.59, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 3.5333}, {"curve": [0.249, 0, 0.627, 0.51], "angle": -4.97, "time": 3.9667}, {"curve": [0.377, 0.51, 0.749, 1], "angle": -1.59, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 4.3667}, {"curve": [0.249, 0, 0.627, 0.51], "angle": -4.97, "time": 4.8}, {"curve": [0.377, 0.51, 0.749, 1], "angle": -1.59, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 5.2}, {"curve": [0.249, 0, 0.627, 0.51], "angle": -4.97, "time": 5.6333}, {"curve": [0.377, 0.51, 0.749, 1], "angle": -1.59, "time": 5.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 6.0333}, {"curve": [0.249, 0, 0.627, 0.51], "angle": -4.97, "time": 6.4667}, {"angle": -1.59, "time": 6.6667}]}, "vaithatlung5": {"rotate": [{"curve": [0.381, 0.55, 0.742, 1], "angle": -0.91, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 0.1667}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -4.97, "time": 0.6}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -0.91, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 1}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -4.97, "time": 1.4333}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -0.91, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 1.8333}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -4.97, "time": 2.2667}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -0.91, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 2.6667}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -4.97, "time": 3.1}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -0.91, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 3.5}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -4.97, "time": 3.9333}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -0.91, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 4.3333}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -4.97, "time": 4.7667}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -0.91, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 5.1667}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -4.97, "time": 5.6}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -0.91, "time": 5.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 6}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -4.97, "time": 6.4333}, {"angle": -0.91, "time": 6.6667}]}, "Frame Light Left": {"scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 2, "y": 2, "time": 0.8333}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 2, "y": 2, "time": 2.5}, {"curve": "stepped", "x": 0, "y": 0, "time": 3.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 3.5}, {"curve": [0.25, 0, 0.75, 1], "x": 2, "y": 2, "time": 4.1667}, {"curve": "stepped", "x": 0, "y": 0, "time": 4.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 5.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 2, "y": 2, "time": 5.8333}, {"x": 0, "y": 0, "time": 6.5}], "translate": [{"curve": "stepped", "x": 0, "y": -115, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -115, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 115, "time": 1.3333}, {"curve": "stepped", "x": 0, "y": -115, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -115, "time": 2}, {"curve": "stepped", "x": 0, "y": 115, "time": 3}, {"curve": "stepped", "x": 0, "y": -115, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -115, "time": 3.6667}, {"curve": "stepped", "x": 0, "y": 115, "time": 4.6667}, {"curve": "stepped", "x": 0, "y": -115, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -115, "time": 5.3333}, {"curve": "stepped", "x": 0, "y": 115, "time": 6.3333}, {"x": 0, "y": -115, "time": 6.6667}]}, "vaithatlung2": {"rotate": [{"curve": [0.364, 0.64, 0.7, 1], "angle": 1.07, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 0.0667}, {"curve": [0.244, 0, 0.704, 0.81], "angle": -4.97, "time": 0.4667}, {"curve": [0.364, 0.64, 0.7, 1], "angle": 1.07, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 0.9}, {"curve": [0.244, 0, 0.704, 0.81], "angle": -4.97, "time": 1.3}, {"curve": [0.364, 0.64, 0.7, 1], "angle": 1.07, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 1.7333}, {"curve": [0.244, 0, 0.704, 0.81], "angle": -4.97, "time": 2.1333}, {"curve": [0.364, 0.64, 0.7, 1], "angle": 1.07, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 2.5667}, {"curve": [0.244, 0, 0.704, 0.81], "angle": -4.97, "time": 2.9667}, {"curve": [0.364, 0.64, 0.7, 1], "angle": 1.07, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 3.4}, {"curve": [0.244, 0, 0.704, 0.81], "angle": -4.97, "time": 3.8}, {"curve": [0.364, 0.64, 0.7, 1], "angle": 1.07, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 4.2333}, {"curve": [0.244, 0, 0.704, 0.81], "angle": -4.97, "time": 4.6333}, {"curve": [0.364, 0.64, 0.7, 1], "angle": 1.07, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 5.0667}, {"curve": [0.244, 0, 0.704, 0.81], "angle": -4.97, "time": 5.4667}, {"curve": [0.364, 0.64, 0.7, 1], "angle": 1.07, "time": 5.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.44, "time": 5.9}, {"curve": [0.244, 0, 0.704, 0.81], "angle": -4.97, "time": 6.3}, {"angle": 1.07, "time": 6.6667}]}, "longtren4": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.7, "time": 1.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.81, "time": 2.3}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.7, "time": 4.5}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.81, "time": 5.6333}, {"angle": 0, "time": 6.6667}]}, "longtren3": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0.44, "time": 1.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.7, "time": 2.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0.44, "time": 4.5}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.7, "time": 5.6}, {"angle": 0, "time": 6.6667}]}, "set1": {"rotate": [{"angle": 12.45, "time": 0}], "scale": [{"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.3667}, {"curve": [0.25, 0, 0.75, 1], "x": 72.289, "y": 1, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "x": 72.264, "y": 1.372, "time": 0.5667}, {"curve": [0.25, 0, 0.75, 1], "x": 72.289, "y": 1, "time": 0.6333}, {"curve": [0.25, 0, 0.75, 1], "x": 74.409, "y": 1, "time": 0.7}, {"curve": "stepped", "x": 72.289, "y": 1, "time": 0.7667}, {"curve": "stepped", "x": 74.409, "y": 1, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 2.0333}, {"curve": [0.25, 0, 0.75, 1], "x": 72.289, "y": 1, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 72.264, "y": 1.372, "time": 2.2333}, {"curve": [0.25, 0, 0.75, 1], "x": 72.289, "y": 1, "time": 2.3}, {"curve": [0.25, 0, 0.75, 1], "x": 74.409, "y": 1, "time": 2.3667}, {"curve": "stepped", "x": 72.289, "y": 1, "time": 2.4333}, {"curve": "stepped", "x": 74.409, "y": 1, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 3.7}, {"curve": [0.25, 0, 0.75, 1], "x": 72.289, "y": 1, "time": 3.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 72.264, "y": 1.372, "time": 3.9}, {"curve": [0.25, 0, 0.75, 1], "x": 72.289, "y": 1, "time": 3.9667}, {"curve": [0.25, 0, 0.75, 1], "x": 74.409, "y": 1, "time": 4.0333}, {"curve": "stepped", "x": 72.289, "y": 1, "time": 4.1}, {"curve": "stepped", "x": 74.409, "y": 1, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 5.3667}, {"curve": [0.25, 0, 0.75, 1], "x": 72.289, "y": 1, "time": 5.5}, {"curve": [0.25, 0, 0.75, 1], "x": 72.264, "y": 1.372, "time": 5.5667}, {"curve": [0.25, 0, 0.75, 1], "x": 72.289, "y": 1, "time": 5.6333}, {"curve": [0.25, 0, 0.75, 1], "x": 74.409, "y": 1, "time": 5.7}, {"curve": "stepped", "x": 72.289, "y": 1, "time": 5.7667}, {"x": 74.409, "y": 1, "time": 5.8333}], "translate": [{"x": 0.66, "y": -102.53, "time": 0}]}, "chanphai2": {"rotate": [{"curve": [0.249, 0, 0.627, 0.51], "angle": 0, "time": 0}, {"curve": "stepped", "angle": 3.97, "time": 0.4333}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 3.97, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.92, "time": 1.1667}, {"angle": 0, "time": 1.6667}, {"curve": "stepped", "angle": 3.97, "time": 2.1}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 3.97, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.92, "time": 2.8333}, {"curve": [0.249, 0, 0.627, 0.51], "angle": 0, "time": 3.3333}, {"curve": "stepped", "angle": 3.97, "time": 3.7667}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 3.97, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.92, "time": 4.5}, {"curve": [0.249, 0, 0.627, 0.51], "angle": 0, "time": 5}, {"curve": "stepped", "angle": 3.97, "time": 5.4333}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 3.97, "time": 5.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.92, "time": 6.1667}, {"angle": 0, "time": 6.6667}]}, "Dust Fly": {"translate": [{"curve": "stepped", "x": -254, "y": 0, "time": 0}, {"x": -9.84, "y": 0, "time": 0.0333}, {"curve": "stepped", "x": -254, "y": 0, "time": 1.6333}, {"x": -9.84, "y": 0, "time": 1.6667}, {"curve": "stepped", "x": -254, "y": 0, "time": 3.3333}, {"x": -9.84, "y": 0, "time": 3.3667}, {"curve": "stepped", "x": -254, "y": 0, "time": 4.9667}, {"x": -9.84, "y": 0, "time": 5}, {"x": -254, "y": 0, "time": 6.6667}]}, "chantrai2": {"rotate": [{"curve": [0.249, 0, 0.627, 0.51], "angle": 0, "time": 0}, {"curve": "stepped", "angle": 7.42, "time": 0.4333}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 7.42, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.91, "time": 1.1667}, {"angle": 0, "time": 1.6667}, {"curve": "stepped", "angle": 7.42, "time": 2.1}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 7.42, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.91, "time": 2.8333}, {"curve": [0.249, 0, 0.627, 0.51], "angle": 0, "time": 3.3333}, {"curve": "stepped", "angle": 7.42, "time": 3.7667}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 7.42, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.91, "time": 4.5}, {"curve": [0.249, 0, 0.627, 0.51], "angle": 0, "time": 5}, {"curve": "stepped", "angle": 7.42, "time": 5.4333}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 7.42, "time": 5.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.91, "time": 6.1667}, {"angle": 0, "time": 6.6667}]}, "longduoi": {"rotate": [{"angle": 0, "time": 0}, {"angle": -3.12, "time": 1.1667}, {"angle": -10.66, "time": 2.3333}, {"angle": 0, "time": 3.3333}, {"angle": -3.12, "time": 4.5}, {"angle": -10.66, "time": 5.6667}, {"angle": 0, "time": 6.6667}]}, "toc8": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -2.04, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 0.5667}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.04, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.9667}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 1.4}, {"angle": -2.04, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.8}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 2.2333}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.04, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.6333}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 3.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.04, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 3.9}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.04, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.3}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 4.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.04, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 5.5667}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.04, "time": 5.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.9667}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 6.4}, {"angle": -2.04, "time": 6.6667}]}, "toc9": {"rotate": [{"curve": [0.382, 0.58, 0.734, 1], "angle": 2.27, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.1333}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 8.51, "time": 0.5667}, {"curve": [0.382, 0.58, 0.734, 1], "angle": 2.27, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.9667}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 8.51, "time": 1.4}, {"curve": [0.382, 0.58, 0.734, 1], "angle": 2.27, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.8}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 8.51, "time": 2.2333}, {"curve": [0.382, 0.58, 0.734, 1], "angle": 2.27, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.6333}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 8.51, "time": 3.0667}, {"curve": [0.382, 0.58, 0.734, 1], "angle": 2.27, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.4667}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 8.51, "time": 3.9}, {"curve": [0.382, 0.58, 0.734, 1], "angle": 2.27, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.3}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 8.51, "time": 4.7333}, {"curve": [0.382, 0.58, 0.734, 1], "angle": 2.27, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.1333}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 8.51, "time": 5.5667}, {"curve": [0.382, 0.58, 0.734, 1], "angle": 2.27, "time": 5.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.9667}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 8.51, "time": 6.4}, {"angle": 2.27, "time": 6.6667}]}, "Wild Mask": {"translate": [{"x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 229.09, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 1}, {"curve": "stepped", "x": 229.09, "y": 0, "time": 1.6667}, {"x": 0, "y": 0, "time": 2.2667}, {"curve": "stepped", "x": 229.09, "y": 0, "time": 2.9333}, {"x": 0, "y": 0, "time": 3.2667}, {"curve": "stepped", "x": 229.09, "y": 0, "time": 3.9333}, {"x": 0, "y": 0, "time": 4.4667}, {"curve": "stepped", "x": 229.09, "y": 0, "time": 5.1333}, {"x": 0, "y": 0, "time": 5.4667}, {"x": 229.09, "y": 0, "time": 6.1333}]}, "Gem Spartkle 2": {"rotate": [{"curve": "stepped", "angle": -90, "time": 0}, {"angle": 0, "time": 0.6667}, {"angle": -90, "time": 1.6667}, {"angle": 0, "time": 2.3333}, {"curve": "stepped", "angle": -90, "time": 3.3333}, {"angle": 0, "time": 4}, {"curve": "stepped", "angle": -90, "time": 5}, {"angle": 0, "time": 5.6667}, {"angle": -90, "time": 6.6667}], "scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.6667}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 1.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.3333}, {"curve": "stepped", "x": 1, "y": 1, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 3}, {"curve": "stepped", "x": 0, "y": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 4}, {"curve": "stepped", "x": 1, "y": 1, "time": 4.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 4.6667}, {"curve": "stepped", "x": 0, "y": 0, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 5.6667}, {"curve": "stepped", "x": 1, "y": 1, "time": 6}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 6.3333}, {"x": 0, "y": 0, "time": 6.6667}]}, "toc6": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -0.77, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 0.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.77, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.9}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 1.3}, {"angle": -0.77, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 2.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.77, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.5667}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 2.9667}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.77, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.4}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 3.8}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.77, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.2333}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 4.6333}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.77, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 5.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.77, "time": 5.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.9}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 6.3}, {"angle": -0.77, "time": 6.6667}]}, "toc7": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -1.28, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.1}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.28, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.9333}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 1.3333}, {"angle": -1.28, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.28, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.6}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 3}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.28, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.4333}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 3.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.28, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 4.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.28, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.1}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 5.5}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.28, "time": 5.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.9333}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 6.3333}, {"angle": -1.28, "time": 6.6667}]}, "toc4": {"rotate": [{"curve": [0.382, 0.58, 0.734, 1], "angle": 1.95, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.1333}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 7.3, "time": 0.5667}, {"curve": [0.382, 0.58, 0.734, 1], "angle": 1.95, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.9667}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 7.3, "time": 1.4}, {"curve": [0.382, 0.58, 0.734, 1], "angle": 1.95, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.8}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 7.3, "time": 2.2333}, {"curve": [0.382, 0.58, 0.734, 1], "angle": 1.95, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.6333}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 7.3, "time": 3.0667}, {"curve": [0.382, 0.58, 0.734, 1], "angle": 1.95, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.4667}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 7.3, "time": 3.9}, {"curve": [0.382, 0.58, 0.734, 1], "angle": 1.95, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.3}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 7.3, "time": 4.7333}, {"curve": [0.382, 0.58, 0.734, 1], "angle": 1.95, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.1333}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 7.3, "time": 5.5667}, {"curve": [0.382, 0.58, 0.734, 1], "angle": 1.95, "time": 5.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.9667}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 7.3, "time": 6.4}, {"angle": 1.95, "time": 6.6667}]}, "toc5": {"rotate": [{"curve": [0.381, 0.55, 0.742, 1], "angle": -2.44, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.1667}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -6.63, "time": 0.6}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -2.44, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -6.63, "time": 1.4333}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -2.44, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.8333}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -6.63, "time": 2.2667}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -2.44, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.6667}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -6.63, "time": 3.1}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -2.44, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.5}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -6.63, "time": 3.9333}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -2.44, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.3333}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -6.63, "time": 4.7667}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -2.44, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.1667}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -6.63, "time": 5.6}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -2.44, "time": 5.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 6}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -6.63, "time": 6.4333}, {"angle": -2.44, "time": 6.6667}]}, "toc2": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 0.4333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 1.2667}, {"angle": 0, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 2.1}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 2.9333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 3.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 4.6}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 5.4333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.38, "time": 6.2667}, {"angle": 0, "time": 6.6667}]}, "Gem Sparkle 1": {"rotate": [{"curve": "stepped", "angle": -90, "time": 0}, {"angle": 0, "time": 0.6667}, {"angle": -90, "time": 1.6667}, {"angle": 0, "time": 2.3333}, {"curve": "stepped", "angle": -90, "time": 3.3333}, {"angle": 0, "time": 4}, {"curve": "stepped", "angle": -90, "time": 5}, {"angle": 0, "time": 5.6667}, {"angle": -90, "time": 6.6667}], "scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.6667}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 1.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.3333}, {"curve": "stepped", "x": 1, "y": 1, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 3}, {"curve": "stepped", "x": 0, "y": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 4}, {"curve": "stepped", "x": 1, "y": 1, "time": 4.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 4.6667}, {"curve": "stepped", "x": 0, "y": 0, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 5.6667}, {"curve": "stepped", "x": 1, "y": 1, "time": 6}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 6.3333}, {"x": 0, "y": 0, "time": 6.6667}]}, "toc3": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 1.46, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.1}, {"curve": [0.25, 0, 0.75, 1], "angle": 7.3, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.46, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.9333}, {"curve": [0.25, 0, 0.75, 1], "angle": 7.3, "time": 1.3333}, {"angle": 1.46, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": 7.3, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.46, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.6}, {"curve": [0.25, 0, 0.75, 1], "angle": 7.3, "time": 3}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.46, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.4333}, {"curve": [0.25, 0, 0.75, 1], "angle": 7.3, "time": 3.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.46, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": 7.3, "time": 4.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.46, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.1}, {"curve": [0.25, 0, 0.75, 1], "angle": 7.3, "time": 5.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.46, "time": 5.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.9333}, {"curve": [0.25, 0, 0.75, 1], "angle": 7.3, "time": 6.3333}, {"angle": 1.46, "time": 6.6667}]}, "Rock 3": {"translate": [{"x": 0, "y": -324.94, "time": 0}, {"x": 0, "y": 353.31, "time": 1.3333}, {"x": 0, "y": -324.94, "time": 1.6667}, {"x": 0, "y": 353.31, "time": 4.6667}]}, "Rock 4": {"translate": [{"curve": "stepped", "x": 0, "y": -324.94, "time": 0}, {"x": 0, "y": -324.94, "time": 1}, {"x": 0, "y": 353.31, "time": 3}]}, "Rock 5": {"translate": [{"x": 0, "y": -324.94, "time": 0}, {"curve": "stepped", "x": 0, "y": 353.31, "time": 1.3333}, {"x": 0, "y": 51.86, "time": 1.6667}, {"curve": "stepped", "x": 0, "y": -324.94, "time": 3.3333}, {"x": 0, "y": -324.94, "time": 5}, {"x": 0, "y": 51.86, "time": 6.6667}]}, "Rock 6": {"translate": [{"curve": "stepped", "x": 0, "y": 353.31, "time": 0}, {"x": 0, "y": -324.94, "time": 0.6667}, {"x": 0, "y": -168.42, "time": 1.6667}, {"curve": "stepped", "x": 0, "y": 353.31, "time": 3.3333}, {"x": 0, "y": -324.94, "time": 4}, {"curve": "stepped", "x": 0, "y": 353.31, "time": 5}, {"x": 0, "y": -324.94, "time": 5.6667}, {"x": 0, "y": -168.42, "time": 6.6667}]}, "taytrai": {"rotate": [{"curve": [0.364, 0.64, 0.7, 1], "angle": 0.34, "time": 0}, {"curve": [0.251, 0, 0.623, 0.49], "angle": 0, "time": 0.1}, {"curve": [0.244, 0, 0.704, 0.81], "angle": 5.82, "time": 0.9333}, {"curve": [0.364, 0.64, 0.7, 1], "angle": 0.34, "time": 1.6667}, {"curve": [0.251, 0, 0.623, 0.49], "angle": 0, "time": 1.7667}, {"curve": [0.244, 0, 0.704, 0.81], "angle": 5.82, "time": 2.6}, {"curve": [0.364, 0.64, 0.7, 1], "angle": 0.34, "time": 3.3333}, {"curve": [0.251, 0, 0.623, 0.49], "angle": 0, "time": 3.4333}, {"curve": [0.244, 0, 0.704, 0.81], "angle": 5.82, "time": 4.2667}, {"curve": [0.364, 0.64, 0.7, 1], "angle": 0.34, "time": 5}, {"curve": [0.251, 0, 0.623, 0.49], "angle": 0, "time": 5.1}, {"curve": [0.244, 0, 0.704, 0.81], "angle": 5.82, "time": 5.9333}, {"angle": 0.34, "time": 6.6667}]}, "Rock 1": {"translate": [{"x": 0, "y": -324.94, "time": 0}, {"x": 0, "y": 353.31, "time": 1.6667}]}, "Rock 2": {"translate": [{"curve": "stepped", "x": 0, "y": 70.7, "time": 0}, {"x": 0, "y": 70.7, "time": 1.6667}, {"x": 0, "y": -324.94, "time": 2.6667}, {"x": 0, "y": 70.7, "time": 5}]}, "taytrai9": {"rotate": [{"curve": [0.377, 0.51, 0.749, 1], "angle": -5.16, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.4}, {"curve": [0.249, 0, 0.627, 0.51], "angle": -10.9, "time": 1.2333}, {"curve": [0.377, 0.51, 0.749, 1], "angle": -5.16, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.0667}, {"curve": [0.249, 0, 0.627, 0.51], "angle": -10.9, "time": 2.9}, {"curve": [0.377, 0.51, 0.749, 1], "angle": -5.16, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.7333}, {"curve": [0.249, 0, 0.627, 0.51], "angle": -10.9, "time": 4.5667}, {"curve": [0.377, 0.51, 0.749, 1], "angle": -5.16, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.4}, {"curve": [0.249, 0, 0.627, 0.51], "angle": -10.9, "time": 6.2333}, {"angle": -5.16, "time": 6.6667}]}, "taytrai8": {"rotate": [{"curve": [0.382, 0.58, 0.734, 1], "angle": -2.91, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.2667}, {"curve": [0.243, 0, 0.651, 0.61], "angle": -10.9, "time": 1.1}, {"curve": [0.382, 0.58, 0.734, 1], "angle": -2.91, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.9333}, {"curve": [0.243, 0, 0.651, 0.61], "angle": -10.9, "time": 2.7667}, {"curve": [0.382, 0.58, 0.734, 1], "angle": -2.91, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.6}, {"curve": [0.243, 0, 0.651, 0.61], "angle": -10.9, "time": 4.4333}, {"curve": [0.382, 0.58, 0.734, 1], "angle": -2.91, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.2667}, {"curve": [0.243, 0, 0.651, 0.61], "angle": -10.9, "time": 6.1}, {"angle": -2.91, "time": 6.6667}]}, "toc10": {"rotate": [{"curve": [0.381, 0.55, 0.742, 1], "angle": 3.13, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.1667}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 8.51, "time": 0.6}, {"curve": [0.381, 0.55, 0.742, 1], "angle": 3.13, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 8.51, "time": 1.4333}, {"curve": [0.381, 0.55, 0.742, 1], "angle": 3.13, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.8333}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 8.51, "time": 2.2667}, {"curve": [0.381, 0.55, 0.742, 1], "angle": 3.13, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.6667}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 8.51, "time": 3.1}, {"curve": [0.381, 0.55, 0.742, 1], "angle": 3.13, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.5}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 8.51, "time": 3.9333}, {"curve": [0.381, 0.55, 0.742, 1], "angle": 3.13, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.3333}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 8.51, "time": 4.7667}, {"curve": [0.381, 0.55, 0.742, 1], "angle": 3.13, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.1667}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 8.51, "time": 5.6}, {"curve": [0.381, 0.55, 0.742, 1], "angle": 3.13, "time": 5.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 6}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 8.51, "time": 6.4333}, {"angle": 3.13, "time": 6.6667}]}, "daychuyen": {"shear": [{"x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 8.64, "y": 7.51, "time": 0.6667}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 8.64, "y": 7.51, "time": 2.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 3}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 8.64, "y": 7.51, "time": 4}, {"curve": "stepped", "x": 0, "y": 0, "time": 4.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": 8.64, "y": 7.51, "time": 5.6667}, {"x": 0, "y": 0, "time": 6.3333}], "scale": [{"x": 1, "y": 1, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0.918, "y": 1, "time": 0.6667}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0.918, "y": 1, "time": 2.3333}, {"curve": "stepped", "x": 1, "y": 1, "time": 3}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0.918, "y": 1, "time": 4}, {"curve": "stepped", "x": 1, "y": 1, "time": 4.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": 0.918, "y": 1, "time": 5.6667}, {"x": 1, "y": 1, "time": 6.3333}]}, "taytrai7": {"rotate": [{"curve": [0.363, 0.44, 0.755, 1], "angle": -3.04, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.5}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -4.81, "time": 1.3333}, {"curve": [0.363, 0.44, 0.755, 1], "angle": -3.04, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.1667}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -4.81, "time": 3}, {"curve": [0.363, 0.44, 0.755, 1], "angle": -3.04, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.8333}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -4.81, "time": 4.6667}, {"curve": [0.363, 0.44, 0.755, 1], "angle": -3.04, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.5}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -4.81, "time": 6.3333}, {"angle": -3.04, "time": 6.6667}]}, "Hit": {"rotate": [{"angle": 0, "time": 0.5}, {"angle": 98.09, "time": 0.6667}, {"curve": "stepped", "angle": 0, "time": 0.8333}, {"angle": 0, "time": 2.1667}, {"angle": 98.09, "time": 2.3333}, {"curve": "stepped", "angle": 0, "time": 2.5}, {"angle": 0, "time": 3.8333}, {"angle": 98.09, "time": 4}, {"curve": "stepped", "angle": 0, "time": 4.1667}, {"angle": 0, "time": 5.5}, {"angle": 98.09, "time": 5.6667}, {"angle": 0, "time": 5.8333}], "scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 0.5}, {"x": 1.5, "y": 1.8, "time": 0.6667}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.8333}, {"x": 0, "y": 0, "time": 2.1667}, {"x": 1.5, "y": 1.8, "time": 2.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 2.5}, {"x": 0, "y": 0, "time": 3.8333}, {"x": 1.5, "y": 1.8, "time": 4}, {"curve": "stepped", "x": 0, "y": 0, "time": 4.1667}, {"x": 0, "y": 0, "time": 5.5}, {"x": 1.5, "y": 1.8, "time": 5.6667}, {"x": 0, "y": 0, "time": 5.8333}]}, "taytrai6": {"rotate": [{"curve": [0.377, 0.51, 0.749, 1], "angle": 2.57, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.4}, {"curve": [0.249, 0, 0.627, 0.51], "angle": 5.43, "time": 1.2333}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 2.57, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.0667}, {"curve": [0.249, 0, 0.627, 0.51], "angle": 5.43, "time": 2.9}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 2.57, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.7333}, {"curve": [0.249, 0, 0.627, 0.51], "angle": 5.43, "time": 4.5667}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 2.57, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.4}, {"curve": [0.249, 0, 0.627, 0.51], "angle": 5.43, "time": 6.2333}, {"angle": 2.57, "time": 6.6667}]}, "taytrai5": {"rotate": [{"curve": [0.363, 0.44, 0.755, 1], "angle": 8.69, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.5}, {"curve": [0.258, 0, 0.619, 0.45], "angle": 13.75, "time": 1.3333}, {"curve": [0.363, 0.44, 0.755, 1], "angle": 8.69, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.1667}, {"curve": [0.258, 0, 0.619, 0.45], "angle": 13.75, "time": 3}, {"curve": [0.363, 0.44, 0.755, 1], "angle": 8.69, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.8333}, {"curve": [0.258, 0, 0.619, 0.45], "angle": 13.75, "time": 4.6667}, {"curve": [0.363, 0.44, 0.755, 1], "angle": 8.69, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.5}, {"curve": [0.258, 0, 0.619, 0.45], "angle": 13.75, "time": 6.3333}, {"angle": 8.69, "time": 6.6667}]}, "taytrai4": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 1.92, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.4}, {"curve": [0.25, 0, 0.75, 1], "angle": 4, "time": 1.2333}, {"angle": 1.92, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": 4, "time": 2.9}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.92, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 4, "time": 4.5667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.92, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.4}, {"curve": [0.25, 0, 0.75, 1], "angle": 4, "time": 6.2333}, {"angle": 1.92, "time": 6.6667}]}, "taytrai3": {"rotate": [{"curve": [0.382, 0.58, 0.734, 1], "angle": -0.24, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.2667}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 4.5, "time": 1.1}, {"curve": [0.382, 0.58, 0.734, 1], "angle": -0.24, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.9333}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 4.5, "time": 2.7667}, {"curve": [0.382, 0.58, 0.734, 1], "angle": -0.24, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.6}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 4.5, "time": 4.4333}, {"curve": [0.382, 0.58, 0.734, 1], "angle": -0.24, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.2667}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 4.5, "time": 6.1}, {"angle": -0.24, "time": 6.6667}]}, "Frame Light Right": {"scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": [0.093, 0.32, 0.463, 1], "x": 0, "y": 0, "time": 0.1667}, {"curve": [0.541, 0, 0.963, 0.87], "x": 2, "y": 2, "time": 0.8333}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 2, "y": 2, "time": 2.5}, {"curve": "stepped", "x": 0, "y": 0, "time": 3.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 3.5}, {"curve": [0.25, 0, 0.75, 1], "x": 2, "y": 2, "time": 4.1667}, {"curve": "stepped", "x": 0, "y": 0, "time": 4.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 5.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 2, "y": 2, "time": 5.8333}, {"x": 0, "y": 0, "time": 6.5}], "translate": [{"curve": "stepped", "x": 0, "y": -115, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -115, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 115, "time": 1.3333}, {"x": 0, "y": 115, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -115, "time": 2}, {"curve": "stepped", "x": 0, "y": 115, "time": 3}, {"curve": "stepped", "x": 0, "y": -115, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -115, "time": 3.6667}, {"curve": "stepped", "x": 0, "y": 115, "time": 4.6667}, {"curve": "stepped", "x": 0, "y": -115, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -115, "time": 5.3333}, {"x": 0, "y": 115, "time": 6.3333}]}, "taytrai2": {"rotate": [{"curve": [0.382, 0.58, 0.734, 1], "angle": 3.66, "time": 0}, {"curve": [0.258, 0, 0.619, 0.45], "angle": 0, "time": 0.2667}, {"curve": "stepped", "angle": 24.54, "time": 1}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 24.54, "time": 1.1}, {"curve": [0.382, 0.58, 0.734, 1], "angle": 3.66, "time": 1.6667}, {"curve": [0.258, 0, 0.619, 0.45], "angle": 0, "time": 1.9333}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 24.54, "time": 2.7667}, {"curve": [0.382, 0.58, 0.734, 1], "angle": 3.66, "time": 3.3333}, {"curve": [0.258, 0, 0.619, 0.45], "angle": 0, "time": 3.6}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 24.54, "time": 4.4333}, {"curve": [0.382, 0.58, 0.734, 1], "angle": 3.66, "time": 5}, {"curve": [0.258, 0, 0.619, 0.45], "angle": 0, "time": 5.2667}, {"curve": [0.243, 0, 0.651, 0.61], "angle": 24.54, "time": 6.1}, {"angle": 3.66, "time": 6.6667}]}, "tayphai": {"rotate": [{"curve": "stepped", "angle": -0.91, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.91, "time": 0.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": -10.04, "time": 0.4}, {"curve": [0.276, 0, 0.621, 0.4], "angle": -9.68, "time": 0.5667}, {"curve": [0.312, 0.27, 0.67, 0.68], "angle": -11.21, "time": 0.8}, {"curve": [0.382, 0.57, 0.737, 1], "angle": -8.55, "time": 1}, {"curve": "stepped", "angle": -0.91, "time": 1.4333}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.91, "time": 1.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": -10.04, "time": 2.0667}, {"curve": [0.276, 0, 0.621, 0.4], "angle": -9.68, "time": 2.2333}, {"curve": [0.312, 0.27, 0.67, 0.68], "angle": -11.21, "time": 2.4667}, {"curve": [0.382, 0.57, 0.737, 1], "angle": -8.55, "time": 2.6667}, {"curve": "stepped", "angle": -0.91, "time": 3.1}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.91, "time": 3.4}, {"curve": [0.25, 0, 0.75, 1], "angle": -10.04, "time": 3.7333}, {"curve": [0.276, 0, 0.621, 0.4], "angle": -9.68, "time": 3.9}, {"curve": [0.312, 0.27, 0.67, 0.68], "angle": -11.21, "time": 4.1333}, {"curve": [0.382, 0.57, 0.737, 1], "angle": -8.55, "time": 4.3333}, {"curve": "stepped", "angle": -0.91, "time": 4.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.91, "time": 5.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": -10.04, "time": 5.4}, {"curve": [0.276, 0, 0.621, 0.4], "angle": -9.68, "time": 5.5667}, {"curve": [0.312, 0.27, 0.67, 0.68], "angle": -11.21, "time": 5.8}, {"curve": [0.382, 0.57, 0.737, 1], "angle": -8.55, "time": 6}, {"angle": -0.91, "time": 6.4333}]}, "tayphai2": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 1.15, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -13.58, "time": 0.3333}, {"curve": [0.263, 0, 0.618, 0.43], "angle": -10.65, "time": 0.5}, {"curve": [0.327, 0.31, 0.673, 0.69], "angle": -16.18, "time": 0.7333}, {"curve": [0.382, 0.57, 0.737, 1], "angle": -9.23, "time": 0.9333}, {"curve": [0.25, 0, 0.75, 1], "angle": -8.61, "time": 1.1667}, {"curve": "stepped", "angle": 1.15, "time": 1.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.15, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -13.58, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -10.65, "time": 2.1667}, {"curve": [0.327, 0.31, 0.673, 0.69], "angle": -16.18, "time": 2.4}, {"curve": [0.382, 0.57, 0.737, 1], "angle": -9.23, "time": 2.6}, {"curve": [0.25, 0, 0.75, 1], "angle": -8.61, "time": 2.8333}, {"curve": "stepped", "angle": 1.15, "time": 3.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.15, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -13.58, "time": 3.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -10.65, "time": 3.8333}, {"curve": [0.327, 0.31, 0.673, 0.69], "angle": -16.18, "time": 4.0667}, {"curve": [0.382, 0.57, 0.737, 1], "angle": -9.23, "time": 4.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": -8.61, "time": 4.5}, {"curve": "stepped", "angle": 1.15, "time": 4.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.15, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": -13.58, "time": 5.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -10.65, "time": 5.5}, {"curve": [0.327, 0.31, 0.673, 0.69], "angle": -16.18, "time": 5.7333}, {"curve": [0.382, 0.57, 0.737, 1], "angle": -9.23, "time": 5.9333}, {"curve": [0.25, 0, 0.75, 1], "angle": -8.61, "time": 6.1667}, {"angle": 1.15, "time": 6.5}]}, "taytrai10": {"rotate": [{"curve": [0.363, 0.44, 0.755, 1], "angle": -6.89, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.5}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -10.9, "time": 1.3333}, {"curve": [0.363, 0.44, 0.755, 1], "angle": -6.89, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.1667}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -10.9, "time": 3}, {"curve": [0.363, 0.44, 0.755, 1], "angle": -6.89, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.8333}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -10.9, "time": 4.6667}, {"curve": [0.363, 0.44, 0.755, 1], "angle": -6.89, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.5}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -10.9, "time": 6.3333}, {"angle": -6.89, "time": 6.6667}]}, "toc11": {"rotate": [{"curve": [0.377, 0.51, 0.749, 1], "angle": 4.03, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.2}, {"curve": [0.249, 0, 0.627, 0.51], "angle": 8.51, "time": 0.6333}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 4.03, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.0333}, {"curve": [0.249, 0, 0.627, 0.51], "angle": 8.51, "time": 1.4667}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 4.03, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.8667}, {"curve": [0.249, 0, 0.627, 0.51], "angle": 8.51, "time": 2.3}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 4.03, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.7}, {"curve": [0.249, 0, 0.627, 0.51], "angle": 8.51, "time": 3.1333}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 4.03, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.5333}, {"curve": [0.249, 0, 0.627, 0.51], "angle": 8.51, "time": 3.9667}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 4.03, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.3667}, {"curve": [0.249, 0, 0.627, 0.51], "angle": 8.51, "time": 4.8}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 4.03, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.2}, {"curve": [0.249, 0, 0.627, 0.51], "angle": 8.51, "time": 5.6333}, {"curve": [0.377, 0.51, 0.749, 1], "angle": 4.03, "time": 5.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 6.0333}, {"curve": [0.249, 0, 0.627, 0.51], "angle": 8.51, "time": 6.4667}, {"angle": 4.03, "time": 6.6667}]}}, "deform": {"default": {"hong": {"hong": [{"offset": 2, "vertices": [-0.1449, 0.26461, -0.00204, 0.07005, 0, 0, 0.13353, 0.48542, 0.07504, -0.1133, -0.11245, -0.21197, -0.16906, -0.20658, -0.14502, -0.04899, 0.02967, 0.23366, 0, 0, 0, 0, 0.02829, 0.13375, -0.00447, -0.04764], "curve": [0.364, 0.64, 0.7, 1], "time": 0}, {"curve": [0.25, 0, 0.75, 1], "time": 0.1}, {"offset": 2, "vertices": [-2.46747, 4.50602, -0.0348, 1.1929, 0, 0, 2.27389, 8.26618, 1.2779, -1.92947, -1.91499, -3.60968, -2.87895, -3.51792, -2.4695, -0.83427, 0.50528, 3.97907, 0, 0, 0, 0, 0.4818, 2.27755, -0.07614, -0.81134], "curve": [0.244, 0, 0.704, 0.81], "time": 0.9333}, {"offset": 2, "vertices": [-0.1449, 0.26461, -0.00204, 0.07005, 0, 0, 0.13353, 0.48542, 0.07504, -0.1133, -0.11245, -0.21197, -0.16906, -0.20658, -0.14502, -0.04899, 0.02967, 0.23366, 0, 0, 0, 0, 0.02829, 0.13375, -0.00447, -0.04764], "curve": [0.364, 0.64, 0.7, 1], "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "time": 1.7667}, {"offset": 2, "vertices": [-2.46747, 4.50602, -0.0348, 1.1929, 0, 0, 2.27389, 8.26618, 1.2779, -1.92947, -1.91499, -3.60968, -2.87895, -3.51792, -2.4695, -0.83427, 0.50528, 3.97907, 0, 0, 0, 0, 0.4818, 2.27755, -0.07614, -0.81134], "curve": [0.244, 0, 0.704, 0.81], "time": 2.6}, {"offset": 2, "vertices": [-0.1449, 0.26461, -0.00204, 0.07005, 0, 0, 0.13353, 0.48542, 0.07504, -0.1133, -0.11245, -0.21197, -0.16906, -0.20658, -0.14502, -0.04899, 0.02967, 0.23366, 0, 0, 0, 0, 0.02829, 0.13375, -0.00447, -0.04764], "curve": [0.364, 0.64, 0.7, 1], "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "time": 3.4333}, {"offset": 2, "vertices": [-2.46747, 4.50602, -0.0348, 1.1929, 0, 0, 2.27389, 8.26618, 1.2779, -1.92947, -1.91499, -3.60968, -2.87895, -3.51792, -2.4695, -0.83427, 0.50528, 3.97907, 0, 0, 0, 0, 0.4818, 2.27755, -0.07614, -0.81134], "curve": [0.244, 0, 0.704, 0.81], "time": 4.2667}, {"offset": 2, "vertices": [-0.1449, 0.26461, -0.00204, 0.07005, 0, 0, 0.13353, 0.48542, 0.07504, -0.1133, -0.11245, -0.21197, -0.16906, -0.20658, -0.14502, -0.04899, 0.02967, 0.23366, 0, 0, 0, 0, 0.02829, 0.13375, -0.00447, -0.04764], "curve": [0.364, 0.64, 0.7, 1], "time": 5}, {"curve": [0.25, 0, 0.75, 1], "time": 5.1}, {"offset": 2, "vertices": [-2.46747, 4.50602, -0.0348, 1.1929, 0, 0, 2.27389, 8.26618, 1.2779, -1.92947, -1.91499, -3.60968, -2.87895, -3.51792, -2.4695, -0.83427, 0.50528, 3.97907, 0, 0, 0, 0, 0.4818, 2.27755, -0.07614, -0.81134], "curve": [0.244, 0, 0.704, 0.81], "time": 5.9333}, {"offset": 2, "vertices": [-0.1449, 0.26461, -0.00204, 0.07005, 0, 0, 0.13353, 0.48542, 0.07504, -0.1133, -0.11245, -0.21197, -0.16906, -0.20658, -0.14502, -0.04899, 0.02967, 0.23366, 0, 0, 0, 0, 0.02829, 0.13375, -0.00447, -0.04764], "time": 6.6667}]}, "dau": {"dau": [{"offset": 36, "vertices": [0.15702, -0.27098, -0.31974, 0.26475, -0.41554, -0.21145, 0.46372, 0.22959], "curve": [0.381, 0.59, 0.728, 1], "time": 0}, {"curve": [0.25, 0, 0.75, 1], "time": 0.2333}, {"offset": 36, "vertices": [0.72276, -1.24731, -1.47176, 1.21864, -1.91273, -0.9733, 2.13452, 1.05682], "curve": [0.31, 0, 0.645, 0.35], "time": 1.0667}, {"offset": 8, "vertices": [-1.9119, 0.02071, -5.45546, 0.04154, -1.9119, 0.02071, 0.15327, -0.06944, 0.67047, 0.12634, -1.18156, -0.23325, -4.02142, -0.73072, -0.69543, 0.16132, 1.59233, -0.35258, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.62808, -1.08391, -1.27895, 1.059, -1.66216, -0.8458, 1.8549, 0.91837], "curve": [0.303, 0.24, 0.678, 0.7], "time": 1.2333}, {"offset": 8, "vertices": [-0.77726, -0.29262, -0.77722, -0.2926, -0.77725, -0.29259, -0.77722, -0.2926, -0.77722, -0.29259, -0.77721, -0.2926, -0.77727, -0.29261, -0.77723, -0.29261, -0.77723, -0.2926, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.70322, -1.21358, -1.43195, 1.18568, -1.861, -0.94698, 2.07679, 1.02823], "curve": [0.309, 0.21, 0.645, 0.55], "time": 1.3667}, {"offset": 36, "vertices": [0.15702, -0.27098, -0.31974, 0.26475, -0.41554, -0.21145, 0.46372, 0.22959], "curve": [0.381, 0.59, 0.728, 1], "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "time": 1.9}, {"offset": 36, "vertices": [0.72276, -1.24731, -1.47176, 1.21864, -1.91273, -0.9733, 2.13452, 1.05682], "curve": [0.31, 0, 0.645, 0.35], "time": 2.7333}, {"offset": 8, "vertices": [-1.9119, 0.02071, -5.45546, 0.04154, -1.9119, 0.02071, 0.15327, -0.06944, 0.67047, 0.12634, -1.18156, -0.23325, -4.02142, -0.73072, -0.69543, 0.16132, 1.59233, -0.35258, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.62808, -1.08391, -1.27895, 1.059, -1.66216, -0.8458, 1.8549, 0.91837], "curve": [0.303, 0.24, 0.678, 0.7], "time": 2.9}, {"offset": 8, "vertices": [-0.77726, -0.29262, -0.77722, -0.2926, -0.77725, -0.29259, -0.77722, -0.2926, -0.77722, -0.29259, -0.77721, -0.2926, -0.77727, -0.29261, -0.77723, -0.29261, -0.77723, -0.2926, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.70322, -1.21358, -1.43195, 1.18568, -1.861, -0.94698, 2.07679, 1.02823], "curve": [0.309, 0.21, 0.645, 0.55], "time": 3.0333}, {"offset": 36, "vertices": [0.15702, -0.27098, -0.31974, 0.26475, -0.41554, -0.21145, 0.46372, 0.22959], "curve": [0.381, 0.59, 0.728, 1], "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "time": 3.5667}, {"offset": 36, "vertices": [0.72276, -1.24731, -1.47176, 1.21864, -1.91273, -0.9733, 2.13452, 1.05682], "curve": [0.31, 0, 0.645, 0.35], "time": 4.4}, {"offset": 8, "vertices": [-1.9119, 0.02071, -5.45546, 0.04154, -1.9119, 0.02071, 0.15327, -0.06944, 0.67047, 0.12634, -1.18156, -0.23325, -4.02142, -0.73072, -0.69543, 0.16132, 1.59233, -0.35258, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.62808, -1.08391, -1.27895, 1.059, -1.66216, -0.8458, 1.8549, 0.91837], "curve": [0.303, 0.24, 0.678, 0.7], "time": 4.5667}, {"offset": 8, "vertices": [-0.77726, -0.29262, -0.77722, -0.2926, -0.77725, -0.29259, -0.77722, -0.2926, -0.77722, -0.29259, -0.77721, -0.2926, -0.77727, -0.29261, -0.77723, -0.29261, -0.77723, -0.2926, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.70322, -1.21358, -1.43195, 1.18568, -1.861, -0.94698, 2.07679, 1.02823], "curve": [0.309, 0.21, 0.645, 0.55], "time": 4.7}, {"offset": 36, "vertices": [0.15702, -0.27098, -0.31974, 0.26475, -0.41554, -0.21145, 0.46372, 0.22959], "curve": [0.381, 0.59, 0.728, 1], "time": 5}, {"curve": [0.25, 0, 0.75, 1], "time": 5.2333}, {"offset": 36, "vertices": [0.72276, -1.24731, -1.47176, 1.21864, -1.91273, -0.9733, 2.13452, 1.05682], "curve": [0.31, 0, 0.645, 0.35], "time": 6.0667}, {"offset": 8, "vertices": [-1.9119, 0.02071, -5.45546, 0.04154, -1.9119, 0.02071, 0.15327, -0.06944, 0.67047, 0.12634, -1.18156, -0.23325, -4.02142, -0.73072, -0.69543, 0.16132, 1.59233, -0.35258, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.62808, -1.08391, -1.27895, 1.059, -1.66216, -0.8458, 1.8549, 0.91837], "curve": [0.303, 0.24, 0.678, 0.7], "time": 6.2333}, {"offset": 8, "vertices": [-0.77726, -0.29262, -0.77722, -0.2926, -0.77725, -0.29259, -0.77722, -0.2926, -0.77722, -0.29259, -0.77721, -0.2926, -0.77727, -0.29261, -0.77723, -0.29261, -0.77723, -0.2926, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.70322, -1.21358, -1.43195, 1.18568, -1.861, -0.94698, 2.07679, 1.02823], "curve": [0.309, 0.21, 0.645, 0.55], "time": 6.3667}, {"offset": 36, "vertices": [0.15702, -0.27098, -0.31974, 0.26475, -0.41554, -0.21145, 0.46372, 0.22959], "time": 6.6667}]}, "vaithatlung": {"vaithatlung": [{"vertices": [-0.04999, 0.00635, 0.0734, -0.76364, 0.14557, -0.76594, 0.0441, -2.09165, 0.1275, -2.0927, 0.22664, -2.08663, 0.0021, -2.09864, -0.41207, -2.31727, -0.35735, -2.32632, -0.26112, -2.33909, -0.50831, -2.29808, -0.48048, -2.30406, 0.10815, -1.70084, -0.07328, -1.70269, -0.05268, -1.70346, 0.33535, -1.72028, 0.35614, -1.7161, 0.38405, -1.71008, 0.46598, -1.68957, -0.0259, -1.77225, 0.05946, -1.77143, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.02258, 0.06241, 0.02971, 0.05953, 0.03592, 0.05874, -0.01772, -0.4156, -0.00488, -0.41566, -0.04859, -0.41288, -0.03839, -0.4143, 0.23903, -0.74067, 0.27225, -0.73041, 0.19274, -0.75515, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.23911, -0.03036], "curve": [0.375, 0.62, 0.716, 1], "time": 0}, {"curve": [0.247, 0, 0.632, 0.53], "time": 0.2667}, {"vertices": [-1.19004, 11.41142, 1.19294, -14.95837, 0.98779, -14.98492, 2.69401, -5.35913, 2.37505, -5.42136, 1.64567, -5.47746, -0.17386, -5.69736, 4.33998, 3.86427, 3.93391, 4.04744, 2.83782, 4.44126, 2.07738, 4.49973, 0.55145, 4.86007, 2.65295, 2.90583, 2.23184, 2.80997, 1.31708, 3.00374, 1.49322, -7.65991, 1.58579, -7.64133, 1.71008, -7.61448, 2.07487, -7.52318, -0.11533, -7.89135, 0.26476, -7.88767, -5.03947, -13.30746, -7.49298, -12.097, 0.43427, 2.09586, 0.51162, 2.07834, 0.31033, 2.11777, 3.61627, 10.91569, 4.03969, 10.76535, 3.0443, 11.08767, 3.70251, 9.07962, 2.74261, 9.37987, 2.96214, 9.28211, 1.9285, 9.52902, 1.53226, 4.83877, 0.93284, 4.89317, 0.8808, 4.78778, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.06469, -0.1352], "curve": [0.38, 0.53, 0.746, 1], "time": 1.0333}, {"vertices": [-4.10052, 3.27092, 3.15116, -2.69452, 3.80049, -2.79183, 1.22056, -7.07628, 2.13278, -7.11513, 2.70729, -7.04623, 1.91187, -7.28795, -2.94538, -9.69305, -2.27991, -9.77311, -1.7096, -9.86322, -2.76661, -9.62304, -2.8502, -9.63245, 0.83129, -13.0736, -0.56325, -13.08786, -0.40494, -13.09374, 2.57769, -13.22303, 2.7375, -13.19094, 2.95206, -13.1446, 3.58177, -12.987, -0.1991, -13.62255, 0.45705, -13.6162, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.17358, 0.47972, 0.22839, 0.45762, 0.27614, 0.45152, 1.34056, -0.24272, 1.3774, -0.21292, 1.34402, -0.35506, 1.31724, -0.30447, 3.25413, 0.37259, 3.38255, 0.47981, 3.39849, 0.12226, -1.35654, -7.61614, 0.96358, 7.67569, 1.19473, 7.64309, 1.035, 7.6664, 0, 0, 0, 0, 1.83794, -0.2334], "curve": [0.254, 0, 0.62, 0.47], "time": 1.6667}, {"vertices": [-0.22086, 0.02805, 0.32427, -3.37385, 0.64313, -3.38401, 0.19486, -9.24119, 0.56332, -9.24581, 1.00132, -9.21901, 0.00928, -9.27206, -2.69254, -13.04149, -2.26999, -13.13144, -1.55659, -13.24261, -2.7737, -13.0413, -2.4254, -13.09996, 4.04666, -26.68206, 2.41563, -26.82607, 3.99364, -26.55927, -3.69124, -63.66001, 0.75893, -63.87396, 53.19636, 15.1883, 10.85557, -63.07058, -0.11444, -7.83006, 0.2627, -7.82641, 0, 0, 0, 0, 1.52949, -8.21467, 1.17397, -8.2729, 1.81137, -8.15711, 0.09977, 0.27574, 0.13127, 0.26303, 0.15872, 0.25953, 0.37889, 3.79261, -0.12843, 3.80986, -0.07806, 3.82153, -0.47087, 3.80889, 1.05607, -3.2724, 1.20282, -3.22704, 0.85153, -3.33635, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.05642, -0.13415], "curve": [0.343, 0.37, 0.696, 0.76], "time": 2.2667}, {"vertices": [-0.04999, 0.00635, 0.0734, -0.76364, 0.14557, -0.76594, 0.0441, -2.09165, 0.1275, -2.0927, 0.22664, -2.08663, 0.0021, -2.09864, -0.41207, -2.31727, -0.35735, -2.32632, -0.26112, -2.33909, -0.50831, -2.29808, -0.48048, -2.30406, 0.10815, -1.70084, -0.07328, -1.70269, -0.05268, -1.70346, 0.33535, -1.72028, 0.35614, -1.7161, 0.38405, -1.71008, 0.46598, -1.68957, -0.0259, -1.77225, 0.05946, -1.77143, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.02258, 0.06241, 0.02971, 0.05953, 0.03592, 0.05874, -0.01772, -0.4156, -0.00488, -0.41566, -0.04859, -0.41288, -0.03839, -0.4143, 0.23903, -0.74067, 0.27225, -0.73041, 0.19274, -0.75515, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.23911, -0.03036], "curve": [0.375, 0.62, 0.716, 1], "time": 2.7667}, {"offset": 2, "vertices": [3.72064, 4.42977, 3.43024, 4.65834, 3.66568, 7.1502, 3.20171, 7.36975, 2.45015, 7.65243, 2.77774, 7.53976, 1.55529, 3.84168, 1.30686, 3.9332, 0.90776, 4.04395, 1.08123, 4.00111, 0.76797, 4.07286, 1.22108, -12.50462, 0.68098, -12.54558, 1.65082, -12.45513, -1.89949, -56.33505, 2.47066, -56.3129, 50.14805, 25.73648, 12.03766, -55.0663], "curve": [0.25, 0, 0.75, 1], "time": 3.0333}, {"vertices": [-0.22259, 0.02827, 0.37249, 0.66053, 0.5438, 0.64917, 0.34107, 3.54563, 0.23729, 3.53746, -0.49749, 3.48024, -1.0231, 3.47384, 4.43913, 5.85653, 4.08102, 6.03673, 2.98372, 6.43067, 2.50435, 6.44823, 1.00971, 6.80145, 2.65295, 2.90583, 2.23184, 2.80997, 1.31708, 3.00374, 1.49322, -7.65991, 1.58579, -7.64133, 1.71008, -7.61448, 2.07487, -7.52318, -0.11533, -7.89135, 0.26476, -7.88767, -5.03947, -13.30746, -7.49298, -12.097, 0.43427, 2.09586, 0.51162, 2.07834, 0.31033, 2.11777, 3.61627, 10.91569, 4.03969, 10.76535, 3.0443, 11.08767, 3.70251, 9.07962, 2.74261, 9.37987, 2.96214, 9.28211, 1.9285, 9.52902, 1.53226, 4.83877, 0.93284, 4.89317, 0.8808, 4.78778, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.06469, -0.1352], "curve": [0.38, 0.53, 0.746, 1], "time": 3.7}, {"vertices": [-0.38424, 0.0488, 3.88516, -0.70888, 4.3817, -0.68959, 1.43484, -6.98388, 1.97363, -6.9802, 2.46315, -6.90788, 1.80047, -7.14732, -3.0945, -12.28283, -2.73603, -12.35195, -2.16156, -12.45223, -3.41393, -12.1569, -3.21233, -12.20176, 0.83129, -13.0736, -0.56325, -13.08786, -0.40494, -13.09374, 2.57769, -13.22303, 2.7375, -13.19094, 2.95206, -13.1446, 3.58177, -12.987, -0.1991, -13.62255, 0.45705, -13.6162, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.17358, 0.47972, 0.22839, 0.45762, 0.27614, 0.45152, 0.19663, 0.70795, 0.17864, 0.71577, 0.29865, 0.68492, 0.36846, 0.67555, 4.12558, 0.15703, 4.20509, 0.30177, 4.27149, -0.17621, -5.53667, -4.87076, 4.95144, 5.46464, 4.88973, 5.51991, 4.72263, 5.66363, 0, 0, 0, 0, 1.83794, -0.2334], "curve": [0.243, 0, 0.68, 0.71], "time": 4.4667}, {"vertices": [-0.22086, 0.02805, 0.32427, -3.37385, 0.64313, -3.38401, 0.19486, -9.24119, 0.56332, -9.24581, 1.00132, -9.21901, 0.00928, -9.27206, -2.69254, -13.04149, -2.26999, -13.13144, -1.55659, -13.24261, -2.7737, -13.0413, -2.4254, -13.09996, 4.04666, -26.68206, 2.41563, -26.82607, 3.99364, -26.55927, -3.69124, -63.66001, 0.75893, -63.87396, 53.19636, 15.1883, 10.85557, -63.07058, -0.11444, -7.83006, 0.2627, -7.82641, 0, 0, 0, 0, 1.52949, -8.21467, 1.17397, -8.2729, 1.81137, -8.15711, 0.09977, 0.27574, 0.13127, 0.26303, 0.15872, 0.25953, 0.37889, 3.79261, -0.12843, 3.80986, -0.07806, 3.82153, -0.47087, 3.80889, 1.05607, -3.2724, 1.20282, -3.22704, 0.85153, -3.33635, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.05642, -0.13415], "curve": [0.343, 0.37, 0.696, 0.76], "time": 5.4}, {"vertices": [-0.04999, 0.00635, 0.0734, -0.76364, 0.14557, -0.76594, 0.0441, -2.09165, 0.1275, -2.0927, 0.22664, -2.08663, 0.0021, -2.09864, -0.41207, -2.31727, -0.35735, -2.32632, -0.26112, -2.33909, -0.50831, -2.29808, -0.48048, -2.30406, 0.10815, -1.70084, -0.07328, -1.70269, -0.05268, -1.70346, 0.33535, -1.72028, 0.35614, -1.7161, 0.38405, -1.71008, 0.46598, -1.68957, -0.0259, -1.77225, 0.05946, -1.77143, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.02258, 0.06241, 0.02971, 0.05953, 0.03592, 0.05874, -0.01772, -0.4156, -0.00488, -0.41566, -0.04859, -0.41288, -0.03839, -0.4143, 0.23903, -0.74067, 0.27225, -0.73041, 0.19274, -0.75515, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.23911, -0.03036], "curve": [0.375, 0.62, 0.716, 1], "time": 6.2333}, {"time": 6.6667}]}}}}, "Wild-SonTinh-Expand": {"slots": {"Wild": {"color": [{"color": "ffffffff", "time": 0}, {"color": "ffffff00", "time": 0.2}]}, "Sparkle 4": {"attachment": [{"name": null, "time": 0}]}, "Sparkle 5": {"attachment": [{"name": null, "time": 0}]}, "Sparkle 2": {"attachment": [{"name": null, "time": 0}]}, "Sparkle 3": {"attachment": [{"name": null, "time": 0}]}, "Rock/Rock 4": {"attachment": [{"name": null, "time": 0}]}, "Rock/Rock 3": {"attachment": [{"name": null, "time": 0}]}, "Sparkle 1": {"attachment": [{"name": null, "time": 0}]}, "thanh-ngangBright2": {"attachment": [{"name": null, "time": 0}]}, "Rock/Rock 2": {"attachment": [{"name": null, "time": 0}]}, "Rock/Rock 1": {"attachment": [{"name": null, "time": 0}]}, "Gem Spartkle 2": {"attachment": [{"name": null, "time": 0}]}, "Rock/Rock 6": {"attachment": [{"name": null, "time": 0}]}, "Rock/Rock 5": {"attachment": [{"name": null, "time": 0}]}, "Smoke 3": {"attachment": [{"name": null, "time": 0}]}, "Smoke 2": {"attachment": [{"name": null, "time": 0}]}, "Dust 1": {"attachment": [{"name": null, "time": 0}]}, "Rock Mask": {"attachment": [{"name": null, "time": 0}]}, "Smoke 1": {"attachment": [{"name": null, "time": 0}]}, "Dust 2": {"attachment": [{"name": null, "time": 0}]}, "Dust 3": {"attachment": [{"name": null, "time": 0}]}, "Dust 4": {"attachment": [{"name": null, "time": 0}]}, "Dust 5": {"attachment": [{"name": null, "time": 0}]}, "chanphai": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.467, 0, 0.958, 0.86], "time": 0.1667}, {"color": "ffffffff", "time": 0.2}]}, "Dust 6": {"attachment": [{"name": null, "time": 0}]}, "Dust 7": {"attachment": [{"name": null, "time": 0}]}, "Dust 9": {"attachment": [{"name": null, "time": 0}]}, "Wild Bright": {"attachment": [{"name": null, "time": 0}]}, "Gem Sparkle 1": {"attachment": [{"name": null, "time": 0}]}, "chantrai": {"color": [{"color": "ffffff00", "curve": [0.467, 0, 0.967, 0.86], "time": 0}, {"color": "ffffff00", "curve": [0.467, 0, 0.958, 0.86], "time": 0.1667}, {"color": "ffffffff", "time": 0.2}]}, "set3": {"attachment": [{"name": null, "time": 0}]}, "set2": {"attachment": [{"name": null, "time": 0}]}, "Dust 15": {"attachment": [{"name": null, "time": 0}]}, "thanh-ngangBright": {"attachment": [{"name": null, "time": 0}]}, "Hit": {"attachment": [{"name": null, "time": 0}]}, "Dust 10": {"attachment": [{"name": null, "time": 0}]}, "Frame Light Left": {"attachment": [{"name": null, "time": 0}]}, "Dust 11": {"attachment": [{"name": null, "time": 0}]}, "Frame Light Right": {"attachment": [{"name": null, "time": 0}]}, "set1": {"attachment": [{"name": null, "time": 0}]}, "thanh-docBright2": {"attachment": [{"name": null, "time": 0}]}, "thanh-docBright": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"ngangduoi": {"scale": [{"x": 0.673, "y": 0.673, "time": 0}, {"x": 1, "y": 1, "time": 0.2}], "translate": [{"x": 0, "y": 151.22, "time": 0}, {"x": 0, "y": 0, "time": 0.2}]}, "hong": {"translate": [{"x": 0, "y": -173.67, "time": 0}, {"x": 0, "y": 0, "time": 0.2}]}, "docphai": {"scale": [{"x": 0.577, "y": 0.232, "time": 0}, {"x": 1, "y": 1, "time": 0.2}], "translate": [{"x": -25.75, "y": 0.87, "time": 0}, {"x": 0, "y": 0, "time": 0.2}]}, "docTrai": {"scale": [{"x": 0.579, "y": 0.243, "time": 0}, {"x": 1, "y": 1, "time": 0.2}], "translate": [{"x": 27.66, "y": 1.86, "time": 0}, {"x": 0, "y": 0, "time": 0.2}]}, "Wild All": {"scale": [{"x": 0.639, "y": 0.639, "time": 0}], "translate": [{"x": 3.17, "y": 140.35, "time": 0}, {"x": 3.17, "y": 14.84, "time": 0.2}]}, "ngangtren": {"scale": [{"x": 0.67, "y": 0.67, "time": 0}, {"x": 1, "y": 1, "time": 0.2}], "translate": [{"x": 0, "y": -148.32, "time": 0}, {"x": 0, "y": 0, "time": 0.2}]}}, "deform": {"default": {"BG-che": {"BG-che": [{"vertices": [27.05802, -150.018, 25.00405, 149.36601, -27.63597, 149.36598, -21.71403, -146.07602], "time": 0}, {"vertices": [2.71204, -7.23196], "time": 0.2}]}, "hong-chenguoi": {"hong-chenguoi": [{"vertices": [-77.32472, 26.40736, -35.47839, 53.71183, 0, 0, 0, 0, -23.15935, 73.35441, 85.05794, 105.24748, 155.99176, 81.87927, 357.55518, 13.25045, 341.1251, -96.42712, 113.04045, -138.70752, -91.95697, -24.87486], "time": 0}, {"vertices": [-53.47052, 49.72324, -29.56533, 44.75986, 0, 0, 3.5956, 6.99015, -55.29657, 54.86804, 30.49628, 91.86836, 103.10107, 75.03236, 300.92905, 16.07433, 288.00626, -87.59302, 63.08765, -125.02425, -143.37914, 6.50455], "time": 0.0333}, {"vertices": [-76.56387, 56.25446, -23.65226, 35.80788, 0, 0, 2.87648, 5.59212, -44.23726, 43.89443, -28.63728, 87.67576, 48.12222, 82.66698, 244.8945, 23.16378, 231.29257, -75.87375, 6.40834, -112.5265, -198.39473, 17.86189], "time": 0.0667}, {"vertices": [-57.42291, 42.19085, -17.7392, 26.85592, 0, 0, 2.15736, 4.19409, -53.92781, 3.35402, -92.1674, 51.4765, -9.30662, 91.91644, 189.83295, 16.59035, 177.42375, -60.98546, -47.5463, -104.31325, -250.88287, 97.02483], "time": 0.1}, {"vertices": [-38.28194, 28.12723, -11.82613, 17.90394, 0, 0, 1.43824, 2.79606, -35.95187, 2.23602, 12.24874, 8.85896, -82.35719, 11.01673, 126.55529, 11.06024, 118.28249, -40.65697, -97.1925, 5.92149, -167.25525, 64.68321], "time": 0.1333}, {"offset": 18, "vertices": [-2.77733, -2.15149], "time": 0.2}]}}}}}}