{"skins": {"default": {"r2": {"r": {"x": 69.93, "width": 39, "y": 1.21, "height": 42}}, "efect_light19": {"efect_light": {"scaleX": 1.325, "scaleY": 1.325, "x": 0.18, "width": 80, "y": 0.33, "height": 86}}, "efect_light17": {"efect_light": {"scaleX": 1.325, "scaleY": 1.325, "x": 0.47, "width": 80, "y": 0.83, "height": 86}}, "efect_light18": {"efect_light": {"scaleX": 1.325, "scaleY": 1.325, "x": 0.18, "width": 80, "y": 0.33, "height": 86}}, "efect_light16": {"efect_light": {"scaleX": 1.325, "scaleY": 1.325, "x": 0.47, "width": 80, "y": 0.83, "height": 86}}, "efect_light": {"efect_light": {"scaleX": 1.325, "scaleY": 1.325, "x": 0.47, "width": 80, "y": 0.83, "height": 86}}, "efect_light13": {"efect_light": {"scaleX": 1.325, "scaleY": 1.325, "x": 0.47, "width": 80, "y": 0.83, "height": 86}}, "trang tri de 2": {"trang tri de 2": {"x": -62.25, "width": 15, "y": 18.61, "height": 21}}, "efect_light14": {"efect_light": {"scaleX": 1.325, "scaleY": 1.325, "x": 0.47, "width": 80, "y": 0.83, "height": 86}}, "trang tri de 3": {"trang tri de 2": {"x": -1.34, "width": 15, "y": 14.41, "height": 21}}, "efect_light11": {"efect_light": {"scaleX": 1.325, "scaleY": 1.325, "x": 0.18, "width": 80, "y": 0.33, "height": 86}}, "efect_light12": {"efect_light": {"scaleX": 1.325, "scaleY": 1.325, "x": 0.47, "width": 80, "y": 0.83, "height": 86}}, "efect_light10": {"efect_light": {"scaleX": 1.325, "scaleY": 1.325, "x": 0.47, "width": 80, "y": 0.83, "height": 86}}, "s2": {"s": {"x": -67.07, "width": 31, "y": 1, "height": 46}}, "binh 2": {"binh 2": {"scaleX": 1.325, "scaleY": 1.325, "rotation": 81.63, "x": -1.1, "width": 85, "y": 1.87, "height": 162}}, "eff heal4": {"eff heal": {"x": 2.29, "width": 39, "y": -0.69, "height": 116}}, "eff heal3": {"eff heal": {"x": 2.29, "width": 39, "y": -0.69, "height": 116}}, "c2": {"c": {"x": -46.06, "width": 42, "y": 0.62, "height": 47}}, "domdom": {"domdom": {"x": 0.28, "width": 24, "y": 0.53, "height": 24}}, "efect_light24": {"efect_light": {"scaleX": 1.325, "scaleY": 1.325, "x": 0.47, "width": 80, "y": 0.83, "height": 86}}, "eff heal2": {"eff heal": {"x": 2.29, "width": 39, "y": -0.69, "height": 116}}, "efect_light22": {"efect_light": {"scaleX": 1.325, "scaleY": 1.325, "x": 0.47, "width": 80, "y": 0.83, "height": 86}}, "efect_light23": {"efect_light": {"scaleX": 1.325, "scaleY": 1.325, "x": 0.47, "width": 80, "y": 0.83, "height": 86}}, "glow deế copy": {"glow deế copy": {"triangles": [1, 2, 3, 1, 3, 0], "path": "glow de copy", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [80.83, 4.83, -85.17, 4.83, -85.17, 98.83, 80.83, 98.83], "width": 166, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 94}}, "efect_light21": {"efect_light": {"scaleX": 1.325, "scaleY": 1.325, "x": 0.18, "width": 80, "y": 0.33, "height": 86}}, "t2": {"t": {"x": -8.19, "width": 38, "y": 2.58, "height": 46}}, "Layer 6": {"de": {"x": -1.67, "width": 147, "y": 14.83, "height": 50}}, "t3": {"t": {"x": 24.28, "width": 38, "y": 1.2, "height": 46}}, "t4": {"t": {"x": 0.54, "width": 38, "y": 1.2, "height": 46}}, "trang tri de": {"trang tri de": {"scaleX": -1, "x": 60.81, "width": 13, "y": 18.99, "height": 16}}, "eff heal": {"eff heal": {"x": 2.29, "width": 39, "y": -0.69, "height": 116}}, "scatter": {"scatter": {"x": -3.77, "width": 173, "y": 2.01, "height": 45}}, "a": {"a": {"x": -28.74, "width": 41, "y": 0.9, "height": 44}}, "c": {"c": {"x": -54.57, "width": 42, "y": 2, "height": 47}}, "e": {"e": {"x": 37.62, "width": 31, "y": 1.52, "height": 45}}, "efect_light3": {"efect_light": {"scaleX": 1.325, "scaleY": 1.325, "x": 0.47, "width": 80, "y": 0.83, "height": 86}}, "efect_light2": {"efect_light": {"scaleX": 1.325, "scaleY": 1.325, "x": 0.47, "width": 80, "y": 0.83, "height": 86}}, "efect_light5": {"efect_light": {"scaleX": 1.325, "scaleY": 1.325, "x": 0.47, "width": 80, "y": 0.83, "height": 86}}, "nuoc": {"nuoc": {"triangles": [25, 19, 0, 25, 0, 1, 18, 19, 25, 24, 25, 1, 24, 1, 2, 25, 29, 17, 25, 17, 18, 29, 25, 24, 16, 17, 29, 23, 24, 2, 28, 16, 29, 22, 23, 2, 22, 2, 3, 15, 16, 28, 21, 22, 3, 21, 3, 4, 14, 15, 28, 20, 21, 4, 29, 23, 28, 23, 29, 24, 27, 23, 22, 27, 28, 23, 5, 20, 4, 13, 14, 28, 13, 28, 27, 6, 20, 5, 7, 20, 6, 26, 27, 22, 26, 22, 21, 26, 21, 20, 12, 13, 27, 8, 26, 20, 8, 20, 7, 9, 27, 26, 11, 12, 27, 9, 26, 8, 27, 10, 11, 9, 10, 27], "uvs": [0.49643, 0.0339, 0.59958, 0.11713, 0.77178, 0.27748, 0.90424, 0.442, 1, 0.62525, 1, 0.71896, 0.936, 0.78574, 0.91417, 0.80851, 0.74197, 0.89597, 0.51017, 0.96678, 0.38102, 1, 0.29824, 0.98136, 0.18565, 0.89597, 0.07968, 0.76894, 0, 0.66898, 0, 0.54404, 0.09293, 0.3816, 0.19558, 0.24416, 0.31148, 0.10672, 0.41083, 0, 0.83997, 0.7248, 0.74472, 0.63769, 0.65387, 0.54023, 0.55982, 0.4286, 0.48783, 0.30452, 0.42606, 0.19106, 0.63016, 0.83991, 0.36494, 0.71908, 0.26057, 0.53889, 0.26131, 0.37353], "vertices": [1.04, -11.43, 7.65, -3.62, 20.72, 9.85, 34.69, 21.03, 50.89, 30.39, 59.79, 32.25, 66.93, 29.76, 69.36, 28.91, 79.82, 20.37, 89.44, 7.94, 94.21, 0.89, 93.47, -4.43, 86.77, -12.85, 76.04, -21.7, 67.54, -28.44, 55.68, -30.93, 39.1, -28.61, 24.77, -25.21, 10.27, -21.03, -1.11, -17.22, 62.34, 22.81, 55.26, 15.4, 47.14, 8.03, 37.72, 0.2, 26.84, -6.57, 16.84, -12.51, 75.89, 12.58, 67.74, -5.66, 51.93, -15.47, 36.23, -18.72], "width": 61, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38, 10, 12, 12, 14], "type": "mesh", "hull": 20, "height": 97}}, "efect_light4": {"efect_light": {"scaleX": 1.325, "scaleY": 1.325, "x": 0.47, "width": 80, "y": 0.83, "height": 86}}, "efect_light6": {"efect_light": {"scaleX": 2, "scaleY": 2, "x": 0.47, "width": 80, "y": 0.83, "height": 86}}, "efect_light9": {"efect_light": {"scaleX": 1.325, "scaleY": 1.325, "x": 0.47, "width": 80, "y": 0.83, "height": 86}}, "e2": {"e": {"x": 46.13, "width": 31, "y": 0.14, "height": 45}}, "efect_light8": {"efect_light": {"scaleX": 1.325, "scaleY": 1.325, "x": 0.47, "width": 80, "y": 0.83, "height": 86}}, "binh": {"binh": {"rotation": 81.63, "x": 1.9, "width": 71, "y": 6.07, "height": 187}}, "a2": {"a": {"x": -20.24, "width": 41, "y": -0.48, "height": 44}}, "r": {"r": {"x": 61.42, "width": 39, "y": 2.59, "height": 42}}, "s": {"s": {"x": -75.58, "width": 31, "y": 2.38, "height": 46}}, "t": {"t": {"x": 15.77, "width": 38, "y": 2.58, "height": 46}}, "domdom2": {"domdom": {"x": 0.28, "width": 24, "y": 0.53, "height": 24}}, "domdom3": {"domdom": {"x": 0.28, "width": 24, "y": 0.53, "height": 24}}, "scatter copy": {"scatter copy": {"x": -13.71, "width": 247, "y": 1.98, "height": 107}}, "domdom4": {"domdom": {"x": 0.28, "width": 24, "y": 0.53, "height": 24}}, "domdom5": {"domdom": {"x": 0.28, "width": 24, "y": 0.53, "height": 24}}, "domdom6": {"domdom": {"x": 0.28, "width": 24, "y": 0.53, "height": 24}}, "domdom7": {"domdom": {"x": 0.28, "width": 24, "y": 0.53, "height": 24}}}}, "skeleton": {"images": "./image/", "width": 332.86, "spine": "3.7.94", "audio": "D:/The witcher/animation scatter/scatter/export", "hash": "oqwAh6uNLdlOmDimb5uCqvdmY64", "height": 286.67}, "slots": [{"attachment": "de", "name": "Layer 6", "bone": "đe"}, {"attachment": "nuoc", "name": "nuoc", "bone": "binh"}, {"attachment": "efect_light", "blend": "additive", "name": "efect_light6", "bone": "eff light6"}, {"attachment": "binh", "name": "binh", "bone": "binh"}, {"attachment": "binh 2", "name": "binh 2", "bone": "binh"}, {"attachment": "glow deế copy", "name": "glow deế copy", "bone": "đe"}, {"attachment": "efect_light", "blend": "additive", "name": "efect_light", "bone": "eff light"}, {"attachment": "efect_light", "blend": "additive", "name": "efect_light2", "bone": "eff light2"}, {"attachment": "efect_light", "blend": "additive", "name": "efect_light17", "bone": "eff light17"}, {"attachment": "efect_light", "blend": "additive", "name": "efect_light22", "bone": "eff light22"}, {"attachment": "efect_light", "blend": "additive", "name": "efect_light8", "bone": "eff light8"}, {"attachment": "efect_light", "blend": "additive", "name": "efect_light9", "bone": "eff light9"}, {"attachment": "efect_light", "blend": "additive", "name": "efect_light10", "bone": "eff light10"}, {"attachment": "efect_light", "blend": "additive", "name": "efect_light11", "bone": "eff light11"}, {"attachment": "efect_light", "blend": "additive", "name": "efect_light18", "bone": "eff light18"}, {"attachment": "efect_light", "blend": "additive", "name": "efect_light21", "bone": "eff light21"}, {"attachment": "efect_light", "blend": "additive", "name": "efect_light19", "bone": "eff light19"}, {"attachment": "efect_light", "blend": "additive", "name": "efect_light12", "bone": "eff light12"}, {"attachment": "efect_light", "blend": "additive", "name": "efect_light13", "bone": "eff light13"}, {"attachment": "efect_light", "blend": "additive", "name": "efect_light24", "bone": "eff light24"}, {"attachment": "efect_light", "blend": "additive", "name": "efect_light14", "bone": "eff light14"}, {"attachment": "efect_light", "blend": "additive", "name": "efect_light3", "bone": "eff light3"}, {"attachment": "efect_light", "blend": "additive", "name": "efect_light5", "bone": "eff light5"}, {"attachment": "efect_light", "blend": "additive", "name": "efect_light4", "bone": "eff light4"}, {"attachment": "efect_light", "blend": "additive", "name": "efect_light16", "bone": "eff light16"}, {"attachment": "efect_light", "blend": "additive", "name": "efect_light23", "bone": "eff light23"}, {"attachment": "trang tri de", "name": "trang tri de", "bone": "đe"}, {"attachment": "trang tri de 2", "name": "trang tri de 2", "bone": "đe"}, {"attachment": "trang tri de 2", "name": "trang tri de 3", "bone": "đe"}, {"attachment": "eff heal", "name": "eff heal", "bone": "eff heal"}, {"attachment": "eff heal", "name": "eff heal2", "bone": "eff heal2"}, {"attachment": "eff heal", "name": "eff heal3", "bone": "eff heal3"}, {"attachment": "eff heal", "name": "eff heal4", "bone": "eff heal4"}, {"attachment": "do<PERSON><PERSON>", "name": "do<PERSON><PERSON>", "bone": "do<PERSON><PERSON>"}, {"attachment": "do<PERSON><PERSON>", "name": "domdom2", "bone": "domdom2"}, {"attachment": "do<PERSON><PERSON>", "name": "domdom3", "bone": "domdom3"}, {"attachment": "do<PERSON><PERSON>", "name": "domdom4", "bone": "domdom4"}, {"attachment": "do<PERSON><PERSON>", "name": "domdom5", "bone": "domdom5"}, {"attachment": "do<PERSON><PERSON>", "name": "domdom6", "bone": "domdom6"}, {"attachment": "do<PERSON><PERSON>", "name": "domdom7", "bone": "domdom7"}, {"attachment": "scatter", "name": "scatter", "bone": "scatter"}, {"attachment": "scatter copy", "name": "scatter copy", "bone": "scatter"}, {"attachment": "r", "name": "r", "bone": "scatter"}, {"attachment": "r", "name": "r2", "bone": "scatter light"}, {"attachment": "e", "name": "e", "bone": "scatter"}, {"attachment": "e", "name": "e2", "bone": "scatter light"}, {"attachment": "t", "name": "t", "bone": "scatter"}, {"attachment": "t", "name": "t3", "bone": "scatter light"}, {"attachment": "t", "name": "t4", "bone": "scatter light"}, {"attachment": "a", "name": "a", "bone": "scatter"}, {"attachment": "a", "name": "a2", "bone": "scatter light"}, {"attachment": "c", "name": "c", "bone": "scatter"}, {"attachment": "c", "name": "c2", "bone": "scatter light"}, {"attachment": "s", "name": "s", "bone": "scatter"}, {"attachment": "s", "name": "s2", "bone": "scatter light"}, {"attachment": "t", "name": "t2", "bone": "scatter"}], "bones": [{"name": "root"}, {"parent": "root", "name": "all"}, {"parent": "all", "name": "scatter", "x": -0.7, "y": -42.57}, {"parent": "all", "rotation": -36.63, "name": "binh", "length": 72.32, "x": -10.83, "y": 20.59}, {"parent": "all", "name": "đe", "x": -1.3, "y": -73.38}, {"scaleX": 1.113, "parent": "binh", "scaleY": 1.113, "rotation": 36.63, "name": "eff light", "x": 48.14, "y": 22.07}, {"parent": "all", "name": "all eff light"}, {"scaleX": 0.702, "parent": "all eff light", "scaleY": 0.702, "name": "eff light2", "x": -71.21, "y": 34.09}, {"scaleX": 1.034, "parent": "all eff light", "scaleY": 1.034, "name": "eff light4", "x": -58.3, "y": 5.34}, {"scaleX": 0.235, "parent": "all eff light", "scaleY": 0.235, "name": "eff light3", "x": -29.01, "y": 94.26}, {"scaleX": 0.521, "parent": "all eff light", "scaleY": 0.521, "name": "eff light5", "x": 79.35, "y": -8.2}, {"scaleX": 0.595, "parent": "all eff light", "scaleY": 0.595, "name": "eff light6", "x": -15.99, "y": 31.23}, {"scaleX": 0.485, "parent": "all eff light", "scaleY": 0.485, "name": "eff light8", "x": 54.85, "y": -36.22}, {"scaleX": 0.272, "parent": "all eff light", "scaleY": 0.272, "name": "eff light9", "x": -0.25, "y": 88.91}, {"scaleX": 0.583, "parent": "all eff light", "scaleY": 0.583, "name": "eff light10", "x": 15.59, "y": 63.29}, {"scaleX": 1.959, "parent": "all eff light", "scaleY": 1.959, "name": "eff light11", "x": 57.99, "y": 30.49}, {"scaleX": 0.505, "parent": "all eff light", "scaleY": 0.505, "name": "eff light12", "x": 7.56, "y": -33.25}, {"scaleX": 0.21, "parent": "all eff light", "scaleY": 0.21, "name": "eff light13", "x": 14.93, "y": -13.35}, {"scaleX": 0.143, "parent": "all eff light", "scaleY": 0.143, "name": "eff light14", "x": -40.41, "y": 15.69}, {"scaleX": 1.034, "parent": "all eff light", "scaleY": 1.034, "name": "eff light16", "x": -51.6, "y": 51.52}, {"scaleX": 0.542, "parent": "all eff light", "scaleY": 0.542, "name": "eff light17", "x": -10.7, "y": -29.44}, {"scaleX": 1.959, "parent": "all eff light", "scaleY": 1.959, "name": "eff light18", "x": -44.24, "y": 73.14}, {"scaleX": 1.959, "parent": "all eff light", "scaleY": 1.959, "name": "eff light19", "x": 7.29, "y": 62.99}, {"scaleX": 1.959, "parent": "all eff light", "scaleY": 1.959, "name": "eff light21", "x": -67.26, "y": 69.74}, {"scaleX": 0.542, "parent": "all eff light", "scaleY": 0.542, "name": "eff light22", "x": -94.68, "y": -27.52}, {"scaleX": 0.678, "parent": "all eff light", "scaleY": 0.678, "name": "eff light23", "x": 37.49, "y": 62.66}, {"scaleX": 0.707, "parent": "all eff light", "scaleY": 0.707, "name": "eff light24", "x": -8.22, "y": 5.93}, {"parent": "all", "name": "all domdom"}, {"parent": "all domdom", "name": "do<PERSON><PERSON>", "x": -51.86, "y": -57.61}, {"parent": "all domdom", "name": "domdom2", "x": -11.37, "y": -0.18}, {"scaleX": 2.093, "parent": "all domdom", "scaleY": 2.093, "name": "domdom3", "x": -81.44, "y": -11.65}, {"scaleX": 1.398, "parent": "all domdom", "scaleY": 1.398, "name": "domdom4", "x": -19.01, "y": 55.88}, {"scaleX": 0.663, "parent": "all domdom", "scaleY": 0.663, "name": "domdom5", "x": 55.27, "y": -3.09}, {"scaleX": 0.663, "parent": "all domdom", "scaleY": 0.663, "name": "domdom6", "x": 55.27, "y": -3.09}, {"parent": "all", "name": "all heal"}, {"parent": "all heal", "color": "f8f8f8ff", "name": "eff heal", "x": 50.95, "y": -1.85}, {"parent": "all heal", "color": "f8f8f8ff", "name": "eff heal2", "x": -67.74, "y": 0.31}, {"parent": "all heal", "color": "f8f8f8ff", "name": "eff heal3", "x": -32.22, "y": -42.61}, {"parent": "all heal", "color": "f8f8f8ff", "name": "eff heal4", "x": 6.26, "y": 63.95}, {"scaleX": 1.202, "parent": "all domdom", "scaleY": 1.202, "name": "domdom7", "x": 85.01, "y": 27.94}, {"parent": "all", "name": "scatter light", "x": -9.33, "y": -40.7}], "animations": {"animation": {"slots": {"r2": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.5}, {"color": "ffffffff", "time": 0.6667}, {"color": "ffffff00", "time": 1.0667}]}, "efect_light19": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 1.1667}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 1.3333}, {"color": "ffffff00", "curve": "stepped", "time": 1.5}, {"color": "ffffff00", "time": 1.6667}]}, "efect_light17": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.9333}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 1.0333}, {"color": "ffffff00", "curve": "stepped", "time": 1.2}, {"color": "ffffff00", "time": 1.6667}]}, "efect_light18": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.5}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 0.6667}, {"color": "ffffff00", "curve": "stepped", "time": 0.8333}, {"color": "ffffff00", "time": 1.6667}]}, "efect_light16": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.3333}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 0.5667}, {"color": "ffffff00", "curve": "stepped", "time": 1}, {"color": "ffffff00", "time": 1.6667}]}, "efect_light": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.8333}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 1.0333}, {"color": "ffffff00", "curve": "stepped", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}]}, "efect_light13": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 1.1333}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 1.1667}, {"color": "ffffff00", "time": 1.3}]}, "efect_light14": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.3}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 0.4}, {"color": "ffffff00", "curve": "stepped", "time": 0.5667}, {"color": "ffffff00", "time": 1.6667}]}, "efect_light11": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.5}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 0.6667}, {"color": "ffffff00", "curve": "stepped", "time": 0.8333}, {"color": "ffffff00", "time": 1.6667}]}, "efect_light12": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.8667}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 0.9333}, {"color": "ffffff00", "curve": "stepped", "time": 1.0667}, {"color": "ffffff00", "time": 1.6667}]}, "efect_light10": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.2333}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 0.3333}, {"color": "ffffff00", "curve": "stepped", "time": 0.4667}, {"color": "ffffff00", "time": 1.6667}]}, "s2": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.5}, {"color": "ffffffff", "time": 0.6667}, {"color": "ffffff00", "time": 1.0667}]}, "binh 2": {"color": [{"color": "ffffff00", "time": 0}, {"color": "fffffff3", "time": 0.3333}, {"color": "ffffff6f", "time": 0.6667}, {"color": "fffffff3", "time": 1}, {"color": "ffffff6f", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}]}, "eff heal4": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.6667}, {"color": "ffffffff", "time": 0.9667}, {"color": "ffffff00", "time": 1.2333}]}, "eff heal3": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.8333}, {"color": "ffffffff", "time": 1.1667}, {"color": "ffffff00", "time": 1.5}]}, "c2": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.5}, {"color": "ffffffff", "time": 0.6667}, {"color": "ffffff00", "time": 1.0667}]}, "domdom": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.3333}, {"color": "ffffff00", "time": 0.6667}]}, "efect_light24": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.2}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 0.2667}, {"color": "ffffff00", "time": 0.4333}]}, "eff heal2": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffffff", "time": 0.6667}, {"color": "ffffff00", "time": 1}]}, "efect_light22": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.1667}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 0.2667}, {"color": "ffffff00", "curve": "stepped", "time": 0.4333}, {"color": "ffffff00", "time": 1.6667}]}, "efect_light23": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.3}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 0.4667}, {"color": "ffffff00", "curve": "stepped", "time": 0.8}, {"color": "ffffff00", "time": 1.6667}]}, "glow deế copy": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.1667}, {"color": "ff25fdff", "curve": [0.25, 0, 0.75, 1], "time": 0.3333}, {"color": "ffffff98", "curve": [0.25, 0, 0.75, 1], "time": 0.5}, {"color": "f674ffff", "curve": [0.25, 0, 0.75, 1], "time": 0.6667}, {"color": "ffffff97", "curve": [0.25, 0, 0.75, 1], "time": 0.8333}, {"color": "f783ffff", "curve": [0.25, 0, 0.75, 1], "time": 1}, {"color": "ffffff97", "curve": [0.25, 0, 0.75, 1], "time": 1.1667}, {"color": "f783ffff", "curve": [0.25, 0, 0.75, 1], "time": 1.3333}, {"color": "ffffff97", "curve": [0.25, 0, 0.75, 1], "time": 1.5}, {"color": "ffffff00", "time": 1.6667}]}, "efect_light21": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.7333}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 0.9}, {"color": "ffffff00", "curve": "stepped", "time": 1.0667}, {"color": "ffffff00", "time": 1.6667}]}, "t2": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffff00", "time": 1.1667}, {"color": "ffffffff", "time": 1.3}, {"color": "ffffff00", "time": 1.4333}]}, "t3": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.5}, {"color": "ffffffff", "time": 0.6667}, {"color": "ffffff00", "time": 1.0667}]}, "t4": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.5}, {"color": "ffffffff", "time": 0.6667}, {"color": "ffffff00", "time": 1.0667}]}, "eff heal": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.3333}, {"color": "ffffff00", "time": 0.6667}]}, "a": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 1}, {"color": "ffffffff", "time": 1.1333}, {"color": "ffffff00", "time": 1.2667}]}, "c": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.9}, {"color": "ffffffff", "time": 1.0333}, {"color": "ffffff00", "time": 1.1667}]}, "e": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 1.4}, {"color": "ffffffff", "time": 1.5}, {"color": "ffffff00", "time": 1.6333}]}, "efect_light3": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.7}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 0.8}, {"color": "ffffff00", "curve": "stepped", "time": 0.9667}, {"color": "ffffff00", "time": 1.6667}]}, "efect_light2": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.0667}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 0.2}, {"color": "ffffff00", "curve": "stepped", "time": 0.4}, {"color": "ffffff00", "time": 1.6667}]}, "efect_light5": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 1.0333}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 1.1667}, {"color": "ffffff00", "curve": "stepped", "time": 1.3667}, {"color": "ffffff00", "time": 1.6667}]}, "efect_light4": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.8333}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 1}, {"color": "ffffff00", "curve": "stepped", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}]}, "efect_light6": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.9333}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 1}, {"color": "ffffff00", "curve": "stepped", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}]}, "efect_light9": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.5667}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 0.6667}, {"color": "ffffff00", "curve": "stepped", "time": 0.8}, {"color": "ffffff00", "time": 1.6667}]}, "e2": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.5}, {"color": "ffffffff", "time": 0.6667}, {"color": "ffffff00", "time": 1.0667}]}, "efect_light8": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 1.0667}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 1.1667}, {"color": "ffffff00", "curve": "stepped", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}]}, "a2": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.5}, {"color": "ffffffff", "time": 0.6667}, {"color": "ffffff00", "time": 1.0667}]}, "r": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffff00", "time": 1.4667}, {"color": "ffffffff", "time": 1.5667}, {"color": "ffffff00", "time": 1.6667}]}, "s": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.7667}, {"color": "ffffffff", "time": 0.9}, {"color": "ffffff00", "time": 1.0333}]}, "t": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffff00", "time": 1.2667}, {"color": "ffffffff", "time": 1.4}, {"color": "ffffff00", "time": 1.5667}]}, "domdom2": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.5}, {"color": "ffffffff", "time": 0.7}, {"color": "ffffff00", "time": 0.9}]}, "domdom3": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.5}, {"color": "ffffffff", "time": 0.7}, {"color": "ffffff00", "time": 0.9}]}, "scatter copy": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.1}, {"color": "fffffff1", "time": 0.2667}, {"color": "ffffff00", "time": 1.1333}]}, "domdom4": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 1.1667}, {"color": "ffffffff", "time": 1.3333}, {"color": "ffffff00", "time": 1.4667}]}, "domdom5": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.8333}, {"color": "ffffffff", "time": 0.9667}, {"color": "ffffff00", "time": 1.0667}]}, "domdom6": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffffff", "time": 0.4667}, {"color": "ffffff00", "time": 0.5333}]}, "domdom7": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.6667}, {"color": "ffffffff", "time": 1}, {"color": "ffffff00", "time": 1.1667}]}}, "bones": {"eff light22": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.1}, {"angle": -57.3, "time": 0.4667}], "scale": [{"x": 1, "y": 1, "time": 0.1}, {"x": 0.728, "y": 0.728, "time": 0.4667}], "translate": [{"x": 0, "y": 0, "time": 0.1}, {"x": 0, "y": 61.49, "time": 0.4667}]}, "eff light21": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.5667}, {"angle": -84.83, "time": 1.2333}], "scale": [{"x": 1, "y": 1, "time": 0.5667}, {"x": 0.728, "y": 0.728, "time": 1.2333}], "translate": [{"x": 0, "y": 0, "time": 0.5667}, {"x": 0, "y": 68.13, "time": 1.2333}]}, "eff light24": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.1333}, {"angle": -57.3, "time": 0.4667}], "scale": [{"x": 1, "y": 1, "time": 0.1333}, {"x": 0.728, "y": 0.728, "time": 0.4667}], "translate": [{"x": 0, "y": 0, "time": 0.1333}, {"x": 0, "y": 57.22, "time": 0.4667}]}, "eff light23": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.3}, {"angle": -36.82, "time": 0.8667}], "scale": [{"x": 1, "y": 1, "time": 0.3}, {"x": 0.423, "y": 0.423, "time": 0.7333}], "translate": [{"x": 0, "y": 16.16, "time": 0}, {"x": 0, "y": 0, "time": 0.3}, {"x": 0, "y": 6.44, "time": 0.7}, {"x": 0, "y": 41.76, "time": 0.8667}]}, "eff light17": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.8667}, {"angle": -32.15, "time": 1.2333}], "scale": [{"x": 1, "y": 1, "time": 0.8667}, {"x": 0.728, "y": 0.728, "time": 1.2333}], "translate": [{"x": 0, "y": 0, "time": 0.8667}, {"x": 0, "y": 61.49, "time": 1.2333}]}, "eff light16": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.3333}, {"angle": -36.82, "time": 1.0667}], "scale": [{"x": 1, "y": 1, "time": 0.3333}, {"x": 0.423, "y": 0.423, "time": 0.9}], "translate": [{"x": 0, "y": 16.16, "time": 0}, {"x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 41.76, "time": 1.0667}]}, "eff light19": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1}, {"angle": -84.83, "time": 1.6667}], "scale": [{"x": 1, "y": 1, "time": 1}, {"x": 0.728, "y": 0.728, "time": 1.6667}]}, "eff light18": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.3333}, {"angle": -84.83, "time": 1}], "scale": [{"x": 1, "y": 1, "time": 0.3333}, {"x": 0.728, "y": 0.728, "time": 1}], "translate": [{"x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 68.13, "time": 1}]}, "scatter": {"scale": [{"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0.924, "y": 0.924, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1.424, "y": 1.424, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "x": 1.322, "y": 1.322, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 1.244, "y": 1.244, "time": 1.3333}, {"x": 1, "y": 1, "time": 1.6667}]}, "eff heal": {"scale": [{"x": 1, "y": 1, "time": 0}, {"x": 1, "y": 2.199, "time": 0.3333}, {"x": 1, "y": 1, "time": 0.6667}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 0, "y": 67.18, "time": 0.6667}]}, "all": {"scale": [{"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1.035, "y": 1.035, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 1.035, "y": 1.035, "time": 1.5}, {"x": 1, "y": 1, "time": 1.6667}]}, "eff heal4": {"scale": [{"x": 1, "y": 1, "time": 0.6667}, {"x": 1, "y": 2.199, "time": 0.9667}, {"x": 1, "y": 1, "time": 1.2333}], "translate": [{"x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 44.31, "time": 1.2333}]}, "eff heal3": {"scale": [{"x": 1, "y": 1, "time": 0.8333}, {"x": 1, "y": 2.199, "time": 1.1667}, {"x": 1, "y": 1, "time": 1.5}], "translate": [{"x": 0, "y": 0, "time": 0.8333}, {"x": 0, "y": 44.31, "time": 1.5}]}, "eff light": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.8333}, {"angle": -36.82, "time": 1.3333}]}, "eff light11": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.3333}, {"angle": -84.83, "time": 1}], "scale": [{"x": 1, "y": 1, "time": 0.3333}, {"x": 0.728, "y": 0.728, "time": 1}], "translate": [{"x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 36.67, "time": 1}]}, "eff light10": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.1667}, {"angle": -57.3, "time": 0.5}], "scale": [{"x": 1, "y": 1, "time": 0.1667}, {"x": 0.728, "y": 0.728, "time": 0.5}], "translate": [{"x": 0, "y": 0, "time": 0.1667}, {"x": 0, "y": 63.37, "time": 0.5}]}, "eff light13": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.0667}, {"angle": -57.3, "time": 1.3333}], "scale": [{"x": 1, "y": 1, "time": 1.0667}, {"x": 0.728, "y": 0.728, "time": 1.3333}], "translate": [{"x": 0, "y": 0, "time": 1.0667}, {"x": 0, "y": 57.22, "time": 1.3333}]}, "eff light12": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.8667}, {"angle": -57.3, "time": 1}], "scale": [{"x": 1, "y": 1, "time": 0.8}, {"x": 0.728, "y": 0.728, "time": 1.1}], "translate": [{"x": 0, "y": 0, "time": 0.8}, {"x": 0, "y": 36.67, "time": 1.1}]}, "binh": {"rotate": [{"curve": [0.566, 0.06, 0.564, 0.81], "angle": 0, "time": 0}, {"curve": [0.566, 0.06, 0.564, 0.81], "angle": -19.47, "time": 0.1667}, {"curve": [0.566, 0.06, 0.564, 0.81], "angle": -25.83, "time": 0.3333}, {"curve": [0.566, 0.06, 0.564, 0.81], "angle": -9.08, "time": 0.5}, {"curve": [0.566, 0.06, 0.564, 0.81], "angle": -25.83, "time": 0.6667}, {"curve": [0.566, 0.06, 0.564, 0.81], "angle": -5.88, "time": 0.8333}, {"curve": [0.566, 0.06, 0.564, 0.81], "angle": -19.47, "time": 1}, {"angle": 0, "time": 1.3333}], "scale": [{"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 1.057, "y": 1.057, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1.04, "y": 1.04, "time": 1.3333}, {"x": 1, "y": 1, "time": 1.5}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.625, 0.5], "x": 4.35, "y": 36.95, "time": 0.3333}, {"curve": [0.375, 0.5, 0.75, 1], "x": -2.04, "y": 29.76, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 4.35, "y": 36.95, "time": 1}, {"curve": [0.375, 0.5, 0.75, 1], "x": -2.04, "y": 29.76, "time": 1.3333}, {"x": 0, "y": 0, "time": 1.6667}]}, "eff light14": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.3}, {"angle": -57.3, "time": 0.5}], "scale": [{"x": 1, "y": 1, "time": 0.2333}, {"x": 0.728, "y": 0.728, "time": 0.6}], "translate": [{"x": 0, "y": 0, "time": 0.2333}, {"x": 0, "y": 38.29, "time": 0.6}]}, "scatter light": {"scale": [{"x": 1, "y": 1, "time": 0}, {"x": 1.481, "y": 1.481, "time": 0.6667}, {"x": 1.713, "y": 1.713, "time": 1.2}]}, "domdom": {"translate": [{"x": 0, "y": 0, "time": 0}, {"x": 0, "y": 94.83, "time": 0.8333}]}, "đe": {"scale": [{"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.1667}, {"curve": "stepped", "x": 1.039, "y": 1.039, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1.039, "y": 1.039, "time": 1.3333}, {"x": 1, "y": 1, "time": 1.5}]}, "eff light2": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.0667}, {"angle": -57.3, "time": 0.3333}], "scale": [{"x": 1, "y": 1, "time": 0}, {"x": 0.728, "y": 0.728, "time": 0.4333}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 0, "y": 36.67, "time": 0.4333}]}, "eff light3": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.7}, {"angle": -57.3, "time": 0.9}], "scale": [{"x": 1, "y": 1, "time": 0.6333}, {"x": 0.728, "y": 0.728, "time": 1}], "translate": [{"x": 0, "y": 0, "time": 0.6333}, {"x": 0, "y": 36.67, "time": 1}]}, "eff heal2": {"scale": [{"x": 1, "y": 1, "time": 0.3333}, {"x": 1, "y": 2.199, "time": 0.6667}, {"x": 1, "y": 1, "time": 1}], "translate": [{"x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 44.31, "time": 1}]}, "eff light4": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.8333}, {"angle": -36.82, "time": 1.4}], "scale": [{"x": 1, "y": 1, "time": 0.8333}, {"x": 0.423, "y": 0.423, "time": 1.2667}], "translate": [{"x": 0, "y": 16.16, "time": 0.5333}, {"x": 0, "y": 0, "time": 0.8333}, {"x": 0, "y": 41.76, "time": 1.4}]}, "eff light5": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.0333}, {"angle": -57.3, "time": 1.3}], "scale": [{"x": 1, "y": 1, "time": 0.9667}, {"x": 0.728, "y": 0.728, "time": 1.4}], "translate": [{"x": 0, "y": 0, "time": 0.9667}, {"x": 0, "y": 36.67, "time": 1.4}]}, "domdom2": {"translate": [{"x": 0, "y": 0, "time": 0.5}, {"x": 0, "y": 94.83, "time": 1}]}, "eff light6": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.9333}, {"angle": -57.3, "time": 1.1}], "scale": [{"x": 1, "y": 1, "time": 0.8667}, {"x": 0.728, "y": 0.728, "time": 1.2}], "translate": [{"x": 0, "y": 0, "time": 0.8667}, {"x": -0.43, "y": 81.43, "time": 1.2333}]}, "domdom3": {"translate": [{"x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 94.83, "time": 1}]}, "domdom4": {"translate": [{"x": 0, "y": 0, "time": 1.1667}, {"x": 0, "y": 94.83, "time": 1.5667}]}, "eff light8": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1}, {"angle": -87.86, "time": 1.3667}], "scale": [{"x": 1, "y": 1, "time": 1}, {"x": 0.607, "y": 0.607, "time": 1.3667}], "translate": [{"x": 0, "y": 0, "time": 1}, {"x": 0, "y": 70.83, "time": 1.3667}]}, "domdom5": {"translate": [{"x": 0, "y": 0, "time": 0.7333}, {"x": 0, "y": 94.83, "time": 1.3}]}, "eff light9": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.5667}, {"angle": -57.3, "time": 0.7333}], "scale": [{"x": 1, "y": 1, "time": 0.5}, {"x": 0.728, "y": 0.728, "time": 0.8333}], "translate": [{"x": 0, "y": 0, "time": 0.5}, {"x": 0, "y": 36.67, "time": 0.8333}]}, "domdom6": {"translate": [{"x": 0, "y": 6.14, "time": 0}, {"x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 94.83, "time": 0.6}]}, "domdom7": {"translate": [{"x": 0, "y": 6.14, "time": 0}, {"x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 94.83, "time": 1.1667}]}}, "deform": {"default": {"nuoc": {"nuoc": [{"curve": [0.25, 0, 0.75, 1], "time": 0}, {"vertices": [11.19064, -1.24381, 9.55341, 0.52412, 5.64314, -0.66853, 1.35559, 1.17431, -4.46046, -0.87538, -9.11863, -1.31767, -15.59303, 1.27075, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.91241, -3.91172, -13.29936, 1.47378, -7.43111, 4.62284, -3.64754, 2.30178, 0, 0, 2.80441, -0.84639, 4.05301, -2.46371, 0, 0, -7.77267, 2.01562, -0.95862, 1.87746], "curve": [0.25, 0, 0.75, 1], "time": 0.1667}, {"vertices": [14.34756, 1.04104, 13.651, 2.10012, 5.67266, 1.06316, 0.15373, 1.80099, -7.69196, -2.46611, -9.11863, -1.31767, -15.59303, 1.27075, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.06195, -2.37184, 12.87561, -2.83613, -13.02332, 2.42177, -8.7666, 2.48025, -6.69257, 1.48738, -0.76893, 0.61934, 5.49895, -0.28589, 7.41962, -3.12716, 0, 0, -7.77267, 2.01562, -0.95862, 1.87746], "curve": [0.25, 0, 0.75, 1], "time": 0.3333}, {"vertices": [26.60532, -0.82931, 22.64902, 1.99121, 11.47598, -2.67404, 2.2246, -5.02523, -8.935, -5.51666, -13.17293, -0.56071, -16.37026, 1.51159, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.99113, -1.59888, 11.3212, -2.7714, 18.76226, -5.57172, 24.38877, -6.95471, -15.91987, 3.40749, -14.42624, 7.43358, -9.50906, 6.0918, -0.92584, 3.25174, 10.28725, -2.42594, 17.28556, -3.94438, -10.50192, 7.06267, -9.0624, 12.73633, 8.73079, 11.12528, 11.6272, 0.10452], "curve": [0.243, 0, 0.68, 0.71], "time": 0.6}, {"vertices": [-14.72876, 4.29969, -4.56478, -1.11649, -9.45577, -11.21788, -12.82991, -17.49043, -15.85934, -18.90455, -13.70942, -14.93499, -9.20372, -7.88006, -0.59911, -3.72377, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.47286, -0.36055, 0.10003, -0.5368, -21.15419, 8.69404, -15.09295, -4.88204, -20.7981, -5.97822, -23.66394, -6.61401, -23.88591, -6.70008, -19.76204, -2.801, -19.9062, 0.8854, -8.07073, 1.27022, -12.48175, 4.7241, -8.00018, 1.40208, -6.50199, 0.66304], "curve": [0.375, 0.62, 0.716, 1], "time": 0.8}, {"vertices": [28.55069, -9.25159, 29.30778, 1.83089, 16.19672, 2.06489, 5.28214, 4.39157, -7.69196, -2.46611, -9.11863, -1.31767, -15.59303, 1.27075, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.39463, -1.40474, 14.24137, -3.25683, 21.55756, -6.4855, 25.7725, -8.11183, -19.56586, 2.51217, -27.56769, 8.19678, -19.50814, 4.66, -7.6301, 2.87245, 6.27392, 2.56089, 18.11133, -7.98433, -17.80645, 9.08187, -16.80732, 14.71768, 5.40775, 4.50439, 8.20697, -0.75024], "curve": [0.25, 0, 0.75, 1], "time": 0.9667}, {"vertices": [1.59275, -4.91637, 6.33043, -9.98172, -0.30724, -15.67156, -5.40457, -18.96495, -13.05655, -15.71369, -15.82065, -9.26003, -14.96183, -0.18273, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.47286, -0.36055, 0.10003, -0.5368, -1.05102, 1.02843, -10.67412, -2.97695, -16.19001, -4.23193, -17.02997, -3.10334, -17.39002, -3.99274, -15.84822, -5.08418, -14.04769, -3.75555, -10.04254, 4.4227, -12.22367, 7.83487, -8.00018, 1.40208, -6.50199, 0.66304], "curve": [0.375, 0.62, 0.716, 1], "time": 1.1333}, {"vertices": [10.50658, -6.03129, 15.95351, -5.46236, 10.8805, -9.593, -0.24391, -11.19277, -5.12756, -5.1695, -9.14392, -1.25864, -6.81192, 1.27971, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.56494, -2.01798, 8.72097, -3.78217, -11.4877, 0.88556, -10.39043, 2.30947, -6.79597, 2.80503, 0.71909, 0.05359, 5.96782, -2.54844, 4.00317, -5.11092, -10.33852, 1.51552, -11.83546, 5.40311, -0.57216, 0.56432, 3.77977, -0.61723], "curve": [0.319, 0.29, 0.708, 0.8], "time": 1.3}, {"vertices": [8.50101, -4.51722, 9.08977, -7.4404, 8.87107, -9.18741, 2.85035, -10.76509, -6.19169, -9.05784, -11.00159, -1.51434, -8.19583, 1.53969, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.10526, 0.67828, 9.61584, -1.75508, -7.99559, 3.52323, -8.40756, 1.31546, -6.59054, -2.96059, -2.63001, -4.33216, -2.47835, -7.0767, 0.53264, -4.88782, -9.89783, 0.98809, -11.2219, 7.30445, -0.08904, 6.21364, 1.96943, 4.61931], "curve": [0.349, 0.43, 0.683, 0.77], "time": 1.4333}, {"vertices": [2.50755, -1.23824, 4.02841, -1.84275, -0.01698, -7.14319, -1.9952, -4.80569, -7.29107, -6.18685, -5.14985, -1.49695, -2.41717, 0.4541, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.6209, 0.20004, 2.83597, -0.51762, -1.52064, 0.67646, -1.3153, 0.40665, -2.44536, -3.86886, 0.14563, -1.94656, 0.76319, -1.45498, -2.78416, -1.91993, -3.0526, 1.7286, -5.98509, 4.06449, -1.83665, 3.58486, 0.58084, 1.36236], "curve": [0.354, 0.65, 0.689, 1], "time": 1.5333}, {"time": 1.6667}]}, "glow deế copy": {"glow deế copy": [{"curve": "stepped", "time": 0}, {"curve": [0.25, 0, 0.75, 1], "time": 0.3333}, {"vertices": [11.33811, 3.60304, -17.17167, 2.37291, -83.43679, 85.69115, 60.04288, 81.30247], "curve": [0.25, 0, 0.75, 1], "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "time": 1}, {"vertices": [14.17775, 1.70996, -19.53808, 0.9531, -83.43679, 85.69115, 60.04288, 81.30247], "curve": [0.25, 0, 0.75, 1], "time": 1.3333}, {"time": 1.6667}]}}}}}}