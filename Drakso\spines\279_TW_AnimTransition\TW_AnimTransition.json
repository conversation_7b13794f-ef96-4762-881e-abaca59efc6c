{"skins": {"default": {"sword_left": {"sword_left": {"rotation": 90.83, "x": 53.02, "width": 40, "y": 1.58, "height": 191}}, "ChanPhai": {"ChanPhai": {"triangles": [5, 2, 4, 2, 3, 4, 5, 6, 13, 6, 8, 13, 5, 13, 2, 6, 7, 8, 8, 9, 13, 13, 1, 2, 13, 14, 1, 13, 9, 14, 1, 14, 10, 14, 9, 10, 1, 10, 0, 12, 0, 11, 0, 10, 11], "uvs": [0.95103, 0.44504, 0.62874, 0.72813, 0.65499, 0.75486, 0.99999, 0.8743, 1, 1, 0.50157, 1, 0, 0.93598, 0, 0.84366, 0.06567, 0.76903, 0.08439, 0.73364, 1e-05, 0.41835, 0, 0, 1, 0, 0.38375, 0.80139, 0.39057, 0.76395], "vertices": [1, 36, 26.48, 21.84, 1, 2, 36, 55.78, 10.82, 0.76726, 37, 7.03, 13.91, 0.23274, 2, 36, 58.24, 12.31, 0.51697, 37, 9.07, 11.89, 0.48303, 2, 36, 67.97, 29.15, 0.00029, 37, 27.76, 6.51, 0.99971, 1, 37, 32.16, -4.99, 1, 1, 37, 11.22, -13.02, 1, 2, 36, 79.45, -14.78, 0.36116, 37, -12.1, -15.23, 0.63884, 2, 36, 70.47, -15.88, 0.61495, 37, -15.34, -6.79, 0.38505, 2, 36, 62.85, -13.84, 0.90581, 37, -15.2, 1.1, 0.09419, 2, 36, 59.31, -13.42, 0.97835, 37, -15.65, 4.64, 0.02165, 1, 36, 29.1, -20.96, 1, 1, 36, -11.59, -25.95, 1, 1, 36, -17.08, 18.71, 1, 1, 36, 64.25, 0.76, 1, 2, 36, 60.57, 0.61, 0.98265, 37, -1.72, 6.8, 0.01735], "type": "mesh", "hull": 13}}, "ThanNgua": {"ThanNgua": {"rotation": 18.53, "x": 88.71, "width": 228, "y": -8.47, "height": 145}}, "TuiDo": {"TuiDo": {"triangles": [6, 5, 16, 5, 4, 16, 6, 16, 7, 4, 3, 16, 3, 15, 16, 16, 8, 7, 16, 15, 8, 3, 2, 15, 15, 9, 8, 2, 14, 15, 15, 14, 9, 2, 1, 14, 14, 10, 9, 14, 13, 10, 13, 11, 10, 1, 13, 14, 1, 0, 13, 0, 12, 13, 13, 12, 11], "uvs": [0, 0.30181, 0.39281, 0.35905, 0.50624, 0.46783, 0.58543, 0.63099, 0.62823, 0.84568, 0.7224, 1, 0.99999, 0.96565, 1, 0.62813, 0.94284, 0.46497, 0.85937, 0.27604, 0.79731, 0.13005, 0.67532, 0, 0, 0, 0.46772, 0.19589, 0.60683, 0.33901, 0.7117, 0.51649, 0.75664, 0.67393], "vertices": [1, 27, -43.88, -23.34, 1, 1, 27, -2.58, -14.32, 1, 4, 27, 11.7, -18.71, 0.36, 28, 1.77, -14.73, 0.54529, 29, -11.63, -16.81, 0.07195, 30, -22.73, -19.76, 0.02277, 4, 27, 23.88, -28.4, 0.00013, 28, 17.25, -16.27, 0.19088, 29, 3.83, -15.07, 0.3628, 30, -7.67, -15.85, 0.44618, 2, 29, 21.24, -18.66, 0.00303, 30, 10.07, -16.94, 0.99697, 1, 30, 24.97, -11.29, 1, 2, 29, 47.62, 12.63, 0.22967, 30, 31.76, 17.76, 0.77033, 3, 28, 44.79, 18.51, 0.02138, 29, 23.47, 24.71, 0.86197, 30, 6.14, 26.3, 0.11665, 3, 28, 30.77, 21.89, 0.18142, 29, 9.06, 25.07, 0.8181, 30, -8.18, 24.63, 0.00048, 3, 27, 42.64, 7.84, 0.09, 28, 13.39, 24.36, 0.64527, 29, -8.46, 23.84, 0.26473, 3, 27, 32.64, 16.81, 0.29, 28, 0.12, 26.47, 0.67759, 29, -21.87, 23.13, 0.03241, 1, 27, 16.95, 22.52, 1, 1, 27, -51.56, -0.45, 1, 2, 27, 0.87, 0.6, 0.97401, 28, -17.79, -4.36, 0.02599, 1, 28, 0.45, 0.11, 1, 1, 29, 1.68, 1.11, 1, 3, 28, 31.38, -4.11, 0, 29, 15.1, -0.22, 0.35179, 30, 1.38, 0.44, 0.64821], "type": "mesh", "hull": 13}}, "ChanTrai": {"ChanPhai": {"triangles": [5, 2, 4, 2, 3, 4, 5, 6, 13, 6, 8, 13, 5, 13, 2, 6, 7, 8, 8, 9, 13, 13, 1, 2, 13, 14, 1, 13, 9, 14, 1, 14, 15, 14, 9, 15, 9, 10, 15, 1, 15, 0, 0, 15, 12, 12, 15, 11, 15, 10, 11], "uvs": [0.94562, 0.45766, 0.62874, 0.72813, 0.65499, 0.75486, 0.99999, 0.8743, 1, 1, 0.50157, 1, 0, 0.93598, 0, 0.84366, 0.06567, 0.76903, 0.08439, 0.73364, 1e-05, 0.41835, 0, 0, 1, 0, 0.38375, 0.80139, 0.39057, 0.76395, 0.45998, 0.44957], "vertices": [1, 80, 27.74, 21.75, 1, 2, 80, 55.78, 10.82, 0.76726, 81, 7.03, 13.91, 0.23274, 2, 80, 58.24, 12.31, 0.51697, 81, 9.07, 11.89, 0.48303, 2, 80, 67.97, 29.15, 0.00029, 81, 27.76, 6.51, 0.99971, 1, 81, 32.16, -4.99, 1, 1, 81, 11.22, -13.02, 1, 2, 80, 79.45, -14.78, 0.36116, 81, -12.1, -15.23, 0.63884, 2, 80, 70.47, -15.88, 0.61495, 81, -15.34, -6.79, 0.38505, 2, 80, 62.85, -13.84, 0.90581, 81, -15.2, 1.1, 0.09419, 2, 80, 59.31, -13.42, 0.97835, 81, -15.65, 4.64, 0.02165, 1, 80, 29.1, -20.96, 1, 1, 80, -11.59, -25.95, 1, 1, 80, -17.08, 18.71, 1, 1, 80, 64.25, 0.76, 1, 2, 80, 60.57, 0.61, 0.98265, 81, -1.72, 6.8, 0.01735, 2, 80, 29.61, -0.04, 0.99902, 81, -9.92, 36.66, 0.00098], "type": "mesh", "hull": 13}}, "baokiem6": {"baokiem2": {"x": 2.06, "width": 12, "y": -88.09, "height": 53}}, "sword_right": {"sword_right": {"rotation": 90.83, "x": 58.78, "width": 42, "y": 0.68, "height": 202}}, "ChanTrai2": {"ChanPhai": {"triangles": [5, 2, 4, 2, 3, 4, 5, 6, 13, 6, 8, 13, 5, 13, 2, 6, 7, 8, 8, 9, 13, 13, 1, 2, 13, 14, 1, 13, 9, 14, 1, 14, 15, 14, 9, 15, 9, 10, 15, 1, 15, 0, 0, 15, 12, 12, 15, 11, 15, 10, 11], "uvs": [0.94562, 0.45766, 0.62874, 0.72813, 0.65499, 0.75486, 0.99999, 0.8743, 1, 1, 0.50157, 1, 0, 0.93598, 0, 0.84366, 0.06567, 0.76903, 0.08439, 0.73364, 1e-05, 0.41835, 0, 0, 1, 0, 0.38375, 0.80139, 0.39057, 0.76395, 0.45998, 0.44957], "vertices": [1, 80, 27.74, 21.75, 1, 2, 80, 55.78, 10.82, 0.76726, 81, 7.03, 13.91, 0.23274, 2, 80, 58.24, 12.31, 0.51697, 81, 9.07, 11.89, 0.48303, 2, 80, 67.97, 29.15, 0.00029, 81, 27.76, 6.51, 0.99971, 1, 81, 32.16, -4.99, 1, 1, 81, 11.22, -13.02, 1, 2, 80, 79.45, -14.78, 0.36116, 81, -12.1, -15.23, 0.63884, 2, 80, 70.47, -15.88, 0.61495, 81, -15.34, -6.79, 0.38505, 2, 80, 62.85, -13.84, 0.90581, 81, -15.2, 1.1, 0.09419, 2, 80, 59.31, -13.42, 0.97835, 81, -15.65, 4.64, 0.02165, 1, 80, 29.1, -20.96, 1, 1, 80, -11.59, -25.95, 1, 1, 80, -17.08, 18.71, 1, 1, 80, 64.25, 0.76, 1, 2, 80, 60.57, 0.61, 0.98265, 81, -1.72, 6.8, 0.01735, 2, 80, 29.61, -0.04, 0.99902, 81, -9.92, 36.66, 0.00098], "type": "mesh", "hull": 13}}, "Chan D1": {"Chan D1": {"triangles": [12, 4, 5, 5, 11, 12, 6, 11, 5, 10, 11, 6, 7, 10, 6, 8, 9, 10, 7, 8, 10, 14, 0, 1, 4, 2, 3, 13, 14, 1, 12, 13, 1, 12, 1, 2, 12, 2, 4], "uvs": [0, 0.0584, 0.327, 0, 0.67867, 0, 1, 0.13878, 0.99999, 0.43243, 0.99999, 0.51506, 0.99946, 0.59731, 1, 0.67952, 0.89419, 1, 0.42697, 0.99999, 0.33861, 0.68495, 0.29615, 0.60645, 0.20977, 0.52789, 0.11105, 0.44751, 2e-05, 0.20853], "vertices": [1, 4, 71.51, 26.71, 1, 1, 4, 85.06, 41.74, 1, 1, 4, 102.9, 47.15, 1, 1, 4, 126.4, 28.31, 1, 1, 4, 141.65, -21.99, 1, 2, 4, 149.46, -46.2, 0.35924, 15, -5.84, 15.26, 0.64076, 2, 4, 154.8, -66.22, 3e-05, 15, 14.84, 15.96, 0.99997, 1, 15, 29.55, 16.37, 1, 1, 15, 87.04, 12.24, 1, 1, 15, 87.68, -12.52, 1, 1, 15, 31.43, -18.65, 1, 1, 15, 17.44, -21.26, 1, 2, 4, 110.8, -63.43, 0.07612, 15, 2.38, -26.25, 0.92388, 2, 4, 97.35, -38.27, 0.99782, 15, -25.15, -33.67, 0.00218, 1, 4, 79.3, 0.99, 1], "type": "mesh", "hull": 15}}, "Chan B2": {"Chan B2": {"triangles": [20, 19, 6, 7, 8, 9, 20, 6, 7, 9, 20, 7, 20, 10, 11, 20, 11, 19, 10, 20, 9, 16, 0, 1, 3, 1, 2, 16, 1, 3, 3, 17, 16, 16, 15, 0, 4, 17, 3, 16, 14, 15, 17, 14, 16, 18, 17, 4, 18, 4, 5, 13, 14, 17, 13, 17, 18, 19, 18, 5, 19, 5, 6, 12, 13, 18, 19, 12, 18, 11, 12, 19], "uvs": [0.09641, 1e-05, 0.54231, 0, 0.80044, 0.11312, 0.63031, 0.24559, 0.59511, 0.57867, 0.62601, 0.63269, 0.66043, 0.70355, 0.71285, 0.74713, 1, 0.88714, 1, 1, 0, 1, 0.05537, 0.79252, 0.07288, 0.7282, 0.07856, 0.66383, 0.06711, 0.59949, 1e-05, 0.08853, 0.37164, 0.11712, 0.29738, 0.58848, 0.31605, 0.6528, 0.32876, 0.71144, 0.37664, 0.76813], "vertices": [1, 10, -4.91, -7.49, 1, 1, 10, -5.43, 5.88, 1, 1, 10, 4.77, 14.03, 1, 1, 10, 17.28, 9.41, 1, 2, 10, 48.28, 9.57, 0.96587, 11, -11.09, 14.75, 0.03413, 2, 10, 53.26, 10.7, 0.83091, 11, -6.23, 13.17, 0.16909, 2, 10, 59.81, 11.99, 0.40737, 11, 0.06, 10.93, 0.59263, 2, 10, 63.79, 13.72, 0.12924, 11, 4.37, 10.37, 0.87076, 1, 11, 19.92, 11.73, 1, 1, 11, 29.14, 6.72, 1, 1, 11, 14.82, -19.64, 1, 2, 10, 68.79, -5.83, 0.2555, 11, -1.34, -8.97, 0.7445, 2, 10, 62.79, -5.54, 0.82513, 11, -6.35, -5.65, 0.17487, 1, 10, 56.8, -5.6, 1, 1, 10, 50.83, -6.18, 1, 1, 10, 3.43, -10.06, 1, 1, 10, 5.65, 1.19, 1, 2, 10, 49.54, 0.68, 0.9989, 11, -14.55, 6.47, 0.0011, 2, 10, 55.5, 1.48, 0.97603, 11, -9.03, 4.1, 0.02397, 2, 10, 60.93, 2.07, 0.77373, 11, -4.05, 1.84, 0.22627, 2, 10, 66.14, 3.72, 0.00649, 11, 1.27, 0.58, 0.99351], "type": "mesh", "hull": 16}}, "sword_left_wood_glow": {"sword_left_wood_glow": {"rotation": 90.83, "x": 53.02, "width": 48, "y": 1.58, "height": 200}}, "dust": {"dust": {"triangles": [11, 2, 3, 16, 2, 11, 9, 10, 11, 3, 9, 11, 15, 9, 3, 10, 4, 16, 10, 16, 11, 6, 4, 10, 6, 10, 9, 8, 9, 15, 6, 9, 8, 14, 15, 3, 12, 8, 15, 12, 15, 14, 5, 4, 6, 7, 6, 8, 5, 6, 7, 13, 12, 14, 1, 2, 16, 1, 16, 4, 1, 4, 5, 14, 3, 0, 13, 14, 0, 7, 8, 12, 13, 7, 12, 7, 13, 0, 1, 5, 7, 0, 1, 7], "uvs": [0.95286, 0.96535, 0.06, 0.96535, 0.06, 0.05941, 0.95286, 0.05941, 0.21378, 0.60518, 0.27202, 0.73853, 0.36354, 0.64483, 0.46546, 0.77096, 0.5029, 0.65564, 0.46338, 0.48625, 0.37186, 0.42859, 0.30738, 0.31326, 0.70674, 0.67726, 0.77954, 0.77096, 0.82738, 0.65924, 0.75666, 0.55473, 0.14722, 0.3493], "vertices": [79.75, -46.5, -76.5, -46.5, -76.5, 45, 79.75, 45, -49.59, -10.12, -39.4, -23.59, -23.38, -14.13, -5.54, -26.87, 1.01, -15.22, -5.91, 1.89, -21.92, 7.71, -33.21, 19.36, 36.68, -17.4, 49.42, -26.87, 57.79, -15.58, 45.42, -5.03, -61.24, 15.72], "type": "mesh", "hull": 4}}, "sword_right_glow": {"sword_right_glow": {"rotation": 90.83, "x": 58.78, "width": 50, "y": 0.68, "height": 210}}, "Chan D2": {"Chan D2": {"triangles": [10, 11, 6, 10, 6, 7, 9, 10, 7, 9, 7, 8, 4, 13, 3, 14, 13, 4, 14, 4, 5, 12, 0, 13, 12, 13, 14, 11, 12, 14, 6, 11, 14, 6, 14, 5, 13, 1, 2, 0, 1, 13, 13, 2, 3], "uvs": [0.04528, 0.08467, 0.23857, 0.00914, 0.66657, 0, 0.91509, 0.10145, 0.64661, 0.30902, 0.65257, 0.64967, 0.6882, 0.69568, 0.72249, 0.74957, 0.99999, 0.98704, 0.14731, 0.99999, 0.08257, 0.77391, 0.08973, 0.71567, 0.09561, 0.65944, 0.4264, 0.10915, 0.47638, 0.65498], "vertices": [2, 15, 68.95, -12.49, 0.53529, 16, 8.82, -12.49, 0.46471, 2, 15, 65.45, -7.01, 0.9755, 16, 5.36, -7.29, 0.0245, 2, 15, 64.24, 6.28, 0.953, 16, 4.18, 6.02, 0.047, 2, 15, 68.64, 14.58, 0.45026, 16, 8.58, 14.62, 0.54974, 3, 15, 99.83, 6, 0.27742, 16, 23.46, 8.09, 0.69239, 17, -27.98, 34.31, 0.03019, 2, 16, 59.34, 11.37, 0.92135, 17, -1.86, 11.22, 0.07865, 2, 16, 63.07, 13.36, 0.58904, 17, 2.62, 11.95, 0.41096, 2, 16, 67.99, 14.2, 0.20073, 17, 7.29, 11.18, 0.79927, 1, 17, 28.65, 4.75, 1, 1, 17, 13.06, -16.64, 1, 2, 16, 65.47, -7.25, 0.04601, 17, -3.14, -5.74, 0.95399, 2, 16, 64.69, -5.7, 0.65225, 17, -4.05, -5.55, 0.34775, 2, 16, 61.85, -6.03, 0.98474, 17, -7.37, -5.14, 0.01526, 2, 15, 68.55, -0.48, 0.30423, 16, 8.45, -0.34, 0.69577, 2, 16, 60.38, 5.7, 0.9527, 17, -1.88, 8.08, 0.0473], "type": "mesh", "hull": 13}}, "DauNgua": {"DauNgua": {"triangles": [19, 11, 10, 12, 11, 19, 19, 10, 9, 19, 13, 12, 6, 18, 19, 18, 13, 19, 14, 13, 18, 15, 14, 18, 9, 6, 19, 9, 7, 6, 8, 7, 9, 16, 18, 4, 5, 18, 6, 16, 15, 18, 5, 4, 18, 16, 17, 0, 16, 4, 17, 1, 0, 17, 2, 17, 4, 2, 4, 3, 1, 17, 2], "uvs": [0, 0.61889, 0.13478, 0.85581, 0.46123, 1, 0.69234, 0.9872, 0.68729, 0.85702, 0.70101, 0.7675, 0.72989, 0.7072, 0.90323, 0.71366, 1, 0.63612, 1, 0.52843, 0.86278, 0.00935, 0.51901, 0, 0.42367, 0.08474, 0.32961, 0.18166, 0.25323, 0.24843, 0.15212, 0.3152, 0, 0.37981, 0.31101, 0.81058, 0.47278, 0.55427, 0.63167, 0.27858], "vertices": [1, 4, 91.15, 59.76, 1, 1, 4, 119.98, 27.19, 1, 1, 4, 166.72, 17.16, 1, 1, 4, 193.64, 28.47, 1, 3, 4, 186.05, 49.13, 0.29, 25, 25.78, -46.9, 0.69027, 26, -34.16, -46.45, 0.01973, 2, 25, 40.17, -41.91, 0.85609, 26, -19.75, -41.53, 0.14391, 2, 25, 50.93, -40.78, 0.60323, 26, -8.98, -40.45, 0.39677, 2, 25, 59.4, -60.94, 0.30993, 26, -0.61, -60.65, 0.69007, 2, 25, 76.49, -66.26, 0.2276, 26, 16.46, -66.05, 0.7724, 2, 25, 92.9, -58.39, 0.14215, 26, 32.9, -58.25, 0.85785, 1, 26, 104.76, -5.04, 1, 1, 26, 87.62, 34.77, 1, 2, 25, 129.07, 39.53, 0.00059, 26, 69.54, 39.49, 0.99941, 2, 25, 109.18, 43.12, 0.02828, 26, 49.66, 43.18, 0.97172, 2, 25, 94.84, 46.92, 0.12456, 26, 35.34, 47.04, 0.87544, 2, 25, 79.15, 53.52, 0.32976, 26, 19.68, 53.72, 0.67024, 3, 4, 78.31, 98.06, 0.65, 25, 61.01, 66.07, 0.2135, 26, 1.61, 66.35, 0.1365, 2, 4, 138.6, 41.5, 0.62, 25, 12.34, -0.76, 0.38, 2, 25, 60.21, -0.39, 0.65, 26, 0.49, -0.1, 0.35, 1, 26, 51.17, 1.77, 1], "type": "mesh", "hull": 17}}, "DuiTrai2": {"DuiPhai": {"rotation": 83.13, "x": 20.72, "width": 50, "y": 1.21, "height": 88}}, "Chan B1": {"Chan B1": {"triangles": [2, 22, 21, 22, 2, 1, 22, 1, 0, 21, 3, 2, 22, 0, 17, 14, 22, 16, 14, 16, 15, 16, 22, 17, 8, 18, 9, 18, 10, 9, 8, 7, 18, 10, 18, 19, 18, 7, 19, 10, 19, 11, 7, 6, 19, 19, 20, 11, 11, 20, 12, 19, 6, 20, 6, 5, 20, 20, 21, 12, 12, 21, 13, 20, 5, 21, 5, 4, 21, 14, 13, 22, 13, 21, 22, 21, 4, 3], "uvs": [0.3481, 0, 0.05007, 0.08039, 0, 0.20011, 0.02048, 0.445, 0.0373, 0.4994, 0.07155, 0.56757, 0.11893, 0.64058, 0.19057, 0.86353, 0.19483, 0.99821, 0.55247, 1, 0.52266, 0.86852, 0.63336, 0.6823, 0.71534, 0.62371, 0.7984, 0.5653, 0.86664, 0.49877, 1, 0.36875, 0.94962, 0.11458, 0.62484, 0, 0.35494, 0.92971, 0.3608, 0.66542, 0.37867, 0.59574, 0.40378, 0.5281, 0.49549, 0.16726], "vertices": [2, 47, -54.06, 41.39, 0.3227, 48, -2.18, 51.27, 0.6773, 2, 47, -68.35, 22.22, 0.17531, 48, -16.47, 32.1, 0.82469, 2, 47, -65.01, 1.99, 0.05844, 48, -13.13, 11.88, 0.94156, 2, 9, -5.98, -33.58, 0.2, 48, 4.28, -30.92, 0.8, 2, 9, 13.51, -26.96, 0.65701, 48, 12.92, -49.45, 0.34299, 2, 9, 29.34, -22.75, 0.84628, 48, 19.31, -64.58, 0.15372, 2, 9, 44.21, -18.5, 0.95895, 48, 25.64, -78.8, 0.04105, 1, 9, 82.59, -11.64, 1, 1, 9, 105.3, -10.1, 1, 1, 9, 104.29, 13.49, 1, 1, 9, 82.21, 10.29, 1, 1, 9, 50.38, 15.84, 1, 2, 9, 39.65, 20.66, 0.98177, 47, 12.29, -78.59, 0.01823, 2, 9, 25.69, 25.07, 0.85726, 47, 14.78, -63.89, 0.14274, 2, 9, 20.82, 28.95, 0.5671, 47, 6.7, -25.54, 0.4329, 1, 47, 7.3, -4.81, 1, 2, 47, -10.26, 35.74, 0.6475, 48, 41.62, 45.62, 0.3525, 2, 47, -36.76, 47.23, 0.42938, 48, 15.12, 57.12, 0.57062, 1, 9, 93.15, -0.19, 1, 2, 9, 48.37, -2.29, 0.99376, 48, 42.59, -80.56, 0.00624, 2, 9, 36.1, -1.81, 0.97744, 48, 41.15, -68.3, 0.02256, 3, 9, 22.92, -1.08, 0.91575, 47, -11.88, -64.93, 0.00688, 48, 40, -55.05, 0.07737, 2, 47, -35.66, 17.77, 0.69, 48, 16.22, 27.65, 0.31], "type": "mesh", "hull": 18}}, "DuiTrai": {"DuiPhai": {"rotation": 83.13, "x": 20.72, "width": 50, "y": 1.21, "height": 88}}, "sword_left_wood": {"sword_left_wood": {"rotation": 90.83, "x": 53.01, "width": 40, "y": 1.58, "height": 191}}, "Character": {"Character": {"triangles": [7, 0, 2, 7, 10, 0, 7, 8, 10, 2, 0, 1, 8, 9, 10, 10, 11, 0, 0, 11, 12, 5, 6, 13, 13, 3, 4, 13, 2, 3, 2, 13, 7, 13, 6, 7, 4, 5, 13], "uvs": [0.74188, 0.22136, 1, 0.304, 0.86716, 0.56487, 1, 0.74959, 1, 1, 0.1417, 0.99999, 0.17375, 0.77065, 0.15849, 0.57165, 0.01395, 0.33583, 0.0131, 0.31537, 0.25911, 0.22211, 0.32213, 0, 0.7681, 0, 0.41266, 0.78037], "vertices": [2, 33, 101.16, -38.2, 0.05132, 34, 61.73, -33.12, 0.94868, 2, 33, 84.64, -64.41, 0.18389, 34, 47.62, -60.7, 0.81611, 3, 32, 27.26, 65.46, 0.05768, 33, 36.45, -48.65, 0.67635, 34, -1.78, -49.31, 0.26597, 3, 32, 51.34, 37.1, 0.39748, 33, 1.38, -61.08, 0.59869, 34, -35.6, -64.82, 0.00382, 2, 32, 66.23, -7.3, 0.76754, 33, -45.41, -59.2, 0.23246, 1, 32, -18.41, -35.67, 1, 2, 32, -28.88, 6.05, 0.27571, 33, 0.88, 24.94, 0.72429, 2, 33, 38.13, 25.04, 0.47637, 34, -6.7, 24.24, 0.52363, 1, 34, 36.61, 41.43, 1, 1, 34, 40.42, 41.71, 1, 2, 33, 103.02, 11.97, 0.0175, 34, 59.11, 17.02, 0.9825, 1, 34, 100.92, 12.53, 1, 1, 34, 103.21, -33.8, 1, 2, 32, -4.74, 12.22, 0.05836, 33, -1.93, 0.19, 0.94164], "type": "mesh", "hull": 13}}, "DuiPhai": {"DuiPhai": {"rotation": 83.13, "x": 20.72, "width": 50, "y": 1.21, "height": 88}}, "sword_right_wood": {"sword_right_wood": {"rotation": 90.83, "x": 58.77, "width": 42, "y": 0.68, "height": 202}}, "CanhTayPhai": {"CanhTayPhai": {"triangles": [12, 8, 9, 0, 12, 9, 7, 8, 12, 12, 0, 1, 10, 7, 12, 10, 12, 1, 6, 7, 10, 11, 10, 1, 2, 11, 1, 5, 6, 10, 5, 10, 11, 11, 2, 3, 4, 5, 11, 3, 4, 11], "uvs": [1, 0.33103, 1, 0.58644, 1, 0.77806, 1, 1, 0, 1, 0, 0.8138, 0, 0.60867, 0, 0.33996, 0, 0, 1, 0, 0.49143, 0.608, 0.51117, 0.80147, 0.49741, 0.33715], "vertices": [18.88, 13.67, 39.05, 13.25, 54.19, 12.94, 71.72, 12.58, 71.16, -14.41, 56.46, -14.11, 40.25, -13.78, 19.03, -13.34, -7.82, -12.79, -7.26, 14.21, 40.47, -0.51, 55.77, -0.29, 19.08, 0.09], "type": "mesh", "hull": 10}}, "CanhTayTrai": {"CanhTayTrai": {"rotation": 87.78, "x": 29.2, "width": 23, "y": 0.33, "height": 72}}, "Duoi": {"Duoi": {"triangles": [36, 35, 9, 36, 9, 10, 36, 10, 11, 12, 36, 11, 9, 35, 34, 13, 14, 35, 13, 35, 36, 12, 13, 36, 34, 15, 16, 14, 15, 34, 14, 34, 35, 8, 9, 34, 32, 17, 18, 33, 16, 17, 32, 33, 17, 33, 34, 16, 8, 33, 7, 34, 33, 8, 7, 32, 6, 33, 32, 7, 31, 32, 18, 6, 31, 5, 32, 31, 6, 18, 19, 30, 31, 30, 5, 18, 30, 31, 3, 29, 2, 4, 29, 3, 30, 29, 4, 19, 20, 29, 19, 29, 30, 5, 30, 4, 28, 21, 22, 29, 28, 2, 20, 21, 28, 20, 28, 29, 28, 27, 2, 26, 24, 25, 23, 24, 26, 27, 23, 26, 22, 23, 27, 27, 26, 0, 27, 28, 22, 26, 25, 0, 27, 0, 1, 27, 1, 2], "uvs": [0.89727, 0.05021, 1, 0.13473, 1, 0.2381, 1, 0.32263, 0.96065, 0.4199, 0.92917, 0.49486, 0.88982, 0.59692, 0.82686, 0.70217, 0.74816, 0.79945, 0.72848, 0.86643, 0.78751, 0.93979, 0.82292, 0.99999, 0.32267, 0.99999, 0.20069, 0.93341, 0.13379, 0.86165, 0.07477, 0.77553, 0.05509, 0.68623, 0.03542, 0.58416, 0.02755, 0.50602, 0.01181, 0.42947, 0, 0.33379, 0, 0.24129, 0, 0.13952, 0.126, 0.05819, 0.24011, 0, 0.8107, 0, 0.47211, 0.06587, 0.40915, 0.1488, 0.39735, 0.24129, 0.41309, 0.33379, 0.4485, 0.42628, 0.47605, 0.5124, 0.46031, 0.59373, 0.41309, 0.69101, 0.37374, 0.77553, 0.38948, 0.85846, 0.44457, 0.93341], "vertices": [4, 8, -20.05, 11.61, 0.86259, 58, -21.78, 35.48, 0.11009, 59, -34.82, 38.17, 0.01114, 60, -48.14, 44.15, 0.01618, 6, 8, -19.28, 29.49, 0.61263, 58, -6.2, 44.3, 0.25579, 59, -18.63, 45.81, 0.06131, 60, -31.16, 49.83, 0.06889, 61, -50.29, 48.73, 0.00137, 62, -74.55, 35.12, 1e-05, 6, 8, -9.85, 46.72, 0.32128, 58, 13.41, 45.43, 0.29187, 59, 1.01, 45.48, 0.15197, 60, -11.7, 47.19, 0.20177, 61, -30.77, 46.52, 0.02571, 62, -54.97, 36.68, 0.0074, 7, 8, -2.15, 60.81, 0.15353, 58, 29.44, 46.36, 0.19453, 59, 17.06, 45.22, 0.16364, 60, 4.22, 45.03, 0.34984, 61, -14.81, 44.72, 0.09596, 62, -38.96, 37.96, 0.04243, 63, -55.94, 35.29, 7e-05, 7, 8, 9.38, 75.57, 0.0503, 58, 48.07, 44.4, 0.07197, 59, 35.49, 41.88, 0.07235, 60, 22.12, 39.55, 0.3639, 61, 3.21, 39.63, 0.2448, 62, -20.3, 36.42, 0.17584, 63, -37.22, 34.65, 0.02085, 7, 8, 18.34, 86.9, 0.01431, 58, 62.43, 42.8, 0.02058, 59, 49.69, 39.22, 0.01566, 60, 35.91, 35.23, 0.19839, 61, 17.09, 35.62, 0.27046, 62, -5.91, 35.14, 0.36912, 63, -22.78, 34.07, 0.11148, 9, 8, 30.31, 102.46, 0.0008, 58, 81.96, 40.89, 0.00107, 59, 69.03, 35.87, 3e-05, 60, 54.72, 29.62, 0.03243, 61, 36.02, 30.43, 0.08628, 62, 13.67, 33.66, 0.44319, 63, -3.16, 33.55, 0.42838, 64, -21.41, 34.01, 0.00365, 67, -36.88, 59.95, 0.00417, 6, 60, 73.88, 22.13, 0.00039, 61, 55.34, 23.36, 0.00281, 62, 33.99, 30.43, 0.15193, 63, 17.29, 31.3, 0.67359, 64, -0.96, 31.68, 0.1221, 67, -21.46, 46.32, 0.04918, 6, 62, 52.89, 25.86, 0.01618, 63, 36.4, 27.66, 0.37479, 64, 18.13, 27.96, 0.36038, 65, 4.26, 27.72, 0.00186, 66, -3.87, 29.2, 0.00023, 67, -7.94, 32.34, 0.24657, 6, 62, 65.7, 25.36, 0.00091, 63, 49.21, 27.78, 0.13639, 64, 30.95, 28.04, 0.23607, 65, 17.01, 26.47, 0.00633, 66, 8.33, 25.28, 0.00093, 67, 2.6, 25.06, 0.61938, 3, 63, 62.45, 34.07, 0.01862, 64, 44.21, 34.28, 0.06687, 67, 17.05, 22.57, 0.91451, 2, 64, 55.22, 38.4, 0.0338, 67, 28.44, 19.64, 0.9662, 2, 66, 27.25, -10.25, 0.73255, 67, 10.46, -14.42, 0.26745, 2, 65, 30.6, -13.89, 0.11324, 66, 13.04, -17.05, 0.88676, 3, 64, 35.72, -17.51, 0.01802, 65, 17.08, -19.33, 0.61026, 66, -1.33, -19.49, 0.37172, 5, 61, 62.68, -35.75, 0.00074, 62, 52.5, -26.19, 0.00038, 64, 20.05, -24.05, 0.31605, 65, 0.82, -24.22, 0.66591, 66, -18.26, -20.82, 0.01692, 5, 60, 62.9, -36.35, 0.00066, 61, 45.65, -35.35, 0.02286, 62, 35.71, -29.05, 0.03435, 64, 3.4, -27.66, 0.67794, 65, -16.11, -26.1, 0.26418, 7, 59, 65.51, -29.87, 0.00165, 60, 43.48, -35.24, 0.03854, 61, 26.21, -34.67, 0.16652, 62, 16.5, -32.11, 0.20362, 63, 2.85, -32.01, 0.00227, 64, -15.65, -31.57, 0.5347, 65, -35.47, -28.02, 0.0527, 7, 58, 68.54, -26.39, 0.00022, 59, 50.65, -30.23, 0.03115, 60, 28.68, -33.85, 0.17496, 61, 11.39, -33.61, 0.32991, 62, 1.74, -33.9, 0.2031, 64, -30.31, -34.01, 0.25284, 65, -50.3, -28.95, 0.00782, 7, 58, 54.09, -28.44, 0.01508, 59, 36.09, -31.2, 0.15376, 60, 14.11, -33.09, 0.38122, 61, -3.2, -33.17, 0.28049, 62, -12.66, -36.27, 0.07823, 64, -44.59, -37.02, 0.09111, 65, -64.81, -30.47, 0.00012, 7, 57, 61.43, -21.6, 0.00178, 58, 35.99, -30.39, 0.13751, 59, 17.9, -31.81, 0.44429, 60, -4.03, -31.55, 0.32164, 61, -21.37, -32.03, 0.07186, 62, -30.71, -38.62, 0.00702, 64, -62.52, -40.18, 0.0159, 6, 57, 44.55, -26.5, 0.07818, 58, 18.45, -31.4, 0.42366, 59, 0.33, -31.51, 0.41739, 60, -21.44, -29.19, 0.07593, 61, -38.83, -30.05, 0.00415, 64, -79.96, -42.36, 0.0007, 4, 57, 25.98, -31.89, 0.39413, 58, -0.86, -32.52, 0.47677, 59, -19.01, -31.19, 0.12739, 60, -40.61, -26.59, 0.00172, 3, 57, 8.43, -26.88, 0.80689, 58, -16.84, -23.72, 0.18088, 59, -34.29, -21.23, 0.01223, 2, 57, -4.63, -21.52, 0.97639, 58, -28.39, -15.59, 0.02361, 4, 8, -18.78, 0.04, 0.95562, 58, -30.92, 28.28, 0.03988, 59, -44.47, 31.66, 0.00081, 60, -58.49, 38.83, 0.00368, 1, 57, 2.41, -0.88, 1, 2, 57, 18.89, -1.14, 0.70866, 58, -0.91, -0.96, 0.29134, 2, 58, 16.69, -0.86, 0.28523, 59, 0.84, -0.92, 0.71477, 5, 59, 18.43, 0, 0.44116, 60, 0.25, -0.03, 0.55883, 61, -17.79, -0.43, 0, 62, -33.24, -6.92, 0, 64, -66.46, -8.61, 1e-05, 5, 8, 44.56, 57.71, 8e-05, 58, 51.55, 5.1, 0.0001, 60, 18.03, 0.31, 0.50671, 61, -0.02, 0.31, 0.49309, 62, -15.94, -2.8, 3e-05, 6, 8, 50.55, 73.08, 3e-05, 58, 67.76, 8.16, 3e-05, 60, 34.53, 0.21, 0.00063, 61, 16.48, 0.57, 0.42487, 62, 0.2, 0.62, 0.57338, 63, -15.01, -0.1, 0.00105, 3, 62, 15.7, 0.65, 0.37397, 63, 0.47, 0.67, 0.62601, 67, -52.86, 30.98, 2e-05, 2, 64, 0.88, -0.19, 0.99927, 65, -15.79, 1.48, 0.00073, 2, 64, 17.2, -1.21, 0.12618, 65, 0.33, -1.21, 0.87382, 2, 65, 16.06, 0.34, 0.02603, 66, 1.84, -0.05, 0.97397, 2, 66, 16.63, 1.39, 0.02557, 67, 3.65, -0.22, 0.97443], "type": "mesh", "hull": 26}}, "TayPhai": {"TayPhai": {"triangles": [8, 6, 7, 8, 5, 6, 8, 7, 9, 0, 9, 7, 4, 5, 8, 3, 8, 9, 4, 8, 3, 1, 2, 9, 3, 9, 2, 1, 9, 0], "uvs": [1, 0.40215, 1, 1, 0.70741, 1, 0.28476, 1, 0, 1, 0, 0.42828, 0, 0, 1, 0, 0.28322, 0.4715, 0.69698, 0.48119], "vertices": [16.97, 13.81, 53.34, 11.11, 52.77, 3.52, 51.96, -7.44, 51.41, -14.82, 16.63, -12.23, -9.42, -10.29, -7.49, 15.64, 19.8, -5.08, 21.19, 5.6], "type": "mesh", "hull": 8}}, "TayTrai": {"TayTrai": {"rotation": 85.31, "x": 16.15, "width": 25, "y": 0.79, "height": 52}}, "YenNgua": {"YenNgua": {"triangles": [25, 12, 24, 11, 12, 25, 9, 11, 25, 9, 25, 8, 10, 11, 9, 12, 13, 24, 24, 6, 7, 25, 24, 7, 8, 25, 7, 3, 23, 22, 23, 3, 4, 14, 22, 23, 13, 14, 23, 5, 24, 23, 5, 23, 4, 24, 5, 6, 13, 23, 24, 3, 22, 2, 22, 21, 1, 22, 1, 2, 14, 15, 21, 14, 21, 22, 20, 0, 1, 21, 20, 1, 15, 20, 21, 19, 18, 0, 20, 19, 0, 16, 18, 19, 17, 18, 16, 20, 15, 16, 20, 16, 19], "uvs": [0.56553, 0, 0.71405, 0.13681, 0.80272, 0.24447, 0.86478, 0.33314, 0.92242, 0.42181, 0.98227, 0.55692, 1, 0.66247, 1, 0.78914, 0.97783, 0.96225, 0.62095, 1, 0.2134, 0.95224, 0.23303, 0.83136, 0.25077, 0.71525, 0.23968, 0.63292, 0.202, 0.51892, 0.16653, 0.41336, 0.0793, 0.24703, 0, 0.09247, 0.03137, 1e-05, 0.36675, 0.08431, 0.43489, 0.18139, 0.49586, 0.27464, 0.56009, 0.36749, 0.61319, 0.52324, 0.63288, 0.67689, 0.62155, 0.82184], "vertices": [3, 18, 4.69, 25.71, 0.53446, 19, -9.39, 25.71, 0.41965, 20, -25.17, 24.06, 0.04589, 5, 18, 29.01, 30.53, 0.09966, 19, 14.93, 30.53, 0.41687, 20, -1.23, 30.51, 0.42407, 21, -21.99, 26.29, 0.05868, 22, -46.3, 18.07, 0.00073, 5, 18, 46.23, 31.54, 0.00785, 19, 32.14, 31.54, 0.15013, 20, 15.87, 32.69, 0.47058, 21, -5.83, 32.29, 0.31537, 22, -31.53, 26.97, 0.05606, 5, 19, 45.56, 31.29, 0.03967, 20, 29.28, 33.35, 0.27348, 21, 7.08, 35.98, 0.46997, 22, -19.53, 32.98, 0.21656, 23, -43.36, 27.8, 0.00032, 5, 19, 58.69, 30.61, 0.00573, 20, 42.42, 33.56, 0.11732, 21, 19.83, 39.17, 0.41569, 22, -7.59, 38.47, 0.44224, 23, -32.24, 34.81, 0.01902, 4, 20, 60.72, 31, 0.02271, 21, 38.23, 40.82, 0.21082, 22, 10.19, 43.51, 0.62204, 23, -15.28, 42.13, 0.14443, 4, 20, 73.23, 26, 0.00372, 21, 51.54, 38.8, 0.10055, 22, 23.65, 43.99, 0.58264, 23, -2, 44.37, 0.3131, 4, 21, 66.75, 33.93, 0.02935, 22, 39.49, 42.03, 0.42871, 23, 13.96, 44.5, 0.54185, 24, -6.35, 44.57, 9e-05, 4, 21, 86.71, 24.76, 0.00117, 22, 60.81, 36.71, 0.27634, 23, 35.8, 42.02, 0.70034, 24, 15.57, 43.09, 0.02215, 4, 21, 78.19, -17.48, 0.00014, 22, 60.27, -6.38, 0.0014, 23, 40.91, -0.77, 0.01508, 24, 22.62, 0.58, 0.98339, 4, 20, 56.16, -73.76, 0.01783, 21, 57.56, -62.23, 0.09488, 22, 48.28, -54.17, 0.0008, 24, 19.24, -48.58, 0.8865, 7, 18, 68.03, -66.78, 1e-05, 19, 53.94, -66.78, 0.00055, 20, 44.29, -63.92, 0.03173, 21, 43.77, -55.35, 0.12933, 22, 33.46, -49.97, 0.01806, 23, 20.04, -47.49, 0.02188, 24, 3.91, -47.05, 0.79843, 7, 18, 57.22, -56.69, 0.00436, 19, 43.13, -56.69, 0.00735, 20, 32.82, -54.59, 0.08191, 21, 30.49, -48.86, 0.23348, 22, 19.2, -46.06, 0.06163, 23, 5.39, -45.48, 0.0589, 24, -10.81, -45.71, 0.55238, 7, 18, 47.94, -51.87, 0.02352, 19, 33.85, -51.87, 0.02473, 20, 23.24, -50.41, 0.15126, 21, 20.2, -46.97, 0.32725, 22, 8.74, -46.1, 0.07275, 23, -4.97, -46.9, 0.05319, 24, -21.1, -47.59, 0.3473, 7, 18, 33.56, -47.4, 0.10969, 19, 19.47, -47.4, 0.07504, 20, 8.59, -46.93, 0.2518, 21, 5.14, -46.9, 0.34877, 22, -6.07, -48.82, 0.03534, 23, -19.29, -51.54, 0.02125, 24, -35.2, -52.88, 0.15811, 7, 18, 20.2, -43.32, 0.30762, 19, 6.11, -43.32, 0.12295, 20, -5.02, -43.76, 0.25761, 21, -8.82, -46.9, 0.23599, 22, -19.79, -51.41, 0.00692, 23, -32.56, -55.9, 0.00455, 24, -48.25, -57.85, 0.06436, 5, 18, -2.99, -39.98, 0.78779, 19, -17.07, -39.98, 0.05838, 20, -28.38, -42, 0.09244, 21, -31.97, -50.49, 0.05371, 24, -68.62, -69.43, 0.00767, 5, 18, -24.41, -36.7, 0.98145, 19, -38.5, -36.7, 0.00333, 20, -49.97, -40.18, 0.01103, 21, -53.42, -53.62, 0.00411, 24, -87.55, -79.98, 8e-05, 3, 18, -31.84, -26.96, 0.99804, 20, -58.04, -30.97, 0.00195, 21, -63.37, -46.48, 1e-05, 2, 18, -0.17, 0.05, 0.99981, 19, -14.26, 0.05, 0.00019, 5, 18, 14.54, -0.2, 0.37267, 19, 0.45, -0.2, 0.62649, 20, -13.59, -1.12, 0.00069, 21, -26.85, -7.33, 0.00014, 24, -79.17, -27.26, 2e-05, 4, 19, 14.28, -0.88, 0.3741, 20, 0.25, -0.87, 0.62311, 21, -13.43, -3.94, 0.00257, 24, -67.83, -19.33, 0.00023, 3, 20, 14.24, -0.25, 0.48606, 21, 0.06, -0.16, 0.51387, 24, -56.57, -11, 7e-05, 4, 20, 34.36, -4.85, 0, 22, 0.54, 0.08, 0.99997, 23, -19.16, -2.19, 1e-05, 24, -37.31, -3.58, 2e-05, 2, 22, 20.04, 0.04, 0.16476, 23, 0.18, 0.33, 0.83524, 3, 20, 67.17, -23.28, 5e-05, 21, 56.83, -10.58, 0.00032, 24, 0.2, -0.56, 0.99963], "type": "mesh", "hull": 19}}, "baokiem4": {"baokiem3": {"x": 1.04, "width": 17, "y": -21.99, "height": 84}}, "baokiem5": {"baokiem1": {"x": 0.9, "width": 15, "y": 17.83, "height": 6}}, "sword_right_wood_glow": {"sword_right_wood_glow": {"rotation": 90.83, "x": 58.78, "width": 50, "y": 0.68, "height": 212}}, "baokiem2": {"baokiem2": {"x": 2.06, "width": 12, "y": -88.09, "height": 53}}, "baokiem3": {"baokiem3": {"x": 1.04, "width": 17, "y": -21.99, "height": 84}}, "baokiem1": {"baokiem1": {"x": 0.9, "width": 15, "y": 17.83, "height": 6}}, "DayCuong": {"DayCuong": {"triangles": [12, 21, 22, 9, 24, 25, 9, 10, 24, 7, 8, 25, 26, 27, 6, 6, 27, 5, 5, 28, 4, 27, 28, 5, 7, 26, 6, 7, 25, 26, 8, 9, 25, 10, 11, 23, 11, 12, 22, 10, 23, 24, 11, 22, 23, 16, 17, 18, 15, 16, 18, 15, 18, 19, 13, 20, 21, 12, 13, 21, 14, 15, 19, 14, 19, 20, 13, 14, 20, 4, 29, 3, 28, 29, 4, 29, 30, 3, 3, 30, 2, 31, 0, 1, 2, 31, 1, 30, 31, 2], "uvs": [0.94616, 0.51655, 1, 0.60019, 0.98008, 0.72691, 0.867, 0.86124, 0.788, 0.91436, 0.72, 0.96009, 0.6469, 0.97993, 0.573, 0.99999, 0.47701, 1, 0.37229, 0.94995, 0.33574, 0.83739, 0.30727, 0.74973, 0.28117, 0.6438, 0.24084, 0.50388, 0.17441, 0.3949, 0.08311, 0.25426, 0, 0.12625, 0.06133, 0, 0.15818, 0.09846, 0.2578, 0.19974, 0.33413, 0.37969, 0.37512, 0.54697, 0.42532, 0.70586, 0.44297, 0.77, 0.46576, 0.828, 0.52381, 0.87401, 0.57724, 0.87645, 0.63643, 0.86811, 0.68608, 0.83083, 0.73822, 0.79549, 0.80198, 0.75226, 0.86983, 0.61286], "vertices": [1, 41, 7.9, 2.25, 1, 1, 41, 5.91, -4.89, 1, 2, 41, -1.62, -6.16, 0.9988, 42, 31.31, -36.66, 0.0012, 2, 41, -13.7, 1.12, 0.83973, 42, 19.23, -29.37, 0.16027, 2, 41, -20, 7.23, 0.61454, 42, 12.93, -23.27, 0.38546, 2, 41, -25.43, 12.48, 0.39778, 42, 7.5, -18.02, 0.60222, 2, 41, -29.72, 18.86, 0.18924, 42, 3.21, -11.64, 0.81076, 2, 41, -34.07, 25.3, 0.03365, 42, -1.14, -5.19, 0.96635, 2, 40, -41.14, -36.85, 0.02811, 42, -5.42, 3.83, 0.97189, 2, 40, -38.78, -25.83, 0.13235, 42, -7.46, 14.91, 0.86765, 2, 40, -32.45, -21.71, 0.2329, 42, -3.19, 21.14, 0.7671, 2, 40, -27.51, -18.5, 0.34973, 42, 0.14, 26, 0.65027, 2, 40, -21.51, -15.48, 0.50815, 42, 4.52, 31.08, 0.49185, 2, 40, -13.61, -10.89, 0.75138, 42, 10.06, 38.35, 0.24862, 2, 40, -7.64, -3.68, 0.93793, 42, 12.81, 47.3, 0.06207, 1, 40, 0.03, 6.21, 1, 1, 40, 7.02, 15.21, 1, 1, 40, 14.65, 9.2, 1, 2, 40, 9.45, -1.14, 0.99982, 42, 27.62, 56.19, 0.00018, 2, 40, 4.09, -11.78, 0.95296, 42, 26.75, 44.31, 0.04704, 2, 40, -5.94, -20.23, 0.76613, 42, 20.72, 32.67, 0.23387, 2, 40, -15.42, -24.96, 0.52339, 42, 13.78, 24.66, 0.47661, 2, 40, -24.37, -30.63, 0.27031, 42, 7.69, 15.99, 0.72969, 2, 40, -27.99, -32.65, 0.18246, 42, 5.12, 12.74, 0.81754, 2, 40, -31.23, -35.19, 0.09601, 42, 3.1, 9.16, 0.90399, 3, 41, -29.66, 33.06, 0.00093, 40, -33.6, -41.35, 0.01088, 42, 3.27, 2.56, 0.98819, 2, 41, -27.4, 27.97, 0.04805, 42, 5.52, -2.52, 0.95195, 2, 41, -24.33, 22.62, 0.19544, 42, 8.6, -7.88, 0.80456, 2, 41, -20.16, 18.88, 0.36654, 42, 12.77, -11.62, 0.63346, 2, 41, -15.99, 14.86, 0.54225, 42, 16.94, -15.64, 0.45775, 2, 41, -10.88, 9.94, 0.77288, 42, 22.05, -20.55, 0.22712, 2, 41, -0.55, 7.03, 0.9766, 42, 32.38, -23.46, 0.0234], "type": "mesh", "hull": 32}}, "dust2": {"dust": {"triangles": [11, 2, 3, 16, 2, 11, 9, 10, 11, 3, 9, 11, 15, 9, 3, 10, 4, 16, 10, 16, 11, 6, 4, 10, 6, 10, 9, 8, 9, 15, 6, 9, 8, 14, 15, 3, 12, 8, 15, 12, 15, 14, 5, 4, 6, 7, 6, 8, 5, 6, 7, 13, 12, 14, 1, 2, 16, 1, 16, 4, 1, 4, 5, 14, 3, 0, 13, 14, 0, 7, 8, 12, 13, 7, 12, 7, 13, 0, 1, 5, 7, 0, 1, 7], "uvs": [0.95286, 0.96535, 0.06, 0.96535, 0.06, 0.05941, 0.95286, 0.05941, 0.21378, 0.60518, 0.27202, 0.73853, 0.36354, 0.64483, 0.46546, 0.77096, 0.5029, 0.65564, 0.46338, 0.48625, 0.37186, 0.42859, 0.30738, 0.31326, 0.70674, 0.67726, 0.77954, 0.77096, 0.82738, 0.65924, 0.75666, 0.55473, 0.14722, 0.3493], "vertices": [79.75, -46.5, -76.5, -46.5, -76.5, 45, 79.75, 45, -49.59, -10.12, -39.4, -23.59, -23.38, -14.13, -5.54, -26.87, 1.01, -15.22, -5.91, 1.89, -21.92, 7.71, -33.21, 19.36, 36.68, -17.4, 49.42, -26.87, 57.79, -15.58, 45.42, -5.03, -61.24, 15.72], "type": "mesh", "hull": 4}}, "Bong": {"Bong": {"x": -142.23, "width": 249, "y": 27.73, "height": 46}}, "dust3": {"dust": {"triangles": [11, 2, 3, 16, 2, 11, 9, 10, 11, 3, 9, 11, 15, 9, 3, 10, 4, 16, 10, 16, 11, 6, 4, 10, 6, 10, 9, 8, 9, 15, 6, 9, 8, 14, 15, 3, 12, 8, 15, 12, 15, 14, 5, 4, 6, 7, 6, 8, 5, 6, 7, 13, 12, 14, 1, 2, 16, 1, 16, 4, 1, 4, 5, 14, 3, 0, 13, 14, 0, 7, 8, 12, 13, 7, 12, 7, 13, 0, 1, 5, 7, 0, 1, 7], "uvs": [0.95286, 0.96535, 0.06, 0.96535, 0.06, 0.05941, 0.95286, 0.05941, 0.21378, 0.60518, 0.27202, 0.73853, 0.36354, 0.64483, 0.46546, 0.77096, 0.5029, 0.65564, 0.46338, 0.48625, 0.37186, 0.42859, 0.30738, 0.31326, 0.70674, 0.67726, 0.77954, 0.77096, 0.82738, 0.65924, 0.75666, 0.55473, 0.14722, 0.3493], "vertices": [79.75, -46.5, -76.5, -46.5, -76.5, 45, 79.75, 45, -49.59, -10.12, -39.4, -23.59, -23.38, -14.13, -5.54, -26.87, 1.01, -15.22, -5.91, 1.89, -21.92, 7.71, -33.21, 19.36, 36.68, -17.4, 49.42, -26.87, 57.79, -15.58, 45.42, -5.03, -61.24, 15.72], "type": "mesh", "hull": 4}}, "sword_left_glow": {"sword_left_glow": {"rotation": 90.83, "x": 53.02, "width": 48, "y": 1.58, "height": 199}}, "dust4": {"dust": {"triangles": [11, 2, 3, 16, 2, 11, 9, 10, 11, 3, 9, 11, 15, 9, 3, 10, 4, 16, 10, 16, 11, 6, 4, 10, 6, 10, 9, 8, 9, 15, 6, 9, 8, 14, 15, 3, 12, 8, 15, 12, 15, 14, 5, 4, 6, 7, 6, 8, 5, 6, 7, 13, 12, 14, 1, 2, 16, 1, 16, 4, 1, 4, 5, 14, 3, 0, 13, 14, 0, 7, 8, 12, 13, 7, 12, 7, 13, 0, 1, 5, 7, 0, 1, 7], "uvs": [0.95286, 0.96535, 0.06, 0.96535, 0.06, 0.05941, 0.95286, 0.05941, 0.21378, 0.60518, 0.27202, 0.73853, 0.36354, 0.64483, 0.46546, 0.77096, 0.5029, 0.65564, 0.46338, 0.48625, 0.37186, 0.42859, 0.30738, 0.31326, 0.70674, 0.67726, 0.77954, 0.77096, 0.82738, 0.65924, 0.75666, 0.55473, 0.14722, 0.3493], "vertices": [79.75, -46.5, -76.5, -46.5, -76.5, 45, 79.75, 45, -49.59, -10.12, -39.4, -23.59, -23.38, -14.13, -5.54, -26.87, 1.01, -15.22, -5.91, 1.89, -21.92, 7.71, -33.21, 19.36, 36.68, -17.4, 49.42, -26.87, 57.79, -15.58, 45.42, -5.03, -61.24, 15.72], "type": "mesh", "hull": 4}}, "dust5": {"dust": {"triangles": [11, 2, 3, 16, 2, 11, 9, 10, 11, 3, 9, 11, 15, 9, 3, 10, 4, 16, 10, 16, 11, 6, 4, 10, 6, 10, 9, 8, 9, 15, 6, 9, 8, 14, 15, 3, 12, 8, 15, 12, 15, 14, 5, 4, 6, 7, 6, 8, 5, 6, 7, 13, 12, 14, 1, 2, 16, 1, 16, 4, 1, 4, 5, 14, 3, 0, 13, 14, 0, 7, 8, 12, 13, 7, 12, 7, 13, 0, 1, 5, 7, 0, 1, 7], "uvs": [0.95286, 0.96535, 0.06, 0.96535, 0.06, 0.05941, 0.95286, 0.05941, 0.21378, 0.60518, 0.27202, 0.73853, 0.36354, 0.64483, 0.46546, 0.77096, 0.5029, 0.65564, 0.46338, 0.48625, 0.37186, 0.42859, 0.30738, 0.31326, 0.70674, 0.67726, 0.77954, 0.77096, 0.82738, 0.65924, 0.75666, 0.55473, 0.14722, 0.3493], "vertices": [79.75, -46.5, -76.5, -46.5, -76.5, 45, 79.75, 45, -49.59, -10.12, -39.4, -23.59, -23.38, -14.13, -5.54, -26.87, 1.01, -15.22, -5.91, 1.89, -21.92, 7.71, -33.21, 19.36, 36.68, -17.4, 49.42, -26.87, 57.79, -15.58, 45.42, -5.03, -61.24, 15.72], "type": "mesh", "hull": 4}}, "Chan A1": {"Chan A1": {"triangles": [16, 13, 14, 12, 13, 16, 17, 15, 0, 15, 16, 14, 1, 17, 0, 17, 16, 15, 11, 12, 16, 17, 11, 16, 2, 10, 11, 2, 17, 1, 2, 11, 17, 10, 4, 9, 2, 3, 10, 3, 4, 10, 5, 9, 4, 8, 9, 5, 6, 8, 5, 7, 8, 6], "uvs": [1, 0.19043, 1, 0.40439, 0.84885, 0.68465, 0.76615, 0.74295, 0.68372, 0.81104, 0.60671, 0.87229, 0.4685, 1, 0.05816, 1, 0.23966, 0.77147, 0.25544, 0.71029, 0.22782, 0.63687, 0.15626, 0.56257, 0, 0.40031, 0, 0.16741, 0.35366, 0, 0.75708, 0, 0.33392, 0.20563, 0.44477, 0.41512], "vertices": [2, 43, -19.85, 12.77, 0.2076, 45, 42.48, 1.67, 0.7924, 2, 5, 9.66, 33.84, 0.29167, 43, 15.94, -0.42, 0.70833, 2, 5, 62.24, 31.61, 0.91387, 43, 55.32, -35.22, 0.08613, 2, 5, 74.31, 27.03, 0.98764, 43, 61.71, -46.49, 0.01236, 2, 5, 88.06, 23.02, 0.99973, 43, 69.8, -58.33, 0.00027, 1, 5, 100.5, 19.21, 1, 1, 5, 125.85, 13.26, 1, 1, 5, 136.26, -23.03, 1, 2, 5, 92.55, -18.19, 0.99787, 44, 82.74, -20.47, 0.00213, 2, 5, 81.69, -19.8, 0.98072, 44, 73.32, -14.83, 0.01928, 2, 5, 69.83, -25.84, 0.90344, 44, 60.31, -11.99, 0.09656, 2, 5, 58.93, -35.82, 0.72202, 44, 45.56, -12.79, 0.27798, 2, 5, 35.13, -57.6, 0.61, 44, 13.34, -14.56, 0.39, 2, 44, -24.72, 1.86, 0.51, 45, -46.87, -23.96, 0.49, 2, 44, -39.2, 43.54, 0.15, 45, -25.49, 14.64, 0.85, 1, 45, 9.7, 26.43, 1, 2, 5, -7.11, -37.3, 0.28964, 45, -15.35, -20.73, 0.71036, 2, 5, 26.17, -17.48, 0.7087, 45, 6.39, -52.78, 0.2913], "type": "mesh", "hull": 16}}, "dust6": {"dust": {"triangles": [11, 2, 3, 16, 2, 11, 9, 10, 11, 3, 9, 11, 15, 9, 3, 10, 4, 16, 10, 16, 11, 6, 4, 10, 6, 10, 9, 8, 9, 15, 6, 9, 8, 14, 15, 3, 12, 8, 15, 12, 15, 14, 5, 4, 6, 7, 6, 8, 5, 6, 7, 13, 12, 14, 1, 2, 16, 1, 16, 4, 1, 4, 5, 14, 3, 0, 13, 14, 0, 7, 8, 12, 13, 7, 12, 7, 13, 0, 1, 5, 7, 0, 1, 7], "uvs": [0.95286, 0.96535, 0.06, 0.96535, 0.06, 0.05941, 0.95286, 0.05941, 0.21378, 0.60518, 0.27202, 0.73853, 0.36354, 0.64483, 0.46546, 0.77096, 0.5029, 0.65564, 0.46338, 0.48625, 0.37186, 0.42859, 0.30738, 0.31326, 0.70674, 0.67726, 0.77954, 0.77096, 0.82738, 0.65924, 0.75666, 0.55473, 0.14722, 0.3493], "vertices": [79.75, -46.5, -76.5, -46.5, -76.5, 45, 79.75, 45, -49.59, -10.12, -39.4, -23.59, -23.38, -14.13, -5.54, -26.87, 1.01, -15.22, -5.91, 1.89, -21.92, 7.71, -33.21, 19.36, 36.68, -17.4, 49.42, -26.87, 57.79, -15.58, 45.42, -5.03, -61.24, 15.72], "type": "mesh", "hull": 4}}, "Chan A2": {"Chan A2": {"triangles": [3, 8, 2, 8, 0, 2, 0, 1, 2, 3, 4, 8, 4, 5, 8, 7, 0, 8, 8, 5, 6, 7, 8, 6], "uvs": [0.7095, 0.76694, 1, 0.92453, 1, 1, 0.2445, 1, 0.22428, 0.87803, 0, 0.77728, 0, 0, 0.80384, 0, 0.5208, 0.83153], "vertices": [2, 6, 75.83, 13.3, 0.17224, 7, 5.24, 12.27, 0.82776, 1, 7, 28.28, 10.04, 1, 1, 7, 35.14, 4.12, 1, 1, 7, 12.43, -22.19, 1, 2, 6, 88.79, -9.24, 0.11109, 7, 0.74, -13.33, 0.88891, 2, 6, 76.53, -19.36, 0.95034, 7, -15.15, -13.25, 0.04966, 1, 6, -16.73, -17.81, 1, 1, 6, -16.12, 19.16, 1, 1, 7, 5.43, 0.64, 1], "type": "mesh", "hull": 8}}, "Day": {"Day": {"triangles": [5, 9, 10, 5, 10, 11, 5, 11, 12, 4, 5, 12, 8, 9, 5, 13, 4, 12, 6, 8, 5, 7, 8, 6, 3, 4, 13, 3, 13, 14, 2, 3, 14, 15, 2, 14, 1, 2, 15, 1, 15, 0], "uvs": [1, 1, 0.87747, 0.84284, 0.70756, 0.6919, 0.57296, 0.62907, 0.42977, 0.56701, 0.29606, 0.55312, 0.15243, 0.5733, 1e-05, 0.62455, 1e-05, 0.06727, 0.15962, 0, 0.29633, 0, 0.44277, 0.01423, 0.57573, 0.10225, 0.72777, 0.241, 0.87775, 0.37281, 0.99999, 0.56073], "vertices": [105.97, -9.17, 87.13, -10.02, 61.7, -13.29, 42.15, -17.65, 21.41, -22.45, 2.5, -28.3, -17.44, -35.69, -38.29, -44.47, -44.14, -27.03, -22.45, -17.42, -3.26, -10.98, 17.44, -4.54, 37.02, -1.04, 59.81, 1.77, 82.24, 4.7, 101.36, 4.57], "type": "mesh", "hull": 16}}, "Chan C1": {"Chan C1": {"triangles": [12, 13, 14, 14, 15, 1, 1, 15, 16, 1, 12, 14, 1, 11, 12, 1, 10, 11, 6, 7, 5, 5, 7, 4, 3, 4, 8, 4, 7, 8, 2, 3, 9, 10, 1, 2, 9, 3, 8, 2, 9, 10, 0, 1, 16], "uvs": [1, 0.45145, 0.67786, 0.56787, 0.56583, 0.63168, 0.52741, 0.662, 0.48756, 0.69344, 0.30354, 1, 0, 1, 0.02025, 0.6787, 0.00674, 0.62958, 0.00183, 0.5798, 0, 0.52264, 0, 0.42579, 0.01331, 0.31774, 0.04538, 0.19882, 0.16628, 0.09277, 0.42089, 0.00405, 1, 0], "vertices": [2, 46, 24.19, 10.77, 0.95621, 68, 75.41, -31.79, 0.04379, 2, 46, 6.59, -19.8, 0.51479, 12, 17.7, 24.27, 0.48521, 2, 46, 1.86, -34.24, 0.11937, 12, 31.88, 18.8, 0.88063, 2, 46, 0.75, -40.72, 0.04751, 12, 38.29, 17.36, 0.95249, 2, 46, -0.4, -47.44, 0.01308, 12, 44.94, 15.86, 0.98692, 1, 12, 105.35, 17.21, 1, 1, 12, 111.94, -6.48, 1, 1, 12, 52.38, -21.36, 1, 2, 12, 43.62, -24.93, 0.99943, 68, 11.41, -91.47, 0.00057, 2, 12, 34.41, -27.86, 0.99346, 68, 7.73, -82.53, 0.00654, 2, 12, 22.2, -30.89, 0.93, 68, 3.7, -70.62, 0.07, 2, 12, 2.75, -35.82, 0.63, 68, -2.82, -51.64, 0.37, 2, 12, -26.41, -40.11, 0.15, 68, -9.5, -22.93, 0.85, 1, 68, -15.82, 13.95, 1, 1, 68, -13.58, 36.34, 1, 1, 68, 0.06, 59.36, 1, 2, 46, -4.4, 112.1, 0.23655, 68, 43.89, 69.97, 0.76345], "type": "mesh", "hull": 17}}, "Chan C2": {"Chan C2": {"triangles": [11, 3, 4, 8, 10, 11, 7, 8, 11, 4, 5, 6, 7, 11, 4, 6, 7, 4, 3, 10, 0, 1, 3, 0, 3, 1, 2, 11, 10, 3, 9, 10, 8], "uvs": [0.1328, 0, 0.69812, 0.08374, 0.89968, 0.1032, 0.60897, 0.68345, 0.65747, 0.74921, 1, 0.90252, 1, 1, 0.27956, 1, 0.07031, 0.79306, 0, 0.73021, 0.04331, 0.63763, 0.43085, 0.7915], "vertices": [1, 13, -6.97, -13.46, 1, 1, 13, -6.13, 10.28, 1, 1, 13, -0.16, 15.33, 1, 2, 13, 64.87, 11.38, 0.71667, 14, -5.54, 10.96, 0.28333, 2, 13, 73.5, 15.72, 0.17162, 14, 4.13, 10.31, 0.82838, 1, 14, 25.91, 9.15, 1, 1, 14, 34.19, 2.18, 1, 1, 14, 17.48, -17.66, 1, 2, 13, 76.31, -7.45, 0.49957, 14, -4.74, -10.93, 0.50043, 2, 13, 70.76, -11.64, 0.91549, 14, -11.81, -12.12, 0.08451, 2, 13, 60.85, -11.18, 0.71, 14, -20.15, -6.67, 0.29, 2, 13, 76.63, 6.87, 0.28657, 14, 2.56, 0.39, 0.71343], "type": "mesh", "hull": 11}}}}, "skeleton": {"width": 1624, "spine": "3.7.94", "hash": "vTInX0VpC7Snxbuf383qr4zPuew", "height": 960}, "slots": [{"name": "TW_BG", "bone": "bg"}, {"name": "TW_BG2", "bone": "bg"}, {"attachment": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "bone": "<PERSON><PERSON>"}, {"color": "636363ff", "attachment": "<PERSON><PERSON><PERSON>", "name": "ChanTrai", "bone": "ChanTrai"}, {"color": "5c5c5cff", "attachment": "<PERSON><PERSON><PERSON><PERSON>", "name": "DuiTrai", "bone": "DuiTrai"}, {"attachment": "DauNgua", "name": "DauNgua", "bone": "DauNgua"}, {"attachment": "Chan D2", "name": "Chan D2", "bone": "Chan D4"}, {"attachment": "Chan D1", "name": "Chan D1", "bone": "Chan D2"}, {"attachment": "Chan C2", "name": "Chan C2", "bone": "Chan C4"}, {"attachment": "Chan C1", "name": "Chan C1", "bone": "Chan C2"}, {"attachment": "ThanNgua", "name": "ThanNgua", "bone": "ThanNgua"}, {"attachment": "DayCuong", "name": "DayCuong", "bone": "DauNgua3"}, {"attachment": "Chan B2", "name": "Chan B2", "bone": "Chan B4"}, {"attachment": "Chan B1", "name": "Chan B1", "bone": "Chan B2"}, {"attachment": "Chan <PERSON>", "name": "Chan <PERSON>", "bone": "Chan A4"}, {"attachment": "Chan <PERSON>", "name": "Chan <PERSON>", "bone": "Chan <PERSON>"}, {"attachment": "Day", "name": "Day", "bone": "ThanNgua"}, {"attachment": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "bone": "<PERSON><PERSON>"}, {"attachment": "YenNgua", "name": "YenNgua", "bone": "YenNgua"}, {"color": "63636300", "attachment": "<PERSON><PERSON><PERSON>", "name": "ChanTrai2", "bone": "ChanTrai"}, {"color": "5c5c5c00", "attachment": "<PERSON><PERSON><PERSON><PERSON>", "name": "DuiTrai2", "bone": "DuiTrai"}, {"attachment": "TayTrai", "name": "TayTrai", "bone": "TayTrai"}, {"attachment": "CanhTayTrai", "name": "CanhTayTrai", "bone": "CanhTayTrai"}, {"attachment": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>"}, {"attachment": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON><PERSON>"}, {"attachment": "Character", "name": "Character", "bone": "Character"}, {"attachment": "TayPhai", "name": "TayPhai", "bone": "TayPhai"}, {"attachment": "CanhTayPhai", "name": "CanhTayPhai", "bone": "CanhTayPhai"}, {"attachment": "TuiDo", "name": "TuiDo", "bone": "TuiDo"}, {"name": "dust", "bone": "dust"}, {"name": "dust3", "bone": "dust3"}, {"name": "dust6", "bone": "dust6"}, {"name": "dust2", "bone": "dust2"}, {"name": "dust4", "bone": "dust4"}, {"name": "dust5", "bone": "dust5"}, {"attachment": "baokiem3", "name": "baokiem4", "bone": "sword_leftt"}, {"attachment": "sword_left_glow", "name": "sword_left_glow", "bone": "sword_left"}, {"attachment": "sword_left", "name": "sword_left", "bone": "sword_left"}, {"attachment": "sword_left_wood_glow", "name": "sword_left_wood_glow", "bone": "sword_left"}, {"attachment": "sword_left_wood", "name": "sword_left_wood", "bone": "sword_left"}, {"attachment": "baokiem1", "name": "baokiem5", "bone": "sword_leftt"}, {"attachment": "baokiem2", "name": "baokiem6", "bone": "sword_leftt"}, {"attachment": "baokiem3", "name": "baokiem3", "bone": "sword_rightt"}, {"attachment": "sword_right_glow", "name": "sword_right_glow", "bone": "sword_right"}, {"attachment": "sword_right", "name": "sword_right", "bone": "sword_right"}, {"attachment": "sword_right_wood_glow", "name": "sword_right_wood_glow", "bone": "sword_right"}, {"attachment": "sword_right_wood", "name": "sword_right_wood", "bone": "sword_right"}, {"attachment": "baokiem1", "name": "baokiem1", "bone": "sword_rightt"}, {"attachment": "baokiem2", "name": "baokiem2", "bone": "sword_rightt"}], "ik": [{"bones": ["Duoi2"], "name": "Duoi3", "order": 12, "target": "Duoi3"}, {"bones": ["Duoi3"], "name": "Duoi4", "order": 21, "target": "Duoi4"}, {"bones": ["Duoi4"], "name": "Duoi5", "order": 20, "target": "Duoi5"}, {"bones": ["Duoi5"], "name": "Duoi6", "order": 19, "target": "Duoi6"}, {"bones": ["Duoi6"], "name": "Duoi7", "order": 18, "target": "Duoi7"}, {"bones": ["Duoi7"], "name": "Duoi8", "order": 17, "target": "Duoi8"}, {"bones": ["Duoi8"], "name": "Duoi9", "order": 16, "target": "Duoi9"}, {"bones": ["Duoi9"], "name": "Duoi10", "order": 15, "target": "Duoi10"}, {"bones": ["Duoi10"], "name": "Duoi11", "order": 14, "target": "Duoi11"}, {"bones": ["Duoi11"], "name": "Duoi12", "order": 13, "target": "Duoi12"}, {"bendPositive": false, "bones": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "name": "<PERSON><PERSON><PERSON>", "order": 6, "target": "<PERSON><PERSON><PERSON>"}, {"bones": ["Chan <PERSON>", "Chan A4"], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>", "order": 0, "target": "<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"bones": ["Chan C2", "Chan C4"], "name": "<PERSON><PERSON><PERSON><PERSON>_<PERSON>", "order": 2, "target": "<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"bendPositive": false, "bones": ["DuiTrai", "ChanTrai"], "name": "<PERSON><PERSON><PERSON>", "order": 8, "target": "<PERSON><PERSON><PERSON>"}, {"bendPositive": false, "bones": ["Chan B2", "Chan B4"], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>", "order": 10, "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"bendPositive": false, "bones": ["Chan D2", "Chan D4"], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_Trong", "order": 4, "target": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_Trong"}, {"bones": ["Character6"], "name": "<PERSON><PERSON><PERSON>", "order": 7, "target": "<PERSON><PERSON><PERSON>"}, {"bones": ["Character8"], "name": "<PERSON><PERSON><PERSON>", "order": 9, "target": "<PERSON><PERSON><PERSON>"}, {"bones": ["Chan A5"], "name": "IK_<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>i", "order": 1, "target": "IK_<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>i"}, {"bones": ["Chan C5"], "name": "IK_VoChan<PERSON>au_Trong", "order": 3, "target": "IK_VoChan<PERSON>au_Trong"}, {"bones": ["Chan B5"], "name": "IK_VoChanTruoc_Ng<PERSON>i", "order": 11, "target": "IK_VoChanTruoc_Ng<PERSON>i"}, {"bones": ["Chan D5"], "name": "IK_VoChanTruoc_Trong", "order": 5, "target": "IK_VoChanTruoc_Trong"}], "bones": [{"name": "root"}, {"parent": "root", "name": "all"}, {"parent": "all", "name": "MoveNgang", "x": 25.82, "y": 22.33}, {"parent": "MoveNgang", "name": "Move", "x": -10.91, "y": -0.26}, {"parent": "Move", "rotation": -18.53, "name": "ThanNgua", "length": 101.17, "x": -98.88, "y": 12.87}, {"parent": "ThanNgua", "rotation": -87.47, "name": "Chan <PERSON>", "length": 108.43, "x": 56.08, "y": -21.26}, {"parent": "Chan <PERSON>", "rotation": 18.15, "name": "Chan A4", "length": 78.94, "x": 125.81, "y": 0.91}, {"parent": "Chan A4", "rotation": 41.2, "name": "Chan A5", "length": 27.43, "x": 79.68, "y": 0.52}, {"parent": "ThanNgua", "rotation": -148.62, "name": "<PERSON><PERSON>", "x": -31.67, "y": 13.42}, {"parent": "ThanNgua", "rotation": -69.4, "name": "Chan B2", "length": 81.25, "x": 172.46, "y": -16.92}, {"parent": "Chan B2", "rotation": -4.41, "name": "Chan B4", "length": 64.15, "x": 93.23, "y": -0.57}, {"parent": "Chan B4", "rotation": 31.14, "name": "Chan B5", "length": 18.05, "x": 65.35, "y": 2.57}, {"parent": "ThanNgua", "rotation": -87.03, "name": "Chan C2", "length": 86.51, "x": 3.78, "y": -58.71}, {"parent": "Chan C2", "rotation": 11.7, "name": "Chan C4", "length": 75.76, "x": 101.27, "y": 0.49}, {"parent": "Chan C4", "rotation": 41.38, "name": "Chan C5", "length": 27.15, "x": 75.44, "y": 0.59}, {"parent": "ThanNgua", "rotation": -74.03, "name": "Chan D2", "length": 62.72, "x": 135.53, "y": -32.49}, {"parent": "Chan D2", "rotation": -3.5, "name": "Chan D4", "length": 71.25, "x": 70.69, "y": -0.69}, {"parent": "Chan D4", "rotation": 40.98, "name": "Chan D5", "length": 23.08, "x": 72.3, "y": 0.92}, {"parent": "ThanNgua", "rotation": -44.26, "name": "YenNgua", "length": 14.09, "x": 92.52, "y": 59.62}, {"parent": "YenNgua", "name": "YenNgua3", "length": 14.09, "x": 14.09}, {"parent": "YenNgua3", "rotation": -3.89, "name": "YenNgua4", "length": 14.09, "x": 14.09}, {"parent": "YenNgua4", "rotation": -13.12, "name": "YenNgua2", "length": 18.96, "x": 14.22, "y": -0.08}, {"parent": "YenNgua2", "rotation": -10.68, "name": "YenNgua5", "length": 18.96, "x": 20.15, "y": -0.05}, {"parent": "YenNgua5", "rotation": -7.53, "name": "YenNgua6", "length": 18.96, "x": 19.81, "y": -0.26}, {"parent": "YenNgua6", "rotation": -2.61, "name": "YenNgua7", "length": 17.32, "x": 18.28, "y": -0.31}, {"parent": "ThanNgua", "rotation": 82.89, "name": "DauNgua", "length": 55.68, "x": 136.32, "y": 29.34}, {"parent": "DauNgua", "rotation": 0.27, "name": "DauNgua2", "length": 53.89, "x": 59.72, "y": -0.29}, {"parent": "ThanNgua", "name": "TuiDo", "x": 32.49, "y": 52.35}, {"parent": "TuiDo", "rotation": -32.81, "name": "TuiDo2", "length": 13.27, "x": 18.19, "y": -5.37}, {"parent": "TuiDo2", "rotation": -12.09, "name": "TuiDo3", "length": 13.9, "x": 16.66, "y": -0.73}, {"parent": "TuiDo3", "rotation": -8.13, "name": "TuiDo4", "length": 16.38, "x": 13.67, "y": -0.46}, {"parent": "ThanNgua", "rotation": 18.53, "name": "CharacterNgang", "x": 69.14, "y": 61.6}, {"parent": "CharacterNgang", "rotation": -18.53, "name": "Character", "x": -11.94, "y": -2.2}, {"parent": "Character", "rotation": 110.82, "name": "Character2", "length": 37.6, "x": -5.25, "y": 14.09}, {"parent": "Character2", "rotation": -5.12, "name": "Character3", "length": 50.66, "x": 42.63, "y": 0.3}, {"parent": "Character", "rotation": -18.01, "name": "<PERSON><PERSON><PERSON><PERSON>", "length": 58.64, "x": 30.35, "y": 7}, {"parent": "<PERSON><PERSON><PERSON><PERSON>", "rotation": -66.72, "name": "<PERSON><PERSON><PERSON>", "length": 65.26, "x": 60.56, "y": -2.94}, {"parent": "<PERSON><PERSON><PERSON>", "rotation": 76.03, "name": "Character6", "length": 23.55, "x": 67.59, "y": 0.64}, {"parent": "Character3", "rotation": -160.41, "name": "CanhTayPhai", "length": 63.25, "x": 35.86, "y": -39.95}, {"parent": "CanhTayPhai", "rotation": 8.26, "name": "TayPhai", "length": 35.8, "x": 64.52, "y": -0.06}, {"parent": "ThanNgua", "rotation": 105.7, "name": "Character7", "x": 110.7, "y": 92.16}, {"parent": "DauNgua2", "name": "DauNgua3", "x": 14.07, "y": -50.64}, {"parent": "DauNgua2", "name": "DauNgua4", "x": -18.86, "y": -20.15}, {"parent": "ThanNgua", "rotation": -48.14, "name": "Chan A3", "x": 76.07, "y": -18.57}, {"parent": "ThanNgua", "rotation": -48.14, "name": "Chan A6", "x": 2.02, "y": -39.24}, {"parent": "ThanNgua", "name": "Chan A7", "x": 33.77, "y": 4.37}, {"parent": "ThanNgua", "name": "Chan C6", "x": 22.35, "y": -55.33}, {"parent": "ThanNgua", "name": "Chan B6", "x": 200.49, "y": 0.59}, {"parent": "ThanNgua", "name": "Chan B7", "x": 148.61, "y": -9.3}, {"parent": "MoveNgang", "name": "IK_<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>i", "x": -76.64, "y": -246.33}, {"parent": "IK_<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>i", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>", "x": -17.48, "y": 20.37}, {"parent": "MoveNgang", "rotation": -3.26, "name": "IK_VoChan<PERSON>au_Trong", "x": -130.77, "y": -238.05}, {"parent": "IK_VoChan<PERSON>au_Trong", "name": "<PERSON><PERSON><PERSON><PERSON>_<PERSON>", "x": -18.32, "y": 18.58}, {"parent": "MoveNgang", "name": "IK_VoChanTruoc_Trong", "x": 11.41, "y": -220.07}, {"parent": "IK_VoChanTruoc_Trong", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_Trong", "x": -14.1, "y": 15.9}, {"parent": "MoveNgang", "rotation": -5.21, "name": "IK_VoChanTruoc_Ng<PERSON>i", "x": 63.64, "y": -231.68}, {"parent": "IK_VoChanTruoc_Ng<PERSON>i", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>", "x": -13.42, "y": 15.18}, {"parent": "<PERSON><PERSON>", "rotation": 45.13, "name": "Duoi2", "length": 19.56, "x": 7.78, "y": -2.58}, {"parent": "<PERSON><PERSON>", "rotation": 58.02, "name": "Duoi3", "length": 15.78, "x": 21.58, "y": 11.29}, {"parent": "<PERSON><PERSON>", "rotation": 62.27, "name": "Duoi4", "length": 18.18, "x": 29.94, "y": 24.67}, {"parent": "<PERSON><PERSON>", "rotation": 69.04, "name": "Duoi5", "length": 18.04, "x": 38.4, "y": 40.77}, {"parent": "<PERSON><PERSON>", "rotation": 67.78, "name": "Duoi6", "length": 16.16, "x": 44.85, "y": 57.61}, {"parent": "<PERSON><PERSON>", "rotation": 56.74, "name": "Duoi7", "length": 15.2, "x": 50.97, "y": 72.57}, {"parent": "<PERSON><PERSON>", "rotation": 53.96, "name": "Duoi8", "length": 18.94, "x": 59.3, "y": 85.28}, {"parent": "<PERSON><PERSON>", "rotation": 54.19, "name": "Duoi9", "length": 17.1, "x": 70.42, "y": 99.92}, {"parent": "<PERSON><PERSON>", "rotation": 60.1, "name": "Duoi10", "length": 14.24, "x": 80.25, "y": 113.48}, {"parent": "<PERSON><PERSON>", "rotation": 72.36, "name": "Duoi11", "length": 12.66, "x": 87.35, "y": 125.82}, {"parent": "<PERSON><PERSON>", "rotation": 89.14, "name": "Duoi12", "length": 12.33, "x": 90.8, "y": 138.44}, {"parent": "ThanNgua", "name": "Chan C7", "x": -27.22, "y": -39.97}, {"parent": "root", "name": "bg", "x": 788.73, "y": 219.24}, {"parent": "MoveNgang", "name": "dust", "x": 71.35, "y": -188.95}, {"scaleX": 1.375, "parent": "Move", "scaleY": 1.375, "name": "<PERSON><PERSON>", "x": 101.77, "y": -257.7}, {"scaleX": 0.732, "parent": "MoveNgang", "scaleY": 0.732, "name": "dust2", "x": 21.94, "y": -178.72}, {"scaleX": 1.458, "parent": "MoveNgang", "scaleY": 1.458, "name": "dust3", "x": 2.34, "y": -175.31}, {"scaleX": 0.882, "parent": "MoveNgang", "scaleY": 0.882, "name": "dust4", "x": -47.08, "y": -165.09}, {"scaleX": 0.554, "parent": "MoveNgang", "scaleY": 0.554, "name": "dust5", "x": -171.47, "y": -175.31}, {"scaleX": 1.235, "parent": "MoveNgang", "scaleY": 1.235, "name": "dust6", "x": -122.05, "y": -185.54}, {"parent": "Character3", "rotation": 172.92, "name": "CanhTayTrai", "length": 58.34, "x": 38.92, "y": 23.34}, {"parent": "CanhTayTrai", "rotation": 74.77, "name": "TayTrai", "length": 30.2, "x": 57.65, "y": 0.73}, {"parent": "Character", "rotation": -36.58, "name": "DuiTrai", "length": 58.64, "x": -4.69, "y": 5}, {"parent": "DuiTrai", "rotation": -81.69, "name": "ChanTrai", "length": 65.26, "x": 60.56, "y": -2.94}, {"parent": "ChanTrai", "rotation": 76.03, "name": "Character8", "length": 23.55, "x": 67.59, "y": 0.64}, {"parent": "CharacterNgang", "name": "<PERSON><PERSON><PERSON>", "x": 72.56, "y": -121.93}, {"parent": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "x": -21.67, "y": 13}, {"parent": "CharacterNgang", "name": "<PERSON><PERSON><PERSON>", "x": -21.59, "y": -116.32}, {"parent": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "x": -10.54, "y": 23.65}, {"parent": "Character3", "rotation": -60.62, "name": "sword_leftt", "x": 14.07, "y": 14.94}, {"parent": "sword_leftt", "rotation": -90.49, "name": "sword_left", "length": 17.3, "x": 0.65, "y": 37.48}, {"parent": "Character3", "rotation": -65.58, "name": "sword_rightt", "x": 15.19, "y": -6.74}, {"parent": "sword_rightt", "rotation": -90.71, "name": "sword_right", "length": 17.3, "x": 0.79, "y": 49.06}], "animations": {"anim-idle-main-kiemsat": {"slots": {"sword_left": {"attachment": [{"name": "sword_left", "time": 0}]}, "sword_right_wood_glow": {"attachment": [{"name": null, "time": 0}]}, "sword_left_wood": {"attachment": [{"name": null, "time": 0}]}, "sword_right_wood": {"attachment": [{"name": null, "time": 0}]}, "sword_left_glow": {"attachment": [{"name": null, "time": 0}]}, "sword_left_wood_glow": {"attachment": [{"name": null, "time": 0}]}, "sword_right_glow": {"attachment": [{"name": null, "time": 0}]}, "sword_right": {"attachment": [{"name": "sword_right", "time": 0}]}}, "bones": {"IK_VoChanSau_Ngoai": {"rotate": [{"angle": -14.93, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.46, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0.9, "time": 0.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.36, "time": 1}, {"angle": -7.04, "time": 1.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": -59.34, "time": 1.5}, {"angle": -14.93, "time": 2}], "translate": [{"x": 39.9, "y": 26.03, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 48.63, "y": -4.64, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "x": 37.92, "y": -4.32, "time": 0.7667}, {"curve": [0.25, 0, 0.75, 1], "x": 2.41, "y": -1.87, "time": 1}, {"x": -24.17, "y": -2.91, "time": 1.2667}, {"curve": [0.25, 0, 0.75, 1], "x": -30.57, "y": 11.88, "time": 1.5}, {"x": 39.9, "y": 26.03, "time": 2}]}, "ChanPhai": {"rotate": [{"curve": [0.32, 0.29, 0.757, 1], "angle": 0.88, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.34, "time": 0.8}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -0.83, "time": 1.8}, {"angle": 0.88, "time": 2}]}, "Duoi12": {"translate": [{"curve": [0.354, 0.41, 0.756, 1], "x": 3.84, "y": -2.22, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 12.62, "y": -7.28, "time": 0.6667}, {"curve": [0.263, 0, 0.618, 0.43], "x": 0, "y": 0, "time": 1.6667}, {"x": 3.84, "y": -2.22, "time": 2}]}, "Character7": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": -4.49, "y": 5.52, "time": 1}, {"x": 0, "y": 0, "time": 2}]}, "Character2": {"rotate": [{"curve": [0.375, 0.62, 0.716, 1], "angle": 1.19, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.53, "time": 0.1}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.08, "time": 0.6}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.53, "time": 1.1}, {"curve": [0.243, 0, 0.68, 0.71], "angle": -1.08, "time": 1.6}, {"angle": 1.19, "time": 2}]}, "Duoi10": {"translate": [{"curve": [0.381, 0.55, 0.742, 1], "x": 10.22, "y": -5.89, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 16.17, "y": -9.32, "time": 0.4}, {"curve": [0.245, 0, 0.637, 0.56], "x": 0, "y": 0, "time": 1.4}, {"x": 10.22, "y": -5.89, "time": 2}]}, "Duoi11": {"translate": [{"curve": [0.375, 0.5, 0.75, 1], "x": 5.92, "y": -3.41, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 11.83, "y": -6.82, "time": 0.5}, {"curve": [0.25, 0, 0.625, 0.5], "x": 0, "y": 0, "time": 1.5}, {"x": 5.92, "y": -3.41, "time": 2}]}, "Character3": {"rotate": [{"curve": [0.382, 0.57, 0.735, 1], "angle": 0.72, "time": 0}, {"angle": 0, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.53, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.1667}, {"curve": [0.243, 0, 0.649, 0.6], "angle": 2.53, "time": 1.6667}, {"angle": 0.72, "time": 2}], "translate": [{"curve": [0.382, 0.57, 0.735, 1], "x": -0.54, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "x": -1.92, "y": -0.01, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1.1667}, {"curve": [0.243, 0, 0.649, 0.6], "x": -1.92, "y": -0.01, "time": 1.6667}, {"x": -0.54, "y": 0, "time": 2}]}, "TuiDo2": {"rotate": [{"curve": [0.375, 0.62, 0.716, 1], "angle": 1.27, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.2}, {"curve": [0.243, 0, 0.68, 0.71], "angle": 9.79, "time": 1.2}, {"angle": 1.27, "time": 2}]}, "IK_VoChanTruoc_Trong": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -16.6, "time": 0}, {"curve": [0.284, 0, 0.625, 0.38], "angle": 6.33, "time": 0.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": 10.65, "time": 0.5}, {"curve": [0.379, 0.52, 0.747, 1], "angle": 3.48, "time": 0.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.2, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": -75.8, "time": 1.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": -83.45, "time": 1.5}, {"angle": -16.6, "time": 2}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 56.98, "y": 8.38, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 47.12, "y": -0.9, "time": 0.2667}, {"curve": [0.25, 0, 0.625, 0.5], "x": 28.5, "y": -9.39, "time": 0.5}, {"curve": [0.375, 0.5, 0.75, 1], "x": -7.75, "y": -2.7, "time": 0.7667}, {"curve": [0.25, 0, 0.75, 1], "x": -49.35, "y": 2.2, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": -51.87, "y": 5.25, "time": 1.2667}, {"curve": [0.25, 0, 0.75, 1], "x": -3.45, "y": 3.66, "time": 1.5}, {"x": 56.98, "y": 8.38, "time": 2}]}, "ChanTrai": {"rotate": [{"curve": [0.32, 0.29, 0.757, 1], "angle": 0.88, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.34, "time": 0.8}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -0.83, "time": 1.8}, {"angle": 0.88, "time": 2}]}, "IK_VoChanTruoc_Ngoai": {"rotate": [{"angle": -1.82, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -62.06, "time": 0.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": -76.75, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "angle": -62.04, "time": 0.7667}, {"curve": [0.25, 0, 0.625, 0.5], "angle": -13.07, "time": 1}, {"curve": [0.375, 0.5, 0.75, 1], "angle": -4.73, "time": 1.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.03, "time": 1.5}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.52, "time": 1.7667}, {"angle": -1.82, "time": 2}], "translate": [{"x": -36.51, "y": 3.54, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": -48.03, "y": 1.6, "time": 0.2667}, {"curve": [0.25, 0, 0.75, 1], "x": -39.39, "y": 6.12, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 15.57, "time": 0.7667}, {"curve": [0.25, 0, 0.625, 0.5], "x": 46.94, "y": 15.12, "time": 1}, {"curve": [0.375, 0.5, 0.75, 1], "x": 46.12, "y": 0.67, "time": 1.2667}, {"curve": [0.25, 0, 0.75, 1], "x": 27.17, "y": -10.17, "time": 1.5}, {"curve": [0.25, 0, 0.75, 1], "x": -5.75, "y": -7.54, "time": 1.7667}, {"x": -36.51, "y": 3.54, "time": 2}]}, "Move": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -11.52, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -11.52, "time": 1.5}, {"x": 0, "y": 0, "time": 2}]}, "DauNgua4": {"translate": [{"curve": [0.375, 0.5, 0.75, 1], "x": 7.71, "y": 4.18, "time": 0}, {"curve": [0.363, 0.44, 0.755, 1], "x": 9.42, "y": 5.41, "time": 0.2667}, {"curve": [0.25, 0, 0.75, 1], "x": 6, "y": 2.95, "time": 0.7667}, {"curve": [0.25, 0, 0.75, 1], "x": 9.42, "y": 5.41, "time": 1.2667}, {"curve": [0.25, 0, 0.625, 0.5], "x": 6, "y": 2.95, "time": 1.7667}, {"x": 7.71, "y": 4.18, "time": 2}]}, "DauNgua2": {"rotate": [{"curve": [0.379, 0.6, 0.724, 1], "angle": 2.94, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.44, "time": 0.2667}, {"curve": [0.242, 0, 0.667, 0.67], "angle": 0.77, "time": 1.2667}, {"angle": 2.94, "time": 2}]}, "DauNgua": {"rotate": [{"curve": [0.369, 0.63, 0.706, 1], "angle": -0.35, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.1667}, {"curve": [0.244, 0, 0.694, 0.77], "angle": -4.18, "time": 1.1667}, {"angle": -0.35, "time": 2}]}, "YenNgua5": {"translate": [{"curve": [0.382, 0.58, 0.731, 1], "x": -1.06, "y": 0.19, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.3}, {"curve": [0.243, 0, 0.655, 0.63], "x": -4.37, "y": 0.79, "time": 1.3}, {"x": -1.06, "y": 0.19, "time": 2}]}, "Duoi": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 20.99, "time": 1}, {"angle": 0, "time": 2}]}, "Duoi2": {"translate": [{"curve": [0.381, 0.55, 0.742, 1], "x": -0.29, "y": 0.17, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.4}, {"curve": [0.245, 0, 0.637, 0.56], "x": -0.79, "y": 0.45, "time": 1.4}, {"x": -0.29, "y": 0.17, "time": 2}]}, "TuiDo3": {"rotate": [{"curve": [0.381, 0.55, 0.742, 1], "angle": 3.6, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.4}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 9.79, "time": 1.4}, {"angle": 3.6, "time": 2}]}, "Duoi8": {"translate": [{"curve": [0.369, 0.63, 0.706, 1], "x": 22.43, "y": -12.93, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 24.46, "y": -14.1, "time": 0.1667}, {"curve": [0.244, 0, 0.694, 0.77], "x": 0, "y": 0, "time": 1.1667}, {"x": 22.43, "y": -12.93, "time": 2}]}, "TuiDo4": {"rotate": [{"curve": [0.363, 0.44, 0.755, 1], "angle": 6.19, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6}, {"curve": [0.258, 0, 0.619, 0.45], "angle": 9.79, "time": 1.6}, {"angle": 6.19, "time": 2}]}, "Bong": {"scale": [{"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 1.107, "y": 1, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 1.107, "y": 1, "time": 1.5}, {"x": 1, "y": 1, "time": 2}]}, "Duoi7": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 24.06, "y": -13.87, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1}, {"x": 24.06, "y": -13.87, "time": 2}]}, "Duoi9": {"translate": [{"curve": [0.379, 0.6, 0.724, 1], "x": 16.41, "y": -9.46, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 20.12, "y": -11.6, "time": 0.2667}, {"curve": [0.242, 0, 0.667, 0.67], "x": 0, "y": 0, "time": 1.2667}, {"x": 16.41, "y": -9.46, "time": 2}]}, "Duoi4": {"translate": [{"curve": [0.354, 0.41, 0.756, 1], "x": 8.5, "y": -4.9, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.6667}, {"curve": [0.263, 0, 0.618, 0.43], "x": 12.23, "y": -7.05, "time": 1.6667}, {"x": 8.5, "y": -4.9, "time": 2}]}, "IK_VoChanSau_Trong": {"rotate": [{"angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0.03, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "angle": -51.02, "time": 0.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": -46.85, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": -14.66, "time": 1.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0.25, "time": 1.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.24, "time": 1.7667}, {"angle": 0, "time": 2}], "translate": [{"x": 5.48, "y": -1.62, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": -45.38, "y": 1.15, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "x": -20.65, "y": 18.84, "time": 0.7667}, {"curve": [0.25, 0, 0.75, 1], "x": 18.71, "y": 18.68, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 62.25, "y": 8.09, "time": 1.2667}, {"curve": [0.25, 0, 0.75, 1], "x": 57.22, "y": 0.4, "time": 1.5}, {"curve": [0.25, 0, 0.75, 1], "x": 30.55, "y": -1.23, "time": 1.7667}, {"x": 5.48, "y": -1.62, "time": 2}]}, "Duoi3": {"translate": [{"curve": [0.375, 0.5, 0.75, 1], "x": 3.55, "y": -2.05, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.5}, {"curve": [0.25, 0, 0.625, 0.5], "x": 7.1, "y": -4.09, "time": 1.5}, {"x": 3.55, "y": -2.05, "time": 2}]}, "Duoi6": {"translate": [{"curve": [0.289, 0.17, 0.755, 1], "x": 18.51, "y": -10.67, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.9}, {"curve": [0.305, 0, 0.64, 0.36], "x": 19.33, "y": -11.14, "time": 1.9}, {"x": 18.51, "y": -10.67, "time": 2}]}, "Duoi5": {"translate": [{"curve": [0.333, 0.33, 0.758, 1], "x": 12.87, "y": -7.42, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.7667}, {"curve": [0.276, 0, 0.621, 0.4], "x": 15.78, "y": -9.1, "time": 1.7667}, {"x": 12.87, "y": -7.42, "time": 2}]}}, "deform": {"default": {"TayPhai": {"TayPhai": [{"offset": 2, "vertices": [-35.72232, 2.66045, -31.33678, 2.3339, -31.58154, 2.35211, -34.35534, 2.55845], "time": 0}]}, "Day": {"Day": [{"curve": [0.25, 0, 0.75, 1], "time": 0}, {"offset": 2, "vertices": [0.08816, -0.2628, -0.48417, 1.44429, -0.53796, 1.60505, -0.80404, 2.39859, -1.07206, 3.19807, -0.759, 2.26415, 0, 0, 0, 0, -0.32066, 0.95669, -0.7021, 2.09446, -1.09897, 3.27821, -1.146, 3.41855, -1.26741, 3.78075, -1.10969, 3.30995], "curve": [0.25, 0, 0.75, 1], "time": 1}, {"time": 2}]}}}}, "anim-xuongngua-kiemgo": {"slots": {"sword_left": {"attachment": [{"name": null, "time": 0}]}, "sword_right_wood_glow": {"attachment": [{"name": null, "time": 0}]}, "ChanTrai2": {"color": [{"color": "63636300", "time": 0.3333}, {"color": "636363ff", "time": 0.4333}]}, "sword_left_glow": {"attachment": [{"name": null, "time": 0}]}, "sword_left_wood_glow": {"attachment": [{"name": null, "time": 0}]}, "sword_right_glow": {"attachment": [{"name": null, "time": 0}]}, "DuiTrai2": {"color": [{"color": "5c5c5c00", "time": 0.3333}, {"color": "5c5c5cff", "time": 0.4333}]}, "sword_right": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"DuiTrai": {"translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 0.4}, {"x": 8, "y": -13.54, "time": 1}]}, "Character8": {"translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.4}, {"x": 0, "y": 0, "time": 1}]}, "ChanPhai": {"translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 1.3333}]}, "Character7": {"translate": [{"x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 32.49, "y": 23.94, "time": 0.4}, {"x": 32.49, "y": 23.94, "time": 0.6333}, {"x": 10.36, "y": -37.04, "time": 1}]}, "Character": {"rotate": [{"angle": 0, "time": 0}, {"angle": -29.68, "time": 0.3333}, {"curve": [0.098, 0.02, 0.96, 0.71], "angle": -25.41, "time": 0.5667}, {"angle": -9.77, "time": 1}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 29.33, "y": 34.74, "time": 0.3333}, {"curve": [0.098, 0.02, 0.96, 0.71], "x": 26.9, "y": 27.95, "time": 0.5667}, {"curve": [0.214, 0, 0.617, 0.4], "x": -32.05, "y": -154.65, "time": 1}, {"curve": [0.515, 0.26, 0.996, 0.64], "x": -25.46, "y": -167.79, "time": 1.2667}, {"curve": "stepped", "x": -28.13, "y": -157.45, "time": 1.6}, {"x": -28.13, "y": -157.45, "time": 2}]}, "DuiPhai": {"translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 1.0667}, {"x": 3.16, "y": -15.12, "time": 1.4}]}, "Character2": {"rotate": [{"angle": 0, "time": 1}, {"angle": 5.2, "time": 1.3333}, {"angle": 9.95, "time": 1.6667}, {"angle": 6.94, "time": 2}]}, "CanhTayPhai": {"rotate": [{"angle": 0, "time": 0}, {"angle": 5.46, "time": 0.4}, {"angle": 15.44, "time": 0.6333}, {"curve": "stepped", "angle": 68.7, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": 68.7, "time": 1.3333}, {"angle": -13.47, "time": 1.6}]}, "IK_ChanTrai": {"translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 0.4}]}, "Character3": {"rotate": [{"angle": 0, "time": 1.1}, {"angle": 4.25, "time": 1.4333}, {"angle": 1.4, "time": 1.7667}]}, "IK_GotTrai": {"rotate": [{"angle": 0, "time": 0}, {"angle": -92.11, "time": 0.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": -83.85, "time": 0.4}, {"angle": 55.87, "time": 1}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": -88.67, "y": 125.78, "time": 0.2667}, {"curve": [0.25, 0, 0.625, 0.5], "x": -99.52, "y": 118.46, "time": 0.4}, {"curve": [0.375, 0.5, 0.75, 1], "x": -66.77, "y": -17.58, "time": 0.7333}, {"x": -6.86, "y": -182.46, "time": 1}]}, "CanhTayTrai": {"rotate": [{"angle": 0, "time": 0}, {"angle": 18.51, "time": 0.4}, {"angle": 42.31, "time": 0.8667}, {"curve": "stepped", "angle": 80.88, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": 80.88, "time": 1.2333}, {"angle": -3.63, "time": 1.5}]}, "ChanTrai": {"translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.4}, {"x": 0, "y": 0, "time": 1}]}, "TayPhai": {"rotate": [{"angle": 0, "time": 0}, {"angle": 11.7, "time": 0.6333}, {"curve": "stepped", "angle": 89.72, "time": 1}, {"angle": 89.72, "time": 1.3333}, {"angle": 9.37, "time": 1.6667}], "translate": [{"x": -5.41, "y": 0.16, "time": 0.6333}, {"curve": "stepped", "x": -23.28, "y": -0.08, "time": 1}, {"x": -23.28, "y": -0.08, "time": 1.3333}]}, "TayTrai": {"rotate": [{"angle": 0, "time": 0}, {"angle": -66.94, "time": 0.4}, {"angle": -42.84, "time": 0.8}, {"curve": "stepped", "angle": -16.06, "time": 1}, {"angle": -16.06, "time": 1.2333}, {"angle": -36.15, "time": 1.5667}]}, "IK_GotPhai": {"rotate": [{"angle": 0, "time": 0.8333}, {"angle": 74.87, "time": 1.0667}, {"angle": 22.75, "time": 1.4}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 1.0667}, {"x": -69.43, "y": -188.13, "time": 1.4}]}, "IK_ChanPhai": {"translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 1.3333}]}}, "deform": {"default": {"CanhTayPhai": {"CanhTayPhai": [{"time": 0.6333}, {"offset": 2, "curve": "stepped", "vertices": [-9.91504, 0.2048, -19.11042, 0.39425, -26.71201, 0.55083, -26.71201, 0.55083, -19.11042, 0.39425, -9.91504, 0.2048, 0, 0, 0, 0, 0, 0, -9.91504, 0.2048, -19.11042, 0.39425], "time": 1}, {"offset": 2, "vertices": [-9.91504, 0.2048, -19.11042, 0.39425, -26.71201, 0.55083, -26.71201, 0.55083, -19.11042, 0.39425, -9.91504, 0.2048, 0, 0, 0, 0, 0, 0, -9.91504, 0.2048, -19.11042, 0.39425], "time": 1.3333}]}, "TayPhai": {"TayPhai": [{"offset": 2, "curve": "stepped", "vertices": [-36.11156, 2.68999, -31.42137, 2.34056, -31.63351, 2.3564, -34.69065, 2.58411], "time": 0}, {"offset": 2, "vertices": [-36.11156, 2.68999, -31.42137, 2.34056, -31.63351, 2.3564, -34.69065, 2.58411], "time": 0.6333}, {"curve": "stepped", "time": 1}, {"time": 1.3333}]}}}, "drawOrder": [{"offsets": [{"offset": 17, "slot": "ChanTrai"}, {"offset": 17, "slot": "DuiTrai"}, {"offset": -9, "slot": "TuiDo"}], "time": 0.4}]}, "anim-doikiemgo": {"slots": {"sword_left": {"color": [{"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 0.4}, {"color": "ffffff00", "time": 0.5333}]}, "sword_right_wood_glow": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.5333}, {"color": "ffffffff", "time": 0.6667}]}, "sword_left_wood": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.7}, {"color": "ffffffff", "time": 0.8667}]}, "sword_right_wood": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.6}, {"color": "ffffffff", "time": 0.7667}]}, "sword_left_glow": {"color": [{"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 0.4}, {"color": "ffffff00", "time": 0.5333}]}, "sword_left_wood_glow": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.5333}, {"color": "ffffffff", "time": 0.7}]}, "sword_right_glow": {"color": [{"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 0.4}, {"color": "ffffff00", "time": 0.5333}]}, "sword_right": {"color": [{"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 0.4}, {"color": "ffffff00", "time": 0.5333}]}}, "bones": {"sword_left": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 1.1, "y": 87.92, "time": 0.6}, {"curve": [0.25, 0, 0.75, 1], "x": 1.1, "y": 87.92, "time": 0.8667}, {"x": 0, "y": 0, "time": 1.2333}]}, "sword_leftt": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1.2667}, {"curve": [0.25, 0, 0.75, 1], "x": -2.77, "y": -1.26, "time": 1.3667}, {"x": 0, "y": 0, "time": 1.5}]}, "sword_rightt": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1.1333}, {"curve": [0.25, 0, 0.75, 1], "x": -2.77, "y": -1.26, "time": 1.2333}, {"x": 0, "y": 0, "time": 1.4}]}, "sword_right": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 1.1, "y": 87.92, "time": 0.6}, {"curve": [0.25, 0, 0.75, 1], "x": 1.1, "y": 87.92, "time": 0.7667}, {"x": 0, "y": 0, "time": 1.1333}]}}}, "anim-doikiemsat": {"slots": {"sword_left": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.7}, {"color": "ffffffff", "time": 0.8667}]}, "sword_right_wood_glow": {"color": [{"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 0.4}, {"color": "ffffff00", "time": 0.5}]}, "sword_left_wood": {"color": [{"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 0.4}, {"color": "ffffff00", "time": 0.5}]}, "sword_right_wood": {"color": [{"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 0.4}, {"color": "ffffff00", "time": 0.5}]}, "sword_left_glow": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.6}, {"color": "ffffffff", "time": 0.8667}]}, "sword_left_wood_glow": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 0.4}, {"color": "ffffff00", "time": 0.5}]}, "sword_right_glow": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.5}, {"color": "ffffffff", "time": 0.7667}]}, "sword_right": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.6}, {"color": "ffffffff", "time": 0.7667}]}}, "bones": {"sword_left": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 1.1, "y": 87.92, "time": 0.6}, {"curve": [0.25, 0, 0.75, 1], "x": 1.1, "y": 87.92, "time": 0.8667}, {"x": 0, "y": 0, "time": 1.2333}]}, "sword_leftt": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1.2667}, {"curve": [0.25, 0, 0.75, 1], "x": -2.77, "y": -1.26, "time": 1.3667}, {"x": 0, "y": 0, "time": 1.5}]}, "sword_rightt": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1.1333}, {"curve": [0.25, 0, 0.75, 1], "x": -2.77, "y": -1.26, "time": 1.2333}, {"x": 0, "y": 0, "time": 1.4}]}, "sword_right": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 1.1, "y": 87.92, "time": 0.6}, {"curve": [0.25, 0, 0.75, 1], "x": 1.1, "y": 87.92, "time": 0.7667}, {"x": 0, "y": 0, "time": 1.1333}]}}}, "anim-idle-main-kiemgo": {"slots": {"sword_left": {"attachment": [{"name": null, "time": 0}]}, "sword_right_wood_glow": {"attachment": [{"name": null, "time": 0}]}, "sword_left_glow": {"attachment": [{"name": null, "time": 0}]}, "sword_left_wood_glow": {"attachment": [{"name": null, "time": 0}]}, "sword_right_glow": {"attachment": [{"name": null, "time": 0}]}, "sword_right": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"IK_VoChanSau_Ngoai": {"rotate": [{"angle": -14.93, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.46, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0.9, "time": 0.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.36, "time": 1}, {"angle": -7.04, "time": 1.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": -59.34, "time": 1.5}, {"angle": -14.93, "time": 2}], "translate": [{"x": 39.9, "y": 26.03, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 48.63, "y": -4.64, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "x": 37.92, "y": -4.32, "time": 0.7667}, {"curve": [0.25, 0, 0.75, 1], "x": 2.41, "y": -1.87, "time": 1}, {"x": -24.17, "y": -2.91, "time": 1.2667}, {"curve": [0.25, 0, 0.75, 1], "x": -30.57, "y": 11.88, "time": 1.5}, {"x": 39.9, "y": 26.03, "time": 2}]}, "ChanPhai": {"rotate": [{"curve": [0.32, 0.29, 0.757, 1], "angle": 0.88, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.34, "time": 0.8}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -0.83, "time": 1.8}, {"angle": 0.88, "time": 2}]}, "Duoi12": {"translate": [{"curve": [0.354, 0.41, 0.756, 1], "x": 3.84, "y": -2.22, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 12.62, "y": -7.28, "time": 0.6667}, {"curve": [0.263, 0, 0.618, 0.43], "x": 0, "y": 0, "time": 1.6667}, {"x": 3.84, "y": -2.22, "time": 2}]}, "Character7": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": -4.49, "y": 5.52, "time": 1}, {"x": 0, "y": 0, "time": 2}]}, "Character2": {"rotate": [{"curve": [0.375, 0.62, 0.716, 1], "angle": 1.19, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.53, "time": 0.1}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.08, "time": 0.6}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.53, "time": 1.1}, {"curve": [0.243, 0, 0.68, 0.71], "angle": -1.08, "time": 1.6}, {"angle": 1.19, "time": 2}]}, "Duoi10": {"translate": [{"curve": [0.381, 0.55, 0.742, 1], "x": 10.22, "y": -5.89, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 16.17, "y": -9.32, "time": 0.4}, {"curve": [0.245, 0, 0.637, 0.56], "x": 0, "y": 0, "time": 1.4}, {"x": 10.22, "y": -5.89, "time": 2}]}, "Duoi11": {"translate": [{"curve": [0.375, 0.5, 0.75, 1], "x": 5.92, "y": -3.41, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 11.83, "y": -6.82, "time": 0.5}, {"curve": [0.25, 0, 0.625, 0.5], "x": 0, "y": 0, "time": 1.5}, {"x": 5.92, "y": -3.41, "time": 2}]}, "Character3": {"rotate": [{"curve": [0.382, 0.57, 0.735, 1], "angle": 0.72, "time": 0}, {"angle": 0, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.53, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.1667}, {"curve": [0.243, 0, 0.649, 0.6], "angle": 2.53, "time": 1.6667}, {"angle": 0.72, "time": 2}], "translate": [{"curve": [0.382, 0.57, 0.735, 1], "x": -0.54, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "x": -1.92, "y": -0.01, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1.1667}, {"curve": [0.243, 0, 0.649, 0.6], "x": -1.92, "y": -0.01, "time": 1.6667}, {"x": -0.54, "y": 0, "time": 2}]}, "TuiDo2": {"rotate": [{"curve": [0.375, 0.62, 0.716, 1], "angle": 1.27, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.2}, {"curve": [0.243, 0, 0.68, 0.71], "angle": 9.79, "time": 1.2}, {"angle": 1.27, "time": 2}]}, "IK_VoChanTruoc_Trong": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -16.6, "time": 0}, {"curve": [0.284, 0, 0.625, 0.38], "angle": 6.33, "time": 0.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": 10.65, "time": 0.5}, {"curve": [0.379, 0.52, 0.747, 1], "angle": 3.48, "time": 0.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.2, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": -75.8, "time": 1.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": -83.45, "time": 1.5}, {"angle": -16.6, "time": 2}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 56.98, "y": 8.38, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 47.12, "y": -0.9, "time": 0.2667}, {"curve": [0.25, 0, 0.625, 0.5], "x": 28.5, "y": -9.39, "time": 0.5}, {"curve": [0.375, 0.5, 0.75, 1], "x": -7.75, "y": -2.7, "time": 0.7667}, {"curve": [0.25, 0, 0.75, 1], "x": -49.35, "y": 2.2, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": -51.87, "y": 5.25, "time": 1.2667}, {"curve": [0.25, 0, 0.75, 1], "x": -3.45, "y": 3.66, "time": 1.5}, {"x": 56.98, "y": 8.38, "time": 2}]}, "ChanTrai": {"rotate": [{"curve": [0.32, 0.29, 0.757, 1], "angle": 0.88, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.34, "time": 0.8}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -0.83, "time": 1.8}, {"angle": 0.88, "time": 2}]}, "IK_VoChanTruoc_Ngoai": {"rotate": [{"angle": -1.82, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -62.06, "time": 0.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": -76.75, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "angle": -62.04, "time": 0.7667}, {"curve": [0.25, 0, 0.625, 0.5], "angle": -13.07, "time": 1}, {"curve": [0.375, 0.5, 0.75, 1], "angle": -4.73, "time": 1.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.03, "time": 1.5}, {"curve": [0.25, 0, 0.75, 1], "angle": -0.52, "time": 1.7667}, {"angle": -1.82, "time": 2}], "translate": [{"x": -36.51, "y": 3.54, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": -48.03, "y": 1.6, "time": 0.2667}, {"curve": [0.25, 0, 0.75, 1], "x": -39.39, "y": 6.12, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 15.57, "time": 0.7667}, {"curve": [0.25, 0, 0.625, 0.5], "x": 46.94, "y": 15.12, "time": 1}, {"curve": [0.375, 0.5, 0.75, 1], "x": 46.12, "y": 0.67, "time": 1.2667}, {"curve": [0.25, 0, 0.75, 1], "x": 27.17, "y": -10.17, "time": 1.5}, {"curve": [0.25, 0, 0.75, 1], "x": -5.75, "y": -7.54, "time": 1.7667}, {"x": -36.51, "y": 3.54, "time": 2}]}, "Move": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -11.52, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -11.52, "time": 1.5}, {"x": 0, "y": 0, "time": 2}]}, "DauNgua4": {"translate": [{"curve": [0.375, 0.5, 0.75, 1], "x": 7.71, "y": 4.18, "time": 0}, {"curve": [0.363, 0.44, 0.755, 1], "x": 9.42, "y": 5.41, "time": 0.2667}, {"curve": [0.25, 0, 0.75, 1], "x": 6, "y": 2.95, "time": 0.7667}, {"curve": [0.25, 0, 0.75, 1], "x": 9.42, "y": 5.41, "time": 1.2667}, {"curve": [0.25, 0, 0.625, 0.5], "x": 6, "y": 2.95, "time": 1.7667}, {"x": 7.71, "y": 4.18, "time": 2}]}, "DauNgua2": {"rotate": [{"curve": [0.379, 0.6, 0.724, 1], "angle": 2.94, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.44, "time": 0.2667}, {"curve": [0.242, 0, 0.667, 0.67], "angle": 0.77, "time": 1.2667}, {"angle": 2.94, "time": 2}]}, "DauNgua": {"rotate": [{"curve": [0.369, 0.63, 0.706, 1], "angle": -0.35, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.1667}, {"curve": [0.244, 0, 0.694, 0.77], "angle": -4.18, "time": 1.1667}, {"angle": -0.35, "time": 2}]}, "YenNgua5": {"translate": [{"curve": [0.382, 0.58, 0.731, 1], "x": -1.06, "y": 0.19, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.3}, {"curve": [0.243, 0, 0.655, 0.63], "x": -4.37, "y": 0.79, "time": 1.3}, {"x": -1.06, "y": 0.19, "time": 2}]}, "Duoi": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 20.99, "time": 1}, {"angle": 0, "time": 2}]}, "Duoi2": {"translate": [{"curve": [0.381, 0.55, 0.742, 1], "x": -0.29, "y": 0.17, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.4}, {"curve": [0.245, 0, 0.637, 0.56], "x": -0.79, "y": 0.45, "time": 1.4}, {"x": -0.29, "y": 0.17, "time": 2}]}, "TuiDo3": {"rotate": [{"curve": [0.381, 0.55, 0.742, 1], "angle": 3.6, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.4}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 9.79, "time": 1.4}, {"angle": 3.6, "time": 2}]}, "Duoi8": {"translate": [{"curve": [0.369, 0.63, 0.706, 1], "x": 22.43, "y": -12.93, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 24.46, "y": -14.1, "time": 0.1667}, {"curve": [0.244, 0, 0.694, 0.77], "x": 0, "y": 0, "time": 1.1667}, {"x": 22.43, "y": -12.93, "time": 2}]}, "TuiDo4": {"rotate": [{"curve": [0.363, 0.44, 0.755, 1], "angle": 6.19, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6}, {"curve": [0.258, 0, 0.619, 0.45], "angle": 9.79, "time": 1.6}, {"angle": 6.19, "time": 2}]}, "Bong": {"scale": [{"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 1.107, "y": 1, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 1.107, "y": 1, "time": 1.5}, {"x": 1, "y": 1, "time": 2}]}, "Duoi7": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 24.06, "y": -13.87, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1}, {"x": 24.06, "y": -13.87, "time": 2}]}, "Duoi9": {"translate": [{"curve": [0.379, 0.6, 0.724, 1], "x": 16.41, "y": -9.46, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 20.12, "y": -11.6, "time": 0.2667}, {"curve": [0.242, 0, 0.667, 0.67], "x": 0, "y": 0, "time": 1.2667}, {"x": 16.41, "y": -9.46, "time": 2}]}, "Duoi4": {"translate": [{"curve": [0.354, 0.41, 0.756, 1], "x": 8.5, "y": -4.9, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.6667}, {"curve": [0.263, 0, 0.618, 0.43], "x": 12.23, "y": -7.05, "time": 1.6667}, {"x": 8.5, "y": -4.9, "time": 2}]}, "IK_VoChanSau_Trong": {"rotate": [{"angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0.03, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "angle": -51.02, "time": 0.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": -46.85, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": -14.66, "time": 1.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0.25, "time": 1.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.24, "time": 1.7667}, {"angle": 0, "time": 2}], "translate": [{"x": 5.48, "y": -1.62, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": -45.38, "y": 1.15, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "x": -20.65, "y": 18.84, "time": 0.7667}, {"curve": [0.25, 0, 0.75, 1], "x": 18.71, "y": 18.68, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 62.25, "y": 8.09, "time": 1.2667}, {"curve": [0.25, 0, 0.75, 1], "x": 57.22, "y": 0.4, "time": 1.5}, {"curve": [0.25, 0, 0.75, 1], "x": 30.55, "y": -1.23, "time": 1.7667}, {"x": 5.48, "y": -1.62, "time": 2}]}, "Duoi3": {"translate": [{"curve": [0.375, 0.5, 0.75, 1], "x": 3.55, "y": -2.05, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.5}, {"curve": [0.25, 0, 0.625, 0.5], "x": 7.1, "y": -4.09, "time": 1.5}, {"x": 3.55, "y": -2.05, "time": 2}]}, "Duoi6": {"translate": [{"curve": [0.289, 0.17, 0.755, 1], "x": 18.51, "y": -10.67, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.9}, {"curve": [0.305, 0, 0.64, 0.36], "x": 19.33, "y": -11.14, "time": 1.9}, {"x": 18.51, "y": -10.67, "time": 2}]}, "Duoi5": {"translate": [{"curve": [0.333, 0.33, 0.758, 1], "x": 12.87, "y": -7.42, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.7667}, {"curve": [0.276, 0, 0.621, 0.4], "x": 15.78, "y": -9.1, "time": 1.7667}, {"x": 12.87, "y": -7.42, "time": 2}]}}, "deform": {"default": {"TayPhai": {"TayPhai": [{"offset": 2, "vertices": [-35.72232, 2.66045, -31.33678, 2.3339, -31.58154, 2.35211, -34.35534, 2.55845], "time": 0}]}, "Day": {"Day": [{"curve": [0.25, 0, 0.75, 1], "time": 0}, {"offset": 2, "vertices": [0.08816, -0.2628, -0.48417, 1.44429, -0.53796, 1.60505, -0.80404, 2.39859, -1.07206, 3.19807, -0.759, 2.26415, 0, 0, 0, 0, -0.32066, 0.95669, -0.7021, 2.09446, -1.09897, 3.27821, -1.146, 3.41855, -1.26741, 3.78075, -1.10969, 3.30995], "curve": [0.25, 0, 0.75, 1], "time": 1}, {"time": 2}]}}}}, "anim-nguachay-kiemsat": {"slots": {"sword_left": {"attachment": [{"name": "sword_left", "time": 0}]}, "sword_left_wood": {"attachment": [{"name": null, "time": 0}]}, "sword_right_wood": {"attachment": [{"name": null, "time": 0}]}, "sword_right": {"attachment": [{"name": "sword_right", "time": 0}]}, "sword_right_wood_glow": {"attachment": [{"name": null, "time": 0}]}, "dust2": {"color": [{"color": "ffffff00", "time": 0.6667}, {"color": "ffffffff", "time": 1.2}, {"color": "ffffff00", "curve": "stepped", "time": 1.5667}, {"color": "ffffff00", "time": 1.8333}, {"color": "ffffffff", "time": 2.3667}, {"color": "ffffff00", "time": 2.7333}], "attachment": [{"name": "dust", "time": 0.9667}, {"name": null, "time": 1.7333}, {"name": "dust", "time": 2.1333}]}, "dust3": {"color": [{"color": "ffffff00", "time": 0.9667}, {"color": "ffffffff", "time": 1.5667}, {"color": "ffffff00", "time": 2.2}], "attachment": [{"name": "dust", "time": 1.4}]}, "sword_left_glow": {"attachment": [{"name": null, "time": 0}]}, "dust4": {"color": [{"color": "ffffff00", "time": 0.8333}, {"color": "ffffffff", "time": 1.4}, {"color": "ffffff00", "curve": "stepped", "time": 1.8}, {"color": "ffffff00", "time": 1.9333}, {"color": "ffffffff", "time": 2.5}, {"color": "ffffff00", "time": 2.9}], "attachment": [{"name": "dust", "time": 1.1333}, {"name": null, "time": 1.8667}, {"name": "dust", "time": 2.2333}]}, "dust5": {"color": [{"color": "ffffff00", "time": 0.7333}, {"color": "ffffffff", "time": 1.3333}, {"color": "ffffff00", "curve": "stepped", "time": 1.7333}, {"color": "ffffff00", "time": 1.9333}, {"color": "ffffffff", "time": 2.5333}, {"color": "ffffff00", "time": 2.9333}], "attachment": [{"name": "dust", "time": 1.0667}, {"name": null, "time": 1.8667}, {"name": "dust", "time": 2.2667}]}, "dust6": {"color": [{"color": "ffffff00", "time": 0.8667}, {"color": "ffffffff", "time": 1.5333}, {"color": "ffffff00", "curve": "stepped", "time": 2}, {"color": "ffffff00", "time": 2.1667}, {"color": "ffffffff", "time": 2.8333}, {"color": "ffffff00", "time": 3.3}], "attachment": [{"name": "dust", "time": 1.2333}, {"name": null, "time": 2.1}, {"name": "dust", "time": 2.5333}]}, "sword_left_wood_glow": {"attachment": [{"name": null, "time": 0}]}, "dust": {"color": [{"color": "ffffff00", "time": 0.4333}, {"color": "ffffffff", "time": 1.0667}, {"color": "ffffff00", "curve": "stepped", "time": 1.3333}, {"color": "ffffff00", "time": 1.4667}, {"color": "ffffffff", "time": 2.1}, {"color": "ffffff00", "time": 2.3667}], "attachment": [{"name": "dust", "time": 0.7333}, {"name": null, "time": 1.4}, {"name": "dust", "time": 1.7667}]}, "sword_right_glow": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"IK_VoChanSau_Ngoai": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -1.36, "time": 0}, {"angle": 0, "time": 0.5333}, {"angle": -3.22, "time": 0.6}, {"angle": -43.24, "time": 0.6667}, {"angle": -56.66, "time": 0.7}, {"angle": -74.13, "time": 0.7667}, {"angle": -42.78, "time": 0.8667}, {"angle": -12.76, "time": 0.9333}, {"angle": -0.77, "time": 1.0333}, {"angle": -1.54, "time": 1.1}, {"angle": 1.14, "time": 1.2}, {"angle": -1.42, "time": 1.2333}, {"angle": -16.39, "time": 1.2667}, {"angle": -43.24, "time": 1.3333}, {"angle": -56.66, "time": 1.3667}, {"angle": -74.13, "time": 1.4333}, {"angle": -42.78, "time": 1.5333}, {"angle": -12.76, "time": 1.6}, {"angle": -0.77, "time": 1.7}, {"angle": -1.54, "time": 1.7667}, {"angle": 1.14, "time": 1.8667}, {"angle": -1.42, "time": 1.9}, {"angle": -16.39, "time": 1.9333}, {"angle": -43.24, "time": 2}, {"angle": -56.66, "time": 2.0333}, {"angle": -74.13, "time": 2.1}, {"angle": -42.78, "time": 2.2}, {"angle": -12.76, "time": 2.2667}, {"angle": -0.77, "time": 2.3667}, {"angle": -1.54, "time": 2.4333}, {"angle": 1.14, "time": 2.5333}, {"angle": -1.42, "time": 2.5667}, {"angle": -16.39, "time": 2.6}, {"angle": -43.24, "time": 2.6667}, {"angle": -56.66, "time": 2.7}, {"angle": -74.13, "time": 2.7667}, {"angle": -42.78, "time": 2.8667}, {"angle": -12.76, "time": 2.9333}, {"angle": -0.77, "time": 3.0333}, {"angle": -1.54, "time": 3.1}, {"angle": 1.14, "time": 3.2}, {"angle": -1.42, "time": 3.2333}, {"angle": -16.39, "time": 3.2667}, {"angle": -43.24, "time": 3.3333}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 2.41, "y": -1.87, "time": 0}, {"x": 0, "y": 0, "time": 0.5333}, {"x": -52.79, "y": 12.14, "time": 0.6}, {"x": -105.57, "y": 38.63, "time": 0.6667}, {"x": -127.14, "y": 51.17, "time": 0.7}, {"x": -110.41, "y": 62.71, "time": 0.7667}, {"x": -20.04, "y": 105.01, "time": 0.8667}, {"x": 24.65, "y": 78.13, "time": 0.9333}, {"x": 77.99, "y": 39.95, "time": 1.0333}, {"x": 99.8, "y": 27.46, "time": 1.1}, {"x": 61.28, "y": 18.71, "time": 1.2}, {"x": 19.51, "y": 14.04, "time": 1.2333}, {"x": -62.43, "y": 13.55, "time": 1.2667}, {"x": -105.57, "y": 38.63, "time": 1.3333}, {"x": -127.14, "y": 51.17, "time": 1.3667}, {"x": -110.41, "y": 62.71, "time": 1.4333}, {"x": -20.04, "y": 105.01, "time": 1.5333}, {"x": 24.65, "y": 78.13, "time": 1.6}, {"x": 77.99, "y": 39.95, "time": 1.7}, {"x": 99.8, "y": 27.46, "time": 1.7667}, {"x": 61.28, "y": 18.71, "time": 1.8667}, {"x": 19.51, "y": 14.04, "time": 1.9}, {"x": -62.43, "y": 13.55, "time": 1.9333}, {"x": -105.57, "y": 38.63, "time": 2}, {"x": -127.14, "y": 51.17, "time": 2.0333}, {"x": -110.41, "y": 62.71, "time": 2.1}, {"x": -20.04, "y": 105.01, "time": 2.2}, {"x": 24.65, "y": 78.13, "time": 2.2667}, {"x": 77.99, "y": 39.95, "time": 2.3667}, {"x": 99.8, "y": 27.46, "time": 2.4333}, {"x": 61.28, "y": 18.71, "time": 2.5333}, {"x": 19.51, "y": 14.04, "time": 2.5667}, {"x": -62.43, "y": 13.55, "time": 2.6}, {"x": -105.57, "y": 38.63, "time": 2.6667}, {"x": -127.14, "y": 51.17, "time": 2.7}, {"x": -110.41, "y": 62.71, "time": 2.7667}, {"x": -20.04, "y": 105.01, "time": 2.8667}, {"x": 24.65, "y": 78.13, "time": 2.9333}, {"x": 77.99, "y": 39.95, "time": 3.0333}, {"x": 99.8, "y": 27.46, "time": 3.1}, {"x": 61.28, "y": 18.71, "time": 3.2}, {"x": 19.51, "y": 14.04, "time": 3.2333}, {"x": -62.43, "y": 13.55, "time": 3.2667}, {"x": -105.57, "y": 38.63, "time": 3.3333}]}, "ChanPhai": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.02, "time": 0.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.82, "time": 0.5333}, {"curve": [0.32, 0.29, 0.757, 1], "angle": 0.88, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.34, "time": 0.9333}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -0.83, "time": 1.2667}, {"curve": [0.32, 0.29, 0.757, 1], "angle": 0.88, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.34, "time": 1.6}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -0.83, "time": 1.9333}, {"curve": [0.32, 0.29, 0.757, 1], "angle": 0.88, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.34, "time": 2.2667}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -0.83, "time": 2.6}, {"curve": [0.32, 0.29, 0.757, 1], "angle": 0.88, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.34, "time": 2.9333}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -0.83, "time": 3.2667}, {"angle": 0.88, "time": 3.3333}]}, "Duoi12": {"rotate": [{"angle": 0, "time": 0}, {"angle": -8.58, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -130.41, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -15.26, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": -130.41, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -15.26, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -130.41, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -15.26, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -130.41, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -15.26, "time": 3}, {"angle": -130.41, "time": 3.3333}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 17.81, "y": -32.7, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 51.76, "y": -96.75, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 39.8, "y": -56.1, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 51.76, "y": -96.75, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 39.8, "y": -56.1, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 51.76, "y": -96.75, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 39.8, "y": -56.1, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 51.76, "y": -96.75, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 39.8, "y": -56.1, "time": 3}, {"x": 51.76, "y": -96.75, "time": 3.3333}]}, "Character7": {"translate": [{"x": -4.49, "y": 5.52, "time": 0}]}, "Character2": {"rotate": [{"angle": 0, "time": 0.0667}, {"angle": 5.95, "time": 0.2}, {"angle": 0.27, "time": 0.3333}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 0.97, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.53, "time": 0.7333}, {"curve": [0.243, 0, 0.68, 0.71], "angle": -2.81, "time": 1.0667}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 0.97, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.53, "time": 1.4}, {"curve": [0.243, 0, 0.68, 0.71], "angle": -2.81, "time": 1.7333}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 0.97, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.53, "time": 2.0667}, {"curve": [0.243, 0, 0.68, 0.71], "angle": -2.81, "time": 2.4}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 0.97, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.53, "time": 2.7333}, {"curve": [0.243, 0, 0.68, 0.71], "angle": -2.81, "time": 3.0667}, {"angle": 0.97, "time": 3.3333}]}, "Duoi10": {"rotate": [{"angle": 0, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 25.9, "y": -39.87, "time": 0.3333}, {"curve": [0.32, 0.29, 0.757, 1], "x": 37.62, "y": -61.39, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 35.84, "y": -55.7, "time": 0.9333}, {"curve": [0.284, 0, 0.625, 0.38], "x": 37.89, "y": -62.24, "time": 1.2667}, {"curve": [0.32, 0.29, 0.757, 1], "x": 37.62, "y": -61.39, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 35.84, "y": -55.7, "time": 1.6}, {"curve": [0.284, 0, 0.625, 0.38], "x": 37.89, "y": -62.24, "time": 1.9333}, {"curve": [0.32, 0.29, 0.757, 1], "x": 37.62, "y": -61.39, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 35.84, "y": -55.7, "time": 2.2667}, {"curve": [0.284, 0, 0.625, 0.38], "x": 37.89, "y": -62.24, "time": 2.6}, {"curve": [0.32, 0.29, 0.757, 1], "x": 37.62, "y": -61.39, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 35.84, "y": -55.7, "time": 2.9333}, {"curve": [0.284, 0, 0.625, 0.38], "x": 37.89, "y": -62.24, "time": 3.2667}, {"x": 37.62, "y": -61.39, "time": 3.3333}]}, "Duoi11": {"rotate": [{"angle": 2.35, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 21.19, "y": -33.92, "time": 0.3333}, {"curve": [0.289, 0.17, 0.755, 1], "x": 42.51, "y": -77.52, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 36.54, "y": -54.85, "time": 0.9667}, {"curve": [0.305, 0, 0.64, 0.36], "x": 42.77, "y": -78.52, "time": 1.3}, {"curve": [0.289, 0.17, 0.755, 1], "x": 42.51, "y": -77.52, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 36.54, "y": -54.85, "time": 1.6333}, {"curve": [0.305, 0, 0.64, 0.36], "x": 42.77, "y": -78.52, "time": 1.9667}, {"curve": [0.289, 0.17, 0.755, 1], "x": 42.51, "y": -77.52, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 36.54, "y": -54.85, "time": 2.3}, {"curve": [0.305, 0, 0.64, 0.36], "x": 42.77, "y": -78.52, "time": 2.6333}, {"curve": [0.289, 0.17, 0.755, 1], "x": 42.51, "y": -77.52, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 36.54, "y": -54.85, "time": 2.9667}, {"curve": [0.305, 0, 0.64, 0.36], "x": 42.77, "y": -78.52, "time": 3.3}, {"x": 42.51, "y": -77.52, "time": 3.3333}]}, "ThanNgua": {"rotate": [{"curve": [0.46, 0, 1, 0.87], "angle": 0, "time": 0}, {"curve": "stepped", "angle": 9.42, "time": 0.1667}, {"curve": [0.188, 0, 1, 0.41], "angle": 9.42, "time": 0.3333}, {"angle": 0, "time": 0.6667}], "translate": [{"curve": [0.46, 0, 1, 0.87], "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": -21.69, "y": -12.86, "time": 0.1667}, {"curve": [0.188, 0, 1, 0.41], "x": -21.69, "y": -12.86, "time": 0.3333}, {"curve": "stepped", "x": 9.3, "y": 0, "time": 0.6667}, {"curve": "stepped", "x": 9.3, "y": 0, "time": 1.3333}, {"curve": "stepped", "x": 9.3, "y": 0, "time": 2}, {"x": 9.3, "y": 0, "time": 2.6667}]}, "Character3": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 4.2, "time": 0}, {"angle": 0, "time": 0.1333}, {"angle": 10.66, "time": 0.2667}, {"angle": 6.14, "time": 0.4}, {"angle": -10.44, "time": 0.5333}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -0.8, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.8}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -2.18, "time": 1.1333}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -0.8, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.4667}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -2.18, "time": 1.8}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -0.8, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.1333}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -2.18, "time": 2.4667}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -0.8, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.8}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -2.18, "time": 3.1333}, {"angle": -0.8, "time": 3.3333}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.1333}, {"x": 0, "y": 0, "time": 0.5333}, {"curve": [0.381, 0.55, 0.742, 1], "x": 2.44, "y": -0.1, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.8}, {"curve": [0.245, 0, 0.637, 0.56], "x": 6.64, "y": -0.27, "time": 1.1333}, {"curve": [0.381, 0.55, 0.742, 1], "x": 2.44, "y": -0.1, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1.4667}, {"curve": [0.245, 0, 0.637, 0.56], "x": 6.64, "y": -0.27, "time": 1.8}, {"curve": [0.381, 0.55, 0.742, 1], "x": 2.44, "y": -0.1, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.1333}, {"curve": [0.245, 0, 0.637, 0.56], "x": 6.64, "y": -0.27, "time": 2.4667}, {"curve": [0.381, 0.55, 0.742, 1], "x": 2.44, "y": -0.1, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.8}, {"curve": [0.245, 0, 0.637, 0.56], "x": 6.64, "y": -0.27, "time": 3.1333}, {"x": 2.44, "y": -0.1, "time": 3.3333}]}, "TuiDo2": {"rotate": [{"angle": 0, "time": 0}, {"angle": 9.96, "time": 0.4}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 1.27, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.7333}, {"curve": [0.243, 0, 0.68, 0.71], "angle": 9.79, "time": 1.0667}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 1.27, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.4}, {"curve": [0.243, 0, 0.68, 0.71], "angle": 9.79, "time": 1.7333}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 1.27, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.0667}, {"curve": [0.243, 0, 0.68, 0.71], "angle": 9.79, "time": 2.4}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 1.27, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.7333}, {"curve": [0.243, 0, 0.68, 0.71], "angle": 9.79, "time": 3.0667}, {"angle": 1.27, "time": 3.3333}]}, "IK_VoChanTruoc_Trong": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -11.77, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": -71.72, "time": 0.3333}, {"curve": "stepped", "angle": -19.96, "time": 0.5333}, {"angle": -19.96, "time": 0.6667}, {"angle": 12.44, "time": 0.7333}, {"angle": 11.22, "time": 0.8333}, {"angle": -4.04, "time": 0.9}, {"angle": -12.13, "time": 1}, {"angle": -48.94, "time": 1.0667}, {"angle": -136.9, "time": 1.1667}, {"angle": -148.69, "time": 1.2333}, {"angle": -19.96, "time": 1.3333}, {"angle": 12.44, "time": 1.4}, {"angle": 11.22, "time": 1.5}, {"angle": -4.04, "time": 1.5667}, {"angle": -12.13, "time": 1.6667}, {"angle": -48.94, "time": 1.7333}, {"angle": -136.9, "time": 1.8333}, {"angle": -148.69, "time": 1.9}, {"angle": -19.96, "time": 2}, {"angle": 12.44, "time": 2.0667}, {"angle": 11.22, "time": 2.1667}, {"angle": -4.04, "time": 2.2333}, {"angle": -12.13, "time": 2.3333}, {"angle": -48.94, "time": 2.4}, {"angle": -136.9, "time": 2.5}, {"angle": -148.69, "time": 2.5667}, {"angle": -19.96, "time": 2.6667}, {"angle": 12.44, "time": 2.7333}, {"angle": 11.22, "time": 2.8333}, {"angle": -4.04, "time": 2.9}, {"angle": -12.13, "time": 3}, {"angle": -48.94, "time": 3.0667}, {"angle": -136.9, "time": 3.1667}, {"angle": -148.69, "time": 3.2333}, {"angle": -19.96, "time": 3.3333}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 1.26, "y": 0.42, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 52.06, "y": 118.05, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 115.7, "y": 136.76, "time": 0.5333}, {"x": 131.7, "y": 106.36, "time": 0.6667}, {"x": 104.8, "y": 39.01, "time": 0.7333}, {"x": 70.89, "y": 24.28, "time": 0.8333}, {"x": 50.09, "y": 23.8, "time": 0.9}, {"x": 3, "y": 21.23, "time": 1}, {"x": -86.8, "y": 48.1, "time": 1.0667}, {"x": -43.74, "y": 119.07, "time": 1.1667}, {"x": -7.82, "y": 94.08, "time": 1.2333}, {"x": 131.7, "y": 106.36, "time": 1.3333}, {"x": 104.8, "y": 39.01, "time": 1.4}, {"x": 70.89, "y": 24.28, "time": 1.5}, {"x": 50.09, "y": 23.8, "time": 1.5667}, {"x": 3, "y": 21.23, "time": 1.6667}, {"x": -86.8, "y": 48.1, "time": 1.7333}, {"x": -43.74, "y": 119.07, "time": 1.8333}, {"x": -7.82, "y": 94.08, "time": 1.9}, {"x": 131.7, "y": 106.36, "time": 2}, {"x": 104.8, "y": 39.01, "time": 2.0667}, {"x": 70.89, "y": 24.28, "time": 2.1667}, {"x": 50.09, "y": 23.8, "time": 2.2333}, {"x": 3, "y": 21.23, "time": 2.3333}, {"x": -86.8, "y": 48.1, "time": 2.4}, {"x": -43.74, "y": 119.07, "time": 2.5}, {"x": -7.82, "y": 94.08, "time": 2.5667}, {"x": 131.7, "y": 106.36, "time": 2.6667}, {"x": 104.8, "y": 39.01, "time": 2.7333}, {"x": 70.89, "y": 24.28, "time": 2.8333}, {"x": 50.09, "y": 23.8, "time": 2.9}, {"x": 3, "y": 21.23, "time": 3}, {"x": -86.8, "y": 48.1, "time": 3.0667}, {"x": -43.74, "y": 119.07, "time": 3.1667}, {"x": -7.82, "y": 94.08, "time": 3.2333}, {"x": 131.7, "y": 106.36, "time": 3.3333}]}, "ChanTrai": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.02, "time": 0.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.82, "time": 0.5333}, {"curve": [0.32, 0.29, 0.757, 1], "angle": 0.88, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.34, "time": 0.9333}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -0.83, "time": 1.2667}, {"curve": [0.32, 0.29, 0.757, 1], "angle": 0.88, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.34, "time": 1.6}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -0.83, "time": 1.9333}, {"curve": [0.32, 0.29, 0.757, 1], "angle": 0.88, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.34, "time": 2.2667}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -0.83, "time": 2.6}, {"curve": [0.32, 0.29, 0.757, 1], "angle": 0.88, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.34, "time": 2.9333}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -0.83, "time": 3.2667}, {"angle": 0.88, "time": 3.3333}]}, "IK_VoChanTruoc_Ngoai": {"rotate": [{"curve": [0.25, 0, 0.625, 0.5], "angle": -13.07, "time": 0}, {"curve": [0.246, 0, 0.633, 0.54], "angle": 0, "time": 0.1}, {"curve": [0.38, 0.53, 0.745, 1], "angle": -46.13, "time": 0.2333}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.66, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -20.67, "time": 0.5333}, {"angle": 10.24, "time": 0.6667}, {"angle": -4.26, "time": 0.7}, {"angle": -2.33, "time": 0.7667}, {"angle": -52.39, "time": 0.8333}, {"angle": -61.46, "time": 0.9333}, {"angle": -172.06, "time": 1}, {"angle": -86.03, "time": 1.1667}, {"angle": 18.99, "time": 1.2667}, {"angle": 1.08, "time": 1.3}, {"angle": 10.24, "time": 1.3333}, {"angle": -4.26, "time": 1.3667}, {"angle": -2.33, "time": 1.4333}, {"angle": -52.39, "time": 1.5}, {"angle": -61.46, "time": 1.6}, {"angle": -172.06, "time": 1.6667}, {"angle": -86.03, "time": 1.8333}, {"angle": 18.99, "time": 1.9333}, {"angle": 1.08, "time": 1.9667}, {"angle": 10.24, "time": 2}, {"angle": -4.26, "time": 2.0333}, {"angle": -2.33, "time": 2.1}, {"angle": -52.39, "time": 2.1667}, {"angle": -61.46, "time": 2.2667}, {"angle": -172.06, "time": 2.3333}, {"angle": -86.03, "time": 2.5}, {"angle": 18.99, "time": 2.6}, {"angle": 1.08, "time": 2.6333}, {"angle": 10.24, "time": 2.6667}, {"angle": -4.26, "time": 2.7}, {"angle": -2.33, "time": 2.7667}, {"angle": -52.39, "time": 2.8333}, {"angle": -61.46, "time": 2.9333}, {"angle": -172.06, "time": 3}, {"angle": -86.03, "time": 3.1667}, {"angle": 18.99, "time": 3.2667}, {"angle": 1.08, "time": 3.3}, {"angle": 10.24, "time": 3.3333}], "translate": [{"curve": [0.25, 0, 0.625, 0.5], "x": 46.94, "y": 15.12, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.1}, {"curve": [0.25, 0, 0.75, 1], "x": 69.42, "y": 183.96, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 115.75, "y": 96.77, "time": 0.5333}, {"x": 42.07, "y": 9.82, "time": 0.6667}, {"x": -1.95, "y": 3.33, "time": 0.7}, {"x": -40.77, "y": 10.72, "time": 0.7667}, {"x": -73.19, "y": 23.73, "time": 0.8333}, {"x": -122.01, "y": 56.54, "time": 0.9333}, {"x": -100.89, "y": 88.66, "time": 1}, {"x": 40.3, "y": 89.63, "time": 1.1667}, {"x": 87.99, "y": 35.32, "time": 1.2667}, {"x": 61.24, "y": 17.09, "time": 1.3}, {"x": 42.07, "y": 9.82, "time": 1.3333}, {"x": -1.95, "y": 3.33, "time": 1.3667}, {"x": -40.77, "y": 10.72, "time": 1.4333}, {"x": -73.19, "y": 23.73, "time": 1.5}, {"x": -122.01, "y": 56.54, "time": 1.6}, {"x": -100.89, "y": 88.66, "time": 1.6667}, {"x": 40.3, "y": 89.63, "time": 1.8333}, {"x": 87.99, "y": 35.32, "time": 1.9333}, {"x": 61.24, "y": 17.09, "time": 1.9667}, {"x": 42.07, "y": 9.82, "time": 2}, {"x": -1.95, "y": 3.33, "time": 2.0333}, {"x": -40.77, "y": 10.72, "time": 2.1}, {"x": -73.19, "y": 23.73, "time": 2.1667}, {"x": -122.01, "y": 56.54, "time": 2.2667}, {"x": -100.89, "y": 88.66, "time": 2.3333}, {"x": 40.3, "y": 89.63, "time": 2.5}, {"x": 87.99, "y": 35.32, "time": 2.6}, {"x": 61.24, "y": 17.09, "time": 2.6333}, {"x": 42.07, "y": 9.82, "time": 2.6667}, {"x": -1.95, "y": 3.33, "time": 2.7}, {"x": -40.77, "y": 10.72, "time": 2.7667}, {"x": -73.19, "y": 23.73, "time": 2.8333}, {"x": -122.01, "y": 56.54, "time": 2.9333}, {"x": -100.89, "y": 88.66, "time": 3}, {"x": 40.3, "y": 89.63, "time": 3.1667}, {"x": 87.99, "y": 35.32, "time": 3.2667}, {"x": 61.24, "y": 17.09, "time": 3.3}, {"x": 42.07, "y": 9.82, "time": 3.3333}]}, "Move": {"rotate": [{"angle": 0, "time": 0}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25.5, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25.5, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25.5, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25.5, "time": 3}, {"x": 0, "y": 0, "time": 3.3333}]}, "DauNgua4": {"translate": [{"x": 0, "y": 0, "time": 0}, {"x": -6.64, "y": -0.59, "time": 0.2333}, {"x": 13.37, "y": 4.69, "time": 0.4667}, {"curve": [0.363, 0.44, 0.755, 1], "x": 9.42, "y": 5.41, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.8667}, {"curve": [0.258, 0, 0.619, 0.45], "x": 14.91, "y": 8.56, "time": 1.2}, {"curve": [0.363, 0.44, 0.755, 1], "x": 9.42, "y": 5.41, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1.5333}, {"curve": [0.258, 0, 0.619, 0.45], "x": 14.91, "y": 8.56, "time": 1.8667}, {"curve": [0.363, 0.44, 0.755, 1], "x": 9.42, "y": 5.41, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.2}, {"curve": [0.258, 0, 0.619, 0.45], "x": 14.91, "y": 8.56, "time": 2.5333}, {"curve": [0.363, 0.44, 0.755, 1], "x": 9.42, "y": 5.41, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.8667}, {"curve": [0.258, 0, 0.619, 0.45], "x": 14.91, "y": 8.56, "time": 3.2}, {"x": 9.42, "y": 5.41, "time": 3.3333}]}, "DauNgua2": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 0.1}, {"angle": 12.53, "time": 0.3333}, {"curve": "stepped", "angle": -2.61, "time": 0.6}, {"curve": [0.32, 0.29, 0.757, 1], "angle": -2.61, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.9333}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -3, "time": 1.2667}, {"curve": [0.32, 0.29, 0.757, 1], "angle": -2.61, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.6}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -3, "time": 1.9333}, {"curve": [0.32, 0.29, 0.757, 1], "angle": -2.61, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.2667}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -3, "time": 2.6}, {"curve": [0.32, 0.29, 0.757, 1], "angle": -2.61, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.9333}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -3, "time": 3.2667}, {"angle": -2.61, "time": 3.3333}]}, "DauNgua": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.4, "time": 0.2333}, {"curve": "stepped", "angle": -1.89, "time": 0.5}, {"curve": [0.363, 0.44, 0.755, 1], "angle": -1.89, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.8667}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -3, "time": 1.2}, {"curve": [0.363, 0.44, 0.755, 1], "angle": -1.89, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.5333}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -3, "time": 1.8667}, {"curve": [0.363, 0.44, 0.755, 1], "angle": -1.89, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.2}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -3, "time": 2.5333}, {"curve": [0.363, 0.44, 0.755, 1], "angle": -1.89, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.8667}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -3, "time": 3.2}, {"angle": -1.89, "time": 3.3333}]}, "YenNgua5": {"translate": [{"curve": [0.382, 0.58, 0.731, 1], "x": -1.06, "y": 0.19, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.7667}, {"curve": [0.243, 0, 0.655, 0.63], "x": -4.37, "y": 0.79, "time": 1.1}, {"curve": [0.382, 0.58, 0.731, 1], "x": -1.06, "y": 0.19, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1.4333}, {"curve": [0.243, 0, 0.655, 0.63], "x": -4.37, "y": 0.79, "time": 1.7667}, {"curve": [0.382, 0.58, 0.731, 1], "x": -1.06, "y": 0.19, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.1}, {"curve": [0.243, 0, 0.655, 0.63], "x": -4.37, "y": 0.79, "time": 2.4333}, {"curve": [0.382, 0.58, 0.731, 1], "x": -1.06, "y": 0.19, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.7667}, {"curve": [0.243, 0, 0.655, 0.63], "x": -4.37, "y": 0.79, "time": 3.1}, {"x": -1.06, "y": 0.19, "time": 3.3333}]}, "Duoi": {"rotate": [{"angle": 20.99, "time": 0}]}, "Duoi2": {"rotate": [{"angle": 0, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": -3.05, "y": 2.22, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0.83, "y": 5.88, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": -4.93, "y": 7.19, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 0.83, "y": 5.88, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": -4.93, "y": 7.19, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0.83, "y": 5.88, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": -4.93, "y": 7.19, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0.83, "y": 5.88, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": -4.93, "y": 7.19, "time": 3}, {"x": 0.83, "y": 5.88, "time": 3.3333}]}, "Duoi8": {"rotate": [{"angle": -1.18, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 31.54, "y": -43.51, "time": 0.3333}, {"curve": [0.363, 0.44, 0.755, 1], "x": 32.49, "y": -37.69, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 35.23, "y": -49.5, "time": 0.8667}, {"curve": [0.258, 0, 0.619, 0.45], "x": 30.9, "y": -30.81, "time": 1.2}, {"curve": [0.363, 0.44, 0.755, 1], "x": 32.49, "y": -37.69, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 35.23, "y": -49.5, "time": 1.5333}, {"curve": [0.258, 0, 0.619, 0.45], "x": 30.9, "y": -30.81, "time": 1.8667}, {"curve": [0.363, 0.44, 0.755, 1], "x": 32.49, "y": -37.69, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 35.23, "y": -49.5, "time": 2.2}, {"curve": [0.258, 0, 0.619, 0.45], "x": 30.9, "y": -30.81, "time": 2.5333}, {"curve": [0.363, 0.44, 0.755, 1], "x": 32.49, "y": -37.69, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 35.23, "y": -49.5, "time": 2.8667}, {"curve": [0.258, 0, 0.619, 0.45], "x": 30.9, "y": -30.81, "time": 3.2}, {"x": 32.49, "y": -37.69, "time": 3.3333}]}, "TuiDo3": {"rotate": [{"angle": 0, "time": 0}, {"angle": 11.36, "time": 0.4}, {"curve": [0.381, 0.55, 0.742, 1], "angle": 3.6, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.8}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 9.79, "time": 1.1333}, {"curve": [0.381, 0.55, 0.742, 1], "angle": 3.6, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.4667}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 9.79, "time": 1.8}, {"curve": [0.381, 0.55, 0.742, 1], "angle": 3.6, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.1333}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 9.79, "time": 2.4667}, {"curve": [0.381, 0.55, 0.742, 1], "angle": 3.6, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.8}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 9.79, "time": 3.1333}, {"angle": 3.6, "time": 3.3333}]}, "Duoi7": {"rotate": [{"angle": 0, "time": 0}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0}, {"x": 29.75, "y": -38.1, "time": 0.3333}, {"curve": [0.375, 0.5, 0.75, 1], "x": 27.17, "y": -32.74, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 31.35, "y": -46.59, "time": 0.8333}, {"curve": [0.25, 0, 0.625, 0.5], "x": 23, "y": -18.9, "time": 1.1667}, {"curve": [0.375, 0.5, 0.75, 1], "x": 27.17, "y": -32.74, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 31.35, "y": -46.59, "time": 1.5}, {"curve": [0.25, 0, 0.625, 0.5], "x": 23, "y": -18.9, "time": 1.8333}, {"curve": [0.375, 0.5, 0.75, 1], "x": 27.17, "y": -32.74, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 31.35, "y": -46.59, "time": 2.1667}, {"curve": [0.25, 0, 0.625, 0.5], "x": 23, "y": -18.9, "time": 2.5}, {"curve": [0.375, 0.5, 0.75, 1], "x": 27.17, "y": -32.74, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 31.35, "y": -46.59, "time": 2.8333}, {"curve": [0.25, 0, 0.625, 0.5], "x": 23, "y": -18.9, "time": 3.1667}, {"x": 27.17, "y": -32.74, "time": 3.3333}]}, "TuiDo4": {"rotate": [{"angle": 0, "time": 0}, {"angle": 12.91, "time": 0.4}, {"curve": [0.363, 0.44, 0.755, 1], "angle": 6.19, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.8667}, {"curve": [0.258, 0, 0.619, 0.45], "angle": 9.79, "time": 1.2}, {"curve": [0.363, 0.44, 0.755, 1], "angle": 6.19, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.5333}, {"curve": [0.258, 0, 0.619, 0.45], "angle": 9.79, "time": 1.8667}, {"curve": [0.363, 0.44, 0.755, 1], "angle": 6.19, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.2}, {"curve": [0.258, 0, 0.619, 0.45], "angle": 9.79, "time": 2.5333}, {"curve": [0.363, 0.44, 0.755, 1], "angle": 6.19, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.8667}, {"curve": [0.258, 0, 0.619, 0.45], "angle": 9.79, "time": 3.2}, {"angle": 6.19, "time": 3.3333}]}, "Bong": {"scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 1.107, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1.107, "y": 1, "time": 1.6667}, {"x": 1, "y": 1, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 1.107, "y": 1, "time": 2.3333}, {"x": 1, "y": 1, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 1.107, "y": 1, "time": 3}, {"x": 1, "y": 1, "time": 3.3333}]}, "Duoi9": {"rotate": [{"angle": -0.15, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 29.87, "y": -41.46, "time": 0.3333}, {"curve": [0.345, 0.37, 0.757, 1], "x": 33.68, "y": -47.32, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 35.43, "y": -53.08, "time": 0.9}, {"curve": [0.269, 0, 0.618, 0.42], "x": 33.12, "y": -45.48, "time": 1.2333}, {"curve": [0.345, 0.37, 0.757, 1], "x": 33.68, "y": -47.32, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 35.43, "y": -53.08, "time": 1.5667}, {"curve": [0.269, 0, 0.618, 0.42], "x": 33.12, "y": -45.48, "time": 1.9}, {"curve": [0.345, 0.37, 0.757, 1], "x": 33.68, "y": -47.32, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 35.43, "y": -53.08, "time": 2.2333}, {"curve": [0.269, 0, 0.618, 0.42], "x": 33.12, "y": -45.48, "time": 2.5667}, {"curve": [0.345, 0.37, 0.757, 1], "x": 33.68, "y": -47.32, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 35.43, "y": -53.08, "time": 2.9}, {"curve": [0.269, 0, 0.618, 0.42], "x": 33.12, "y": -45.48, "time": 3.2333}, {"x": 33.68, "y": -47.32, "time": 3.3333}]}, "Duoi4": {"rotate": [{"angle": 0, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 12.75, "y": -18.42, "time": 0.3333}, {"curve": [0.375, 0.62, 0.716, 1], "x": 8.75, "y": -15.35, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 9.95, "y": -18.45, "time": 0.7333}, {"curve": [0.243, 0, 0.68, 0.71], "x": 0.72, "y": 5.4, "time": 1.0667}, {"curve": [0.375, 0.62, 0.716, 1], "x": 8.75, "y": -15.35, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 9.95, "y": -18.45, "time": 1.4}, {"curve": [0.243, 0, 0.68, 0.71], "x": 0.72, "y": 5.4, "time": 1.7333}, {"curve": [0.375, 0.62, 0.716, 1], "x": 8.75, "y": -15.35, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 9.95, "y": -18.45, "time": 2.0667}, {"curve": [0.243, 0, 0.68, 0.71], "x": 0.72, "y": 5.4, "time": 2.4}, {"curve": [0.375, 0.62, 0.716, 1], "x": 8.75, "y": -15.35, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 9.95, "y": -18.45, "time": 2.7333}, {"curve": [0.243, 0, 0.68, 0.71], "x": 0.72, "y": 5.4, "time": 3.0667}, {"x": 8.75, "y": -15.35, "time": 3.3333}]}, "Duoi3": {"rotate": [{"angle": 0, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 3.7, "y": -8.89, "time": 0.3333}, {"curve": [0.36, 0.64, 0.695, 1], "x": 5.53, "y": -6.77, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 5.89, "y": -7.41, "time": 0.7}, {"curve": [0.245, 0, 0.711, 0.83], "x": -2.79, "y": 7.72, "time": 1.0333}, {"curve": [0.36, 0.64, 0.695, 1], "x": 5.53, "y": -6.77, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 5.89, "y": -7.41, "time": 1.3667}, {"curve": [0.245, 0, 0.711, 0.83], "x": -2.79, "y": 7.72, "time": 1.7}, {"curve": [0.36, 0.64, 0.695, 1], "x": 5.53, "y": -6.77, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 5.89, "y": -7.41, "time": 2.0333}, {"curve": [0.245, 0, 0.711, 0.83], "x": -2.79, "y": 7.72, "time": 2.3667}, {"curve": [0.36, 0.64, 0.695, 1], "x": 5.53, "y": -6.77, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 5.89, "y": -7.41, "time": 2.7}, {"curve": [0.245, 0, 0.711, 0.83], "x": -2.79, "y": 7.72, "time": 3.0333}, {"x": 5.53, "y": -6.77, "time": 3.3333}]}, "IK_VoChanSau_Trong": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -46.85, "time": 0}, {"angle": 0, "time": 0.5333}, {"angle": -22.22, "time": 0.6}, {"angle": 0, "time": 0.6667}, {"angle": -3.03, "time": 0.7667}, {"angle": 1.99, "time": 0.8333}, {"angle": -81.87, "time": 0.9333}, {"angle": -14.86, "time": 1}, {"angle": 27.81, "time": 1.1}, {"angle": 5.95, "time": 1.1667}, {"angle": 2.38, "time": 1.2667}, {"angle": 0, "time": 1.3333}, {"angle": -3.03, "time": 1.4333}, {"angle": 1.99, "time": 1.5}, {"angle": -81.87, "time": 1.6}, {"angle": -14.86, "time": 1.6667}, {"angle": 27.81, "time": 1.7667}, {"angle": 5.95, "time": 1.8333}, {"angle": 2.38, "time": 1.9333}, {"angle": 0, "time": 2}, {"angle": -3.03, "time": 2.1}, {"angle": 1.99, "time": 2.1667}, {"angle": -81.87, "time": 2.2667}, {"angle": -14.86, "time": 2.3333}, {"angle": 27.81, "time": 2.4333}, {"angle": 5.95, "time": 2.5}, {"angle": 2.38, "time": 2.6}, {"angle": 0, "time": 2.6667}, {"angle": -3.03, "time": 2.7667}, {"angle": 1.99, "time": 2.8333}, {"angle": -81.87, "time": 2.9333}, {"angle": -14.86, "time": 3}, {"angle": 27.81, "time": 3.1}, {"angle": 5.95, "time": 3.1667}, {"angle": 2.38, "time": 3.2667}, {"angle": 0, "time": 3.3333}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 18.71, "y": 18.68, "time": 0}, {"x": 0, "y": 0, "time": 0.5333}, {"x": 5.44, "y": 61.4, "time": 0.6}, {"x": 37.19, "y": 19.95, "time": 0.6667}, {"x": 5.59, "y": 18.71, "time": 0.7667}, {"x": -61.28, "y": 38.94, "time": 0.8333}, {"x": -117.85, "y": 82.06, "time": 0.9333}, {"x": 6.21, "y": 108.11, "time": 1}, {"x": 113.28, "y": 72.37, "time": 1.1}, {"x": 127.55, "y": 56.37, "time": 1.1667}, {"x": 73.33, "y": 34.52, "time": 1.2667}, {"x": 37.19, "y": 19.95, "time": 1.3333}, {"x": 5.59, "y": 18.71, "time": 1.4333}, {"x": -61.28, "y": 38.94, "time": 1.5}, {"x": -117.85, "y": 82.06, "time": 1.6}, {"x": 6.21, "y": 108.11, "time": 1.6667}, {"x": 113.28, "y": 72.37, "time": 1.7667}, {"x": 127.55, "y": 56.37, "time": 1.8333}, {"x": 73.33, "y": 34.52, "time": 1.9333}, {"x": 37.19, "y": 19.95, "time": 2}, {"x": 5.59, "y": 18.71, "time": 2.1}, {"x": -61.28, "y": 38.94, "time": 2.1667}, {"x": -117.85, "y": 82.06, "time": 2.2667}, {"x": 6.21, "y": 108.11, "time": 2.3333}, {"x": 113.28, "y": 72.37, "time": 2.4333}, {"x": 127.55, "y": 56.37, "time": 2.5}, {"x": 73.33, "y": 34.52, "time": 2.6}, {"x": 37.19, "y": 19.95, "time": 2.6667}, {"x": 5.59, "y": 18.71, "time": 2.7667}, {"x": -61.28, "y": 38.94, "time": 2.8333}, {"x": -117.85, "y": 82.06, "time": 2.9333}, {"x": 6.21, "y": 108.11, "time": 3}, {"x": 113.28, "y": 72.37, "time": 3.1}, {"x": 127.55, "y": 56.37, "time": 3.1667}, {"x": 73.33, "y": 34.52, "time": 3.2667}, {"x": 37.19, "y": 19.95, "time": 3.3333}]}, "Duoi6": {"rotate": [{"angle": 0, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 24.02, "y": -31.61, "time": 0.3333}, {"curve": [0.381, 0.55, 0.742, 1], "x": 21.01, "y": -28.47, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 25.01, "y": -41.1, "time": 0.8}, {"curve": [0.245, 0, 0.637, 0.56], "x": 14.13, "y": -6.76, "time": 1.1333}, {"curve": [0.381, 0.55, 0.742, 1], "x": 21.01, "y": -28.47, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 25.01, "y": -41.1, "time": 1.4667}, {"curve": [0.245, 0, 0.637, 0.56], "x": 14.13, "y": -6.76, "time": 1.8}, {"curve": [0.381, 0.55, 0.742, 1], "x": 21.01, "y": -28.47, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 25.01, "y": -41.1, "time": 2.1333}, {"curve": [0.245, 0, 0.637, 0.56], "x": 14.13, "y": -6.76, "time": 2.4667}, {"curve": [0.381, 0.55, 0.742, 1], "x": 21.01, "y": -28.47, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 25.01, "y": -41.1, "time": 2.8}, {"curve": [0.245, 0, 0.637, 0.56], "x": 14.13, "y": -6.76, "time": 3.1333}, {"x": 21.01, "y": -28.47, "time": 3.3333}]}, "Duoi5": {"rotate": [{"angle": 0, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 16.83, "y": -26.91, "time": 0.3333}, {"curve": [0.382, 0.58, 0.731, 1], "x": 13, "y": -23.43, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 15.6, "y": -31.37, "time": 0.7667}, {"curve": [0.243, 0, 0.655, 0.63], "x": 4.87, "y": 1.42, "time": 1.1}, {"curve": [0.382, 0.58, 0.731, 1], "x": 13, "y": -23.43, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 15.6, "y": -31.37, "time": 1.4333}, {"curve": [0.243, 0, 0.655, 0.63], "x": 4.87, "y": 1.42, "time": 1.7667}, {"curve": [0.382, 0.58, 0.731, 1], "x": 13, "y": -23.43, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 15.6, "y": -31.37, "time": 2.1}, {"curve": [0.243, 0, 0.655, 0.63], "x": 4.87, "y": 1.42, "time": 2.4333}, {"curve": [0.382, 0.58, 0.731, 1], "x": 13, "y": -23.43, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 15.6, "y": -31.37, "time": 2.7667}, {"curve": [0.243, 0, 0.655, 0.63], "x": 4.87, "y": 1.42, "time": 3.1}, {"x": 13, "y": -23.43, "time": 3.3333}]}}, "deform": {"default": {"dust2": {"dust": [{"time": 1}, {"vertices": [-87.84035, 24.11005, -34.64826, 21.18204, -41.96827, -24.20193, -99.55228, -9.07394, -41.20419, 8.69379, -35.29206, 16.11383, -51.22389, 13.95364, -48.12944, 19.68262, -50.71693, 16.42253, -51.12024, 7.74536, -57.46804, 2.94844, -53.81874, -1.51173, -71.35097, 16.45085, -76.7865, 21.73021, -84.89554, 24.35208, -83.06694, 16.21475, -41.74913, -8.84407], "time": 1.2667}, {"vertices": [-213.9578, 49.9147, -56.07459, 49.96111, -55.95857, -42.26979, -213.77219, -42.50955, -83.30888, 13.20592, -93.75607, 26.76971, -109.77335, 17.18999, -127.94105, 30.04108, -134.55594, 18.2601, -127.52389, 1.01854, -111.15347, -4.8217, -99.7485, -16.5836, -170.4661, 20.4783, -183.32178, 30.00865, -191.6979, 18.50424, -179.15482, 7.90994, -71.46764, -12.76968], "time": 1.5667}, {"time": 2.1667}, {"vertices": [-87.84035, 24.11005, -34.64826, 21.18204, -41.96827, -24.20193, -99.55228, -9.07394, -41.20419, 8.69379, -35.29206, 16.11383, -51.22389, 13.95364, -48.12944, 19.68262, -50.71693, 16.42253, -51.12024, 7.74536, -57.46804, 2.94844, -53.81874, -1.51173, -71.35097, 16.45085, -76.7865, 21.73021, -84.89554, 24.35208, -83.06694, 16.21475, -41.74913, -8.84407], "time": 2.4333}, {"vertices": [-213.9578, 49.9147, -56.07459, 49.96111, -55.95857, -42.26979, -213.77219, -42.50955, -83.30888, 13.20592, -93.75607, 26.76971, -109.77335, 17.18999, -127.94105, 30.04108, -134.55594, 18.2601, -127.52389, 1.01854, -111.15347, -4.8217, -99.7485, -16.5836, -170.4661, 20.4783, -183.32178, 30.00865, -191.6979, 18.50424, -179.15482, 7.90994, -71.46764, -12.76968], "time": 2.7333}]}, "dust3": {"dust": [{"time": 1.4333}, {"vertices": [-43.46499, 5.20752, -29.26479, -10.88641, -35.89159, -8.5198, -68.55206, 3.78751, -41.09832, -14.19963, -39.20492, -8.04585, -47.25176, -0.94608, -49.14498, -8.51858, -50.56505, 10.41459, -53.87846, 6.62787, -56.71852, -8.99292, -56.2452, -3.31272, -50.56502, 4.73509, -51.98513, 6.15408, -38.7316, 7.10085, -55.77181, 9.94141, -42.99169, -17.03973], "time": 1.6}, {"vertices": [-106.10681, 4.73066, -36.82115, -10.50385, -63.07483, -37.90125, -118.29201, 4.68147, -43.85048, -19.20573, -45.98512, -13.20567, -56.23684, -9.21252, -56.92912, -4.43013, -64.24998, -1.06326, -73.64676, -8.79219, -64.84772, -18.79771, -62.14511, -15.68444, -89.1441, -0.08829, -92.68629, 12.29096, -102.68872, 13.96674, -103.22678, -0.79648, -46.28876, -35.79694], "time": 1.8}, {"vertices": [-204.01752, 26.89085, -46.13425, 26.93726, -46.01823, -65.29364, -203.83191, -65.5334, -73.36854, -9.81793, -83.81577, 3.74586, -99.83304, -5.83386, -118.00072, 7.01723, -124.61568, -4.76375, -117.58357, -22.00531, -101.21316, -27.84555, -89.80818, -39.60745, -160.52582, -2.54555, -173.3815, 6.9848, -181.75763, -4.51961, -169.21454, -15.11391, -61.52729, -35.79353], "time": 2.2}]}, "dust4": {"dust": [{"time": 1.1333}, {"vertices": [-52.77102, 1.72142, -40.02659, -4.52197, -49.78658, -5.98598, -44.90662, -1.59401, -53.70322, 2.0972, -51.85131, -7.08105, -59.56562, 3.56119, -57.05081, -6.30565, -62.91919, 4.7305, -49.80572, 4.92493, -54.29129, 2.59155, -46.8586, 8.166, -61.6431, 5.41821, -43.07431, -1.91365, -45.40737, 10.37445, -44.70687, 20.64722, -44.98818, 10.01154], "time": 1.3333}, {"vertices": [-122.25154, 10.61207, -75.8007, 7.1315, -83.52739, -30.93249, -130.70068, -17.59383, -92.3876, -8.93551, -93.16704, -6.90324, -93.39052, -2.22712, -102.87836, -4.29976, -97.68886, -3.09613, -107.2845, -5.0924, -103.95767, -8.43833, -92.91478, -9.66532, -114.67119, -0.87709, -111.42387, 8.02303, -118.57025, 12.25594, -122.13229, 7.70691, -82.54512, -10.26582], "time": 1.5333}, {"vertices": [-245.77676, 15.98849, -87.89369, 16.0349, -87.77768, -76.196, -245.59116, -76.43576, -115.12798, -20.72029, -125.57518, -7.15649, -141.5924, -16.73622, -159.76004, -3.88513, -166.375, -15.66611, -159.3429, -32.90767, -142.97253, -38.74791, -131.5676, -50.50981, -202.28503, -13.44791, -215.14075, -3.91756, -223.51688, -15.42197, -210.97375, -26.01627, -103.28674, -46.69589], "time": 1.8}, {"time": 2.2333}, {"vertices": [-52.77102, 1.72142, -40.02659, -4.52197, -49.78658, -5.98598, -44.90662, -1.59401, -53.70322, 2.0972, -51.85131, -7.08105, -59.56562, 3.56119, -57.05081, -6.30565, -62.91919, 4.7305, -49.80572, 4.92493, -54.29129, 2.59155, -46.8586, 8.166, -61.6431, 5.41821, -43.07431, -1.91365, -45.40737, 10.37445, -44.70687, 20.64722, -44.98818, 10.01154], "time": 2.4333}, {"vertices": [-122.25154, 10.61207, -75.8007, 7.1315, -83.52739, -30.93249, -130.70068, -17.59383, -92.3876, -8.93551, -93.16704, -6.90324, -93.39052, -2.22712, -102.87836, -4.29976, -97.68886, -3.09613, -107.2845, -5.0924, -103.95767, -8.43833, -92.91478, -9.66532, -114.67119, -0.87709, -111.42387, 8.02303, -118.57025, 12.25594, -122.13229, 7.70691, -82.54512, -10.26582], "time": 2.6333}, {"vertices": [-245.77676, 15.98849, -87.89369, 16.0349, -87.77768, -76.196, -245.59116, -76.43576, -115.12798, -20.72029, -125.57518, -7.15649, -141.5924, -16.73622, -159.76004, -3.88513, -166.375, -15.66611, -159.3429, -32.90767, -142.97253, -38.74791, -131.5676, -50.50981, -202.28503, -13.44791, -215.14075, -3.91756, -223.51688, -15.42197, -210.97375, -26.01627, -103.28674, -46.69589], "time": 2.9}]}, "dust5": {"dust": [{"time": 1.1}, {"vertices": [-51.11044, -1.71819, -73.55838, -9.52615, -83.31837, -10.99016, -78.43841, -6.59819, -79.41441, 8.04182, -75.99841, 1.20979, -81.36642, 9.50583, -64.77441, 5.11382, -62.82243, 11.45782, -71.60641, 12.43381, -78.43834, 4.62587, -80.3904, 3.16182, -66.2384, 9.01787, -63.31042, 9.50584, -71.11842, 28.0498, -81.36644, 22.68178, -88.68643, 10.48181], "time": 1.3}, {"vertices": [-166.18073, 8.59418, -112.98862, 5.66617, -120.30864, -39.7178, -177.89264, -24.58981, -119.54456, -6.82208, -113.63242, 0.59796, -129.56425, -1.56223, -126.4698, 4.16675, -129.0573, 0.90666, -129.4606, -7.77051, -135.80841, -12.56743, -132.1591, -17.0276, -149.69133, 0.93498, -155.12686, 6.21434, -163.2359, 8.83621, -161.40732, 0.69888, -120.08951, -24.35994], "time": 1.5}, {"vertices": [-286.86642, 31.56308, -128.98315, 31.60948, -128.86714, -60.62141, -286.68082, -60.86118, -156.21744, -5.14571, -166.66464, 8.41809, -182.68192, -1.16164, -200.84961, 11.68945, -207.46457, -0.09152, -200.43246, -17.33308, -184.06204, -23.17332, -172.65707, -34.93523, -243.37473, 2.12668, -256.2304, 11.65703, -264.60654, 0.15262, -252.06343, -10.44168, -144.3762, -31.12131], "time": 1.7333}, {"time": 2.3}, {"vertices": [-51.11044, -1.71819, -73.55838, -9.52615, -83.31837, -10.99016, -78.43841, -6.59819, -79.41441, 8.04182, -75.99841, 1.20979, -81.36642, 9.50583, -64.77441, 5.11382, -62.82243, 11.45782, -71.60641, 12.43381, -78.43834, 4.62587, -80.3904, 3.16182, -66.2384, 9.01787, -63.31042, 9.50584, -71.11842, 28.0498, -81.36644, 22.68178, -88.68643, 10.48181], "time": 2.5}, {"vertices": [-166.18073, 8.59418, -112.98862, 5.66617, -120.30864, -39.7178, -177.89264, -24.58981, -119.54456, -6.82208, -113.63242, 0.59796, -129.56425, -1.56223, -126.4698, 4.16675, -129.0573, 0.90666, -129.4606, -7.77051, -135.80841, -12.56743, -132.1591, -17.0276, -149.69133, 0.93498, -155.12686, 6.21434, -163.2359, 8.83621, -161.40732, 0.69888, -120.08951, -24.35994], "time": 2.7}, {"vertices": [-286.86642, 31.56308, -128.98315, 31.60948, -128.86714, -60.62141, -286.68082, -60.86118, -156.21744, -5.14571, -166.66464, 8.41809, -182.68192, -1.16164, -200.84961, 11.68945, -207.46457, -0.09152, -200.43246, -17.33308, -184.06204, -23.17332, -172.65707, -34.93523, -243.37473, 2.12668, -256.2304, 11.65703, -264.60654, 0.15262, -252.06343, -10.44168, -144.3762, -31.12131], "time": 2.9333}]}, "TayPhai": {"TayPhai": [{"offset": 2, "vertices": [-35.72232, 2.66045, -31.33678, 2.3339, -31.58154, 2.35211, -34.35534, 2.55845], "time": 0}]}, "dust6": {"dust": [{"time": 1.2667}, {"vertices": [-26.46455, 10.04489, -19.85011, -3.34967, -38.55199, -2.57779, -29.20149, -4.3349, -28.50072, 0.24571, -26.11776, -2.35831, -39.39462, 2.26878, -30.06876, 0.66924, -34.26364, 7.01521, -40.25375, 9.10791, -43.17313, -2.61098, -45.12556, 4.8656, -45.50452, 8.48607, -50.95956, 11.20988, -58.76801, 23.04555, -63.42694, 16.00203, -28.27174, -10.16833], "time": 1.5}, {"vertices": [-101.5842, 20.10468, -34.42014, 18.85335, -43.97563, -31.00145, -101.55964, -15.87346, -44.88823, 8.0417, -52.38935, 5.40096, -57.70256, 6.03552, -69.13863, 8.97072, -64.46082, 5.71049, -65.98194, 2.62195, -57.79884, 0.61971, -55.8261, -8.31125, -83.41816, 9.65109, -78.79385, 14.93069, -86.9029, 17.55257, -85.07436, 12.76828, -44.87432, -10.05495], "time": 1.7}, {"vertices": [-201.66187, 36.03452, -43.7788, 36.08092, -43.66278, -56.14998, -201.47626, -56.38974, -71.01308, -0.67427, -81.46029, 12.88953, -97.47756, 3.3098, -115.64525, 16.16089, -122.26022, 4.37991, -115.2281, -12.86165, -98.85768, -18.70189, -87.45271, -30.46379, -158.17032, 6.59811, -171.02585, 16.12846, -179.40198, 4.62405, -166.85901, -5.97025, -59.17184, -26.64987], "time": 2}, {"time": 2.5667}, {"vertices": [-26.46455, 10.04489, -19.85011, -3.34967, -38.55199, -2.57779, -29.20149, -4.3349, -28.50072, 0.24571, -26.11776, -2.35831, -39.39462, 2.26878, -30.06876, 0.66924, -34.26364, 7.01521, -40.25375, 9.10791, -43.17313, -2.61098, -45.12556, 4.8656, -45.50452, 8.48607, -50.95956, 11.20988, -58.76801, 23.04555, -63.42694, 16.00203, -28.27174, -10.16833], "time": 2.8}, {"vertices": [-101.5842, 20.10468, -34.42014, 18.85335, -43.97563, -31.00145, -101.55964, -15.87346, -44.88823, 8.0417, -52.38935, 5.40096, -57.70256, 6.03552, -69.13863, 8.97072, -64.46082, 5.71049, -65.98194, 2.62195, -57.79884, 0.61971, -55.8261, -8.31125, -83.41816, 9.65109, -78.79385, 14.93069, -86.9029, 17.55257, -85.07436, 12.76828, -44.87432, -10.05495], "time": 3}, {"vertices": [-201.66187, 36.03452, -43.7788, 36.08092, -43.66278, -56.14998, -201.47626, -56.38974, -71.01308, -0.67427, -81.46029, 12.88953, -97.47756, 3.3098, -115.64525, 16.16089, -122.26022, 4.37991, -115.2281, -12.86165, -98.85768, -18.70189, -87.45271, -30.46379, -158.17032, 6.59811, -171.02585, 16.12846, -179.40198, 4.62405, -166.85901, -5.97025, -59.17184, -26.64987], "time": 3.3}]}, "Day": {"Day": [{"offset": 2, "vertices": [0.08816, -0.2628, -0.48417, 1.44429, -0.53796, 1.60505, -0.80404, 2.39859, -1.07206, 3.19807, -0.759, 2.26415, 0, 0, 0, 0, -0.32066, 0.95669, -0.7021, 2.09446, -1.09897, 3.27821, -1.146, 3.41855, -1.26741, 3.78075, -1.10969, 3.30995], "curve": [0.25, 0, 0.75, 1], "time": 0}, {"curve": [0.25, 0, 0.75, 1], "time": 0.6667}, {"offset": 2, "vertices": [-1.1897, 3.54887, -1.56541, 4.66956, -1.81581, 5.41671, -1.68871, 5.03744, -1.07206, 3.19807, -0.759, 2.26415, 0, 0, 0, 0, -0.32066, 0.95669, -0.70205, 2.09445, -1.68867, 5.03745, -2.12897, 6.3506, -2.25041, 6.7128, -1.50288, 4.48277], "curve": [0.25, 0, 0.75, 1], "time": 0.8333}, {"offset": 2, "vertices": [-1.56541, 4.66957, -1.63689, 4.88292, -1.88728, 5.63007, -1.63689, 4.88292, -1.51162, 4.50936, -1.25235, 3.73564, 0, 0, 0, 0, -0.81401, 2.42818, -1.13593, 3.38865, -1.63689, 4.88292, -2.20044, 6.56396, -2.77897, 8.28957, -1.87856, 5.60347], "curve": [0.25, 0, 0.75, 1], "time": 1}, {"offset": 2, "vertices": [-1.1897, 3.54887, -1.56541, 4.66956, -1.81581, 5.41671, -1.68871, 5.03744, -1.07206, 3.19807, -0.759, 2.26415, 0, 0, 0, 0, -0.32066, 0.95669, -0.70205, 2.09445, -1.68867, 5.03745, -2.12897, 6.3506, -2.25041, 6.7128, -1.50288, 4.48277], "curve": [0.25, 0, 0.75, 1], "time": 1.1667}, {"curve": [0.25, 0, 0.75, 1], "time": 1.3333}, {"offset": 2, "vertices": [-1.1897, 3.54887, -1.56541, 4.66956, -1.81581, 5.41671, -1.68871, 5.03744, -1.07206, 3.19807, -0.759, 2.26415, 0, 0, 0, 0, -0.32066, 0.95669, -0.70205, 2.09445, -1.68867, 5.03745, -2.12897, 6.3506, -2.25041, 6.7128, -1.50288, 4.48277], "curve": [0.25, 0, 0.75, 1], "time": 1.5}, {"offset": 2, "vertices": [-1.56541, 4.66957, -1.63689, 4.88292, -1.88728, 5.63007, -1.63689, 4.88292, -1.51162, 4.50936, -1.25235, 3.73564, 0, 0, 0, 0, -0.81401, 2.42818, -1.13593, 3.38865, -1.63689, 4.88292, -2.20044, 6.56396, -2.77897, 8.28957, -1.87856, 5.60347], "curve": [0.25, 0, 0.75, 1], "time": 1.6667}, {"offset": 2, "vertices": [-1.1897, 3.54887, -1.56541, 4.66956, -1.81581, 5.41671, -1.68871, 5.03744, -1.07206, 3.19807, -0.759, 2.26415, 0, 0, 0, 0, -0.32066, 0.95669, -0.70205, 2.09445, -1.68867, 5.03745, -2.12897, 6.3506, -2.25041, 6.7128, -1.50288, 4.48277], "curve": [0.25, 0, 0.75, 1], "time": 1.8333}, {"curve": [0.25, 0, 0.75, 1], "time": 2}, {"offset": 2, "vertices": [-1.1897, 3.54887, -1.56541, 4.66956, -1.81581, 5.41671, -1.68871, 5.03744, -1.07206, 3.19807, -0.759, 2.26415, 0, 0, 0, 0, -0.32066, 0.95669, -0.70205, 2.09445, -1.68867, 5.03745, -2.12897, 6.3506, -2.25041, 6.7128, -1.50288, 4.48277], "curve": [0.25, 0, 0.75, 1], "time": 2.1667}, {"offset": 2, "vertices": [-1.56541, 4.66957, -1.63689, 4.88292, -1.88728, 5.63007, -1.63689, 4.88292, -1.51162, 4.50936, -1.25235, 3.73564, 0, 0, 0, 0, -0.81401, 2.42818, -1.13593, 3.38865, -1.63689, 4.88292, -2.20044, 6.56396, -2.77897, 8.28957, -1.87856, 5.60347], "curve": [0.25, 0, 0.75, 1], "time": 2.3333}, {"offset": 2, "vertices": [-1.1897, 3.54887, -1.56541, 4.66956, -1.81581, 5.41671, -1.68871, 5.03744, -1.07206, 3.19807, -0.759, 2.26415, 0, 0, 0, 0, -0.32066, 0.95669, -0.70205, 2.09445, -1.68867, 5.03745, -2.12897, 6.3506, -2.25041, 6.7128, -1.50288, 4.48277], "curve": [0.25, 0, 0.75, 1], "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "time": 2.6667}, {"offset": 2, "vertices": [-1.1897, 3.54887, -1.56541, 4.66956, -1.81581, 5.41671, -1.68871, 5.03744, -1.07206, 3.19807, -0.759, 2.26415, 0, 0, 0, 0, -0.32066, 0.95669, -0.70205, 2.09445, -1.68867, 5.03745, -2.12897, 6.3506, -2.25041, 6.7128, -1.50288, 4.48277], "curve": [0.25, 0, 0.75, 1], "time": 2.8333}, {"offset": 2, "vertices": [-1.56541, 4.66957, -1.63689, 4.88292, -1.88728, 5.63007, -1.63689, 4.88292, -1.51162, 4.50936, -1.25235, 3.73564, 0, 0, 0, 0, -0.81401, 2.42818, -1.13593, 3.38865, -1.63689, 4.88292, -2.20044, 6.56396, -2.77897, 8.28957, -1.87856, 5.60347], "curve": [0.25, 0, 0.75, 1], "time": 3}, {"offset": 2, "vertices": [-1.1897, 3.54887, -1.56541, 4.66956, -1.81581, 5.41671, -1.68871, 5.03744, -1.07206, 3.19807, -0.759, 2.26415, 0, 0, 0, 0, -0.32066, 0.95669, -0.70205, 2.09445, -1.68867, 5.03745, -2.12897, 6.3506, -2.25041, 6.7128, -1.50288, 4.48277], "curve": [0.25, 0, 0.75, 1], "time": 3.1667}, {"time": 3.3333}]}, "dust": {"dust": [{"time": 0.7667}, {"vertices": [-42.27502, 0.56096, -64.72296, -7.24701, -74.48296, -8.71101, -69.60299, -4.31905, -70.57899, 10.32097, -67.16299, 3.48894, -72.53101, 11.78497, -55.939, 7.39296, -53.98701, 13.73697, -62.771, 14.71295, -69.60292, 6.90501, -71.55498, 5.44096, -57.40298, 11.29701, -54.47501, 11.78499, -62.283, 30.32895, -72.53102, 24.96092, -79.85101, 12.76096], "time": 0.9}, {"vertices": [-137.40009, 25.82303, -84.20798, 22.89502, -91.528, -22.48895, -149.112, -7.36096, -90.76392, 10.40677, -84.85178, 17.82681, -100.78362, 15.66663, -97.68916, 21.3956, -100.27666, 18.13551, -100.67996, 9.45834, -107.02777, 4.66142, -103.37846, 0.20125, -120.91069, 18.16383, -126.34622, 23.44319, -134.45526, 26.06506, -132.62668, 17.92773, -91.30886, -7.13109], "time": 1.0333}, {"vertices": [-261.87878, 50.27042, -103.99553, 50.31682, -103.87951, -41.91408, -261.69318, -42.15384, -131.22981, 13.56163, -141.67702, 27.12543, -157.69429, 17.5457, -175.86197, 30.39679, -182.47693, 18.61581, -175.44482, 1.37425, -159.0744, -4.46599, -147.66943, -16.22789, -218.38708, 20.83401, -231.24277, 30.36436, -239.6189, 18.85995, -227.0758, 8.26566, -119.38857, -12.41397], "time": 1.3333}, {"time": 1.8}, {"vertices": [-42.27502, 0.56096, -64.72296, -7.24701, -74.48296, -8.71101, -69.60299, -4.31905, -70.57899, 10.32097, -67.16299, 3.48894, -72.53101, 11.78497, -55.939, 7.39296, -53.98701, 13.73697, -62.771, 14.71295, -69.60292, 6.90501, -71.55498, 5.44096, -57.40298, 11.29701, -54.47501, 11.78499, -62.283, 30.32895, -72.53102, 24.96092, -79.85101, 12.76096], "time": 1.9333}, {"vertices": [-137.40009, 25.82303, -84.20798, 22.89502, -91.528, -22.48895, -149.112, -7.36096, -90.76392, 10.40677, -84.85178, 17.82681, -100.78362, 15.66663, -97.68916, 21.3956, -100.27666, 18.13551, -100.67996, 9.45834, -107.02777, 4.66142, -103.37846, 0.20125, -120.91069, 18.16383, -126.34622, 23.44319, -134.45526, 26.06506, -132.62668, 17.92773, -91.30886, -7.13109], "time": 2.0667}, {"vertices": [-261.87878, 50.27042, -103.99553, 50.31682, -103.87951, -41.91408, -261.69318, -42.15384, -131.22981, 13.56163, -141.67702, 27.12543, -157.69429, 17.5457, -175.86197, 30.39679, -182.47693, 18.61581, -175.44482, 1.37425, -159.0744, -4.46599, -147.66943, -16.22789, -218.38708, 20.83401, -231.24277, 30.36436, -239.6189, 18.85995, -227.0758, 8.26566, -119.38857, -12.41397], "time": 2.3667}]}, "DauNgua": {"DauNgua": [{"curve": "stepped", "time": 0}, {"time": 2.5}]}}}}, "anim-xuongngua-kiemsat": {"slots": {"sword_left": {"attachment": [{"name": "sword_left", "time": 0}]}, "sword_right_wood_glow": {"attachment": [{"name": null, "time": 0}]}, "sword_left_wood": {"attachment": [{"name": null, "time": 0}]}, "ChanTrai2": {"color": [{"color": "63636300", "time": 0.3333}, {"color": "636363ff", "time": 0.4333}]}, "sword_right_wood": {"attachment": [{"name": null, "time": 0}]}, "sword_left_glow": {"attachment": [{"name": null, "time": 0}]}, "sword_left_wood_glow": {"attachment": [{"name": null, "time": 0}]}, "sword_right_glow": {"attachment": [{"name": null, "time": 0}]}, "DuiTrai2": {"color": [{"color": "5c5c5c00", "time": 0.3333}, {"color": "5c5c5cff", "time": 0.4333}]}, "sword_right": {"attachment": [{"name": "sword_right", "time": 0}]}}, "bones": {"DuiTrai": {"translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 0.4}, {"x": 8, "y": -13.54, "time": 1}]}, "Character8": {"translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.4}, {"x": 0, "y": 0, "time": 1}]}, "ChanPhai": {"translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 1.3333}]}, "Character7": {"translate": [{"x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 32.49, "y": 23.94, "time": 0.4}, {"x": 32.49, "y": 23.94, "time": 0.6333}, {"x": 10.36, "y": -37.04, "time": 1}]}, "Character": {"rotate": [{"angle": 0, "time": 0}, {"angle": -29.68, "time": 0.3333}, {"curve": [0.098, 0.02, 0.96, 0.71], "angle": -25.41, "time": 0.5667}, {"angle": -9.77, "time": 1}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 29.33, "y": 34.74, "time": 0.3333}, {"curve": [0.098, 0.02, 0.96, 0.71], "x": 26.9, "y": 27.95, "time": 0.5667}, {"curve": [0.214, 0, 0.617, 0.4], "x": -32.05, "y": -154.65, "time": 1}, {"curve": [0.515, 0.26, 0.996, 0.64], "x": -25.46, "y": -167.79, "time": 1.2667}, {"curve": "stepped", "x": -28.13, "y": -157.45, "time": 1.6}, {"x": -28.13, "y": -157.45, "time": 2}]}, "DuiPhai": {"translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 1.0667}, {"x": 3.16, "y": -15.12, "time": 1.4}]}, "Character2": {"rotate": [{"angle": 0, "time": 1}, {"angle": 5.2, "time": 1.3333}, {"angle": 9.95, "time": 1.6667}, {"angle": 6.94, "time": 2}]}, "CanhTayPhai": {"rotate": [{"angle": 0, "time": 0}, {"angle": 5.46, "time": 0.4}, {"angle": 15.44, "time": 0.6333}, {"curve": "stepped", "angle": 68.7, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": 68.7, "time": 1.3333}, {"angle": -13.47, "time": 1.6}]}, "IK_ChanTrai": {"translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 0.4}]}, "Character3": {"rotate": [{"angle": 0, "time": 1.1}, {"angle": 4.25, "time": 1.4333}, {"angle": 1.4, "time": 1.7667}]}, "IK_GotTrai": {"rotate": [{"angle": 0, "time": 0}, {"angle": -92.11, "time": 0.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": -83.85, "time": 0.4}, {"angle": 55.87, "time": 1}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": -88.67, "y": 125.78, "time": 0.2667}, {"curve": [0.25, 0, 0.625, 0.5], "x": -99.52, "y": 118.46, "time": 0.4}, {"curve": [0.375, 0.5, 0.75, 1], "x": -66.77, "y": -17.58, "time": 0.7333}, {"x": -6.86, "y": -182.46, "time": 1}]}, "CanhTayTrai": {"rotate": [{"angle": 0, "time": 0}, {"angle": 18.51, "time": 0.4}, {"angle": 42.31, "time": 0.8667}, {"curve": "stepped", "angle": 80.88, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": 80.88, "time": 1.2333}, {"angle": -3.63, "time": 1.5}]}, "ChanTrai": {"translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.4}, {"x": 0, "y": 0, "time": 1}]}, "TayPhai": {"rotate": [{"angle": 0, "time": 0}, {"angle": 11.7, "time": 0.6333}, {"curve": "stepped", "angle": 89.72, "time": 1}, {"angle": 89.72, "time": 1.3333}, {"angle": 9.37, "time": 1.6667}], "translate": [{"x": -5.41, "y": 0.16, "time": 0.6333}, {"curve": "stepped", "x": -23.28, "y": -0.08, "time": 1}, {"x": -23.28, "y": -0.08, "time": 1.3333}]}, "TayTrai": {"rotate": [{"angle": 0, "time": 0}, {"angle": -66.94, "time": 0.4}, {"angle": -42.84, "time": 0.8}, {"curve": "stepped", "angle": -16.06, "time": 1}, {"angle": -16.06, "time": 1.2333}, {"angle": -36.15, "time": 1.5667}]}, "IK_GotPhai": {"rotate": [{"angle": 0, "time": 0.8333}, {"angle": 74.87, "time": 1.0667}, {"angle": 22.75, "time": 1.4}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 1.0667}, {"x": -69.43, "y": -188.13, "time": 1.4}]}, "IK_ChanPhai": {"translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 1.3333}]}}, "deform": {"default": {"CanhTayPhai": {"CanhTayPhai": [{"time": 0.6333}, {"offset": 2, "curve": "stepped", "vertices": [-9.91504, 0.2048, -19.11042, 0.39425, -26.71201, 0.55083, -26.71201, 0.55083, -19.11042, 0.39425, -9.91504, 0.2048, 0, 0, 0, 0, 0, 0, -9.91504, 0.2048, -19.11042, 0.39425], "time": 1}, {"offset": 2, "vertices": [-9.91504, 0.2048, -19.11042, 0.39425, -26.71201, 0.55083, -26.71201, 0.55083, -19.11042, 0.39425, -9.91504, 0.2048, 0, 0, 0, 0, 0, 0, -9.91504, 0.2048, -19.11042, 0.39425], "time": 1.3333}]}, "TayPhai": {"TayPhai": [{"offset": 2, "curve": "stepped", "vertices": [-36.11156, 2.68999, -31.42137, 2.34056, -31.63351, 2.3564, -34.69065, 2.58411], "time": 0}, {"offset": 2, "vertices": [-36.11156, 2.68999, -31.42137, 2.34056, -31.63351, 2.3564, -34.69065, 2.58411], "time": 0.6333}, {"curve": "stepped", "time": 1}, {"time": 1.3333}]}}}, "drawOrder": [{"offsets": [{"offset": 17, "slot": "ChanTrai"}, {"offset": 17, "slot": "DuiTrai"}, {"offset": -9, "slot": "TuiDo"}], "time": 0.4}]}, "anim-nguachay-kiemgo": {"slots": {"sword_left": {"attachment": [{"name": null, "time": 0}]}, "sword_right_wood_glow": {"attachment": [{"name": null, "time": 0}]}, "dust2": {"color": [{"color": "ffffff00", "time": 0.6667}, {"color": "ffffffff", "time": 1.2}, {"color": "ffffff00", "curve": "stepped", "time": 1.5667}, {"color": "ffffff00", "time": 1.8333}, {"color": "ffffffff", "time": 2.3667}, {"color": "ffffff00", "time": 2.7333}], "attachment": [{"name": "dust", "time": 0.9667}, {"name": null, "time": 1.7333}, {"name": "dust", "time": 2.1333}]}, "dust3": {"color": [{"color": "ffffff00", "time": 0.9667}, {"color": "ffffffff", "time": 1.5667}, {"color": "ffffff00", "time": 2.2}], "attachment": [{"name": "dust", "time": 1.4}]}, "sword_left_glow": {"attachment": [{"name": null, "time": 0}]}, "dust4": {"color": [{"color": "ffffff00", "time": 0.8333}, {"color": "ffffffff", "time": 1.4}, {"color": "ffffff00", "curve": "stepped", "time": 1.8}, {"color": "ffffff00", "time": 1.9333}, {"color": "ffffffff", "time": 2.5}, {"color": "ffffff00", "time": 2.9}], "attachment": [{"name": "dust", "time": 1.1333}, {"name": null, "time": 1.8667}, {"name": "dust", "time": 2.2333}]}, "dust5": {"color": [{"color": "ffffff00", "time": 0.7333}, {"color": "ffffffff", "time": 1.3333}, {"color": "ffffff00", "curve": "stepped", "time": 1.7333}, {"color": "ffffff00", "time": 1.9333}, {"color": "ffffffff", "time": 2.5333}, {"color": "ffffff00", "time": 2.9333}], "attachment": [{"name": "dust", "time": 1.0667}, {"name": null, "time": 1.8667}, {"name": "dust", "time": 2.2667}]}, "dust6": {"color": [{"color": "ffffff00", "time": 0.8667}, {"color": "ffffffff", "time": 1.5333}, {"color": "ffffff00", "curve": "stepped", "time": 2}, {"color": "ffffff00", "time": 2.1667}, {"color": "ffffffff", "time": 2.8333}, {"color": "ffffff00", "time": 3.3}], "attachment": [{"name": "dust", "time": 1.2333}, {"name": null, "time": 2.1}, {"name": "dust", "time": 2.5333}]}, "sword_left_wood_glow": {"attachment": [{"name": null, "time": 0}]}, "dust": {"color": [{"color": "ffffff00", "time": 0.4333}, {"color": "ffffffff", "time": 1.0667}, {"color": "ffffff00", "curve": "stepped", "time": 1.3333}, {"color": "ffffff00", "time": 1.4667}, {"color": "ffffffff", "time": 2.1}, {"color": "ffffff00", "time": 2.3667}], "attachment": [{"name": "dust", "time": 0.7333}, {"name": null, "time": 1.4}, {"name": "dust", "time": 1.7667}]}, "sword_right_glow": {"attachment": [{"name": null, "time": 0}]}, "sword_right": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"IK_VoChanSau_Ngoai": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -1.36, "time": 0}, {"angle": 0, "time": 0.5333}, {"angle": -3.22, "time": 0.6}, {"angle": -43.24, "time": 0.6667}, {"angle": -56.66, "time": 0.7}, {"angle": -74.13, "time": 0.7667}, {"angle": -42.78, "time": 0.8667}, {"angle": -12.76, "time": 0.9333}, {"angle": -0.77, "time": 1.0333}, {"angle": -1.54, "time": 1.1}, {"angle": 1.14, "time": 1.2}, {"angle": -1.42, "time": 1.2333}, {"angle": -16.39, "time": 1.2667}, {"angle": -43.24, "time": 1.3333}, {"angle": -56.66, "time": 1.3667}, {"angle": -74.13, "time": 1.4333}, {"angle": -42.78, "time": 1.5333}, {"angle": -12.76, "time": 1.6}, {"angle": -0.77, "time": 1.7}, {"angle": -1.54, "time": 1.7667}, {"angle": 1.14, "time": 1.8667}, {"angle": -1.42, "time": 1.9}, {"angle": -16.39, "time": 1.9333}, {"angle": -43.24, "time": 2}, {"angle": -56.66, "time": 2.0333}, {"angle": -74.13, "time": 2.1}, {"angle": -42.78, "time": 2.2}, {"angle": -12.76, "time": 2.2667}, {"angle": -0.77, "time": 2.3667}, {"angle": -1.54, "time": 2.4333}, {"angle": 1.14, "time": 2.5333}, {"angle": -1.42, "time": 2.5667}, {"angle": -16.39, "time": 2.6}, {"angle": -43.24, "time": 2.6667}, {"angle": -56.66, "time": 2.7}, {"angle": -74.13, "time": 2.7667}, {"angle": -42.78, "time": 2.8667}, {"angle": -12.76, "time": 2.9333}, {"angle": -0.77, "time": 3.0333}, {"angle": -1.54, "time": 3.1}, {"angle": 1.14, "time": 3.2}, {"angle": -1.42, "time": 3.2333}, {"angle": -16.39, "time": 3.2667}, {"angle": -43.24, "time": 3.3333}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 2.41, "y": -1.87, "time": 0}, {"x": 0, "y": 0, "time": 0.5333}, {"x": -52.79, "y": 12.14, "time": 0.6}, {"x": -105.57, "y": 38.63, "time": 0.6667}, {"x": -127.14, "y": 51.17, "time": 0.7}, {"x": -110.41, "y": 62.71, "time": 0.7667}, {"x": -20.04, "y": 105.01, "time": 0.8667}, {"x": 24.65, "y": 78.13, "time": 0.9333}, {"x": 77.99, "y": 39.95, "time": 1.0333}, {"x": 99.8, "y": 27.46, "time": 1.1}, {"x": 61.28, "y": 18.71, "time": 1.2}, {"x": 19.51, "y": 14.04, "time": 1.2333}, {"x": -62.43, "y": 13.55, "time": 1.2667}, {"x": -105.57, "y": 38.63, "time": 1.3333}, {"x": -127.14, "y": 51.17, "time": 1.3667}, {"x": -110.41, "y": 62.71, "time": 1.4333}, {"x": -20.04, "y": 105.01, "time": 1.5333}, {"x": 24.65, "y": 78.13, "time": 1.6}, {"x": 77.99, "y": 39.95, "time": 1.7}, {"x": 99.8, "y": 27.46, "time": 1.7667}, {"x": 61.28, "y": 18.71, "time": 1.8667}, {"x": 19.51, "y": 14.04, "time": 1.9}, {"x": -62.43, "y": 13.55, "time": 1.9333}, {"x": -105.57, "y": 38.63, "time": 2}, {"x": -127.14, "y": 51.17, "time": 2.0333}, {"x": -110.41, "y": 62.71, "time": 2.1}, {"x": -20.04, "y": 105.01, "time": 2.2}, {"x": 24.65, "y": 78.13, "time": 2.2667}, {"x": 77.99, "y": 39.95, "time": 2.3667}, {"x": 99.8, "y": 27.46, "time": 2.4333}, {"x": 61.28, "y": 18.71, "time": 2.5333}, {"x": 19.51, "y": 14.04, "time": 2.5667}, {"x": -62.43, "y": 13.55, "time": 2.6}, {"x": -105.57, "y": 38.63, "time": 2.6667}, {"x": -127.14, "y": 51.17, "time": 2.7}, {"x": -110.41, "y": 62.71, "time": 2.7667}, {"x": -20.04, "y": 105.01, "time": 2.8667}, {"x": 24.65, "y": 78.13, "time": 2.9333}, {"x": 77.99, "y": 39.95, "time": 3.0333}, {"x": 99.8, "y": 27.46, "time": 3.1}, {"x": 61.28, "y": 18.71, "time": 3.2}, {"x": 19.51, "y": 14.04, "time": 3.2333}, {"x": -62.43, "y": 13.55, "time": 3.2667}, {"x": -105.57, "y": 38.63, "time": 3.3333}]}, "ChanPhai": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.02, "time": 0.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.82, "time": 0.5333}, {"curve": [0.32, 0.29, 0.757, 1], "angle": 0.88, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.34, "time": 0.9333}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -0.83, "time": 1.2667}, {"curve": [0.32, 0.29, 0.757, 1], "angle": 0.88, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.34, "time": 1.6}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -0.83, "time": 1.9333}, {"curve": [0.32, 0.29, 0.757, 1], "angle": 0.88, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.34, "time": 2.2667}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -0.83, "time": 2.6}, {"curve": [0.32, 0.29, 0.757, 1], "angle": 0.88, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.34, "time": 2.9333}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -0.83, "time": 3.2667}, {"angle": 0.88, "time": 3.3333}]}, "Duoi12": {"rotate": [{"angle": 0, "time": 0}, {"angle": -8.58, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -130.41, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -15.26, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": -130.41, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -15.26, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -130.41, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -15.26, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -130.41, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -15.26, "time": 3}, {"angle": -130.41, "time": 3.3333}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 17.81, "y": -32.7, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 51.76, "y": -96.75, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 39.8, "y": -56.1, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 51.76, "y": -96.75, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 39.8, "y": -56.1, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 51.76, "y": -96.75, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 39.8, "y": -56.1, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 51.76, "y": -96.75, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 39.8, "y": -56.1, "time": 3}, {"x": 51.76, "y": -96.75, "time": 3.3333}]}, "Character7": {"translate": [{"x": -4.49, "y": 5.52, "time": 0}]}, "Character2": {"rotate": [{"angle": 0, "time": 0.0667}, {"angle": 5.95, "time": 0.2}, {"angle": 0.27, "time": 0.3333}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 0.97, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.53, "time": 0.7333}, {"curve": [0.243, 0, 0.68, 0.71], "angle": -2.81, "time": 1.0667}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 0.97, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.53, "time": 1.4}, {"curve": [0.243, 0, 0.68, 0.71], "angle": -2.81, "time": 1.7333}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 0.97, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.53, "time": 2.0667}, {"curve": [0.243, 0, 0.68, 0.71], "angle": -2.81, "time": 2.4}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 0.97, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.53, "time": 2.7333}, {"curve": [0.243, 0, 0.68, 0.71], "angle": -2.81, "time": 3.0667}, {"angle": 0.97, "time": 3.3333}]}, "Duoi10": {"rotate": [{"angle": 0, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 25.9, "y": -39.87, "time": 0.3333}, {"curve": [0.32, 0.29, 0.757, 1], "x": 37.62, "y": -61.39, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 35.84, "y": -55.7, "time": 0.9333}, {"curve": [0.284, 0, 0.625, 0.38], "x": 37.89, "y": -62.24, "time": 1.2667}, {"curve": [0.32, 0.29, 0.757, 1], "x": 37.62, "y": -61.39, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 35.84, "y": -55.7, "time": 1.6}, {"curve": [0.284, 0, 0.625, 0.38], "x": 37.89, "y": -62.24, "time": 1.9333}, {"curve": [0.32, 0.29, 0.757, 1], "x": 37.62, "y": -61.39, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 35.84, "y": -55.7, "time": 2.2667}, {"curve": [0.284, 0, 0.625, 0.38], "x": 37.89, "y": -62.24, "time": 2.6}, {"curve": [0.32, 0.29, 0.757, 1], "x": 37.62, "y": -61.39, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 35.84, "y": -55.7, "time": 2.9333}, {"curve": [0.284, 0, 0.625, 0.38], "x": 37.89, "y": -62.24, "time": 3.2667}, {"x": 37.62, "y": -61.39, "time": 3.3333}]}, "Duoi11": {"rotate": [{"angle": 2.35, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 21.19, "y": -33.92, "time": 0.3333}, {"curve": [0.289, 0.17, 0.755, 1], "x": 42.51, "y": -77.52, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 36.54, "y": -54.85, "time": 0.9667}, {"curve": [0.305, 0, 0.64, 0.36], "x": 42.77, "y": -78.52, "time": 1.3}, {"curve": [0.289, 0.17, 0.755, 1], "x": 42.51, "y": -77.52, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 36.54, "y": -54.85, "time": 1.6333}, {"curve": [0.305, 0, 0.64, 0.36], "x": 42.77, "y": -78.52, "time": 1.9667}, {"curve": [0.289, 0.17, 0.755, 1], "x": 42.51, "y": -77.52, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 36.54, "y": -54.85, "time": 2.3}, {"curve": [0.305, 0, 0.64, 0.36], "x": 42.77, "y": -78.52, "time": 2.6333}, {"curve": [0.289, 0.17, 0.755, 1], "x": 42.51, "y": -77.52, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 36.54, "y": -54.85, "time": 2.9667}, {"curve": [0.305, 0, 0.64, 0.36], "x": 42.77, "y": -78.52, "time": 3.3}, {"x": 42.51, "y": -77.52, "time": 3.3333}]}, "ThanNgua": {"rotate": [{"curve": [0.46, 0, 1, 0.87], "angle": 0, "time": 0}, {"curve": "stepped", "angle": 9.42, "time": 0.1667}, {"curve": [0.188, 0, 1, 0.41], "angle": 9.42, "time": 0.3333}, {"angle": 0, "time": 0.6667}], "translate": [{"curve": [0.46, 0, 1, 0.87], "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": -21.69, "y": -12.86, "time": 0.1667}, {"curve": [0.188, 0, 1, 0.41], "x": -21.69, "y": -12.86, "time": 0.3333}, {"curve": "stepped", "x": 9.3, "y": 0, "time": 0.6667}, {"curve": "stepped", "x": 9.3, "y": 0, "time": 1.3333}, {"curve": "stepped", "x": 9.3, "y": 0, "time": 2}, {"x": 9.3, "y": 0, "time": 2.6667}]}, "Character3": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 4.2, "time": 0}, {"angle": 0, "time": 0.1333}, {"angle": 10.66, "time": 0.2667}, {"angle": 6.14, "time": 0.4}, {"angle": -10.44, "time": 0.5333}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -0.8, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.8}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -2.18, "time": 1.1333}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -0.8, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.4667}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -2.18, "time": 1.8}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -0.8, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.1333}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -2.18, "time": 2.4667}, {"curve": [0.381, 0.55, 0.742, 1], "angle": -0.8, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.8}, {"curve": [0.245, 0, 0.637, 0.56], "angle": -2.18, "time": 3.1333}, {"angle": -0.8, "time": 3.3333}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.1333}, {"x": 0, "y": 0, "time": 0.5333}, {"curve": [0.381, 0.55, 0.742, 1], "x": 2.44, "y": -0.1, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.8}, {"curve": [0.245, 0, 0.637, 0.56], "x": 6.64, "y": -0.27, "time": 1.1333}, {"curve": [0.381, 0.55, 0.742, 1], "x": 2.44, "y": -0.1, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1.4667}, {"curve": [0.245, 0, 0.637, 0.56], "x": 6.64, "y": -0.27, "time": 1.8}, {"curve": [0.381, 0.55, 0.742, 1], "x": 2.44, "y": -0.1, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.1333}, {"curve": [0.245, 0, 0.637, 0.56], "x": 6.64, "y": -0.27, "time": 2.4667}, {"curve": [0.381, 0.55, 0.742, 1], "x": 2.44, "y": -0.1, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.8}, {"curve": [0.245, 0, 0.637, 0.56], "x": 6.64, "y": -0.27, "time": 3.1333}, {"x": 2.44, "y": -0.1, "time": 3.3333}]}, "TuiDo2": {"rotate": [{"angle": 0, "time": 0}, {"angle": 9.96, "time": 0.4}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 1.27, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.7333}, {"curve": [0.243, 0, 0.68, 0.71], "angle": 9.79, "time": 1.0667}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 1.27, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.4}, {"curve": [0.243, 0, 0.68, 0.71], "angle": 9.79, "time": 1.7333}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 1.27, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.0667}, {"curve": [0.243, 0, 0.68, 0.71], "angle": 9.79, "time": 2.4}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 1.27, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.7333}, {"curve": [0.243, 0, 0.68, 0.71], "angle": 9.79, "time": 3.0667}, {"angle": 1.27, "time": 3.3333}]}, "IK_VoChanTruoc_Trong": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -11.77, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": -71.72, "time": 0.3333}, {"curve": "stepped", "angle": -19.96, "time": 0.5333}, {"angle": -19.96, "time": 0.6667}, {"angle": 12.44, "time": 0.7333}, {"angle": 11.22, "time": 0.8333}, {"angle": -4.04, "time": 0.9}, {"angle": -12.13, "time": 1}, {"angle": -48.94, "time": 1.0667}, {"angle": -136.9, "time": 1.1667}, {"angle": -148.69, "time": 1.2333}, {"angle": -19.96, "time": 1.3333}, {"angle": 12.44, "time": 1.4}, {"angle": 11.22, "time": 1.5}, {"angle": -4.04, "time": 1.5667}, {"angle": -12.13, "time": 1.6667}, {"angle": -48.94, "time": 1.7333}, {"angle": -136.9, "time": 1.8333}, {"angle": -148.69, "time": 1.9}, {"angle": -19.96, "time": 2}, {"angle": 12.44, "time": 2.0667}, {"angle": 11.22, "time": 2.1667}, {"angle": -4.04, "time": 2.2333}, {"angle": -12.13, "time": 2.3333}, {"angle": -48.94, "time": 2.4}, {"angle": -136.9, "time": 2.5}, {"angle": -148.69, "time": 2.5667}, {"angle": -19.96, "time": 2.6667}, {"angle": 12.44, "time": 2.7333}, {"angle": 11.22, "time": 2.8333}, {"angle": -4.04, "time": 2.9}, {"angle": -12.13, "time": 3}, {"angle": -48.94, "time": 3.0667}, {"angle": -136.9, "time": 3.1667}, {"angle": -148.69, "time": 3.2333}, {"angle": -19.96, "time": 3.3333}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 1.26, "y": 0.42, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 52.06, "y": 118.05, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 115.7, "y": 136.76, "time": 0.5333}, {"x": 131.7, "y": 106.36, "time": 0.6667}, {"x": 104.8, "y": 39.01, "time": 0.7333}, {"x": 70.89, "y": 24.28, "time": 0.8333}, {"x": 50.09, "y": 23.8, "time": 0.9}, {"x": 3, "y": 21.23, "time": 1}, {"x": -86.8, "y": 48.1, "time": 1.0667}, {"x": -43.74, "y": 119.07, "time": 1.1667}, {"x": -7.82, "y": 94.08, "time": 1.2333}, {"x": 131.7, "y": 106.36, "time": 1.3333}, {"x": 104.8, "y": 39.01, "time": 1.4}, {"x": 70.89, "y": 24.28, "time": 1.5}, {"x": 50.09, "y": 23.8, "time": 1.5667}, {"x": 3, "y": 21.23, "time": 1.6667}, {"x": -86.8, "y": 48.1, "time": 1.7333}, {"x": -43.74, "y": 119.07, "time": 1.8333}, {"x": -7.82, "y": 94.08, "time": 1.9}, {"x": 131.7, "y": 106.36, "time": 2}, {"x": 104.8, "y": 39.01, "time": 2.0667}, {"x": 70.89, "y": 24.28, "time": 2.1667}, {"x": 50.09, "y": 23.8, "time": 2.2333}, {"x": 3, "y": 21.23, "time": 2.3333}, {"x": -86.8, "y": 48.1, "time": 2.4}, {"x": -43.74, "y": 119.07, "time": 2.5}, {"x": -7.82, "y": 94.08, "time": 2.5667}, {"x": 131.7, "y": 106.36, "time": 2.6667}, {"x": 104.8, "y": 39.01, "time": 2.7333}, {"x": 70.89, "y": 24.28, "time": 2.8333}, {"x": 50.09, "y": 23.8, "time": 2.9}, {"x": 3, "y": 21.23, "time": 3}, {"x": -86.8, "y": 48.1, "time": 3.0667}, {"x": -43.74, "y": 119.07, "time": 3.1667}, {"x": -7.82, "y": 94.08, "time": 3.2333}, {"x": 131.7, "y": 106.36, "time": 3.3333}]}, "ChanTrai": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.02, "time": 0.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.82, "time": 0.5333}, {"curve": [0.32, 0.29, 0.757, 1], "angle": 0.88, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.34, "time": 0.9333}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -0.83, "time": 1.2667}, {"curve": [0.32, 0.29, 0.757, 1], "angle": 0.88, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.34, "time": 1.6}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -0.83, "time": 1.9333}, {"curve": [0.32, 0.29, 0.757, 1], "angle": 0.88, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.34, "time": 2.2667}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -0.83, "time": 2.6}, {"curve": [0.32, 0.29, 0.757, 1], "angle": 0.88, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.34, "time": 2.9333}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -0.83, "time": 3.2667}, {"angle": 0.88, "time": 3.3333}]}, "IK_VoChanTruoc_Ngoai": {"rotate": [{"curve": [0.25, 0, 0.625, 0.5], "angle": -13.07, "time": 0}, {"curve": [0.246, 0, 0.633, 0.54], "angle": 0, "time": 0.1}, {"curve": [0.38, 0.53, 0.745, 1], "angle": -46.13, "time": 0.2333}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.66, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -20.67, "time": 0.5333}, {"angle": 10.24, "time": 0.6667}, {"angle": -4.26, "time": 0.7}, {"angle": -2.33, "time": 0.7667}, {"angle": -52.39, "time": 0.8333}, {"angle": -61.46, "time": 0.9333}, {"angle": -172.06, "time": 1}, {"angle": -86.03, "time": 1.1667}, {"angle": 18.99, "time": 1.2667}, {"angle": 1.08, "time": 1.3}, {"angle": 10.24, "time": 1.3333}, {"angle": -4.26, "time": 1.3667}, {"angle": -2.33, "time": 1.4333}, {"angle": -52.39, "time": 1.5}, {"angle": -61.46, "time": 1.6}, {"angle": -172.06, "time": 1.6667}, {"angle": -86.03, "time": 1.8333}, {"angle": 18.99, "time": 1.9333}, {"angle": 1.08, "time": 1.9667}, {"angle": 10.24, "time": 2}, {"angle": -4.26, "time": 2.0333}, {"angle": -2.33, "time": 2.1}, {"angle": -52.39, "time": 2.1667}, {"angle": -61.46, "time": 2.2667}, {"angle": -172.06, "time": 2.3333}, {"angle": -86.03, "time": 2.5}, {"angle": 18.99, "time": 2.6}, {"angle": 1.08, "time": 2.6333}, {"angle": 10.24, "time": 2.6667}, {"angle": -4.26, "time": 2.7}, {"angle": -2.33, "time": 2.7667}, {"angle": -52.39, "time": 2.8333}, {"angle": -61.46, "time": 2.9333}, {"angle": -172.06, "time": 3}, {"angle": -86.03, "time": 3.1667}, {"angle": 18.99, "time": 3.2667}, {"angle": 1.08, "time": 3.3}, {"angle": 10.24, "time": 3.3333}], "translate": [{"curve": [0.25, 0, 0.625, 0.5], "x": 46.94, "y": 15.12, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.1}, {"curve": [0.25, 0, 0.75, 1], "x": 69.42, "y": 183.96, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 115.75, "y": 96.77, "time": 0.5333}, {"x": 42.07, "y": 9.82, "time": 0.6667}, {"x": -1.95, "y": 3.33, "time": 0.7}, {"x": -40.77, "y": 10.72, "time": 0.7667}, {"x": -73.19, "y": 23.73, "time": 0.8333}, {"x": -122.01, "y": 56.54, "time": 0.9333}, {"x": -100.89, "y": 88.66, "time": 1}, {"x": 40.3, "y": 89.63, "time": 1.1667}, {"x": 87.99, "y": 35.32, "time": 1.2667}, {"x": 61.24, "y": 17.09, "time": 1.3}, {"x": 42.07, "y": 9.82, "time": 1.3333}, {"x": -1.95, "y": 3.33, "time": 1.3667}, {"x": -40.77, "y": 10.72, "time": 1.4333}, {"x": -73.19, "y": 23.73, "time": 1.5}, {"x": -122.01, "y": 56.54, "time": 1.6}, {"x": -100.89, "y": 88.66, "time": 1.6667}, {"x": 40.3, "y": 89.63, "time": 1.8333}, {"x": 87.99, "y": 35.32, "time": 1.9333}, {"x": 61.24, "y": 17.09, "time": 1.9667}, {"x": 42.07, "y": 9.82, "time": 2}, {"x": -1.95, "y": 3.33, "time": 2.0333}, {"x": -40.77, "y": 10.72, "time": 2.1}, {"x": -73.19, "y": 23.73, "time": 2.1667}, {"x": -122.01, "y": 56.54, "time": 2.2667}, {"x": -100.89, "y": 88.66, "time": 2.3333}, {"x": 40.3, "y": 89.63, "time": 2.5}, {"x": 87.99, "y": 35.32, "time": 2.6}, {"x": 61.24, "y": 17.09, "time": 2.6333}, {"x": 42.07, "y": 9.82, "time": 2.6667}, {"x": -1.95, "y": 3.33, "time": 2.7}, {"x": -40.77, "y": 10.72, "time": 2.7667}, {"x": -73.19, "y": 23.73, "time": 2.8333}, {"x": -122.01, "y": 56.54, "time": 2.9333}, {"x": -100.89, "y": 88.66, "time": 3}, {"x": 40.3, "y": 89.63, "time": 3.1667}, {"x": 87.99, "y": 35.32, "time": 3.2667}, {"x": 61.24, "y": 17.09, "time": 3.3}, {"x": 42.07, "y": 9.82, "time": 3.3333}]}, "Move": {"rotate": [{"angle": 0, "time": 0}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25.5, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25.5, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25.5, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25.5, "time": 3}, {"x": 0, "y": 0, "time": 3.3333}]}, "DauNgua4": {"translate": [{"x": 0, "y": 0, "time": 0}, {"x": -6.64, "y": -0.59, "time": 0.2333}, {"x": 13.37, "y": 4.69, "time": 0.4667}, {"curve": [0.363, 0.44, 0.755, 1], "x": 9.42, "y": 5.41, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.8667}, {"curve": [0.258, 0, 0.619, 0.45], "x": 14.91, "y": 8.56, "time": 1.2}, {"curve": [0.363, 0.44, 0.755, 1], "x": 9.42, "y": 5.41, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1.5333}, {"curve": [0.258, 0, 0.619, 0.45], "x": 14.91, "y": 8.56, "time": 1.8667}, {"curve": [0.363, 0.44, 0.755, 1], "x": 9.42, "y": 5.41, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.2}, {"curve": [0.258, 0, 0.619, 0.45], "x": 14.91, "y": 8.56, "time": 2.5333}, {"curve": [0.363, 0.44, 0.755, 1], "x": 9.42, "y": 5.41, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.8667}, {"curve": [0.258, 0, 0.619, 0.45], "x": 14.91, "y": 8.56, "time": 3.2}, {"x": 9.42, "y": 5.41, "time": 3.3333}]}, "DauNgua2": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 0.1}, {"angle": 12.53, "time": 0.3333}, {"curve": "stepped", "angle": -2.61, "time": 0.6}, {"curve": [0.32, 0.29, 0.757, 1], "angle": -2.61, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.9333}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -3, "time": 1.2667}, {"curve": [0.32, 0.29, 0.757, 1], "angle": -2.61, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.6}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -3, "time": 1.9333}, {"curve": [0.32, 0.29, 0.757, 1], "angle": -2.61, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.2667}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -3, "time": 2.6}, {"curve": [0.32, 0.29, 0.757, 1], "angle": -2.61, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.9333}, {"curve": [0.284, 0, 0.625, 0.38], "angle": -3, "time": 3.2667}, {"angle": -2.61, "time": 3.3333}]}, "DauNgua": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.4, "time": 0.2333}, {"curve": "stepped", "angle": -1.89, "time": 0.5}, {"curve": [0.363, 0.44, 0.755, 1], "angle": -1.89, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.8667}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -3, "time": 1.2}, {"curve": [0.363, 0.44, 0.755, 1], "angle": -1.89, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.5333}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -3, "time": 1.8667}, {"curve": [0.363, 0.44, 0.755, 1], "angle": -1.89, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.2}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -3, "time": 2.5333}, {"curve": [0.363, 0.44, 0.755, 1], "angle": -1.89, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.8667}, {"curve": [0.258, 0, 0.619, 0.45], "angle": -3, "time": 3.2}, {"angle": -1.89, "time": 3.3333}]}, "YenNgua5": {"translate": [{"curve": [0.382, 0.58, 0.731, 1], "x": -1.06, "y": 0.19, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.7667}, {"curve": [0.243, 0, 0.655, 0.63], "x": -4.37, "y": 0.79, "time": 1.1}, {"curve": [0.382, 0.58, 0.731, 1], "x": -1.06, "y": 0.19, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1.4333}, {"curve": [0.243, 0, 0.655, 0.63], "x": -4.37, "y": 0.79, "time": 1.7667}, {"curve": [0.382, 0.58, 0.731, 1], "x": -1.06, "y": 0.19, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.1}, {"curve": [0.243, 0, 0.655, 0.63], "x": -4.37, "y": 0.79, "time": 2.4333}, {"curve": [0.382, 0.58, 0.731, 1], "x": -1.06, "y": 0.19, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.7667}, {"curve": [0.243, 0, 0.655, 0.63], "x": -4.37, "y": 0.79, "time": 3.1}, {"x": -1.06, "y": 0.19, "time": 3.3333}]}, "Duoi": {"rotate": [{"angle": 20.99, "time": 0}]}, "Duoi2": {"rotate": [{"angle": 0, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": -3.05, "y": 2.22, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0.83, "y": 5.88, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": -4.93, "y": 7.19, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 0.83, "y": 5.88, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": -4.93, "y": 7.19, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0.83, "y": 5.88, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": -4.93, "y": 7.19, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0.83, "y": 5.88, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": -4.93, "y": 7.19, "time": 3}, {"x": 0.83, "y": 5.88, "time": 3.3333}]}, "Duoi8": {"rotate": [{"angle": -1.18, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 31.54, "y": -43.51, "time": 0.3333}, {"curve": [0.363, 0.44, 0.755, 1], "x": 32.49, "y": -37.69, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 35.23, "y": -49.5, "time": 0.8667}, {"curve": [0.258, 0, 0.619, 0.45], "x": 30.9, "y": -30.81, "time": 1.2}, {"curve": [0.363, 0.44, 0.755, 1], "x": 32.49, "y": -37.69, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 35.23, "y": -49.5, "time": 1.5333}, {"curve": [0.258, 0, 0.619, 0.45], "x": 30.9, "y": -30.81, "time": 1.8667}, {"curve": [0.363, 0.44, 0.755, 1], "x": 32.49, "y": -37.69, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 35.23, "y": -49.5, "time": 2.2}, {"curve": [0.258, 0, 0.619, 0.45], "x": 30.9, "y": -30.81, "time": 2.5333}, {"curve": [0.363, 0.44, 0.755, 1], "x": 32.49, "y": -37.69, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 35.23, "y": -49.5, "time": 2.8667}, {"curve": [0.258, 0, 0.619, 0.45], "x": 30.9, "y": -30.81, "time": 3.2}, {"x": 32.49, "y": -37.69, "time": 3.3333}]}, "TuiDo3": {"rotate": [{"angle": 0, "time": 0}, {"angle": 11.36, "time": 0.4}, {"curve": [0.381, 0.55, 0.742, 1], "angle": 3.6, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.8}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 9.79, "time": 1.1333}, {"curve": [0.381, 0.55, 0.742, 1], "angle": 3.6, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.4667}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 9.79, "time": 1.8}, {"curve": [0.381, 0.55, 0.742, 1], "angle": 3.6, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.1333}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 9.79, "time": 2.4667}, {"curve": [0.381, 0.55, 0.742, 1], "angle": 3.6, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.8}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 9.79, "time": 3.1333}, {"angle": 3.6, "time": 3.3333}]}, "Duoi7": {"rotate": [{"angle": 0, "time": 0}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0}, {"x": 29.75, "y": -38.1, "time": 0.3333}, {"curve": [0.375, 0.5, 0.75, 1], "x": 27.17, "y": -32.74, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 31.35, "y": -46.59, "time": 0.8333}, {"curve": [0.25, 0, 0.625, 0.5], "x": 23, "y": -18.9, "time": 1.1667}, {"curve": [0.375, 0.5, 0.75, 1], "x": 27.17, "y": -32.74, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 31.35, "y": -46.59, "time": 1.5}, {"curve": [0.25, 0, 0.625, 0.5], "x": 23, "y": -18.9, "time": 1.8333}, {"curve": [0.375, 0.5, 0.75, 1], "x": 27.17, "y": -32.74, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 31.35, "y": -46.59, "time": 2.1667}, {"curve": [0.25, 0, 0.625, 0.5], "x": 23, "y": -18.9, "time": 2.5}, {"curve": [0.375, 0.5, 0.75, 1], "x": 27.17, "y": -32.74, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 31.35, "y": -46.59, "time": 2.8333}, {"curve": [0.25, 0, 0.625, 0.5], "x": 23, "y": -18.9, "time": 3.1667}, {"x": 27.17, "y": -32.74, "time": 3.3333}]}, "TuiDo4": {"rotate": [{"angle": 0, "time": 0}, {"angle": 12.91, "time": 0.4}, {"curve": [0.363, 0.44, 0.755, 1], "angle": 6.19, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.8667}, {"curve": [0.258, 0, 0.619, 0.45], "angle": 9.79, "time": 1.2}, {"curve": [0.363, 0.44, 0.755, 1], "angle": 6.19, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.5333}, {"curve": [0.258, 0, 0.619, 0.45], "angle": 9.79, "time": 1.8667}, {"curve": [0.363, 0.44, 0.755, 1], "angle": 6.19, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.2}, {"curve": [0.258, 0, 0.619, 0.45], "angle": 9.79, "time": 2.5333}, {"curve": [0.363, 0.44, 0.755, 1], "angle": 6.19, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.8667}, {"curve": [0.258, 0, 0.619, 0.45], "angle": 9.79, "time": 3.2}, {"angle": 6.19, "time": 3.3333}]}, "Bong": {"scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 1.107, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1.107, "y": 1, "time": 1.6667}, {"x": 1, "y": 1, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 1.107, "y": 1, "time": 2.3333}, {"x": 1, "y": 1, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 1.107, "y": 1, "time": 3}, {"x": 1, "y": 1, "time": 3.3333}]}, "Duoi9": {"rotate": [{"angle": -0.15, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 29.87, "y": -41.46, "time": 0.3333}, {"curve": [0.345, 0.37, 0.757, 1], "x": 33.68, "y": -47.32, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 35.43, "y": -53.08, "time": 0.9}, {"curve": [0.269, 0, 0.618, 0.42], "x": 33.12, "y": -45.48, "time": 1.2333}, {"curve": [0.345, 0.37, 0.757, 1], "x": 33.68, "y": -47.32, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 35.43, "y": -53.08, "time": 1.5667}, {"curve": [0.269, 0, 0.618, 0.42], "x": 33.12, "y": -45.48, "time": 1.9}, {"curve": [0.345, 0.37, 0.757, 1], "x": 33.68, "y": -47.32, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 35.43, "y": -53.08, "time": 2.2333}, {"curve": [0.269, 0, 0.618, 0.42], "x": 33.12, "y": -45.48, "time": 2.5667}, {"curve": [0.345, 0.37, 0.757, 1], "x": 33.68, "y": -47.32, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 35.43, "y": -53.08, "time": 2.9}, {"curve": [0.269, 0, 0.618, 0.42], "x": 33.12, "y": -45.48, "time": 3.2333}, {"x": 33.68, "y": -47.32, "time": 3.3333}]}, "Duoi4": {"rotate": [{"angle": 0, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 12.75, "y": -18.42, "time": 0.3333}, {"curve": [0.375, 0.62, 0.716, 1], "x": 8.75, "y": -15.35, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 9.95, "y": -18.45, "time": 0.7333}, {"curve": [0.243, 0, 0.68, 0.71], "x": 0.72, "y": 5.4, "time": 1.0667}, {"curve": [0.375, 0.62, 0.716, 1], "x": 8.75, "y": -15.35, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 9.95, "y": -18.45, "time": 1.4}, {"curve": [0.243, 0, 0.68, 0.71], "x": 0.72, "y": 5.4, "time": 1.7333}, {"curve": [0.375, 0.62, 0.716, 1], "x": 8.75, "y": -15.35, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 9.95, "y": -18.45, "time": 2.0667}, {"curve": [0.243, 0, 0.68, 0.71], "x": 0.72, "y": 5.4, "time": 2.4}, {"curve": [0.375, 0.62, 0.716, 1], "x": 8.75, "y": -15.35, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 9.95, "y": -18.45, "time": 2.7333}, {"curve": [0.243, 0, 0.68, 0.71], "x": 0.72, "y": 5.4, "time": 3.0667}, {"x": 8.75, "y": -15.35, "time": 3.3333}]}, "Duoi3": {"rotate": [{"angle": 0, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 3.7, "y": -8.89, "time": 0.3333}, {"curve": [0.36, 0.64, 0.695, 1], "x": 5.53, "y": -6.77, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 5.89, "y": -7.41, "time": 0.7}, {"curve": [0.245, 0, 0.711, 0.83], "x": -2.79, "y": 7.72, "time": 1.0333}, {"curve": [0.36, 0.64, 0.695, 1], "x": 5.53, "y": -6.77, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 5.89, "y": -7.41, "time": 1.3667}, {"curve": [0.245, 0, 0.711, 0.83], "x": -2.79, "y": 7.72, "time": 1.7}, {"curve": [0.36, 0.64, 0.695, 1], "x": 5.53, "y": -6.77, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 5.89, "y": -7.41, "time": 2.0333}, {"curve": [0.245, 0, 0.711, 0.83], "x": -2.79, "y": 7.72, "time": 2.3667}, {"curve": [0.36, 0.64, 0.695, 1], "x": 5.53, "y": -6.77, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 5.89, "y": -7.41, "time": 2.7}, {"curve": [0.245, 0, 0.711, 0.83], "x": -2.79, "y": 7.72, "time": 3.0333}, {"x": 5.53, "y": -6.77, "time": 3.3333}]}, "IK_VoChanSau_Trong": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -46.85, "time": 0}, {"angle": 0, "time": 0.5333}, {"angle": -22.22, "time": 0.6}, {"angle": 0, "time": 0.6667}, {"angle": -3.03, "time": 0.7667}, {"angle": 1.99, "time": 0.8333}, {"angle": -81.87, "time": 0.9333}, {"angle": -14.86, "time": 1}, {"angle": 27.81, "time": 1.1}, {"angle": 5.95, "time": 1.1667}, {"angle": 2.38, "time": 1.2667}, {"angle": 0, "time": 1.3333}, {"angle": -3.03, "time": 1.4333}, {"angle": 1.99, "time": 1.5}, {"angle": -81.87, "time": 1.6}, {"angle": -14.86, "time": 1.6667}, {"angle": 27.81, "time": 1.7667}, {"angle": 5.95, "time": 1.8333}, {"angle": 2.38, "time": 1.9333}, {"angle": 0, "time": 2}, {"angle": -3.03, "time": 2.1}, {"angle": 1.99, "time": 2.1667}, {"angle": -81.87, "time": 2.2667}, {"angle": -14.86, "time": 2.3333}, {"angle": 27.81, "time": 2.4333}, {"angle": 5.95, "time": 2.5}, {"angle": 2.38, "time": 2.6}, {"angle": 0, "time": 2.6667}, {"angle": -3.03, "time": 2.7667}, {"angle": 1.99, "time": 2.8333}, {"angle": -81.87, "time": 2.9333}, {"angle": -14.86, "time": 3}, {"angle": 27.81, "time": 3.1}, {"angle": 5.95, "time": 3.1667}, {"angle": 2.38, "time": 3.2667}, {"angle": 0, "time": 3.3333}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 18.71, "y": 18.68, "time": 0}, {"x": 0, "y": 0, "time": 0.5333}, {"x": 5.44, "y": 61.4, "time": 0.6}, {"x": 37.19, "y": 19.95, "time": 0.6667}, {"x": 5.59, "y": 18.71, "time": 0.7667}, {"x": -61.28, "y": 38.94, "time": 0.8333}, {"x": -117.85, "y": 82.06, "time": 0.9333}, {"x": 6.21, "y": 108.11, "time": 1}, {"x": 113.28, "y": 72.37, "time": 1.1}, {"x": 127.55, "y": 56.37, "time": 1.1667}, {"x": 73.33, "y": 34.52, "time": 1.2667}, {"x": 37.19, "y": 19.95, "time": 1.3333}, {"x": 5.59, "y": 18.71, "time": 1.4333}, {"x": -61.28, "y": 38.94, "time": 1.5}, {"x": -117.85, "y": 82.06, "time": 1.6}, {"x": 6.21, "y": 108.11, "time": 1.6667}, {"x": 113.28, "y": 72.37, "time": 1.7667}, {"x": 127.55, "y": 56.37, "time": 1.8333}, {"x": 73.33, "y": 34.52, "time": 1.9333}, {"x": 37.19, "y": 19.95, "time": 2}, {"x": 5.59, "y": 18.71, "time": 2.1}, {"x": -61.28, "y": 38.94, "time": 2.1667}, {"x": -117.85, "y": 82.06, "time": 2.2667}, {"x": 6.21, "y": 108.11, "time": 2.3333}, {"x": 113.28, "y": 72.37, "time": 2.4333}, {"x": 127.55, "y": 56.37, "time": 2.5}, {"x": 73.33, "y": 34.52, "time": 2.6}, {"x": 37.19, "y": 19.95, "time": 2.6667}, {"x": 5.59, "y": 18.71, "time": 2.7667}, {"x": -61.28, "y": 38.94, "time": 2.8333}, {"x": -117.85, "y": 82.06, "time": 2.9333}, {"x": 6.21, "y": 108.11, "time": 3}, {"x": 113.28, "y": 72.37, "time": 3.1}, {"x": 127.55, "y": 56.37, "time": 3.1667}, {"x": 73.33, "y": 34.52, "time": 3.2667}, {"x": 37.19, "y": 19.95, "time": 3.3333}]}, "Duoi6": {"rotate": [{"angle": 0, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 24.02, "y": -31.61, "time": 0.3333}, {"curve": [0.381, 0.55, 0.742, 1], "x": 21.01, "y": -28.47, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 25.01, "y": -41.1, "time": 0.8}, {"curve": [0.245, 0, 0.637, 0.56], "x": 14.13, "y": -6.76, "time": 1.1333}, {"curve": [0.381, 0.55, 0.742, 1], "x": 21.01, "y": -28.47, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 25.01, "y": -41.1, "time": 1.4667}, {"curve": [0.245, 0, 0.637, 0.56], "x": 14.13, "y": -6.76, "time": 1.8}, {"curve": [0.381, 0.55, 0.742, 1], "x": 21.01, "y": -28.47, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 25.01, "y": -41.1, "time": 2.1333}, {"curve": [0.245, 0, 0.637, 0.56], "x": 14.13, "y": -6.76, "time": 2.4667}, {"curve": [0.381, 0.55, 0.742, 1], "x": 21.01, "y": -28.47, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 25.01, "y": -41.1, "time": 2.8}, {"curve": [0.245, 0, 0.637, 0.56], "x": 14.13, "y": -6.76, "time": 3.1333}, {"x": 21.01, "y": -28.47, "time": 3.3333}]}, "Duoi5": {"rotate": [{"angle": 0, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 16.83, "y": -26.91, "time": 0.3333}, {"curve": [0.382, 0.58, 0.731, 1], "x": 13, "y": -23.43, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 15.6, "y": -31.37, "time": 0.7667}, {"curve": [0.243, 0, 0.655, 0.63], "x": 4.87, "y": 1.42, "time": 1.1}, {"curve": [0.382, 0.58, 0.731, 1], "x": 13, "y": -23.43, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 15.6, "y": -31.37, "time": 1.4333}, {"curve": [0.243, 0, 0.655, 0.63], "x": 4.87, "y": 1.42, "time": 1.7667}, {"curve": [0.382, 0.58, 0.731, 1], "x": 13, "y": -23.43, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 15.6, "y": -31.37, "time": 2.1}, {"curve": [0.243, 0, 0.655, 0.63], "x": 4.87, "y": 1.42, "time": 2.4333}, {"curve": [0.382, 0.58, 0.731, 1], "x": 13, "y": -23.43, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 15.6, "y": -31.37, "time": 2.7667}, {"curve": [0.243, 0, 0.655, 0.63], "x": 4.87, "y": 1.42, "time": 3.1}, {"x": 13, "y": -23.43, "time": 3.3333}]}}, "deform": {"default": {"dust2": {"dust": [{"time": 1}, {"vertices": [-87.84035, 24.11005, -34.64826, 21.18204, -41.96827, -24.20193, -99.55228, -9.07394, -41.20419, 8.69379, -35.29206, 16.11383, -51.22389, 13.95364, -48.12944, 19.68262, -50.71693, 16.42253, -51.12024, 7.74536, -57.46804, 2.94844, -53.81874, -1.51173, -71.35097, 16.45085, -76.7865, 21.73021, -84.89554, 24.35208, -83.06694, 16.21475, -41.74913, -8.84407], "time": 1.2667}, {"vertices": [-213.9578, 49.9147, -56.07459, 49.96111, -55.95857, -42.26979, -213.77219, -42.50955, -83.30888, 13.20592, -93.75607, 26.76971, -109.77335, 17.18999, -127.94105, 30.04108, -134.55594, 18.2601, -127.52389, 1.01854, -111.15347, -4.8217, -99.7485, -16.5836, -170.4661, 20.4783, -183.32178, 30.00865, -191.6979, 18.50424, -179.15482, 7.90994, -71.46764, -12.76968], "time": 1.5667}, {"time": 2.1667}, {"vertices": [-87.84035, 24.11005, -34.64826, 21.18204, -41.96827, -24.20193, -99.55228, -9.07394, -41.20419, 8.69379, -35.29206, 16.11383, -51.22389, 13.95364, -48.12944, 19.68262, -50.71693, 16.42253, -51.12024, 7.74536, -57.46804, 2.94844, -53.81874, -1.51173, -71.35097, 16.45085, -76.7865, 21.73021, -84.89554, 24.35208, -83.06694, 16.21475, -41.74913, -8.84407], "time": 2.4333}, {"vertices": [-213.9578, 49.9147, -56.07459, 49.96111, -55.95857, -42.26979, -213.77219, -42.50955, -83.30888, 13.20592, -93.75607, 26.76971, -109.77335, 17.18999, -127.94105, 30.04108, -134.55594, 18.2601, -127.52389, 1.01854, -111.15347, -4.8217, -99.7485, -16.5836, -170.4661, 20.4783, -183.32178, 30.00865, -191.6979, 18.50424, -179.15482, 7.90994, -71.46764, -12.76968], "time": 2.7333}]}, "dust3": {"dust": [{"time": 1.4333}, {"vertices": [-43.46499, 5.20752, -29.26479, -10.88641, -35.89159, -8.5198, -68.55206, 3.78751, -41.09832, -14.19963, -39.20492, -8.04585, -47.25176, -0.94608, -49.14498, -8.51858, -50.56505, 10.41459, -53.87846, 6.62787, -56.71852, -8.99292, -56.2452, -3.31272, -50.56502, 4.73509, -51.98513, 6.15408, -38.7316, 7.10085, -55.77181, 9.94141, -42.99169, -17.03973], "time": 1.6}, {"vertices": [-106.10681, 4.73066, -36.82115, -10.50385, -63.07483, -37.90125, -118.29201, 4.68147, -43.85048, -19.20573, -45.98512, -13.20567, -56.23684, -9.21252, -56.92912, -4.43013, -64.24998, -1.06326, -73.64676, -8.79219, -64.84772, -18.79771, -62.14511, -15.68444, -89.1441, -0.08829, -92.68629, 12.29096, -102.68872, 13.96674, -103.22678, -0.79648, -46.28876, -35.79694], "time": 1.8}, {"vertices": [-204.01752, 26.89085, -46.13425, 26.93726, -46.01823, -65.29364, -203.83191, -65.5334, -73.36854, -9.81793, -83.81577, 3.74586, -99.83304, -5.83386, -118.00072, 7.01723, -124.61568, -4.76375, -117.58357, -22.00531, -101.21316, -27.84555, -89.80818, -39.60745, -160.52582, -2.54555, -173.3815, 6.9848, -181.75763, -4.51961, -169.21454, -15.11391, -61.52729, -35.79353], "time": 2.2}]}, "dust4": {"dust": [{"time": 1.1333}, {"vertices": [-52.77102, 1.72142, -40.02659, -4.52197, -49.78658, -5.98598, -44.90662, -1.59401, -53.70322, 2.0972, -51.85131, -7.08105, -59.56562, 3.56119, -57.05081, -6.30565, -62.91919, 4.7305, -49.80572, 4.92493, -54.29129, 2.59155, -46.8586, 8.166, -61.6431, 5.41821, -43.07431, -1.91365, -45.40737, 10.37445, -44.70687, 20.64722, -44.98818, 10.01154], "time": 1.3333}, {"vertices": [-122.25154, 10.61207, -75.8007, 7.1315, -83.52739, -30.93249, -130.70068, -17.59383, -92.3876, -8.93551, -93.16704, -6.90324, -93.39052, -2.22712, -102.87836, -4.29976, -97.68886, -3.09613, -107.2845, -5.0924, -103.95767, -8.43833, -92.91478, -9.66532, -114.67119, -0.87709, -111.42387, 8.02303, -118.57025, 12.25594, -122.13229, 7.70691, -82.54512, -10.26582], "time": 1.5333}, {"vertices": [-245.77676, 15.98849, -87.89369, 16.0349, -87.77768, -76.196, -245.59116, -76.43576, -115.12798, -20.72029, -125.57518, -7.15649, -141.5924, -16.73622, -159.76004, -3.88513, -166.375, -15.66611, -159.3429, -32.90767, -142.97253, -38.74791, -131.5676, -50.50981, -202.28503, -13.44791, -215.14075, -3.91756, -223.51688, -15.42197, -210.97375, -26.01627, -103.28674, -46.69589], "time": 1.8}, {"time": 2.2333}, {"vertices": [-52.77102, 1.72142, -40.02659, -4.52197, -49.78658, -5.98598, -44.90662, -1.59401, -53.70322, 2.0972, -51.85131, -7.08105, -59.56562, 3.56119, -57.05081, -6.30565, -62.91919, 4.7305, -49.80572, 4.92493, -54.29129, 2.59155, -46.8586, 8.166, -61.6431, 5.41821, -43.07431, -1.91365, -45.40737, 10.37445, -44.70687, 20.64722, -44.98818, 10.01154], "time": 2.4333}, {"vertices": [-122.25154, 10.61207, -75.8007, 7.1315, -83.52739, -30.93249, -130.70068, -17.59383, -92.3876, -8.93551, -93.16704, -6.90324, -93.39052, -2.22712, -102.87836, -4.29976, -97.68886, -3.09613, -107.2845, -5.0924, -103.95767, -8.43833, -92.91478, -9.66532, -114.67119, -0.87709, -111.42387, 8.02303, -118.57025, 12.25594, -122.13229, 7.70691, -82.54512, -10.26582], "time": 2.6333}, {"vertices": [-245.77676, 15.98849, -87.89369, 16.0349, -87.77768, -76.196, -245.59116, -76.43576, -115.12798, -20.72029, -125.57518, -7.15649, -141.5924, -16.73622, -159.76004, -3.88513, -166.375, -15.66611, -159.3429, -32.90767, -142.97253, -38.74791, -131.5676, -50.50981, -202.28503, -13.44791, -215.14075, -3.91756, -223.51688, -15.42197, -210.97375, -26.01627, -103.28674, -46.69589], "time": 2.9}]}, "dust5": {"dust": [{"time": 1.1}, {"vertices": [-51.11044, -1.71819, -73.55838, -9.52615, -83.31837, -10.99016, -78.43841, -6.59819, -79.41441, 8.04182, -75.99841, 1.20979, -81.36642, 9.50583, -64.77441, 5.11382, -62.82243, 11.45782, -71.60641, 12.43381, -78.43834, 4.62587, -80.3904, 3.16182, -66.2384, 9.01787, -63.31042, 9.50584, -71.11842, 28.0498, -81.36644, 22.68178, -88.68643, 10.48181], "time": 1.3}, {"vertices": [-166.18073, 8.59418, -112.98862, 5.66617, -120.30864, -39.7178, -177.89264, -24.58981, -119.54456, -6.82208, -113.63242, 0.59796, -129.56425, -1.56223, -126.4698, 4.16675, -129.0573, 0.90666, -129.4606, -7.77051, -135.80841, -12.56743, -132.1591, -17.0276, -149.69133, 0.93498, -155.12686, 6.21434, -163.2359, 8.83621, -161.40732, 0.69888, -120.08951, -24.35994], "time": 1.5}, {"vertices": [-286.86642, 31.56308, -128.98315, 31.60948, -128.86714, -60.62141, -286.68082, -60.86118, -156.21744, -5.14571, -166.66464, 8.41809, -182.68192, -1.16164, -200.84961, 11.68945, -207.46457, -0.09152, -200.43246, -17.33308, -184.06204, -23.17332, -172.65707, -34.93523, -243.37473, 2.12668, -256.2304, 11.65703, -264.60654, 0.15262, -252.06343, -10.44168, -144.3762, -31.12131], "time": 1.7333}, {"time": 2.3}, {"vertices": [-51.11044, -1.71819, -73.55838, -9.52615, -83.31837, -10.99016, -78.43841, -6.59819, -79.41441, 8.04182, -75.99841, 1.20979, -81.36642, 9.50583, -64.77441, 5.11382, -62.82243, 11.45782, -71.60641, 12.43381, -78.43834, 4.62587, -80.3904, 3.16182, -66.2384, 9.01787, -63.31042, 9.50584, -71.11842, 28.0498, -81.36644, 22.68178, -88.68643, 10.48181], "time": 2.5}, {"vertices": [-166.18073, 8.59418, -112.98862, 5.66617, -120.30864, -39.7178, -177.89264, -24.58981, -119.54456, -6.82208, -113.63242, 0.59796, -129.56425, -1.56223, -126.4698, 4.16675, -129.0573, 0.90666, -129.4606, -7.77051, -135.80841, -12.56743, -132.1591, -17.0276, -149.69133, 0.93498, -155.12686, 6.21434, -163.2359, 8.83621, -161.40732, 0.69888, -120.08951, -24.35994], "time": 2.7}, {"vertices": [-286.86642, 31.56308, -128.98315, 31.60948, -128.86714, -60.62141, -286.68082, -60.86118, -156.21744, -5.14571, -166.66464, 8.41809, -182.68192, -1.16164, -200.84961, 11.68945, -207.46457, -0.09152, -200.43246, -17.33308, -184.06204, -23.17332, -172.65707, -34.93523, -243.37473, 2.12668, -256.2304, 11.65703, -264.60654, 0.15262, -252.06343, -10.44168, -144.3762, -31.12131], "time": 2.9333}]}, "TayPhai": {"TayPhai": [{"offset": 2, "vertices": [-35.72232, 2.66045, -31.33678, 2.3339, -31.58154, 2.35211, -34.35534, 2.55845], "time": 0}]}, "dust6": {"dust": [{"time": 1.2667}, {"vertices": [-26.46455, 10.04489, -19.85011, -3.34967, -38.55199, -2.57779, -29.20149, -4.3349, -28.50072, 0.24571, -26.11776, -2.35831, -39.39462, 2.26878, -30.06876, 0.66924, -34.26364, 7.01521, -40.25375, 9.10791, -43.17313, -2.61098, -45.12556, 4.8656, -45.50452, 8.48607, -50.95956, 11.20988, -58.76801, 23.04555, -63.42694, 16.00203, -28.27174, -10.16833], "time": 1.5}, {"vertices": [-101.5842, 20.10468, -34.42014, 18.85335, -43.97563, -31.00145, -101.55964, -15.87346, -44.88823, 8.0417, -52.38935, 5.40096, -57.70256, 6.03552, -69.13863, 8.97072, -64.46082, 5.71049, -65.98194, 2.62195, -57.79884, 0.61971, -55.8261, -8.31125, -83.41816, 9.65109, -78.79385, 14.93069, -86.9029, 17.55257, -85.07436, 12.76828, -44.87432, -10.05495], "time": 1.7}, {"vertices": [-201.66187, 36.03452, -43.7788, 36.08092, -43.66278, -56.14998, -201.47626, -56.38974, -71.01308, -0.67427, -81.46029, 12.88953, -97.47756, 3.3098, -115.64525, 16.16089, -122.26022, 4.37991, -115.2281, -12.86165, -98.85768, -18.70189, -87.45271, -30.46379, -158.17032, 6.59811, -171.02585, 16.12846, -179.40198, 4.62405, -166.85901, -5.97025, -59.17184, -26.64987], "time": 2}, {"time": 2.5667}, {"vertices": [-26.46455, 10.04489, -19.85011, -3.34967, -38.55199, -2.57779, -29.20149, -4.3349, -28.50072, 0.24571, -26.11776, -2.35831, -39.39462, 2.26878, -30.06876, 0.66924, -34.26364, 7.01521, -40.25375, 9.10791, -43.17313, -2.61098, -45.12556, 4.8656, -45.50452, 8.48607, -50.95956, 11.20988, -58.76801, 23.04555, -63.42694, 16.00203, -28.27174, -10.16833], "time": 2.8}, {"vertices": [-101.5842, 20.10468, -34.42014, 18.85335, -43.97563, -31.00145, -101.55964, -15.87346, -44.88823, 8.0417, -52.38935, 5.40096, -57.70256, 6.03552, -69.13863, 8.97072, -64.46082, 5.71049, -65.98194, 2.62195, -57.79884, 0.61971, -55.8261, -8.31125, -83.41816, 9.65109, -78.79385, 14.93069, -86.9029, 17.55257, -85.07436, 12.76828, -44.87432, -10.05495], "time": 3}, {"vertices": [-201.66187, 36.03452, -43.7788, 36.08092, -43.66278, -56.14998, -201.47626, -56.38974, -71.01308, -0.67427, -81.46029, 12.88953, -97.47756, 3.3098, -115.64525, 16.16089, -122.26022, 4.37991, -115.2281, -12.86165, -98.85768, -18.70189, -87.45271, -30.46379, -158.17032, 6.59811, -171.02585, 16.12846, -179.40198, 4.62405, -166.85901, -5.97025, -59.17184, -26.64987], "time": 3.3}]}, "Day": {"Day": [{"offset": 2, "vertices": [0.08816, -0.2628, -0.48417, 1.44429, -0.53796, 1.60505, -0.80404, 2.39859, -1.07206, 3.19807, -0.759, 2.26415, 0, 0, 0, 0, -0.32066, 0.95669, -0.7021, 2.09446, -1.09897, 3.27821, -1.146, 3.41855, -1.26741, 3.78075, -1.10969, 3.30995], "curve": [0.25, 0, 0.75, 1], "time": 0}, {"curve": [0.25, 0, 0.75, 1], "time": 0.6667}, {"offset": 2, "vertices": [-1.1897, 3.54887, -1.56541, 4.66956, -1.81581, 5.41671, -1.68871, 5.03744, -1.07206, 3.19807, -0.759, 2.26415, 0, 0, 0, 0, -0.32066, 0.95669, -0.70205, 2.09445, -1.68867, 5.03745, -2.12897, 6.3506, -2.25041, 6.7128, -1.50288, 4.48277], "curve": [0.25, 0, 0.75, 1], "time": 0.8333}, {"offset": 2, "vertices": [-1.56541, 4.66957, -1.63689, 4.88292, -1.88728, 5.63007, -1.63689, 4.88292, -1.51162, 4.50936, -1.25235, 3.73564, 0, 0, 0, 0, -0.81401, 2.42818, -1.13593, 3.38865, -1.63689, 4.88292, -2.20044, 6.56396, -2.77897, 8.28957, -1.87856, 5.60347], "curve": [0.25, 0, 0.75, 1], "time": 1}, {"offset": 2, "vertices": [-1.1897, 3.54887, -1.56541, 4.66956, -1.81581, 5.41671, -1.68871, 5.03744, -1.07206, 3.19807, -0.759, 2.26415, 0, 0, 0, 0, -0.32066, 0.95669, -0.70205, 2.09445, -1.68867, 5.03745, -2.12897, 6.3506, -2.25041, 6.7128, -1.50288, 4.48277], "curve": [0.25, 0, 0.75, 1], "time": 1.1667}, {"curve": [0.25, 0, 0.75, 1], "time": 1.3333}, {"offset": 2, "vertices": [-1.1897, 3.54887, -1.56541, 4.66956, -1.81581, 5.41671, -1.68871, 5.03744, -1.07206, 3.19807, -0.759, 2.26415, 0, 0, 0, 0, -0.32066, 0.95669, -0.70205, 2.09445, -1.68867, 5.03745, -2.12897, 6.3506, -2.25041, 6.7128, -1.50288, 4.48277], "curve": [0.25, 0, 0.75, 1], "time": 1.5}, {"offset": 2, "vertices": [-1.56541, 4.66957, -1.63689, 4.88292, -1.88728, 5.63007, -1.63689, 4.88292, -1.51162, 4.50936, -1.25235, 3.73564, 0, 0, 0, 0, -0.81401, 2.42818, -1.13593, 3.38865, -1.63689, 4.88292, -2.20044, 6.56396, -2.77897, 8.28957, -1.87856, 5.60347], "curve": [0.25, 0, 0.75, 1], "time": 1.6667}, {"offset": 2, "vertices": [-1.1897, 3.54887, -1.56541, 4.66956, -1.81581, 5.41671, -1.68871, 5.03744, -1.07206, 3.19807, -0.759, 2.26415, 0, 0, 0, 0, -0.32066, 0.95669, -0.70205, 2.09445, -1.68867, 5.03745, -2.12897, 6.3506, -2.25041, 6.7128, -1.50288, 4.48277], "curve": [0.25, 0, 0.75, 1], "time": 1.8333}, {"curve": [0.25, 0, 0.75, 1], "time": 2}, {"offset": 2, "vertices": [-1.1897, 3.54887, -1.56541, 4.66956, -1.81581, 5.41671, -1.68871, 5.03744, -1.07206, 3.19807, -0.759, 2.26415, 0, 0, 0, 0, -0.32066, 0.95669, -0.70205, 2.09445, -1.68867, 5.03745, -2.12897, 6.3506, -2.25041, 6.7128, -1.50288, 4.48277], "curve": [0.25, 0, 0.75, 1], "time": 2.1667}, {"offset": 2, "vertices": [-1.56541, 4.66957, -1.63689, 4.88292, -1.88728, 5.63007, -1.63689, 4.88292, -1.51162, 4.50936, -1.25235, 3.73564, 0, 0, 0, 0, -0.81401, 2.42818, -1.13593, 3.38865, -1.63689, 4.88292, -2.20044, 6.56396, -2.77897, 8.28957, -1.87856, 5.60347], "curve": [0.25, 0, 0.75, 1], "time": 2.3333}, {"offset": 2, "vertices": [-1.1897, 3.54887, -1.56541, 4.66956, -1.81581, 5.41671, -1.68871, 5.03744, -1.07206, 3.19807, -0.759, 2.26415, 0, 0, 0, 0, -0.32066, 0.95669, -0.70205, 2.09445, -1.68867, 5.03745, -2.12897, 6.3506, -2.25041, 6.7128, -1.50288, 4.48277], "curve": [0.25, 0, 0.75, 1], "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "time": 2.6667}, {"offset": 2, "vertices": [-1.1897, 3.54887, -1.56541, 4.66956, -1.81581, 5.41671, -1.68871, 5.03744, -1.07206, 3.19807, -0.759, 2.26415, 0, 0, 0, 0, -0.32066, 0.95669, -0.70205, 2.09445, -1.68867, 5.03745, -2.12897, 6.3506, -2.25041, 6.7128, -1.50288, 4.48277], "curve": [0.25, 0, 0.75, 1], "time": 2.8333}, {"offset": 2, "vertices": [-1.56541, 4.66957, -1.63689, 4.88292, -1.88728, 5.63007, -1.63689, 4.88292, -1.51162, 4.50936, -1.25235, 3.73564, 0, 0, 0, 0, -0.81401, 2.42818, -1.13593, 3.38865, -1.63689, 4.88292, -2.20044, 6.56396, -2.77897, 8.28957, -1.87856, 5.60347], "curve": [0.25, 0, 0.75, 1], "time": 3}, {"offset": 2, "vertices": [-1.1897, 3.54887, -1.56541, 4.66956, -1.81581, 5.41671, -1.68871, 5.03744, -1.07206, 3.19807, -0.759, 2.26415, 0, 0, 0, 0, -0.32066, 0.95669, -0.70205, 2.09445, -1.68867, 5.03745, -2.12897, 6.3506, -2.25041, 6.7128, -1.50288, 4.48277], "curve": [0.25, 0, 0.75, 1], "time": 3.1667}, {"time": 3.3333}]}, "dust": {"dust": [{"time": 0.7667}, {"vertices": [-42.27502, 0.56096, -64.72296, -7.24701, -74.48296, -8.71101, -69.60299, -4.31905, -70.57899, 10.32097, -67.16299, 3.48894, -72.53101, 11.78497, -55.939, 7.39296, -53.98701, 13.73697, -62.771, 14.71295, -69.60292, 6.90501, -71.55498, 5.44096, -57.40298, 11.29701, -54.47501, 11.78499, -62.283, 30.32895, -72.53102, 24.96092, -79.85101, 12.76096], "time": 0.9}, {"vertices": [-137.40009, 25.82303, -84.20798, 22.89502, -91.528, -22.48895, -149.112, -7.36096, -90.76392, 10.40677, -84.85178, 17.82681, -100.78362, 15.66663, -97.68916, 21.3956, -100.27666, 18.13551, -100.67996, 9.45834, -107.02777, 4.66142, -103.37846, 0.20125, -120.91069, 18.16383, -126.34622, 23.44319, -134.45526, 26.06506, -132.62668, 17.92773, -91.30886, -7.13109], "time": 1.0333}, {"vertices": [-261.87878, 50.27042, -103.99553, 50.31682, -103.87951, -41.91408, -261.69318, -42.15384, -131.22981, 13.56163, -141.67702, 27.12543, -157.69429, 17.5457, -175.86197, 30.39679, -182.47693, 18.61581, -175.44482, 1.37425, -159.0744, -4.46599, -147.66943, -16.22789, -218.38708, 20.83401, -231.24277, 30.36436, -239.6189, 18.85995, -227.0758, 8.26566, -119.38857, -12.41397], "time": 1.3333}, {"time": 1.8}, {"vertices": [-42.27502, 0.56096, -64.72296, -7.24701, -74.48296, -8.71101, -69.60299, -4.31905, -70.57899, 10.32097, -67.16299, 3.48894, -72.53101, 11.78497, -55.939, 7.39296, -53.98701, 13.73697, -62.771, 14.71295, -69.60292, 6.90501, -71.55498, 5.44096, -57.40298, 11.29701, -54.47501, 11.78499, -62.283, 30.32895, -72.53102, 24.96092, -79.85101, 12.76096], "time": 1.9333}, {"vertices": [-137.40009, 25.82303, -84.20798, 22.89502, -91.528, -22.48895, -149.112, -7.36096, -90.76392, 10.40677, -84.85178, 17.82681, -100.78362, 15.66663, -97.68916, 21.3956, -100.27666, 18.13551, -100.67996, 9.45834, -107.02777, 4.66142, -103.37846, 0.20125, -120.91069, 18.16383, -126.34622, 23.44319, -134.45526, 26.06506, -132.62668, 17.92773, -91.30886, -7.13109], "time": 2.0667}, {"vertices": [-261.87878, 50.27042, -103.99553, 50.31682, -103.87951, -41.91408, -261.69318, -42.15384, -131.22981, 13.56163, -141.67702, 27.12543, -157.69429, 17.5457, -175.86197, 30.39679, -182.47693, 18.61581, -175.44482, 1.37425, -159.0744, -4.46599, -147.66943, -16.22789, -218.38708, 20.83401, -231.24277, 30.36436, -239.6189, 18.85995, -227.0758, 8.26566, -119.38857, -12.41397], "time": 2.3667}]}, "DauNgua": {"DauNgua": [{"curve": "stepped", "time": 0}, {"time": 2.5}]}}}}}}