{"skins": {"default": {"FRFoot": {"FRFoot": {"triangles": [29, 12, 13, 22, 12, 29, 11, 12, 22, 30, 11, 22, 10, 11, 30, 22, 29, 6, 7, 22, 6, 30, 22, 7, 9, 10, 30, 8, 30, 7, 9, 30, 8, 25, 14, 23, 26, 2, 3, 23, 26, 3, 4, 23, 3, 25, 23, 4, 5, 13, 25, 29, 13, 5, 5, 25, 4, 6, 29, 5, 26, 14, 15, 26, 0, 1, 26, 15, 28, 23, 14, 26, 26, 1, 2, 13, 14, 25, 27, 19, 20, 24, 27, 20, 24, 20, 21, 28, 24, 21, 28, 21, 0, 19, 17, 18, 19, 27, 17, 16, 27, 24, 16, 24, 28, 27, 16, 17, 15, 16, 28, 28, 0, 26], "uvs": [0.6684, 0.05109, 0.99999, 0.33959, 0.99999, 0.44429, 0.99999, 0.48033, 0.9647, 0.50774, 0.5986, 0.79211, 0.56812, 0.81579, 0.55605, 0.83855, 0.47045, 1, 0, 1, 0, 0.72947, 1e-05, 0.70598, 0.03362, 0.67829, 0.35354, 0.41469, 0.37863, 0.39401, 0.35211, 0.36262, 0.15874, 0.13378, 0.11954, 0.13507, 0, 0.13221, 0, 0, 0.50977, 0.00828, 0.62128, 0.01009, 0.27163, 0.76471, 0.69033, 0.44226, 0.40965, 0.09753, 0.67896, 0.46602, 0.70425, 0.4193, 0.33405, 0.08194, 0.43992, 0.12144, 0.28724, 0.74526, 0.24055, 0.77886], "vertices": [3, 5, 79.63, 13.31, 0.76486, 6, -11.4, 29.2, 0.22912, 24, -140.47, -77.46, 0.00602, 2, 6, 113.24, 49.4, 0.95165, 24, -57, 17.28, 0.04835, 2, 6, 154.86, 40.28, 0.2816, 24, -19.05, 36.65, 0.7184, 2, 6, 169.19, 37.14, 0.05092, 24, -5.98, 43.32, 0.94908, 3, 6, 179.03, 29.92, 0.04676, 24, 6.2, 44, 0.91901, 27, -97.75, 105.98, 0.03424, 3, 6, 281.11, -44.93, 0.00359, 24, 132.59, 50.97, 0.60704, 27, 15.81, 50.06, 0.38937, 2, 24, 143.11, 51.56, 0.58106, 27, 25.26, 45.4, 0.41894, 2, 24, 152.13, 54.26, 0.50928, 27, 34.45, 43.33, 0.49072, 2, 24, 216.1, 73.47, 8e-05, 27, 99.62, 28.68, 0.99992, 1, 27, 96.94, -37.12, 1, 2, 24, 147.99, -35.26, 0.05396, 27, -13.08, -32.64, 0.94604, 2, 24, 139.47, -39.6, 0.05739, 27, -22.63, -32.25, 0.94261, 3, 6, 218.92, -112.27, 0.06336, 24, 127.29, -40.54, 0.07771, 27, -33.7, -27.09, 0.85893, 3, 6, 123.72, -45.55, 0.66643, 24, 11.38, -49.43, 0.2711, 27, -139.07, 22.03, 0.06247, 2, 6, 116.25, -40.31, 0.71373, 24, 2.28, -50.13, 0.28627, 3, 5, 49.35, -117.54, 0.07782, 6, 102.98, -41.2, 0.67044, 24, -7.4, -59.24, 0.25174, 2, 5, 12.34, -27.88, 0.64522, 6, 6.2, -47.7, 0.35478, 2, 5, 6.94, -29, 0.76273, 6, 5.54, -53.18, 0.23727, 2, 5, -9.82, -29.65, 0.90686, 6, 0.82, -69.28, 0.09314, 1, 5, -15.65, 23.84, 1, 2, 5, 55.66, 28.22, 0.89624, 6, -33.17, 11.24, 0.10376, 2, 5, 71.26, 29.18, 0.87355, 6, -29.11, 26.33, 0.12645, 2, 24, 143.47, 5.13, 0.30926, 27, 2.8, 4.77, 0.69074, 2, 6, 144.77, -1.89, 0.39742, 24, -0.07, -2.33, 0.60258, 3, 5, 45.66, -9.41, 0.74108, 6, -0.69, -10.23, 0.2499, 24, -107.16, -101.13, 0.00902, 3, 6, 153.88, -5.52, 0.37505, 24, 9.27, 0.65, 0.59771, 27, -116.34, 66.7, 0.02724, 3, 5, 100.86, -135.12, 0.03521, 6, 136.06, 2.01, 0.4164, 24, -9.28, -4.85, 0.54839, 3, 5, 34.46, -4.25, 0.78247, 6, -9.15, -19.21, 0.21026, 24, -108, -113.44, 0.00727, 3, 5, 50.93, -18.62, 0.68692, 6, 9.72, -8.18, 0.26003, 24, -100.42, -92.93, 0.05305, 3, 6, 253.15, -83.42, 0.04053, 24, 135.42, 3.48, 0.31668, 27, -5.02, 7.28, 0.64278, 2, 24, 150.58, 3.88, 0.28081, 27, 8.38, 0.19, 0.71919], "type": "mesh", "hull": 22}}, "grow17": {"grow": {"scaleX": -1, "scaleY": -1, "x": 70.4, "width": 75, "y": 6.46, "height": 143}}, "grow16": {"grow": {"scaleX": -1, "scaleY": -1, "x": 70.4, "width": 75, "y": 6.46, "height": 143}}, "grow18": {"grow": {"scaleX": -1, "scaleY": -1, "x": 70.4, "width": 75, "y": 6.46, "height": 143}}, "grow13": {"grow": {"scaleX": -1, "scaleY": -1, "x": 70.4, "width": 75, "y": 6.46, "height": 143}}, "grow12": {"grow": {"scaleX": -1, "scaleY": -1, "x": 70.4, "width": 75, "y": 6.46, "height": 143}}, "grow15": {"grow": {"scaleX": -1, "scaleY": -1, "x": 70.4, "width": 75, "y": 6.46, "height": 143}}, "grow14": {"grow": {"scaleX": -1, "scaleY": -1, "x": 70.4, "width": 75, "y": 6.46, "height": 143}}, "grow11": {"grow": {"scaleX": -1, "scaleY": -1, "x": 70.4, "width": 75, "y": 6.46, "height": 143}}, "grow10": {"grow": {"scaleX": -1, "scaleY": -1, "x": 70.36, "width": 75, "y": 6.46, "height": 143}}, "FLFoot": {"FLFoot": {"triangles": [28, 13, 14, 29, 13, 28, 30, 13, 29, 12, 13, 30, 10, 12, 30, 11, 12, 10, 10, 30, 9, 27, 5, 6, 15, 26, 27, 28, 27, 6, 28, 6, 7, 28, 7, 8, 14, 27, 28, 15, 27, 14, 29, 28, 8, 9, 29, 8, 30, 29, 9, 18, 19, 24, 3, 24, 2, 25, 24, 3, 25, 3, 4, 17, 18, 24, 17, 24, 25, 25, 16, 17, 26, 25, 4, 26, 4, 5, 27, 26, 5, 26, 16, 25, 15, 16, 26, 23, 20, 21, 22, 20, 23, 19, 20, 22, 24, 19, 22, 1, 23, 21, 2, 23, 1, 22, 23, 2, 1, 21, 0, 24, 22, 2], "uvs": [1, 0.18795, 0.42058, 0.1541, 0.36583, 0.15968, 0.37155, 0.18858, 0.49789, 0.34866, 0.52349, 0.38977, 0.57499, 0.42138, 0.76457, 0.61405, 0.76686, 0.66979, 0.75683, 0.71634, 0.39063, 1, 0.19779, 1, 0.31473, 0.77755, 0.32284, 0.74238, 0.30489, 0.71257, 0.09184, 0.49339, 0.04664, 0.44689, 0, 0.39891, 0.01005, 0.10569, 0.05517, 0.0496, 0.13184, 0, 0.99999, 1e-05, 0.23164, 0.09359, 0.305, 0.07548, 0.20163, 0.11964, 0.25675, 0.35567, 0.24898, 0.3991, 0.28186, 0.44325, 0.53754, 0.67362, 0.53446, 0.71494, 0.5292, 0.76145], "vertices": [1, 3, -28.34, 56.64, 1, 2, 3, 86.6, 35.13, 0.8019, 4, 36.54, 48.07, 0.1981, 2, 3, 97.67, 37.16, 0.5042, 4, 36.71, 36.82, 0.4958, 2, 3, 97.2, 50.58, 0.17657, 4, 50.02, 35.07, 0.82343, 2, 4, 127.84, 43.82, 0.94739, 25, -22.14, 47.32, 0.05261, 2, 4, 147.53, 44.73, 0.6807, 25, -2.5, 45.69, 0.3193, 2, 4, 164.04, 51.64, 0.36125, 25, 14.76, 50.43, 0.63875, 1, 25, 111.53, 55.92, 1, 1, 25, 135.97, 47.62, 1, 2, 25, 155.57, 38.44, 0.96681, 26, -2.29, 49.11, 0.03319, 1, 26, 145.2, 19.8, 1, 1, 26, 157.06, -16.9, 1, 1, 26, 51.87, -26.31, 1, 2, 25, 137.54, -47.32, 0.02547, 26, 35.87, -29.77, 0.97453, 2, 25, 123.34, -46.03, 0.1633, 26, 23.85, -37.43, 0.8367, 3, 4, 175.83, -49.9, 0.18059, 25, 13.43, -51.78, 0.8194, 26, -59.62, -109.18, 1e-05, 2, 4, 152.86, -54.1, 0.52541, 25, -9.89, -53, 0.47459, 2, 4, 129.16, -58.43, 0.81256, 25, -33.95, -54.26, 0.18744, 2, 3, 167.47, 8.6, 0.06325, 4, -3, -27.3, 0.93675, 2, 3, 157.15, -16.88, 0.73426, 4, -26.42, -12.9, 0.26574, 2, 3, 140.68, -39.04, 0.99915, 4, -45.55, 7.01, 0.00085, 1, 3, -32.73, -30.27, 1, 2, 3, 122.93, 5.24, 0.87018, 4, 1.06, 17.19, 0.12982, 1, 3, 107.85, -2.39, 1, 2, 3, 129.53, 16.98, 0.29878, 4, 11.55, 8.74, 0.70122, 2, 4, 120.65, -3.98, 0.99762, 25, -35.41, 0.83, 0.00238, 2, 4, 139.95, -9.82, 0.90924, 25, -17.02, -7.43, 0.09076, 2, 4, 161.33, -7.79, 0.03656, 25, 4.45, -8.16, 0.96344, 1, 25, 122.12, 3.86, 1, 2, 25, 139.91, -3.19, 0.41352, 26, 10.77, 6.59, 0.58648, 2, 25, 159.82, -11.47, 0.14917, 26, 31.58, 12.21, 0.85083], "type": "mesh", "hull": 22}}, "blood2": {"blood": {"scaleX": 1.886, "scaleY": 1.886, "x": 0.5, "width": 185, "y": 0.5, "height": 180}}, "BRFoot": {"BRFoot": {"triangles": [2, 3, 0, 2, 0, 1, 3, 4, 5, 3, 5, 0], "uvs": [1, 0.3725, 1, 1, 0, 1, 0, 0.36494, 0, 0, 1, 0], "vertices": [2, 7, 22.06, 12.23, 0.48411, 29, 1.66, 12.28, 0.51589, 1, 29, 53.2, 4.77, 1, 1, 29, 50.6, -13.04, 1, 2, 7, 24.99, -5.54, 0.18966, 29, -1.55, -5.44, 0.81034, 1, 7, -4.7, -11.51, 1, 1, 7, -8.25, 6.14, 1], "type": "mesh", "hull": 6}}, "cut6": {"cut": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-297.37, -560.58, -691.28, -560.58, -691.28, 461.58, -297.37, 461.58], "type": "mesh", "hull": 4}}, "cut7": {"cut": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-297.37, -560.58, -691.28, -560.58, -691.28, 461.58, -297.37, 461.58], "type": "mesh", "hull": 4}}, "cut4": {"cut": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-282.86, -560.58, -705.79, -560.58, -705.79, 461.58, -282.86, 461.58], "type": "mesh", "hull": 4}}, "Body": {"Body": {"triangles": [5, 6, 3, 15, 12, 13, 0, 1, 2, 16, 14, 0, 14, 15, 13, 3, 0, 2, 16, 0, 3, 4, 5, 3, 3, 6, 16, 17, 11, 12, 15, 17, 12, 14, 16, 15, 15, 8, 17, 7, 15, 16, 6, 7, 16, 7, 8, 15, 10, 11, 17, 10, 17, 8, 9, 10, 8], "uvs": [0.73743, 0.17314, 1, 0.05433, 1, 0.40326, 0.80113, 0.65414, 0.75608, 1, 0.41373, 1, 0.29644, 0.8349, 0.25788, 0.80349, 0.23272, 0.81814, 0.13756, 1, 0, 1, 0, 0, 0.47672, 1e-05, 0.58677, 1e-05, 0.63712, 0.03621, 0.26789, 0.32337, 0.32005, 0.42487, 0.23103, 0.27566], "vertices": [1, 11, 96.94, 123.95, 1, 1, 11, 166.85, 202.29, 1, 1, 11, 210.87, 134.78, 1, 1, 11, 178.23, 44.3, 1, 1, 11, 207.3, -32.12, 1, 1, 11, 96.61, -104.3, 1, 2, 10, 1.37, -72.89, 0.84, 11, 37.86, -97.08, 0.16, 2, 10, -6.63, -58.39, 0.91, 11, 21.42, -99.14, 0.09, 1, 10, -16.52, -55.57, 1, 2, 2, -10.38, 119.31, 0.03225, 10, -70.73, -68.8, 0.96775, 1, 10, -114.16, -38.24, 1, 1, 10, 18.77, 150.68, 1, 2, 10, 169.26, 44.79, 0.27, 11, -9.2, 102.48, 0.73, 2, 10, 204, 20.35, 0.12, 11, 26.38, 125.68, 0.88, 2, 10, 215.08, 2.33, 0.04, 11, 47.23, 129.29, 0.96, 1, 10, 60.35, 30.09, 1, 3, 2, 96.64, 13.67, 0.0019, 10, 63.33, -0.67, 0.99261, 11, -6.25, -12.76, 0.0055, 1, 10, 55.06, 47.29, 1], "type": "mesh", "hull": 15}}, "cut5": {"cut": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [-282.86, -560.58, -705.79, -560.58, -705.79, 461.58, -282.86, 461.58], "type": "mesh", "hull": 4}}, "grow2": {"grow": {"x": 0.5, "width": 75, "y": 0.5, "height": 143}}, "grow3": {"grow": {"x": 0.5, "width": 75, "y": 0.5, "height": 143}}, "grow4": {"grow": {"x": 0.5, "width": 75, "y": 0.5, "height": 143}}, "grow5": {"grow": {"x": 0.5, "width": 75, "y": 0.5, "height": 143}}, "grow": {"grow": {"x": 0.5, "width": 75, "y": 0.5, "height": 143}}, "grow6": {"grow": {"x": 0.5, "width": 75, "y": 0.5, "height": 143}}, "grow7": {"grow": {"x": 0.5, "width": 75, "y": 0.5, "height": 143}}, "grow8": {"grow": {"x": 0.5, "width": 75, "y": 0.5, "height": 143}}, "grow9": {"grow": {"x": 0.5, "width": 75, "y": 0.5, "height": 143}}, "BLFoot": {"BLFoot": {"triangles": [7, 18, 6, 18, 5, 6, 8, 16, 7, 16, 4, 7, 7, 4, 18, 18, 4, 5, 16, 17, 4, 8, 9, 16, 9, 17, 16, 9, 10, 17, 4, 17, 3, 17, 15, 3, 3, 15, 2, 17, 10, 15, 15, 14, 2, 2, 14, 1, 1, 12, 0, 1, 14, 12, 10, 11, 15, 15, 11, 14, 14, 11, 12, 12, 13, 0], "uvs": [1, 0.1425, 0.60968, 0.23903, 0.53143, 0.25838, 0.39363, 0.54232, 0.36665, 0.59791, 1, 0.83264, 1, 1, 0.67996, 1, 1e-05, 0.75455, 1e-05, 0.63001, 0, 0.10235, 0.1374, 0.08226, 0.70005, 0, 1, 0, 0.40607, 0.15068, 0.32484, 0.17677, 0.20208, 0.65542, 0.19096, 0.58175, 0.8459, 0.91322], "vertices": [1, 8, 0.83, 30.6, 1, 2, 8, 58.27, 30.74, 0.68296, 9, 5.67, 41.16, 0.31704, 2, 8, 69.79, 30.77, 0.34316, 9, 12.43, 31.84, 0.65684, 2, 9, 93.37, 25.12, 0.84109, 28, 3.72, 40.27, 0.15891, 2, 9, 109.22, 23.81, 0.37911, 28, 10.82, 26.05, 0.62089, 1, 28, 116.02, 27.45, 1, 1, 28, 145.57, -8.7, 1, 1, 28, 113.36, -35.03, 1, 2, 9, 159.13, -17.31, 0.96451, 28, 1.58, -37.96, 0.03549, 1, 9, 124.72, -22.15, 1, 2, 8, 110.58, -39.96, 0.99713, 9, -21.07, -42.64, 0.00287, 1, 8, 92.17, -36.59, 1, 1, 8, 16.77, -22.76, 1, 1, 8, -17.72, -4.57, 1, 1, 8, 70.18, -3.41, 1, 1, 8, 82.92, -1.9, 1, 1, 28, 4.41, 0.08, 1, 2, 9, 107.93, 0.56, 0.98884, 28, -9.71, 15.08, 0.01116, 1, 28, 114.73, -2.64, 1], "type": "mesh", "hull": 14}}, "Tooth7": {"Tooth7": {"triangles": [7, 0, 1, 2, 7, 1, 3, 7, 2, 7, 5, 6, 7, 6, 0, 4, 5, 7, 3, 4, 7], "uvs": [0.61611, 0.29696, 1, 0.38471, 1, 1, 0.35636, 0.99999, 0, 0.85721, 0, 0, 0.40244, 0, 0.36055, 0.51296], "vertices": [2, 17, 26.39, 16.43, 0.13344, 18, 9.37, 13.22, 0.86656, 1, 18, 31.85, 13.95, 1, 1, 18, 35.66, -7.87, 1, 2, 17, 24.23, -12.94, 0.38561, 18, -1.11, -14.3, 0.61439, 1, 17, 3.45, -17.59, 1, 1, 17, -10.35, 10.01, 1, 1, 17, 10.52, 20.45, 1, 2, 17, 16.61, 2.85, 0.76954, 18, -3.89, 3.01, 0.23046], "type": "mesh", "hull": 7}}, "Tooth6": {"Tooth6": {"triangles": [7, 0, 2, 0, 1, 2, 3, 7, 2, 3, 4, 7, 4, 6, 7, 4, 5, 6, 7, 6, 0], "uvs": [0.58734, 0.2552, 1, 0.16097, 1, 1, 0.41976, 1, 0, 0.60234, 0, 0, 0.31362, 0, 0.43651, 0.4982], "vertices": [2, 15, 34.85, 19.12, 0.26154, 16, 1.94, 18.06, 0.73846, 1, 16, 35.59, 31.39, 1, 1, 16, 45.74, -8.45, 1, 2, 15, 39.58, -19.89, 0.61382, 16, -3.19, -20.9, 0.38618, 1, 15, -1.81, -20.33, 1, 1, 15, -15.98, 5.57, 1, 1, 15, 7.96, 18.66, 1, 2, 15, 29.05, 2.38, 0.94352, 16, -7.84, 3.28, 0.05648], "type": "mesh", "hull": 7}}, "Tooth5": {"Tooth5": {"triangles": [3, 1, 2, 1, 3, 8, 3, 4, 8, 4, 5, 8, 8, 0, 1, 8, 6, 7, 8, 5, 6, 8, 7, 0], "uvs": [0.68546, 0.2462, 1, 0.77583, 1, 1, 0.49345, 0.99881, 0.2324, 0.94708, 0, 0.62994, 0, 0, 0.48838, 0, 0.5065, 0.41112], "vertices": [2, 21, 19.8, 12.48, 0.60958, 23, -0.73, 13.27, 0.39042, 1, 23, 21.51, 3.97, 1, 1, 23, 25.14, -2.99, 1, 2, 21, 19.21, -15.48, 0.33141, 23, 3.11, -14.43, 0.66859, 1, 21, 6.5, -17.81, 1, 1, 21, -7.81, -10.88, 1, 1, 21, -14.78, 10.04, 1, 1, 21, 7.92, 17.61, 1, 2, 21, 13.31, 4.24, 0.94397, 23, -5.83, 4.1, 0.05603], "type": "mesh", "hull": 8}}, "Tooth4": {"Tooth4": {"triangles": [6, 0, 1, 2, 6, 1, 2, 3, 6, 3, 4, 6, 6, 5, 0, 6, 4, 5], "uvs": [0.94875, 0.19623, 1, 1, 0.29644, 1, 1e-05, 0.754, 0, 0, 0.62758, 1e-05, 0.39619, 0.36644], "vertices": [2, 22, 33.76, 17.08, 0.4179, 31, 3.26, 23.09, 0.5821, 1, 31, 17.06, -4.27, 1, 2, 22, 16.28, -23.02, 0.62637, 31, -14.21, -17.01, 0.37363, 1, 22, -0.42, -19.73, 1, 1, 22, -11.23, 6.8, 1, 1, 22, 16.67, 18.17, 1, 1, 22, 11.64, 1.08, 1], "type": "mesh", "hull": 6}}, "Tooth3": {"Tooth3": {"triangles": [3, 1, 2, 11, 1, 3, 4, 11, 3, 7, 8, 9, 10, 7, 9, 0, 10, 9, 11, 0, 1, 6, 7, 10, 10, 0, 11, 6, 10, 11, 5, 6, 11, 5, 11, 4], "uvs": [0.46775, 0.33861, 0.70028, 0.51753, 1, 0.16027, 0.99999, 0.63409, 0.69175, 1, 0.39133, 1, 0.20949, 0.87605, 0, 0.2662, 0, 0, 0.31735, 0, 0.27671, 0.40839, 0.65385, 0.74095], "vertices": [2, 14, 29.68, 18.58, 0.87496, 33, -43.39, -22.12, 0.12504, 2, 14, 56.17, 27.65, 0.30015, 33, -16.91, -13.05, 0.69985, 1, 33, -2.79, 23.28, 1, 1, 33, 13.35, 3.42, 1, 2, 14, 71.85, 6.82, 0.3845, 33, -1.23, -33.88, 0.6155, 2, 14, 45.5, -14.59, 0.85193, 33, -27.57, -55.29, 0.14807, 2, 14, 25.33, -22.35, 0.98521, 33, -47.74, -63.05, 0.01479, 1, 14, -13.81, -11.72, 1, 1, 14, -22.87, -0.56, 1, 2, 14, 4.96, 22.05, 0.99971, 33, -68.11, -18.65, 0.00029, 2, 14, 15.3, 2.04, 0.99971, 33, -57.77, -38.66, 0.00029, 2, 14, 59.7, 14.98, 0.42054, 33, -13.37, -25.72, 0.57946], "type": "mesh", "hull": 10}}, "Tooth2": {"Tooth2": {"triangles": [2, 3, 4, 14, 5, 6, 4, 14, 2, 4, 5, 14, 13, 10, 11, 13, 11, 0, 12, 13, 0, 12, 0, 1, 9, 10, 13, 9, 13, 12, 14, 1, 2, 12, 1, 14, 8, 9, 12, 7, 8, 12, 14, 7, 12, 7, 14, 6], "uvs": [0.34472, 0.24091, 0.4296, 0.38737, 0.64181, 0.52091, 1, 0.41752, 1, 0.63291, 0.70312, 1, 0.56872, 1, 0.23155, 1, 0.08535, 0.83106, 0.01651, 0.61999, 0, 0, 0.3235, 0, 0.23154, 0.5166, 0.11129, 0.2926, 0.53807, 0.67168], "vertices": [1, 13, 7.05, 20.28, 1, 2, 13, 17.14, 24.88, 0.80847, 32, -27.21, -32.94, 0.19153, 2, 13, 31.18, 40.93, 0.31142, 32, -13.17, -16.89, 0.68858, 1, 32, -5.31, 16.65, 1, 1, 32, 5.06, 12.43, 1, 2, 13, 56.45, 36.92, 0.23406, 32, 12.1, -20.89, 0.76594, 2, 13, 51.63, 25.1, 0.43559, 32, 7.28, -32.72, 0.56441, 2, 13, 39.55, -4.57, 0.93152, 32, -4.8, -62.38, 0.06848, 2, 13, 26.17, -14.12, 0.99765, 32, -18.18, -71.93, 0.00235, 1, 13, 13.54, -16.03, 1, 1, 13, -16.91, -5.32, 1, 2, 13, -5.31, 23.14, 0.99679, 32, -49.66, -34.67, 0.00321, 2, 13, 16.27, 4.92, 0.98369, 32, -28.08, -52.9, 0.01631, 1, 13, 1.17, -1.27, 1, 2, 13, 34.72, 28.84, 0.51457, 32, -9.63, -28.97, 0.48543], "type": "mesh", "hull": 12}}, "blood": {"blood": {"scaleX": 1.886, "scaleY": 1.886, "x": 0.5, "width": 185, "y": 0.5, "height": 180}}, "Tooth1": {"Tooth1": {"triangles": [2, 12, 1, 2, 4, 12, 4, 2, 3, 9, 7, 8, 9, 8, 0, 11, 9, 0, 11, 0, 1, 9, 6, 7, 10, 9, 11, 10, 6, 9, 12, 10, 11, 5, 10, 12, 6, 10, 5, 1, 12, 11, 5, 12, 4], "uvs": [0.44463, 0.33655, 0.66162, 0.47965, 1, 0.70281, 1, 1, 0.43345, 0.99999, 0.22989, 0.99999, 0, 0.72394, 0, 0, 0.40319, 0, 0.18844, 0.31542, 0.11686, 0.66759, 0.43054, 0.51982, 0.38403, 0.78612], "vertices": [2, 12, 15.07, 22.97, 0.83416, 30, -37.86, -22.54, 0.16584, 2, 12, 29.59, 36.42, 0.50825, 30, -23.34, -9.1, 0.49175, 1, 30, -0.71, 11.87, 1, 1, 30, 11.39, 5.51, 1, 2, 12, 41.63, 7.91, 0.67954, 30, -11.3, -37.61, 0.32046, 2, 12, 33.48, -7.58, 0.9237, 30, -19.46, -53.1, 0.0763, 2, 12, 13.03, -19.16, 1, 30, -39.9, -64.68, 0, 1, 12, -16.44, -3.66, 1, 1, 12, -0.29, 27.03, 1, 2, 12, 3.95, 3.93, 0.9972, 30, -48.98, -41.59, 0.0028, 2, 12, 15.42, -9.06, 0.99823, 30, -37.51, -54.58, 0.00177, 2, 12, 21.97, 17.97, 0.73982, 30, -30.97, -27.54, 0.26018, 2, 12, 30.94, 8.73, 0.77645, 30, -21.99, -36.79, 0.22355], "type": "mesh", "hull": 9}}, "Tooth8": {"Tooth8": {"triangles": [1, 4, 0, 3, 4, 1, 3, 1, 2, 5, 6, 7, 5, 7, 0, 4, 5, 0], "uvs": [0.55719, 0.07798, 0.99999, 0.48981, 1, 1, 0.8482, 1, 0.44768, 0.84356, 0, 0.77781, 0, 0, 0.43934, 0], "vertices": [2, 19, 18.47, 15.65, 0.60551, 20, -4.16, 15.28, 0.39449, 1, 20, 23.13, 5.79, 1, 1, 20, 26.85, -12.2, 1, 2, 19, 39.3, -14.9, 0.00051, 20, 18.53, -13.92, 0.99949, 2, 19, 16.3, -12.5, 0.68797, 20, -4.58, -12.95, 0.31203, 1, 19, -8.86, -13.7, 1, 1, 19, -12.82, 14.02, 1, 1, 19, 11.54, 17.5, 1], "type": "mesh", "hull": 8}}}}, "skeleton": {"width": 893.49, "spine": "3.7.94", "hash": "A9c7LAiSdij86rx0PDENTFW+nY8", "height": 1488.33}, "slots": [{"attachment": "BRFoot", "name": "BRFoot", "bone": "BRFoot"}, {"attachment": "FRFoot", "name": "FRFoot", "bone": "FRFoot"}, {"attachment": "BLFoot", "name": "BLFoot", "bone": "BLFoot"}, {"attachment": "Body", "name": "Body", "bone": "Body"}, {"attachment": "Tooth8", "name": "Tooth8", "bone": "Tooth11"}, {"attachment": "Tooth7", "name": "Tooth7", "bone": "Tooth9"}, {"attachment": "Tooth6", "name": "Tooth6", "bone": "Tooth7"}, {"attachment": "Tooth5", "name": "Tooth5", "bone": "Tooth13"}, {"attachment": "Tooth4", "name": "Tooth4", "bone": "Tooth14"}, {"attachment": "Tooth3", "name": "Tooth3", "bone": "Tooth5"}, {"attachment": "Tooth2", "name": "Tooth2", "bone": "Tooth3"}, {"attachment": "Tooth1", "name": "Tooth1", "bone": "Tooth1"}, {"attachment": "FLFoot", "name": "FLFoot", "bone": "FLFoot"}, {"name": "grow", "bone": "Grow"}, {"name": "grow10", "bone": "Grow"}, {"name": "grow11", "bone": "Grow2"}, {"name": "grow12", "bone": "Grow3"}, {"name": "grow13", "bone": "Grow4"}, {"name": "grow14", "bone": "Grow5"}, {"name": "grow15", "bone": "Grow6"}, {"name": "grow16", "bone": "Grow7"}, {"name": "grow17", "bone": "Grow8"}, {"name": "grow18", "bone": "Grow9"}, {"name": "grow2", "bone": "Grow2"}, {"name": "grow3", "bone": "Grow3"}, {"name": "grow4", "bone": "Grow4"}, {"name": "grow5", "bone": "Grow5"}, {"name": "grow6", "bone": "Grow6"}, {"name": "grow7", "bone": "Grow7"}, {"name": "grow8", "bone": "Grow8"}, {"name": "grow9", "bone": "Grow9"}, {"color": "ffffff00", "attachment": "blood", "name": "blood", "bone": "bone"}, {"color": "ffffff00", "attachment": "blood", "name": "blood2", "bone": "bone2"}, {"color": "ffffff00", "attachment": "cut", "name": "cut4", "bone": "bone10"}, {"color": "ffffff00", "attachment": "cut", "name": "cut6", "bone": "bone14"}, {"color": "ffffff00", "attachment": "cut", "name": "cut5", "bone": "bone12"}, {"color": "ffffff00", "attachment": "cut", "name": "cut7", "bone": "bone16"}], "ik": [{"bones": ["BLFoot3"], "name": "IK_LBFoot", "order": 5, "target": "IK_LBFoot"}, {"bones": ["BLFoot", "BLFoot2"], "name": "IK_LBFoot_Upper", "order": 4, "target": "IK_BFoot_Upper"}, {"bones": ["FLFoot4"], "name": "IK_LFoot", "order": 3, "target": "IK_LFoot"}, {"bones": ["FLFoot2", "FLFoot3"], "name": "IK_LFoot_Upper", "order": 2, "target": "IK_LFoot_Upper"}, {"bones": ["BRFoot", "BRFoot2"], "name": "IK_RBFoot", "order": 6, "target": "IK_RBFoot"}, {"bones": ["FRFoot4"], "name": "IK_RFoot", "order": 1, "target": "IK_RFoot"}, {"bendPositive": false, "bones": ["FRFoot2", "FRFoot3"], "name": "IK_RFoot_Upper", "order": 0, "target": "IK_RFoot_Upper"}], "bones": [{"name": "root"}, {"parent": "root", "name": "All"}, {"parent": "All", "rotation": 90, "name": "Body", "length": 48.17, "x": 31.32, "y": 13.58}, {"parent": "Body", "rotation": 46.88, "name": "FLFoot", "length": 134.13, "x": 67.41, "y": 26.14}, {"parent": "FLFoot", "rotation": 99.51, "name": "FLFoot2", "length": 150.2, "x": 140.06, "y": 7.04}, {"parent": "Body", "rotation": -53.97, "name": "FRFoot", "length": 52.84, "x": 91.87, "y": -100.79}, {"parent": "FRFoot", "rotation": -71.42, "name": "FRFoot2", "length": 138.2, "x": 55.58, "y": -6.8}, {"scaleX": 1.225, "parent": "Body", "scaleY": 1.225, "rotation": 140.01, "name": "BRFoot", "length": 22.6, "x": 9.56, "y": 23.35}, {"parent": "Body", "rotation": 64.8, "name": "BLFoot", "length": 87.59, "x": 70.7, "y": 108.1}, {"parent": "BLFoot", "rotation": 54.19, "name": "BLFoot2", "length": 126.48, "x": 88.34, "y": 2.07}, {"parent": "Body", "rotation": -71.57, "name": "Body2", "length": 72.26, "x": 77.25, "y": 73.96}, {"parent": "Body2", "rotation": -68.24, "name": "Body3", "length": 133.08, "x": 77.5, "y": -1.74}, {"parent": "Body3", "rotation": -12.44, "name": "Tooth1", "length": 12.71, "x": 115.31, "y": -24.82}, {"parent": "Body3", "rotation": -18.03, "name": "Tooth3", "length": 18.43, "x": 131.66, "y": -48.56}, {"parent": "Body3", "rotation": 10.71, "name": "Tooth5", "length": 19.26, "x": 165.03, "y": -56.07}, {"parent": "Body3", "rotation": 21.12, "name": "Tooth7", "length": 33.55, "x": 185.38, "y": -29.75}, {"parent": "Tooth7", "rotation": 14.41, "name": "Tooth8", "length": 21.94, "x": 37.46, "y": 1.15}, {"parent": "Body3", "rotation": 23.24, "name": "Tooth9", "length": 15.63, "x": 183.2, "y": 0.16}, {"parent": "Tooth9", "rotation": 16.64, "name": "Tooth10", "length": 18.92, "x": 21.2, "y": 1.08}, {"parent": "Body3", "rotation": 41.67, "name": "Tooth11", "length": 16.48, "x": 166.25, "y": 24.85}, {"parent": "Tooth11", "rotation": -3.56, "name": "Tooth12", "length": 13.8, "x": 21.67, "y": 0.14}, {"parent": "Body3", "rotation": 31.37, "name": "Tooth13", "length": 14.74, "x": 139.53, "y": 35.84}, {"parent": "Body3", "rotation": 27.64, "name": "Tooth14", "length": 13.59, "x": 114.02, "y": 12.86}, {"parent": "Tooth13", "rotation": -9.12, "name": "Tooth16", "length": 12.09, "x": 18.42, "y": -0.74}, {"parent": "FRFoot2", "rotation": -39.41, "name": "FRFoot3", "length": 144.14, "x": 146.31, "y": -0.13}, {"parent": "FLFoot2", "rotation": 7.37, "name": "FLFoot3", "length": 126.89, "x": 155.87, "y": -0.27}, {"parent": "FLFoot3", "rotation": -37.69, "name": "FLFoot4", "length": 136.57, "x": 127.36, "y": -1.83}, {"parent": "FRFoot3", "rotation": 29.38, "name": "FRFoot4", "length": 84.28, "x": 143.37, "y": -0.4}, {"parent": "BLFoot2", "rotation": 58.74, "name": "BLFoot3", "length": 131.47, "x": 125.86, "y": 1.04}, {"parent": "BRFoot", "rotation": 19.65, "name": "BRFoot2", "length": 46.89, "x": 24.63, "y": 0.1}, {"parent": "Tooth1", "name": "Tooth2", "x": 52.93, "y": 45.52}, {"parent": "Tooth14", "name": "Tooth15", "x": 30.5, "y": -6.01}, {"parent": "Tooth3", "name": "Tooth4", "x": 44.35, "y": 57.81}, {"parent": "Tooth5", "name": "Tooth6", "x": 73.07, "y": 40.7}, {"parent": "root", "name": "IK_RFoot", "x": 397.79, "y": -153.18}, {"parent": "IK_RFoot", "name": "IK_RFoot_Upper", "x": -62.24, "y": 64.57}, {"parent": "root", "rotation": 29.93, "name": "IK_LFoot", "x": -311.38, "y": -152.29}, {"parent": "IK_LFoot", "name": "IK_LFoot_Upper", "x": 109.36, "y": 26.64}, {"parent": "root", "rotation": -4.42, "name": "IK_LBFoot", "x": -298.94, "y": -65.45}, {"parent": "IK_LBFoot", "name": "IK_BFoot_Upper", "x": 20.32, "y": 124.97}, {"parent": "root", "name": "IK_RBFoot", "x": -24.94, "y": -55.65}, {"scaleX": 2.421, "parent": "All", "scaleY": 2.3, "shearX": 2.82, "name": "Grow", "x": 69.69, "y": -28.06, "shearY": -0.49}, {"scaleX": 2.42, "parent": "All", "scaleY": 2.3, "shearX": 2.82, "name": "Grow2", "x": 69.69, "y": -28.06, "shearY": -0.49}, {"scaleX": 2.42, "parent": "All", "scaleY": 2.3, "shearX": 2.82, "name": "Grow3", "x": 69.69, "y": -28.06, "shearY": -0.49}, {"scaleX": 2.42, "parent": "All", "scaleY": 2.3, "shearX": 2.82, "name": "Grow4", "x": 69.69, "y": -28.06, "shearY": -0.49}, {"scaleX": 2.42, "parent": "All", "scaleY": 2.3, "shearX": 2.82, "name": "Grow5", "x": 69.69, "y": -28.06, "shearY": -0.49}, {"scaleX": 2.42, "parent": "All", "scaleY": 2.3, "shearX": 2.82, "name": "Grow6", "x": 69.69, "y": -28.06, "shearY": -0.49}, {"scaleX": 2.42, "parent": "All", "scaleY": 2.3, "shearX": 2.82, "name": "Grow7", "x": 69.69, "y": -28.06, "shearY": -0.49}, {"scaleX": 2.42, "parent": "All", "scaleY": 2.3, "shearX": 2.82, "name": "Grow8", "x": 69.69, "y": -28.06, "shearY": -0.49}, {"scaleX": 2.42, "parent": "All", "scaleY": 2.3, "shearX": 2.82, "name": "Grow9", "x": 69.69, "y": -28.06, "shearY": -0.49}, {"scaleX": -0.564, "parent": "root", "scaleY": 1.15, "rotation": -6.86, "name": "bone6", "x": 395.71, "y": 653.54}, {"scaleX": 0.382, "parent": "bone6", "rotation": 22.9, "name": "bone9", "x": 223.5, "y": -261.55}, {"parent": "bone9", "rotation": -65.77, "name": "bone10", "x": 241.25, "y": 1.79}, {"scaleX": 0.382, "parent": "bone6", "rotation": 22.9, "name": "bone11", "x": 223.5, "y": -261.55}, {"parent": "bone11", "rotation": -65.77, "name": "bone12", "x": 241.25, "y": 1.79}, {"scaleX": 0.55, "parent": "root", "scaleY": 1.15, "rotation": 20.75, "name": "bone7", "x": -211.24, "y": 536.42}, {"scaleX": 0.382, "parent": "bone7", "rotation": 22.9, "name": "bone13", "x": 223.5, "y": -261.55}, {"parent": "bone13", "rotation": -65.77, "name": "bone14", "x": 241.25, "y": 1.79}, {"scaleX": 0.382, "parent": "bone7", "rotation": 22.9, "name": "bone15", "x": 223.5, "y": -261.55}, {"parent": "bone15", "rotation": -65.77, "name": "bone16", "x": 241.25, "y": 1.79}, {"scaleX": 1.033, "parent": "root", "scaleY": 1.033, "name": "bone", "x": 160.77, "y": 54.83}, {"scaleX": 0.782, "parent": "root", "scaleY": 0.782, "rotation": 24.76, "name": "bone2", "x": -52.71, "y": 200.71}], "animations": {"animation": {"slots": {"FRFoot": {"color": [{"color": "ffffffff", "time": 2.0333}, {"color": "ff1f1fff", "time": 2.1}, {"color": "ffffffff", "time": 2.2}, {"color": "ff1f1fff", "time": 2.3}]}, "grow17": {"color": [{"color": "ffffff00", "time": 1.3333}, {"color": "ffffffff", "time": 1.4333}, {"color": "ffffff00", "time": 1.7667}], "attachment": [{"name": "grow", "time": 1.3333}]}, "grow16": {"color": [{"color": "ffffff00", "time": 1.2333}, {"color": "ffffffff", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}], "attachment": [{"name": "grow", "time": 1.2333}]}, "grow18": {"color": [{"color": "ffffff00", "time": 1.4333}, {"color": "ffffffff", "time": 1.5333}, {"color": "ffffff00", "time": 1.8667}], "attachment": [{"name": "grow", "time": 1.4333}]}, "grow13": {"color": [{"color": "ffffff00", "time": 0.9333}, {"color": "ffffffff", "time": 1.0333}, {"color": "ffffff00", "time": 1.3667}], "attachment": [{"name": "grow", "time": 0.9333}]}, "grow12": {"color": [{"color": "ffffff00", "time": 0.8333}, {"color": "ffffffff", "time": 0.9333}, {"color": "ffffff00", "time": 1.2667}], "attachment": [{"name": "grow", "time": 0.8333}]}, "grow15": {"color": [{"color": "ffffff00", "time": 1.1333}, {"color": "ffffffff", "time": 1.2333}, {"color": "ffffff00", "time": 1.5667}], "attachment": [{"name": "grow", "time": 1.1333}]}, "grow14": {"color": [{"color": "ffffff00", "time": 1.0333}, {"color": "ffffffff", "time": 1.1333}, {"color": "ffffff00", "time": 1.4667}], "attachment": [{"name": "grow", "time": 1.0333}]}, "grow11": {"color": [{"color": "ffffff00", "time": 0.7333}, {"color": "ffffffff", "time": 0.8333}, {"color": "ffffff00", "time": 1.1667}], "attachment": [{"name": "grow", "time": 0.7333}]}, "grow10": {"color": [{"color": "ffffff00", "time": 0.6667}, {"color": "ffffffff", "time": 0.7333}, {"color": "ffffff00", "time": 1.0667}], "attachment": [{"name": "grow", "time": 0.6667}]}, "FLFoot": {"color": [{"color": "ffffffff", "time": 2.0333}, {"color": "ff4848ff", "time": 2.1}, {"color": "ffffffff", "time": 2.2}, {"color": "ff4848ff", "time": 2.3}]}, "blood2": {"color": [{"color": "ffffff00", "time": 2.0333}, {"color": "ffffffff", "time": 2.1}]}, "BRFoot": {"color": [{"color": "ffffffff", "time": 2.0333}, {"color": "ff1f1fff", "time": 2.1}, {"color": "ffffffff", "time": 2.2}, {"color": "ff1f1fff", "time": 2.3}]}, "cut6": {"color": [{"color": "000000ff", "time": 2.0333}]}, "cut7": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 2.0333}, {"color": "ffffffff", "curve": [0.38, 0.01, 0.475, 1], "time": 2.2333}, {"color": "ffffff00", "time": 2.3}]}, "cut4": {"color": [{"color": "ffffff00", "time": 1.9}, {"color": "000000ff", "time": 1.9333}]}, "Body": {"color": [{"color": "ffffffff", "time": 1.9333}, {"color": "ff3c3cff", "time": 2}, {"color": "ffffffff", "time": 2.0333}, {"color": "ff3c3cff", "time": 2.0667}, {"color": "ffffffff", "time": 2.1}, {"color": "ff3c3cff", "time": 2.1333}, {"color": "ffffffff", "time": 2.2}]}, "cut5": {"color": [{"color": "ffffff00", "time": 1.9}, {"color": "ffffffff", "curve": "stepped", "time": 1.9333}, {"color": "ffffffff", "curve": [0.38, 0.01, 0.475, 1], "time": 2.2}, {"color": "ffffff00", "time": 2.2667}]}, "grow2": {"color": [{"color": "ffffff00", "time": 0.7333}, {"color": "ffffffff", "time": 0.8333}, {"color": "ffffff00", "time": 1.1667}], "attachment": [{"name": "grow", "time": 0.7333}]}, "grow3": {"color": [{"color": "ffffff00", "time": 0.8333}, {"color": "ffffffff", "time": 0.9333}, {"color": "ffffff00", "time": 1.2667}], "attachment": [{"name": "grow", "time": 0.8333}]}, "grow4": {"color": [{"color": "ffffff00", "time": 0.9333}, {"color": "ffffffff", "time": 1.0333}, {"color": "ffffff00", "time": 1.3667}], "attachment": [{"name": "grow", "time": 0.9333}]}, "grow5": {"color": [{"color": "ffffff00", "time": 1.0333}, {"color": "ffffffff", "time": 1.1333}, {"color": "ffffff00", "time": 1.4667}], "attachment": [{"name": "grow", "time": 1.0333}]}, "grow": {"color": [{"color": "ffffff00", "time": 0.6667}, {"color": "ffffffff", "time": 0.7333}, {"color": "ffffff00", "time": 1.0667}], "attachment": [{"name": "grow", "time": 0.6667}]}, "grow6": {"color": [{"color": "ffffff00", "time": 1.1333}, {"color": "ffffffff", "time": 1.2333}, {"color": "ffffff00", "time": 1.5667}], "attachment": [{"name": "grow", "time": 1.1333}]}, "grow7": {"color": [{"color": "ffffff00", "time": 1.2333}, {"color": "ffffffff", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}], "attachment": [{"name": "grow", "time": 1.2333}]}, "grow8": {"color": [{"color": "ffffff00", "time": 1.3333}, {"color": "ffffffff", "time": 1.4333}, {"color": "ffffff00", "time": 1.7667}], "attachment": [{"name": "grow", "time": 1.3333}]}, "grow9": {"color": [{"color": "ffffff00", "time": 1.4333}, {"color": "ffffffff", "time": 1.5333}, {"color": "ffffff00", "time": 1.8667}], "attachment": [{"name": "grow", "time": 1.4333}]}, "BLFoot": {"color": [{"color": "ffffffff", "time": 2.0333}, {"color": "ff1f1fff", "time": 2.1}, {"color": "ffffffff", "time": 2.2}, {"color": "ff1f1fff", "time": 2.3}]}, "Tooth7": {"color": [{"color": "ffffffff", "time": 2.0333}, {"color": "ff4848ff", "time": 2.1}, {"color": "ffffffff", "time": 2.2}, {"color": "ff4848ff", "time": 2.3}]}, "Tooth6": {"color": [{"color": "ffffffff", "time": 2.0333}, {"color": "ff4848ff", "time": 2.1}, {"color": "ffffffff", "time": 2.2}, {"color": "ff4848ff", "time": 2.3}]}, "Tooth5": {"color": [{"color": "ffffffff", "time": 2.0333}, {"color": "ff4848ff", "time": 2.1}, {"color": "ffffffff", "time": 2.2}, {"color": "ff4848ff", "time": 2.3}]}, "Tooth4": {"color": [{"color": "ffffffff", "time": 2.0333}, {"color": "ff4848ff", "time": 2.1}, {"color": "ffffffff", "time": 2.2}, {"color": "ff4848ff", "time": 2.3}]}, "Tooth3": {"color": [{"color": "ffffffff", "time": 2.0333}, {"color": "ff4848ff", "time": 2.1}, {"color": "ffffffff", "time": 2.2}, {"color": "ff4848ff", "time": 2.3}]}, "Tooth2": {"color": [{"color": "ffffffff", "time": 2.0333}, {"color": "ff4848ff", "time": 2.1}, {"color": "ffffffff", "time": 2.2}, {"color": "ff4848ff", "time": 2.3}]}, "blood": {"color": [{"color": "ffffff00", "time": 1.9333}, {"color": "ffffffff", "time": 2}]}, "Tooth1": {"color": [{"color": "ffffffff", "time": 2.0333}, {"color": "ff4848ff", "time": 2.1}, {"color": "ffffffff", "time": 2.2}, {"color": "ff4848ff", "time": 2.3}]}, "Tooth8": {"color": [{"color": "ffffffff", "time": 2.0333}, {"color": "ff4848ff", "time": 2.1}, {"color": "ffffffff", "time": 2.2}, {"color": "ff4848ff", "time": 2.3}]}}, "bones": {"Tooth10": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6667}, {"curve": "stepped", "angle": -21.55, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "angle": -21.55, "time": 1.8}, {"angle": 0, "time": 2.0333}]}, "Grow2": {"scale": [{"x": 0, "y": 0, "time": 0.7333}, {"x": 2.304, "y": 2.304, "time": 1.1667}], "translate": [{"x": 0, "y": 0, "time": 0.7333}, {"x": 147.7, "y": -105.34, "time": 1.4}]}, "Tooth16": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6667}, {"curve": "stepped", "angle": -12.79, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "angle": -12.79, "time": 1.8}, {"angle": 0, "time": 2.0333}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.6667}, {"curve": "stepped", "x": -0.59, "y": 3.17, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "x": -0.59, "y": 3.17, "time": 1.8}, {"x": 0, "y": 0, "time": 2.0333}]}, "Tooth15": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6667}, {"curve": "stepped", "angle": 0.22, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "angle": 0.22, "time": 1.8}, {"angle": 0, "time": 2.0333}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.6667}, {"curve": "stepped", "x": -4.6, "y": 4.16, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "x": -4.6, "y": 4.16, "time": 1.8}, {"x": 0, "y": 0, "time": 2.0333}]}, "Tooth14": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6667}, {"curve": "stepped", "angle": 20.98, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "angle": 20.98, "time": 1.8}, {"angle": 0, "time": 2.0333}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.6667}, {"curve": "stepped", "x": -5.92, "y": -7.48, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "x": -5.92, "y": -7.48, "time": 1.8}, {"x": 0, "y": 0, "time": 2.0333}]}, "Tooth13": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6667}, {"curve": "stepped", "angle": 26.88, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "angle": 26.88, "time": 1.8}, {"angle": 0, "time": 2.0333}]}, "Tooth12": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6667}, {"curve": "stepped", "angle": -2.11, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.11, "time": 1.8}, {"angle": 0, "time": 2.0333}]}, "Tooth11": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6667}, {"curve": "stepped", "angle": 6.69, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "angle": 6.69, "time": 1.8}, {"angle": 0, "time": 2.0333}]}, "Grow": {"scale": [{"x": 0, "y": 0, "time": 0.6667}, {"x": 2.304, "y": 2.304, "time": 1.0667}], "translate": [{"x": 0, "y": 0, "time": 0.6667}, {"x": 147.7, "y": -105.34, "time": 1.3}]}, "Body": {"rotate": [{"curve": [0.373, 0.01, 1, 0.73], "angle": 0, "time": 2.3}, {"angle": 3.05, "time": 2.3333}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -15.15, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "x": -18.24, "y": 8.85, "time": 0.6667}, {"curve": "stepped", "x": 7.17, "y": -20.62, "time": 0.8}, {"curve": [0.338, 0.35, 0.671, 0.68], "x": 7.17, "y": -20.62, "time": 1.8333}, {"curve": [0.336, 0.34, 0.669, 0.68], "x": 7.17, "y": -17.69, "time": 1.9}, {"curve": [0.336, 0.34, 0.67, 0.68], "x": 7.17, "y": -61.52, "time": 1.9333}, {"curve": [0.337, 0.35, 0.67, 0.68], "x": 7.17, "y": -22.17, "time": 1.9667}, {"curve": [0.337, 0.35, 0.67, 0.68], "x": 7.17, "y": -21.92, "time": 2}, {"curve": [0.372, 0.62, 0.712, 1], "x": 7.17, "y": -65.52, "time": 2.0333}, {"curve": [0.245, 0, 0.637, 0.56], "x": 7.17, "y": -20.62, "time": 2.1333}, {"curve": "stepped", "x": 2.64, "y": -40.29, "time": 2.2}, {"curve": [0.381, 0.55, 0.742, 1], "x": 2.64, "y": -40.29, "time": 2.2333}, {"curve": [0.297, 0, 0.634, 0.37], "x": -10.17, "y": 0, "time": 2.3}, {"x": -3.69, "y": 12.3, "time": 2.3333}]}, "IK_LBFoot": {"rotate": [{"angle": 0, "time": 2.2667}], "translate": [{"x": 0, "y": 0, "time": 2.2667}]}, "bone16": {"rotate": [{"angle": 0, "time": 1.9333}, {"angle": 95.17, "time": 2.0333}, {"angle": 118.75, "time": 2.2667}]}, "Body2": {"rotate": [{"curve": [0.369, 0.63, 0.706, 1], "angle": -0.13, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.1}, {"curve": [0.253, 0, 0.621, 0.48], "angle": -1.54, "time": 0.6}, {"curve": "stepped", "angle": -0.87, "time": 0.8667}, {"curve": [0.37, 0.47, 0.753, 1], "angle": -0.87, "time": 1.9667}, {"angle": -1.54, "time": 2.1}]}, "Body3": {"rotate": [{"curve": [0.379, 0.6, 0.724, 1], "angle": -0.35, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.1667}, {"curve": [0.25, 0, 0.625, 0.5], "angle": -1.92, "time": 0.6667}, {"curve": [0.334, 0.33, 0.668, 0.67], "angle": -0.96, "time": 0.8667}, {"curve": [0.335, 0.34, 0.669, 0.67], "angle": 1.17, "time": 0.9667}, {"curve": [0.382, 0.55, 0.742, 1], "angle": -0.89, "time": 1.0667}, {"curve": [0.335, 0.34, 0.669, 0.67], "angle": 1.17, "time": 1.1667}, {"curve": [0.382, 0.55, 0.742, 1], "angle": -0.89, "time": 1.2667}, {"curve": [0.335, 0.34, 0.669, 0.67], "angle": 1.17, "time": 1.3667}, {"curve": [0.382, 0.55, 0.742, 1], "angle": -0.89, "time": 1.4667}, {"curve": [0.335, 0.34, 0.669, 0.67], "angle": 1.17, "time": 1.5667}, {"curve": [0.375, 0.5, 0.75, 1], "angle": -0.96, "time": 1.6667}, {"curve": [0.335, 0.34, 0.669, 0.67], "angle": 1.17, "time": 1.7667}, {"curve": [0.324, 0, 0.658, 0.34], "angle": -1.92, "time": 1.8667}, {"curve": [0.282, 0.08, 0.626, 0.46], "angle": -1.91, "time": 1.9}, {"curve": [0.317, 0.28, 0.659, 0.64], "angle": 16.67, "time": 1.9667}, {"curve": [0.259, 0.05, 0.632, 0.53], "angle": -1.91, "time": 2.0333}, {"curve": [0.337, 0.35, 0.674, 0.69], "angle": -0.91, "time": 2.1}, {"angle": 11.87, "time": 2.1667}], "scale": [{"x": 1, "y": 1, "time": 1.9}, {"x": 0.696, "y": 0.844, "time": 1.9667}, {"x": 1, "y": 1, "time": 2.0333}], "translate": [{"x": 0, "y": 0, "time": 1.9}, {"x": -6.17, "y": -19.51, "time": 1.9667}, {"x": 0, "y": 0, "time": 2.0333}]}, "Tooth7": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6667}, {"curve": "stepped", "angle": -18.79, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "angle": -18.79, "time": 1.8}, {"angle": 0, "time": 2.0333}]}, "Tooth6": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6667}, {"curve": "stepped", "angle": -39.08, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "angle": -39.08, "time": 1.8}, {"angle": 0, "time": 2.0333}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.6667}, {"curve": "stepped", "x": -18.69, "y": -21.83, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "x": -18.69, "y": -21.83, "time": 1.8}, {"x": 0, "y": 0, "time": 2.0333}]}, "IK_RFoot": {"rotate": [{"angle": 0, "time": 2.1}, {"angle": -16.19, "time": 2.2333}], "translate": [{"x": 0, "y": 0, "time": 2.1}, {"x": 24.58, "y": 55.3, "time": 2.2333}]}, "bone10": {"rotate": [{"angle": -4, "time": 1.8333}, {"angle": 103.28, "time": 1.9667}, {"angle": 116.58, "time": 2.3}]}, "Tooth5": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6667}, {"curve": "stepped", "angle": -39.35, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "angle": -39.35, "time": 1.8}, {"angle": 0, "time": 2.0333}]}, "bone": {"scale": [{"x": 1, "y": 1, "time": 1.9333}, {"x": 1.673, "y": 1.673, "time": 2}]}, "Tooth4": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6667}, {"curve": "stepped", "angle": -7.97, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "angle": -7.97, "time": 1.8}, {"angle": 0, "time": 2.0333}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.6667}, {"curve": "stepped", "x": 2.85, "y": -30.89, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "x": 2.85, "y": -30.89, "time": 1.8}, {"x": 0, "y": 0, "time": 2.0333}]}, "IK_LFoot": {"rotate": [{"angle": 0, "time": 2.2667}], "translate": [{"x": 0, "y": 0, "time": 2.2667}]}, "Tooth3": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6667}, {"curve": "stepped", "angle": -42.5, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "angle": -42.5, "time": 1.8}, {"angle": 0, "time": 2.0333}]}, "Tooth2": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6667}, {"curve": "stepped", "angle": 22.4, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "angle": 22.4, "time": 1.8}, {"angle": 0, "time": 2.0333}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.6667}, {"curve": "stepped", "x": -21.35, "y": -29.95, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "x": -21.35, "y": -29.95, "time": 1.8}, {"x": 0, "y": 0, "time": 2.0333}]}, "bone14": {"rotate": [{"angle": 0, "time": 1.9333}, {"angle": 85.37, "time": 2.0333}]}, "Tooth1": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6667}, {"curve": "stepped", "angle": -26.66, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "angle": -26.66, "time": 1.8}, {"angle": 0, "time": 2.0333}]}, "bone12": {"rotate": [{"angle": -9.92, "time": 1.8333}, {"angle": 111.1, "time": 1.9667}, {"angle": 130.3, "time": 2.2}]}, "Tooth9": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6667}, {"curve": "stepped", "angle": 5.52, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.52, "time": 1.8}, {"angle": 0, "time": 2.0333}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.6667}, {"curve": "stepped", "x": 0.99, "y": 1.25, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "x": 0.99, "y": 1.25, "time": 1.8}, {"x": 0, "y": 0, "time": 2.0333}]}, "Tooth8": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6667}, {"curve": "stepped", "angle": -14.17, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "angle": -14.17, "time": 1.8}, {"angle": 0, "time": 2.0333}]}, "bone2": {"scale": [{"x": 1, "y": 1, "time": 2.0333}, {"x": 1.673, "y": 1.673, "time": 2.1}]}, "Grow9": {"scale": [{"x": 0, "y": 0, "time": 1.4333}, {"x": 2.304, "y": 2.304, "time": 1.8667}], "translate": [{"x": 0, "y": 0, "time": 1.4333}, {"x": 147.7, "y": -105.34, "time": 2.0667}]}, "Grow7": {"scale": [{"x": 0, "y": 0, "time": 1.2333}, {"x": 2.304, "y": 2.304, "time": 1.6667}], "translate": [{"x": 0, "y": 0, "time": 1.2333}, {"x": 147.7, "y": -105.34, "time": 1.9}]}, "Grow8": {"scale": [{"x": 0, "y": 0, "time": 1.3333}, {"x": 2.304, "y": 2.304, "time": 1.7667}], "translate": [{"x": 0, "y": 0, "time": 1.3333}, {"x": 147.7, "y": -105.34, "time": 2}]}, "Grow5": {"scale": [{"x": 0, "y": 0, "time": 1.0333}, {"x": 2.304, "y": 2.304, "time": 1.4667}], "translate": [{"x": 0, "y": 0, "time": 1.0333}, {"x": 147.7, "y": -105.34, "time": 1.7}]}, "Grow6": {"scale": [{"x": 0, "y": 0, "time": 1.1333}, {"x": 2.304, "y": 2.304, "time": 1.5667}], "translate": [{"x": 0, "y": 0, "time": 1.1333}, {"x": 147.7, "y": -105.34, "time": 1.8}]}, "Grow3": {"scale": [{"x": 0, "y": 0, "time": 0.8333}, {"x": 2.304, "y": 2.304, "time": 1.2667}], "translate": [{"x": 0, "y": 0, "time": 0.8333}, {"x": 147.7, "y": -105.34, "time": 1.5}]}, "Grow4": {"scale": [{"x": 0, "y": 0, "time": 0.9333}, {"x": 2.304, "y": 2.304, "time": 1.3667}], "translate": [{"x": 0, "y": 0, "time": 0.9333}, {"x": 147.7, "y": -105.34, "time": 1.6}]}}}}}