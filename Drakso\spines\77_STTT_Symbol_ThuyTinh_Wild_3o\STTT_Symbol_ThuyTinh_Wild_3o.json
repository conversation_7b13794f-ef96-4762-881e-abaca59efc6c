{"skins": {"default": {"Horn Left": {"Horn Left": {"rotation": -3.34, "x": 66.42, "width": 60, "y": 34.52, "height": 34}}, "Thuy Tinh Mask": {"Thuy Tinh Mask": {"color": "00ff00ff", "vertices": [-83.21, 299.4, -83.22, 181.74, -88.86, 161.82, -89.4, -163.04, -83.01, -182.59, -79.52, -206.78, 82.72, -207.19, 82.57, -183.72, 88.96, -162.66, 88.82, -118.9, 79.15, -109.7, 78.94, 65.98, 201, 100.48, 200.76, 300.67], "end": "Wave Splash Big 1", "type": "clipping", "vertexCount": 14}}, "Wave Long Right 12": {"Wave Long Left": {"scaleX": -1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 80.34, "width": 339, "y": 54.77, "height": 110}}, "Wave Long Right 11": {"Wave Long Left": {"scaleX": 1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 80.34, "width": 339, "y": 54.77, "height": 110}}, "Wave Long Right 10": {"Wave Long Left": {"scaleX": -1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 80.34, "width": 339, "y": 54.77, "height": 110}}, "Sparkle 4": {"Sparkle": {"width": 53, "y": 0.5, "height": 54}}, "Moon": {"Moon": {"width": 94, "height": 94}}, "Sparkle 5": {"Sparkle": {"width": 53, "y": 0.5, "height": 54}}, "Sparkle 2": {"Sparkle": {"width": 53, "y": 0.5, "height": 54}}, "Sparkle 3": {"Sparkle": {"width": 53, "y": 1.13, "height": 54}}, "Sparkle 1": {"Sparkle": {"width": 53, "y": 0.5, "height": 54}}, "thanh-ngangBright2": {"thanh-ngangBright": {"scaleY": -1, "width": 200, "y": -181.91, "height": 39}}, "Wave Splash Big 1": {"Wave Big": {"path": "Wave/Wave Big", "width": 263, "y": 125, "height": 290}}, "Arm Left": {"Arm Left": {"rotation": 77, "x": 33.54, "width": 44, "y": -6.53, "height": 100}}, "Hair Front 2": {"Hair": {"triangles": [2, 1, 3, 1, 0, 3, 0, 4, 3], "uvs": [0.36939, 0.26988, 0.63658, 0.27417, 0.82813, 0.25156, 0.74305, 0, 0.23363, 0], "vertices": [1, 70, 22.2, 26.67, 1, 1, 70, 20.39, -1.6, 1, 2, 70, 22.6, -22.02, 0.792, 80, 6.15, 9.63, 0.208, 1, 70, 57.42, -14.5, 1, 1, 70, 59.77, 39.44, 1], "width": 106, "edges": [0, 2, 4, 6, 6, 8, 2, 4, 8, 0], "type": "mesh", "hull": 5, "height": 137}}, "Hair Front 1": {"Hair": {"triangles": [21, 20, 22, 20, 19, 22, 22, 19, 23, 23, 19, 18, 18, 24, 23, 18, 17, 24, 17, 25, 24, 25, 16, 0, 25, 17, 16, 7, 6, 8, 6, 5, 8, 8, 4, 9, 4, 8, 5, 9, 4, 10, 4, 3, 10, 3, 11, 10, 3, 2, 11, 2, 1, 11, 16, 15, 0, 15, 14, 0, 12, 1, 13, 1, 0, 13, 0, 14, 13, 1, 12, 11], "uvs": [0.36939, 0.26988, 0.63658, 0.27417, 0.6534, 0.40249, 0.68391, 0.51552, 0.69315, 0.6407, 0.73476, 0.76373, 0.85864, 0.90394, 1, 0.87532, 1, 0.69935, 0.98345, 0.5334, 0.87436, 0.4633, 0.82906, 0.36172, 0.82813, 0.25156, 0.74305, 0, 0.23363, 0, 0, 0.27311, 0, 0.4312, 0.02857, 0.57059, 0.07295, 0.71007, 0.13489, 0.85314, 0.24121, 1, 0.44738, 1, 0.45477, 0.82953, 0.48898, 0.6879, 0.46772, 0.53124, 0.42519, 0.39605], "vertices": [1, 70, 22.2, 26.67, 1, 1, 70, 20.39, -1.6, 1, 3, 80, 23.51, -14.59, 0.14906, 81, 5.87, -14.26, 0.85089, 86, 23.02, 43.31, 5e-05, 3, 81, 21.57, -12.32, 0.32116, 82, -2.26, -12.52, 0.66488, 83, -19.22, -12.74, 0.01396, 3, 82, 14.6, -17.53, 0.50085, 83, -2.3, -17.56, 0.44934, 84, -10.64, -24.52, 0.0498, 3, 82, 31.45, -21.97, 0.04153, 83, 14.6, -21.8, 0.48301, 84, 6.44, -21.08, 0.47546, 2, 83, 37.76, -19.53, 0.00984, 84, 26.36, -9.07, 0.99016, 1, 84, 23.3, 6.11, 1, 2, 83, 20.4, 7.09, 0.50845, 84, -0.77, 7.49, 0.49155, 3, 81, 26.89, 22.05, 0.00384, 82, 16.92, 16.49, 0.48493, 83, -0.38, 16.49, 0.51123, 3, 81, 16.36, 11.32, 0.38097, 82, 2.87, 11.14, 0.60419, 83, -14.36, 10.97, 0.01484, 3, 80, 21.1, 7.55, 0.28076, 81, 2.1, 7.69, 0.70768, 82, -11.61, 13.8, 0.01156, 2, 70, 22.6, -22.02, 0.792, 80, 6.15, 9.63, 0.208, 1, 70, 57.42, -14.5, 1, 1, 70, 59.77, 39.44, 1, 2, 70, 23.46, 65.81, 0.816, 86, 18.02, -25.16, 0.184, 4, 86, 39.29, -21.11, 0.11466, 87, 7.58, -27.28, 0.69639, 88, -12.32, -27.34, 0.15913, 89, -20.7, -36.58, 0.02982, 5, 86, 57.49, -14.56, 0.00042, 87, 26.79, -29.47, 0.32989, 88, 6.9, -29.44, 0.42942, 89, -1.91, -32.01, 0.22445, 90, -21.43, -31.52, 0.01581, 4, 87, 46.47, -30.04, 0.04592, 88, 26.57, -29.93, 0.20318, 89, 16.75, -25.76, 0.45537, 90, -2.63, -25.71, 0.29552, 3, 88, 47.21, -28.75, 0.00948, 89, 35.75, -17.62, 0.06005, 90, 16.56, -18.01, 0.93047, 1, 90, 35.99, -5.59, 1, 1, 90, 34.72, 16.22, 1, 2, 89, 29.77, 15.91, 0.10716, 90, 11.36, 15.65, 0.89284, 3, 88, 35.25, 13.42, 0.03327, 89, 10.14, 17.94, 0.82634, 90, -8.22, 18.14, 0.14039, 3, 87, 34.04, 16.83, 0.03594, 88, 13.95, 16.89, 0.80391, 89, -11.07, 13.95, 0.16015, 4, 80, 19.57, -35.49, 0.0007, 86, 26.13, 22.27, 0.06419, 87, 14.98, 17.43, 0.64546, 88, -5.11, 17.41, 0.28965], "width": 106, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50], "type": "mesh", "hull": 26, "height": 137}}, "Trident": {"Trident": {"triangles": [12, 3, 4, 2, 3, 12, 12, 4, 5, 10, 0, 1, 9, 10, 1, 11, 2, 12, 11, 12, 5, 8, 9, 2, 2, 9, 1, 11, 8, 2, 6, 11, 5, 7, 8, 11, 6, 7, 11], "uvs": [0.15126, 0.36187, 0.26313, 0.3523, 0.31276, 0.33318, 0.37041, 0, 0.9109, 0, 1, 0.50629, 0.84874, 1, 0.24666, 1, 0.27689, 0.78737, 0.2534, 0.65548, 0.15126, 0.6296, 0.57161, 0.65984, 0.59286, 0.30623], "vertices": [-20.67, 7.33, 18.43, 15.53, 35.5, 20.7, 49.43, 58.82, 239.21, 93.64, 280.09, 47.08, 236.33, -13.65, 24.92, -52.43, 31.5, -28.52, 20.76, -16.41, -15.6, -20.32, 132.58, 3.63, 133.34, 41.52], "width": 357, "edges": [4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20, 16, 4, 0, 2, 2, 4, 18, 2, 8, 10, 10, 12], "type": "mesh", "hull": 11, "height": 105}}, "Wild Bright": {"Wild Bright": {"scaleX": 1.165, "scaleY": 1.165, "x": 0.15, "width": 192, "y": 7.04, "height": 55}}, "Hair Behind": {"Hair": {"triangles": [6, 5, 7, 5, 4, 7, 4, 8, 7, 8, 4, 3, 8, 3, 9, 3, 2, 9, 2, 10, 9, 0, 11, 10, 2, 1, 10, 1, 0, 10], "uvs": [0.63658, 0.27417, 0.6534, 0.40249, 0.68391, 0.51552, 0.69315, 0.6407, 0.73476, 0.76373, 0.85864, 0.90394, 1, 0.87532, 1, 0.69935, 0.92818, 0.54833, 0.86099, 0.49556, 0.79558, 0.38648, 0.78305, 0.23226], "vertices": [2, 70, 20.39, 2.79, 0.15295, 103, 5.84, -15.27, 0.84705, 1, 70, 2.87, 5.62, 1, 4, 70, -12.74, 3.06, 0.24476, 104, 21.36, -17.62, 0.26348, 105, -4.66, -17.24, 0.48588, 106, -21.56, -17.5, 0.00588, 4, 70, -30.04, -0.11, 0.00623, 105, 12.2, -22.26, 0.57972, 106, -4.64, -22.31, 0.36581, 107, -10.72, -29.82, 0.04825, 3, 105, 29.6, -25.61, 0.07461, 106, 12.8, -25.46, 0.43308, 107, 6.38, -25.16, 0.49231, 2, 106, 37.76, -19.53, 0.00745, 107, 26.36, -9.07, 0.99255, 1, 107, 23.3, 6.11, 1, 2, 106, 20.4, 7.09, 0.48174, 107, -0.77, 7.49, 0.51826, 3, 104, 28.45, 16.04, 0.02049, 105, 15.82, 10.39, 0.53501, 106, -1.4, 10.37, 0.4445, 3, 104, 20.68, 9.93, 0.49657, 105, 6.22, 8.07, 0.48718, 106, -10.98, 7.94, 0.01626, 3, 103, 23.85, 2.82, 0.30167, 104, 5.13, 3.14, 0.69666, 105, -10.75, 8.39, 0.00167, 1, 103, 2.81, 4.89, 1], "width": 106, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 0], "type": "mesh", "hull": 12, "height": 137}}, "Eyes": {"Eyes": {"triangles": [13, 0, 3, 14, 13, 3, 10, 11, 13, 14, 10, 13, 13, 11, 12, 6, 11, 10, 5, 11, 6, 12, 0, 13, 11, 15, 12, 15, 11, 4, 5, 4, 11, 7, 5, 6, 4, 5, 7, 8, 15, 4, 9, 7, 6, 8, 4, 7, 9, 8, 7, 9, 6, 10, 2, 14, 3, 1, 0, 12, 1, 12, 15, 1, 15, 8, 2, 1, 8, 2, 9, 10, 2, 10, 14, 2, 8, 9], "path": "Face", "uvs": [0, 0, 0, 0.67758, 0.99999, 0.6678, 1, 0, 0.55631, 0.47369, 0.5819, 0.45074, 0.6912, 0.45369, 0.6433, 0.47927, 0.55027, 0.49664, 0.64782, 0.4998, 0.78875, 0.43094, 0.58409, 0.42668, 0.51432, 0.45783, 0.57754, 0.41437, 0.82313, 0.4183, 0.52731, 0.4724], "vertices": [46.67, 32.22, 5.38, 34.01, 4.1, -8.97, 44.8, -10.74, 16.76, 9.57, 18.11, 8.41, 17.73, 3.73, 16.26, 5.85, 15.38, 9.89, 15, 5.71, 18.93, -0.53, 19.58, 8.26, 17.81, 11.33, 20.34, 8.5, 19.64, -2.04, 16.9, 10.82], "width": 43, "edges": [0, 2, 2, 4, 4, 6, 0, 6, 8, 14, 14, 12, 12, 10, 10, 8, 22, 30, 30, 16, 16, 18, 18, 20, 20, 22], "type": "mesh", "hull": 4, "height": 61}}, "thanh-ngangBright": {"thanh-ngangBright": {"width": 200, "y": 218.96, "height": 39}}, "thanh-ngang": {"thanh-ngang": {"width": 200, "y": -3.2, "height": 39}}, "Loincloth": {"Loincloth": {"triangles": [14, 10, 9, 13, 10, 14, 11, 10, 13, 11, 13, 12, 14, 9, 15, 9, 16, 15, 8, 4, 9, 5, 4, 8, 7, 6, 5, 8, 7, 5, 21, 2, 1, 21, 1, 0, 19, 18, 17, 19, 17, 16, 20, 2, 21, 16, 9, 20, 16, 20, 19, 9, 2, 20, 3, 2, 9, 4, 3, 9], "uvs": [0.34924, 0, 0.29779, 0.02906, 0.29931, 0.14826, 0.17977, 0.30827, 0.08142, 0.51726, 0, 0.79646, 0, 1, 0.18129, 1, 0.4022, 0.80586, 0.58273, 0.5862, 0.65337, 0.79443, 0.91665, 1, 1, 1, 1, 0.7781, 0.90757, 0.66054, 0.82889, 0.48257, 0.79106, 0.28827, 0.77744, 0.1103, 0.71995, 0.1152, 0.73659, 0.17234, 0.6226, 0.1586, 0.38253, 0.05152], "vertices": [2, 67, 28.12, 59.16, 0.5, 68, -12.83, 58.92, 0.5, 1, 67, 21.39, 67.8, 1, 1, 67, 0.62, 63.96, 1, 4, 67, -31.05, 82.05, 0.25934, 71, 38.38, -25.42, 0.25999, 72, 3.95, -25.42, 0.48033, 73, -30.47, -25.42, 0.00034, 2, 72, 45.41, -23.89, 0.22379, 73, 10.99, -23.89, 0.77621, 1, 73, 61.75, -13.5, 1, 1, 73, 93.24, 3.98, 1, 2, 72, 110.86, 34.26, 0.0007, 73, 76.44, 34.26, 0.9993, 4, 67, -110.88, 25.57, 0.00565, 71, 94.76, 54.47, 6e-05, 72, 60.34, 54.47, 0.19945, 73, 25.91, 54.47, 0.79484, 1, 67, -66.83, -2.02, 1, 4, 67, -100.94, -21.41, 0.001, 74, 85.05, -39.44, 0.00026, 75, 47.73, -39.44, 0.39425, 76, 10.41, -39.44, 0.60449, 1, 76, 61.12, -3.65, 1, 1, 76, 66.28, 11.42, 1, 1, 76, 29.12, 24.13, 1, 2, 75, 41.03, 14.17, 0.24539, 76, 3.71, 14.17, 0.75461, 2, 74, 43.68, 10.15, 0.0746, 75, 6.36, 10.15, 0.9254, 1, 74, 8.8, 14.46, 1, 1, 67, 22.35, -25.12, 1, 1, 67, 19.68, -14.44, 1, 1, 67, 10.23, -19.25, 1, 2, 67, 9.03, 2.68, 0.928, 68, -30.77, 2.05, 0.072, 2, 67, 20.15, 51.17, 0.65, 68, -20.64, 50.77, 0.35], "width": 191, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42], "type": "mesh", "hull": 22, "height": 177}}, "Wave Long Left 7": {"Wave Long Left": {"scaleX": 1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 68.64, "width": 339, "y": 44.53, "height": 110}}, "Wave Long Left 6": {"Wave Long Left": {"scaleX": -1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 80.34, "width": 339, "y": 54.77, "height": 110}}, "Wave Long Left 5": {"Wave Long Left": {"scaleX": 1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 80.34, "width": 339, "y": 54.77, "height": 110}}, "Wave Long Left 4": {"Wave Long Left": {"scaleX": -1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 80.34, "width": 339, "y": 54.77, "height": 110}}, "Trident Holder": {"Trident Holder": {"scaleX": 0.848, "scaleY": 0.848, "rotation": 9.51, "x": -65.22, "width": 127, "y": -15.48, "height": 21}}, "Frame Light Left": {"Frame Light": {"x": 0.5, "width": 31, "y": 0.5, "height": 225}}, "Wave Long Left 9": {"Wave Long Left": {"scaleX": 1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 80.34, "width": 339, "y": 54.77, "height": 110}}, "Ear": {"Ear": {"rotation": -92.49, "x": 13.4, "width": 9, "y": -7.95, "height": 18}}, "Wave Long Left 8": {"Wave Long Left": {"scaleX": -1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 80.34, "width": 339, "y": 54.77, "height": 110}}, "Wave Long Left 3": {"Wave Long Left": {"scaleX": 1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 80.34, "width": 339, "y": 54.77, "height": 110}}, "thanh-docBright": {"thanh-docBright": {"x": -85.75, "width": 23, "y": 18.17, "height": 381}}, "Fabric": {"Fabric": {"triangles": [1, 2, 17, 1, 17, 0, 3, 4, 16, 2, 3, 16, 17, 2, 16, 16, 4, 15, 15, 5, 14, 4, 5, 15, 14, 6, 13, 5, 6, 14, 12, 7, 11, 13, 7, 12, 6, 7, 13, 7, 8, 11, 8, 10, 11, 8, 9, 10], "uvs": [0.99999, 0.97522, 0.37917, 0.99999, 0, 0.73446, 0.0728, 0.61523, 0.21342, 0.47707, 0.33334, 0.35586, 0.41651, 0.2361, 0.46661, 0.11228, 0.46464, 0.03503, 0.46215, 0, 0.98652, 0.02828, 1, 0.06475, 0.99995, 0.14519, 0.98539, 0.26998, 0.94742, 0.39896, 0.90795, 0.52571, 0.86857, 0.65214, 0.93454, 0.78674], "vertices": [2, 99, 78.71, 39.12, 7e-05, 100, 47.42, 39.12, 0.99993, 3, 97, 151.64, -19.25, 0, 99, 89.06, -19.25, 0.0049, 100, 57.78, -19.25, 0.9951, 4, 97, 90.23, -59.95, 0.08651, 98, 58.94, -59.95, 0.12651, 99, 27.66, -59.95, 0.54436, 100, -3.63, -59.95, 0.24261, 4, 97, 60.94, -55.19, 0.23853, 98, 29.66, -55.19, 0.28037, 99, -1.63, -55.19, 0.42672, 100, -32.92, -55.19, 0.05438, 4, 96, 57.9, -44.35, 0.03653, 97, 26.61, -44.35, 0.64594, 98, -4.68, -44.35, 0.2404, 99, -35.96, -44.35, 0.07712, 5, 95, 59.09, -35.17, 0.02186, 96, 27.8, -35.16, 0.46501, 97, -3.49, -35.16, 0.49124, 98, -34.77, -35.17, 0.0212, 99, -66.06, -35.17, 0.00069, 3, 95, 29.6, -29.44, 0.53184, 96, -1.69, -29.44, 0.44177, 97, -32.98, -29.44, 0.02639, 3, 94, -2.06, -2.33, 0.34821, 95, -2, -39.98, 0.64669, 96, -33.28, -39.98, 0.0051, 2, 94, -25.65, 14.12, 0.84226, 95, -22.55, -60.09, 0.15774, 1, 94, -34, 21.74, 1, 1, 94, 15.89, 28.03, 1, 2, 94, 28.2, 20.57, 0.76606, 95, -18.86, -5.98, 0.23394, 3, 94, 54.98, 3.6, 0.1875, 95, 2.81, 17.17, 0.81092, 96, -28.47, 17.17, 0.00158, 2, 95, 33.77, 25.07, 0.52089, 96, 2.48, 25.07, 0.47911, 4, 95, 65.16, 23.79, 0.01332, 96, 33.87, 23.79, 0.4737, 97, 2.59, 23.79, 0.50259, 98, -28.7, 23.79, 0.01039, 2, 97, 33.45, 22.32, 0.26524, 98, 2.16, 22.32, 0.73476, 3, 98, 32.95, 20.86, 0.48735, 99, 1.67, 20.86, 0.51196, 100, -29.62, 20.86, 0.00069, 3, 98, 64.97, 29.53, 0.0006, 99, 33.68, 29.53, 0.48887, 100, 2.4, 29.53, 0.51053], "width": 95, "edges": [0, 2, 4, 6, 12, 14, 24, 26, 32, 34, 34, 0, 18, 20, 2, 4, 14, 24, 26, 12, 6, 32, 4, 34, 10, 12, 26, 28, 28, 10, 14, 16, 16, 18, 20, 22, 22, 24, 16, 22, 6, 8, 8, 10, 28, 30, 30, 32, 8, 30], "type": "mesh", "hull": 18, "height": 242}}, "Wave Long Left 2": {"Wave Long Left": {"scaleX": -1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 80.34, "width": 339, "y": 54.77, "height": 110}}, "Wave Long Left 1": {"Wave Long Left": {"scaleX": 1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 68.64, "width": 339, "y": 44.53, "height": 110}}, "Wild": {"Wild": {"scaleX": 1.189, "scaleY": 1.189, "x": -0.08, "width": 192, "y": 5.87, "height": 55}}, "mask-body": {"mask-body": {"color": "ce3a3aff", "vertices": [-47.28, 99.86, 37.01, 115.67, 78.28, 80.55, 69.5, -26.69, 54.75, -31.82, 54.71, -40.96, 43.25, -40.19, 42.87, -50.51, -33.53, -50.89, -35.06, -41.72, -50.34, -40.96, -50.72, -31.41, -58.74, -29.11, -91.98, 45.38], "end": "non", "type": "clipping", "vertexCount": 14}}, "BG": {"BG": {"triangles": [2, 3, 0, 2, 0, 1], "uvs": [1, 0.0724, 1, 0.96362, 0, 0.94914, 0, 0.07562], "vertices": [89, 201.7, 89, -208.26, -89, -201.6, -89, 200.22], "width": 178, "edges": [0, 2, 4, 6, 2, 4, 6, 0], "type": "mesh", "hull": 4, "height": 230}}, "Wild Mask": {"Wild Mask": {"color": "ffff00ff", "vertices": [9.22, 75.47, 9.36, -74.45, -4.15, -74.64, -3.49, 73.48, -5.99, 73.53, -6.24, -74.53, -9.54, -74.6, -9.03, 75.42], "end": "<PERSON>", "type": "clipping", "vertexCount": 8}}, "non": {"non": {"scaleX": 0.955, "scaleY": 0.955, "rotation": -92.49, "x": 23.89, "width": 68, "y": 11.11, "height": 75}}, "Leg Right": {"Leg Right": {"triangles": [0, 2, 3, 1, 2, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [172.27, 34.34, 173.52, -30.65, -17.44, -34.31, -18.69, 30.67], "width": 65, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 191}}, "thanh-ngang2": {"thanh-ngang": {"scaleY": -1, "width": 200, "y": 6.83, "height": 39}}, "mask-bg": {"mask-bg": {"color": "ce3a3aff", "vertices": [-55.39, 54.97, 59.29, 55.47, 59.29, -55.19, -57.9, -56.7], "end": "BG", "type": "clipping", "vertexCount": 4}}, "thanh-doc2": {"thanh-doc": {"scaleX": -1, "scaleY": -1, "x": 1.46, "width": 23, "y": -2.47, "height": 381}}, "Mouth": {"Mouth": {"triangles": [10, 0, 3, 0, 10, 9, 6, 5, 10, 5, 9, 10, 4, 9, 5, 7, 5, 6, 8, 4, 5, 8, 5, 7, 11, 7, 6, 11, 6, 10, 12, 4, 8, 12, 8, 7, 12, 7, 11, 9, 4, 12, 1, 0, 9, 10, 3, 2, 11, 10, 2, 12, 11, 2, 1, 9, 12, 1, 12, 2], "path": "Face", "uvs": [0, 0.65219, 0, 1, 1, 1, 1, 0.64974, 0.24908, 0.78233, 0.54908, 0.7479, 0.70955, 0.74298, 0.56484, 0.87211, 0.39414, 0.87637, 0.22126, 0.77775, 0.7701, 0.7084, 0.57429, 0.88726, 0.38824, 0.89054], "vertices": [6.92, 33.94, -14.27, 34.87, -16.14, -8.09, 5.2, -9.02, -1.47, 23.59, 0.06, 10.61, 0.06, 3.7, -7.53, 10.26, -7.47, 17.61, -1.14, 24.77, 2.06, 1.01, -8.48, 9.9, -8.33, 17.9], "width": 43, "edges": [0, 2, 2, 4, 4, 6, 0, 6, 20, 22, 22, 24, 24, 18, 18, 20, 14, 12, 12, 10, 10, 8, 8, 16, 16, 14], "type": "mesh", "hull": 4, "height": 61}}, "Shoulder": {"Shoulder": {"triangles": [7, 10, 8, 8, 10, 9, 9, 10, 0, 10, 1, 0, 10, 2, 1, 10, 7, 6, 10, 6, 4, 4, 6, 5, 4, 3, 10, 3, 2, 10], "uvs": [1, 0.40493, 0.82166, 0, 0.10496, 0, 0.0477, 0.1845, 0, 0.44647, 0, 0.99982, 0.23436, 1, 0.45866, 0.84472, 0.62381, 0.81479, 0.85204, 0.69748, 0.41587, 0.49987], "vertices": [1, 68, 84.03, 15.99, 1, 2, 91, -41.18, 22.89, 0.25, 68, 120.5, 36.49, 0.75, 2, 91, -16.47, -66.95, 0.5, 68, 127.5, 129.4, 0.5, 1, 91, 0.8, -69.92, 1, 1, 91, 24.17, -69.92, 1, 1, 91, 70.05, -57.3, 1, 2, 91, 61.99, -27.92, 0.75, 68, 40.48, 119.08, 0.25, 2, 91, 41.38, -3.35, 0.5, 68, 51.6, 89, 0.5, 2, 91, 33.21, 16.67, 0.25, 68, 52.56, 67.4, 0.75, 1, 68, 60.39, 37.06, 1, 2, 91, 14.26, -16.58, 0.5, 68, 81.59, 92.32, 0.5], "width": 130, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "type": "mesh", "hull": 10, "height": 86}}, "Frame Mask": {"Frame Mask": {"color": "ce3a3aff", "vertices": [-82.41, 204.39, -82.89, 181.24, -88.55, 163.7, -88.36, -164.16, -82.7, -182.27, -81.94, -207.88, 81.31, -209.31, 82.74, -183.69, 88.11, -162.75, 88.27, 164.29, 82.05, 182.4, 83.84, 204.41], "end": "Wave Splash Small 1", "type": "clipping", "vertexCount": 12}}, "Wave Long Right 9": {"Wave Long Left": {"scaleX": 1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 80.34, "width": 339, "y": 54.77, "height": 110}}, "Wave Long Right 8": {"Wave Long Left": {"scaleX": -1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 80.34, "width": 339, "y": 54.77, "height": 110}}, "Wave Long Right 7": {"Wave Long Left": {"scaleX": 1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 68.64, "width": 339, "y": 44.53, "height": 110}}, "Wave Long Right 6": {"Wave Long Left": {"scaleX": -1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 80.34, "width": 339, "y": 54.77, "height": 110}}, "Wave Long Right 5": {"Wave Long Left": {"scaleX": 1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 80.34, "width": 339, "y": 54.77, "height": 110}}, "Gem Sparkle 1": {"Gem Sparkle": {"x": 0.95, "width": 55, "y": 0.3, "height": 60}}, "Wave Long Right 4": {"Wave Long Left": {"scaleX": -1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 80.34, "width": 339, "y": 54.77, "height": 110}}, "Body": {"Body": {"triangles": [6, 8, 9, 6, 7, 8, 9, 21, 6, 10, 21, 9, 20, 10, 11, 10, 20, 21, 16, 20, 11, 21, 4, 5, 21, 5, 6, 20, 16, 4, 20, 4, 21, 17, 16, 11, 17, 11, 12, 13, 17, 12, 3, 4, 16, 3, 16, 17, 18, 17, 13, 2, 3, 17, 14, 18, 13, 18, 2, 17, 19, 2, 18, 19, 18, 14, 1, 2, 19, 0, 19, 14, 1, 19, 0, 0, 14, 15], "uvs": [0.76267, 0.99953, 0.20838, 0.99843, 0.13912, 0.84501, 0, 0.6851, 0, 0.37393, 0.16484, 0.1744, 0.29191, 0.14454, 0.26959, 0.00245, 0.62822, 1e-05, 0.67535, 0.12219, 0.69862, 0.17504, 0.85822, 0.20214, 0.99999, 0.41333, 0.97687, 0.64836, 0.89926, 0.82289, 1, 1, 0.52974, 0.2324, 0.71837, 0.46027, 0.73606, 0.69971, 0.71542, 0.85999, 0.52097, 0.21718, 0.3147, 0.22402], "vertices": [1, 67, -1.13, -1.74, 1, 2, 67, 5.1, 61.3, 0.60749, 68, -35.89, 60.58, 0.39251, 2, 67, 32.44, 66.71, 0.38141, 68, -8.66, 66.54, 0.61859, 2, 67, 61.66, 79.94, 0.12143, 68, 20.28, 80.37, 0.87857, 2, 67, 115.57, 74.83, 0.00158, 68, 74.28, 76.35, 0.99842, 2, 67, 148.28, 52.42, 0.00085, 68, 107.49, 55.01, 0.99915, 2, 68, 111.58, 40.17, 0.25, 69, 9.81, 22.98, 0.75, 1, 69, 34.64, 24.07, 1, 1, 69, 32.67, -16.77, 1, 2, 68, 112.18, -3.71, 0.25, 69, 11.13, -20.89, 0.75, 2, 68, 102.81, -5.66, 0.75, 69, 1.79, -23, 0.25, 1, 68, 96.74, -23.45, 1, 1, 68, 58.88, -36.81, 1, 2, 67, 57.42, -31.48, 0.0276, 68, 18.3, -31.12, 0.9724, 2, 67, 27.99, -20.08, 0.78811, 68, -11.35, -20.32, 0.21189, 1, 67, -3.79, -28.66, 1, 1, 68, 94.3, 14.29, 1, 1, 68, 53.15, -4.18, 1, 1, 68, 11.45, -3.06, 1, 2, 67, 23.55, 1.31, 0.99716, 68, -16.23, 0.98, 0.00284, 1, 68, 97.02, 15.08, 1, 3, 67, 138.06, 36.24, 0.00042, 68, 97.6, 38.62, 0.74958, 69, -4.15, 21.2, 0.25], "width": 114, "edges": [2, 4, 4, 6, 6, 8, 14, 16, 24, 26, 26, 28, 28, 30, 2, 0, 0, 30, 38, 0, 38, 36, 36, 34, 34, 32, 22, 24, 32, 22, 6, 34, 8, 32, 34, 24, 4, 36, 36, 26, 2, 38, 38, 28, 40, 22, 40, 8, 12, 14, 20, 22, 8, 10, 10, 12, 16, 18, 18, 20, 12, 18, 42, 20], "type": "mesh", "hull": 16, "height": 174}}, "Gem Sparkle 2": {"Gem Sparkle": {"x": 0.95, "width": 55, "y": 0.3, "height": 60}}, "Wave Long Right 3": {"Wave Long Left": {"scaleX": 1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 80.34, "width": 339, "y": 54.77, "height": 110}}, "Wave Long Right 2": {"Wave Long Left": {"scaleX": -1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 80.34, "width": 339, "y": 54.77, "height": 110}}, "Wave Long Right 1": {"Wave Long Left": {"scaleX": 1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 68.64, "width": 339, "y": 44.53, "height": 110}}, "Arm Right": {"Arm Right": {"rotation": 102.06, "x": 26.94, "width": 76, "y": 5.78, "height": 104}}, "Horn Right": {"Horn Right": {"rotation": 176.81, "x": 71.42, "width": 60, "y": -6.86, "height": 40}}, "Forearm Right": {"Forearm Right": {"rotation": 41.33, "x": -25.98, "width": 105, "y": 8.62, "height": 117}}, "Wave Splash Small 1": {"Wave Small": {"path": "Wave/Wave Small", "scaleY": 2, "x": -15, "width": 217, "y": 122.5, "height": 137}}, "Wave Splash Small 2": {"Wave Small": {"scaleX": -1, "path": "Wave/Wave Small", "x": 15, "width": 217, "y": 122.5, "height": 137}}, "thanh-doc": {"thanh-doc": {"x": -0.23, "width": 23, "y": -0.5, "height": 381}}, "Wave Long Left 11": {"Wave Long Left": {"scaleX": 1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 80.34, "width": 339, "y": 54.77, "height": 110}}, "Wave Splash Big 4": {"Wave Big": {"scaleX": -1, "path": "Wave/Wave Big", "width": 263, "y": 125, "height": 290}}, "Wave Long Left 12": {"Wave Long Left": {"scaleX": -1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 80.34, "width": 339, "y": 54.77, "height": 110}}, "Wave Splash Big 2": {"Wave Big": {"scaleX": -1, "path": "Wave/Wave Big", "width": 263, "y": 125, "height": 290}}, "mask-triden": {"mask-triden": {"color": "ce3a3aff", "vertices": [-83.82, 57.56, -82.99, -64.84, 187.44, -217.83, 266.69, -171.35, 280.43, -17.44, -72.24, 81.54], "end": "Trident", "type": "clipping", "vertexCount": 6}}, "Wave Splash Big 3": {"Wave Big": {"path": "Wave/Wave Big", "width": 263, "y": 125, "height": 290}}, "Frame Light Right": {"Frame Light": {"scaleX": -1, "x": -0.5, "width": 31, "y": 0.5, "height": 225}}, "Forearm Left": {"Forearm Left": {"rotation": -174.33, "x": 43.14, "width": 102, "y": -1.23, "height": 49}}, "Wave Long Left 10": {"Wave Long Left": {"scaleX": -1.325, "path": "Wave/Wave Long", "scaleY": 1.325, "rotation": -30.1, "x": 80.34, "width": 339, "y": 54.77, "height": 110}}, "thanh-docBright2": {"thanh-docBright": {"scaleX": -1, "scaleY": -1, "x": 85.02, "width": 23, "y": 18.17, "height": 381}}, "Arm Left Layer Front": {"Arm Left": {"triangles": [1, 2, 0], "uvs": [1, 1, 0, 1, 1, 0.00359], "vertices": [87.21, 3.66, 77.31, -39.21, -9.88, 26.07], "width": 44, "edges": [0, 2, 0, 4, 2, 4], "type": "mesh", "hull": 3, "height": 100}}}}, "skeleton": {"images": "./images/", "width": 345.06, "spine": "3.7.94", "audio": "D:/Chec D/Anime Draw/KTEK/Project KTEK/Son Tin<PERSON> Thu<PERSON> Tinh 2020 WFH/SonTinh 3o", "hash": "I+CXFOt39GHMetEewoI0TSZXIk8", "height": 493.13}, "slots": [{"attachment": "mask-bg", "name": "mask-bg", "bone": "Frame All"}, {"attachment": "Frame Mask", "name": "Frame Mask", "bone": "Frame All"}, {"attachment": "BG", "name": "BG", "bone": "Frame All"}, {"attachment": "Moon", "name": "Moon", "bone": "Moon"}, {"attachment": "Wave Small", "name": "Wave Splash Small 2", "bone": "Wave Splash Small 2"}, {"attachment": "Wave Small", "name": "Wave Splash Small 1", "bone": "Wave Splash Small 1"}, {"attachment": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "bone": "Frame All"}, {"color": "8c8c8cff", "attachment": "Wave Long Left", "name": "Wave Long Right 12", "bone": "Wave Long Right Vertical 12"}, {"color": "969696ff", "attachment": "Wave Long Left", "name": "Wave Long Right 11", "bone": "Wave Long Right Vertical 11"}, {"color": "a0a0a0ff", "attachment": "Wave Long Left", "name": "Wave Long Right 10", "bone": "Wave Long Right Vertical 10"}, {"color": "aaa<PERSON><PERSON><PERSON>", "attachment": "Wave Long Left", "name": "Wave Long Right 9", "bone": "Wave Long Right Vertical 9"}, {"color": "b4b4b4ff", "attachment": "Wave Long Left", "name": "Wave Long Right 8", "bone": "Wave Long Right Vertical 8"}, {"color": "8c8c8cff", "attachment": "Wave Long Left", "name": "<PERSON> Long Left 12", "bone": "<PERSON> Long Left Vertical 12"}, {"color": "969696ff", "attachment": "Wave Long Left", "name": "<PERSON> Long Left 11", "bone": "<PERSON> Long Left Vertical 11"}, {"color": "a0a0a0ff", "attachment": "Wave Long Left", "name": "<PERSON> Long Left 10", "bone": "<PERSON> Long Left Vertical 10"}, {"color": "aaa<PERSON><PERSON><PERSON>", "attachment": "Wave Long Left", "name": "<PERSON> Long Left 9", "bone": "<PERSON> Long Left Vertical 9"}, {"color": "b4b4b4ff", "attachment": "Wave Long Left", "name": "<PERSON> Long Left 8", "bone": "<PERSON> Long Left Vertical 8"}, {"attachment": "mask-body", "name": "mask-body", "bone": "root"}, {"attachment": "Hair", "name": "Hair Behind", "bone": "Head"}, {"attachment": "Arm Left", "name": "Arm Left", "bone": "Arm Left"}, {"attachment": "Body", "name": "Body", "bone": "Body"}, {"attachment": "Leg Right", "name": "Leg Right", "bone": "Leg Right"}, {"attachment": "Loincloth", "name": "Loincloth", "bone": "Hip"}, {"attachment": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "bone": "Fabric 6"}, {"color": "bebe<PERSON><PERSON>", "attachment": "Wave Long Left", "name": "Wave Long Right 7", "bone": "Wave Long Right Vertical 7"}, {"color": "c8c8c8ff", "attachment": "Wave Long Left", "name": "<PERSON> Long Right 6", "bone": "Wave Long Right Vertical 6"}, {"color": "d2d2d2ff", "attachment": "Wave Long Left", "name": "Wave Long Right 5", "bone": "Wave Long Right Vertical 5"}, {"color": "dcdcdcff", "attachment": "Wave Long Left", "name": "Wave Long Right 4", "bone": "Wave Long Right Vertical 4"}, {"color": "e6e6e6ff", "attachment": "Wave Long Left", "name": "Wave Long Right 3", "bone": "Wave Long Right Vertical 3"}, {"color": "f0f0f0ff", "attachment": "Wave Long Left", "name": "Wave Long Right 2", "bone": "Wave Long Right Vertical 2"}, {"attachment": "Wave Long Left", "name": "Wave Long Right 1", "bone": "Wave Long Right Vertical 1"}, {"color": "bebe<PERSON><PERSON>", "attachment": "Wave Long Left", "name": "<PERSON> Long Left 7", "bone": "<PERSON> Long Left Vertical 7"}, {"color": "c8c8c8ff", "attachment": "Wave Long Left", "name": "<PERSON> Long Left 6", "bone": "<PERSON> Long Left Vertical 6"}, {"color": "d2d2d2ff", "attachment": "Wave Long Left", "name": "Wave Long Left 5", "bone": "<PERSON> Long Left Vertical 5"}, {"color": "dcdcdcff", "attachment": "Wave Long Left", "name": "Wave Long Left 4", "bone": "Wave Long Left Vertical 4"}, {"attachment": "Wave Big", "name": "Wave Splash Big 4", "bone": "Wave Splash Big 4"}, {"color": "e6e6e6ff", "attachment": "Wave Long Left", "name": "Wave Long Left 3", "bone": "Wave Long Left Vertical 3"}, {"attachment": "Wave Big", "name": "Wave Splash Big 3", "bone": "Wave Splash Big 3"}, {"color": "f0f0f0ff", "attachment": "Wave Long Left", "name": "Wave Long Left 2", "bone": "Wave Long Left Vertical 2"}, {"attachment": "Wave Big", "name": "Wave Splash Big 2", "bone": "Wave Splash Big 2"}, {"attachment": "Wave Long Left", "name": "Wave Long Left 1", "bone": "Wave Long Left Vertical 1"}, {"attachment": "Wave Big", "name": "Wave Splash Big 1", "bone": "Wave Splash Big 1"}, {"attachment": "thanh-doc", "name": "thanh-doc", "bone": "docT"}, {"attachment": "thanh-doc", "name": "thanh-doc2", "bone": "docP"}, {"attachment": "<PERSON><PERSON>-<PERSON><PERSON>", "name": "<PERSON><PERSON>-<PERSON><PERSON>", "bone": "ngangtren"}, {"attachment": "<PERSON><PERSON>-<PERSON><PERSON>", "name": "thanh-ngang2", "bone": "<PERSON><PERSON><PERSON><PERSON>"}, {"attachment": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "bone": "Frame-New"}, {"attachment": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "name": "thanh-docBright2", "bone": "Frame-New"}, {"attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bone": "Frame-New"}, {"attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "thanh-ngangBright2", "bone": "Frame-New"}, {"attachment": "Frame Light", "name": "Frame Light Right", "bone": "Frame Light Right"}, {"attachment": "Frame Light", "name": "Frame Light Left", "bone": "Frame Light Left"}, {"attachment": "Arm Left", "name": "Arm Left Layer Front", "bone": "Arm Left"}, {"attachment": "<PERSON>earm Left", "name": "<PERSON>earm Left", "bone": "<PERSON>earm Left"}, {"attachment": "mask-triden", "name": "mask-triden", "bone": "root"}, {"attachment": "Trident Holder", "name": "Trident Holder", "bone": "Hand Right"}, {"attachment": "Trident", "name": "Trident", "bone": "Hand Right"}, {"attachment": "Arm Right", "name": "Arm Right", "bone": "Arm Right"}, {"attachment": "Shoulder", "name": "Shoulder", "bone": "Chest"}, {"attachment": "Forearm Right", "name": "Forearm Right", "bone": "Hand Right"}, {"attachment": "<PERSON><PERSON>", "name": "Gem Sparkle 1", "bone": "Gem Sparkle 1"}, {"attachment": "<PERSON><PERSON>", "name": "Gem Sparkle 2", "bone": "Gem Sparkle 2"}, {"attachment": "Hair", "name": "Hair Front 1", "bone": "Head"}, {"attachment": "Eyes", "name": "Eyes", "bone": "Head"}, {"attachment": "Mouth", "name": "Mouth", "bone": "Head"}, {"attachment": "Hair", "name": "Hair Front 2", "bone": "Head"}, {"attachment": "<PERSON> Left", "name": "<PERSON> Left", "bone": "Head"}, {"attachment": "Horn Right", "name": "Horn Right", "bone": "Head"}, {"attachment": "Ear", "name": "Ear", "bone": "Head"}, {"attachment": "Wild", "name": "Wild", "bone": "Wild All"}, {"attachment": "Wild Mask", "name": "Wild Mask", "bone": "Wild Mask"}, {"attachment": "<PERSON>", "name": "<PERSON>", "bone": "Wild All"}, {"attachment": "Sparkle", "name": "Sparkle 1", "bone": "Sparkle 1"}, {"attachment": "Sparkle", "name": "Sparkle 2", "bone": "Sparkle 2"}, {"attachment": "Sparkle", "name": "Sparkle 3", "bone": "Sparkle 3"}, {"attachment": "Sparkle", "name": "Sparkle 4", "bone": "Sparkle 4"}, {"attachment": "Sparkle", "name": "Sparkle 5", "bone": "Sparkle 5"}, {"attachment": "non", "name": "non", "bone": "Head"}], "bones": [{"name": "root"}, {"parent": "root", "name": "all", "y": -20}, {"parent": "all", "name": "Frame All", "y": 20}, {"parent": "Frame All", "color": "ff1c1cff", "name": "Gem Sparkle 1", "x": -76.85, "y": 197.13}, {"parent": "Frame All", "color": "ff1c1cff", "name": "Gem Sparkle 2", "x": 75.82, "y": 197.13}, {"parent": "Frame All", "name": "Frame Light Left", "x": -85.75}, {"parent": "Frame All", "name": "Frame Light Right", "x": 85.75}, {"parent": "all", "name": "Wave All"}, {"parent": "all", "name": "Body", "x": 39.14, "y": 24.48}, {"parent": "Wave All", "name": "<PERSON> Long Left All"}, {"parent": "<PERSON> Long Left All", "color": "0000ffff", "name": "Wave Long Left Horiontal 1", "x": -126.45, "y": -242.13}, {"parent": "Wave Long Left Horiontal 1", "color": "0000ffff", "name": "Wave Long Left Vertical 1"}, {"parent": "<PERSON> Long Left All", "color": "0000ffff", "name": "Wave Long Left Horiontal 2", "x": -126.45, "y": -242.13}, {"parent": "Wave Long Left Horiontal 2", "color": "0000ffff", "name": "Wave Long Left Vertical 2"}, {"parent": "<PERSON> Long Left All", "color": "0000ffff", "name": "Wave Long Left Horiontal 3", "x": -126.45, "y": -242.13}, {"parent": "Wave Long Left Horiontal 3", "color": "0000ffff", "name": "Wave Long Left Vertical 3"}, {"parent": "<PERSON> Long Left All", "color": "0000ffff", "name": "Wave Long Left Horiontal 4", "x": -126.45, "y": -242.13}, {"parent": "Wave Long Left Horiontal 4", "color": "0000ffff", "name": "Wave Long Left Vertical 4"}, {"parent": "<PERSON> Long Left All", "color": "0000ffff", "name": "<PERSON> Long Left Horiontal 5", "x": -126.45, "y": -242.13}, {"parent": "<PERSON> Long Left Horiontal 5", "color": "0000ffff", "name": "<PERSON> Long Left Vertical 5"}, {"parent": "<PERSON> Long Left All", "color": "0000ffff", "name": "<PERSON> Left Horiontal 6", "x": -126.45, "y": -242.13}, {"parent": "<PERSON> Left Horiontal 6", "color": "0000ffff", "name": "<PERSON> Long Left Vertical 6"}, {"parent": "<PERSON> Long Left All", "color": "ff0000ff", "name": "<PERSON> Long Left Horiontal 7", "x": -126.45, "y": -242.13}, {"parent": "<PERSON> Long Left Horiontal 7", "color": "ff0000ff", "name": "<PERSON> Long Left Vertical 7"}, {"parent": "<PERSON> Long Left All", "color": "ff0000ff", "name": "<PERSON> Long Left Horiontal 8", "x": -126.45, "y": -242.13}, {"parent": "<PERSON> Long Left Horiontal 8", "color": "ff0000ff", "name": "<PERSON> Long Left Vertical 8"}, {"parent": "<PERSON> Long Left All", "color": "ff0000ff", "name": "<PERSON> Long Left Horiontal 9", "x": -126.45, "y": -242.13}, {"parent": "<PERSON> Long Left Horiontal 9", "color": "ff0000ff", "name": "<PERSON> Long Left Vertical 9"}, {"parent": "<PERSON> Long Left All", "color": "ff0000ff", "name": "<PERSON> Long Left Horiontal 10", "x": -126.45, "y": -242.13}, {"parent": "<PERSON> Long Left Horiontal 10", "color": "ff0000ff", "name": "<PERSON> Long Left Vertical 10"}, {"parent": "<PERSON> Long Left All", "color": "ff0000ff", "name": "<PERSON> Long Left Horiontal 11", "x": -126.45, "y": -242.13}, {"parent": "<PERSON> Long Left Horiontal 11", "color": "ff0000ff", "name": "<PERSON> Long Left Vertical 11"}, {"parent": "<PERSON> Long Left All", "color": "ff0000ff", "name": "<PERSON> Long Left Horiontal 12", "x": -126.45, "y": -242.13}, {"parent": "<PERSON> Long Left Horiontal 12", "color": "ff0000ff", "name": "<PERSON> Long Left Vertical 12"}, {"scaleX": -1, "parent": "Wave All", "scaleY": -1, "rotation": -125, "name": "Wave Long Right All", "x": -60, "y": -6.7}, {"parent": "Wave Long Right All", "color": "0000ffff", "name": "Wave Long Right Horiontal 1", "x": -126.45, "y": -242.13}, {"parent": "Wave Long Right Horiontal 1", "color": "0000ffff", "name": "Wave Long Right Vertical 1"}, {"parent": "Wave Long Right All", "color": "0000ffff", "name": "Wave Long Right Horiontal 2", "x": -126.45, "y": -242.13}, {"parent": "Wave Long Right Horiontal 2", "color": "0000ffff", "name": "Wave Long Right Vertical 2"}, {"parent": "Wave Long Right All", "color": "0000ffff", "name": "Wave Long Right Horiontal 3", "x": -126.45, "y": -242.13}, {"parent": "Wave Long Right Horiontal 3", "color": "0000ffff", "name": "Wave Long Right Vertical 3"}, {"parent": "Wave Long Right All", "color": "0000ffff", "name": "Wave Long Right Horiontal 4", "x": -126.45, "y": -242.13}, {"parent": "Wave Long Right Horiontal 4", "color": "0000ffff", "name": "Wave Long Right Vertical 4"}, {"parent": "Wave Long Right All", "color": "0000ffff", "name": "Wave Long Right Horiontal 5", "x": -126.45, "y": -242.13}, {"parent": "Wave Long Right Horiontal 5", "color": "0000ffff", "name": "Wave Long Right Vertical 5"}, {"parent": "Wave Long Right All", "color": "0000ffff", "name": "Wave Long Right Horiontal 6", "x": -126.45, "y": -242.13}, {"parent": "Wave Long Right Horiontal 6", "color": "0000ffff", "name": "Wave Long Right Vertical 6"}, {"parent": "Wave Long Right All", "color": "ff0000ff", "name": "Wave Long Right Horiontal 7", "x": -126.45, "y": -242.13}, {"parent": "Wave Long Right Horiontal 7", "color": "ff0000ff", "name": "Wave Long Right Vertical 7"}, {"parent": "Wave Long Right All", "color": "ff0000ff", "name": "Wave Long Right Horiontal 8", "x": -126.45, "y": -242.13}, {"parent": "Wave Long Right Horiontal 8", "color": "ff0000ff", "name": "Wave Long Right Vertical 8"}, {"parent": "Wave Long Right All", "color": "ff0000ff", "name": "Wave Long Right Horiontal 9", "x": -126.45, "y": -242.13}, {"parent": "Wave Long Right Horiontal 9", "color": "ff0000ff", "name": "Wave Long Right Vertical 9"}, {"parent": "Wave Long Right All", "color": "ff0000ff", "name": "Wave Long Right Horiontal 10", "x": -126.45, "y": -242.13}, {"parent": "Wave Long Right Horiontal 10", "color": "ff0000ff", "rotation": -0.02, "name": "Wave Long Right Vertical 10"}, {"parent": "Wave Long Right All", "color": "ff0000ff", "name": "Wave Long Right Horiontal 11", "x": -126.45, "y": -242.13}, {"parent": "Wave Long Right Horiontal 11", "color": "ff0000ff", "name": "Wave Long Right Vertical 11"}, {"parent": "Wave Long Right All", "color": "ff0000ff", "name": "Wave Long Right Horiontal 12", "x": -126.45, "y": -242.13}, {"parent": "Wave Long Right Horiontal 12", "color": "ff0000ff", "name": "Wave Long Right Vertical 12"}, {"parent": "Wave All", "name": "Wave Splash Big All"}, {"parent": "Wave Splash Big All", "name": "Wave Splash Big 1", "y": -200}, {"parent": "Wave Splash Big All", "name": "Wave Splash Big 2", "y": -200}, {"parent": "Wave Splash Big All", "name": "Wave Splash Big 3", "y": -200}, {"parent": "Wave Splash Big All", "name": "Wave Splash Big 4", "y": -200}, {"parent": "Wave All", "name": "Wave Splash Small All"}, {"parent": "Wave Splash Small All", "name": "Wave Splash Small 1", "y": -175}, {"parent": "Wave Splash Small All", "name": "Wave Splash Small 2", "y": -175}, {"parent": "Body", "rotation": 95.47, "name": "Hip", "length": 39.75}, {"parent": "Hip", "rotation": -1.16, "name": "Chest", "length": 86.58, "x": 39.7, "y": -0.4}, {"parent": "Chest", "rotation": -0.94, "name": "Neck", "length": 26.36, "x": 101.39, "y": 17.36}, {"parent": "Neck", "rotation": -0.88, "name": "Head", "length": 15.19, "x": 25.68, "y": -6.11}, {"parent": "Body", "color": "ff0000ff", "rotation": -104.04, "name": "Loincloth Right 1", "length": 34.42, "x": -44.35, "y": -7.68}, {"parent": "Loincloth Right 1", "color": "ff0000ff", "name": "Loincloth Right 2", "length": 34.42, "x": 34.42}, {"parent": "Loincloth Right 2", "color": "ff0000ff", "name": "Loincloth Right 3", "length": 34.42, "x": 34.42}, {"parent": "Body", "color": "ff0000ff", "rotation": -56.1, "name": "Loincloth Left 1", "length": 37.32, "x": 16.64, "y": -5.86}, {"parent": "Loincloth Left 1", "color": "ff0000ff", "name": "Loincloth Left 2", "length": 37.32, "x": 37.32}, {"parent": "Loincloth Left 2", "color": "ff0000ff", "name": "Loincloth Left 3", "length": 37.32, "x": 37.32}, {"parent": "Chest", "rotation": -156.31, "name": "Arm Left", "length": 78.16, "x": 76.16, "y": -24.33}, {"parent": "Arm Left", "rotation": -138.67, "name": "<PERSON>earm Left", "length": 82.59, "x": 78.16}, {"parent": "Head", "color": "ff00ffff", "rotation": -174.19, "name": "Hair Right Rotation", "x": 27.74, "y": -11.81}, {"parent": "Hair Right Rotation", "color": "ff00ffff", "name": "Hair Right 1", "length": 18.53}, {"parent": "Hair Right 1", "color": "ff00ffff", "rotation": -3.53, "name": "Hair Right 2", "length": 18.4, "x": 18.53}, {"parent": "Hair Right 2", "color": "ff00ffff", "rotation": 24.66, "name": "Hair Right 3", "length": 17.1, "x": 18.4}, {"parent": "Hair Right 3", "color": "ff00ffff", "rotation": -0.67, "name": "Hair Right 4", "length": 17.87, "x": 17.1}, {"parent": "Hair Right 4", "color": "ff00ffff", "rotation": -25.49, "name": "Hair Right 5", "length": 20.09, "x": 17.87}, {"parent": "Head", "color": "ff00ffff", "name": "Hair Left Rotation", "x": 35.22, "y": 37.18}, {"parent": "Hair Left Rotation", "color": "ff00ffff", "rotation": 166.73, "name": "Hair Left 1", "length": 20.42}, {"parent": "Hair Left 1", "color": "ff00ffff", "rotation": 26.28, "name": "Hair Left 2", "length": 20.02, "x": 20.42}, {"parent": "Hair Left 2", "color": "ff00ffff", "rotation": -0.24, "name": "Hair Left 3", "length": 19.6, "x": 20.02}, {"parent": "Hair Left 3", "color": "ff00ffff", "rotation": -19.92, "name": "Hair Left 4", "length": 18.78, "x": 19.6}, {"parent": "Hair Left 4", "color": "ff00ffff", "rotation": 1.33, "name": "Hair Left 5", "length": 19.71, "x": 18.78}, {"parent": "Chest", "rotation": 160.32, "name": "Arm Right", "length": 95.83, "x": 89.44, "y": 71.91}, {"parent": "Arm Right", "rotation": 17.95, "name": "Forearm Right", "length": 63.76, "x": 55.29, "y": 8.02}, {"parent": "Forearm Right", "rotation": 44.27, "name": "Hand Right", "length": 34.98, "x": 79.71, "y": 29.08}, {"parent": "Body", "color": "ff0000ff", "name": "Fabric <PERSON>", "x": 2.51, "y": -10.78}, {"parent": "Fabric <PERSON>", "color": "ff0000ff", "rotation": -79.26, "name": "Fabric 1", "length": 31.29}, {"parent": "Fabric 1", "color": "ff0000ff", "name": "Fabric 2", "length": 31.29, "x": 31.29}, {"parent": "Fabric 2", "color": "ff0000ff", "name": "Fabric 3", "length": 31.29, "x": 31.29}, {"parent": "Fabric 3", "color": "ff0000ff", "name": "Fabric 4", "length": 31.29, "x": 31.29}, {"parent": "Fabric 4", "color": "ff0000ff", "name": "Fabric 5", "length": 31.29, "x": 31.29}, {"parent": "Fabric 5", "color": "ff0000ff", "name": "Fabric 6", "length": 31.29, "x": 31.29}, {"scaleX": 1.733, "parent": "Body", "rotation": -99.1, "name": "Leg Right", "length": 145.98, "x": -26.21, "y": -49.07}, {"parent": "Frame All", "name": "Moon", "x": 51, "y": 180.5}, {"parent": "Hair Right Rotation", "color": "ff00ffff", "name": "Hair Behind 1", "length": 18.53}, {"parent": "Hair Behind 1", "color": "ff00ffff", "rotation": -3.53, "name": "Hair Behind 2", "length": 18.4, "x": 18.53}, {"parent": "Hair Behind 2", "color": "ff00ffff", "rotation": 24.66, "name": "Hair Behind 3", "length": 17.1, "x": 18.4}, {"parent": "Hair Behind 3", "color": "ff00ffff", "rotation": -0.67, "name": "Hair Behind 4", "length": 17.87, "x": 17.1}, {"parent": "Hair Behind 4", "color": "ff00ffff", "rotation": -58.32, "name": "Hair Behind 5", "length": 20.09, "x": 17.87}, {"scaleX": 0.857, "parent": "all", "scaleY": 0.857, "name": "Wild All", "x": 1.35, "y": -161.33}, {"scaleX": 2, "parent": "Wild All", "rotation": -17.5, "name": "Wild Mask", "x": -120}, {"scaleX": 0.85, "parent": "Wild All", "scaleY": 0.85, "name": "Sparkle 1", "x": -72.15, "y": 25.48}, {"scaleX": 0.85, "parent": "Wild All", "scaleY": 0.85, "name": "Sparkle 2", "x": -36, "y": 1}, {"scaleX": 0.85, "parent": "Wild All", "scaleY": 0.85, "name": "Sparkle 3", "x": 2.26, "y": 22.71}, {"scaleX": 0.85, "parent": "Wild All", "scaleY": 0.85, "name": "Sparkle 4", "x": 30.46, "y": -8.16}, {"scaleX": 0.85, "parent": "Wild All", "scaleY": 0.85, "name": "Sparkle 5", "x": 68.28, "y": 21.76}, {"parent": "all", "color": "fcec00ff", "name": "Frame-New"}, {"parent": "Frame-New", "color": "fcec00ff", "name": "docT", "x": -85.52, "y": 18.68}, {"parent": "Frame-New", "color": "fcec00ff", "name": "docP", "x": 83.56, "y": 20.64}, {"parent": "Frame-New", "color": "fcec00ff", "name": "ngangtren", "y": 222.16}, {"parent": "Frame-New", "color": "fcec00ff", "name": "<PERSON><PERSON><PERSON><PERSON>", "y": -188.74}], "animations": {"Wild-ThuyTinh": {"slots": {"Wave Splash Big 4": {"color": [{"color": "ffffff71", "time": 0}, {"color": "ffffff00", "curve": "stepped", "time": 2.6667}, {"color": "ffffffff", "time": 3.3333}, {"color": "ffffff71", "time": 6.6667}]}, "Wave Splash Big 2": {"color": [{"color": "ffffff78", "time": 0}, {"color": "ffffff00", "curve": "stepped", "time": 1.5}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 1.6667}, {"color": "ffffff00", "curve": "stepped", "time": 4.8333}, {"color": "ffffffff", "time": 5}, {"color": "ffffff78", "time": 6.6667}]}, "Wave Splash Big 3": {"color": [{"color": "ffffffff", "time": 0}, {"color": "ffffff00", "time": 6}]}, "mask-body": {"attachment": [{"name": null, "time": 0}]}, "BG": {"color": [{"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 0.4667}, {"color": "4b4b4bff", "curve": "stepped", "time": 0.6667}, {"color": "4b4b4bff", "curve": [0.25, 0, 0.75, 1], "time": 1.0667}, {"color": "ffffffff", "curve": "stepped", "time": 1.2667}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 2.1333}, {"color": "4b4b4bff", "curve": "stepped", "time": 2.3333}, {"color": "4b4b4bff", "curve": [0.25, 0, 0.75, 1], "time": 2.7333}, {"color": "ffffffff", "curve": "stepped", "time": 2.9333}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 3.8}, {"color": "4b4b4bff", "curve": "stepped", "time": 4}, {"color": "4b4b4bff", "curve": [0.25, 0, 0.75, 1], "time": 4.4}, {"color": "ffffffff", "curve": "stepped", "time": 4.6}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 5.4667}, {"color": "4b4b4bff", "curve": "stepped", "time": 5.6667}, {"color": "4b4b4bff", "curve": [0.25, 0, 0.75, 1], "time": 6.0667}, {"color": "ffffffff", "time": 6.2667}]}, "mask-bg": {"attachment": [{"name": null, "time": 0}]}, "thanh-ngangBright2": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.1667}, {"color": "ffffffff", "curve": "stepped", "time": 0.4667}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 1.2667}, {"color": "ffffff00", "curve": "stepped", "time": 1.6667}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 1.8333}, {"color": "ffffffff", "curve": "stepped", "time": 2.1333}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 2.9333}, {"color": "ffffff00", "curve": "stepped", "time": 3.3333}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 3.5}, {"color": "ffffffff", "curve": "stepped", "time": 3.8}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 4.6}, {"color": "ffffff00", "curve": "stepped", "time": 5}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 5.1667}, {"color": "ffffffff", "curve": "stepped", "time": 5.4667}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 6.2667}, {"color": "ffffff00", "time": 6.6667}]}, "thanh-docBright2": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.1667}, {"color": "ffffffff", "curve": "stepped", "time": 0.4667}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 1.2667}, {"color": "ffffff00", "curve": "stepped", "time": 1.6667}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 1.8333}, {"color": "ffffffff", "curve": "stepped", "time": 2.1333}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 2.9333}, {"color": "ffffff00", "curve": "stepped", "time": 3.3333}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 3.5}, {"color": "ffffffff", "curve": "stepped", "time": 3.8}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 4.6}, {"color": "ffffff00", "curve": "stepped", "time": 5}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 5.1667}, {"color": "ffffffff", "curve": "stepped", "time": 5.4667}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 6.2667}, {"color": "ffffff00", "time": 6.6667}]}, "thanh-docBright": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.1667}, {"color": "ffffffff", "curve": "stepped", "time": 0.4667}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 1.2667}, {"color": "ffffff00", "curve": "stepped", "time": 1.6667}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 1.8333}, {"color": "ffffffff", "curve": "stepped", "time": 2.1333}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 2.9333}, {"color": "ffffff00", "curve": "stepped", "time": 3.3333}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 3.5}, {"color": "ffffffff", "curve": "stepped", "time": 3.8}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 4.6}, {"color": "ffffff00", "curve": "stepped", "time": 5}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 5.1667}, {"color": "ffffffff", "curve": "stepped", "time": 5.4667}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 6.2667}, {"color": "ffffff00", "time": 6.6667}]}, "thanh-ngangBright": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0.1667}, {"color": "ffffffff", "curve": "stepped", "time": 0.4667}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 1.2667}, {"color": "ffffff00", "curve": "stepped", "time": 1.6667}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 1.8333}, {"color": "ffffffff", "curve": "stepped", "time": 2.1333}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 2.9333}, {"color": "ffffff00", "curve": "stepped", "time": 3.3333}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 3.5}, {"color": "ffffffff", "curve": "stepped", "time": 3.8}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 4.6}, {"color": "ffffff00", "curve": "stepped", "time": 5}, {"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 5.1667}, {"color": "ffffffff", "curve": "stepped", "time": 5.4667}, {"color": "ffffffff", "curve": [0.25, 0, 0.75, 1], "time": 6.2667}, {"color": "ffffff00", "time": 6.6667}]}, "Wave Splash Big 1": {"color": [{"color": "ffffffff", "time": 0}, {"color": "ffffff00", "curve": "stepped", "time": 3.1667}, {"color": "ffffffff", "time": 3.3333}, {"color": "ffffff00", "curve": "stepped", "time": 6.5}, {"color": "ffffffff", "time": 6.6667}]}}, "bones": {"Chest": {"rotate": [{"curve": [0.375, 0.5, 0.75, 1], "angle": 2.92, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.84, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.1667}, {"curve": [0.25, 0, 0.625, 0.5], "angle": 5.84, "time": 5.8333}, {"angle": 2.92, "time": 6.6667}]}, "Wave Long Left Vertical 8": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 1.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 2.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 4.5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 6.1667}, {"x": 0, "y": 25, "time": 6.6667}]}, "Sparkle 4": {"scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.2}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.5333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.8667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.4667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 2.8}, {"curve": "stepped", "x": 0, "y": 0, "time": 3.1333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 4.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 5}, {"x": 0, "y": 0, "time": 5.3333}]}, "Wave Long Left Vertical 9": {"translate": [{"curve": [0.342, 0.36, 0.757, 1], "x": 0, "y": -2.79, "time": 0}, {"x": 0, "y": -125, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 32.57, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 5}, {"curve": [0.271, 0, 0.619, 0.41], "x": 0, "y": 32.57, "time": 6}, {"x": 0, "y": -2.79, "time": 6.6667}]}, "Sparkle 5": {"scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.2667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.6}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.9333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.5333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 2.8667}, {"curve": "stepped", "x": 0, "y": 0, "time": 3.2}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 4.7333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 5.0667}, {"x": 0, "y": 0, "time": 5.4}]}, "Wave Long Left Vertical 6": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 2}, {"x": 0, "y": -125, "time": 6.6667}]}, "Sparkle 2": {"scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.0667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.4}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.7333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 2.6667}, {"curve": "stepped", "x": 0, "y": 0, "time": 3}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 4.5333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 4.8667}, {"x": 0, "y": 0, "time": 5.2}]}, "Wave Long Left Vertical 7": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 32.57, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 1.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 2.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 32.57, "time": 3.3333}, {"x": 0, "y": -125, "time": 4.5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 6.1667}, {"x": 0, "y": 32.57, "time": 6.6667}]}, "Sparkle 3": {"scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.1333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.4667}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.4}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 2.7333}, {"curve": "stepped", "x": 0, "y": 0, "time": 3.0667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 4.6}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 4.9333}, {"x": 0, "y": 0, "time": 5.2667}]}, "Wave Long Left Vertical 4": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 4.3333}, {"x": 0, "y": -125, "time": 6.6667}]}, "Wave Long Left Vertical 5": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 32.57, "time": 2}, {"x": 0, "y": -125, "time": 6.6667}]}, "Sparkle 1": {"scale": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.2667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 2.6}, {"curve": "stepped", "x": 0, "y": 0, "time": 2.9333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 4.4667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 4.8}, {"x": 0, "y": 0, "time": 5.1333}]}, "Wave Long Left Vertical 2": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 3.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 5.5}, {"x": 0, "y": -125, "time": 6.6667}]}, "Wave Long Left Vertical 3": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 32.57, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 32.57, "time": 4.3333}, {"x": 0, "y": -125, "time": 6.6667}]}, "Wave Long Left Vertical 1": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 32.57, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 32.57, "time": 3.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 5.5}, {"x": 0, "y": -125, "time": 6.6667}]}, "Wave Splash Big 1": {"scale": [{"curve": [0.25, 0, 0.75, 1], "x": 0.5, "y": 0.5, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1.25, "time": 0.5}, {"curve": "stepped", "x": 1.2, "y": 1.2, "time": 3.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 0.5, "y": 0.5, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1.25, "time": 3.8333}, {"curve": "stepped", "x": 1.2, "y": 1.2, "time": 6.5}, {"x": 0.5, "y": 0.5, "time": 6.6667}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -275, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": 0, "y": -275, "time": 3.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -275, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 3.8333}, {"x": 0, "y": -275, "time": 6.5}]}, "Wave Long Right Horiontal 10": {"translate": [{"x": -64.4, "y": 63.48, "time": 0}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 1.3333}, {"x": 109.63, "y": -31.84, "time": 1.6667}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 4.6667}, {"x": 109.63, "y": -31.84, "time": 5}, {"x": -64.4, "y": 63.48, "time": 6.6667}]}, "Wave Long Right Horiontal 11": {"translate": [{"x": -131.27, "y": 0, "time": 0}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 2.6667}, {"x": 109.63, "y": 0, "time": 3.3333}, {"x": -131.27, "y": 0, "time": 6.6667}]}, "Wave Long Left Horiontal 4": {"translate": [{"x": 109.63, "y": -31.84, "time": 0}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 3}, {"x": 109.63, "y": -31.84, "time": 3.3333}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 6.3333}, {"x": 109.63, "y": -31.84, "time": 6.6667}]}, "Wave Long Left Horiontal 5": {"translate": [{"x": 109.63, "y": 0, "time": 0}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 6}, {"x": 109.63, "y": 0, "time": 6.6667}]}, "Arm Left": {"rotate": [{"curve": [0.375, 0.5, 0.75, 1], "angle": 10.6, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 6.45, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 14.76, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 6.45, "time": 4.1667}, {"curve": [0.25, 0, 0.625, 0.5], "angle": 14.76, "time": 5.8333}, {"angle": 10.6, "time": 6.6667}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 2.36, "y": -7.22, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 2.36, "y": -7.22, "time": 5}, {"x": 0, "y": 0, "time": 6.6667}]}, "Wave Long Left Horiontal 2": {"translate": [{"x": 109.63, "y": -31.84, "time": 0}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 1.5}, {"x": 109.63, "y": -31.84, "time": 1.6667}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 3.1667}, {"x": 109.63, "y": -31.84, "time": 3.3333}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 4.8333}, {"x": 109.63, "y": -31.84, "time": 5}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 6.5}, {"x": 109.63, "y": -31.84, "time": 6.6667}]}, "Wave Long Left Horiontal 3": {"translate": [{"x": 109.63, "y": 0, "time": 0}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 3}, {"x": 109.63, "y": 0, "time": 3.3333}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 6.3333}, {"x": 109.63, "y": 0, "time": 6.6667}]}, "Wave Long Left Horiontal 8": {"translate": [{"curve": [0.351, 0.4, 0.757, 1], "x": 20.73, "y": 16.85, "time": 0}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 1}, {"x": 109.63, "y": -31.84, "time": 1.1667}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 2.6667}, {"x": 109.63, "y": -31.84, "time": 2.8333}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 4.3333}, {"x": 109.63, "y": -31.84, "time": 4.5}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 6}, {"curve": [0.265, 0, 0.618, 0.43], "x": 109.63, "y": -31.84, "time": 6.1667}, {"x": 20.73, "y": 16.85, "time": 6.6667}]}, "Wave Long Left Horiontal 9": {"translate": [{"x": -131.27, "y": 0, "time": 0}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 1.3333}, {"x": 109.63, "y": 0, "time": 1.6667}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 4.6667}, {"x": 109.63, "y": 0, "time": 5}, {"x": -131.27, "y": 0, "time": 6.6667}]}, "Wave Long Left Horiontal 6": {"translate": [{"x": 109.63, "y": -31.84, "time": 0}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 6}, {"x": 109.63, "y": -31.84, "time": 6.6667}]}, "Fabric 2": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.2333}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 0.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 0.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 1.3333}, {"curve": "stepped", "angle": 0, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.9}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 2.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 2.4}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 3}, {"curve": "stepped", "angle": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.5667}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 3.8}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 4.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 4.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 4.6667}, {"curve": "stepped", "angle": 0, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.2333}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 5.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 5.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 6}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 6.3333}, {"angle": 0, "time": 6.6667}]}, "Wave Long Left Horiontal 7": {"translate": [{"x": -34.91, "y": 0, "time": 0}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 1}, {"x": 109.63, "y": 0, "time": 1.1667}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 109.63, "y": 0, "time": 2.8333}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 4.3333}, {"x": 109.63, "y": 0, "time": 4.5}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 6}, {"x": 109.63, "y": 0, "time": 6.1667}, {"x": -34.91, "y": 0, "time": 6.6667}]}, "Fabric 1": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.2333}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 0.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 0.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 1.3333}, {"curve": "stepped", "angle": 0, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.9}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 2.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 2.4}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 3}, {"curve": "stepped", "angle": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.5667}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 3.8}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 4.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 4.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 4.6667}, {"curve": "stepped", "angle": 0, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.2333}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 5.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 5.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 6}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 6.3333}, {"angle": 0, "time": 6.6667}]}, "Fabric 4": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.2333}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 0.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 0.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 1.3333}, {"curve": "stepped", "angle": 0, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.9}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 2.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 2.4}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 3}, {"curve": "stepped", "angle": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.5667}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 3.8}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 4.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 4.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 4.6667}, {"curve": "stepped", "angle": 0, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.2333}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 5.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 5.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 6}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 6.3333}, {"angle": 0, "time": 6.6667}]}, "Fabric 3": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.2333}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 0.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 0.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 1.3333}, {"curve": "stepped", "angle": 0, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.9}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 2.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 2.4}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 3}, {"curve": "stepped", "angle": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.5667}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 3.8}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 4.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 4.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 4.6667}, {"curve": "stepped", "angle": 0, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.2333}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 5.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 5.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 6}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 6.3333}, {"angle": 0, "time": 6.6667}]}, "Fabric 6": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.2333}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 0.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 0.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 1.3333}, {"curve": "stepped", "angle": 0, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.9}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 2.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 2.4}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 3}, {"curve": "stepped", "angle": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.5667}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 3.8}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 4.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 4.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 4.6667}, {"curve": "stepped", "angle": 0, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.2333}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 5.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 5.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 6}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 6.3333}, {"angle": 0, "time": 6.6667}]}, "Fabric 5": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.2333}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 0.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 0.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 1.3333}, {"curve": "stepped", "angle": 0, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.9}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 2.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 2.4}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 3}, {"curve": "stepped", "angle": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.5667}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 3.8}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 4.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 4.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 4.6667}, {"curve": "stepped", "angle": 0, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.2333}, {"curve": [0.25, 0, 0.75, 1], "angle": 11.19, "time": 5.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.4, "time": 5.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.11, "time": 6}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.28, "time": 6.3333}, {"angle": 0, "time": 6.6667}]}, "Wave Long Left Horiontal 1": {"translate": [{"x": 109.63, "y": 0, "time": 0}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 1.5}, {"x": 109.63, "y": 0, "time": 1.6667}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 3.1667}, {"x": 109.63, "y": 0, "time": 3.3333}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 4.8333}, {"x": 109.63, "y": 0, "time": 5}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 6.5}, {"x": 109.63, "y": 0, "time": 6.6667}]}, "Head": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.2, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 4.2, "time": 5}, {"angle": 0, "time": 6.6667}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": -1.67, "y": -0.02, "time": 1.6667}, {"x": 0, "y": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": -0.19, "y": -2.71, "time": 5}, {"x": 0, "y": 0, "time": 6.6667}]}, "Wave Long Right Vertical 9": {"translate": [{"curve": [0.342, 0.36, 0.757, 1], "x": 0, "y": -2.79, "time": 0}, {"x": 0, "y": -125, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 32.57, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 5}, {"curve": [0.271, 0, 0.619, 0.41], "x": 0, "y": 32.57, "time": 6}, {"x": 0, "y": -2.79, "time": 6.6667}]}, "Loincloth Left 3": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 17.96, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.05, "time": 0.8333}, {"curve": "stepped", "angle": 0, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 17.96, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.05, "time": 2.5}, {"curve": "stepped", "angle": 0, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 17.96, "time": 3.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.05, "time": 4.1667}, {"curve": "stepped", "angle": 0, "time": 4.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 17.96, "time": 5.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.05, "time": 5.8333}, {"angle": 0, "time": 6}]}, "Loincloth Left 2": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 17.96, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.05, "time": 0.8333}, {"curve": "stepped", "angle": 0, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 17.96, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.05, "time": 2.5}, {"curve": "stepped", "angle": 0, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 17.96, "time": 3.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.05, "time": 4.1667}, {"curve": "stepped", "angle": 0, "time": 4.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 17.96, "time": 5.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.05, "time": 5.8333}, {"angle": 0, "time": 6}]}, "Loincloth Left 1": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 22.82, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.05, "time": 0.8333}, {"curve": "stepped", "angle": 0, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 22.82, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.05, "time": 2.5}, {"curve": "stepped", "angle": 0, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 22.82, "time": 3.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.05, "time": 4.1667}, {"curve": "stepped", "angle": 0, "time": 4.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 22.82, "time": 5.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.05, "time": 5.8333}, {"angle": 0, "time": 6}]}, "Wave Long Right Horiontal 12": {"translate": [{"x": -64.4, "y": 63.48, "time": 0}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 2.6667}, {"x": 109.63, "y": -31.84, "time": 3.3333}, {"x": -64.4, "y": 63.48, "time": 6.6667}]}, "Frame Light Left": {"scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 2, "y": 2, "time": 0.8333}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 2, "y": 2, "time": 2.5}, {"curve": "stepped", "x": 0, "y": 0, "time": 3.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 3.5}, {"curve": [0.25, 0, 0.75, 1], "x": 2, "y": 2, "time": 4.1667}, {"curve": "stepped", "x": 0, "y": 0, "time": 4.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 5.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 2, "y": 2, "time": 5.8333}, {"x": 0, "y": 0, "time": 6.5}], "translate": [{"curve": "stepped", "x": 0, "y": -115, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -115, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 115, "time": 1.3333}, {"curve": "stepped", "x": 0, "y": -115, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -115, "time": 2}, {"curve": "stepped", "x": 0, "y": 115, "time": 3}, {"curve": "stepped", "x": 0, "y": -115, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -115, "time": 3.6667}, {"curve": "stepped", "x": 0, "y": 115, "time": 4.6667}, {"curve": "stepped", "x": 0, "y": -115, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -115, "time": 5.3333}, {"curve": "stepped", "x": 0, "y": 115, "time": 6.3333}, {"x": 0, "y": -115, "time": 6.6667}]}, "Wave Long Right Vertical 8": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 1.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 2.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 4.5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 6.1667}, {"x": 0, "y": 25, "time": 6.6667}]}, "Wave Long Right Vertical 7": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 32.57, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 1.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 2.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 32.57, "time": 3.3333}, {"x": 0, "y": -125, "time": 4.5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 6.1667}, {"x": 0, "y": 32.57, "time": 6.6667}]}, "Wave Long Right Vertical 6": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 2}, {"x": 0, "y": -125, "time": 6.6667}]}, "Wave Long Right Vertical 5": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 32.57, "time": 2}, {"x": 0, "y": -125, "time": 6.6667}]}, "Wave Long Right Vertical 4": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 4.3333}, {"x": 0, "y": -125, "time": 6.6667}]}, "Wave Long Right Vertical 3": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 32.57, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 32.57, "time": 4.3333}, {"x": 0, "y": -125, "time": 6.6667}]}, "Wave Long Right Vertical 2": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 3.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 5.5}, {"x": 0, "y": -125, "time": 6.6667}]}, "Loincloth Right 1": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -7.46, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.82, "time": 0.8333}, {"curve": "stepped", "angle": 0, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -7.46, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.82, "time": 2.5}, {"curve": "stepped", "angle": 0, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -7.46, "time": 3.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.82, "time": 4.1667}, {"curve": "stepped", "angle": 0, "time": 4.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -7.46, "time": 5.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.82, "time": 5.8333}, {"angle": 0, "time": 6}]}, "Wave Long Right Vertical 1": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 32.57, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 32.57, "time": 3.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 5.5}, {"x": 0, "y": -125, "time": 6.6667}]}, "Fabric Position": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": -4.07, "y": 0.14, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": -4.07, "y": 0.14, "time": 5}, {"x": 0, "y": 0, "time": 6.6667}]}, "Wave Long Left Vertical 10": {"translate": [{"curve": [0.342, 0.36, 0.757, 1], "x": 0, "y": -8.66, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 5}, {"curve": [0.271, 0, 0.619, 0.41], "x": 0, "y": 25, "time": 6}, {"x": 0, "y": -8.66, "time": 6.6667}]}, "Wave Long Left Vertical 11": {"translate": [{"curve": [0.342, 0.36, 0.757, 1], "x": 0, "y": -2.79, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 3.3333}, {"curve": [0.271, 0, 0.619, 0.41], "x": 0, "y": 32.57, "time": 5.3333}, {"x": 0, "y": -2.79, "time": 6.6667}]}, "Wave Long Left Vertical 12": {"translate": [{"curve": [0.342, 0.36, 0.757, 1], "x": 0, "y": -8.66, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 3.3333}, {"curve": [0.271, 0, 0.619, 0.41], "x": 0, "y": 25, "time": 5.3333}, {"x": 0, "y": -8.66, "time": 6.6667}]}, "Neck": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": -6.61, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.86, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -6.61, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.86, "time": 5}, {"angle": -6.61, "time": 6.6667}]}, "Gem Sparkle 1": {"rotate": [{"curve": "stepped", "angle": -90, "time": 0}, {"angle": 0, "time": 0.6667}, {"curve": "stepped", "angle": -90, "time": 1.6667}, {"angle": 0, "time": 2.3333}, {"curve": "stepped", "angle": -90, "time": 3.3333}, {"angle": 0, "time": 4}, {"curve": "stepped", "angle": -90, "time": 5}, {"angle": 0, "time": 5.6667}, {"angle": -90, "time": 6.6667}], "scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.6667}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 1.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.3333}, {"curve": "stepped", "x": 1, "y": 1, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 3}, {"curve": "stepped", "x": 0, "y": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 4}, {"curve": "stepped", "x": 1, "y": 1, "time": 4.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 4.6667}, {"curve": "stepped", "x": 0, "y": 0, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 5.6667}, {"curve": "stepped", "x": 1, "y": 1, "time": 6}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 6.3333}, {"x": 0, "y": 0, "time": 6.6667}]}, "Gem Sparkle 2": {"rotate": [{"curve": "stepped", "angle": -90, "time": 0}, {"angle": 0, "time": 0.6667}, {"curve": "stepped", "angle": -90, "time": 1.6667}, {"angle": 0, "time": 2.3333}, {"curve": "stepped", "angle": -90, "time": 3.3333}, {"angle": 0, "time": 4}, {"curve": "stepped", "angle": -90, "time": 5}, {"angle": 0, "time": 5.6667}, {"angle": -90, "time": 6.6667}], "scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.6667}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 1.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.3333}, {"curve": "stepped", "x": 1, "y": 1, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 3}, {"curve": "stepped", "x": 0, "y": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 4}, {"curve": "stepped", "x": 1, "y": 1, "time": 4.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 4.6667}, {"curve": "stepped", "x": 0, "y": 0, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 5.6667}, {"curve": "stepped", "x": 1, "y": 1, "time": 6}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 6.3333}, {"x": 0, "y": 0, "time": 6.6667}]}, "Wave Splash Big 4": {"scale": [{"curve": [0.378, 0.51, 0.748, 1], "x": 1.016, "y": 1.23, "time": 0}, {"curve": "stepped", "x": 1.2, "y": 1.2, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0.5, "y": 0.5, "time": 3.3333}, {"curve": [0.248, 0, 0.628, 0.52], "x": 0.8, "y": 1.264, "time": 3.6667}, {"x": 1.016, "y": 1.23, "time": 6.6667}], "translate": [{"curve": [0.378, 0.51, 0.748, 1], "x": 0, "y": -148.19, "time": 0}, {"curve": "stepped", "x": 0, "y": -275, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -275, "time": 3.3333}, {"curve": [0.248, 0, 0.628, 0.52], "x": 0, "y": 0, "time": 3.6667}, {"x": 0, "y": -148.19, "time": 6.6667}]}, "Wave Splash Big 2": {"scale": [{"curve": [0.368, 0.47, 0.753, 1], "x": 1.083, "y": 1.229, "time": 0}, {"curve": "stepped", "x": 1.2, "y": 1.2, "time": 1.5}, {"curve": [0.25, 0, 0.75, 1], "x": 0.5, "y": 0.5, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1.25, "time": 2.1667}, {"curve": "stepped", "x": 1.2, "y": 1.2, "time": 4.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 0.5, "y": 0.5, "time": 5}, {"curve": [0.254, 0, 0.62, 0.47], "x": 1, "y": 1.25, "time": 5.5}, {"x": 1.083, "y": 1.229, "time": 6.6667}], "translate": [{"curve": [0.368, 0.47, 0.753, 1], "x": 0, "y": -114.79, "time": 0}, {"curve": "stepped", "x": 0, "y": -275, "time": 1.5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -275, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.1667}, {"curve": "stepped", "x": 0, "y": -275, "time": 4.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -275, "time": 5}, {"curve": [0.254, 0, 0.62, 0.47], "x": 0, "y": 0, "time": 5.5}, {"x": 0, "y": -114.79, "time": 6.6667}]}, "Wave Splash Big 3": {"scale": [{"curve": [0.25, 0, 0.75, 1], "x": 0.5, "y": 0.5, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0.8, "y": 1.264, "time": 0.3333}, {"curve": "stepped", "x": 1.2, "y": 1.2, "time": 6}, {"x": 0.5, "y": 0.5, "time": 6.6667}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -275, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": -275, "time": 6}]}, "Frame Light Right": {"scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 2, "y": 2, "time": 0.8333}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 2, "y": 2, "time": 2.5}, {"curve": "stepped", "x": 0, "y": 0, "time": 3.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 3.5}, {"curve": [0.25, 0, 0.75, 1], "x": 2, "y": 2, "time": 4.1667}, {"curve": "stepped", "x": 0, "y": 0, "time": 4.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 5.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 2, "y": 2, "time": 5.8333}, {"x": 0, "y": 0, "time": 6.5}], "translate": [{"curve": "stepped", "x": 0, "y": -115, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -115, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 115, "time": 1.3333}, {"curve": "stepped", "x": 0, "y": -115, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -115, "time": 2}, {"curve": "stepped", "x": 0, "y": 115, "time": 3}, {"curve": "stepped", "x": 0, "y": -115, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -115, "time": 3.6667}, {"curve": "stepped", "x": 0, "y": 115, "time": 4.6667}, {"curve": "stepped", "x": 0, "y": -115, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -115, "time": 5.3333}, {"curve": "stepped", "x": 0, "y": 115, "time": 6.3333}, {"x": 0, "y": -115, "time": 6.6667}]}, "Forearm Left": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 1.22, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.06, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.22, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 12.06, "time": 5}, {"angle": 1.22, "time": 6.6667}]}, "Wave Long Right Vertical 12": {"translate": [{"curve": [0.342, 0.36, 0.757, 1], "x": 0, "y": -8.66, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 3.3333}, {"curve": [0.271, 0, 0.619, 0.41], "x": 0, "y": 25, "time": 5.3333}, {"x": 0, "y": -8.66, "time": 6.6667}]}, "Wave Long Right Vertical 11": {"translate": [{"curve": [0.342, 0.36, 0.757, 1], "x": 0, "y": -2.79, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 3.3333}, {"curve": [0.271, 0, 0.619, 0.41], "x": 0, "y": 32.57, "time": 5.3333}, {"x": 0, "y": -2.79, "time": 6.6667}]}, "Wave Long Right Vertical 10": {"translate": [{"curve": [0.342, 0.36, 0.757, 1], "x": 0, "y": -8.66, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -125, "time": 5}, {"curve": [0.271, 0, 0.619, 0.41], "x": 0, "y": 25, "time": 6}, {"x": 0, "y": -8.66, "time": 6.6667}]}, "Hair Right Rotation": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 5.34, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -7.02, "time": 3.3333}, {"angle": 5.34, "time": 6.6667}]}, "Wild All": {"scale": [{"x": 1, "y": 1, "time": 0}, {"x": 1.481, "y": 1.481, "time": 0.2667}, {"x": 0.969, "y": 0.969, "time": 0.4667}, {"x": 1.031, "y": 1.031, "time": 0.5333}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.6333}, {"x": 1, "y": 1, "time": 3.3333}, {"x": 1.481, "y": 1.481, "time": 3.6}, {"x": 0.969, "y": 0.969, "time": 3.8}, {"x": 1.031, "y": 1.031, "time": 3.8667}, {"x": 1, "y": 1, "time": 3.9667}]}, "Wave Long Left Horiontal 11": {"translate": [{"x": -131.27, "y": 0, "time": 0}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 2.6667}, {"x": 109.63, "y": 0, "time": 3.3333}, {"x": -131.27, "y": 0, "time": 6.6667}]}, "Wave Long Left Horiontal 12": {"translate": [{"x": -64.4, "y": 63.48, "time": 0}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 2.6667}, {"x": 109.63, "y": -31.84, "time": 3.3333}, {"x": -64.4, "y": 63.48, "time": 6.6667}]}, "Wave Long Left Horiontal 10": {"translate": [{"x": -64.4, "y": 63.48, "time": 0}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 1.3333}, {"x": 109.63, "y": -31.84, "time": 1.6667}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 4.6667}, {"x": 109.63, "y": -31.84, "time": 5}, {"x": -64.4, "y": 63.48, "time": 6.6667}]}, "Hand Right": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.12, "time": 3.3333}, {"angle": 0, "time": 6.6667}], "scale": [{"curve": [0.25, 0, 0.75, 1], "x": 0.95, "y": 1, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0.95, "y": 1, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 5}, {"x": 0.95, "y": 1, "time": 6.6667}]}, "Loincloth Right 3": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -9.68, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -7.08, "time": 0.8333}, {"curve": "stepped", "angle": 0, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -9.68, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -7.08, "time": 2.5}, {"curve": "stepped", "angle": 0, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -9.68, "time": 3.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4}, {"curve": [0.25, 0, 0.75, 1], "angle": -7.08, "time": 4.1667}, {"curve": "stepped", "angle": 0, "time": 4.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -9.68, "time": 5.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -7.08, "time": 5.8333}, {"angle": 0, "time": 6}]}, "Loincloth Right 2": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -7.46, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.82, "time": 0.8333}, {"curve": "stepped", "angle": 0, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -7.46, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.82, "time": 2.5}, {"curve": "stepped", "angle": 0, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -7.46, "time": 3.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.82, "time": 4.1667}, {"curve": "stepped", "angle": 0, "time": 4.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -7.46, "time": 5.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": -2.82, "time": 5.8333}, {"angle": 0, "time": 6}]}, "Hair Behind 5": {"rotate": [{"curve": [0.381, 0.55, 0.742, 1], "angle": 23.73, "time": 0}, {"curve": [0.305, 0, 0.64, 0.36], "angle": 37.55, "time": 0.1333}, {"curve": [0.278, 0.15, 0.651, 0.61], "angle": 36.03, "time": 0.1667}, {"curve": [0.381, 0.55, 0.742, 1], "angle": 13.81, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": 37.55, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": 37.55, "time": 1.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.8}, {"curve": [0.25, 0, 0.75, 1], "angle": 37.55, "time": 2.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": 37.55, "time": 2.8}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": 37.55, "time": 3.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.8}, {"curve": [0.25, 0, 0.75, 1], "angle": 37.55, "time": 4.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": 37.55, "time": 4.8}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": 37.55, "time": 5.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.8}, {"curve": [0.25, 0, 0.75, 1], "angle": 37.55, "time": 6.1333}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 0, "time": 6.4667}, {"angle": 23.73, "time": 6.6667}]}, "Hair Behind 4": {"rotate": [{"curve": [0.382, 0.58, 0.731, 1], "angle": 12.15, "time": 0}, {"curve": [0.284, 0, 0.625, 0.38], "angle": 16.04, "time": 0.1}, {"curve": [0.303, 0.24, 0.674, 0.69], "angle": 13.94, "time": 0.1667}, {"curve": [0.382, 0.58, 0.731, 1], "angle": 3.88, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.4333}, {"curve": [0.25, 0, 0.75, 1], "angle": 16.04, "time": 0.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.1}, {"curve": [0.25, 0, 0.75, 1], "angle": 16.04, "time": 1.4333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": 16.04, "time": 2.1}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.4333}, {"curve": [0.25, 0, 0.75, 1], "angle": 16.04, "time": 2.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.1}, {"curve": [0.25, 0, 0.75, 1], "angle": 16.04, "time": 3.4333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": 16.04, "time": 4.1}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.4333}, {"curve": [0.25, 0, 0.75, 1], "angle": 16.04, "time": 4.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.1}, {"curve": [0.25, 0, 0.75, 1], "angle": 16.04, "time": 5.4333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": 16.04, "time": 6.1}, {"curve": [0.243, 0, 0.655, 0.63], "angle": 0, "time": 6.4333}, {"angle": 12.15, "time": 6.6667}]}, "Wave Long Right Horiontal 8": {"translate": [{"curve": [0.351, 0.4, 0.757, 1], "x": 20.73, "y": 16.85, "time": 0}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 1}, {"x": 109.63, "y": -31.84, "time": 1.1667}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 2.6667}, {"x": 109.63, "y": -31.84, "time": 2.8333}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 4.3333}, {"x": 109.63, "y": -31.84, "time": 4.5}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 6}, {"curve": [0.265, 0, 0.618, 0.43], "x": 109.63, "y": -31.84, "time": 6.1667}, {"x": 20.73, "y": 16.85, "time": 6.6667}]}, "Hair Behind 3": {"rotate": [{"curve": [0.375, 0.62, 0.716, 1], "angle": 4.69, "time": 0}, {"curve": [0.269, 0, 0.618, 0.42], "angle": 5.39, "time": 0.0667}, {"curve": [0.326, 0.31, 0.697, 0.76], "angle": 4.07, "time": 0.1667}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 0.7, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.4}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 0.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 1.4}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 2.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.4}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 2.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 3.4}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 4.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.4}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 4.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 5.4}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 6.0667}, {"curve": [0.243, 0, 0.68, 0.71], "angle": 0, "time": 6.4}, {"angle": 4.69, "time": 6.6667}]}, "Wave Long Right Horiontal 7": {"translate": [{"x": -34.91, "y": 0, "time": 0}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 1}, {"x": 109.63, "y": 0, "time": 1.1667}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 109.63, "y": 0, "time": 2.8333}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 4.3333}, {"x": 109.63, "y": 0, "time": 4.5}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 6}, {"x": 109.63, "y": 0, "time": 6.1667}, {"x": -34.91, "y": 0, "time": 6.6667}]}, "Hair Behind 2": {"rotate": [{"curve": [0.36, 0.64, 0.695, 1], "angle": 5.16, "time": 0}, {"curve": [0.258, 0, 0.619, 0.45], "angle": 5.39, "time": 0.0333}, {"curve": [0.349, 0.39, 0.722, 0.85], "angle": 3.38, "time": 0.1667}, {"curve": [0.36, 0.64, 0.695, 1], "angle": 0.23, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.3667}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 0.7}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.0333}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 1.3667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.7}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 2.0333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.3667}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 2.7}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.0333}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 3.3667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.7}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 4.0333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.3667}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 4.7}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.0333}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 5.3667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.7}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 6.0333}, {"curve": [0.245, 0, 0.711, 0.83], "angle": 0, "time": 6.3667}, {"angle": 5.16, "time": 6.6667}]}, "Hair Behind 1": {"rotate": [{"curve": [0.25, 0, 0.625, 0.5], "angle": 5.39, "time": 0}, {"curve": [0.375, 0.5, 0.75, 1], "angle": 2.7, "time": 0.1667}, {"angle": 0, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 4}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 4.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 5.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 6}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 6.3333}, {"angle": 5.39, "time": 6.6667}]}, "Wave Long Right Horiontal 9": {"translate": [{"x": -131.27, "y": 0, "time": 0}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 1.3333}, {"x": 109.63, "y": 0, "time": 1.6667}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 4.6667}, {"x": 109.63, "y": 0, "time": 5}, {"x": -131.27, "y": 0, "time": 6.6667}]}, "Wave Long Right Horiontal 4": {"translate": [{"x": 109.63, "y": -31.84, "time": 0}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 3}, {"x": 109.63, "y": -31.84, "time": 3.3333}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 6.3333}, {"x": 109.63, "y": -31.84, "time": 6.6667}]}, "Wild Mask": {"translate": [{"x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 229.09, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 1}, {"curve": "stepped", "x": 229.09, "y": 0, "time": 1.6667}, {"x": 0, "y": 0, "time": 2.2667}, {"curve": "stepped", "x": 229.09, "y": 0, "time": 2.9333}, {"x": 0, "y": 0, "time": 3.2667}, {"curve": "stepped", "x": 229.09, "y": 0, "time": 3.9333}, {"x": 0, "y": 0, "time": 4.4667}, {"curve": "stepped", "x": 229.09, "y": 0, "time": 5.1333}, {"x": 0, "y": 0, "time": 5.4667}, {"x": 229.09, "y": 0, "time": 6.1333}]}, "Wave Long Right Horiontal 3": {"translate": [{"x": 109.63, "y": 0, "time": 0}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 3}, {"x": 109.63, "y": 0, "time": 3.3333}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 6.3333}, {"x": 109.63, "y": 0, "time": 6.6667}]}, "Wave Long Right Horiontal 6": {"translate": [{"x": 109.63, "y": -31.84, "time": 0}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 6}, {"x": 109.63, "y": -31.84, "time": 6.6667}]}, "Wave Long Right Horiontal 5": {"translate": [{"x": 109.63, "y": 0, "time": 0}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 6}, {"x": 109.63, "y": 0, "time": 6.6667}]}, "Hair Right 1": {"rotate": [{"curve": [0.375, 0.5, 0.75, 1], "angle": 2.7, "time": 0}, {"angle": 0, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 1.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 1.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 3.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 3.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 4.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 5.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 5.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 6.1667}, {"curve": [0.25, 0, 0.625, 0.5], "angle": 5.39, "time": 6.5}, {"angle": 2.7, "time": 6.6667}]}, "Wave Long Right Horiontal 2": {"translate": [{"x": 109.63, "y": -31.84, "time": 0}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 1.5}, {"x": 109.63, "y": -31.84, "time": 1.6667}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 3.1667}, {"x": 109.63, "y": -31.84, "time": 3.3333}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 4.8333}, {"x": 109.63, "y": -31.84, "time": 5}, {"curve": "stepped", "x": -203.63, "y": 139.74, "time": 6.5}, {"x": 109.63, "y": -31.84, "time": 6.6667}]}, "Wave Long Right Horiontal 1": {"translate": [{"x": 109.63, "y": 0, "time": 0}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 1.5}, {"x": 109.63, "y": 0, "time": 1.6667}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 3.1667}, {"x": 109.63, "y": 0, "time": 3.3333}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 4.8333}, {"x": 109.63, "y": 0, "time": 5}, {"curve": "stepped", "x": -323.99, "y": 0, "time": 6.5}, {"x": 109.63, "y": 0, "time": 6.6667}]}, "Hair Left Rotation": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0.29, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -7.98, "time": 3.3333}, {"angle": 0.29, "time": 6.6667}]}, "Hair Right 2": {"rotate": [{"curve": [0.349, 0.39, 0.722, 0.85], "angle": 3.38, "time": 0}, {"curve": [0.36, 0.64, 0.695, 1], "angle": 0.23, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.2}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 0.5333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.8667}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 1.2}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.5333}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 1.8667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.2}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 2.5333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.8667}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 3.2}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.5333}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 3.8667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.2}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 4.5333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.8667}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 5.2}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.5333}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 5.8667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 6.2}, {"curve": [0.258, 0, 0.619, 0.45], "angle": 5.39, "time": 6.5333}, {"angle": 3.38, "time": 6.6667}]}, "Hair Right 3": {"rotate": [{"curve": [0.326, 0.31, 0.697, 0.76], "angle": 4.07, "time": 0}, {"curve": [0.375, 0.62, 0.716, 1], "angle": 0.7, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.2333}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 0.5667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.9}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 1.2333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.5667}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 1.9}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.2333}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 2.5667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.9}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 3.2333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.5667}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 3.9}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.2333}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 4.5667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.9}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 5.2333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.5667}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.39, "time": 5.9}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 6.2333}, {"curve": [0.269, 0, 0.618, 0.42], "angle": 5.39, "time": 6.5667}, {"angle": 4.07, "time": 6.6667}]}, "Hair Right 4": {"rotate": [{"curve": [0.303, 0.24, 0.674, 0.69], "angle": 13.94, "time": 0}, {"curve": [0.382, 0.58, 0.731, 1], "angle": 3.88, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": 16.04, "time": 0.6}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.9333}, {"curve": [0.25, 0, 0.75, 1], "angle": 16.04, "time": 1.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.6}, {"curve": [0.25, 0, 0.75, 1], "angle": 16.04, "time": 1.9333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": 16.04, "time": 2.6}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.9333}, {"curve": [0.25, 0, 0.75, 1], "angle": 16.04, "time": 3.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.6}, {"curve": [0.25, 0, 0.75, 1], "angle": 16.04, "time": 3.9333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": 16.04, "time": 4.6}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.9333}, {"curve": [0.25, 0, 0.75, 1], "angle": 16.04, "time": 5.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.6}, {"curve": [0.25, 0, 0.75, 1], "angle": 16.04, "time": 5.9333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 6.2667}, {"curve": [0.284, 0, 0.625, 0.38], "angle": 16.04, "time": 6.6}, {"angle": 13.94, "time": 6.6667}]}, "Hair Right 5": {"rotate": [{"curve": [0.278, 0.15, 0.651, 0.61], "angle": 36.03, "time": 0}, {"curve": [0.381, 0.55, 0.742, 1], "angle": 13.81, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.3}, {"curve": [0.25, 0, 0.75, 1], "angle": 37.55, "time": 0.6333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.9667}, {"curve": [0.25, 0, 0.75, 1], "angle": 37.55, "time": 1.3}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.6333}, {"curve": [0.25, 0, 0.75, 1], "angle": 37.55, "time": 1.9667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.3}, {"curve": [0.25, 0, 0.75, 1], "angle": 37.55, "time": 2.6333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.9667}, {"curve": [0.25, 0, 0.75, 1], "angle": 37.55, "time": 3.3}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.6333}, {"curve": [0.25, 0, 0.75, 1], "angle": 37.55, "time": 3.9667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.3}, {"curve": [0.25, 0, 0.75, 1], "angle": 37.55, "time": 4.6333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.9667}, {"curve": [0.25, 0, 0.75, 1], "angle": 37.55, "time": 5.3}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.6333}, {"curve": [0.25, 0, 0.75, 1], "angle": 37.55, "time": 5.9667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 6.3}, {"curve": [0.305, 0, 0.64, 0.36], "angle": 37.55, "time": 6.6333}, {"angle": 36.03, "time": 6.6667}]}, "Body": {"translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -5.94, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -5.94, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -5.94, "time": 4.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -5.94, "time": 6}, {"x": 0, "y": 0, "time": 6.6667}]}, "Hair Left 1": {"rotate": [{"curve": [0.36, 0.64, 0.695, 1], "angle": 0.1, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.0333}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 0.3667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.7}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 1.0333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.3667}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 1.7}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.0333}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 2.3667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.7}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 3.0333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.3667}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 3.7}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.0333}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 4.3667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.7}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 5.0333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.3667}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 5.7}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 6.0333}, {"curve": [0.245, 0, 0.711, 0.83], "angle": 2.4, "time": 6.3667}, {"angle": 0.1, "time": 6.6667}]}, "Hair Left 2": {"rotate": [{"curve": [0.375, 0.62, 0.716, 1], "angle": 0.31, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 0.4}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 1.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.4}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 1.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 2.4}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 3.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.4}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 3.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 4.4}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 5.0667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.4}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 5.7333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 6.0667}, {"curve": [0.243, 0, 0.68, 0.71], "angle": 2.4, "time": 6.4}, {"angle": 0.31, "time": 6.6667}]}, "Arm Right": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 3.05, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.24, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 3.05, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.24, "time": 5}, {"angle": 3.05, "time": 6.6667}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": -0.58, "y": 2.31, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": -0.58, "y": 2.31, "time": 5}, {"x": 0, "y": 0, "time": 6.6667}]}, "Forearm Right": {"rotate": [{"curve": [0.375, 0.5, 0.75, 1], "angle": 9.06, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 16.45, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "angle": 1.67, "time": 2.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 16.45, "time": 4.1667}, {"curve": [0.25, 0, 0.625, 0.5], "angle": 1.67, "time": 5.8333}, {"angle": 9.06, "time": 6.6667}]}, "Hair Left 3": {"rotate": [{"curve": [0.382, 0.58, 0.731, 1], "angle": 0.58, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.1}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 0.4333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 1.1}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.4333}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 1.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.1}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 2.4333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 3.1}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.4333}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 3.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.1}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 4.4333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 5.1}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.4333}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.4, "time": 5.7667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 6.1}, {"curve": [0.243, 0, 0.655, 0.63], "angle": 2.4, "time": 6.4333}, {"angle": 0.58, "time": 6.6667}]}, "Hair Left 4": {"rotate": [{"curve": [0.381, 0.55, 0.742, 1], "angle": 7.49, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": 20.37, "time": 0.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "angle": 20.37, "time": 1.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": 20.37, "time": 1.8}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": 20.37, "time": 2.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.8}, {"curve": [0.25, 0, 0.75, 1], "angle": 20.37, "time": 3.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": 20.37, "time": 3.8}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": 20.37, "time": 4.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.8}, {"curve": [0.25, 0, 0.75, 1], "angle": 20.37, "time": 5.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": 20.37, "time": 5.8}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 6.1333}, {"curve": [0.245, 0, 0.637, 0.56], "angle": 20.37, "time": 6.4667}, {"angle": 7.49, "time": 6.6667}]}, "Wave Splash Small 1": {"scale": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.5}, {"curve": "stepped", "x": 1.5, "y": 1.5, "time": 1.5333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 2.1667}, {"curve": "stepped", "x": 1.5, "y": 1.5, "time": 3.2}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 3.8333}, {"curve": "stepped", "x": 1.5, "y": 1.5, "time": 4.8667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 5.5}, {"curve": "stepped", "x": 1.5, "y": 1.5, "time": 6.5333}, {"x": 0, "y": 0, "time": 6.6667}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -100, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 1.05, "y": 155.78, "time": 0.5}, {"curve": "stepped", "x": 0, "y": -100, "time": 1.5333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -100, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 1.05, "y": 155.78, "time": 2.1667}, {"curve": "stepped", "x": 0, "y": -100, "time": 3.2}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -100, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1.05, "y": 155.78, "time": 3.8333}, {"curve": "stepped", "x": 0, "y": -100, "time": 4.8667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -100, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": 1.05, "y": 155.78, "time": 5.5}, {"x": 0, "y": -100, "time": 6.5333}]}, "Hair Left 5": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 29.18, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 29.18, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 29.18, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 29.18, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 29.18, "time": 3}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 29.18, "time": 3.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4}, {"curve": [0.25, 0, 0.75, 1], "angle": 29.18, "time": 4.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 4.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 29.18, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 5.3333}, {"curve": [0.25, 0, 0.75, 1], "angle": 29.18, "time": 5.6667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 6}, {"curve": [0.25, 0, 0.75, 1], "angle": 29.18, "time": 6.3333}, {"angle": 0, "time": 6.6667}]}, "Wave Splash Small 2": {"scale": [{"curve": [0.25, 0, 0.75, 1], "x": 1.3, "y": 1.5, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 1.1, "y": 1.059, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1.3, "y": 1.5, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": 1.1, "y": 1.059, "time": 5.6667}, {"x": 1.3, "y": 1.5, "time": 6.6667}], "translate": [{"curve": "stepped", "x": 0, "y": -100, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -100, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 1.05, "y": 155.78, "time": 2.3333}, {"curve": "stepped", "x": 0, "y": -100, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -100, "time": 5}, {"curve": [0.25, 0, 0.75, 1], "x": 1.05, "y": 155.78, "time": 5.6667}, {"x": 0, "y": -100, "time": 6.6667}]}}, "deform": {"default": {"Eyes": {"Eyes": [{"curve": "stepped", "vertices": [-0.07335], "time": 0}, {"vertices": [-0.07335], "curve": [0.25, 0, 0.75, 1], "time": 1.1667}, {"vertices": [-0.07335, 0.00318, 0, 0, 0, 0, 0, 0, -0.19722, 0.00858, -1.51715, 0.06398, -1.36537, 0.73393, 0.18224, -0.00792, 0.53865, -0.02342, 0.53865, -0.02342, -2.1994, 0.09563, -1.82068, 0.08059], "curve": [0.25, 0, 0.75, 1], "time": 1.3333}, {"curve": "stepped", "vertices": [-0.07335], "time": 1.6667}, {"vertices": [-0.07335], "curve": [0.25, 0, 0.75, 1], "time": 2.8333}, {"vertices": [-0.07335, 0.00318, 0, 0, 0, 0, 0, 0, -0.19722, 0.00858, -1.51715, 0.06398, -1.36537, 0.73393, 0.18224, -0.00792, 0.53865, -0.02342, 0.53865, -0.02342, -2.1994, 0.09563, -1.82068, 0.08059], "curve": [0.25, 0, 0.75, 1], "time": 3}, {"curve": "stepped", "vertices": [-0.07335], "time": 3.3333}, {"vertices": [-0.07335], "curve": [0.25, 0, 0.75, 1], "time": 4.5}, {"vertices": [-0.07335, 0.00318, 0, 0, 0, 0, 0, 0, -0.19722, 0.00858, -1.51715, 0.06398, -1.36537, 0.73393, 0.18224, -0.00792, 0.53865, -0.02342, 0.53865, -0.02342, -2.1994, 0.09563, -1.82068, 0.08059], "curve": [0.25, 0, 0.75, 1], "time": 4.6667}, {"curve": "stepped", "vertices": [-0.07335], "time": 5}, {"vertices": [-0.07335], "curve": [0.25, 0, 0.75, 1], "time": 6.1667}, {"vertices": [-0.07335, 0.00318, 0, 0, 0, 0, 0, 0, -0.19722, 0.00858, -1.51715, 0.06398, -1.36537, 0.73393, 0.18224, -0.00792, 0.53865, -0.02342, 0.53865, -0.02342, -2.1994, 0.09563, -1.82068, 0.08059], "curve": [0.25, 0, 0.75, 1], "time": 6.3333}, {"vertices": [-0.07335], "time": 6.6667}]}, "Mouth": {"Mouth": [{"offset": 2, "curve": "stepped", "vertices": [-0.05859, 0.00255, 0, 0, 0, 0, 0, 0, 0, 0, 1.67931, -0.07303], "time": 0}, {"offset": 2, "vertices": [-0.05859, 0.00255, 0, 0, 0, 0, 0, 0, 0, 0, 1.67931, -0.07303], "curve": [0.25, 0, 0.75, 1], "time": 0.3333}, {"offset": 2, "curve": "stepped", "vertices": [-0.05859], "time": 0.6667}, {"offset": 2, "vertices": [-0.05859], "curve": [0.25, 0, 0.75, 1], "time": 1.1667}, {"offset": 2, "curve": "stepped", "vertices": [-0.05859, 0.00255, 0, 0, 0, 0, 0, 0, 0, 0, 1.67931, -0.07303], "time": 1.5}, {"offset": 2, "vertices": [-0.05859, 0.00255, 0, 0, 0, 0, 0, 0, 0, 0, 1.67931, -0.07303], "curve": [0.25, 0, 0.75, 1], "time": 2}, {"offset": 2, "curve": "stepped", "vertices": [-0.05859], "time": 2.3333}, {"offset": 2, "vertices": [-0.05859], "curve": [0.25, 0, 0.75, 1], "time": 2.8333}, {"offset": 2, "curve": "stepped", "vertices": [-0.05859, 0.00255, 0, 0, 0, 0, 0, 0, 0, 0, 1.67931, -0.07303], "time": 3.1667}, {"offset": 2, "vertices": [-0.05859, 0.00255, 0, 0, 0, 0, 0, 0, 0, 0, 1.67931, -0.07303], "curve": [0.25, 0, 0.75, 1], "time": 3.6667}, {"offset": 2, "curve": "stepped", "vertices": [-0.05859], "time": 4}, {"offset": 2, "vertices": [-0.05859], "curve": [0.25, 0, 0.75, 1], "time": 4.5}, {"offset": 2, "curve": "stepped", "vertices": [-0.05859, 0.00255, 0, 0, 0, 0, 0, 0, 0, 0, 1.67931, -0.07303], "time": 4.8333}, {"offset": 2, "vertices": [-0.05859, 0.00255, 0, 0, 0, 0, 0, 0, 0, 0, 1.67931, -0.07303], "curve": [0.25, 0, 0.75, 1], "time": 5.3333}, {"offset": 2, "curve": "stepped", "vertices": [-0.05859], "time": 5.6667}, {"offset": 2, "vertices": [-0.05859], "curve": [0.25, 0, 0.75, 1], "time": 6.1667}, {"offset": 2, "vertices": [-0.05859, 0.00255, 0, 0, 0, 0, 0, 0, 0, 0, 1.67931, -0.07303], "time": 6.5}]}, "Shoulder": {"Shoulder": [{"vertices": [-1.83012, 1.25199], "curve": [0.25, 0, 0.75, 1], "time": 0}, {"vertices": [-0.33958, 6.96104, -1.00976, 4.77852, -0.65886, -4.83941, -0.49126, 2.32469, -0.32054, -2.3543, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.60757, -3.51426, 0.6115, 3.51362, 2.02276, -5.10398, -0.18565, 5.48716, -0.18565, 5.48716], "curve": [0.25, 0, 0.75, 1], "time": 1.6667}, {"vertices": [-1.83012, 1.25199], "curve": [0.25, 0, 0.75, 1], "time": 3.3333}, {"vertices": [-0.33958, 6.96104, -1.00976, 4.77852, -0.65886, -4.83941, -0.49126, 2.32469, -0.32054, -2.3543, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.60757, -3.51426, 0.6115, 3.51362, 2.02276, -5.10398, -0.18565, 5.48716, -0.18565, 5.48716], "curve": [0.25, 0, 0.75, 1], "time": 5}, {"vertices": [-1.83012, 1.25199], "time": 6.6667}]}, "Trident": {"Trident": [{"offset": 4, "vertices": [-0.14776, 1.49177, 1.12228, 6.18709, -0.62553, 9.29306, 0, 0, 4.97106, -8.80338, 5.08967, 0.43286, 0.34977, -3.52921, 0.00261, -0.02616, -0.42682, 0.45513, 0.35148, -3.54694, -0.52472, 5.29528], "curve": [0.25, 0, 0.75, 1], "time": 0}, {"offset": 4, "curve": "stepped", "vertices": [-1.21964, -0.12084, -1.21964, -0.12084, 0, 0, 0, 0, 0, 0, -1.21964, -0.12084, -1.21964, -0.12084, 0, 0, -0.42682, 0.45513], "time": 0.6667}, {"offset": 4, "vertices": [-1.21964, -0.12084, -1.21964, -0.12084, 0, 0, 0, 0, 0, 0, -1.21964, -0.12084, -1.21964, -0.12084, 0, 0, -0.42682, 0.45513], "curve": [0.25, 0, 0.75, 1], "time": 1}, {"offset": 4, "vertices": [-0.14776, 1.49177, 1.12228, 6.18709, -0.62553, 9.29306, 0, 0, 4.97106, -8.80338, 5.08967, 0.43286, 0.34977, -3.52921, 0.00261, -0.02616, -0.42682, 0.45513, 0.35148, -3.54694, -0.52472, 5.29528], "curve": [0.25, 0, 0.75, 1], "time": 1.6667}, {"offset": 4, "curve": "stepped", "vertices": [-1.21964, -0.12084, -1.21964, -0.12084, 0, 0, 0, 0, 0, 0, -1.21964, -0.12084, -1.21964, -0.12084, 0, 0, -0.42682, 0.45513], "time": 2.3333}, {"offset": 4, "vertices": [-1.21964, -0.12084, -1.21964, -0.12084, 0, 0, 0, 0, 0, 0, -1.21964, -0.12084, -1.21964, -0.12084, 0, 0, -0.42682, 0.45513], "curve": [0.25, 0, 0.75, 1], "time": 2.6667}, {"offset": 4, "vertices": [-0.14776, 1.49177, 1.12228, 6.18709, -0.62553, 9.29306, 0, 0, 4.97106, -8.80338, 5.08967, 0.43286, 0.34977, -3.52921, 0.00261, -0.02616, -0.42682, 0.45513, 0.35148, -3.54694, -0.52472, 5.29528], "curve": [0.25, 0, 0.75, 1], "time": 3.3333}, {"offset": 4, "curve": "stepped", "vertices": [-1.21964, -0.12084, -1.21964, -0.12084, 0, 0, 0, 0, 0, 0, -1.21964, -0.12084, -1.21964, -0.12084, 0, 0, -0.42682, 0.45513], "time": 4}, {"offset": 4, "vertices": [-1.21964, -0.12084, -1.21964, -0.12084, 0, 0, 0, 0, 0, 0, -1.21964, -0.12084, -1.21964, -0.12084, 0, 0, -0.42682, 0.45513], "curve": [0.25, 0, 0.75, 1], "time": 4.3333}, {"offset": 4, "vertices": [-0.14776, 1.49177, 1.12228, 6.18709, -0.62553, 9.29306, 0, 0, 4.97106, -8.80338, 5.08967, 0.43286, 0.34977, -3.52921, 0.00261, -0.02616, -0.42682, 0.45513, 0.35148, -3.54694, -0.52472, 5.29528], "curve": [0.25, 0, 0.75, 1], "time": 5}, {"offset": 4, "curve": "stepped", "vertices": [-1.21964, -0.12084, -1.21964, -0.12084, 0, 0, 0, 0, 0, 0, -1.21964, -0.12084, -1.21964, -0.12084, 0, 0, -0.42682, 0.45513], "time": 5.6667}, {"offset": 4, "vertices": [-1.21964, -0.12084, -1.21964, -0.12084, 0, 0, 0, 0, 0, 0, -1.21964, -0.12084, -1.21964, -0.12084, 0, 0, -0.42682, 0.45513], "curve": [0.25, 0, 0.75, 1], "time": 6}, {"offset": 4, "vertices": [-0.14776, 1.49177, 1.12228, 6.18709, -0.62553, 9.29306, 0, 0, 4.97106, -8.80338, 5.08967, 0.43286, 0.34977, -3.52921, 0.00261, -0.02616, -0.42682, 0.45513, 0.35148, -3.54694, -0.52472, 5.29528], "time": 6.6667}]}, "Body": {"Body": [{"vertices": [0.1085, -0.01039], "curve": [0.25, 0, 0.75, 1], "time": 0}, {"vertices": [0.39749, 3.00784, 0, 0, 0, 0, -0.11643, -1.21604, -0.16479, -1.21045, 0.15524, 1.62136, 0.2197, 1.61387, 0.15524, 1.62136, 0.2197, 1.61387, 0, 0, 0, 0, 0.04727, 0.34777, 0.02394, 0.35023, -0.21049, -3.04373, -0.21049, -3.04373, -0.34556, -2.53762, -0.17705, -2.55481, 0.64312, -2.49563, 0.80672, -2.44741, -1.24178, -9.5722, -2.43451, -5.59924, -0.12829, -1.33985, -0.2214, -1.32882, -2.0698, -5.57044, -2.29011, -5.48357, 0, 0, 0.52857, 2.52293, 1.0467, 9.62564, 0.86099, 6.93083, 0.51198, 5.34727, 0.68334, 5.32748, 0.24903, 0.47192, 0.98884, -2.07365, 0.90541, -2.11143, 1.04327, -2.04682], "curve": [0.25, 0, 0.75, 1], "time": 1.6667}, {"vertices": [0.1085, -0.01039], "curve": [0.25, 0, 0.75, 1], "time": 3.3333}, {"vertices": [0.39749, 3.00784, 0, 0, 0, 0, -0.11643, -1.21604, -0.16479, -1.21045, 0.15524, 1.62136, 0.2197, 1.61387, 0.15524, 1.62136, 0.2197, 1.61387, 0, 0, 0, 0, 0.04727, 0.34777, 0.02394, 0.35023, -0.21049, -3.04373, -0.21049, -3.04373, -0.34556, -2.53762, -0.17705, -2.55481, 0.64312, -2.49563, 0.80672, -2.44741, -1.24178, -9.5722, -2.43451, -5.59924, -0.12829, -1.33985, -0.2214, -1.32882, -2.0698, -5.57044, -2.29011, -5.48357, 0, 0, 0.52857, 2.52293, 1.0467, 9.62564, 0.86099, 6.93083, 0.51198, 5.34727, 0.68334, 5.32748, 0.24903, 0.47192, 0.98884, -2.07365, 0.90541, -2.11143, 1.04327, -2.04682], "curve": [0.25, 0, 0.75, 1], "time": 5}, {"vertices": [0.1085, -0.01039], "time": 6.6667}]}, "Loincloth": {"Loincloth": [{"offset": 66, "vertices": [1.45564, 1.54234], "curve": [0.25, 0, 0.75, 1], "time": 0}, {"offset": 36, "vertices": [0.5161, 5.39034, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.50422, -3.31496, -0.05515, -4.47911, 0.74851, -1.94009], "curve": [0.25, 0, 0.75, 1], "time": 1.6667}, {"offset": 66, "vertices": [1.45564, 1.54234], "curve": [0.25, 0, 0.75, 1], "time": 3.3333}, {"offset": 36, "vertices": [0.5161, 5.39034, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.50422, -3.31496, -0.05515, -4.47911, 0.74851, -1.94009], "curve": [0.25, 0, 0.75, 1], "time": 5}, {"offset": 66, "vertices": [1.45564, 1.54234], "time": 6.6667}]}}}}, "Wild-ThuyTinh-Expand": {"slots": {"Wave Long Right 12": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Long Left", "time": 0.2}]}, "Wave Long Right 11": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Long Left", "time": 0.2}]}, "Wave Long Right 10": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Long Left", "time": 0.2}]}, "Sparkle 4": {"attachment": [{"name": null, "time": 0}]}, "Moon": {"attachment": [{"name": null, "time": 0}]}, "Sparkle 5": {"attachment": [{"name": null, "time": 0}]}, "Sparkle 2": {"attachment": [{"name": null, "time": 0}]}, "Sparkle 3": {"attachment": [{"name": null, "time": 0}]}, "Sparkle 1": {"attachment": [{"name": null, "time": 0}]}, "thanh-ngangBright2": {"attachment": [{"name": null, "time": 0}]}, "Wave Splash Big 1": {"attachment": [{"name": null, "time": 0}]}, "Wild Bright": {"attachment": [{"name": null, "time": 0}]}, "thanh-ngangBright": {"attachment": [{"name": null, "time": 0}]}, "Wave Long Left 7": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Long Left", "time": 0.2}]}, "Wave Long Left 6": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Long Left", "time": 0.2}]}, "Wave Long Left 5": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Long Left", "time": 0.2}]}, "Wave Long Left 4": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Long Left", "time": 0.2}]}, "Frame Light Left": {"attachment": [{"name": null, "time": 0}]}, "Wave Long Left 9": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Long Left", "time": 0.2}]}, "Ear": {"attachment": [{"name": null, "time": 0}]}, "Wave Long Left 8": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Long Left", "time": 0.2}]}, "Wave Long Left 3": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Long Left", "time": 0.2}]}, "thanh-docBright": {"attachment": [{"name": null, "time": 0}]}, "Wave Long Left 2": {"attachment": [{"name": null, "time": 0}]}, "Wave Long Left 1": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Long Left", "time": 0.2}]}, "Wild": {"color": [{"color": "ffffffff", "time": 0}, {"color": "ffffff00", "time": 0.2}]}, "Wild Mask": {"attachment": [{"name": null, "time": 0}]}, "mask-bg": {"attachment": [{"name": null, "time": 0.2}]}, "Wave Long Right 9": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Long Left", "time": 0.2}]}, "Wave Long Right 8": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Long Left", "time": 0.2}]}, "Wave Long Right 7": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Long Left", "time": 0.2}]}, "Wave Long Right 6": {"attachment": [{"name": null, "time": 0}]}, "Wave Long Right 5": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Long Left", "time": 0.2}]}, "Gem Sparkle 1": {"attachment": [{"name": null, "time": 0}]}, "Wave Long Right 4": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Long Left", "time": 0.2}]}, "Gem Sparkle 2": {"attachment": [{"name": null, "time": 0}]}, "Wave Long Right 3": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Long Left", "time": 0.2}]}, "Wave Long Right 2": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Long Left", "time": 0.2}]}, "Wave Long Right 1": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Long Left", "time": 0.2}]}, "Wave Splash Small 1": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Small", "time": 0.2}]}, "Wave Splash Small 2": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Small", "time": 0.2}]}, "Wave Long Left 11": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Long Left", "time": 0.2}]}, "Wave Splash Big 4": {"attachment": [{"name": null, "time": 0}]}, "Wave Long Left 12": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Long Left", "time": 0.2}]}, "Wave Splash Big 2": {"attachment": [{"name": null, "time": 0}]}, "Wave Splash Big 3": {"attachment": [{"name": null, "time": 0}]}, "Frame Light Right": {"attachment": [{"name": null, "time": 0}]}, "Wave Long Left 10": {"attachment": [{"name": null, "time": 0}, {"name": "Wave Long Left", "time": 0.2}]}, "thanh-docBright2": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"ngangduoi": {"scale": [{"x": 0.67, "y": 0.67, "time": 0}, {"x": 1, "y": 1, "time": 0.2}], "translate": [{"x": 0, "y": 150.98, "time": 0}, {"x": 0, "y": 0, "time": 0.2}]}, "docT": {"scale": [{"x": 0.799, "y": 0.231, "time": 0}, {"x": 1, "y": 1, "time": 0.2}], "translate": [{"x": 29.07, "y": 2.42, "time": 0}, {"x": 0, "y": 0, "time": 0.2}]}, "docP": {"scale": [{"x": 0.733, "y": 0.237, "time": 0}, {"x": 1, "y": 1, "time": 0.2}], "translate": [{"x": -25.37, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 0.2}]}, "Wild All": {"scale": [{"x": 0.752, "y": 0.752, "time": 0}, {"x": 1, "y": 1, "time": 0.2}], "translate": [{"x": 2.01, "y": 130.94, "time": 0}, {"x": 0, "y": 0, "time": 0.2}]}, "ngangtren": {"scale": [{"x": 0.67, "y": 0.67, "time": 0}, {"x": 1, "y": 1, "time": 0.2}], "translate": [{"x": 0, "y": -146.45, "time": 0}, {"x": 0, "y": 0, "time": 0.2}]}, "Body": {"translate": [{"x": -2.2, "y": -175.07, "time": 0}, {"x": 0, "y": 0, "time": 0.2}]}}, "deform": {"default": {"mask-body": {"mask-body": [{"offset": 26, "vertices": [-5.92798], "time": 0}, {"vertices": [-37.90033, 24.74347, -5.59466, 31.46999, 7.86749, 30.07132, 0.65317, 21.35201, 13.65897, -24.21715, 13.64248, -30.24067, 5.78952, -43.3712, -7.10781, -32.67667, -6.54953, -32.73915, -22.81668, -35.38266, -13.28733, -1.5735, -12.76283, 0.17484, -7.60798, 3.93464, 14.88773, -47.80885], "time": 0.0333}, {"vertices": [-45.00626, 53.15297, -11.18932, 62.93999, 15.73499, 60.14266, 34.30034, -9.3532, 44.91478, -50.63394, 31.68419, -56.08214, 14.95042, -69.58414, -10.30742, -57.58275, -10.99764, -56.87637, -38.42355, -59.4007, -21.68665, -34.30798, -60.35266, -11.25933, -59.57461, 3.7145, -45.43743, -36.71731], "time": 0.0667}, {"vertices": [-52.11218, 81.56247, -16.78398, 94.40997, 23.60248, 90.21398, 67.94749, -40.05839, 76.17056, -77.05071, 32.58092, -70.49358, 26.01628, -95.79709, -3.3471, -81.2188, -20.52573, -83.55352, -71.90213, -92.43102, -26.116, -34.57124, -25.52881, -15.38222, -58.84246, 9.60436, -44.66258, -24.09824], "time": 0.1}, {"vertices": [-59.21811, 109.97197, -22.37864, 125.87997, 31.46997, 120.28532, 101.59465, -70.76359, 107.42636, -103.4675, 65.93462, -124.26207, 29.06717, -137.64507, 12.1303, -112.04485, -36.23582, -112.26865, -56.29706, -136.66304, -50.70533, -21.8745, -29.58489, -16.62516, -22.83034, 31.33422, -43.88772, -11.47916], "time": 0.1333}, {"vertices": [-66.32404, 138.38147, -27.9733, 157.34998, 39.33747, 150.35664, 135.2418, -101.46878, 174.99712, -170.23424, 96.19081, -149.92503, 50.16006, -158.67302, 29.1866, -136.87445, -33.65044, -139.44781, -55.66055, -175.09149, -65.21467, -15.65776, -35.80098, 4.45195, -27.1382, 64.58403, -43.11287, 1.13991], "time": 0.1667}, {"vertices": [-73.42996, 166.79097, -33.56796, 188.81996, 47.20496, 180.42798, 168.88896, -132.17398, 169.93796, -156.30106, 103.85101, -159.44798, 53.49896, -187.771, -19.93101, -197.212, -47.20499, -194.065, -140.56598, -127.97797, -79.72401, -9.44101, -76.577, 1.04903, -56.646, 27.27396, -42.33801, 13.75899], "time": 0.2}]}, "mask-bg": {"mask-bg": [{"vertices": [-0.79797], "time": 0}, {"vertices": [-33.51588, 146.83188, 26.33396, 145.23586, 27.92995, -147.6299, -25.53603, -149.22586], "time": 0.2}]}}}, "drawOrder": [{"offsets": [{"offset": -13, "slot": "mask-body"}, {"offset": 29, "slot": "thanh-ngang2"}, {"offset": 6, "slot": "Wild"}, {"offset": 6, "slot": "Wild Mask"}, {"offset": 6, "slot": "<PERSON>"}], "time": 0}, {"offsets": [{"offset": -13, "slot": "mask-body"}, {"offset": 6, "slot": "Wild"}, {"offset": 6, "slot": "Wild Mask"}, {"offset": 6, "slot": "<PERSON>"}], "time": 0.2}]}}}