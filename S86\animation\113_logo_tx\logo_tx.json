{"skins": [{"attachments": {"Eyebrow": {"Eyebrow": {"triangles": [4, 5, 1, 3, 4, 2, 5, 0, 1, 4, 1, 2], "uvs": [0.0149, 0, 0.59545, 0, 1, 0.37229, 1, 0.80984, 0.5099, 1, 0, 1], "vertices": [2, 7, 27.49, 8.37, 5e-05, 8, 15.65, 4.86, 0.99995, 1, 8, 13.53, -5.37, 1, 3, 7, 11.29, -9.86, 0.91242, 8, -4.35, -9.1, 0.0771, 6, 21.49, -10.09, 0.01048, 2, 7, -8.39, -10.47, 0.10947, 6, 1.8, -10.3, 0.89053, 1, 6, -6.85, -1.56, 1, 1, 6, -6.94, 7.61, 1], "width": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "type": "mesh", "hull": 6, "height": 36}}, "bg2": {"bg1": {"x": 1.49, "width": 235, "y": 165.44, "height": 122}}, "bg1": {"bg1": {"x": 1.49, "width": 235, "y": 165.44, "height": 122}}, "Beard20": {"Beard": {"triangles": [16, 17, 20, 17, 19, 20, 17, 18, 19, 16, 20, 21, 15, 16, 22, 16, 21, 22, 23, 15, 22, 14, 23, 13, 13, 23, 24, 23, 14, 15, 13, 24, 12, 11, 12, 26, 12, 25, 26, 12, 24, 25, 26, 27, 11, 11, 27, 10, 10, 27, 9, 9, 27, 28, 28, 8, 9, 28, 29, 8, 29, 30, 8, 30, 7, 8, 30, 31, 7, 31, 6, 7, 31, 5, 6, 31, 32, 5, 5, 32, 33, 4, 5, 33, 33, 34, 4, 4, 34, 3, 34, 35, 3, 3, 35, 36, 2, 0, 1, 2, 36, 0, 2, 3, 36], "uvs": [1, 0.05372, 1, 0.16485, 0.86575, 0.10674, 0.7548, 0.1018, 0.66533, 0.15125, 0.63908, 0.25139, 0.65817, 0.36885, 0.688, 0.51721, 0.72612, 0.66943, 0.73566, 0.83633, 0.70465, 0.96615, 0.59967, 1, 0.46725, 1, 0.3563, 0.94513, 0.25489, 0.85612, 0.17139, 0.74608, 0.10219, 0.62739, 0.04612, 0.52601, 0, 0.4135, 0.0664, 0.37394, 0.14395, 0.48026, 0.19405, 0.56434, 0.26921, 0.67684, 0.34675, 0.78317, 0.42191, 0.85488, 0.50542, 0.89815, 0.56388, 0.91175, 0.59967, 0.88455, 0.61398, 0.82273, 0.59967, 0.6991, 0.57342, 0.56434, 0.5424, 0.40855, 0.51974, 0.24535, 0.5436, 0.1143, 0.6116, 0.02528, 0.73447, 0, 0.86928, 0], "vertices": [1, 110, -1.48, 2.74, 1, 1, 110, -2.97, -3.19, 1, 1, 110, 5.23, -1.94, 1, 3, 110, 11.43, -3.21, 0.10215, 111, 2.1, -2.59, 0.88741, 112, -1.55, -6.89, 0.01043, 3, 111, 7.58, -4.43, 0.25554, 112, 3.05, -3.39, 0.73637, 113, -2.67, -6.18, 0.00809, 2, 112, 8.68, -4.34, 0.26602, 113, 2.42, -3.6, 0.73398, 2, 113, 8.97, -3.36, 0.8478, 114, -1.71, -3.39, 0.1522, 1, 114, 6.63, -3.27, 1, 2, 114, 15.27, -3.57, 0.05733, 115, 2.25, -3.88, 0.94267, 2, 115, 11.44, -3.57, 0.88867, 116, -3.07, -2.13, 0.11133, 1, 116, 3.88, -4.54, 1, 2, 116, 8.7, -0.54, 0.12863, 117, 0.49, -2.22, 0.87137, 1, 117, 7.99, -3.11, 1, 2, 117, 14.62, -0.86, 0.00162, 118, 1.67, -2.58, 0.99838, 2, 118, 9.18, -3.53, 0.82819, 119, -4.53, -2.54, 0.17181, 1, 119, 3.14, -3.25, 1, 2, 119, 10.76, -3.03, 0.68365, 120, -0.59, -3.03, 0.31635, 1, 120, 5.82, -2.7, 1, 1, 120, 12.45, -1.56, 1, 1, 120, 12.23, 2.8, 1, 1, 120, 4.92, 3.35, 1, 2, 119, 10.83, 3.25, 0.6417, 120, -0.51, 3.25, 0.3583, 2, 118, 15.9, 3.73, 0.40379, 119, 3.31, 3.5, 0.59621, 1, 118, 8.61, 3.04, 1, 2, 117, 11.49, 4.51, 0.21919, 118, 2.8, 3.53, 0.78081, 3, 116, 6.94, 7.01, 0.00064, 117, 6.49, 2.71, 0.95649, 118, -2.17, 5.44, 0.04286, 3, 115, 14.66, 6.56, 0.00052, 116, 5.75, 3.81, 0.19062, 117, 3.09, 2.35, 0.80887, 3, 115, 13.36, 4.39, 0.07422, 116, 3.39, 2.92, 0.74981, 117, 1.24, 4.08, 0.17598, 3, 115, 10.05, 3.26, 0.78925, 116, 0.09, 4.09, 0.20893, 117, 0.83, 7.55, 0.00182, 2, 114, 15.3, 3.82, 0.03753, 115, 3.2, 3.44, 0.96247, 2, 114, 7.74, 3.67, 0.99972, 115, -4.32, 4.24, 0.00028, 2, 113, 9.77, 3.54, 0.66442, 114, -1.01, 3.53, 0.33558, 2, 112, 11.22, 1.98, 0.14169, 113, 0.72, 2.99, 0.85831, 1, 112, 4.11, 3.76, 1, 2, 111, 9.45, 2.91, 0.64205, 112, -1.96, 2.29, 0.35795, 2, 110, 13.92, 1.94, 0.02036, 111, 2.32, 3.12, 0.97964, 1, 110, 6.46, 3.8, 1], "width": 45, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 2, 0, 72, 0], "type": "mesh", "hull": 37, "height": 43}}, "Bread_Mouth9": {"Bread_Mouth2": {"rotation": 85.76, "x": 9.15, "width": 13, "y": 1.16, "height": 23}}, "Eye": {"Eye": {"x": 0.5, "width": 14, "y": -0.18, "height": 13}}, "Bread_Mouth8": {"Bread_Mouth": {"rotation": 101.48, "x": 11.13, "width": 11, "y": 1.16, "height": 29}}, "Bread_Mouth10": {"Bread_Mouth3": {"rotation": 89.2, "x": 6.94, "width": 13, "y": -1.59, "height": 20}}, "Head2": {"Head": {"triangles": [27, 0, 3, 21, 22, 27, 27, 22, 23, 27, 24, 0, 27, 23, 24, 18, 19, 25, 20, 21, 19, 21, 25, 19, 18, 25, 17, 3, 28, 27, 28, 21, 27, 40, 21, 28, 36, 21, 40, 26, 25, 21, 57, 44, 43, 12, 44, 57, 2, 0, 1, 3, 0, 2, 41, 4, 5, 32, 4, 41, 3, 32, 28, 42, 5, 6, 7, 42, 6, 41, 5, 42, 39, 32, 41, 28, 32, 39, 40, 28, 39, 31, 41, 42, 30, 31, 42, 35, 40, 39, 36, 40, 35, 41, 37, 39, 35, 39, 34, 42, 7, 8, 30, 42, 8, 41, 31, 37, 37, 31, 30, 38, 37, 30, 54, 53, 37, 35, 47, 26, 35, 26, 36, 50, 35, 34, 47, 35, 50, 29, 30, 8, 29, 38, 30, 55, 54, 37, 55, 37, 38, 8, 9, 29, 53, 51, 50, 53, 50, 34, 51, 53, 54, 52, 54, 55, 51, 54, 52, 33, 55, 38, 33, 38, 29, 48, 16, 47, 43, 33, 29, 50, 48, 47, 49, 50, 51, 49, 48, 50, 33, 44, 52, 33, 52, 55, 44, 33, 43, 29, 9, 10, 43, 29, 10, 15, 16, 48, 15, 48, 49, 45, 51, 52, 45, 52, 44, 46, 49, 51, 57, 43, 10, 45, 46, 51, 56, 45, 44, 56, 44, 12, 57, 10, 11, 12, 57, 11, 14, 49, 46, 15, 49, 14, 56, 13, 46, 56, 46, 45, 14, 46, 13, 13, 56, 12, 34, 39, 53, 37, 53, 39, 17, 26, 47, 16, 17, 47, 17, 25, 26, 26, 21, 36, 32, 3, 4], "uvs": [0.50065, 0.2344, 0.60234, 0.07681, 0.69387, 0.10717, 0.61251, 0.33416, 0.73455, 0.3891, 0.86472, 0.3891, 1, 0.46284, 1, 0.51466, 1, 0.61731, 1, 0.69249, 1, 0.84859, 1, 0.96135, 0.83421, 0.95701, 0.66743, 1, 0.47217, 0.9758, 0.34404, 0.85725, 0.35014, 0.71701, 0.34404, 0.63576, 0, 0.35962, 0, 0.29223, 0, 0.23383, 0.42946, 0.45649, 0.15692, 0, 0.24234, 0, 0.3054, 0, 0.29726, 0.52211, 0.39489, 0.59007, 0.44573, 0.37031, 0.52709, 0.47151, 0.87801, 0.70695, 0.8719, 0.61008, 0.80479, 0.5537, 0.60343, 0.45827, 0.78648, 0.73731, 0.62377, 0.61586, 0.55055, 0.61008, 0.46919, 0.53056, 0.77021, 0.61876, 0.8414, 0.6708, 0.61157, 0.53924, 0.51191, 0.50888, 0.76818, 0.47129, 0.90445, 0.51755, 0.82513, 0.80088, 0.7397, 0.82257, 0.68682, 0.86739, 0.56275, 0.89775, 0.43258, 0.68522, 0.42852, 0.76763, 0.47123, 0.80955, 0.57902, 0.68522, 0.61157, 0.7257, 0.70716, 0.73582, 0.66648, 0.64184, 0.70309, 0.68088, 0.74784, 0.70257, 0.70822, 0.93026, 0.87201, 0.89024], "vertices": [2, 4, 28.87, 18.58, 0.5, 5, 28.87, 103.58, 0.5, 2, 4, 22.87, 31.66, 0.5, 5, 22.87, 116.66, 0.5, 2, 4, 17.47, 29.14, 0.5, 5, 17.47, 114.14, 0.5, 2, 4, 22.27, 10.3, 0.5, 5, 22.27, 95.3, 0.5, 2, 4, 15.07, 5.74, 0.5, 5, 15.07, 90.74, 0.5, 2, 4, 7.39, 5.74, 0.5, 5, 7.39, 90.74, 0.5, 2, 4, -0.59, -0.38, 0.524, 5, -0.59, 84.62, 0.476, 2, 4, -0.59, -4.68, 0.53, 5, -0.59, 80.32, 0.47, 2, 4, -0.59, -13.2, 0.55, 5, -0.59, 71.8, 0.45, 2, 4, -0.59, -19.44, 0.57, 5, -0.59, 65.56, 0.43, 2, 4, -0.59, -32.39, 0.59, 5, -0.59, 52.61, 0.41, 2, 4, -0.59, -41.75, 0.59, 5, -0.59, 43.25, 0.41, 2, 4, 9.19, -41.39, 0.564, 5, 9.19, 43.61, 0.436, 2, 4, 19.03, -44.96, 0.53, 5, 19.03, 40.04, 0.47, 2, 4, 30.55, -42.95, 0.51, 5, 30.55, 42.05, 0.49, 2, 4, 38.11, -33.11, 0.53, 5, 38.11, 51.89, 0.47, 2, 4, 37.75, -21.47, 0.5, 5, 37.75, 63.53, 0.5, 2, 4, 38.11, -14.73, 0.5, 5, 38.11, 70.27, 0.5, 2, 4, 58.41, 8.19, 0.46, 5, 58.41, 93.19, 0.54, 2, 4, 58.41, 13.78, 0.46, 5, 58.41, 98.78, 0.54, 2, 4, 58.41, 18.63, 0.48, 5, 58.41, 103.63, 0.52, 2, 4, 33.07, 0.15, 0.492, 5, 33.07, 85.15, 0.508, 2, 4, 49.15, 38.04, 0.45, 5, 49.15, 123.04, 0.55, 2, 4, 44.11, 38.04, 0.45, 5, 44.11, 123.04, 0.55, 2, 4, 40.39, 38.04, 0.46, 5, 40.39, 123.04, 0.54, 2, 4, 40.87, -5.3, 0.516, 5, 40.87, 79.7, 0.484, 2, 4, 35.11, -10.94, 0.524, 5, 35.11, 74.06, 0.476, 2, 4, 32.11, 7.3, 0.492, 5, 32.11, 92.3, 0.508, 2, 4, 27.31, -1.1, 0.516, 5, 27.31, 83.9, 0.484, 2, 4, 6.61, -20.64, 0.548, 5, 6.61, 64.36, 0.452, 2, 4, 6.97, -12.6, 0.564, 5, 6.97, 72.4, 0.436, 2, 4, 10.93, -7.92, 0.564, 5, 10.93, 77.08, 0.436, 2, 4, 22.81, 0, 0.516, 5, 22.81, 85, 0.484, 2, 4, 12.01, -23.16, 0.556, 5, 12.01, 61.84, 0.444, 2, 4, 21.61, -13.08, 0.56, 5, 21.61, 71.92, 0.44, 2, 4, 25.93, -12.6, 0.54, 5, 25.93, 72.4, 0.46, 2, 4, 30.73, -6, 0.524, 5, 30.73, 79, 0.476, 2, 4, 12.97, -13.32, 0.564, 5, 12.97, 71.68, 0.436, 2, 4, 8.77, -17.64, 0.564, 5, 8.77, 67.36, 0.436, 2, 4, 22.33, -6.72, 0.532, 5, 22.33, 78.28, 0.468, 2, 4, 28.21, -4.2, 0.524, 5, 28.21, 80.8, 0.476, 2, 4, 13.09, -1.08, 0.55, 5, 13.09, 83.92, 0.45, 2, 4, 5.05, -4.92, 0.54, 5, 5.05, 80.08, 0.46, 2, 4, 9.73, -28.43, 0.56, 5, 9.73, 56.57, 0.44, 2, 4, 14.77, -30.23, 0.548, 5, 14.77, 54.77, 0.452, 2, 4, 17.89, -33.95, 0.54, 5, 17.89, 51.05, 0.46, 2, 4, 25.21, -36.47, 0.54, 5, 25.21, 48.53, 0.46, 2, 4, 32.89, -18.83, 0.54, 5, 32.89, 66.17, 0.46, 2, 4, 33.13, -25.67, 0.528, 5, 33.13, 59.33, 0.472, 2, 4, 30.61, -29.15, 0.54, 5, 30.61, 55.85, 0.46, 2, 4, 24.25, -18.83, 0.55, 5, 24.25, 66.17, 0.45, 2, 4, 22.33, -22.19, 0.538, 5, 22.33, 62.81, 0.462, 2, 4, 16.69, -23.03, 0.54, 5, 16.69, 61.97, 0.46, 2, 4, 19.09, -15.23, 0.548, 5, 19.09, 69.77, 0.452, 2, 4, 16.93, -18.47, 0.548, 5, 16.93, 66.53, 0.452, 2, 4, 14.29, -20.27, 0.556, 5, 14.29, 64.73, 0.444, 2, 4, 16.63, -39.17, 0.53, 5, 16.63, 45.83, 0.47, 2, 4, 6.96, -35.85, 0.54, 5, 6.96, 49.15, 0.46], "width": 44, "edges": [40, 42, 42, 44, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 48, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 44, 46, 46, 48, 36, 38, 38, 40, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "type": "mesh", "hull": 25, "height": 65}}, "Bread_Mouth3": {"Bread_Mouth3": {"rotation": 89.2, "x": 6.94, "width": 13, "y": -1.59, "height": 20}}, "Bread_Mouth2": {"Bread_Mouth2": {"rotation": 85.76, "x": 9.15, "width": 13, "y": 1.16, "height": 23}}, "Bread_Mouth5": {"Bread_Mouth5": {"triangles": [6, 2, 3, 4, 6, 3, 5, 6, 4, 8, 9, 0, 1, 8, 0, 7, 8, 1, 7, 1, 2, 6, 7, 2], "uvs": [0, 0, 0.72059, 0, 1, 0.36558, 1, 0.75225, 0.33393, 1, 0, 1, 0.27593, 0.65155, 0.23726, 0.48641, 0.09226, 0.26489, 0, 0.15211], "vertices": [1, 54, -3.19, -5.92, 1, 1, 54, 0.34, 4.29, 1, 2, 54, 14.15, 3.94, 0.83886, 55, -3.27, 2.55, 0.16114, 2, 55, 10.18, 6.14, 0.4471, 56, -1.92, 5.98, 0.5529, 1, 56, 11.45, 5.24, 1, 1, 56, 15, 1.7, 1, 3, 54, 20.32, -9.69, 0.04355, 55, 9.48, -5.29, 0.19309, 56, 3.21, -4.25, 0.76336, 3, 54, 14.52, -8.29, 0.50163, 55, 3.88, -7.38, 0.39679, 56, -0.58, -8.87, 0.10159, 3, 54, 6.27, -7.74, 0.98769, 55, -3.26, -11.54, 0.01226, 56, -4.67, -16.05, 5e-05, 1, 54, 1.98, -7.71, 1], "width": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "type": "mesh", "hull": 10, "height": 36}}, "Bread_Mouth4": {"Bread_Mouth4": {"rotation": 100.97, "x": 8.56, "width": 11, "y": 0.46, "height": 28}}, "Bread_Mouth7": {"Bread_Mouth4": {"rotation": 95.48, "x": 7.74, "width": 11, "y": 0.8, "height": 28}}, "Bread_Mouth6": {"Bread_Mouth5": {"triangles": [5, 6, 4, 4, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 1, 8, 0, 8, 9, 0], "uvs": [0, 0, 0.72059, 0, 1, 0.36558, 1, 0.75225, 0.33393, 1, 0, 1, 0.27593, 0.65155, 0.23726, 0.48641, 0.09226, 0.26489, 0, 0.15211], "vertices": [1, 100, -3.72, 4.95, 1, 1, 100, 0.26, -5.1, 1, 2, 100, 14.04, -4.14, 0.97271, 101, -3.38, -2.77, 0.02729, 2, 101, 10.3, -5.31, 0.24012, 102, -2.08, -5.1, 0.75988, 1, 102, 11.31, -5.08, 1, 1, 102, 15.04, -1.74, 1, 3, 100, 19.61, 9.75, 0.02206, 101, 8.73, 6.03, 0.42154, 102, 3.59, 4.84, 0.5564, 3, 100, 13.87, 8.09, 0.37297, 101, 2.99, 7.68, 0.59291, 102, 0.06, 9.66, 0.03412, 2, 100, 5.65, 7.18, 0.94416, 101, -4.46, 11.28, 0.05584, 2, 100, 1.37, 6.97, 0.99525, 101, -8.19, 13.38, 0.00475], "width": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "type": "mesh", "hull": 10, "height": 36}}, "Head": {"Head": {"triangles": [21, 25, 19, 27, 24, 0, 27, 22, 23, 27, 23, 24, 18, 19, 25, 2, 0, 1, 20, 21, 19, 21, 22, 27, 13, 56, 12, 14, 46, 13, 56, 13, 46, 56, 46, 45, 45, 46, 59, 15, 49, 14, 14, 49, 46, 12, 57, 11, 57, 10, 11, 56, 44, 12, 12, 44, 57, 57, 44, 43, 56, 45, 44, 46, 49, 59, 57, 43, 10, 45, 58, 44, 45, 59, 58, 15, 48, 49, 15, 16, 48, 43, 29, 10, 29, 9, 10, 59, 51, 58, 59, 49, 51, 58, 52, 44, 44, 33, 43, 33, 52, 55, 33, 44, 52, 49, 48, 50, 49, 50, 51, 50, 48, 47, 43, 33, 29, 58, 51, 52, 48, 16, 47, 33, 38, 29, 33, 55, 38, 51, 54, 52, 52, 54, 55, 51, 53, 54, 53, 50, 34, 53, 51, 50, 16, 17, 47, 8, 9, 29, 55, 37, 38, 55, 54, 37, 29, 38, 30, 29, 30, 8, 47, 35, 50, 50, 35, 34, 17, 26, 47, 35, 26, 36, 35, 47, 26, 54, 53, 37, 38, 37, 30, 37, 53, 39, 18, 25, 17, 17, 25, 26, 37, 31, 30, 41, 31, 37, 30, 42, 8, 42, 7, 8, 35, 39, 34, 41, 37, 39, 34, 39, 53, 36, 40, 35, 35, 40, 39, 30, 31, 42, 26, 21, 36, 26, 25, 21, 31, 41, 42, 40, 28, 39, 28, 32, 39, 39, 32, 41, 36, 21, 40, 41, 5, 42, 7, 42, 6, 42, 5, 6, 40, 21, 28, 21, 27, 28, 28, 60, 32, 28, 27, 60, 32, 4, 41, 41, 4, 5, 32, 3, 4, 32, 60, 3, 3, 60, 0, 60, 27, 0, 3, 0, 2], "uvs": [0.50065, 0.2344, 0.60234, 0.07681, 0.69387, 0.10717, 0.61251, 0.33416, 0.73455, 0.3891, 0.86472, 0.3891, 1, 0.46284, 1, 0.51466, 1, 0.61731, 1, 0.69249, 1, 0.84859, 1, 0.96135, 0.83421, 0.95701, 0.66743, 1, 0.47217, 0.9758, 0.34404, 0.85725, 0.35014, 0.71701, 0.34404, 0.63576, 0, 0.35962, 0, 0.29223, 0, 0.23383, 0.42946, 0.45649, 0.15692, 0, 0.24234, 0, 0.3054, 0, 0.29726, 0.52211, 0.39489, 0.59007, 0.44573, 0.37031, 0.52709, 0.47151, 0.87801, 0.70695, 0.8719, 0.61008, 0.80479, 0.5537, 0.60343, 0.45827, 0.78648, 0.73731, 0.62377, 0.61586, 0.55055, 0.61008, 0.46919, 0.53056, 0.77021, 0.61876, 0.8414, 0.6708, 0.61157, 0.53924, 0.51191, 0.50888, 0.76818, 0.47129, 0.90445, 0.51755, 0.82513, 0.80088, 0.7397, 0.82257, 0.68682, 0.86739, 0.56275, 0.89775, 0.43258, 0.68522, 0.42852, 0.76763, 0.47123, 0.80955, 0.57902, 0.68522, 0.61157, 0.7257, 0.70716, 0.73582, 0.66648, 0.64184, 0.70309, 0.68088, 0.74784, 0.70257, 0.70822, 0.93026, 0.87201, 0.89024, 0.69262, 0.78202, 0.5988, 0.82848, 0.56384, 0.3732], "vertices": [2, 4, -27.12, 18.58, 0.508, 5, -27.12, 103.58, 0.492, 2, 4, -21.12, 31.66, 0.476, 5, -21.12, 116.66, 0.524, 2, 4, -15.72, 29.14, 0.492, 5, -15.72, 114.14, 0.508, 2, 4, -20.52, 10.3, 0.5, 5, -20.52, 95.3, 0.5, 2, 4, -13.32, 5.74, 0.508, 5, -13.32, 90.74, 0.492, 2, 4, -5.64, 5.74, 0.5, 5, -5.64, 90.74, 0.5, 2, 4, 2.34, -0.38, 0.524, 5, 2.34, 84.62, 0.476, 2, 4, 2.34, -4.68, 0.532, 5, 2.34, 80.32, 0.468, 2, 4, 2.34, -13.2, 0.55, 5, 2.34, 71.8, 0.45, 2, 4, 2.34, -19.44, 0.57, 5, 2.34, 65.56, 0.43, 2, 4, 2.34, -32.39, 0.59, 5, 2.34, 52.61, 0.41, 2, 4, 2.34, -41.75, 0.59, 5, 2.34, 43.25, 0.41, 2, 4, -7.44, -41.39, 0.564, 5, -7.44, 43.61, 0.436, 2, 4, -17.28, -44.96, 0.524, 5, -17.28, 40.04, 0.476, 2, 4, -28.8, -42.95, 0.508, 5, -28.8, 42.05, 0.492, 2, 4, -36.36, -33.11, 0.524, 5, -36.36, 51.89, 0.476, 2, 4, -36, -21.47, 0.548, 5, -36, 63.53, 0.452, 2, 4, -36.36, -14.73, 0.548, 5, -36.36, 70.27, 0.452, 2, 4, -56.66, 8.19, 0.46, 5, -56.66, 93.19, 0.54, 2, 4, -56.66, 13.78, 0.46, 5, -56.66, 98.78, 0.54, 2, 4, -56.66, 18.63, 0.484, 5, -56.66, 103.63, 0.516, 2, 4, -31.32, 0.15, 0.508, 5, -31.32, 85.15, 0.492, 2, 4, -47.4, 38.04, 0.468, 5, -47.4, 123.04, 0.532, 2, 4, -42.36, 38.04, 0.452, 5, -42.36, 123.04, 0.548, 2, 4, -38.64, 38.04, 0.452, 5, -38.64, 123.04, 0.548, 2, 4, -39.12, -5.3, 0.524, 5, -39.12, 79.7, 0.476, 2, 4, -33.36, -10.94, 0.54, 5, -33.36, 74.06, 0.46, 2, 4, -30.36, 7.3, 0.492, 5, -30.36, 92.3, 0.508, 2, 4, -25.56, -1.1, 0.516, 5, -25.56, 83.9, 0.484, 2, 4, -4.86, -20.64, 0.548, 5, -4.86, 64.36, 0.452, 2, 4, -5.22, -12.6, 0.564, 5, -5.22, 72.4, 0.436, 2, 4, -9.18, -7.92, 0.564, 5, -9.18, 77.08, 0.436, 2, 4, -21.06, 0, 0.524, 5, -21.06, 85, 0.476, 2, 4, -10.26, -23.16, 0.556, 5, -10.26, 61.84, 0.444, 2, 4, -19.86, -13.08, 0.556, 5, -19.86, 71.92, 0.444, 2, 4, -24.18, -12.6, 0.54, 5, -24.18, 72.4, 0.46, 2, 4, -28.98, -6, 0.532, 5, -28.98, 79, 0.468, 2, 4, -11.22, -13.32, 0.564, 5, -11.22, 71.68, 0.436, 2, 4, -7.02, -17.64, 0.564, 5, -7.02, 67.36, 0.436, 2, 4, -20.58, -6.72, 0.532, 5, -20.58, 78.28, 0.468, 2, 4, -26.46, -4.2, 0.532, 5, -26.46, 80.8, 0.468, 2, 4, -11.34, -1.08, 0.556, 5, -11.34, 83.92, 0.444, 2, 4, -3.3, -4.92, 0.548, 5, -3.3, 80.08, 0.452, 2, 4, -7.98, -28.43, 0.564, 5, -7.98, 56.57, 0.436, 2, 4, -13.02, -30.23, 0.548, 5, -13.02, 54.77, 0.452, 2, 4, -16.14, -33.95, 0.54, 5, -16.14, 51.05, 0.46, 2, 4, -23.46, -36.47, 0.54, 5, -23.46, 48.53, 0.46, 2, 4, -31.14, -18.83, 0.556, 5, -31.14, 66.17, 0.444, 2, 4, -31.38, -25.67, 0.556, 5, -31.38, 59.33, 0.444, 2, 4, -28.86, -29.15, 0.54, 5, -28.86, 55.85, 0.46, 2, 4, -22.5, -18.83, 0.548, 5, -22.5, 66.17, 0.452, 2, 4, -20.58, -22.19, 0.54, 5, -20.58, 62.81, 0.46, 2, 4, -14.94, -23.03, 0.54, 5, -14.94, 61.97, 0.46, 2, 4, -17.34, -15.23, 0.564, 5, -17.34, 69.77, 0.436, 2, 4, -15.18, -18.47, 0.548, 5, -15.18, 66.53, 0.452, 2, 4, -12.54, -20.27, 0.556, 5, -12.54, 64.73, 0.444, 2, 4, -14.88, -39.17, 0.532, 5, -14.88, 45.83, 0.468, 2, 4, -5.21, -35.85, 0.54, 5, -5.21, 49.15, 0.46, 2, 4, -15.8, -26.87, 0.53939, 5, -15.8, 58.13, 0.46061, 2, 4, -21.33, -30.72, 0.55291, 5, -21.33, 54.28, 0.44709, 2, 4, -23.39, 7.06, 0.5186, 5, -23.39, 92.06, 0.4814], "width": 44, "edges": [40, 42, 42, 44, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 48, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 44, 46, 46, 48, 36, 38, 38, 40, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "type": "mesh", "hull": 25, "height": 65}}, "c1": {"c1": {"x": -2.43, "width": 31, "y": -1.28, "height": 47}}, "c2": {"c2": {"x": 1.05, "width": 26, "y": -1.02, "height": 37}}, "c3": {"c2": {"x": 1.05, "width": 26, "y": -1.02, "height": 37}}, "frame_set/E3": {"frame_set/E4": {"width": 56, "height": 48}, "frame_set/E2": {"width": 56, "height": 48}, "frame_set/E0": {"width": 56, "height": 48}, "frame_set/E8": {"width": 56, "height": 48}, "frame_set/E6": {"width": 56, "height": 48}}, "frame_set/E2": {"frame_set/E4": {"width": 56, "height": 48}, "frame_set/E2": {"width": 56, "height": 48}, "frame_set/E0": {"width": 56, "height": 48}, "frame_set/E8": {"width": 56, "height": 48}, "frame_set/E6": {"width": 56, "height": 48}}, "frame_set/E1": {"frame_set/E4": {"width": 56, "height": 48}, "frame_set/E2": {"width": 56, "height": 48}, "frame_set/E0": {"width": 56, "height": 48}, "frame_set/E8": {"width": 56, "height": 48}, "frame_set/E6": {"width": 56, "height": 48}}, "frame_set/E0": {"frame_set/E4": {"width": 56, "height": 48}, "frame_set/E2": {"width": 56, "height": 48}, "frame_set/E0": {"width": 56, "height": 48}, "frame_set/E8": {"width": 56, "height": 48}, "frame_set/E6": {"width": 56, "height": 48}}, "images/bg_vang2": {"images/bg_vang": {"x": -5.98, "width": 544, "y": 22.09, "height": 103}}, "images/bg_vang": {"images/bg_vang": {"x": -5.98, "width": 544, "y": 22.09, "height": 103}}, "images/v4": {"images/v4": {"x": -3.26, "width": 63, "y": 1.19, "height": 46}}, "Beard": {"Beard": {"triangles": [17, 18, 19, 17, 19, 20, 16, 17, 20, 16, 20, 21, 16, 21, 22, 15, 16, 22, 23, 14, 15, 23, 15, 22, 13, 23, 24, 14, 23, 13, 12, 24, 25, 13, 24, 12, 12, 25, 26, 11, 12, 26, 10, 27, 9, 11, 27, 10, 26, 27, 11, 29, 30, 8, 28, 29, 8, 28, 8, 9, 9, 27, 28, 30, 31, 7, 30, 7, 8, 31, 32, 5, 31, 5, 6, 31, 6, 7, 33, 34, 4, 5, 32, 33, 4, 5, 33, 3, 35, 36, 34, 35, 3, 4, 34, 3, 2, 3, 36, 2, 36, 0, 2, 0, 1], "uvs": [1, 0.05372, 1, 0.16485, 0.86575, 0.10674, 0.7548, 0.1018, 0.66533, 0.15125, 0.63908, 0.25139, 0.65817, 0.36885, 0.688, 0.51721, 0.72612, 0.66943, 0.73566, 0.83633, 0.70465, 0.96615, 0.59967, 1, 0.46725, 1, 0.3563, 0.94513, 0.25489, 0.85612, 0.17139, 0.74608, 0.10219, 0.62739, 0.04612, 0.52601, 0, 0.4135, 0.0664, 0.37394, 0.14395, 0.48026, 0.19405, 0.56434, 0.26921, 0.67684, 0.34675, 0.78317, 0.42191, 0.85488, 0.50542, 0.89815, 0.56388, 0.91175, 0.59967, 0.88455, 0.61398, 0.82273, 0.59967, 0.6991, 0.57342, 0.56434, 0.5424, 0.40855, 0.51974, 0.24535, 0.5436, 0.1143, 0.6116, 0.02528, 0.73447, 0, 0.86928, 0], "vertices": [1, 13, -1.44, -2.77, 1, 1, 13, -3.01, 3.14, 1, 1, 13, 5.2, 2.02, 1, 3, 13, 11.38, 3.38, 0.09514, 14, 2.4, 2.53, 0.904, 15, -1.64, 6.56, 0.00086, 2, 14, 8.02, 3.87, 0.25718, 15, 2.75, 2.8, 0.74282, 2, 15, 8.42, 3.41, 0.40932, 16, 1.38, 3.15, 0.59068, 2, 16, 7.93, 3.13, 0.957, 17, -2.64, 3.26, 0.043, 1, 17, 5.69, 3.16, 1, 2, 17, 14.33, 3.48, 0.18925, 18, 1.14, 3.74, 0.81075, 2, 18, 10.34, 3.9, 0.97528, 19, -4.27, 1.34, 0.02472, 1, 19, 2.28, 4.69, 1, 2, 19, 7.61, 1.39, 0.4496, 20, -0.79, 1.6, 0.5504, 1, 20, 6.47, 3.69, 1, 2, 20, 13.38, 2.53, 0.24129, 21, 0.3, 3.04, 0.75871, 1, 21, 7.86, 3.56, 1, 2, 21, 15.49, 2.54, 0.73289, 22, 1.51, 3.28, 0.26711, 3, 21, 22.87, 0.61, 0.00346, 22, 9.13, 3.23, 0.88951, 23, -1.96, 3.4, 0.10702, 1, 23, 4.43, 2.7, 1, 1, 23, 10.98, 1.19, 1, 1, 23, 10.52, -3.15, 1, 2, 22, 14.79, -3.03, 0.0062, 23, 3.19, -3.29, 0.9938, 2, 22, 9.35, -3.04, 0.91274, 23, -2.23, -2.88, 0.08726, 1, 22, 1.84, -3.46, 1, 2, 21, 6.91, -2.97, 0.91488, 22, -5.46, -4.17, 0.08512, 2, 20, 11.16, -3.27, 0.40685, 21, 1.08, -3.13, 0.59315, 2, 19, 6.91, -6.34, 0.005, 20, 5.92, -2.3, 0.995, 3, 18, 14.08, -6.05, 0.00504, 19, 5.29, -3.33, 0.36704, 20, 2.51, -2.5, 0.62792, 3, 18, 12.67, -3.95, 0.16143, 19, 2.82, -2.77, 0.76288, 20, 0.97, -4.5, 0.07569, 2, 18, 9.31, -3, 0.88319, 19, -0.28, -4.39, 0.11681, 2, 17, 14.37, -3.91, 0.13783, 18, 2.48, -3.53, 0.86217, 1, 17, 6.81, -3.77, 1, 2, 16, 8.97, -3.74, 0.8549, 17, -1.94, -3.65, 0.1451, 2, 15, 10.59, -3.05, 0.33438, 16, -0.09, -3.5, 0.66562, 1, 15, 3.38, -4.4, 1, 2, 14, 9.23, -3.61, 0.73392, 15, -2.59, -2.58, 0.26608, 2, 13, 13.94, -1.73, 0.01753, 14, 2.1, -3.18, 0.98247, 1, 13, 6.52, -3.71, 1], "width": 45, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 2, 0, 72, 0], "type": "mesh", "hull": 37, "height": 43}}, "images/v3": {"images/v3": {"x": 0.26, "width": 43, "y": -0.13, "height": 38}}, "images/v6": {"images/v6": {"x": -0.93, "width": 43, "y": -2.28, "height": 38}}, "images/v5": {"images/v5": {"x": 5.1, "width": 51, "y": -0.31, "height": 37}}, "images/v2": {"images/v2": {"x": -7.2, "width": 51, "y": -0.31, "height": 37}}, "images/v1": {"images/v1": {"x": 6.16, "width": 63, "y": -1.67, "height": 46}}, "Rubi": {"Rubi": {"x": -0.07, "width": 29, "y": 0.33, "height": 29}}, "Mouth2": {"Mouth": {"triangles": [14, 27, 26, 27, 8, 9, 6, 17, 5, 5, 18, 4, 5, 17, 18, 6, 7, 16, 7, 15, 16, 17, 16, 28, 17, 6, 16, 4, 18, 25, 3, 4, 2, 25, 18, 29, 4, 25, 2, 17, 23, 18, 17, 28, 23, 25, 1, 2, 23, 24, 18, 18, 24, 29, 23, 28, 24, 16, 15, 28, 25, 29, 1, 28, 19, 24, 19, 28, 14, 24, 19, 29, 1, 29, 20, 7, 8, 15, 29, 19, 20, 20, 26, 1, 26, 0, 1, 28, 15, 14, 15, 8, 14, 19, 14, 20, 20, 14, 26, 26, 22, 0, 22, 13, 0, 21, 12, 22, 9, 11, 21, 21, 11, 12, 22, 12, 13, 9, 10, 11, 8, 27, 14, 27, 22, 26, 27, 9, 21, 27, 21, 22], "uvs": [1, 0.19466, 1, 0.73534, 1, 0.86566, 1, 0.94884, 0.85238, 0.94884, 0.71295, 1, 0.50962, 1, 0.10296, 0.75752, 0.132, 0.47748, 0, 0.1808, 0, 0, 0.34695, 0, 0.72457, 0, 1, 0, 0.376, 0.49412, 0.32372, 0.70762, 0.42829, 0.82407, 0.59676, 0.89061, 0.75943, 0.86289, 0.68972, 0.65216, 0.72457, 0.58562, 0.34695, 0.19189, 0.76524, 0.18634, 0.60286, 0.83097, 0.72852, 0.7772, 0.92352, 0.81029, 0.76977, 0.41023, 0.34996, 0.34114, 0.55029, 0.78341, 0.85029, 0.76627], "vertices": [3, 4, -1.74, -53.71, 0.47242, 5, -1.74, 31.29, 0.42528, 109, -1.74, 36.29, 0.1023, 3, 4, -1.74, -81.72, 0.458, 5, -1.74, 3.28, 0.32108, 109, -1.74, 8.28, 0.22092, 3, 4, -1.74, -88.35, 0.45193, 5, -1.74, -3.35, 0.30223, 109, -1.74, 1.65, 0.24585, 3, 4, -1.74, -92.44, 0.43803, 5, -1.74, -7.44, 0.29951, 109, -1.74, -2.44, 0.26246, 3, 4, 1.36, -92, 0.44051, 5, 1.36, -7, 0.30225, 109, 1.36, -2, 0.25723, 3, 4, 4.29, -93.37, 0.42359, 5, 4.29, -8.37, 0.29487, 109, 4.29, -3.37, 0.28154, 3, 4, 8.56, -93.81, 0.43044, 5, 8.56, -8.81, 0.26802, 109, 8.56, -3.81, 0.30154, 3, 4, 17.1, -82.7, 0.426, 5, 17.1, 2.3, 0.32662, 109, 17.1, 7.3, 0.24738, 3, 4, 16.49, -69.05, 0.414, 5, 16.49, 15.95, 0.33892, 109, 16.49, 20.95, 0.24708, 2, 4, 19.26, -52.75, 0.5, 5, 19.26, 32.25, 0.5, 2, 4, 19.26, -43.52, 0.5, 5, 19.26, 41.48, 0.5, 2, 4, 11.98, -42.54, 0.5, 5, 11.98, 42.46, 0.5, 2, 4, 4.05, -43.86, 0.5, 5, 4.05, 41.14, 0.5, 2, 4, -1.74, -43.64, 0.508, 5, -1.74, 41.36, 0.492, 3, 4, 11.37, -68.46, 0.43516, 5, 11.37, 16.54, 0.37727, 109, 11.37, 21.54, 0.18757, 3, 4, 12.47, -80.06, 0.43, 5, 12.47, 4.94, 0.35369, 109, 12.47, 9.94, 0.21631, 3, 4, 10.27, -86.07, 0.43761, 5, 10.27, -1.07, 0.32116, 109, 10.27, 3.93, 0.24123, 3, 4, 6.73, -89.44, 0.43906, 5, 6.73, -4.44, 0.3034, 109, 6.73, 0.56, 0.25754, 3, 4, 3.32, -87.33, 0.43263, 5, 3.32, -2.33, 0.32144, 109, 3.32, 2.67, 0.24593, 3, 4, 4.78, -78.06, 0.47242, 5, 4.78, 6.94, 0.35927, 109, 4.78, 11.94, 0.16831, 3, 4, 4.05, -73.81, 0.40887, 5, 4.05, 11.19, 0.34649, 109, 4.05, 16.19, 0.24464, 3, 4, 11.98, -52.96, 0.444, 5, 11.98, 32.04, 0.432, 109, 11.98, 37.04, 0.124, 3, 4, 3.19, -51.84, 0.432, 5, 3.19, 33.16, 0.432, 109, 3.19, 38.16, 0.136, 3, 4, 6.6, -85.49, 0.43605, 5, 6.6, -0.49, 0.32734, 109, 6.6, 4.51, 0.23662, 3, 4, 3.96, -83.14, 0.45595, 5, 3.96, 1.86, 0.33943, 109, 3.96, 6.86, 0.20461, 3, 4, -0.13, -84.95, 0.45904, 5, -0.13, 0.05, 0.33204, 109, -0.13, 5.05, 0.20892, 3, 4, 3.1, -64.55, 0.44387, 5, 3.1, 20.45, 0.40922, 109, 3.1, 25.45, 0.14691, 3, 4, 11.91, -60.19, 0.384, 5, 11.91, 24.81, 0.35926, 109, 11.91, 29.81, 0.25674, 3, 4, 7.71, -83.88, 0.44392, 5, 7.71, 1.12, 0.32315, 109, 7.71, 6.12, 0.23292, 3, 4, 1.41, -82.73, 0.446, 5, 1.41, 2.27, 0.33338, 109, 1.41, 7.27, 0.22061], "width": 17, "edges": [18, 20, 18, 16, 16, 14, 14, 12, 20, 22, 10, 12, 10, 8, 4, 6, 8, 6, 38, 40, 4, 2, 40, 2, 38, 28, 28, 30, 22, 24, 24, 26, 2, 0, 0, 26], "type": "mesh", "hull": 14, "height": 36}}, "Fang2": {"Fang": {"scaleX": -1, "rotation": 89.2, "x": 11.02, "width": 20, "y": 1.29, "height": 21}}, "Mouth": {"Mouth": {"triangles": [9, 10, 11, 22, 12, 13, 21, 11, 12, 9, 11, 21, 21, 12, 22, 22, 13, 0, 27, 21, 22, 0, 20, 27, 19, 27, 20, 15, 8, 14, 28, 15, 14, 0, 27, 22, 1, 20, 0, 29, 19, 20, 1, 29, 20, 7, 8, 15, 19, 28, 14, 24, 19, 29, 28, 19, 24, 25, 29, 1, 16, 15, 28, 23, 28, 24, 16, 28, 23, 18, 24, 29, 18, 29, 25, 23, 24, 18, 25, 1, 2, 17, 16, 23, 17, 23, 18, 4, 25, 2, 4, 18, 25, 3, 4, 2, 6, 16, 17, 7, 15, 16, 6, 7, 16, 5, 17, 18, 5, 18, 4, 6, 17, 5, 26, 21, 27, 8, 9, 26, 14, 26, 27, 26, 9, 21, 8, 26, 14, 14, 27, 19], "uvs": [1, 0.19466, 1, 0.73534, 1, 0.86566, 1, 0.94884, 0.85238, 0.94884, 0.71295, 1, 0.50962, 1, 0.10296, 0.75752, 0.132, 0.47748, 0, 0.1808, 0, 0, 0.34695, 0, 0.72457, 0, 1, 0, 0.376, 0.49412, 0.32372, 0.70762, 0.42829, 0.82407, 0.59676, 0.89061, 0.75943, 0.86289, 0.68972, 0.65216, 0.72457, 0.58562, 0.34695, 0.19189, 0.76524, 0.18634, 0.60286, 0.83097, 0.72852, 0.7772, 0.92352, 0.81029, 0.3227, 0.36013, 0.68741, 0.4459, 0.58912, 0.77631, 0.86769, 0.75164], "vertices": [2, 4, 0.84, -53.67, 0.53198, 5, 0.84, 31.33, 0.46802, 3, 4, 0.84, -81.28, 0.46542, 5, 0.84, 3.72, 0.32258, 109, 0.84, 8.72, 0.212, 3, 4, 0.84, -87.9, 0.466, 5, 0.84, -2.9, 0.30508, 109, 0.84, 2.1, 0.22892, 3, 4, 0.84, -91.56, 0.45338, 5, 0.84, -6.56, 0.31312, 109, 0.84, -1.56, 0.2335, 3, 4, -2.26, -91.56, 0.44248, 5, -2.26, -6.56, 0.30731, 109, -2.26, -1.56, 0.25021, 3, 4, -5.19, -93.37, 0.4358, 5, -5.19, -8.37, 0.30054, 109, -5.19, -3.37, 0.26366, 3, 4, -9.46, -93.37, 0.41603, 5, -9.46, -8.37, 0.34786, 109, -9.46, -3.37, 0.23611, 3, 4, -18, -80.05, 0.45, 5, -18, 4.95, 0.36046, 109, -18, 9.95, 0.18954, 3, 4, -17.39, -66.85, 0.44, 5, -17.39, 18.15, 0.40533, 109, -17.39, 23.15, 0.15467, 3, 4, -20.16, -51.86, 0.454, 5, -20.16, 33.14, 0.46091, 109, -20.16, 38.14, 0.08509, 2, 4, -20.16, -43.49, 0.5, 5, -20.16, 41.51, 0.5, 2, 4, -12.88, -44.18, 0.5, 5, -12.88, 40.82, 0.5, 2, 4, -4.95, -42.98, 0.5, 5, -4.95, 42.02, 0.5, 2, 4, 0.84, -43.42, 0.508, 5, 0.84, 41.58, 0.492, 3, 4, -12.27, -68.9, 0.44465, 5, -12.27, 16.1, 0.32928, 109, -12.27, 21.1, 0.22607, 3, 4, -13.37, -79.62, 0.45913, 5, -13.37, 5.38, 0.36087, 109, -13.37, 10.38, 0.18, 3, 4, -11.17, -85.19, 0.4511, 5, -11.17, -0.19, 0.35967, 109, -11.17, 4.81, 0.18923, 3, 4, -7.63, -88.55, 0.43761, 5, -7.63, -3.55, 0.32116, 109, -7.63, 1.45, 0.24123, 3, 4, -4.22, -87.33, 0.44829, 5, -4.22, -2.33, 0.31529, 109, -4.22, 2.67, 0.23643, 3, 4, -5.68, -76.3, 0.498, 5, -5.68, 8.7, 0.38138, 109, -5.68, 13.7, 0.12062, 3, 4, -4.95, -73.37, 0.45268, 5, -4.95, 11.63, 0.30896, 109, -4.95, 16.63, 0.23836, 3, 4, -12.88, -52.3, 0.474, 5, -12.88, 32.7, 0.43036, 109, -12.88, 37.7, 0.09564, 3, 4, -4.09, -52.94, 0.474, 5, -4.09, 32.06, 0.43036, 109, -4.09, 37.06, 0.09564, 3, 4, -7.5, -85.05, 0.45479, 5, -7.5, -0.05, 0.32833, 109, -7.5, 4.95, 0.21688, 3, 4, -4.86, -82.68, 0.46112, 5, -4.86, 2.32, 0.33111, 109, -4.86, 7.32, 0.20777, 3, 4, -0.77, -85.46, 0.45898, 5, -0.77, -0.46, 0.32041, 109, -0.77, 4.54, 0.22062, 3, 4, -13.39, -60.81, 0.424, 5, -13.39, 24.19, 0.40533, 109, -13.39, 29.19, 0.17067, 3, 4, -5.73, -66.12, 0.45193, 5, -5.73, 18.88, 0.32447, 109, -5.73, 23.88, 0.2236, 3, 4, -7.79, -82.48, 0.46304, 5, -7.79, 2.52, 0.32404, 109, -7.79, 7.52, 0.21292, 3, 4, -1.94, -82.28, 0.46591, 5, -1.94, 2.72, 0.33414, 109, -1.94, 7.72, 0.19995], "width": 17, "edges": [18, 20, 18, 16, 16, 14, 14, 12, 20, 22, 10, 12, 10, 8, 4, 6, 8, 6, 38, 40, 4, 2, 40, 2, 38, 28, 28, 30, 22, 24, 24, 26, 2, 0, 0, 26], "type": "mesh", "hull": 14, "height": 36}}, "Nose": {"Nose": {"triangles": [18, 8, 9, 7, 8, 18, 10, 18, 9, 11, 18, 19, 21, 6, 7, 22, 6, 21, 23, 22, 14, 23, 14, 15, 5, 6, 22, 5, 22, 23, 12, 20, 3, 4, 12, 3, 0, 1, 20, 3, 20, 1, 1, 2, 3, 4, 15, 16, 4, 16, 12, 4, 23, 15, 5, 23, 4, 15, 14, 17, 15, 17, 16, 16, 17, 19, 20, 16, 19, 12, 16, 20, 22, 21, 13, 22, 13, 14, 14, 13, 11, 14, 11, 17, 13, 21, 7, 11, 13, 7, 17, 11, 19, 19, 10, 0, 19, 18, 10, 20, 19, 0, 11, 7, 18], "uvs": [1, 0.2328, 1, 1, 0.90724, 1, 0.82951, 0.90029, 0.50634, 0.95529, 0, 0.75529, 0, 0.3953, 0.31202, 0.2878, 0.39793, 0, 0.54111, 0, 0.79474, 0, 0.48179, 0.4628, 0.7477, 0.79529, 0.29975, 0.5028, 0.33043, 0.61279, 0.49611, 0.69529, 0.6577, 0.70529, 0.55747, 0.5753, 0.48588, 0.2553, 0.63724, 0.4803, 0.80906, 0.67029, 0.17089, 0.4278, 0.14634, 0.5578, 0.25271, 0.67779], "vertices": [2, 5, 0.83, 54.01, 0.436, 4, 0.83, -30.99, 0.564, 2, 5, 0.83, 40.2, 0.428, 4, 0.83, -44.8, 0.572, 2, 5, -1.21, 40.2, 0.42, 4, -1.21, -44.8, 0.58, 2, 5, -2.92, 42, 0.428, 4, -2.92, -43, 0.572, 2, 5, -10.03, 41.01, 0.436, 4, -10.03, -43.99, 0.564, 2, 5, -21.17, 44.61, 0.436, 4, -21.17, -40.39, 0.564, 2, 5, -21.17, 51.09, 0.436, 4, -21.17, -33.91, 0.564, 2, 5, -14.3, 53.02, 0.436, 4, -14.3, -31.98, 0.564, 2, 5, -12.41, 58.2, 0.436, 4, -12.41, -26.8, 0.564, 2, 5, -9.26, 58.2, 0.436, 4, -9.26, -26.8, 0.564, 2, 5, -3.68, 58.2, 0.436, 4, -3.68, -26.8, 0.564, 2, 5, -10.57, 49.87, 0.42, 4, -10.57, -35.13, 0.58, 2, 5, -4.72, 43.89, 0.42, 4, -4.72, -41.11, 0.58, 2, 5, -14.57, 49.15, 0.412, 4, -14.57, -35.85, 0.588, 2, 5, -13.9, 47.17, 0.412, 4, -13.9, -37.83, 0.588, 2, 5, -10.25, 45.69, 0.42, 4, -10.25, -39.31, 0.58, 2, 5, -6.7, 45.51, 0.412, 4, -6.7, -39.49, 0.588, 2, 5, -8.9, 47.85, 0.412, 4, -8.9, -37.15, 0.588, 2, 5, -10.48, 53.61, 0.42, 4, -10.48, -31.39, 0.58, 2, 5, -7.15, 49.56, 0.42, 4, -7.15, -35.44, 0.58, 2, 5, -3.37, 46.14, 0.412, 4, -3.37, -38.86, 0.588, 2, 5, -17.41, 50.5, 0.428, 4, -17.41, -34.5, 0.572, 2, 5, -17.95, 48.16, 0.428, 4, -17.95, -36.84, 0.572, 2, 5, -15.61, 46, 0.42, 4, -15.61, -39, 0.58], "width": 23, "edges": [12, 14, 14, 16, 2, 0, 20, 0, 10, 12, 10, 8, 8, 6, 2, 4, 6, 4, 16, 18, 18, 20], "type": "mesh", "hull": 11, "height": 18}}, "Eye2": {"Eye": {"scaleX": -1, "x": 0.5, "width": 14, "y": -0.18, "height": 13}}, "Nose2": {"Nose": {"triangles": [0, 1, 20, 3, 20, 1, 4, 12, 3, 5, 23, 4, 4, 23, 15, 23, 14, 15, 4, 16, 12, 4, 15, 16, 12, 20, 3, 1, 2, 3, 12, 16, 20, 5, 22, 23, 5, 6, 22, 15, 17, 16, 20, 16, 19, 15, 14, 17, 23, 22, 14, 16, 17, 19, 20, 19, 0, 22, 13, 14, 14, 11, 17, 14, 13, 11, 17, 11, 19, 22, 21, 13, 22, 6, 21, 11, 13, 7, 11, 18, 19, 19, 18, 10, 19, 10, 0, 10, 18, 9, 13, 21, 7, 11, 7, 18, 21, 6, 7, 7, 8, 18, 18, 8, 9], "uvs": [1, 0.2328, 1, 1, 0.90724, 1, 0.82951, 0.90029, 0.50634, 0.95529, 0, 0.75529, 0, 0.3953, 0.31202, 0.2878, 0.39793, 0, 0.54111, 0, 0.79474, 0, 0.48179, 0.4628, 0.7477, 0.79529, 0.29975, 0.5028, 0.33043, 0.61279, 0.49611, 0.69529, 0.6577, 0.70529, 0.55747, 0.5753, 0.48588, 0.2553, 0.63724, 0.4803, 0.80906, 0.67029, 0.17089, 0.4278, 0.14634, 0.5578, 0.25271, 0.67779], "vertices": [2, 4, -1.46, -30.99, 0.572, 5, -1.46, 54.01, 0.428, 2, 4, -1.46, -44.8, 0.572, 5, -1.46, 40.2, 0.428, 2, 4, 0.58, -44.8, 0.572, 5, 0.58, 40.2, 0.428, 2, 4, 2.29, -43, 0.58, 5, 2.29, 42, 0.42, 2, 4, 9.4, -43.99, 0.572, 5, 9.4, 41.01, 0.428, 2, 4, 20.54, -40.39, 0.572, 5, 20.54, 44.61, 0.428, 2, 4, 20.54, -33.91, 0.572, 5, 20.54, 51.09, 0.428, 2, 4, 13.68, -31.98, 0.588, 5, 13.68, 53.02, 0.412, 2, 4, 11.79, -26.8, 0.572, 5, 11.79, 58.2, 0.428, 2, 4, 8.64, -26.8, 0.572, 5, 8.64, 58.2, 0.428, 2, 4, 3.06, -26.8, 0.572, 5, 3.06, 58.2, 0.428, 2, 4, 9.94, -35.13, 0.604, 5, 9.94, 49.87, 0.396, 2, 4, 4.09, -41.11, 0.588, 5, 4.09, 43.89, 0.412, 2, 4, 13.95, -35.85, 0.588, 5, 13.95, 49.15, 0.412, 2, 4, 13.27, -37.83, 0.596, 5, 13.27, 47.17, 0.404, 2, 4, 9.63, -39.31, 0.596, 5, 9.63, 45.69, 0.404, 2, 4, 6.07, -39.49, 0.604, 5, 6.07, 45.51, 0.396, 2, 4, 8.28, -37.15, 0.604, 5, 8.28, 47.85, 0.396, 2, 4, 9.85, -31.39, 0.588, 5, 9.85, 53.61, 0.412, 2, 4, 6.52, -35.44, 0.588, 5, 6.52, 49.56, 0.412, 2, 4, 2.74, -38.86, 0.572, 5, 2.74, 46.14, 0.428, 2, 4, 16.78, -34.5, 0.58, 5, 16.78, 50.5, 0.42, 2, 4, 17.32, -36.84, 0.58, 5, 17.32, 48.16, 0.42, 2, 4, 14.98, -39, 0.596, 5, 14.98, 46, 0.404], "width": 23, "edges": [12, 14, 14, 16, 2, 0, 20, 0, 10, 12, 10, 8, 8, 6, 2, 4, 6, 4, 16, 18, 18, 20], "type": "mesh", "hull": 11, "height": 18}}, "Beard10": {"Beard10": {"triangles": [0, 7, 8, 6, 7, 0, 0, 4, 6, 4, 5, 6, 3, 4, 0, 1, 3, 0, 3, 1, 2], "uvs": [0.7557, 0.38983, 1, 0.66263, 1, 1, 0.5056, 1, 0.33191, 0.78583, 0, 0.60396, 0, 0.36049, 0.56118, 0, 0.81128, 0], "vertices": [3, 50, 24.09, -5.51, 0.00103, 51, 11.33, -6.73, 0.50207, 52, 2.29, -6.8, 0.4969, 3, 50, 11.14, -7.65, 0.65738, 51, -1.77, -7.51, 0.34258, 52, -7.16, -15.91, 5e-05, 1, 50, -3.75, -4.67, 1, 1, 50, -1.91, 4.54, 1, 2, 50, 8.19, 5.88, 0.86453, 51, -3.29, 6.26, 0.13547, 3, 50, 17.45, 10.46, 0.06726, 51, 6.4, 9.85, 0.9309, 52, -12.22, 2.6, 0.00184, 2, 51, 16.86, 6.59, 0.20796, 52, -2.15, 6.91, 0.79204, 1, 52, 16.96, 3.5, 1, 1, 52, 18.83, -0.86, 1], "width": 16, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 4, 2, 0, 2, 14, 16, 0, 16], "type": "mesh", "hull": 9, "height": 43}}, "Beard11": {"Beard10": {"triangles": [0, 7, 8, 6, 7, 0, 4, 5, 6, 0, 4, 6, 3, 4, 0, 1, 3, 0, 3, 1, 2], "uvs": [0.7557, 0.38983, 1, 0.66263, 1, 1, 0.5056, 1, 0.33191, 0.78583, 0, 0.60396, 0, 0.36049, 0.56118, 0, 0.81128, 0], "vertices": [3, 64, 24.3, 4.47, 0.00172, 65, 11.31, 6.36, 0.72462, 66, 2.65, 6.45, 0.27366, 2, 64, 11.46, 7.17, 0.8494, 65, -1.8, 6.97, 0.1506, 1, 64, -3.54, 4.83, 1, 2, 64, -2.1, -4.45, 0.99976, 65, -13.32, -6.67, 0.00024, 3, 64, 7.93, -6.23, 0.60869, 65, -3.13, -6.82, 0.37724, 66, -16.88, 7.37, 0.01407, 3, 64, 16.99, -11.2, 0.00207, 65, 6.61, -10.28, 0.70694, 66, -12.46, -1.97, 0.29099, 2, 65, 17.02, -6.88, 0.00812, 66, -2.7, -6.94, 0.99188, 1, 66, 16.6, -4.82, 1, 1, 66, 18.76, -0.58, 1], "width": 16, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 4, 2, 0, 2, 14, 16, 0, 16], "type": "mesh", "hull": 9, "height": 43}}, "Beard12": {"Beard9": {"triangles": [7, 8, 4, 6, 7, 4, 5, 6, 4, 9, 0, 3, 8, 9, 3, 8, 3, 4, 2, 0, 1, 3, 0, 2], "uvs": [0.55322, 0, 1, 0, 0.74222, 0.24908, 0.65822, 0.41921, 0.66522, 0.58539, 0.88222, 0.90586, 0.65822, 1, 0.33622, 1, 0, 0.60517, 0, 0.30843], "vertices": [1, 69, 9.43, -6.4, 1, 1, 69, 18.43, 0.95, 1, 2, 69, 5.99, 5.59, 0.78966, 68, 14.83, 8.19, 0.21034, 3, 69, -0.65, 10.27, 0.08657, 68, 6.86, 6.64, 0.90616, 67, 19.77, 8.9, 0.00727, 2, 68, -0.75, 7.42, 0.39947, 67, 12.54, 6.42, 0.60053, 1, 67, -3.24, 6.61, 1, 1, 67, -5.29, -0.35, 1, 1, 67, -2.39, -8.2, 1, 2, 68, -3.02, -9.75, 0.35528, 67, 17.67, -10.12, 0.64472, 3, 69, -10.68, -4.5, 0.00256, 68, 10.58, -10.83, 0.99254, 67, 30.48, -5.39, 0.0049], "width": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "type": "mesh", "hull": 10, "height": 46}}, "Beard13": {"Beard8": {"triangles": [6, 7, 5, 7, 4, 5, 8, 10, 4, 8, 9, 10, 7, 8, 4, 4, 10, 3, 10, 0, 3, 3, 0, 2, 2, 0, 1], "uvs": [0.72434, 0, 1, 0, 0.99665, 0.13717, 0.76519, 0.3063, 0.74476, 0.47544, 0.85369, 0.80977, 0.84007, 1, 0.40438, 1, 0, 0.57377, 0, 0.29057, 0.35672, 0.10177], "vertices": [1, 91, 14.45, -5.16, 1, 1, 91, 20.39, -1.14, 1, 2, 91, 16.86, 3.92, 0.99955, 90, 22.01, 16.01, 0.00045, 3, 91, 7.61, 6.85, 0.72728, 90, 14.26, 10.17, 0.26762, 89, 17.42, 16.71, 0.0051, 3, 91, 2.9, 12.86, 0.12546, 90, 6.63, 9.82, 0.69065, 89, 11.55, 11.83, 0.18389, 2, 90, -8.34, 13, 0.01075, 89, -2.31, 5.34, 0.98925, 1, 89, -9.05, 0.05, 1, 1, 89, -2.43, -9.14, 1, 2, 90, 1.76, -9.44, 0.96252, 89, 19.28, -6.47, 0.03748, 2, 91, -8.47, -4.88, 0.00077, 90, 14.5, -9.74, 0.99923, 2, 91, 3.97, -6.72, 0.99967, 90, 23.21, -0.66, 0.00033], "width": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "type": "mesh", "hull": 11, "height": 45}}, "Beard14": {"Beard7": {"triangles": [0, 2, 10, 1, 2, 0, 3, 10, 2, 8, 9, 10, 8, 10, 3, 7, 8, 3, 7, 3, 4, 6, 7, 4, 5, 6, 4], "uvs": [1, 0, 1, 0.15434, 0.73464, 0.32773, 0.70464, 0.4368, 0.75714, 0.55706, 1, 0.81994, 0.89964, 1, 0.47964, 1, 0, 0.63816, 0, 0.32773, 0.35964, 0.15714], "vertices": [1, 28, 47.27, -25.24, 1, 1, 28, 38.72, -22.1, 1, 1, 28, 31.13, -13.1, 1, 1, 28, 25.31, -10.27, 1, 1, 28, 18.25, -8.91, 1, 1, 28, 1.85, -8.6, 1, 1, 28, -7.37, -2.87, 1, 1, 28, -4.19, 5.81, 1, 1, 28, 19.49, 8.37, 1, 1, 28, 36.68, 2.07, 1, 1, 28, 43.41, -8.82, 1], "width": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "type": "mesh", "hull": 11, "height": 59}}, "Beard8": {"Beard8": {"triangles": [7, 8, 4, 7, 4, 5, 6, 7, 5, 8, 9, 10, 8, 10, 4, 2, 0, 1, 3, 0, 2, 10, 0, 3, 4, 10, 3], "uvs": [0.72434, 0, 1, 0, 0.99665, 0.13717, 0.76519, 0.3063, 0.74476, 0.47544, 0.85369, 0.80977, 0.84007, 1, 0.40438, 1, 0, 0.57377, 0, 0.29057, 0.35672, 0.10177], "vertices": [1, 27, 15.56, 3.44, 1, 1, 27, 21.04, -1.19, 1, 2, 27, 16.99, -5.85, 0.9976, 26, 22.75, -16.22, 0.0024, 3, 27, 7.48, -7.77, 0.68385, 26, 14.72, -10.78, 0.28124, 25, 25.13, -16.16, 0.03491, 3, 27, 2.16, -13.24, 0.11004, 26, 7.09, -10.81, 0.55855, 25, 18.27, -12.83, 0.33141, 2, 26, -7.71, -14.75, 0.00418, 25, 3.26, -9.84, 0.99582, 1, 25, -4.55, -6.32, 1, 1, 25, -0.33, 4.19, 1, 2, 26, 1.25, 8.17, 0.87368, 25, 21.39, 6.79, 0.12632, 2, 27, -7.26, 5.61, 0.27972, 26, 13.96, 9.11, 0.72028, 1, 27, 5.31, 6.11, 1], "width": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "type": "mesh", "hull": 11, "height": 45}}, "Beard15": {"Beard6": {"triangles": [4, 5, 3, 5, 6, 2, 6, 7, 2, 3, 5, 2, 7, 8, 2, 0, 2, 8, 1, 2, 0], "uvs": [1, 0, 1, 0.16578, 0.81606, 0.36942, 0.89512, 0.5459, 1, 0.82421, 0.68429, 1, 0.2363, 1, 0, 0.72578, 0, 0.27439], "vertices": [1, 81, 16.99, -1.16, 1, 3, 81, 7.48, 4.26, 0.99923, 79, 29.28, 15.4, 6e-05, 80, 21.01, 5, 0.00071, 4, 81, -5.74, 8.2, 0.06261, 79, 16.46, 10.3, 0.40489, 80, 7.45, 7.58, 0.53199, 78, 30.31, 12.23, 0.0005, 3, 79, 4.74, 9.88, 0.77794, 80, -2.65, 13.52, 0.00017, 78, 18.73, 10.39, 0.22189, 1, 78, 0.56, 7.15, 1, 1, 78, -9.16, -1.15, 1, 1, 78, -7.11, -8.48, 1, 2, 79, -4.72, -6.94, 0.02277, 78, 11.4, -7.47, 0.97723, 2, 81, -7.16, -6.96, 0.01988, 80, 7.57, -7.65, 0.98012], "width": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 0, 16], "type": "mesh", "hull": 9, "height": 66}}, "Beard9": {"Beard9": {"triangles": [7, 8, 4, 6, 7, 4, 5, 6, 4, 9, 0, 3, 8, 9, 3, 8, 3, 4, 2, 0, 1, 3, 0, 2], "uvs": [0.55322, 0, 1, 0, 0.74222, 0.24908, 0.65822, 0.41921, 0.66522, 0.58539, 0.88222, 0.90586, 0.65822, 1, 0.33622, 1, 0, 0.60517, 0, 0.30843], "vertices": [1, 49, 10.57, 6.75, 1, 1, 49, 18.84, -1.41, 1, 2, 49, 6.02, -4.86, 0.87237, 48, 15.89, -7.65, 0.12763, 3, 49, -1.03, -8.9, 0.10416, 48, 7.96, -5.87, 0.86755, 47, 20.27, -9.07, 0.02829, 3, 49, -6.27, -14.47, 4e-05, 48, 0.34, -6.45, 0.35912, 47, 13.39, -5.75, 0.64084, 1, 47, -2.31, -4.05, 1, 1, 47, -3.51, 3.11, 1, 1, 47, 0.31, 10.56, 1, 2, 48, -1.47, 10.77, 0.61022, 47, 20.46, 10.06, 0.38978, 3, 49, -9.64, 6.75, 0.02159, 48, 12.16, 11.48, 0.9782, 47, 32.61, 3.84, 0.0002], "width": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "type": "mesh", "hull": 10, "height": 46}}, "Beard16": {"Beard5": {"triangles": [6, 4, 5, 6, 3, 4, 3, 7, 2, 3, 6, 7, 7, 8, 2, 2, 8, 1, 8, 9, 1, 1, 9, 0], "uvs": [0.75898, 0, 0.64298, 0.31206, 0.63331, 0.4298, 0.68165, 0.5516, 1, 0.81956, 1, 1, 0.35298, 1, 0, 0.63686, 0, 0.36078, 0.49798, 0], "vertices": [1, 84, 26.83, -0.93, 1, 1, 84, 11.86, 4.11, 1, 2, 84, 6.55, 6.65, 0.95062, 83, 14.93, 9.52, 0.04938, 3, 84, 1.61, 10.36, 0.39867, 83, 9.04, 7.65, 0.57746, 82, 17.24, 10.41, 0.02386, 2, 83, -5.93, 7.51, 0.00606, 82, 3.35, 4.84, 0.99394, 1, 82, -2.65, -1.9, 1, 2, 83, -7.78, -8.7, 0.01339, 82, 7.5, -10.93, 0.98661, 2, 83, 11.76, -7.03, 0.99994, 82, 25.11, -2.28, 6e-05, 2, 84, 3.47, -6.74, 0.49007, 83, 24.05, -0.75, 0.50993, 1, 84, 24.3, -5.79, 1], "width": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "type": "mesh", "hull": 10, "height": 50}}, "Fang": {"Fang": {"rotation": 89.2, "x": 10.96, "width": 20, "y": -2.51, "height": 21}}, "Beard17": {"Beard4": {"triangles": [6, 7, 2, 2, 7, 8, 2, 8, 9, 6, 2, 5, 2, 3, 5, 3, 4, 5, 0, 1, 10, 2, 9, 1, 9, 10, 1], "uvs": [0, 0, 0.33896, 0.24836, 0.55016, 0.21693, 0.85376, 0, 1, 0, 1, 0.46836, 0.82736, 0.89265, 0.53696, 1, 0.30266, 0.99479, 0.14426, 0.67265, 0, 0.20908], "vertices": [2, 76, 18.83, -1.59, 0.00052, 77, 6.75, 1.09, 0.99948, 3, 76, 4.03, 8.16, 0.32921, 74, 23.45, 12.86, 0.00387, 75, 13.22, 8.2, 0.66692, 3, 76, -2.18, 16.73, 0.00029, 74, 13.64, 8.91, 0.4155, 75, 2.64, 8.57, 0.58421, 1, 74, -2.03, 6.47, 1, 1, 74, -8.62, 3.32, 1, 1, 74, -4.38, -5.56, 1, 2, 74, 7.25, -9.87, 0.99994, 75, -10.83, -5.99, 6e-05, 2, 74, 21.32, -5.65, 0.0809, 75, 3.75, -7.85, 0.9191, 2, 76, -6.91, -3.21, 0.00649, 75, 15.45, -7.42, 0.99351, 3, 76, 3.35, -5.02, 0.94146, 77, -7.09, -6.66, 0.05004, 75, 23.19, -0.45, 0.0085, 1, 77, 4.32, -2.57, 1], "width": 50, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "type": "mesh", "hull": 11, "height": 21}}, "Beard18": {"Beard3": {"triangles": [5, 10, 3, 7, 9, 6, 7, 8, 9, 9, 10, 6, 10, 5, 6, 5, 3, 4, 1, 2, 11, 11, 2, 10, 2, 3, 10, 12, 13, 11, 11, 13, 1, 13, 14, 1, 14, 0, 1], "uvs": [0, 0.51115, 0.16672, 0.54215, 0.29489, 0.4104, 0.48268, 0.1314, 0.71518, 0, 0.94172, 0.1934, 1, 0.55765, 1, 1, 0.89403, 1, 0.75989, 0.77465, 0.64662, 0.7359, 0.47076, 0.8754, 0.28595, 1, 0.0773, 0.91415, 0, 0.74365], "vertices": [1, 73, 17.38, 2.31, 1, 2, 73, 8.71, 2.88, 0.98541, 72, 20.46, 7.4, 0.01459, 2, 73, 2.47, 6.4, 0.40178, 72, 13.33, 6.64, 0.59822, 2, 72, 2.09, 7.06, 0.88201, 71, 22.05, 5.03, 0.11799, 1, 71, 10.11, 8.25, 1, 1, 71, -1.85, 4.97, 1, 1, 71, -5.24, -2.15, 1, 1, 71, -5.67, -10.99, 1, 1, 71, -0.17, -11.26, 1, 2, 72, -4.72, -11.02, 0.00775, 71, 7.02, -7.11, 0.99225, 2, 72, 0.14, -7.6, 0.2592, 71, 12.94, -6.62, 0.7408, 2, 72, 9.54, -5.84, 0.97971, 71, 21.94, -9.86, 0.02029, 2, 73, 1.32, -5.35, 0.85893, 72, 19.21, -3.59, 0.14107, 1, 73, 12.3, -5.13, 1, 1, 73, 16.75, -2.3, 1], "width": 52, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28], "type": "mesh", "hull": 15, "height": 20}}, "Beard4": {"Beard4": {"triangles": [6, 2, 5, 2, 3, 5, 3, 4, 5, 2, 9, 1, 2, 8, 9, 9, 10, 1, 6, 7, 2, 2, 7, 8, 0, 1, 10], "uvs": [0, 0, 0.33896, 0.24836, 0.55016, 0.21693, 0.85376, 0, 1, 0, 1, 0.46836, 0.82736, 0.89265, 0.53696, 1, 0.30266, 0.99479, 0.14426, 0.67265, 0, 0.20908], "vertices": [1, 42, 11.95, -0.2, 1, 3, 42, -4.56, -6.64, 0.01764, 40, 17.36, -8.83, 0.18767, 41, 6.64, -6.04, 0.79469, 3, 40, 6.78, -8.44, 0.87911, 41, -2.02, -12.12, 0.05006, 39, 15.33, -10.74, 0.07083, 2, 40, -8.77, -11.47, 0.00705, 39, 0.46, -5.25, 0.99295, 1, 39, -5.39, -0.86, 1, 1, 39, 0.52, 7, 1, 2, 40, -5.6, 7.05, 0.15495, 39, 12.78, 8.94, 0.84505, 1, 40, 9.07, 7.86, 1, 2, 40, 20.71, 6.59, 0.33855, 41, 0.01, 8.28, 0.66145, 2, 42, -2.48, 6.39, 0.1642, 41, 10.3, 6.64, 0.8358, 2, 42, 9.22, 3.24, 0.9996, 41, 21.53, 2.1, 0.0004], "width": 50, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "type": "mesh", "hull": 11, "height": 21}}, "Beard19": {"Beard2": {"triangles": [13, 8, 14, 6, 7, 5, 13, 7, 8, 13, 5, 7, 13, 4, 5, 10, 11, 15, 15, 14, 10, 14, 9, 10, 8, 9, 14, 3, 13, 14, 2, 3, 14, 4, 13, 3, 15, 11, 12, 15, 12, 0, 15, 0, 1, 2, 15, 1, 14, 15, 2], "uvs": [1, 0.24565, 1, 0.46609, 0.88512, 0.68654, 0.65978, 0.88631, 0.43801, 0.8932, 0.36468, 1, 0, 1, 0, 0.77265, 0.18763, 0.6142, 0.29493, 0.32143, 0.46841, 0.07343, 0.60791, 0, 0.81001, 0, 0.34322, 0.69687, 0.51134, 0.43165, 0.71522, 0.25254], "vertices": [1, 97, -6.24, -3.22, 1, 2, 97, -4.48, -8.9, 0.99287, 98, -14.33, -12.77, 0.00713, 2, 97, 2.9, -12.8, 0.88332, 98, -6.22, -14.78, 0.11668, 2, 97, 15.2, -14.6, 0.2983, 98, 6.11, -13.53, 0.7017, 3, 97, 25.93, -11.55, 0.00548, 98, 15.79, -8, 0.70067, 99, -1.67, -7.87, 0.29386, 2, 98, 20.31, -8.28, 0.23976, 99, 2.56, -9.42, 0.76024, 1, 99, 20.52, -3.5, 1, 1, 99, 18.65, 2.34, 1, 2, 98, 21.91, 5.6, 0.00658, 99, 8.06, 3.44, 0.99342, 2, 98, 14.08, 9.05, 0.68412, 99, 1.59, 9, 0.31588, 2, 98, 3.51, 10.34, 0.99907, 99, -8.18, 13.29, 0.00093, 2, 97, 10.97, 9.08, 0.20429, 98, -3.69, 8.47, 0.79571, 2, 97, 1.21, 6.05, 0.97663, 98, -12.39, 3.14, 0.02337, 2, 98, 16.54, -0.81, 0.05319, 99, 1.08, -1.17, 0.94681, 2, 98, 6.42, 0.85, 0.99998, 99, -8.12, 3.33, 2e-05, 1, 97, 7.95, 1, 1], "width": 40, "edges": [22, 20, 20, 18, 18, 16, 12, 14, 16, 14, 10, 12, 10, 8, 8, 6, 6, 4, 4, 2, 22, 24, 2, 0, 24, 0], "type": "mesh", "hull": 13, "height": 21}}, "Beard5": {"Beard5": {"triangles": [6, 3, 4, 6, 4, 5, 7, 8, 2, 3, 6, 7, 3, 7, 2, 1, 9, 0, 8, 9, 1, 2, 8, 1], "uvs": [0.75898, 0, 0.64298, 0.31206, 0.63331, 0.4298, 0.68165, 0.5516, 1, 0.81956, 1, 1, 0.35298, 1, 0, 0.63686, 0, 0.36078, 0.49798, 0], "vertices": [1, 38, 25.36, -2.76, 1, 2, 38, 9.91, -6.02, 0.80769, 37, 20.6, -10.53, 0.19231, 2, 38, 4.33, -7.92, 0.20034, 37, 15.05, -8.57, 0.79966, 2, 37, 8.93, -7.71, 0.97126, 36, 16.69, -10.64, 0.02874, 2, 37, -5.85, -10.06, 0.06859, 36, 2.2, -6.87, 0.93141, 1, 36, -4.59, -0.94, 1, 2, 37, -10.38, 5.61, 0.00206, 36, 4.34, 9.3, 0.99794, 2, 38, -10.06, 0.84, 0.20994, 37, 9.17, 7.23, 0.79006, 1, 38, 2.84, 5.74, 1, 1, 38, 23.42, 2.36, 1], "width": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "type": "mesh", "hull": 10, "height": 50}}, "Bread_Mouth": {"Bread_Mouth": {"rotation": 101.48, "x": 12.08, "width": 11, "y": 0.67, "height": 29}}, "Beard6": {"Beard6": {"triangles": [6, 7, 2, 5, 6, 2, 4, 5, 3, 0, 2, 8, 7, 8, 2, 3, 5, 2, 1, 2, 0], "uvs": [1, 0, 1, 0.16578, 0.81606, 0.36942, 0.89512, 0.5459, 1, 0.82421, 0.68429, 1, 0.2363, 1, 0, 0.72578, 0, 0.27439], "vertices": [1, 35, 17.53, 1.89, 1, 1, 35, 8.6, -4.43, 1, 2, 35, -4.18, -9.65, 0.06901, 34, 6.86, -7.72, 0.93099, 3, 34, -3.97, -12.22, 0.48415, 32, 22.68, -9.86, 0.15165, 33, 6.62, -10.44, 0.3642, 3, 34, -21.13, -18.98, 0.00337, 32, 4.4, -12.37, 0.99661, 33, -11.81, -11.44, 2e-05, 1, 32, -7.4, -7.46, 1, 1, 32, -7.7, 0.15, 1, 2, 32, 10.22, 4.88, 0.97997, 33, -4.6, 5.27, 0.02003, 3, 35, -7.07, 5.3, 0.02713, 34, 9.07, 7.34, 0.84511, 33, 25.17, 4.01, 0.12776], "width": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 0, 16], "type": "mesh", "hull": 9, "height": 66}}, "Beard7": {"Beard7": {"triangles": [0, 2, 10, 1, 2, 0, 3, 10, 2, 8, 9, 10, 8, 10, 3, 7, 8, 3, 7, 3, 4, 6, 7, 4, 5, 6, 4], "uvs": [1, 0, 1, 0.15434, 0.73464, 0.32773, 0.70464, 0.4368, 0.75714, 0.55706, 1, 0.81994, 0.89964, 1, 0.47964, 1, 0, 0.63816, 0, 0.32773, 0.35964, 0.15714], "vertices": [1, 28, 47.27, -25.24, 1, 1, 28, 38.72, -22.1, 1, 1, 28, 31.13, -13.1, 1, 1, 28, 25.31, -10.27, 1, 1, 28, 18.25, -8.91, 1, 1, 28, 1.85, -8.6, 1, 1, 28, -7.37, -2.87, 1, 1, 28, -4.19, 5.81, 1, 1, 28, 19.49, 8.37, 1, 1, 28, 36.68, 2.07, 1, 1, 28, 43.41, -8.82, 1], "width": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "type": "mesh", "hull": 11, "height": 59}}, "Beard2": {"Beard2": {"triangles": [13, 4, 5, 13, 5, 7, 13, 7, 8, 6, 7, 5, 13, 8, 14, 4, 13, 3, 2, 3, 14, 3, 13, 14, 8, 9, 14, 14, 9, 10, 14, 15, 2, 2, 15, 1, 15, 0, 1, 15, 14, 10, 10, 11, 15, 15, 12, 0, 15, 11, 12], "uvs": [1, 0.24565, 1, 0.46609, 0.88512, 0.68654, 0.65978, 0.88631, 0.43801, 0.8932, 0.36468, 1, 0, 1, 0, 0.77265, 0.18763, 0.6142, 0.29493, 0.32143, 0.46841, 0.07343, 0.60791, 0, 0.81001, 0, 0.34322, 0.69687, 0.51134, 0.43165, 0.71522, 0.25254], "vertices": [2, 10, -8.03, 5.17, 0.94846, 11, -18.39, 10.44, 0.05154, 2, 10, -6.02, 10.77, 0.86737, 11, -14.95, 15.3, 0.13263, 2, 10, 1.61, 14.35, 0.56962, 11, -6.63, 16.71, 0.43038, 3, 10, 14.46, 15.47, 0.02785, 11, 6.05, 14.33, 0.97201, 12, -12.79, 11.19, 0.00013, 2, 11, 15.57, 7.82, 0.57037, 12, -1.82, 7.61, 0.42963, 2, 11, 20.34, 7.97, 0.12815, 12, 2.72, 9.1, 0.87185, 1, 12, 20.65, 2.93, 1, 1, 12, 18.65, -2.88, 1, 3, 10, 35.09, 0.27, 0.00064, 11, 21.84, -5.85, 0.02287, 12, 8.04, -3.75, 0.97649, 3, 10, 27.17, -5.29, 0.12228, 11, 12.71, -9.08, 0.6045, 12, 0.19, -9.4, 0.27322, 3, 10, 16.42, -8.54, 0.88359, 11, 1.48, -9.33, 0.11615, 12, -10.52, -12.8, 0.00027, 1, 10, 8.92, -7.96, 1, 1, 10, -0.97, -4.41, 1, 2, 11, 16.52, 0.64, 0.0426, 12, 1.11, 1, 0.9574, 3, 10, 17.58, 1.31, 0.00464, 11, 5.25, -0.15, 0.99535, 12, -9.48, -2.93, 1e-05, 2, 10, 5.97, 0.34, 0.98978, 11, -6.2, 2.04, 0.01022], "width": 40, "edges": [22, 20, 20, 18, 18, 16, 12, 14, 16, 14, 10, 12, 10, 8, 8, 6, 6, 4, 4, 2, 22, 24, 2, 0, 24, 0], "type": "mesh", "hull": 13, "height": 21}}, "Eyebrow2": {"Eyebrow": {"triangles": [3, 4, 2, 4, 5, 1, 4, 1, 2, 5, 0, 1], "uvs": [0.0149, 0, 0.59545, 0, 1, 0.37229, 1, 0.80984, 0.5099, 1, 0, 1], "vertices": [1, 95, 16.7, -3.51, 1, 1, 95, 14.18, 6.63, 1, 2, 95, -3.84, 9.66, 0.30992, 94, 11.07, 10.13, 0.69008, 2, 94, -8.54, 8.33, 0.01224, 93, 3.85, 9.97, 0.98776, 1, 93, -5.74, 2.29, 1, 1, 93, -6.89, -6.82, 1], "width": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "type": "mesh", "hull": 6, "height": 36}}, "Beard3": {"Beard3": {"triangles": [5, 3, 4, 10, 5, 6, 9, 10, 6, 7, 8, 9, 7, 9, 6, 5, 10, 3, 2, 3, 10, 11, 2, 10, 14, 0, 1, 1, 2, 11, 13, 14, 1, 11, 13, 1, 12, 13, 11], "uvs": [0, 0.51115, 0.16672, 0.54215, 0.29489, 0.4104, 0.48268, 0.1314, 0.71518, 0, 0.94172, 0.1934, 1, 0.55765, 1, 1, 0.89403, 1, 0.75989, 0.77465, 0.64662, 0.7359, 0.47076, 0.8754, 0.28595, 1, 0.0773, 0.91415, 0, 0.74365], "vertices": [1, 45, 19.25, -3.46, 1, 1, 45, 10.59, -2.8, 1, 2, 45, 3.91, -5.41, 0.71099, 44, 16.52, -6.59, 0.28901, 2, 45, -5.88, -10.95, 0.00081, 44, 5.29, -7.18, 0.99919, 2, 44, -6.69, -4.1, 0.04339, 43, 9.33, -7.31, 0.95661, 1, 43, -2.96, -5.63, 1, 1, 43, -7.26, 0.99, 1, 1, 43, -8.86, 9.69, 1, 1, 43, -3.44, 10.68, 1, 2, 44, -1.81, 10.79, 0.0662, 43, 4.24, 7.51, 0.9338, 2, 44, 3.11, 7.45, 0.53738, 43, 10.17, 7.81, 0.46262, 3, 45, -5.19, 3.93, 0.07144, 44, 12.53, 5.83, 0.9259, 43, 18.66, 12.21, 0.00266, 1, 45, 4.43, 6.38, 1, 1, 45, 15.27, 4.62, 1, 1, 45, 19.27, 1.19, 1], "width": 52, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28], "type": "mesh", "hull": 15, "height": 20}}}, "name": "default"}], "skeleton": {"images": "C:/Users/<USER>/Desktop/ic_tx/6. zon club/btn rut tien/", "x": -277.95, "width": 544, "y": 1.06, "spine": "3.8.99", "audio": "", "hash": "04887fq6zAug8WIiCbMMBFIYbus", "height": 166.58}, "slots": [{"attachment": "images/bg_vang", "name": "images/bg_vang", "bone": "images/bg_vang"}, {"attachment": "images/bg_vang", "blend": "additive", "name": "images/bg_vang2", "bone": "images/bg_vang2"}, {"attachment": "images/v1", "name": "images/v1", "bone": "images/v1"}, {"attachment": "images/v2", "name": "images/v2", "bone": "images/v2"}, {"attachment": "images/v3", "name": "images/v3", "bone": "images/v3"}, {"attachment": "images/v4", "name": "images/v4", "bone": "images/v4"}, {"attachment": "images/v5", "name": "images/v5", "bone": "images/v5"}, {"attachment": "images/v6", "name": "images/v6", "bone": "images/v6"}, {"attachment": "bg1", "name": "bg1", "bone": "bg1"}, {"attachment": "bg1", "blend": "additive", "name": "bg2", "bone": "bg2"}, {"attachment": "c1", "name": "c1", "bone": "c1"}, {"attachment": "c2", "name": "c2", "bone": "c2"}, {"attachment": "c2", "name": "c3", "bone": "c3"}, {"attachment": "Beard10", "name": "Beard10", "bone": "Beard40"}, {"attachment": "Beard10", "name": "Beard11", "bone": "Beard43"}, {"attachment": "Beard9", "name": "Beard9", "bone": "Beard16"}, {"attachment": "Beard9", "name": "Beard12", "bone": "Beard46"}, {"attachment": "Beard8", "name": "Beard8", "bone": "Beard17"}, {"attachment": "Beard8", "name": "Beard13", "bone": "Beard68"}, {"attachment": "Beard7", "name": "Beard7", "bone": "Beard20"}, {"name": "Beard21", "bone": "Beard20"}, {"attachment": "Beard7", "name": "Beard14", "bone": "Beard64"}, {"attachment": "Beard6", "name": "Beard6", "bone": "Beard24"}, {"name": "Beard22", "bone": "Beard24"}, {"attachment": "Beard6", "name": "Beard15", "bone": "Beard57"}, {"attachment": "Beard5", "name": "Beard5", "bone": "Beard28"}, {"attachment": "Beard5", "name": "Beard16", "bone": "Beard61"}, {"attachment": "Beard4", "name": "Beard4", "bone": "Beard31"}, {"attachment": "Beard4", "name": "Beard17", "bone": "Beard53"}, {"attachment": "Beard3", "name": "Beard3", "bone": "Beard35"}, {"attachment": "Beard3", "name": "Beard18", "bone": "Beard50"}, {"attachment": "Bread_Mouth5", "name": "Bread_Mouth5", "bone": "Bread_Mouth5"}, {"attachment": "Bread_Mouth5", "name": "Bread_Mouth6", "bone": "Bread_Mouth8"}, {"attachment": "Bread_Mouth4", "name": "Bread_Mouth4", "bone": "Bread_Mouth4"}, {"attachment": "Bread_Mouth4", "name": "Bread_Mouth7", "bone": "Bread_Mouth13"}, {"attachment": "Bread_Mouth3", "name": "Bread_Mouth3", "bone": "Bread_Mouth10"}, {"attachment": "Bread_Mouth3", "name": "Bread_Mouth10", "bone": "Bread_Mouth15"}, {"attachment": "Bread_Mouth2", "name": "Bread_Mouth2", "bone": "Bread_Mouth3"}, {"attachment": "Bread_Mouth2", "name": "Bread_Mouth9", "bone": "Bread_Mouth14"}, {"attachment": "Bread_Mouth", "name": "Bread_Mouth", "bone": "Bread_Mouth"}, {"attachment": "Bread_Mouth", "name": "Bread_Mouth8", "bone": "Bread_Mouth2"}, {"attachment": "Mouth", "name": "Mouth", "bone": "Mouth"}, {"attachment": "Mouth", "name": "Mouth2", "bone": "Mouth2"}, {"attachment": "Rubi", "name": "Rubi", "bone": "Rubi"}, {"attachment": "<PERSON>", "name": "<PERSON>", "bone": "<PERSON>"}, {"attachment": "<PERSON>", "name": "Fang2", "bone": "Fang2"}, {"attachment": "Head", "name": "Head", "bone": "Dragon"}, {"attachment": "Head", "name": "Head2", "bone": "Dragon"}, {"attachment": "Eye", "name": "Eye", "bone": "Eye"}, {"attachment": "Eye", "name": "Eye2", "bone": "Eye2"}, {"attachment": "Eyebrow", "name": "Eyebrow", "bone": "Eyebrow"}, {"attachment": "Eyebrow", "name": "Eyebrow2", "bone": "Eyebrow4"}, {"attachment": "Beard2", "name": "Beard2", "bone": "Beard2"}, {"attachment": "Beard2", "name": "Beard19", "bone": "Beard71"}, {"attachment": "<PERSON>", "name": "<PERSON>", "bone": "<PERSON>"}, {"attachment": "<PERSON>", "name": "Beard20", "bone": "Beard70"}, {"attachment": "Nose", "name": "Nose", "bone": "Dragon"}, {"attachment": "Nose", "name": "Nose2", "bone": "Dragon"}, {"name": "frame_set/E0", "bone": "Fx_Rubi"}, {"name": "frame_set/E3", "bone": "Fx_Rubi4"}, {"name": "frame_set/E1", "bone": "Fx_Rubi2"}, {"name": "frame_set/E2", "bone": "Fx_Rubi3"}], "transform": [{"scaleMix": 0, "bones": ["3D_B"], "rotateMix": 0, "name": "3D_B", "y": -200, "translateMix": -0.25, "target": "3D", "shearMix": 0}, {"scaleMix": 0, "bones": ["Beard16"], "rotation": 142.52, "rotateMix": 0, "name": "3D_Br_T", "x": -4.41, "y": -6.36, "translateMix": -0.05, "shearY": -360, "order": 7, "target": "3D_T", "shearMix": 0}, {"scaleMix": 0, "bones": ["Beard40"], "rotation": 101.31, "rotateMix": 0, "name": "3D_Br_T2", "x": -3.98, "y": -4.2, "translateMix": -0.04, "shearY": -360, "order": 8, "target": "3D_T", "shearMix": 0}, {"scaleMix": 0, "bones": ["Beard43"], "rotation": 77.95, "rotateMix": 0, "name": "3D_Br_T3", "x": 4.89, "y": -4.42, "translateMix": -0.04, "order": 15, "target": "3D_T", "shearMix": 0}, {"scaleMix": 0, "bones": ["Beard46"], "rotation": 44.33, "rotateMix": 0, "name": "3D_Br_T4", "x": 6.25, "y": -7.01, "translateMix": -0.05, "order": 16, "target": "3D_T", "shearMix": 0}, {"scaleMix": 0, "bones": ["Eyebrow"], "rotation": 144.57, "rotateMix": 0, "name": "3D_EyeBrowL", "x": -7.68, "y": -18.26, "translateMix": 0.12, "shearY": -360, "order": 2, "target": "3D_T", "shearMix": 0}, {"scaleMix": 0, "bones": ["Eyebrow4"], "rotation": 37.48, "rotateMix": 0, "name": "3D_EyeBrowR", "x": 8.32, "y": -18, "translateMix": 0.12, "order": 3, "target": "3D_T", "shearMix": 0}, {"scaleMix": 0, "bones": ["Eye"], "rotateMix": 0, "name": "3D_EyeL", "x": -16.16, "y": -17.28, "translateMix": 0.12, "order": 5, "target": "3D_T", "shearMix": 0}, {"scaleMix": 0, "bones": ["Eye2"], "rotateMix": 0, "name": "3D_EyeR", "x": 19.17, "y": -17.28, "translateMix": 0.12, "order": 4, "target": "3D_T", "shearMix": 0}, {"scaleMix": 0, "bones": ["Tong_MouthL"], "rotateMix": 0, "name": "3D_MouthL", "x": -13.42, "y": 19.24, "translateMix": -0.05, "order": 20, "target": "3D_B", "shearMix": 0}, {"scaleMix": 0, "bones": ["Tong_MouthR"], "rotateMix": 0, "name": "3D_MouthR", "x": 11.85, "y": 19.24, "translateMix": -0.05, "order": 21, "target": "3D_B", "shearMix": 0}, {"scaleMix": 0, "bones": ["Bread_Mouth11"], "rotateMix": 0, "name": "3D_Nose", "x": 1.02, "y": -33.49, "translateMix": 0.08, "order": 6, "target": "3D_T", "shearMix": 0}, {"scaleMix": 0, "bones": ["Beard71"], "rotation": -15.94, "rotateMix": 0, "name": "3D_Rau1", "x": 4.7, "y": -38.43, "translateMix": 0.13, "order": 17, "target": "3D_T", "shearMix": 0}, {"scaleMix": 0, "bones": ["Beard2"], "rotation": -160.27, "rotateMix": 0, "name": "3D_Rau2", "x": -6.71, "y": -37.44, "translateMix": 0.13, "order": 18, "target": "3D_T", "shearMix": 0}, {"scaleMix": 0, "bones": ["Bread_Mouth3"], "rotation": -85.76, "rotateMix": 0, "name": "3D_RauB1", "x": -12, "y": 5.58, "translateMix": -0.12, "order": 11, "target": "3D_B", "shearMix": 0}, {"scaleMix": 0, "bones": ["Bread_Mouth15"], "rotation": -89.2, "rotateMix": 0, "name": "3D_RauB2", "x": -2.67, "y": 1, "translateMix": -0.15, "order": 12, "target": "3D_B", "shearMix": 0}, {"scaleMix": 0, "rotation": -84.91, "rotateMix": 0, "target": "3D_B", "shearMix": 0, "scaleX": -2, "bones": ["Bread_Mouth10"], "name": "3D_RauB3", "x": 2.65, "y": 1, "translateMix": -0.15, "shearY": -180, "order": 13}, {"scaleMix": 0, "rotation": -86.07, "rotateMix": 0, "target": "3D_B", "shearMix": 0, "scaleX": -2, "bones": ["Bread_Mouth14"], "name": "3D_RauB4", "x": 10.06, "y": 5.58, "translateMix": -0.12, "shearY": -180, "order": 14}, {"scaleMix": 0, "bones": ["Beard15"], "rotateMix": 0, "name": "3D_RauL2", "x": -19.48, "y": -29.06, "translateMix": -0.05, "order": 22, "target": "3D_T", "shearMix": 0}, {"scaleMix": 0, "bones": ["Beard49"], "rotation": 179.05, "rotateMix": 0, "name": "3D_RauR2", "x": 19.27, "y": -29.06, "translateMix": -0.05, "shearY": -360, "order": 23, "target": "3D_T", "shearMix": 0}, {"scaleMix": 0, "bones": ["<PERSON>"], "rotation": 165.11, "rotateMix": 0, "name": "3D_RiaL", "x": -18.77, "y": 50.78, "translateMix": -0.1, "shearY": -360, "order": 10, "target": "3D_B", "shearMix": 0}, {"scaleMix": 0, "bones": ["Beard74"], "rotation": 14.2, "rotateMix": 0, "name": "3D_RiaR", "x": 15.13, "y": 50.78, "translateMix": -0.1, "order": 9, "target": "3D_B", "shearMix": 0}, {"scaleMix": 0, "bones": ["Rubi"], "rotateMix": 0, "name": "3D_Rubi", "x": -0.09, "y": 24.21, "translateMix": -0.08, "order": 19, "target": "3D_B", "shearMix": 0}, {"scaleMix": 0, "bones": ["3D_T"], "rotateMix": 0, "name": "3D_T", "y": -115, "translateMix": 0.25, "order": 1, "target": "3D", "shearMix": 0}], "bones": [{"name": "root", "x": -0.54, "y": -38.11}, {"parent": "root", "name": "All", "x": 1, "y": 125.4}, {"scaleX": 1.1, "parent": "All", "scaleY": 1.1, "name": "Dragon"}, {"parent": "Dragon", "color": "abe323ff", "name": "3D", "y": 150}, {"parent": "Dragon", "color": "4a00ffff", "name": "3D_T", "y": 35}, {"parent": "Dragon", "color": "4a00ffff", "name": "3D_B", "y": -50}, {"parent": "Dragon", "color": "ff00c6ff", "rotation": 144.57, "name": "Eyebrow", "length": 10.4, "x": -7.68, "y": 16.74}, {"parent": "Eyebrow", "color": "ff00c6ff", "rotation": -1.16, "name": "Eyebrow2", "length": 13.4, "x": 10.4}, {"parent": "Eyebrow2", "color": "ff00c6ff", "rotation": 13.48, "name": "Eyebrow3", "length": 14.53, "x": 13.4}, {"parent": "Dragon", "name": "Eye", "x": -16.16, "y": 17.72}, {"parent": "Dragon", "color": "ff0000ff", "rotation": -160.27, "name": "Beard2", "length": 12.61, "x": -6.71, "y": -2.44}, {"parent": "Beard2", "color": "ff0000ff", "rotation": 15.58, "name": "Beard3", "length": 15.18, "x": 12.49, "y": 0.04}, {"parent": "Beard3", "color": "ff0000ff", "rotation": -16.31, "name": "Beard4", "length": 16.64, "x": 15.18}, {"parent": "Dragon", "color": "ff4509ff", "rotation": 165.11, "name": "<PERSON>", "length": 10.54, "x": -18.77, "y": 0.78}, {"parent": "<PERSON>", "color": "ff4509ff", "rotation": 29.54, "name": "Beard5", "length": 8.67, "x": 10.54}, {"parent": "Beard5", "color": "ff4509ff", "rotation": 54.01, "name": "Beard6", "length": 8.86, "x": 8.67}, {"parent": "Beard6", "color": "ff4509ff", "rotation": 31.03, "name": "Beard7", "length": 10.73, "x": 8.86}, {"parent": "Beard7", "color": "ff4509ff", "rotation": 2.78, "name": "Beard8", "length": 12.55, "x": 10.73}, {"parent": "Beard8", "color": "ff4509ff", "rotation": -10.1, "name": "Beard9", "length": 12.52, "x": 12.55}, {"parent": "Beard9", "color": "ff4509ff", "rotation": -43.33, "name": "Beard10", "length": 6.49, "x": 12.52}, {"parent": "Beard10", "color": "ff4509ff", "rotation": -65.06, "name": "Beard11", "length": 11.68, "x": 6.49}, {"parent": "Beard11", "color": "ff4509ff", "rotation": -28.17, "name": "Beard12", "length": 13.14, "x": 11.68}, {"parent": "Beard12", "color": "ff4509ff", "rotation": -14.27, "name": "Beard13", "length": 11.35, "x": 13.22, "y": -0.27}, {"parent": "Beard13", "color": "ff4509ff", "rotation": 4.51, "name": "Beard14", "length": 10.53, "x": 11.35}, {"parent": "Dragon", "color": "2bff00ff", "name": "Beard15", "x": -19.48, "y": 5.94}, {"parent": "Beard15", "color": "2bff00ff", "rotation": 137.68, "name": "Beard17", "length": 16.67, "x": 1.17, "y": 17.03}, {"parent": "Beard17", "color": "2bff00ff", "rotation": -26.14, "name": "Beard18", "length": 15.03, "x": 16.67}, {"parent": "Beard18", "color": "2bff00ff", "rotation": -45.57, "name": "Beard19", "length": 16.72, "x": 15.03}, {"parent": "Beard15", "color": "2bff00ff", "rotation": 148.3, "name": "Beard20", "length": 14.71, "x": -1.22, "y": 7.83}, {"parent": "Beard20", "color": "2bff00ff", "rotation": -13.3, "name": "Beard21", "length": 17.17, "x": 14.71}, {"parent": "Beard21", "color": "2bff00ff", "rotation": -33.86, "name": "Beard22", "length": 12.38, "x": 17.17}, {"parent": "Beard22", "color": "2bff00ff", "rotation": -11.14, "name": "Beard23", "length": 12.51, "x": 12.05, "y": -0.69}, {"parent": "Beard15", "color": "2bff00ff", "rotation": 142.85, "name": "Beard24", "length": 15.24, "x": -1.04, "y": -7.44}, {"parent": "Beard24", "color": "2bff00ff", "rotation": 4.68, "name": "Beard25", "length": 14.39, "x": 15.24}, {"parent": "Beard25", "color": "2bff00ff", "rotation": -18.4, "name": "Beard26", "length": 13.99, "x": 14.24, "y": -0.1}, {"parent": "Beard26", "color": "2bff00ff", "rotation": -19.33, "name": "Beard27", "length": 14.67, "x": 13.99}, {"parent": "Beard15", "color": "2bff00ff", "rotation": 178.18, "name": "Beard28", "length": 11.6, "x": -0.48, "y": -5.97}, {"parent": "Beard28", "color": "2bff00ff", "rotation": -23.62, "name": "Beard29", "length": 16.71, "x": 11.6}, {"parent": "Beard29", "color": "2bff00ff", "rotation": -38.26, "name": "Beard30", "length": 17.86, "x": 16.55, "y": 0.33}, {"parent": "Beard15", "color": "2bff00ff", "rotation": -154.11, "name": "Beard31", "length": 13.91, "x": 1.36, "y": -5.6}, {"parent": "Beard31", "color": "2bff00ff", "rotation": -31.26, "name": "Beard32", "length": 15.71, "x": 13.91}, {"parent": "Beard32", "color": "2bff00ff", "rotation": -37.13, "name": "Beard33", "length": 11.98, "x": 15.71}, {"parent": "Beard33", "color": "2bff00ff", "rotation": -6.97, "name": "Beard34", "length": 13.31, "x": 11.98}, {"parent": "Beard15", "color": "2bff00ff", "rotation": 151.11, "name": "Beard35", "length": 12.19, "x": 0.62, "y": -22.9}, {"parent": "Beard35", "color": "2bff00ff", "rotation": 37.11, "name": "Beard36", "length": 15.43, "x": 12.19}, {"parent": "Beard36", "color": "2bff00ff", "rotation": -26.46, "name": "Beard37", "length": 16.47, "x": 15.43}, {"parent": "Dragon", "name": "Rubi", "x": -0.09, "y": -25.79}, {"parent": "Dragon", "color": "0159ffff", "rotation": 142.52, "name": "Beard16", "length": 16.33, "x": -4.41, "y": 28.64}, {"parent": "Beard16", "color": "0159ffff", "rotation": -30.1, "name": "Beard38", "length": 14.72, "x": 16.33}, {"parent": "Beard38", "color": "0159ffff", "rotation": -42.41, "name": "Beard39", "length": 15.17, "x": 14.72}, {"parent": "Dragon", "color": "0159ffff", "rotation": 101.31, "name": "Beard40", "length": 12.12, "x": -3.98, "y": 30.8}, {"parent": "Beard40", "color": "0159ffff", "rotation": 5.99, "name": "Beard41", "length": 13.8, "x": 12.12}, {"parent": "Beard41", "color": "0159ffff", "rotation": -40.5, "name": "Beard42", "length": 14.81, "x": 14.01, "y": -0.06}, {"parent": "Dragon", "name": "Tong_MouthL", "x": -13.42, "y": -30.76}, {"parent": "Tong_MouthL", "color": "ff0000ff", "rotation": -110.71, "name": "Bread_Mouth5", "length": 15.43, "x": -2.45, "y": 18.55}, {"parent": "Bread_Mouth5", "color": "ff0000ff", "rotation": -34.02, "name": "Bread_Mouth6", "length": 8.84, "x": 15.43}, {"parent": "Bread_Mouth6", "color": "ff0000ff", "rotation": -30.16, "name": "Bread_Mouth7", "length": 11.84, "x": 8.84}, {"parent": "Tong_MouthL", "color": "ff0000ff", "rotation": -122.24, "name": "Bread_Mouth4", "length": 13.53, "x": -4.07, "y": 2.29}, {"parent": "Tong_MouthL", "color": "ff0000ff", "rotation": -101.48, "name": "Bread_Mouth", "length": 11.49, "x": -2.45, "y": -0.46}, {"parent": "Dragon", "color": "ff0000ff", "rotation": -85.76, "name": "Bread_Mouth3", "length": 9.53, "x": -12, "y": -44.42}, {"parent": "Dragon", "name": "Tong_MouthR", "x": 11.85, "y": -30.76}, {"scaleX": -1, "parent": "Dragon", "color": "ff0000ff", "rotation": 95.09, "name": "Bread_Mouth10", "length": 12.67, "x": 2.65, "y": -49}, {"parent": "Dragon", "name": "Bread_Mouth11", "x": 1.02, "y": 1.51}, {"parent": "Bread_Mouth11", "rotation": -89.2, "name": "<PERSON>", "x": -9.51, "y": -8.98}, {"parent": "Dragon", "color": "0159ffff", "rotation": 77.95, "name": "Beard43", "length": 12.12, "x": 4.89, "y": 30.58}, {"parent": "Beard43", "color": "0159ffff", "rotation": -9.23, "name": "Beard44", "length": 13.8, "x": 12.12}, {"parent": "Beard44", "color": "0159ffff", "rotation": 45.1, "name": "Beard45", "length": 14.81, "x": 14.01, "y": -0.06}, {"parent": "Dragon", "color": "0159ffff", "rotation": 44.33, "name": "Beard46", "length": 16.33, "x": 6.25, "y": 27.99}, {"parent": "Beard46", "color": "0159ffff", "rotation": 24.79, "name": "Beard47", "length": 14.72, "x": 16.33}, {"parent": "Beard47", "color": "0159ffff", "rotation": 46.24, "name": "Beard48", "length": 15.17, "x": 14.72}, {"parent": "Dragon", "color": "2bff00ff", "rotation": 179.05, "name": "Beard49", "x": 19.27, "y": 5.94}, {"parent": "Beard49", "color": "2bff00ff", "rotation": -157.73, "name": "Beard50", "length": 16.67, "x": 1.81, "y": 23.79}, {"parent": "Beard50", "color": "2bff00ff", "rotation": -30.45, "name": "Beard51", "length": 15.03, "x": 16.67}, {"parent": "Beard51", "color": "2bff00ff", "rotation": 35.45, "name": "Beard52", "length": 16.72, "x": 15.03}, {"parent": "Beard49", "color": "2bff00ff", "rotation": 166.46, "name": "Beard53", "length": 14.71, "x": -1.22, "y": 7.83}, {"parent": "Beard53", "color": "2bff00ff", "rotation": 24, "name": "Beard54", "length": 17.17, "x": 14.71}, {"parent": "Beard54", "color": "2bff00ff", "rotation": 52.02, "name": "Beard55", "length": 12.38, "x": 17.17}, {"parent": "Beard55", "color": "2bff00ff", "rotation": -16.75, "name": "Beard56", "length": 12.51, "x": 12.05, "y": -0.69}, {"parent": "Beard49", "color": "2bff00ff", "rotation": -156.72, "name": "Beard57", "length": 15.24, "x": -0.96, "y": 2.81}, {"parent": "Beard57", "color": "2bff00ff", "rotation": 7.03, "name": "Beard58", "length": 14.39, "x": 15.24}, {"parent": "Beard58", "color": "2bff00ff", "rotation": 32.49, "name": "Beard59", "length": 13.99, "x": 14.24, "y": -0.1}, {"parent": "Beard59", "color": "2bff00ff", "rotation": 5.83, "name": "Beard60", "length": 14.67, "x": 13.99}, {"parent": "Beard49", "color": "2bff00ff", "rotation": 175.52, "name": "Beard61", "length": 11.6, "x": 0.11, "y": 3.54}, {"parent": "Beard61", "color": "2bff00ff", "rotation": 21.28, "name": "Beard62", "length": 16.71, "x": 11.6}, {"parent": "Beard62", "color": "2bff00ff", "rotation": 54.57, "name": "Beard63", "length": 17.86, "x": 16.55, "y": 0.33}, {"parent": "Beard49", "color": "2bff00ff", "rotation": -143.44, "name": "Beard64", "length": 13.91, "x": 1.36, "y": -5.6}, {"parent": "Beard64", "color": "2bff00ff", "rotation": 1.71, "name": "Beard65", "length": 15.71, "x": 13.91}, {"parent": "Beard65", "color": "2bff00ff", "rotation": 26.8, "name": "Beard66", "length": 11.98, "x": 15.71}, {"parent": "Beard66", "color": "2bff00ff", "rotation": 30.98, "name": "Beard67", "length": 13.31, "x": 11.98}, {"parent": "Beard49", "color": "2bff00ff", "rotation": -150.58, "name": "Beard68", "length": 12.19, "x": 0.62, "y": -22.9}, {"parent": "Beard68", "color": "2bff00ff", "rotation": 37.11, "name": "Beard69", "length": 15.43, "x": 12.19}, {"parent": "Beard69", "color": "2bff00ff", "rotation": 54.57, "name": "Beard70", "length": 16.47, "x": 15.43}, {"parent": "Dragon", "name": "Eye2", "x": 19.17, "y": 17.72}, {"parent": "Dragon", "color": "ff00c6ff", "rotation": 37.48, "name": "Eyebrow4", "length": 10.4, "x": 8.32, "y": 17}, {"parent": "Eyebrow4", "color": "ff00c6ff", "rotation": -12.42, "name": "Eyebrow5", "length": 13.4, "x": 10.4}, {"parent": "Eyebrow5", "color": "ff00c6ff", "rotation": -8.71, "name": "Eyebrow6", "length": 14.53, "x": 13.4}, {"parent": "Bread_Mouth11", "rotation": -89.2, "name": "Fang2", "x": 7.66, "y": -8.98}, {"parent": "Dragon", "color": "ff0000ff", "rotation": -15.94, "name": "Beard71", "length": 12.61, "x": 4.7, "y": -3.43}, {"parent": "Beard71", "color": "ff0000ff", "rotation": -12.73, "name": "Beard72", "length": 15.18, "x": 13.06, "y": 0.11}, {"parent": "Beard72", "color": "ff0000ff", "rotation": 12.25, "name": "Beard73", "length": 16.64, "x": 16.32, "y": -0.07}, {"parent": "Tong_MouthR", "color": "ff0000ff", "rotation": -71.8, "name": "Bread_Mouth8", "length": 15.43, "x": 3.01, "y": 18.55}, {"parent": "Bread_Mouth8", "color": "ff0000ff", "rotation": 32.15, "name": "Bread_Mouth9", "length": 8.84, "x": 15.43}, {"parent": "Bread_Mouth9", "color": "ff0000ff", "rotation": 37.63, "name": "Bread_Mouth12", "length": 11.84, "x": 8.84}, {"scaleX": -1, "parent": "Tong_MouthR", "color": "ff0000ff", "rotation": 117.77, "name": "Bread_Mouth13", "length": 13.53, "x": 2.38, "y": 2.68}, {"scaleX": -1, "parent": "Tong_MouthR", "color": "ff0000ff", "rotation": 105.52, "name": "Bread_Mouth2", "length": 11.49, "x": 1.24, "y": -0.46}, {"scaleX": -1, "parent": "Dragon", "color": "ff0000ff", "rotation": 93.93, "name": "Bread_Mouth14", "length": 9.53, "x": 10.06, "y": -44.42}, {"parent": "Dragon", "color": "ff0000ff", "rotation": -89.2, "name": "Bread_Mouth15", "length": 12.67, "x": -2.67, "y": -49}, {"parent": "Dragon", "color": "1a00ffff", "name": "Mouth", "x": -20.1, "y": -10.78}, {"parent": "Dragon", "color": "1a00ffff", "name": "Mouth2", "x": 17.42, "y": -10.14}, {"parent": "Dragon", "color": "00ff0aff", "name": "Mouth_B", "y": -55}, {"parent": "Dragon", "color": "ff4509ff", "rotation": 14.2, "name": "Beard74", "length": 10.54, "x": 15.13, "y": 0.78}, {"parent": "Beard74", "color": "ff4509ff", "rotation": -23.55, "name": "Beard75", "length": 8.67, "x": 10.54}, {"parent": "Beard75", "color": "ff4509ff", "rotation": -55.78, "name": "Beard76", "length": 8.86, "x": 8.67}, {"parent": "Beard76", "color": "ff4509ff", "rotation": -36.39, "name": "Beard77", "length": 10.73, "x": 8.86}, {"parent": "Beard77", "color": "ff4509ff", "rotation": -0.89, "name": "Beard78", "length": 12.55, "x": 10.73}, {"parent": "Beard78", "color": "ff4509ff", "rotation": 7.22, "name": "Beard79", "length": 12.52, "x": 12.55}, {"parent": "Beard79", "color": "ff4509ff", "rotation": 38.43, "name": "Beard80", "length": 6.49, "x": 12.52}, {"parent": "Beard80", "color": "ff4509ff", "rotation": 63.68, "name": "Beard81", "length": 11.68, "x": 6.49}, {"parent": "Beard81", "color": "ff4509ff", "rotation": 40.76, "name": "Beard82", "length": 13.14, "x": 11.68}, {"parent": "Beard82", "color": "ff4509ff", "rotation": 9.64, "name": "Beard83", "length": 11.35, "x": 13.22, "y": -0.27}, {"parent": "Beard83", "color": "ff4509ff", "rotation": 0.08, "name": "Beard84", "length": 10.53, "x": 11.35}, {"parent": "Rubi", "name": "Fx_Rubi", "x": 0.09, "y": 0.29}, {"parent": "Eye", "name": "Fx_Rubi2", "x": -0.16, "y": 0.3}, {"scaleX": -1, "parent": "Eye2", "name": "Fx_Rubi3", "x": 0.55, "y": -0.25}, {"parent": "Rubi", "rotation": 90.66, "name": "Fx_Rubi4", "x": 0.09, "y": 0.29}, {"parent": "root", "rotation": -3.98, "name": "c1", "x": 60.41, "y": 168.65}, {"parent": "root", "name": "c2", "x": -83.41, "y": 150.7}, {"parent": "root", "name": "c3", "x": -5.11, "y": 162.3}, {"parent": "root", "name": "bg1", "x": -0.21, "y": -65.27}, {"parent": "root", "name": "bg2", "x": -0.21, "y": -65.27}, {"parent": "root", "name": "images/v1", "x": -206.06, "y": 74.37}, {"parent": "root", "name": "images/v2", "x": -131.7, "y": 71.51}, {"parent": "root", "name": "images/v3", "x": -163.16, "y": 105.83}, {"parent": "root", "name": "images/v5", "x": 140, "y": 71.51}, {"parent": "root", "name": "images/v6", "x": 170.03, "y": 107.97}, {"parent": "bg1", "name": "images/v4", "x": 209.56, "y": 136.78}, {"parent": "root", "color": "0dff00ff", "name": "images/bg_vang", "x": 0.57, "y": 79.11}, {"parent": "root", "color": "0dff00ff", "name": "images/bg_vang2", "x": 0.57, "y": 79.11}], "animations": {"animation": {"slots": {"frame_set/E3": {"color": [{"c3": 0.75, "color": "ffffff00", "curve": 0.25}, {"color": "ffffffff", "curve": "stepped", "time": 0.1333}, {"c3": 0.75, "color": "ffffffff", "curve": 0.25, "time": 1.8}, {"color": "ffffff00", "time": 2}], "attachment": [{"name": "frame_set/E0", "time": 0.0333}, {"name": null, "time": 0.0667}, {"name": "frame_set/E2", "time": 0.1667}, {"name": null, "time": 0.2333}, {"name": "frame_set/E4", "time": 0.3333}, {"name": null, "time": 0.3667}, {"name": "frame_set/E6", "time": 0.4667}, {"name": null, "time": 0.5}, {"name": "frame_set/E8", "time": 0.6333}, {"name": null, "time": 0.6667}, {"name": "frame_set/E0", "time": 0.7}, {"name": null, "time": 0.7333}, {"name": "frame_set/E2", "time": 0.8333}, {"name": null, "time": 0.8667}, {"name": "frame_set/E4", "time": 1}, {"name": null, "time": 1.0333}, {"name": "frame_set/E6", "time": 1.1333}, {"name": null, "time": 1.1667}, {"name": "frame_set/E8", "time": 1.2667}, {"name": null, "time": 1.3}, {"name": "frame_set/E0", "time": 1.3667}, {"name": null, "time": 1.4}, {"name": "frame_set/E2", "time": 1.5}, {"name": null, "time": 1.5333}, {"name": "frame_set/E4", "time": 1.6333}, {"name": null, "time": 1.6667}, {"name": "frame_set/E6", "time": 1.8}, {"name": null, "time": 1.8333}, {"name": "frame_set/E8", "time": 1.9333}, {"name": null, "time": 1.9667}]}, "frame_set/E2": {"color": [{"c3": 0.75, "color": "ffffff00", "curve": 0.25}, {"color": "ffffffff", "curve": "stepped", "time": 0.1333}, {"c3": 0.75, "color": "ffffffff", "curve": 0.25, "time": 1.8}, {"color": "ffffff00", "time": 2}], "attachment": [{"name": "frame_set/E2", "time": 0.0333}, {"name": null, "time": 0.0667}, {"name": "frame_set/E4", "time": 0.1667}, {"name": null, "time": 0.2333}, {"name": "frame_set/E6", "time": 0.3333}, {"name": null, "time": 0.3667}, {"name": "frame_set/E8", "time": 0.4667}, {"name": null, "time": 0.5}, {"name": "frame_set/E0", "time": 0.5333}, {"name": null, "time": 0.6}, {"name": "frame_set/E2", "time": 0.7}, {"name": null, "time": 0.7333}, {"name": "frame_set/E4", "time": 0.8333}, {"name": null, "time": 0.8667}, {"name": "frame_set/E6", "time": 1}, {"name": null, "time": 1.0333}, {"name": "frame_set/E8", "time": 1.1333}, {"name": null, "time": 1.1667}, {"name": "frame_set/E0", "time": 1.2}, {"name": null, "time": 1.2333}, {"name": "frame_set/E2", "time": 1.3667}, {"name": null, "time": 1.4}, {"name": "frame_set/E4", "time": 1.5}, {"name": null, "time": 1.5333}, {"name": "frame_set/E6", "time": 1.6333}, {"name": null, "time": 1.6667}, {"name": "frame_set/E8", "time": 1.8}, {"name": null, "time": 1.8333}, {"name": "frame_set/E0", "time": 1.8667}, {"name": null, "time": 1.9}]}, "frame_set/E1": {"color": [{"c3": 0.75, "color": "ffffff00", "curve": 0.25}, {"color": "ffffffff", "curve": "stepped", "time": 0.1333}, {"c3": 0.75, "color": "ffffffff", "curve": 0.25, "time": 1.8}, {"color": "ffffff00", "time": 2}], "attachment": [{"name": "frame_set/E0", "time": 0.0333}, {"name": null, "time": 0.0667}, {"name": "frame_set/E2", "time": 0.1667}, {"name": null, "time": 0.2333}, {"name": "frame_set/E4", "time": 0.3333}, {"name": null, "time": 0.3667}, {"name": "frame_set/E6", "time": 0.4667}, {"name": null, "time": 0.5}, {"name": "frame_set/E8", "time": 0.6333}, {"name": null, "time": 0.6667}, {"name": "frame_set/E0", "time": 0.7}, {"name": null, "time": 0.7333}, {"name": "frame_set/E2", "time": 0.8333}, {"name": null, "time": 0.8667}, {"name": "frame_set/E4", "time": 1}, {"name": null, "time": 1.0333}, {"name": "frame_set/E6", "time": 1.1333}, {"name": null, "time": 1.1667}, {"name": "frame_set/E8", "time": 1.2667}, {"name": null, "time": 1.3}, {"name": "frame_set/E0", "time": 1.3667}, {"name": null, "time": 1.4}, {"name": "frame_set/E2", "time": 1.5}, {"name": null, "time": 1.5333}, {"name": "frame_set/E4", "time": 1.6333}, {"name": null, "time": 1.6667}, {"name": "frame_set/E6", "time": 1.8}, {"name": null, "time": 1.8333}, {"name": "frame_set/E8", "time": 1.9333}, {"name": null, "time": 1.9667}]}, "frame_set/E0": {"color": [{"c3": 0.75, "color": "ffffff00", "curve": 0.25}, {"color": "ffffffff", "curve": "stepped", "time": 0.1333}, {"c3": 0.75, "color": "ffffffff", "curve": 0.25, "time": 1.8}, {"color": "ffffff00", "time": 2}], "attachment": [{"name": "frame_set/E0", "time": 0.0667}, {"name": null, "time": 0.1}, {"name": "frame_set/E2", "time": 0.2333}, {"name": null, "time": 0.2667}, {"name": "frame_set/E4", "time": 0.3667}, {"name": null, "time": 0.4}, {"name": "frame_set/E6", "time": 0.5}, {"name": null, "time": 0.5333}, {"name": "frame_set/E8", "time": 0.6667}, {"name": null, "time": 0.7}, {"name": "frame_set/E0", "time": 0.7333}, {"name": null, "time": 0.7667}, {"name": "frame_set/E2", "time": 0.8667}, {"name": null, "time": 0.9}, {"name": "frame_set/E4", "time": 1.0333}, {"name": null, "time": 1.0667}, {"name": "frame_set/E6", "time": 1.1667}, {"name": null, "time": 1.2}, {"name": "frame_set/E8", "time": 1.3}, {"name": null, "time": 1.3667}, {"name": "frame_set/E0", "time": 1.4}, {"name": null, "time": 1.4333}, {"name": "frame_set/E2", "time": 1.5333}, {"name": null, "time": 1.5667}, {"name": "frame_set/E4", "time": 1.6667}, {"name": null, "time": 1.7}, {"name": "frame_set/E6", "time": 1.8333}, {"name": null, "time": 1.8667}, {"name": "frame_set/E8", "time": 1.9667}, {"name": null, "time": 2}]}, "images/bg_vang2": {"color": [{"color": "ffffff00"}, {"color": "ffffff7b", "time": 0.6667}, {"color": "ffffff00", "time": 0.8333}, {"color": "ffffffd2", "time": 1.1667}]}, "bg2": {"color": [{"c3": 0.742, "color": "ffffff1b", "curve": 0.381, "c2": 0.55}, {"c3": 0.75, "color": "ffffff00", "curve": 0.25, "time": 0.2}, {"c3": 0.75, "color": "ffffff4b", "curve": 0.25, "time": 0.7}, {"c3": 0.75, "color": "ffffff00", "curve": 0.25, "time": 1.2}, {"c3": 0.637, "c4": 0.56, "color": "ffffff4b", "curve": 0.245, "time": 1.7}, {"color": "ffffff1b", "time": 2}]}, "c1": {"color": [{"color": "ffffffff", "time": 0.6}, {"color": "ffffff00", "curve": "stepped", "time": 0.7667}, {"color": "ffffff00", "time": 0.8}, {"color": "ffffffff", "time": 0.9667}]}, "c2": {"color": [{"color": "ffffff00"}, {"color": "ffffffff", "curve": "stepped", "time": 0.1667}, {"color": "ffffffff", "time": 1.8}, {"color": "ffffff00", "time": 1.9667}]}}, "bones": {"Beard20": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -7.06, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -8.23, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 0.75, "time": 1.2}, {"angle": -7.06, "time": 2}]}, "Beard21": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -3.72, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -8.77, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 4.96, "time": 1.4}, {"angle": -3.72, "time": 2}]}, "Eye": {"rotate": [{"c3": 0.75, "curve": 0.25, "time": 0.3667}, {"c3": 0.75, "curve": 0.25, "angle": -18.12, "time": 0.7333}, {"c3": 0.75, "curve": 0.25, "angle": -15.3, "time": 1.6333}, {"time": 2}], "scale": [{"c3": 0.75, "curve": 0.25}, {"c3": 0.75, "curve": 0.25, "x": 1.2, "y": 1.2, "time": 0.3667}, {"c3": 0.75, "curve": 0.25, "x": 0.85, "y": 0.85, "time": 0.7333}, {"c3": 0.75, "curve": 0.25, "x": 0.85, "y": 0.637, "time": 1.6333}, {"time": 2}], "translate": [{"c3": 0.75, "curve": 0.25, "time": 0.3667}, {"c3": 0.75, "curve": 0.25, "x": 0.54, "y": -1.99, "time": 0.7333}, {"c3": 0.75, "curve": 0.25, "x": -0.91, "y": -2.49, "time": 1.6333}, {"time": 2}]}, "Beard22": {"rotate": [{"c3": 0.755, "curve": 0.363, "angle": -9.43, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "angle": -13.91, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "angle": -6.83, "time": 1.6}, {"angle": -9.43, "time": 2}]}, "Bread_Mouth9": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": 5.69, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": 6.88, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": -2.3, "time": 1.2}, {"angle": 5.69, "time": 2}]}, "Beard23": {"rotate": [{"c3": 0.757, "curve": 0.32, "angle": 15.16, "c2": 0.29}, {"c3": 0.75, "curve": 0.25, "angle": -7.39, "time": 0.8}, {"c3": 0.625, "c4": 0.38, "curve": 0.284, "angle": 18.53, "time": 1.8}, {"angle": 15.16, "time": 2}]}, "Bread_Mouth8": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": 9.64}, {"c3": 0.75, "curve": 0.25, "angle": 3.4, "time": 1}, {"angle": 9.64, "time": 2}]}, "Beard24": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -4.82, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -5.62, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 0.53, "time": 1.2}, {"angle": -4.82, "time": 2}]}, "Beard25": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -2.66, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -5.43, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 2.1, "time": 1.4}, {"angle": -2.66, "time": 2}]}, "Beard26": {"rotate": [{"c3": 0.755, "curve": 0.363, "angle": -1.62, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "angle": -8.54, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "angle": 2.42, "time": 1.6}, {"angle": -1.62, "time": 2}]}, "Beard27": {"rotate": [{"c3": 0.757, "curve": 0.32, "angle": 2.46, "c2": 0.29}, {"c3": 0.75, "curve": 0.25, "angle": -19.59, "time": 0.8}, {"c3": 0.625, "c4": 0.38, "curve": 0.284, "angle": 5.75, "time": 1.8}, {"angle": 2.46, "time": 2}]}, "Beard28": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -4.19, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -4.98, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 1.11, "time": 1.2}, {"angle": -4.19, "time": 2}]}, "Beard29": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -3.9, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -7.67, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 2.58, "time": 1.4}, {"angle": -3.9, "time": 2}]}, "Bread_Mouth2": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": 9.47}, {"c3": 0.75, "curve": 0.25, "angle": 2.51, "time": 1}, {"angle": 9.47, "time": 2}]}, "Bread_Mouth5": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -6.33}, {"c3": 0.75, "curve": 0.25, "angle": 0.57, "time": 1}, {"angle": -6.33, "time": 2}]}, "Bread_Mouth4": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -5.27}, {"c3": 0.75, "curve": 0.25, "angle": 3.39, "time": 1}, {"angle": -5.27, "time": 2}]}, "Bread_Mouth7": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -2.62, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -6.62, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 4.27, "time": 1.4}, {"angle": -2.62, "time": 2}]}, "Bread_Mouth6": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -4.52, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -5.62, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 2.88, "time": 1.2}, {"angle": -4.52, "time": 2}]}, "Bread_Mouth12": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": 4.85, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": 10.45, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": -4.77, "time": 1.4}, {"angle": 4.85, "time": 2}]}, "Beard30": {"rotate": [{"c3": 0.755, "curve": 0.363, "angle": 2.42, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "angle": -6.67, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "angle": 7.71, "time": 1.6}, {"angle": 2.42, "time": 2}]}, "c1": {"rotate": [{"angle": 142.76}, {"curve": "stepped", "time": 0.7667}, {"time": 0.8}, {"angle": 180, "time": 1.8}, {"angle": 142.76, "time": 2}], "translate": [{"y": 36.78}, {"y": -50.71, "time": 0.7667}, {"y": 173.71, "time": 0.8}, {"y": 36.78, "time": 2}]}, "Beard31": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -3.5, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -4.1, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 0.52, "time": 1.2}, {"angle": -3.5, "time": 2}]}, "c2": {"rotate": [{}, {"angle": 180, "time": 1}, {"time": 1.9667}], "translate": [{"y": 173.71}, {"y": -50.71, "time": 1.9667}, {"y": 173.71, "time": 2}]}, "Beard32": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -3.99, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -6.06, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": -0.43, "time": 1.4}, {"angle": -3.99, "time": 2}]}, "Bread_Mouth13": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": 13.37}, {"c3": 0.75, "curve": 0.25, "angle": 5.58, "time": 1}, {"angle": 13.37, "time": 2}]}, "Beard33": {"rotate": [{"c3": 0.755, "curve": 0.363, "angle": -0.99, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "angle": -5.66, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "angle": 1.72, "time": 1.6}, {"angle": -0.99, "time": 2}]}, "Beard34": {"rotate": [{"c3": 0.757, "curve": 0.32, "angle": 4.26, "c2": 0.29}, {"c3": 0.75, "curve": 0.25, "angle": -7.48, "time": 0.8}, {"c3": 0.625, "c4": 0.38, "curve": 0.284, "angle": 6.02, "time": 1.8}, {"angle": 4.26, "time": 2}]}, "Beard35": {"rotate": [{"c3": 0.75, "curve": 0.25}, {"c3": 0.75, "curve": 0.25, "angle": -0.15, "time": 1}, {"angle": -1.98, "time": 2}]}, "Beard36": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -3.3, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -4.17, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 2.5, "time": 1.2}, {"angle": -3.3, "time": 2}]}, "Beard37": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -6.23, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -9.62, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": -0.39, "time": 1.4}, {"angle": -6.23, "time": 2}]}, "Beard38": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -7.78, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -9.4, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 3.05, "time": 1.2}, {"angle": -7.78, "time": 2}]}, "Beard39": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -3.81, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -11.47, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 9.35, "time": 1.4}, {"angle": -3.81, "time": 2}]}, "Beard80": {"rotate": [{"c3": 0.71, "curve": 0.371, "angle": -4.89, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -6.22, "time": 0.1667}, {"c3": 0.689, "c4": 0.75, "curve": 0.243, "angle": 7.51, "time": 1.1667}, {"angle": -4.89, "time": 2}]}, "Beard81": {"rotate": [{"c3": 0.735, "curve": 0.382, "angle": -3.99, "c2": 0.57}, {"c3": 0.75, "curve": 0.25, "angle": -7.71, "time": 0.3333}, {"c3": 0.649, "c4": 0.6, "curve": 0.243, "angle": 5.38, "time": 1.3333}, {"angle": -3.99, "time": 2}]}, "Beard82": {"rotate": [{"c3": 0.757, "curve": 0.351, "angle": 2.9, "c2": 0.4}, {"c3": 0.75, "curve": 0.25, "angle": -5.62, "time": 0.6667}, {"c3": 0.618, "c4": 0.43, "curve": 0.265, "angle": 6.28, "time": 1.6667}, {"angle": 2.9, "time": 2}]}, "Beard83": {"rotate": [{"c3": 0.758, "curve": 0.329, "angle": 9.38, "c2": 0.32}, {"c3": 0.75, "curve": 0.25, "angle": -0.67, "time": 0.7667}, {"c3": 0.622, "c4": 0.39, "curve": 0.278, "angle": 11.38, "time": 1.7667}, {"angle": 9.38, "time": 2}]}, "Beard84": {"rotate": [{"c3": 0.755, "curve": 0.289, "angle": 19.69, "c2": 0.17}, {"c3": 0.75, "curve": 0.25, "angle": 11.83, "time": 0.9}, {"c3": 0.64, "c4": 0.36, "curve": 0.305, "angle": 20.04, "time": 1.9}, {"angle": 19.69, "time": 2}]}, "Eye2": {"rotate": [{"c3": 0.75, "curve": 0.25}, {"c3": 0.75, "curve": 0.25, "angle": 11.7, "time": 0.3667}, {"c3": 0.75, "curve": 0.25, "angle": 14.38, "time": 0.7333}, {"c3": 0.75, "curve": 0.25, "angle": 18.42, "time": 1.6333}, {"time": 2}], "scale": [{"c3": 0.75, "curve": 0.25}, {"c3": 0.75, "curve": 0.25, "x": 1.2, "y": 1.2, "time": 0.3667}, {"c3": 0.75, "curve": 0.25, "x": 0.85, "y": 0.85, "time": 0.7333}, {"c3": 0.75, "curve": 0.25, "x": 0.85, "y": 0.709, "time": 1.6333}, {"time": 2}], "translate": [{"c3": 0.75, "curve": 0.25, "time": 0.3667}, {"c3": 0.75, "curve": 0.25, "y": -1.63, "time": 0.7333}, {"c3": 0.75, "curve": 0.25, "x": -2.75, "y": -1.63, "time": 1.6333}, {"time": 2}]}, "Beard10": {"rotate": [{"c3": 0.752, "curve": 0.264, "angle": 3.77, "c2": 0.06}, {"c3": 0.75, "curve": 0.25, "angle": -6, "time": 0.9667}, {"c3": 0.656, "c4": 0.34, "curve": 0.323, "angle": 3.89, "time": 1.9667}, {"angle": 3.77, "time": 2}]}, "Beard11": {"rotate": [{"c3": 0.722, "curve": 0.378, "angle": 7.24, "c2": 0.61}, {"c3": 0.75, "curve": 0.25, "angle": 9.28, "time": 0.2333}, {"c3": 0.671, "c4": 0.68, "curve": 0.242, "angle": -2.98, "time": 1.2333}, {"angle": 7.24, "time": 2}]}, "3D": {"translate": [{"c4": 0.8, "curve": 0.25}, {"c3": 0.75, "curve": 0, "y": 108.4, "time": 0.3667, "c2": 0.13}, {"c3": 0.75, "curve": 0, "x": -216.88, "time": 0.7333, "c2": 0.13}, {"c3": 0.75, "curve": 0.25, "x": 264.84, "time": 1.6333}, {"time": 2}]}, "Beard12": {"rotate": [{"c3": 0.739, "curve": 0.382, "angle": 5.36, "c2": 0.56}, {"c3": 0.75, "curve": 0.25, "angle": 8.9, "time": 0.3667}, {"c3": 0.643, "c4": 0.58, "curve": 0.244, "angle": -1.98, "time": 1.3667}, {"angle": 5.36, "time": 2}]}, "Beard13": {"rotate": [{"c3": 0.752, "curve": 0.372, "angle": -9.58, "c2": 0.48}, {"c3": 0.75, "curve": 0.25, "angle": -5.11, "time": 0.5333}, {"c3": 0.622, "c4": 0.48, "curve": 0.252, "angle": -13.33, "time": 1.5333}, {"angle": -9.58, "time": 2}]}, "Beard14": {"rotate": [{"c3": 0.758, "curve": 0.329, "angle": -23.37, "c2": 0.32}, {"c3": 0.75, "curve": 0.25, "angle": -36.42, "time": 0.7667}, {"c3": 0.622, "c4": 0.39, "curve": 0.278, "angle": -20.77, "time": 1.7667}, {"angle": -23.37, "time": 2}]}, "Beard8": {"rotate": [{"c3": 0.755, "curve": 0.363, "angle": -2.01, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "angle": -14.35, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "angle": 5.18, "time": 1.6}, {"angle": -2.01, "time": 2}]}, "Beard9": {"rotate": [{"c3": 0.758, "curve": 0.329, "angle": 0.78, "c2": 0.32}, {"c3": 0.75, "curve": 0.25, "angle": -7.64, "time": 0.7667}, {"c3": 0.622, "c4": 0.39, "curve": 0.278, "angle": 2.46, "time": 1.7667}, {"angle": 0.78, "time": 2}]}, "Beard16": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -4.6}, {"c3": 0.75, "curve": 0.25, "angle": 0.21, "time": 1}, {"angle": -4.6, "time": 2}]}, "Beard17": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -8.32}, {"c3": 0.75, "curve": 0.25, "angle": 4.94, "time": 1}, {"angle": -8.32, "time": 2}]}, "Beard18": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -11.38, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -14.82, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 11.64, "time": 1.2}, {"angle": -11.38, "time": 2}]}, "Beard19": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -0.93, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -15.8, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 24.64, "time": 1.4}, {"angle": -0.93, "time": 2}]}, "Beard4": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -2.56, "c2": 0.55}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 0.68, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": -8.13, "time": 1.4}, {"angle": -2.56, "time": 2}]}, "Beard5": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -21.12}, {"c3": 0.75, "curve": 0.25, "angle": -1.17, "time": 1}, {"angle": -21.12, "time": 2}]}, "Beard6": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -9.92, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -11.71, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 2.07, "time": 1.2}, {"angle": -9.92, "time": 2}]}, "Beard7": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -5.78, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -13.44, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 7.39, "time": 1.4}, {"angle": -5.78, "time": 2}]}, "Beard2": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -4.24}, {"c3": 0.75, "curve": 0.25, "angle": 2.69, "time": 1}, {"angle": -4.24, "time": 2}]}, "Beard3": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -2.68, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -3.7, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 4.15, "time": 1.2}, {"angle": -2.68, "time": 2}]}, "Beard60": {"rotate": [{"c3": 0.752, "curve": 0.372, "angle": -7.94, "c2": 0.48}, {"c3": 0.75, "curve": 0.25, "angle": -16.77, "time": 0.5333}, {"c3": 0.622, "c4": 0.48, "curve": 0.252, "angle": -0.53, "time": 1.5333}, {"angle": -7.94, "time": 2}]}, "Beard61": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -4.24, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -5.36, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 3.22, "time": 1.2}, {"angle": -4.24, "time": 2}]}, "Beard62": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -2.61, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -7.42, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 5.64, "time": 1.4}, {"angle": -2.61, "time": 2}]}, "Eyebrow": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -5.85}, {"c3": 0.75, "curve": 0.25, "angle": 1.15, "time": 1}, {"angle": -5.85, "time": 2}]}, "Beard63": {"rotate": [{"c3": 0.755, "curve": 0.363, "angle": -3.82, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "angle": -11.78, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "angle": 0.81, "time": 1.6}, {"angle": -3.82, "time": 2}]}, "Beard64": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -2.81, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -4.56, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 8.89, "time": 1.2}, {"angle": -2.81, "time": 2}]}, "Beard65": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -3.49, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -8.27, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 4.72, "time": 1.4}, {"angle": -3.49, "time": 2}]}, "Beard66": {"rotate": [{"c3": 0.755, "curve": 0.363, "angle": -3.78, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "angle": -11.03, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "angle": 0.44, "time": 1.6}, {"angle": -3.78, "time": 2}]}, "Beard67": {"rotate": [{"c3": 0.757, "curve": 0.32, "angle": -2.52, "c2": 0.29}, {"c3": 0.75, "curve": 0.25, "angle": -12.08, "time": 0.8}, {"c3": 0.625, "c4": 0.38, "curve": 0.284, "angle": -1.09, "time": 1.8}, {"angle": -2.52, "time": 2}]}, "Beard68": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -10.22}, {"c3": 0.75, "curve": 0.25, "angle": -1.98, "time": 1}, {"angle": -10.22, "time": 2}]}, "Beard69": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -9.77, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -11.12, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": -0.78, "time": 1.2}, {"angle": -9.77, "time": 2}]}, "Tong_MouthL": {"scale": [{"x": 0.992, "y": 0.992, "time": 0.3667}]}, "Fx_Rubi3": {"rotate": [{"angle": 19.44}], "scale": [{"x": 0.48, "y": 0.349}], "translate": [{"x": 1.42, "y": -0.47}]}, "Fx_Rubi4": {"rotate": [{"angle": -15.1}]}, "Beard70": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -24.5, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -37.97, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": -1.37, "time": 1.4}, {"angle": -24.5, "time": 2}]}, "Beard71": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": 5.83}, {"c3": 0.75, "curve": 0.25, "angle": 0.24, "time": 1}, {"angle": 5.83, "time": 2}]}, "Beard72": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": 6.43, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": 7.67, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": -1.85, "time": 1.2}, {"angle": 6.43, "time": 2}]}, "Beard73": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": 4.24, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": 9.31, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": -4.47, "time": 1.4}, {"angle": 4.24, "time": 2}]}, "Beard74": {"rotate": [{"angle": 18.98}, {"angle": 1.19, "time": 1}, {"angle": 18.98, "time": 2}]}, "Beard75": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": 6.76, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": 7.92, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": -1.01, "time": 1.2}, {"angle": 6.76, "time": 2}]}, "Fx_Rubi2": {"rotate": [{"angle": 146.76}], "scale": [{"x": 0.48, "y": 0.349}], "translate": [{"x": -2.4, "y": -0.66}]}, "Beard76": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": 5.32, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": 11.7, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": -5.64, "time": 1.4}, {"angle": 5.32, "time": 2}]}, "Beard77": {"rotate": [{"c3": 0.755, "curve": 0.363, "angle": 8.38, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "angle": 13.35, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "angle": 5.49, "time": 1.6}, {"angle": 8.38, "time": 2}]}, "Beard78": {"rotate": [{"c3": 0.757, "curve": 0.32, "angle": -4.36, "c2": 0.29}, {"c3": 0.75, "curve": 0.25, "angle": 9.81, "time": 0.8}, {"c3": 0.625, "c4": 0.38, "curve": 0.284, "angle": -6.48, "time": 1.8}, {"angle": -4.36, "time": 2}]}, "Beard79": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": 0.56}, {"c3": 0.75, "curve": 0.25, "angle": 12.87, "time": 1}, {"angle": 0.56, "time": 2}]}, "images/v4": {"scale": [{"c3": 0.755, "curve": 0.363, "x": 1.095, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "x": 1.15, "time": 1.6}, {"x": 1.095, "time": 2}], "translate": [{"c3": 0.755, "curve": 0.363, "x": 1.81, "y": 2.26, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "x": 2.86, "y": 3.58, "time": 1.6}, {"x": 1.81, "y": 2.26, "time": 2}]}, "Beard": {"rotate": [{"c3": 0.75, "curve": 0.25}, {"c3": 0.75, "curve": 0.25, "angle": -4.86, "time": 0.3667}, {"c3": 0.75, "curve": 0.25, "angle": 1.02, "time": 0.7}, {"c3": 0.75, "curve": 0.25, "angle": -3.25, "time": 1}, {"c3": 0.75, "curve": 0.25, "angle": 1.8, "time": 1.3}, {"c3": 0.75, "curve": 0.25, "angle": -4.25, "time": 1.6333}, {"time": 2}]}, "images/v3": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -5.47, "c2": 0.62}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": -9.17, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 19.21, "time": 1.2}, {"angle": -5.47, "time": 2}], "translate": [{"c3": 0.716, "curve": 0.375, "y": -6.44, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "y": -8.58, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "y": 7.86, "time": 1.2}, {"y": -6.44, "time": 2}]}, "images/v6": {"scale": [{"c3": 0.742, "curve": 0.381, "x": 1.058, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "x": 1.157, "time": 1.4}, {"x": 1.058, "time": 2}], "translate": [{"c3": 0.742, "curve": 0.381, "x": 1.17, "y": 4.52, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "x": 1.43, "y": 7.15, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "x": 0.71, "time": 1.4}, {"x": 1.17, "y": 4.52, "time": 2}]}, "images/v5": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": 21.74, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": 26.51, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": -10.13, "time": 1.2}, {"angle": 21.74, "time": 2}], "scale": [{"c3": 0.716, "curve": 0.375, "y": 1.016, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "y": 1.126, "time": 1.2}, {"y": 1.016, "time": 2}], "translate": [{"c3": 0.716, "curve": 0.375, "y": 0.93, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "y": 7.15, "time": 1.2}, {"y": 0.93, "time": 2}]}, "images/v2": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -10.15, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -25.51, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 16.24, "time": 1.4}, {"angle": -10.15, "time": 2}]}, "images/v1": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": 14.76}, {"c3": 0.75, "curve": 0.25, "angle": -15.63, "time": 1}, {"angle": 14.76, "time": 2}], "scale": [{"c3": 0.75, "curve": 0.25}, {"c3": 0.75, "curve": 0.25, "x": 0.805, "y": 0.805, "time": 1}, {"time": 2}]}, "Beard40": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -7.8}, {"c3": 0.75, "curve": 0.25, "angle": 1.46, "time": 1}, {"angle": -7.8, "time": 2}]}, "Beard41": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -7.1, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -8.67, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 3.38, "time": 1.2}, {"angle": -7.1, "time": 2}]}, "Beard42": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -3.54, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -9.56, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 6.8, "time": 1.4}, {"angle": -3.54, "time": 2}]}, "Beard43": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": 7.21}, {"c3": 0.75, "curve": 0.25, "angle": 0.11, "time": 1}, {"angle": 7.21, "time": 2}]}, "Beard44": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -13.13, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -15.83, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 4.87, "time": 1.2}, {"angle": -13.13, "time": 2}]}, "Beard45": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -3.65, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -10.48, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 8.07, "time": 1.4}, {"angle": -3.65, "time": 2}]}, "Beard46": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -7.57}, {"c3": 0.75, "curve": 0.25, "angle": 4.14, "time": 1}, {"angle": -7.57, "time": 2}]}, "Beard47": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -7.32, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -9.29, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 5.89, "time": 1.2}, {"angle": -7.32, "time": 2}]}, "Beard48": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -7.2, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -12.45, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 1.82, "time": 1.4}, {"angle": -7.2, "time": 2}]}, "Beard50": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -6.14, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -7.14, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 0.49, "time": 1.2}, {"angle": -6.14, "time": 2}]}, "Beard51": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -1.48, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -5.71, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 5.81, "time": 1.4}, {"angle": -1.48, "time": 2}]}, "Beard52": {"rotate": [{"c3": 0.755, "curve": 0.363, "angle": -1.38, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "angle": -11.38, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "angle": 4.44, "time": 1.6}, {"angle": -1.38, "time": 2}]}, "Beard53": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -6.29, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -7.59, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 2.43, "time": 1.2}, {"angle": -6.29, "time": 2}]}, "Beard54": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -4.61, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -7.5, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 0.37, "time": 1.4}, {"angle": -4.61, "time": 2}]}, "Beard55": {"rotate": [{"c3": 0.755, "curve": 0.363, "angle": -1.29, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "angle": -11.86, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "angle": 4.86, "time": 1.6}, {"angle": -1.29, "time": 2}]}, "Beard56": {"rotate": [{"c3": 0.757, "curve": 0.32, "angle": 14.51, "c2": 0.29}, {"c3": 0.75, "curve": 0.25, "angle": -19.39, "time": 0.8}, {"c3": 0.625, "c4": 0.38, "curve": 0.284, "angle": 19.58, "time": 1.8}, {"angle": 14.51, "time": 2}]}, "Beard57": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -6.42, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -7.53, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 1.04, "time": 1.2}, {"angle": -6.42, "time": 2}]}, "Beard58": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -2.17, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -7.68, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 7.31, "time": 1.4}, {"angle": -2.17, "time": 2}]}, "Beard59": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -9.25}, {"c3": 0.75, "curve": 0.25, "angle": 6.6, "time": 1}, {"angle": -7.68, "time": 2}]}, "Bread_Mouth": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -6.3}, {"c3": 0.75, "curve": 0.25, "angle": -0.23, "time": 1}, {"angle": -6.3, "time": 2}]}, "Eyebrow6": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": 4.53, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": 9.93, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": -4.75, "time": 1.4}, {"angle": 4.53, "time": 2}]}, "Eyebrow5": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": 5.51, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": 6.7, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": -2.5, "time": 1.2}, {"angle": 5.51, "time": 2}]}, "Eyebrow4": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": 6.4}, {"c3": 0.75, "curve": 0.25, "angle": -1.14, "time": 1}, {"angle": 6.4, "time": 2}]}, "Eyebrow3": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -4.69, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -10.41, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 5.14, "time": 1.4}, {"angle": -4.69, "time": 2}]}, "Eyebrow2": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -6.17, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -7.41, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 2.15, "time": 1.2}, {"angle": -6.17, "time": 2}]}}}}}