{"skeleton": {"hash": "p3hOli+TnRKFNPtgu1Dum+mBgOU", "spine": "3.8.75", "x": -170.31, "y": -203.35, "width": 332, "height": 412.9}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": 30.91, "y": 96.05}, {"name": "xx2", "parent": "bone", "x": -14.96, "y": -34.46}, {"name": "xx3", "parent": "bone", "x": 25.29, "y": 46.73}, {"name": "bone2", "parent": "root", "x": 14.15, "y": -22.64}, {"name": "xx4", "parent": "bone2", "x": 29.74, "y": 19.41}, {"name": "xx5", "parent": "bone2", "x": -43.56, "y": -22.47}, {"name": "bone3", "parent": "root", "x": -65.12, "y": 29.61}, {"name": "xx1", "parent": "bone3", "x": 15.54, "y": -3.44}, {"name": "xx6", "parent": "bone3", "x": -32.19, "y": 4.77}, {"name": "c2", "parent": "root", "x": 104.01, "y": 70.04}, {"name": "c3", "parent": "root", "x": 78.86, "y": -91.09}, {"name": "c1", "parent": "root", "x": -79.12, "y": -87.95}, {"name": "bat", "parent": "root", "length": 70.85, "rotation": -70.56, "x": -61.83, "y": 161.21}, {"name": "dia", "parent": "root", "length": 59.74, "rotation": 122.64, "x": 60, "y": -54.94}, {"name": "bau", "parent": "root", "x": -54.86, "y": -150.93, "scaleX": 0.88, "scaleY": 0.98}, {"name": "cua", "parent": "root", "x": 70.42, "y": -157.84}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "c1", "bone": "c1", "attachment": "c1"}, {"name": "c2", "bone": "c2", "attachment": "c2"}, {"name": "c3", "bone": "c3", "attachment": "c3"}, {"name": "fx", "bone": "root", "attachment": "fx", "blend": "additive"}, {"name": "bat", "bone": "bat", "attachment": "bat"}, {"name": "dia", "bone": "dia", "attachment": "dia"}, {"name": "xx3", "bone": "xx5", "attachment": "xx3"}, {"name": "xx2", "bone": "xx3", "attachment": "xx2"}, {"name": "xx1", "bone": "xx6", "attachment": "xx1"}, {"name": "cua", "bone": "cua", "attachment": "cua"}, {"name": "bau", "bone": "bau", "attachment": "bau"}], "skins": [{"name": "default", "attachments": {"bg": {"bg": {"x": 2.82, "y": 1.05, "scaleX": 0.7, "scaleY": 0.7, "width": 386, "height": 584}}, "cua": {"cua": {"x": 1.27, "y": 13.39, "width": 124, "height": 86}}, "fx": {"fx": {"x": -4.31, "y": 46.55, "width": 332, "height": 326}}, "bat": {"bat": {"x": 67.97, "y": 7.32, "rotation": 70.56, "width": 146, "height": 154}}, "bau": {"bau": {"x": -6.45, "y": 8.48, "width": 142, "height": 82}}, "c1": {"c1": {"x": -0.69, "y": -2, "width": 61, "height": 61}}, "c2": {"c2": {"x": 3.18, "y": 1.52, "width": 45, "height": 56}}, "c3": {"c3": {"x": 1.33, "y": 0.65, "width": 53, "height": 48}}, "xx1": {"xx1": {"type": "mesh", "uvs": [0.00307, 0.41404, 0.40799, 0, 0.71441, 0.04959, 1, 0.59349, 0.61122, 0.97458, 0.27823, 0.99123, 0.67689, 0.56019], "triangles": [4, 5, 6, 4, 6, 3, 3, 6, 2, 6, 5, 0, 6, 1, 2, 0, 1, 6], "vertices": [1, 9, -0.78, 0.33, 1, 2, 9, 27.97, 25.18, 0.496, 8, -19.76, 33.39, 0.504, 1, 9, 49.73, 22.2, 1, 2, 9, 70, -10.43, 0.376, 8, 22.28, -2.22, 0.624, 1, 9, 42.4, -33.3, 1, 2, 9, 18.76, -34.3, 0.488, 8, -28.97, -26.08, 0.512, 2, 9, 47.06, -8.44, 0.00559, 8, -0.67, -0.22, 0.99441], "hull": 6}}, "xx2": {"xx2": {"type": "mesh", "uvs": [0, 0.50605, 0.21955, 0.99927, 0.78037, 0.84127, 1, 0.56797, 0.75691, 0, 0.09988, 0.17297, 0.35712, 0.73968], "triangles": [1, 6, 2, 1, 0, 6, 6, 4, 3, 2, 6, 3, 6, 5, 4, 0, 5, 6], "vertices": [1, 3, -75.51, -55.4, 1, 2, 3, -53.34, -110.14, 0.67, 2, -13.09, -28.95, 0.33, 1, 3, 3.3, -92.6, 1, 2, 3, 25.49, -62.27, 0.5, 2, 65.74, 18.92, 0.5, 1, 3, 0.93, 0.78, 1, 2, 3, -65.43, -18.42, 0.5, 2, -25.18, 62.77, 0.5, 1, 2, 0.8, -0.14, 1], "hull": 6}}, "xx3": {"xx3": {"type": "mesh", "uvs": [0, 0.77133, 0.13173, 0.09744, 0.37952, 0, 1, 0.17386, 0.87741, 0.79449, 0.6551, 1, 0.78015, 0.31975], "triangles": [5, 0, 6, 5, 6, 4, 6, 0, 1, 6, 1, 2, 6, 2, 3, 4, 6, 3], "vertices": [1, 6, -1.9, -1.61, 1, 2, 6, 10.62, 62.41, 0.488, 5, -62.68, 20.53, 0.512, 1, 6, 34.16, 71.67, 1, 2, 6, 93.1, 55.15, 0.64, 5, 19.8, 13.27, 0.36, 1, 6, 81.46, -3.81, 1, 2, 6, 60.34, -23.33, 0.488, 5, -12.96, -65.22, 0.512, 1, 5, -1.08, -0.59, 1], "hull": 6}}, "dia": {"dia": {"x": 32.44, "y": 9.87, "rotation": -122.64, "width": 171, "height": 127}}}}], "animations": {"animation": {"slots": {"bau": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff"}]}, "cua": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffff00"}, {"time": 0.5667, "color": "ffffffff"}]}}, "bones": {"bau": {"scale": [{"x": 2, "y": 2}, {"time": 0.3333}, {"time": 0.4333, "x": 1.097, "y": 1.097}, {"time": 0.5333, "curve": "stepped"}, {"time": 1.2667}, {"time": 1.3667, "x": 1.097, "y": 1.097}, {"time": 1.4667}, {"time": 1.5667, "x": 1.097, "y": 1.097}, {"time": 1.6667}]}, "cua": {"scale": [{"x": 2, "y": 2, "curve": "stepped"}, {"time": 0.2333, "x": 2, "y": 2}, {"time": 0.5667}, {"time": 0.6667, "x": 1.097, "y": 1.097}, {"time": 0.7667, "curve": "stepped"}, {"time": 1.6667}, {"time": 1.7667, "x": 1.097, "y": 1.097}, {"time": 1.8667}, {"time": 1.9667, "x": 1.097, "y": 1.097}, {"time": 2.0667}]}, "bone": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -73.71, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}], "translate": [{"y": 2.71, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "y": 9.55, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 3.3333, "y": 2.71}]}, "c1": {"translate": [{"x": -0.86, "y": 6.37, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 1.3333, "x": -0.86, "y": 43.2, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -0.86, "y": 0.86, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 3.3333, "x": -0.86, "y": 6.37}]}, "c2": {"translate": [{"y": -9.53, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "y": -25.92, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "y": -9.53}]}, "xx1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -25.52, "y": 12.13, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "c3": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": -25.92, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "xx2": {"translate": [{"x": 1.89, "y": 4.36, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": 19.58, "y": 45.15, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 3.3333, "x": 1.89, "y": 4.36}]}, "xx5": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 7.61, "y": 8.56, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 4.12, "y": 22.82, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "bat": {"translate": [{"y": -1.64, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": -12.58, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "y": -1.64}]}, "bone3": {"rotate": [{"angle": -4.81, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -36.94, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": -4.81}], "translate": [{"y": 5.84, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "y": 15.88, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 3.3333, "y": 5.84}]}, "bone2": {"rotate": [{"angle": 26.01, "curve": 0.381, "c2": 0.54, "c3": 0.744}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": 65.96, "curve": 0.246, "c3": 0.634, "c4": 0.54}, {"time": 3.3333, "angle": 26.01}], "translate": [{"y": 1.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": 14.25, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "y": 1.85}]}, "dia": {"translate": [{"x": 0.82, "y": -2.86, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 6.29, "y": -22.01, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "x": 0.82, "y": -2.86}]}}}}}