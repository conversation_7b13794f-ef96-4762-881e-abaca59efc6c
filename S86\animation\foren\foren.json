{"skeleton": {"hash": "ZJNT7gm+fbV5zOy8VAlNPvoJSV0", "spine": "3.8.75", "x": -154.11, "y": -232.76, "width": 308.8, "height": 467.2}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": 1.43, "y": -34.01, "scaleX": 1.318, "scaleY": 1.318}, {"name": "elsa", "parent": "bone", "x": 3.71, "y": -52.08}, {"name": "elsa3", "parent": "elsa", "length": 92.66, "rotation": 88.23, "x": -1.63, "y": 8.98}, {"name": "elsa2", "parent": "elsa3", "length": 15.93, "rotation": -1.17, "x": 97.95, "y": 0.57}, {"name": "dau", "parent": "elsa2", "length": 62.1, "rotation": -0.08, "x": 15.03, "y": 1.59}, {"name": "o<PERSON><PERSON>", "parent": "bone", "length": 37.24, "rotation": 94.4, "x": -45.66, "y": -58.2}, {"name": "olaf3", "parent": "o<PERSON><PERSON>", "length": 48.39, "rotation": 1.41, "x": 40.77, "y": -1.91}, {"name": "olaf4", "parent": "o<PERSON><PERSON>", "length": 24.84, "rotation": 48.94, "x": 36.54, "y": 11.53}, {"name": "olaf2", "parent": "olaf4", "length": 6.44, "rotation": -10.63, "x": 27.01, "y": 0.14}, {"name": "tuan_loc", "parent": "bone", "length": 66.66, "rotation": 62.77, "x": 29.3, "y": -53.6}, {"name": "tuan_loc3", "parent": "tuan_loc", "length": 27.99, "rotation": 34.36, "x": 73.65, "y": -0.03}, {"name": "tuan_loc2", "parent": "tuan_loc3", "length": 13.32, "rotation": -68.17, "x": 32.05, "y": -9.5}, {"name": "tuan_loc5", "parent": "tuan_loc3", "length": 16.1, "rotation": 49.18, "x": 33.28, "y": 8.34}, {"name": "kri", "parent": "bone", "length": 116.56, "rotation": 90.24, "x": 44.43, "y": -7.97}, {"name": "anna", "parent": "bone", "length": 114.26, "rotation": 68.61, "x": -67.17, "y": -42.94}, {"name": "anna3", "parent": "anna", "length": 48.17, "rotation": 43.46, "x": 121.34, "y": 4.37}, {"name": "txt", "parent": "root", "x": -10.25, "y": -149.62}, {"name": "olaf6", "parent": "olaf3", "length": 11.5, "rotation": 17.73, "x": 54.02, "y": -0.2}, {"name": "olaf5", "parent": "olaf6", "length": 8.05, "rotation": 22.32, "x": 13.46, "y": 0.26}, {"name": "olaf8", "parent": "olaf5", "length": 7.16, "rotation": 40.06, "x": 8.91, "y": 1.07}, {"name": "elsa5", "parent": "elsa3", "length": 46.11, "rotation": -168.77, "x": 75.19, "y": -29.11}, {"name": "elsa4", "parent": "elsa5", "length": 39.02, "rotation": -1.65, "x": 51.4, "y": -0.5}, {"name": "elsa7", "parent": "elsa3", "length": 43.96, "rotation": -178.23, "x": 66.47, "y": 32.42}, {"name": "elsa6", "parent": "elsa7", "length": 33.01, "rotation": -15.31, "x": 48.13, "y": -0.38}, {"name": "toc", "parent": "elsa2", "x": 2.62, "y": 6.81}, {"name": "toc3", "parent": "toc", "length": 16.76, "rotation": 122.18, "x": -1.52, "y": 3.05}, {"name": "toc2", "parent": "toc3", "length": 18.58, "rotation": 9.79, "x": 18.75, "y": -0.44}, {"name": "toc5", "parent": "toc2", "length": 18.09, "rotation": -8.6, "x": 19.4, "y": 0.09}, {"name": "toc4", "parent": "toc5", "length": 8.47, "rotation": -22.49, "x": 19.2, "y": -0.42}, {"name": "toc7", "parent": "toc4", "length": 11.25, "rotation": -33.62, "x": 8.94, "y": -0.85}, {"name": "t1", "parent": "bone", "x": 55.7, "y": 160.28, "scaleX": 1.291, "scaleY": 1.291}, {"name": "t2", "parent": "bone", "x": 39.51, "y": 156.45, "scaleX": 1.291, "scaleY": 1.291}, {"name": "t3", "parent": "bone", "x": -79.45, "y": 160.28, "scaleX": 1.291, "scaleY": 1.291}, {"name": "t4", "parent": "bone", "x": 73.26, "y": 160.28, "scaleX": 1.158, "scaleY": 1.158}, {"name": "t5", "parent": "bone", "x": 66.41, "y": 145.1, "scaleX": 1.052, "scaleY": 1.052}, {"name": "t6", "parent": "bone", "x": 66.41, "y": 145.1, "scaleX": 1.052, "scaleY": 1.052}, {"name": "t7", "parent": "bone", "x": 66.41, "y": 145.1, "scaleX": 1.291, "scaleY": 1.291}, {"name": "t8", "parent": "bone", "x": 66.41, "y": 145.1, "scaleX": 1.032, "scaleY": 1.032}, {"name": "t9", "parent": "bone", "x": -47.75, "y": 156.45, "scaleX": 1.032, "scaleY": 1.032}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "nuoc", "bone": "bone", "attachment": "nuoc", "blend": "additive"}, {"name": "t3", "bone": "t3", "attachment": "t1", "blend": "additive"}, {"name": "t4", "bone": "t4", "attachment": "t1", "blend": "additive"}, {"name": "kri", "bone": "kri", "attachment": "kri"}, {"name": "tuan_loc", "bone": "tuan_loc5", "attachment": "tuan_loc"}, {"name": "anna", "bone": "anna3", "attachment": "anna"}, {"name": "toc", "bone": "toc7", "attachment": "toc"}, {"name": "o<PERSON><PERSON>", "bone": "olaf8", "attachment": "o<PERSON><PERSON>"}, {"name": "alsa1", "bone": "elsa6", "attachment": "alsa1"}, {"name": "dau", "bone": "dau", "attachment": "dau1"}, {"name": "txt", "bone": "txt", "attachment": "txt1"}, {"name": "t1", "bone": "t1", "attachment": "t1", "blend": "additive"}, {"name": "t2", "bone": "t2", "attachment": "t2", "blend": "additive"}, {"name": "t9", "bone": "t9", "attachment": "t2", "blend": "additive"}, {"name": "t5", "bone": "t5", "attachment": "t1", "blend": "additive"}, {"name": "t7", "bone": "t7", "attachment": "t1", "blend": "additive"}, {"name": "t6", "bone": "t6", "attachment": "t1", "blend": "additive"}, {"name": "t8", "bone": "t8", "attachment": "t1", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"t4": {"t1": {"x": -0.81, "y": -0.98, "width": 19, "height": 21}}, "t5": {"t1": {"x": -0.81, "y": -0.98, "width": 19, "height": 21}}, "olaf": {"olaf": {"type": "mesh", "uvs": [0.27006, 0.95404, 0.26785, 0.87004, 0.29222, 0.81124, 0.33873, 0.76224, 0.38304, 0.73704, 0.4362, 0.73284, 0.42669, 0.68292, 0.44617, 0.65571, 0.46155, 0.64534, 0.21856, 0.53129, 0.12115, 0.53518, 0.04015, 0.53, 0.02887, 0.5125, 0.0176, 0.48917, 0, 0.45289, 0.0258, 0.43863, 0.06476, 0.45029, 0.08937, 0.46455, 0.13243, 0.45483, 0.1314, 0.4328, 0.14986, 0.3991, 0.18985, 0.38031, 0.22368, 0.3842, 0.25444, 0.41141, 0.25752, 0.43863, 0.22881, 0.45418, 0.26059, 0.45742, 0.27495, 0.47492, 0.26264, 0.48917, 0.24624, 0.49565, 0.49949, 0.61035, 0.5241, 0.60257, 0.44105, 0.47881, 0.39593, 0.46001, 0.37748, 0.42632, 0.37133, 0.38485, 0.39183, 0.35698, 0.42054, 0.3194, 0.43387, 0.29024, 0.43592, 0.25525, 0.44002, 0.22738, 0.4431, 0.21054, 0.49332, 0.18777, 0.53743, 0.16777, 0.44105, 0.09714, 0.40721, 0.05372, 0.31493, 0.0291, 0.32621, 0, 0.49026, 0, 0.54871, 0.04854, 0.56614, 0.07381, 0.59997, 0.0453, 0.64816, 0.04141, 0.68712, 0.06215, 0.68815, 0.08094, 0.64201, 0.08353, 0.6133, 0.09714, 0.61432, 0.13407, 0.61227, 0.1561, 0.65113, 0.15986, 0.6861, 0.16323, 0.7712, 0.21118, 0.8305, 0.26894, 0.87767, 0.32272, 0.90433, 0.3778, 0.88587, 0.44908, 0.88382, 0.49638, 0.82538, 0.58062, 0.80282, 0.62533, 0.85306, 0.63988, 0.8869, 0.67876, 0.89715, 0.71828, 0.88895, 0.76753, 0.92073, 0.78588, 0.95559, 0.83578, 0.9843, 0.88308, 0.9966, 0.94075, 0.99148, 0.97575, 0.8869, 1, 0.29734, 0.96927, 0.66225, 0.43697, 0.544, 0.41898, 0.59437, 0.2958, 0.78051, 0.29027, 0.67101, 0.60997, 0.75204, 0.49648, 0.53524, 0.53246, 0.73354, 0.73355, 0.54411, 0.72371, 0.48962, 0.88279, 0.72057, 0.93527, 0.86329, 0.88279, 0.54411, 0.81555, 0.48067, 0.63783, 0.57902, 0.1682, 0.53978, 0.19572, 0.57447, 0.1834, 0.63725, 0.17716, 0.65016, 0.16372], "triangles": [44, 45, 48, 44, 48, 49, 45, 47, 48, 46, 47, 45, 55, 51, 52, 55, 52, 53, 55, 53, 54, 56, 51, 55, 50, 51, 56, 43, 50, 94, 57, 50, 56, 50, 43, 44, 50, 44, 49, 50, 57, 94, 58, 94, 57, 58, 96, 94, 96, 43, 94, 42, 43, 95, 95, 43, 96, 83, 61, 62, 82, 96, 97, 95, 96, 82, 42, 95, 40, 34, 35, 36, 40, 41, 42, 95, 82, 40, 82, 39, 40, 82, 38, 39, 81, 38, 82, 98, 60, 83, 59, 60, 98, 83, 60, 61, 83, 97, 98, 83, 82, 97, 80, 82, 83, 81, 82, 80, 65, 63, 64, 36, 32, 34, 33, 34, 32, 81, 37, 38, 81, 36, 37, 81, 32, 36, 63, 65, 83, 85, 80, 83, 83, 62, 63, 65, 85, 83, 66, 85, 65, 86, 32, 81, 86, 81, 80, 67, 85, 66, 31, 32, 86, 84, 80, 85, 68, 84, 85, 86, 80, 84, 31, 86, 84, 67, 68, 85, 96, 58, 97, 97, 58, 98, 98, 58, 59, 25, 22, 23, 25, 23, 24, 25, 18, 19, 13, 14, 15, 13, 15, 16, 28, 26, 27, 29, 25, 26, 12, 13, 16, 12, 16, 17, 11, 12, 17, 21, 25, 20, 25, 21, 22, 20, 25, 19, 9, 18, 25, 10, 17, 18, 10, 18, 9, 11, 17, 10, 29, 26, 28, 9, 25, 29, 30, 8, 29, 93, 8, 30, 9, 29, 8, 93, 30, 31, 88, 93, 31, 5, 6, 7, 88, 8, 93, 8, 5, 7, 8, 88, 5, 88, 31, 84, 87, 84, 68, 88, 84, 87, 71, 69, 70, 92, 88, 87, 92, 5, 88, 89, 5, 92, 69, 72, 87, 71, 72, 69, 91, 72, 73, 91, 73, 74, 90, 92, 87, 87, 68, 69, 91, 87, 72, 90, 87, 91, 79, 0, 1, 4, 5, 89, 2, 89, 1, 89, 79, 1, 3, 4, 89, 89, 2, 3, 75, 76, 91, 91, 76, 78, 75, 91, 74, 77, 78, 76, 90, 91, 78, 89, 92, 90, 79, 89, 90, 79, 90, 78], "vertices": [2, 6, -2.51, 31.42, 0.86683, 8, -10.65, 42.52, 0.13317, 2, 6, 7.97, 30.79, 0.82757, 8, -4.24, 34.2, 0.17243, 2, 6, 15.15, 28.31, 0.7776, 8, -1.4, 27.15, 0.2224, 2, 6, 20.98, 24.17, 0.71583, 8, -0.69, 20.04, 0.28417, 2, 6, 23.85, 20.44, 0.66613, 8, -1.61, 15.43, 0.33387, 2, 6, 24.05, 16.22, 0.5753, 8, -4.67, 12.5, 0.4247, 2, 6, 30.33, 16.49, 0.24824, 8, -0.34, 7.94, 0.75176, 2, 6, 33.6, 14.69, 0.10949, 8, 0.46, 4.3, 0.89051, 2, 6, 34.8, 13.38, 0.02533, 8, 0.25, 2.53, 0.97467, 2, 8, 24.17, 2.56, 0.79731, 9, -3.24, 1.85, 0.20269, 2, 8, 30.05, 7.54, 0.00679, 9, 1.62, 7.84, 0.99321, 1, 9, 6.44, 12.1, 1, 1, 9, 8.65, 11.27, 1, 1, 9, 11.4, 9.95, 1, 1, 9, 15.67, 7.89, 1, 1, 9, 15.6, 5.19, 1, 1, 9, 12.44, 3.91, 1, 1, 9, 9.81, 3.69, 1, 1, 9, 8.4, 0.37, 1, 1, 9, 10.48, -1.44, 1, 1, 9, 12.58, -5.37, 1, 1, 9, 12.17, -9.28, 1, 2, 8, 34.82, -12.44, 0.00098, 9, 10, -10.92, 0.99902, 2, 8, 30.84, -11.16, 0.01778, 9, 5.85, -10.4, 0.98222, 2, 8, 28.61, -8.57, 0.0559, 9, 3.18, -8.27, 0.9441, 2, 8, 29.27, -5.66, 0.10709, 9, 3.29, -5.28, 0.89291, 2, 8, 27.01, -6.83, 0.30391, 9, 1.29, -6.85, 0.69609, 2, 8, 24.8, -5.76, 0.39315, 9, -1.08, -6.2, 0.60685, 2, 8, 24.52, -3.75, 0.52622, 9, -1.73, -4.28, 0.47378, 2, 8, 25.07, -2.32, 0.69785, 9, -1.45, -2.78, 0.30215, 3, 6, 38.93, 10.06, 0.07593, 8, 0.46, -2.77, 0.86803, 7, -1.55, 12.01, 0.05604, 3, 6, 39.75, 8.04, 0.25588, 8, -0.52, -4.71, 0.50949, 7, -0.78, 9.97, 0.23463, 3, 6, 55.68, 13.4, 0.00051, 8, 13.98, -13.2, 0.01222, 7, 15.28, 14.94, 0.98727, 2, 8, 18.24, -12.96, 0.00345, 7, 17.98, 18.24, 0.99655, 2, 8, 21.93, -15.47, 0.0008, 7, 22.32, 19.27, 0.9992, 1, 7, 27.52, 19.23, 1, 1, 7, 30.82, 17.26, 1, 1, 7, 35.27, 14.53, 1, 1, 7, 38.79, 13.12, 1, 2, 7, 43.12, 12.51, 0.99846, 18, -6.51, 15.43, 0.00154, 2, 7, 46.55, 11.84, 0.98065, 18, -3.44, 13.74, 0.01935, 2, 7, 48.63, 11.38, 0.96415, 18, -1.61, 12.68, 0.03585, 1, 7, 51.06, 7.15, 1, 1, 18, 0.32, 3.71, 1, 1, 18, 11.45, 7.17, 1, 2, 20, 0.73, 4.68, 0.51399, 19, 6.46, 5.12, 0.48601, 1, 20, 8.22, 2.13, 1, 1, 20, 7.6, -1.56, 1, 1, 19, 6.43, -4.27, 1, 2, 18, 13.63, -3.06, 0.37581, 19, -1.11, -3.13, 0.62419, 1, 18, 10.18, -3.06, 1, 1, 18, 12.38, -6.93, 1, 1, 18, 11.31, -10.62, 1, 1, 18, 7.7, -12.4, 1, 1, 18, 5.52, -11.54, 1, 1, 18, 6.67, -8.07, 1, 1, 18, 6.02, -5.31, 1, 1, 18, 1.75, -3.54, 1, 1, 18, -0.71, -2.29, 1, 1, 7, 53.27, -5.61, 1, 1, 7, 52.57, -8.31, 1, 2, 7, 45.92, -14.4, 0.9728, 18, -12.03, -11.05, 0.0272, 1, 7, 38.27, -18.33, 1, 1, 7, 31.2, -21.35, 1, 1, 7, 24.14, -22.75, 1, 1, 7, 15.42, -20.4, 1, 1, 7, 9.56, -19.64, 1, 2, 6, 40.66, -15.9, 0.15028, 7, -0.45, -13.98, 0.84972, 2, 6, 35.23, -13.69, 0.54619, 7, -5.83, -11.64, 0.45381, 2, 6, 33.11, -17.51, 0.74397, 7, -8.04, -15.41, 0.25603, 2, 6, 28.06, -19.8, 0.85149, 7, -13.15, -17.58, 0.14851, 2, 6, 23.07, -20.23, 0.91677, 7, -18.15, -17.88, 0.08323, 2, 6, 16.98, -19.11, 0.97513, 7, -24.21, -16.62, 0.02487, 2, 6, 14.5, -21.44, 0.99071, 7, -26.74, -18.88, 0.00929, 2, 6, 8.07, -23.71, 0.99941, 7, -33.23, -20.99, 0.00059, 1, 6, 2.01, -25.52, 1, 1, 6, -5.26, -25.93, 1, 1, 6, -9.59, -25.19, 1, 1, 6, -11.98, -16.72, 1, 2, 6, -4.57, 29.42, 0.87061, 8, -13.51, 42.76, 0.12939, 1, 7, 18.72, -2.98, 1, 2, 8, 11.92, -24.06, 0.00062, 7, 21.9, 6.09, 0.99938, 1, 7, 36.81, 0.57, 1, 1, 7, 36.01, -14.13, 1, 2, 6, 37.94, -3.46, 0.48011, 7, -2.87, -1.48, 0.51989, 1, 7, 10.6, -9.28, 1, 3, 6, 48.42, 6.49, 0.04037, 8, 4.01, -12.26, 0.07106, 7, 7.85, 8.21, 0.88857, 2, 6, 22.16, -7.2, 0.97111, 7, -18.74, -4.83, 0.02889, 2, 6, 24.54, 7.63, 0.7222, 8, -10.83, 6.49, 0.2778, 2, 6, 5.04, 13.45, 0.90928, 8, -19.25, 25.02, 0.09072, 1, 6, -2.9, -4.24, 1, 1, 6, 2.78, -15.99, 1, 2, 6, 13.09, 8.51, 0.90022, 8, -17.68, 15.7, 0.09978, 2, 6, 35.62, 11.8, 0.00857, 8, -0.4, 0.88, 0.99143, 1, 18, -1.04, 0.72, 1, 2, 7, 49.7, 3.6, 0.936, 18, -2.96, 4.93, 0.064, 1, 7, 50.95, 0.71, 1, 1, 7, 51.22, -4.3, 1, 1, 7, 52.79, -5.48, 1], "hull": 80}}, "t7": {"t1": {"x": -0.81, "y": -0.98, "width": 19, "height": 21}}, "t8": {"t1": {"x": -0.81, "y": -0.98, "width": 19, "height": 21}}, "bg": {"bg": {"x": 0.29, "y": 0.84, "scaleX": 0.8, "scaleY": 0.8, "width": 386, "height": 584}}, "toc": {"toc": {"type": "mesh", "uvs": [0.97496, 0.00177, 0.92112, 0.04522, 0.84439, 0.04884, 0.75823, 0.03617, 0.68015, 0.04522, 0.60746, 0.07962, 0.48092, 0.08686, 0.39612, 0.19548, 0.33689, 0.30591, 0.30323, 0.40004, 0.24939, 0.47427, 0.18746, 0.51591, 0.11477, 0.51591, 0.06227, 0.47246, 0.02323, 0.46703, 0.02997, 0.57746, 0.10131, 0.62996, 0.0515, 0.63358, 0.00304, 0.60099, 0.00439, 0.67341, 0.07573, 0.74763, 0.069, 0.78384, 0, 0.74401, 0.00977, 0.84177, 0.06631, 0.93047, 0.14708, 1, 0.29785, 1, 0.40823, 0.97935, 0.50919, 0.91418, 0.60477, 0.83634, 0.72189, 0.69332, 0.83092, 0.55573, 0.89688, 0.47246, 0.97092, 0.4272, 1, 0.40367, 1, 0.18461, 1, 0.02168, 0.99246, 0, 0.95881, 0.1828, 0.95881, 0.31858, 0.94669, 0.1122, 0.79727, 0.30953, 0.75689, 0.4978, 0.59939, 0.26789, 0.74881, 0.17013, 0.67208, 0.38918, 0.65862, 0.63358, 0.55631, 0.50867, 0.51323, 0.34392, 0.46343, 0.25522, 0.39477, 0.42901, 0.46881, 0.58289, 0.53612, 0.75125, 0.38266, 0.80918, 0.31939, 0.68608, 0.27362, 0.5503, 0.16862, 0.66435, 0.23189, 0.80918, 0.2467, 0.92142, 0.14977, 0.88159, 0.13227, 0.80737, 0.20362, 0.71685], "triangles": [25, 58, 26, 26, 58, 27, 25, 59, 58, 25, 24, 59, 23, 59, 24, 59, 57, 58, 58, 57, 53, 59, 60, 57, 60, 61, 57, 57, 61, 54, 59, 23, 60, 60, 23, 21, 23, 22, 21, 21, 20, 60, 60, 20, 61, 19, 17, 20, 20, 56, 61, 20, 16, 56, 20, 17, 16, 61, 56, 54, 56, 55, 54, 19, 18, 17, 56, 16, 11, 56, 11, 55, 11, 16, 12, 16, 15, 12, 15, 13, 12, 15, 14, 13, 11, 10, 55, 58, 53, 27, 27, 53, 28, 28, 52, 29, 28, 53, 52, 30, 52, 46, 30, 29, 52, 57, 54, 53, 53, 54, 52, 54, 51, 52, 52, 51, 46, 54, 55, 51, 55, 50, 51, 50, 10, 9, 50, 55, 10, 30, 46, 31, 51, 47, 46, 46, 42, 31, 46, 47, 42, 51, 50, 47, 50, 48, 47, 47, 45, 42, 47, 48, 45, 9, 8, 50, 48, 8, 49, 48, 50, 8, 48, 43, 45, 45, 43, 41, 48, 49, 43, 8, 7, 49, 43, 5, 44, 49, 6, 43, 43, 6, 5, 49, 7, 6, 31, 42, 32, 32, 42, 41, 42, 45, 41, 32, 39, 33, 32, 41, 39, 33, 39, 34, 39, 41, 38, 40, 44, 2, 43, 44, 41, 44, 38, 41, 5, 4, 44, 44, 3, 2, 44, 4, 3, 39, 35, 34, 39, 38, 35, 40, 38, 44, 2, 1, 40, 35, 40, 36, 35, 38, 40, 36, 1, 0, 0, 37, 36, 36, 40, 1], "vertices": [2, 25, 11.33, -1.02, 0.99085, 26, -10.28, -8.71, 0.00915, 3, 25, 8.59, 3.05, 0.72347, 26, -5.39, -8.56, 0.27514, 27, -25.17, -3.9, 0.0014, 3, 25, 8.08, 9.01, 0.19099, 26, -0.06, -11.3, 0.77488, 27, -20.38, -7.51, 0.03412, 3, 25, 8.47, 15.76, 0.01596, 26, 5.44, -15.23, 0.82092, 27, -15.63, -12.31, 0.16312, 2, 26, 11.01, -17.75, 0.64118, 27, -10.57, -15.74, 0.35882, 3, 26, 16.93, -18.78, 0.3756, 27, -4.91, -17.76, 0.62299, 28, -21.36, -21.28, 0.00141, 4, 26, 25.75, -23.23, 0.10176, 27, 3.02, -23.65, 0.86776, 28, -12.64, -25.92, 0.02977, 30, -4.51, -44.88, 0.00071, 5, 26, 34.6, -20.97, 0.01998, 27, 12.13, -22.92, 0.84434, 28, -3.75, -23.83, 0.1234, 30, -1.28, -36.34, 0.01101, 29, -12.25, -30.41, 0.00127, 5, 26, 41.76, -17.64, 0.00032, 27, 19.75, -20.86, 0.64103, 28, 3.48, -20.65, 0.29807, 30, 0.11, -28.57, 0.0466, 29, -6.79, -24.7, 0.01398, 4, 27, 25.23, -18.27, 0.36987, 28, 8.51, -17.27, 0.44824, 30, 0.11, -22.51, 0.13084, 29, -3.43, -19.66, 0.05104, 4, 27, 31.2, -17.57, 0.12358, 28, 14.31, -15.69, 0.39392, 30, 2.03, -16.81, 0.36108, 29, 1.32, -15.97, 0.12142, 4, 27, 36.48, -18.73, 0.02735, 28, 19.7, -16.05, 0.15488, 30, 5.34, -12.54, 0.72514, 29, 6.44, -14.25, 0.09263, 4, 27, 40.88, -22.31, 0.00204, 28, 24.59, -18.93, 0.02, 30, 10.45, -10.08, 0.97028, 29, 12.06, -15.03, 0.00768, 2, 28, 26.84, -23.17, 0.00071, 30, 15.23, -10.58, 0.99929, 1, 30, 18.11, -9.54, 1, 1, 30, 14.86, -4, 1, 4, 27, 45.86, -17.83, 0.00029, 28, 28.84, -13.76, 0.00527, 30, 8.53, -3.67, 0.99437, 29, 14.01, -8.63, 8e-05, 1, 30, 11.94, -1.79, 1, 1, 30, 16.16, -1.86, 1, 1, 30, 14.25, 1.88, 1, 2, 30, 7.37, 3.35, 0.9294, 29, 16.93, -2.14, 0.0706, 2, 30, 6.93, 5.47, 0.78157, 29, 17.74, -0.14, 0.21843, 2, 30, 12.78, 5.72, 0.77918, 29, 22.75, -3.17, 0.22082, 2, 30, 9.64, 10.5, 0.72002, 29, 22.78, 2.55, 0.27998, 2, 30, 3.44, 13.23, 0.46798, 29, 19.12, 8.26, 0.53202, 3, 28, 36.64, 6.56, 0.00795, 30, -3.99, 14.13, 0.15414, 29, 13.44, 13.12, 0.8379, 2, 28, 26.5, 12.52, 0.30066, 29, 1.79, 14.75, 0.69934, 2, 28, 18.47, 15.84, 0.72876, 29, -6.9, 14.75, 0.27124, 3, 27, 31.53, 15.01, 0.00578, 28, 9.76, 16.57, 0.95504, 29, -15.22, 12.1, 0.03918, 3, 26, 38.56, 19.42, 0.00463, 27, 22.9, 16.2, 0.14153, 28, 1.05, 16.46, 0.85384, 3, 26, 26.54, 16.64, 0.14621, 27, 10.58, 15.51, 0.59792, 28, -11.03, 13.93, 0.25587, 4, 25, -21.34, 8.56, 0.0002, 26, 15.22, 13.83, 0.70227, 27, -1.05, 14.67, 0.28314, 28, -22.4, 11.36, 0.0144, 4, 25, -16.25, 3.67, 0.03228, 26, 8.37, 12.13, 0.93034, 27, -8.09, 14.16, 0.03731, 28, -29.29, 9.8, 8e-05, 3, 25, -13.33, -1.97, 0.1659, 26, 2.05, 12.67, 0.83398, 27, -14.23, 15.76, 0.00013, 2, 25, -11.85, -4.16, 0.21652, 26, -0.6, 12.58, 0.78348, 1, 25, 0.83, -3.51, 1, 1, 25, 10.27, -3.03, 1, 2, 25, 11.5, -2.38, 0.99946, 26, -11.52, -8.13, 0.00054, 1, 25, 0.77, -0.3, 1, 2, 25, -7.09, -0.7, 0.30374, 26, -0.21, 6.71, 0.69626, 2, 25, 4.82, 0.86, 0.84314, 26, -5.23, -4.2, 0.15686, 1, 26, 10.53, 0.09, 1, 3, 26, 18.62, 8.08, 0.54356, 27, 1.32, 8.42, 0.44084, 28, -19.13, 5.54, 0.0156, 4, 26, 22.82, -9.56, 0.14021, 27, 2.46, -9.67, 0.85608, 28, -15.29, -12.18, 0.00366, 30, -17.39, -39.42, 4e-05, 3, 25, 0.67, 16.1, 0.00109, 26, 9.88, -8.81, 0.8296, 27, -10.16, -6.74, 0.1693, 1, 27, 2.49, -0.64, 1, 3, 26, 29.15, 11.21, 0.07997, 27, 12.23, 9.71, 0.65864, 28, -8.53, 8.45, 0.26139, 2, 27, 13.87, -0.94, 0.99986, 30, -20.41, -25.38, 0.00014, 5, 26, 30.84, -8.99, 0.01036, 27, 10.46, -10.48, 0.91967, 28, -7.26, -11.78, 0.06553, 30, -13.24, -32.54, 0.00423, 29, -20.1, -20.62, 0.0002, 5, 26, 31.71, -15.38, 0.02622, 27, 10.24, -16.92, 0.87084, 28, -6.52, -18.19, 0.09503, 30, -7.51, -35.49, 0.00731, 29, -16.97, -26.25, 0.00059, 4, 27, 20.74, -12.47, 0.49891, 28, 3.21, -12.21, 0.4294, 30, -7.05, -24.09, 0.0522, 29, -10.27, -17.01, 0.0195, 4, 27, 21.88, -1.9, 0.05575, 28, 2.75, -1.59, 0.94163, 30, -16.12, -18.55, 0.00206, 29, -14.76, -7.37, 0.00056, 4, 26, 40.82, 12.49, 0.001, 27, 23.95, 9, 0.06839, 28, 3.17, 9.49, 0.9306, 29, -18.61, 3.03, 1e-05, 2, 28, 15.19, 6.32, 0.89912, 29, -6.29, 4.7, 0.10088, 4, 27, 34.7, -4.59, 0.00386, 28, 15.83, -2.33, 0.74504, 30, -8.21, -8.1, 0.03872, 29, -2.39, -3.05, 0.21239, 4, 27, 32.51, -12.95, 0.07596, 28, 14.92, -10.93, 0.41441, 30, -1.58, -13.65, 0.33681, 29, 0.06, -11.35, 0.17282, 4, 27, 43.04, -12.97, 0.00178, 28, 25.33, -9.38, 0.01799, 30, 2.93, -4.14, 0.94078, 29, 9.09, -5.93, 0.03945, 2, 28, 25.33, 0.37, 0.00991, 29, 5.36, 3.08, 0.99009, 3, 28, 27.63, 6.56, 0.1222, 30, -9.02, 6.66, 0.00633, 29, 5.12, 9.68, 0.87147, 3, 28, 32.98, 0.74, 0.00109, 30, -1.2, 7.85, 0.25509, 29, 12.28, 6.35, 0.74382, 2, 30, 1.89, 4.56, 0.59536, 29, 13.04, 1.9, 0.40464, 4, 27, 42.84, -8.89, 0.00054, 28, 24.52, -5.37, 0.00458, 30, -0.85, -2.58, 0.61868, 29, 6.8, -2.53, 0.37619], "hull": 38}}, "alsa1": {"alsa1": {"type": "mesh", "uvs": [0.40342, 0.00881, 0.38761, 0.10667, 0.21689, 0.17713, 0.11572, 0.22214, 0.07146, 0.43155, 0.02088, 0.66641, 0, 0.85625, 0, 0.99717, 0.10878, 0.99748, 0.8931, 0.9997, 1, 1, 1, 0.80341, 1, 0.61161, 0.95037, 0.4433, 0.92191, 0.21823, 0.84287, 0.14777, 0.63105, 0.08318, 0.60576, 0, 0.12316, 0.94181, 0.18313, 0.7948, 0.2479, 0.68788, 0.26229, 0.60027, 0.22151, 0.47405, 0.19272, 0.35228, 0.18793, 0.31071, 0.86678, 0.86459, 0.8092, 0.7547, 0.72285, 0.66412, 0.68687, 0.55869, 0.73724, 0.41762, 0.71805, 0.31071, 0.28706, 0.34706, 0.45463, 0.34169, 0.5933, 0.33633, 0.34484, 0.73157, 0.5673, 0.72799, 0.61352, 0.84067, 0.64241, 0.94082, 0.32751, 0.8049, 0.3044, 0.91757], "triangles": [13, 28, 29, 13, 27, 28, 13, 30, 14, 33, 30, 29, 13, 29, 30, 33, 16, 30, 30, 15, 14, 30, 16, 15, 10, 25, 11, 37, 25, 9, 10, 9, 25, 37, 26, 25, 37, 36, 26, 25, 26, 11, 36, 27, 26, 26, 12, 11, 26, 27, 12, 27, 13, 12, 8, 37, 9, 37, 8, 39, 37, 39, 36, 36, 39, 38, 35, 36, 38, 38, 34, 35, 36, 35, 27, 8, 18, 39, 8, 7, 18, 7, 6, 18, 18, 19, 39, 18, 6, 19, 39, 19, 38, 6, 5, 19, 19, 20, 38, 38, 20, 34, 19, 5, 20, 20, 21, 34, 20, 5, 21, 5, 22, 21, 31, 32, 22, 5, 4, 22, 32, 21, 22, 4, 23, 22, 22, 23, 31, 4, 24, 23, 4, 3, 24, 23, 24, 31, 31, 24, 2, 31, 2, 1, 24, 3, 2, 35, 34, 21, 35, 21, 32, 35, 28, 27, 28, 32, 33, 28, 35, 32, 28, 33, 29, 1, 32, 31, 32, 1, 33, 33, 1, 16, 16, 0, 17, 0, 16, 1], "vertices": [1, 4, 20.26, 11.44, 1, 3, 4, 3.55, 12.25, 0.7947, 3, 101.75, 12.75, 0.14461, 23, -34.66, 20.75, 0.06068, 3, 4, -9.34, 29.59, 0.11137, 3, 89.22, 30.35, 0.28015, 23, -22.67, 2.78, 0.60848, 3, 4, -17.53, 39.84, 0.02778, 3, 81.24, 40.76, 0.07942, 23, -15.02, -7.87, 0.8928, 1, 23, 20.6, -12.53, 1, 2, 23, 60.54, -17.86, 0.01756, 24, 16.59, -13.58, 0.98244, 2, 24, 48.31, -7.18, 0.91647, 2, -52.05, -16.42, 0.08353, 2, 24, 71.42, -0.85, 0.7839, 2, -52.05, -40.39, 0.2161, 3, 24, 68.45, 10.21, 0.72511, 2, -40.6, -40.44, 0.27462, 22, 61.56, -87.52, 0.00027, 3, 24, 47.01, 89.96, 9e-05, 2, 41.98, -40.82, 0.22984, 22, 73.16, -5.77, 0.77007, 2, 2, 53.24, -40.87, 0.16792, 22, 74.74, 5.38, 0.83208, 2, 2, 53.24, -7.43, 0.00074, 22, 41.62, 9.92, 0.99926, 2, 22, 9.3, 14.36, 0.93717, 21, 61.11, 13.59, 0.06283, 2, 22, -19.77, 13.08, 0.00055, 21, 32.01, 13.14, 0.99945, 3, 4, -12.52, -44.9, 0.01378, 3, 84.52, -44.06, 0.00431, 21, -6.24, 16.48, 0.98191, 3, 4, -0.98, -35.97, 0.09293, 3, 96.24, -35.37, 0.09774, 21, -19.42, 10.24, 0.80933, 3, 4, 8.85, -13.14, 0.7752, 3, 106.53, -12.74, 0.10965, 21, -33.93, -9.95, 0.11515, 2, 4, 22.84, -9.75, 0.99749, 21, -48.32, -10.25, 0.00251, 3, 24, 58.92, 9.17, 0.73053, 2, -39.08, -30.97, 0.2692, 22, 52.38, -84.74, 0.00027, 3, 3, -15.9, 30.66, 0.03069, 24, 33.14, 8.66, 0.83252, 2, -32.77, -5.97, 0.1368, 4, 3, 2.49, 24.41, 0.20658, 23, 64.19, 6.04, 7e-05, 24, 13.8, 10.43, 0.75904, 2, -25.95, 12.22, 0.03432, 3, 3, 17.43, 23.35, 0.21325, 23, 49.29, 7.56, 0.26483, 24, -0.98, 7.96, 0.52192, 2, 3, 38.76, 28.3, 0.05342, 23, 27.83, 3.26, 0.94658, 2, 3, 59.36, 31.97, 0.00201, 23, 7.12, 0.23, 0.99799, 1, 23, 0.05, -0.27, 1, 2, 2, 39.21, -17.84, 0.15609, 22, 50.02, -5.39, 0.84391, 3, 3, -7.05, -35.01, 0.03431, 2, 33.15, 0.85, 0.11866, 22, 30.68, -8.85, 0.84703, 4, 3, 8.07, -25.45, 0.27859, 2, 24.06, 16.26, 0.08206, 22, 14.18, -15.76, 0.6327, 21, 65.12, -16.66, 0.00665, 4, 3, 25.88, -21.11, 0.4555, 2, 20.27, 34.19, 0.00174, 22, -4.1, -17.08, 0.27023, 21, 46.81, -17.45, 0.27254, 2, 3, 50.02, -25.67, 0.19168, 21, 24.02, -8.28, 0.80832, 2, 3, 68.13, -23.09, 0.19759, 21, 5.75, -7.28, 0.80241, 2, 3, 60.56, 22.07, 0.27388, 23, 6.23, 10.17, 0.72612, 2, 3, 62.01, 4.47, 0.92636, 23, 5.32, 27.81, 0.07364, 2, 3, 63.37, -10.1, 0.74086, 21, 7.89, -20.95, 0.25914, 3, 3, -4.62, 13.98, 0.40612, 24, 18.27, 22.24, 0.37125, 2, -15.74, 4.79, 0.22263, 3, 3, -3.29, -9.42, 0.40518, 2, 7.68, 5.39, 0.46163, 22, 22.71, -33.46, 0.13319, 4, 3, -22.3, -14.87, 0.00323, 24, 28.69, 54.43, 0.01389, 2, 12.54, -13.77, 0.70435, 22, 42.36, -31.25, 0.27853, 3, 24, 44.32, 61.86, 0.03292, 2, 15.59, -30.8, 0.61671, 22, 59.65, -30.55, 0.35038, 4, 3, -17.14, 15.42, 0.094, 24, 30.78, 23.78, 0.41252, 2, -17.57, -7.68, 0.49224, 22, 32.24, -60.26, 0.00125, 4, 3, -36.37, 17.26, 0.00104, 24, 49.9, 26.49, 0.43366, 2, -20, -26.85, 0.5478, 22, 50.89, -65.27, 0.0175], "hull": 18}}, "nuoc": {"nuoc": {"x": 3.87, "y": 49.64, "width": 208, "height": 231}}, "anna": {"anna": {"type": "mesh", "uvs": [0.37181, 0.75418, 0.33604, 0.82362, 0.38204, 0.91911, 0.46004, 1, 0.68204, 1, 0.86004, 0.81639, 0.98804, 0.6312, 1, 0.52558, 1, 0.12212, 0.91404, 0, 0.65004, 0, 0.51604, 0.04689, 0.47404, 0.14527, 0.50604, 0.23352, 0.63004, 0.28416, 0.53804, 0.29284, 0.45204, 0.31889, 0.40204, 0.37676, 0.31004, 0.42016, 0.39204, 0.45923, 0.43604, 0.48961, 0.50204, 0.48961, 0.54204, 0.51999, 0.57804, 0.53446, 0.50804, 0.57208, 0.47604, 0.63284, 0.41404, 0.71242], "triangles": [7, 14, 8, 9, 8, 14, 14, 10, 9, 13, 11, 14, 14, 11, 10, 13, 12, 11, 4, 26, 25, 4, 25, 5, 5, 25, 23, 25, 24, 23, 2, 26, 3, 4, 3, 26, 1, 0, 2, 2, 0, 26, 5, 23, 6, 6, 23, 7, 7, 23, 14, 23, 22, 14, 22, 21, 14, 14, 21, 15, 21, 17, 16, 21, 16, 15, 19, 17, 20, 21, 20, 17, 19, 18, 17], "vertices": [1, 15, 14.97, 33.89, 1, 1, 15, 1.03, 33.69, 1, 1, 15, -13.4, 21.31, 1, 1, 15, -23.7, 5.89, 1, 1, 15, -12.69, -22.22, 1, 1, 15, 28.28, -32.18, 1, 1, 15, 67.05, -35.69, 1, 1, 15, 86.13, -29.96, 1, 1, 16, 21.12, -29.21, 1, 1, 16, 46.79, -27.01, 1, 1, 16, 60.28, 6.27, 1, 1, 16, 58.96, 26.47, 1, 2, 15, 126.62, 62.71, 0.00893, 16, 43.97, 38.71, 0.99107, 2, 15, 112.76, 52.61, 0.05661, 16, 26.96, 40.91, 0.94339, 2, 15, 110.05, 33.44, 0.41526, 16, 11.8, 28.86, 0.58474, 2, 15, 103.96, 44.49, 0.7556, 16, 14.99, 41.07, 0.2444, 2, 15, 95.14, 53.6, 0.85657, 16, 14.85, 53.75, 0.14343, 2, 15, 82.53, 55.96, 0.91864, 16, 7.32, 64.14, 0.08136, 2, 15, 70.37, 64.64, 0.94442, 16, 4.46, 78.8, 0.05558, 2, 15, 67.6, 51.57, 0.94469, 16, -6.54, 71.23, 0.05531, 2, 15, 64.46, 43.92, 0.94891, 16, -14.08, 67.83, 0.05109, 2, 15, 67.73, 35.56, 0.95566, 16, -17.45, 59.51, 0.04434, 2, 15, 64.4, 28.41, 0.97429, 16, -24.79, 56.62, 0.02571, 2, 15, 63.65, 22.86, 0.98896, 16, -29.15, 53.1, 0.01104, 2, 15, 53.6, 29.15, 0.99968, 16, -32.13, 64.58, 0.00032, 1, 15, 41.37, 29.04, 1, 1, 15, 24.37, 31.43, 1], "hull": 27}}, "kri": {"kri": {"x": 57.11, "y": -1.68, "rotation": -90.24, "width": 96, "height": 136}}, "t6": {"t1": {"x": -0.81, "y": -0.98, "width": 19, "height": 21}}, "dau": {"dau1": {"x": 30.8, "y": -0.54, "scaleX": 0.786, "scaleY": 0.786, "rotation": -86.99, "width": 87, "height": 92}}, "tuan_loc": {"tuan_loc": {"type": "mesh", "uvs": [0.00333, 0.82822, 0.00717, 0.73176, 0.10476, 0.66839, 0.19721, 0.61697, 0.30251, 0.55599, 0.37185, 0.50098, 0.38122, 0.4792, 0.2918, 0.46078, 0.25662, 0.42597, 0.25516, 0.41163, 0.30793, 0.40685, 0.35337, 0.4089, 0.4208, 0.41436, 0.45452, 0.413, 0.41201, 0.40003, 0.27421, 0.3722, 0.13349, 0.3251, 0.06019, 0.26572, 0.03674, 0.23364, 0, 0.21521, 0, 0.16675, 0.05286, 0.16198, 0.08218, 0.10942, 0.12029, 0.06506, 0.20678, 0.02683, 0.25955, 0, 0.32699, 0, 0.35484, 0.00977, 0.31819, 0.03298, 0.22144, 0.08758, 0.18479, 0.10737, 0.19652, 0.12239, 0.17746, 0.14969, 0.14814, 0.16812, 0.14961, 0.19542, 0.17453, 0.22204, 0.19652, 0.26436, 0.2273, 0.2978, 0.26688, 0.3094, 0.25369, 0.27664, 0.27568, 0.25071, 0.31086, 0.23296, 0.35191, 0.23364, 0.36803, 0.25412, 0.34604, 0.28756, 0.36803, 0.32032, 0.40761, 0.33807, 0.46625, 0.35445, 0.50436, 0.36128, 0.51609, 0.32852, 0.52342, 0.28893, 0.56593, 0.26436, 0.59964, 0.27732, 0.60111, 0.31009, 0.59378, 0.33602, 0.62896, 0.34763, 0.63189, 0.37015, 0.66854, 0.37083, 0.71105, 0.35172, 0.74981, 0.31964, 0.75128, 0.27869, 0.69117, 0.24593, 0.63694, 0.22818, 0.65159, 0.19269, 0.65159, 0.13604, 0.7117, 0.11488, 0.77326, 0.0828, 0.84656, 0.08212, 0.88907, 0.14901, 0.88907, 0.22955, 0.87148, 0.2862, 0.82017, 0.34353, 0.88467, 0.32852, 0.93598, 0.27937, 0.99168, 0.27869, 1, 0.33056, 0.94917, 0.35786, 0.91399, 0.37561, 0.91106, 0.39697, 0.86122, 0.4154, 0.74834, 0.40857, 0.74742, 0.41563, 0.79038, 0.41806, 0.80606, 0.42431, 0.80226, 0.43988, 0.78044, 0.44898, 0.82249, 0.45818, 0.86347, 0.47892, 0.85097, 0.49754, 0.84722, 0.51675, 0.87472, 0.53828, 0.85597, 0.55632, 0.80597, 0.57844, 0.83722, 0.62209, 0.86472, 0.664, 0.86472, 0.71347, 0.81722, 0.78156, 0.77097, 0.85606, 0.69097, 0.92066, 0.61847, 0.98003, 0.03972, 0.97712, 0, 0.91193, 0.25421, 0.91562, 0.26594, 0.7509, 0.55912, 0.82098, 0.72917, 0.71268, 0.37735, 0.62349, 0.61385, 0.58694, 0.37149, 0.71253, 0.58649, 0.73619, 0.60017, 0.3988, 0.69594, 0.39334, 0.53371, 0.40699, 0.66858, 0.47798, 0.51026, 0.56807, 0.41644, 0.5344, 0.4868, 0.49527, 0.68617, 0.52439, 0.76435, 0.48435, 0.65839, 0.39554], "triangles": [113, 119, 111, 81, 111, 80, 80, 71, 79, 78, 79, 77, 79, 71, 72, 111, 58, 80, 80, 59, 71, 80, 58, 59, 77, 79, 72, 119, 57, 111, 119, 56, 57, 111, 57, 58, 77, 72, 76, 72, 73, 76, 75, 73, 74, 75, 76, 73, 70, 71, 60, 71, 59, 60, 70, 60, 69, 69, 60, 68, 68, 60, 66, 61, 65, 60, 60, 65, 66, 62, 63, 61, 61, 63, 65, 66, 67, 68, 63, 64, 65, 110, 56, 119, 56, 110, 54, 12, 13, 116, 48, 13, 47, 13, 48, 112, 6, 7, 11, 7, 10, 11, 6, 11, 12, 7, 8, 10, 8, 9, 10, 13, 14, 47, 112, 48, 54, 112, 54, 110, 54, 48, 49, 14, 15, 45, 45, 38, 44, 40, 41, 44, 38, 40, 44, 45, 46, 14, 14, 46, 47, 56, 54, 55, 16, 37, 15, 37, 38, 15, 45, 15, 38, 49, 50, 54, 54, 50, 53, 53, 51, 52, 51, 53, 50, 16, 36, 37, 16, 35, 36, 35, 17, 34, 35, 16, 17, 38, 39, 40, 44, 42, 43, 44, 41, 42, 17, 18, 34, 34, 21, 33, 21, 34, 18, 18, 19, 21, 19, 20, 21, 21, 22, 33, 32, 33, 30, 30, 33, 22, 32, 30, 31, 30, 22, 23, 29, 30, 24, 30, 23, 24, 29, 25, 28, 29, 24, 25, 28, 26, 27, 28, 25, 26, 84, 82, 83, 85, 81, 82, 105, 117, 92, 107, 114, 113, 116, 110, 113, 107, 113, 117, 113, 110, 119, 112, 110, 116, 117, 118, 92, 92, 118, 89, 92, 89, 91, 86, 118, 85, 118, 86, 89, 115, 116, 114, 113, 114, 116, 91, 89, 90, 5, 6, 115, 116, 115, 6, 117, 113, 118, 89, 86, 88, 88, 86, 87, 113, 81, 118, 118, 81, 85, 85, 82, 84, 113, 111, 81, 116, 6, 12, 116, 13, 112, 100, 102, 99, 99, 102, 104, 108, 104, 102, 108, 102, 103, 104, 108, 109, 99, 104, 98, 100, 101, 102, 98, 104, 97, 97, 109, 105, 109, 97, 104, 114, 109, 108, 106, 115, 114, 101, 0, 102, 102, 0, 103, 2, 103, 0, 2, 0, 1, 97, 105, 96, 114, 108, 106, 96, 105, 95, 2, 3, 103, 108, 103, 106, 109, 107, 105, 109, 114, 107, 105, 94, 95, 107, 117, 105, 105, 93, 94, 105, 92, 93, 106, 103, 3, 106, 3, 4, 106, 4, 115, 115, 4, 5], "vertices": [1, 10, 3.4, 26.65, 1, 1, 10, 19.76, 34.69, 1, 2, 10, 34.34, 32.54, 0.99984, 11, -14.07, 49.07, 0.00016, 2, 10, 46.7, 29.75, 0.97894, 11, -5.44, 39.79, 0.02106, 2, 10, 61.19, 26.79, 0.79835, 11, 4.85, 29.17, 0.20165, 2, 10, 73.23, 26.12, 0.41521, 11, 14.41, 21.82, 0.58479, 3, 10, 77.27, 27.27, 0.24181, 11, 18.39, 20.49, 0.71819, 13, -0.53, 19.22, 0.04, 3, 10, 76.76, 35.86, 0.03578, 11, 22.82, 27.87, 0.25965, 13, 7.95, 20.68, 0.70457, 3, 10, 81.19, 41.62, 0.01899, 11, 29.73, 30.13, 0.22739, 13, 14.17, 16.93, 0.75362, 3, 10, 83.54, 42.98, 0.01791, 11, 32.44, 29.92, 0.22505, 13, 15.78, 14.74, 0.75703, 3, 10, 86.47, 39.26, 0.01928, 11, 32.76, 25.2, 0.22936, 13, 12.42, 11.42, 0.75136, 3, 10, 87.96, 35.53, 0.02316, 11, 31.88, 21.28, 0.24186, 13, 8.88, 9.52, 0.73498, 3, 10, 89.76, 29.78, 0.02468, 11, 30.12, 15.52, 0.25205, 13, 3.37, 7.09, 0.72327, 3, 10, 91.34, 27.26, 0.01257, 11, 30.01, 12.54, 0.19477, 13, 1.04, 5.23, 0.79266, 3, 10, 91.81, 31.71, 0.00139, 11, 32.9, 15.95, 0.01716, 13, 5.51, 5.26, 0.98145, 1, 13, 18.52, 7.61, 1, 1, 13, 33.76, 7.08, 1, 1, 13, 45.35, 1.32, 1, 1, 13, 50.43, -2.58, 1, 1, 13, 55.06, -3.69, 1, 1, 13, 60.14, -11.31, 1, 1, 13, 56.77, -14.64, 1, 1, 13, 60.13, -24.34, 1, 1, 13, 61.99, -33.17, 1, 1, 13, 59.67, -43.41, 1, 1, 13, 58.61, -50.2, 1, 1, 13, 53.68, -53.49, 1, 1, 13, 50.61, -53.32, 1, 1, 13, 50.86, -47.88, 1, 1, 13, 52.22, -34.57, 1, 1, 13, 52.83, -29.67, 1, 1, 13, 50.4, -27.88, 1, 1, 13, 48.93, -22.66, 1, 1, 13, 49.15, -18.33, 1, 1, 13, 46.18, -14.1, 1, 1, 13, 41.56, -11.13, 1, 1, 13, 35.52, -5.55, 1, 1, 13, 29.75, -1.8, 1, 1, 13, 25.64, -1.9, 1, 1, 13, 30.04, -6.41, 1, 1, 13, 31.15, -11.56, 1, 1, 13, 30.43, -16.07, 1, 1, 13, 27.36, -17.97, 1, 1, 13, 24.03, -15.54, 1, 1, 13, 22.13, -9.2, 1, 1, 13, 17.09, -5.12, 1, 2, 13, 12.33, -4.27, 0.99986, 12, -17.98, 20.69, 0.00014, 2, 13, 6.32, -4.55, 0.99174, 12, -14.97, 15.48, 0.00826, 1, 13, 2.81, -5.34, 1, 1, 13, 5.39, -11.06, 1, 1, 13, 9, -17.65, 1, 1, 13, 8.47, -23.59, 1, 1, 13, 4.64, -23.19, 1, 1, 13, 1.1, -18.11, 1, 1, 13, -1.09, -13.67, 1, 1, 13, -4.88, -13.57, 1, 1, 12, -3.65, 5.83, 1, 1, 12, -0.89, 4.15, 1, 1, 12, 4.13, 5.5, 1, 1, 12, 10.05, 9.16, 1, 1, 12, 13.91, 15.87, 1, 1, 12, 12.28, 23.85, 1, 1, 12, 9.73, 29.09, 1, 1, 12, 14.1, 34.34, 1, 1, 12, 19.29, 43.71, 1, 1, 12, 25.85, 44.65, 1, 1, 12, 33.53, 47.33, 1, 1, 12, 39.23, 44.32, 1, 1, 12, 36.39, 31.45, 1, 1, 12, 29.02, 18.13, 1, 1, 12, 22.48, 9.51, 1, 1, 12, 13.29, 2.21, 1, 1, 12, 19.63, 1.95, 1, 1, 12, 28.07, 7.89, 1, 1, 12, 32.42, 5.63, 1, 1, 12, 28.32, -3.31, 1, 1, 12, 21.91, -5.66, 1, 1, 12, 17.58, -7.09, 1, 1, 12, 15.4, -10.5, 1, 1, 12, 9.87, -11.42, 1, 1, 12, 1.8, -5.49, 1, 1, 11, 26.32, -12.97, 1, 1, 11, 25.39, -16.67, 1, 1, 11, 24.05, -17.89, 1, 1, 11, 21.17, -17.19, 1, 1, 11, 19.7, -15.08, 1, 1, 11, 17.52, -18.53, 1, 1, 11, 13.18, -21.62, 1, 2, 11, 9.82, -20.1, 0.96, 12, 1.57, -24.57, 0.04, 2, 11, 6.26, -19.32, 0.936, 12, -0.48, -27.59, 0.064, 2, 11, 1.92, -21.21, 0.984, 12, -0.33, -32.32, 0.016, 2, 10, 83.42, -16.55, 0.05655, 11, -1.25, -19.15, 0.94345, 2, 10, 77.69, -14.55, 0.2305, 11, -4.86, -14.27, 0.7695, 2, 10, 71.62, -20.77, 0.64605, 11, -13.38, -15.97, 0.35395, 2, 10, 65.68, -26.55, 0.84291, 11, -21.54, -17.39, 0.15709, 2, 10, 57.37, -30.83, 0.94467, 11, -30.82, -16.23, 0.05533, 2, 10, 44.01, -33, 0.99629, 11, -43.07, -10.49, 0.00371, 1, 10, 29.63, -35.82, 1, 1, 10, 15.55, -35.15, 1, 1, 10, 2.66, -34.61, 1, 1, 10, -20.16, 10.93, 1, 1, 10, -10.81, 19.67, 1, 1, 10, -1.19, -0.54, 1, 1, 10, 26.96, 12.79, 1, 1, 10, 26.99, -16.21, 1, 2, 10, 52.04, -20.15, 0.97279, 11, -29.19, -4.42, 0.02721, 2, 10, 52.86, 15.09, 0.95731, 11, -8.63, 24.21, 0.04269, 2, 10, 68.53, -0.25, 0.79673, 11, -4.35, 2.71, 0.20327, 2, 10, 37.66, 7.85, 0.99939, 13, -24.28, 56.38, 0.00061, 1, 10, 42.34, -11.02, 1, 3, 11, 31.08, -0.51, 0.71673, 13, -8.14, -4.11, 0.1498, 12, -8.71, 2.44, 0.13347, 1, 12, -0.84, -0.74, 1, 3, 10, 95.54, 21.58, 0.00212, 11, 30.27, 5.48, 0.39655, 13, -4.13, 0.42, 0.60133, 1, 11, 15.48, -4.63, 1, 2, 10, 67.53, 9.48, 0.65991, 11, 0.32, 11.31, 0.34009, 2, 10, 69.41, 19.74, 0.54816, 11, 7.65, 18.71, 0.45184, 2, 10, 78.82, 17.62, 0.19432, 11, 14.23, 11.65, 0.80568, 1, 11, 6.59, -5.08, 1, 1, 11, 13.24, -12.84, 1, 1, 12, -3.93, 0.5, 1], "hull": 102}}, "txt": {"txt1": {"x": 10.04, "y": -6.6, "width": 289, "height": 87}}, "t9": {"t2": {"x": 1.1, "y": -0.15, "width": 12, "height": 13}}, "t1": {"t1": {"x": -0.81, "y": -0.98, "width": 19, "height": 21}}, "t2": {"t2": {"x": 39.88, "y": -0.15, "width": 12, "height": 13}}, "t3": {"t1": {"x": -0.81, "y": -0.98, "width": 19, "height": 21}}}}], "animations": {"animation": {"slots": {"t9": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2.0333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.7, "color": "ffffffff", "curve": "stepped"}, {"time": 6.5667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 7, "color": "ffffff00"}]}, "t3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.3667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 5.9, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "color": "ffffff00"}]}, "t4": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 6.5667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 7, "color": "ffffff00"}]}, "t8": {"color": [{"time": 3.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "color": "ffffff00"}]}, "t6": {"color": [{"time": 2.6667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "color": "ffffff00"}]}, "t7": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 4.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 5.3333, "color": "ffffffff"}]}, "t1": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 5, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "color": "ffffff00"}]}, "nuoc": {"color": [{"color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "color": "ffffff5f", "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "color": "ffffff5f", "curve": 0.25, "c3": 0.75}, {"time": 3.5, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 4.3667, "color": "ffffff5f", "curve": 0.25, "c3": 0.75}, {"time": 5.2667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "color": "ffffff5f", "curve": 0.25, "c3": 0.75}, {"time": 7, "color": "ffffffff"}]}, "t5": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 3.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 4, "color": "ffffffff"}]}, "t2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.8667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 5.4, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "color": "ffffff00"}]}}, "bones": {"t4": {"translate": [{"y": 13.1, "curve": "stepped"}, {"time": 1.6667, "y": 13.1, "curve": 0.25, "c3": 0.75}, {"time": 7, "x": 15.72, "y": -205.93}]}, "t5": {"translate": [{"y": 13.1, "curve": "stepped"}, {"time": 3.3333, "y": 13.1}, {"time": 7, "x": -19.14, "y": -97.78}]}, "t6": {"translate": [{"x": -19.14, "y": -97.78}, {"time": 3.3333, "x": -68.89, "y": -215.26}]}, "t7": {"translate": [{"x": -105.02, "y": 28.41, "curve": "stepped"}, {"time": 4.6667, "x": -105.02, "y": 28.41}, {"time": 7, "x": -98.34, "y": -50.26}]}, "t8": {"translate": [{"x": -98.34, "y": -50.26}, {"time": 4.3333, "x": -85.14, "y": -211.3}]}, "t9": {"translate": [{"y": 13.1, "curve": "stepped"}, {"time": 2.0333, "y": 13.1, "curve": 0.249, "c3": 0.745, "c4": 0.98}, {"time": 7, "x": -12.04, "y": -133.69}]}, "t1": {"translate": [{"y": 13.1, "curve": 0.25, "c3": 0.75}, {"time": 5.4333, "x": 15.72, "y": -205.93}]}, "t2": {"translate": [{"y": 13.1, "curve": "stepped"}, {"time": 0.8667, "y": 13.1, "curve": 0.249, "c3": 0.745, "c4": 0.98}, {"time": 5.8333, "x": -12.04, "y": -133.69}]}, "t3": {"translate": [{"y": 13.1, "curve": "stepped"}, {"time": 1.3667, "y": 13.1, "curve": 0.249, "c3": 0.745, "c4": 0.98}, {"time": 6.3333, "x": -12.04, "y": -133.69}]}, "anna": {"translate": [{"y": -0.73, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.5, "y": -2.27, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": 1.51, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2.3333, "y": -0.73, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 2.8333, "y": -2.27, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": 1.51, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 4.6667, "y": -0.73, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 5.1667, "y": -2.27, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "y": 1.51, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 7, "y": -0.73}]}, "toc7": {"rotate": [{"angle": 9.37, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.6667, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 20.18, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2.3333, "angle": 9.37, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 3, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 20.18, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 4.6667, "angle": 9.37, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 5.3333, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 6.5, "angle": 20.18, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 7, "angle": 9.37}]}, "toc4": {"rotate": [{"angle": 4.05, "curve": 0.376, "c2": 0.51, "c3": 0.749}, {"time": 0.5667, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 15.42, "curve": 0.249, "c3": 0.626, "c4": 0.51}, {"time": 2.3333, "angle": 4.05, "curve": 0.376, "c2": 0.51, "c3": 0.749}, {"time": 2.9, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "angle": 15.42, "curve": 0.249, "c3": 0.626, "c4": 0.51}, {"time": 4.6667, "angle": 4.05, "curve": 0.376, "c2": 0.51, "c3": 0.749}, {"time": 5.2333, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 6.4, "angle": 15.42, "curve": 0.249, "c3": 0.626, "c4": 0.51}, {"time": 7, "angle": 4.05}]}, "toc5": {"rotate": [{"angle": -3.92, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.4333, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 1.26, "curve": 0.244, "c3": 0.642, "c4": 0.57}, {"time": 2.3333, "angle": -3.92, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 2.7667, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": 1.26, "curve": 0.244, "c3": 0.642, "c4": 0.57}, {"time": 4.6667, "angle": -3.92, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 5.1, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 6.2667, "angle": 1.26, "curve": 0.244, "c3": 0.642, "c4": 0.57}, {"time": 7, "angle": -3.92}]}, "elsa3": {"translate": [{"y": -6.48, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.2, "y": -7.26, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "y": 0.91, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2.3333, "y": -6.48, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.5333, "y": -7.26, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "y": 0.91, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4.6667, "y": -6.48, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 4.8667, "y": -7.26, "curve": 0.25, "c3": 0.75}, {"time": 6.0333, "y": 0.91, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 7, "y": -6.48}]}, "toc2": {"rotate": [{"angle": -5.07, "curve": 0.38, "c2": 0.6, "c3": 0.725}, {"time": 0.3, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 0.9, "curve": 0.242, "c3": 0.665, "c4": 0.66}, {"time": 2.3333, "angle": -5.07, "curve": 0.38, "c2": 0.6, "c3": 0.725}, {"time": 2.6333, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "angle": 0.9, "curve": 0.242, "c3": 0.665, "c4": 0.66}, {"time": 4.6667, "angle": -5.07, "curve": 0.38, "c2": 0.6, "c3": 0.725}, {"time": 4.9667, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 6.1333, "angle": 0.9, "curve": 0.242, "c3": 0.665, "c4": 0.66}, {"time": 7, "angle": -5.07}]}, "toc3": {"rotate": [{"angle": -6.06, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.1667, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.87, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 2.3333, "angle": -6.06, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 2.5, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -0.87, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 4.6667, "angle": -6.06, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 4.8333, "angle": -6.49, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -0.87, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 7, "angle": -6.06}]}, "elsa4": {"rotate": [{"angle": 2.46, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 10.99, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 2.3333, "angle": 2.46, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 10.99, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 4.6667, "angle": 2.46, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": 10.99, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 7, "angle": 2.46}]}, "elsa7": {"rotate": [{"angle": 0.01, "curve": 0.357, "c2": 0.65, "c3": 0.692}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 0.26, "curve": 0.246, "c3": 0.716, "c4": 0.85}, {"time": 2.3333, "angle": 0.01, "curve": 0.357, "c2": 0.65, "c3": 0.692}, {"time": 2.4333, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": 0.26, "curve": 0.246, "c3": 0.716, "c4": 0.85}, {"time": 4.6667, "angle": 0.01, "curve": 0.357, "c2": 0.65, "c3": 0.692}, {"time": 4.7667, "curve": 0.25, "c3": 0.75}, {"time": 5.9333, "angle": 0.26, "curve": 0.246, "c3": 0.716, "c4": 0.85}, {"time": 7, "angle": 0.01}]}, "elsa5": {"rotate": [{"angle": 0.17, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.14, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 2.3333, "angle": 0.17, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 2.14, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 4.6667, "angle": 0.17, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 2.14, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 7, "angle": 0.17}]}, "elsa6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 5.52, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 5.52, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 5.52, "curve": 0.25, "c3": 0.75}, {"time": 7}]}, "kri": {"scale": [{"x": 1.004, "y": 1.004, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 1, "x": 1.05, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 2.3333, "x": 1.004, "y": 1.004, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 3.3333, "x": 1.05, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 4.5, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 4.6667, "x": 1.004, "y": 1.004, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 5.6667, "x": 1.05, "y": 1.05, "curve": 0.25, "c3": 0.75}, {"time": 6.8333, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 7, "x": 1.004, "y": 1.004}]}, "tuan_loc2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 6.15, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": "stepped"}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 6.15, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": "stepped"}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": 6.15, "curve": 0.25, "c3": 0.75}, {"time": 5}]}, "tuan_loc5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -8.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -8.26, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": "stepped"}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -8.26, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": "stepped"}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "angle": -8.26, "curve": 0.25, "c3": 0.75}, {"time": 5}]}, "tuan_loc": {"rotate": [{"angle": -0.55, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.1667, "angle": -0.66, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.81, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 2.3333, "angle": -0.55, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 2.5, "angle": -0.66, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 0.81, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 4.6667, "angle": -0.55, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 4.8333, "angle": -0.66, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": 0.81, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 7, "angle": -0.55}]}, "tuan_loc3": {"rotate": [{"angle": -7.18, "curve": 0.378, "c2": 0.61, "c3": 0.721}, {"time": 0.2667, "angle": -8.71, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": 0.81, "curve": 0.242, "c3": 0.672, "c4": 0.69}, {"time": 2.3333, "angle": -7.18, "curve": 0.378, "c2": 0.61, "c3": 0.721}, {"time": 2.6, "angle": -8.71, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "angle": 0.81, "curve": 0.242, "c3": 0.672, "c4": 0.69}, {"time": 4.6667, "angle": -7.18, "curve": 0.378, "c2": 0.61, "c3": 0.721}, {"time": 4.9333, "angle": -8.71, "curve": 0.25, "c3": 0.75}, {"time": 6.1, "angle": 0.81, "curve": 0.242, "c3": 0.672, "c4": 0.69}, {"time": 7, "angle": -7.18}]}, "txt": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.25, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -2.08, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.14, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": "stepped"}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": 5.25, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -2.08, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "angle": -1.14, "curve": 0.25, "c3": 0.75}, {"time": 3, "curve": "stepped"}, {"time": 4.6667, "curve": 0.25, "c3": 0.75}, {"time": 4.8667, "angle": 5.25, "curve": 0.25, "c3": 0.75}, {"time": 5, "angle": -2.08, "curve": 0.25, "c3": 0.75}, {"time": 5.1, "angle": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 5.1667, "angle": -1.14, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "olaf": {"rotate": [{"angle": 1.82, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 1.13, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 1.82, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 1.13, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "angle": 1.82, "curve": 0.25, "c3": 0.75}, {"time": 5.8333, "angle": 1.13, "curve": 0.25, "c3": 0.75}, {"time": 7, "angle": 1.82}]}, "olaf8": {"rotate": [{"angle": -19.61, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -48.34, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2.3333, "angle": -19.61, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -48.34, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 4.6667, "angle": -19.61, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 5.1667, "curve": 0.25, "c3": 0.75}, {"time": 6.3333, "angle": -48.34, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 7, "angle": -19.61}]}, "olaf6": {"rotate": [{"angle": -0.94, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -12.12, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 2.3333, "angle": -0.94, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -12.12, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 4.6667, "angle": -0.94, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 4.8333, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -12.12, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 7, "angle": -0.94}]}, "olaf4": {"rotate": [{"angle": 0.9, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.1667, "angle": 1.86, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -10.61, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 2.3333, "angle": 0.9, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 2.5, "angle": 1.86, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -10.61, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 4.6667, "angle": 0.9, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 4.8333, "angle": 1.86, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -10.61, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 7, "angle": 0.9}]}, "olaf5": {"rotate": [{"angle": -6.25, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -27.86, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 2.3333, "angle": -6.25, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -27.86, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 4.6667, "angle": -6.25, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 5, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": -27.86, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 7, "angle": -6.25}]}, "olaf2": {"rotate": [{"angle": -1.67, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.3333, "angle": 1.86, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -13.9, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 2.3333, "angle": -1.67, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 2.6667, "angle": 1.86, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -13.9, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 4.6667, "angle": -1.67, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 5, "angle": 1.86, "curve": 0.25, "c3": 0.75}, {"time": 6.1667, "angle": -13.9, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 7, "angle": -1.67}]}, "olaf3": {"rotate": [{"angle": 1.38, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.1667, "angle": 1.82, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.8, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 2.3333, "angle": 1.38, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 2.5, "angle": 1.82, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -3.8, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 4.6667, "angle": 1.38, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 4.8333, "angle": 1.82, "curve": 0.25, "c3": 0.75}, {"time": 6, "angle": -3.8, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 7, "angle": 1.38}]}}}}}