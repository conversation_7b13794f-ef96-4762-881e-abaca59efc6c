{"skeleton": {"hash": "ZmRjl6MB+pOjArISesjHyqOGOIY", "spine": "3.8.75", "x": -166.99, "y": -93.54, "width": 302.59, "height": 408.8, "images": "C:/Users/<USER>/PSD/out/xvipp15.win/spines/19_skeleton/png", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "scaleX": 1.106, "scaleY": 1.106}, {"name": "1", "parent": "bone", "length": 37.71, "rotation": 131.79, "x": -48.72, "y": -28.6, "scaleX": 1.225, "scaleY": 1.225}, {"name": "2", "parent": "bone", "length": 58.52, "rotation": 90.34, "x": 0.84, "y": -39.18}, {"name": "line", "parent": "2", "rotation": 41.45, "x": 93.05, "y": 67.48}, {"name": "3", "parent": "bone", "length": 42.33, "rotation": 49.9, "x": 44.21, "y": -37.74, "scaleX": 1.055, "scaleY": 1.055}], "slots": [{"name": "bg11", "bone": "root", "attachment": "bg11"}, {"name": "2", "bone": "2", "attachment": "2"}, {"name": "1", "bone": "1", "attachment": "1"}, {"name": "3", "bone": "3", "attachment": "3"}, {"name": "line", "bone": "line", "attachment": "line", "blend": "additive"}, {"name": "kc", "bone": "root", "attachment": "kc"}], "skins": [{"name": "default", "attachments": {"kc": {"kc": {"y": -55.62, "width": 226, "height": 48}}, "bg11": {"bg11": {"x": 0.5, "y": 110.86, "scaleX": 0.7, "scaleY": 0.7, "width": 386, "height": 584}}, "1": {"1": {"x": 21.14, "y": -0.64, "rotation": -131.79, "width": 58, "height": 54}}, "2": {"2": {"x": 65.03, "y": -0.08, "rotation": -90.34, "width": 177, "height": 142}}, "3": {"3": {"x": 28.6, "y": 1.66, "rotation": -49.9, "width": 68, "height": 63}}, "line": {"line": {"x": 3.65, "y": 11.89, "rotation": -131.79, "width": 145, "height": 117}}}}], "animations": {"animation": {"slots": {"line": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.5, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "color": "ffffff00"}]}}, "bones": {"line": {"scale": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "y": 1.64, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "y": 1.64}]}, "1": {"rotate": [{"angle": -2.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -11.36, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -11.36, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 3.1667, "angle": -2.1}], "scale": [{"time": 0.1333}, {"time": 0.1667, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 1.103, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "2": {"rotate": [{"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -5.73, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -5.73, "curve": 0.25, "c3": 0.75}, {"time": 3.1667}], "scale": [{"x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 1.103, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "3": {"rotate": [{"angle": -4.67, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.5, "curve": "stepped"}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.73, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": -5.73, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.1667, "angle": -4.67}], "scale": [{"time": 0.4667}, {"time": 0.5, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 1.103, "curve": 0.25, "c3": 0.75}, {"time": 1}]}}}}}