{"skeleton": {"hash": "wdmXHB5JIRpbx9XEc+dyCevvb8M", "spine": "3.8.75", "x": -313.77, "y": -2.28, "width": 647, "height": 157.37, "images": "../UI GAME/out/xoc.vin/spines/88_logo/PNG/", "audio": ""}, "bones": [{"name": "root"}, {"name": "bg_logo", "parent": "root", "x": 4.71, "y": 32.56}, {"name": "bg_logo3", "parent": "bg_logo", "length": 78.39, "rotation": -178.64, "x": -34.52, "y": 2.8}, {"name": "bg_logo2", "parent": "bg_logo3", "length": 76.51, "rotation": -1.36, "x": 82.1, "y": -1.02}, {"name": "bg_logo5", "parent": "bg_logo2", "length": 66.56, "rotation": -5.63, "x": 77.44, "y": -2.8}, {"name": "bg_logo4", "parent": "bg_logo5", "length": 52.28, "rotation": 7.68, "x": 65.73, "y": -1.02}, {"name": "bg_logo7", "parent": "bg_logo", "length": 61.81, "x": 11.78, "y": -6.07}, {"name": "bg_logo6", "parent": "bg_logo7", "length": 80.21, "rotation": 1.75, "x": 61.81, "y": -1.22}, {"name": "bg_logo9", "parent": "bg_logo6", "length": 83.88, "rotation": -0.08, "x": 79.6, "y": 0.02}, {"name": "bg_logo8", "parent": "bg_logo9", "length": 57.62, "rotation": 10.59, "x": 90.66, "y": 1.64}, {"name": "XX2", "parent": "root", "x": -201.96, "y": 38.91}, {"name": "XX3", "parent": "root", "x": 219.31, "y": 36}], "slots": [{"name": "bg_logo", "bone": "bg_logo8", "attachment": "bg_logo"}, {"name": "XX2", "bone": "XX2", "attachment": "XX2"}, {"name": "XX3", "bone": "XX3", "attachment": "XX3"}], "skins": [{"name": "default", "attachments": {"bg_logo": {"bg_logo": {"type": "mesh", "uvs": [0.33604, 0.62365, 0.2766, 0.51218, 0.23499, 0.50819, 0.21616, 0.6356, 0.21814, 0.75106, 0.18644, 0.56792, 0.13492, 0.52014, 0.08043, 0.56792, 7e-05, 0.59977, 0, 0.86254, 0.01988, 0.86652, 0.05852, 0.80282, 0.10806, 0.84661, 0.15066, 0.93818, 0.21604, 0.98198, 0.28837, 0.9979, 0.31016, 0.95411, 0.31611, 0.87448, 0.35177, 0.95013, 0.39041, 1, 0.62224, 1, 0.65196, 0.93818, 0.68763, 0.84661, 0.70348, 0.88244, 0.69159, 0.94216, 0.70943, 0.98994, 0.73915, 0.98198, 0.76392, 0.92226, 0.82534, 0.93022, 0.86497, 0.86652, 0.93234, 0.85059, 0.98782, 0.87846, 1, 0.8267, 1, 0.5918, 0.93531, 0.5918, 0.8719, 0.54005, 0.79661, 0.61171, 0.78571, 0.69134, 0.77481, 0.50421, 0.69655, 0.51616, 0.70447, 0.38875, 0.63413, 0.23746, 0.46075, 0.02247, 0.29728, 0.21357, 0.31115, 0.3569, 0.31214, 0.48033, 0.0187, 0.7431, 0.05634, 0.6993, 0.12272, 0.75902, 0.20495, 0.8267, 0.27034, 0.84263, 0.30997, 0.71921, 0.39815, 0.75902, 0.49127, 0.78689, 0.58044, 0.79883, 0.66663, 0.75106, 0.74094, 0.73115, 0.79443, 0.8267, 0.85982, 0.73513, 0.94007, 0.71124, 0.49226, 0.54801, 0.47146, 0.2932, 0.39121, 0.3569, 0.58935, 0.39274], "triangles": [59, 34, 33, 36, 35, 34, 58, 36, 34, 58, 34, 59, 59, 33, 32, 30, 58, 59, 30, 59, 32, 30, 29, 58, 31, 30, 32, 38, 37, 39, 56, 39, 37, 37, 36, 58, 58, 56, 37, 57, 56, 58, 22, 56, 57, 29, 57, 58, 27, 23, 57, 28, 27, 57, 29, 28, 57, 26, 24, 27, 55, 60, 39, 55, 39, 56, 22, 55, 56, 54, 55, 22, 23, 22, 57, 21, 54, 22, 24, 23, 27, 25, 24, 26, 20, 54, 21, 53, 54, 20, 63, 61, 41, 63, 41, 40, 39, 63, 40, 60, 63, 39, 55, 54, 60, 47, 8, 7, 46, 8, 47, 5, 47, 7, 46, 47, 48, 11, 46, 48, 9, 8, 46, 9, 46, 11, 10, 9, 11, 7, 6, 5, 48, 5, 4, 48, 47, 5, 12, 11, 48, 12, 48, 49, 2, 1, 3, 3, 1, 0, 51, 3, 0, 4, 3, 51, 49, 48, 4, 50, 4, 51, 50, 51, 52, 49, 4, 50, 13, 12, 49, 16, 50, 17, 50, 13, 49, 14, 50, 16, 14, 13, 50, 15, 14, 16, 61, 43, 42, 62, 44, 43, 61, 62, 43, 45, 44, 62, 63, 62, 61, 45, 62, 60, 0, 45, 60, 52, 0, 60, 51, 0, 52, 17, 50, 52, 53, 18, 17, 53, 17, 52, 19, 18, 53, 19, 53, 20, 41, 61, 42, 60, 62, 63, 53, 52, 60, 60, 54, 53], "vertices": [3, 1, -101.07, 25.75, 0.0362, 2, 65.98, -24.53, 0.78982, 3, -15.56, -23.88, 0.17397, 2, 2, 104, -43.39, 0.00133, 3, 22.9, -41.83, 0.99867, 1, 3, 49.82, -42.47, 1, 1, 3, 62, -21.96, 1, 2, 3, 60.72, -3.37, 0.99031, 4, -16.58, -2.21, 0.00969, 2, 4, 6.72, -29.54, 0.94254, 5, -62.28, -20.38, 0.05746, 2, 4, 40.65, -33.92, 0.57569, 5, -29.25, -29.26, 0.42431, 1, 5, 6.26, -22.83, 1, 1, 5, 58.41, -19.56, 1, 1, 5, 59.96, 22.71, 1, 2, 4, 109.25, 28.88, 0.00705, 5, 47.13, 23.81, 0.99295, 2, 4, 85.38, 16.22, 0.36715, 5, 21.78, 14.46, 0.63284, 2, 3, 131.95, 12.01, 0.03093, 4, 52.79, 20.09, 0.96907, 2, 3, 104.38, 26.76, 0.47519, 4, 23.91, 32.06, 0.52481, 2, 2, 144.97, 31.3, 0.00028, 3, 62.08, 33.81, 0.99972, 2, 2, 98.25, 34.98, 0.155, 3, 15.28, 36.37, 0.845, 2, 2, 83.98, 28.26, 0.31045, 3, 1.18, 29.32, 0.68955, 2, 2, 79.83, 15.54, 0.63492, 3, -2.66, 16.5, 0.36508, 1, 2, 57.05, 28.26, 1, 3, 1, -65.89, -34.84, 0.00055, 2, 32.25, 36.88, 0.99902, 6, -77.67, -28.77, 0.00043, 1, 7, 9.67, -27.85, 1, 1, 7, 29.19, -18.49, 1, 1, 7, 52.71, -4.46, 1, 2, 7, 62.78, -10.54, 0.97112, 8, -16.8, -10.58, 0.02888, 2, 7, 54.8, -19.91, 0.96774, 8, -24.77, -19.97, 0.03226, 2, 7, 66.1, -27.95, 0.8728, 8, -13.46, -27.99, 0.1272, 2, 7, 85.36, -27.26, 0.45862, 8, 5.8, -27.27, 0.54138, 2, 7, 101.67, -18.14, 0.05764, 8, 22.1, -18.13, 0.94236, 1, 8, 61.78, -20.57, 1, 2, 8, 87.71, -11.07, 0.59324, 9, -5.23, -11.95, 0.40676, 1, 9, 37.9, -18.7, 1, 1, 9, 72.03, -30.71, 1, 1, 9, 81.5, -24.24, 1, 1, 9, 89.53, 12.71, 1, 1, 9, 48.63, 21.61, 1, 2, 8, 93.73, 41.34, 0.20877, 9, 10.32, 38.46, 0.79123, 3, 7, 124.34, 31.19, 8e-05, 8, 44.7, 31.23, 0.96005, 9, -39.74, 37.54, 0.03988, 4, 6, 178.09, 20.93, 0.00199, 7, 116.9, 18.59, 0.01633, 8, 37.28, 18.62, 0.97891, 9, -49.35, 26.51, 0.00277, 4, 1, 182.82, 44.98, 0.00031, 6, 171.04, 51.05, 0.0225, 7, 110.77, 48.92, 0.13378, 8, 31.11, 48.94, 0.84342, 4, 1, 132.18, 43.06, 0.05301, 6, 120.4, 49.13, 0.38825, 7, 60.1, 48.54, 0.42781, 8, -19.56, 48.5, 0.13092, 5, 1, 137.31, 63.57, 0.09328, 2, -173.23, -56.66, 2e-05, 6, 125.53, 69.64, 0.5669, 7, 65.85, 68.89, 0.30509, 8, -13.84, 68.85, 0.03471, 4, 1, 91.8, 87.93, 0.22251, 2, -128.31, -82.1, 0.02501, 6, 80.02, 94, 0.67977, 7, 21.1, 94.62, 0.07272, 3, 1, -20.38, 122.54, 0.4391, 2, -16.99, -119.37, 0.43105, 6, -32.16, 128.61, 0.12985, 2, 1, -126.14, 91.77, 0.17313, 2, 89.48, -91.13, 0.82687, 2, 1, -117.17, 68.7, 0.15585, 2, 81.05, -67.85, 0.84415, 3, 1, -116.53, 48.82, 0.12023, 2, 80.89, -47.96, 0.87374, 3, -0.1, -46.96, 0.00603, 2, 4, 111.96, 9.18, 0.00167, 5, 47.18, 3.93, 0.99833, 1, 5, 22.59, -2.25, 1, 2, 3, 122.46, -2.09, 0.01289, 4, 44.73, 5.12, 0.98711, 1, 3, 69.25, 8.81, 1, 2, 2, 109.31, 9.71, 0.0303, 3, 26.95, 11.37, 0.9697, 3, 1, -117.93, 10.36, 0.00126, 2, 83.21, -9.55, 0.15358, 3, 1.31, -8.5, 0.84517, 2, 1, -60.88, 3.95, 0.00257, 2, 26.33, -1.78, 0.99743, 2, 1, -0.63, -0.53, 0.9901, 2, -33.8, 4.14, 0.0099, 2, 1, 57.06, -2.46, 0.00806, 6, 45.28, 3.62, 0.99194, 4, 1, 112.83, 5.24, 0.00847, 6, 101.05, 11.31, 0.13344, 7, 39.6, 11.33, 0.85486, 8, -40.02, 11.26, 0.00323, 4, 1, 160.9, 8.44, 0.00076, 6, 149.12, 14.52, 0.0143, 7, 87.75, 13.07, 0.2153, 8, 8.13, 13.06, 0.76964, 1, 8, 42.28, -3.33, 1, 2, 8, 85, 10.18, 0.4893, 9, -4, 9.43, 0.5107, 1, 9, 47.56, 2.16, 1, 3, 1, 0.01, 37.93, 0.57982, 2, -35.36, -34.3, 0.2483, 6, -11.77, 44, 0.17188, 3, 1, -13.45, 78.95, 0.45887, 2, -22.88, -75.63, 0.39486, 6, -25.23, 85.03, 0.14627, 3, 1, -65.37, 68.7, 0.25109, 2, 29.27, -66.61, 0.73551, 6, -77.15, 74.77, 0.0134, 4, 1, 62.83, 62.93, 0.28126, 2, -98.75, -57.79, 0.04698, 6, 51.05, 69, 0.65592, 7, -8.62, 70.52, 0.01584], "hull": 46, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 0, 90], "width": 647, "height": 161}}, "XX2": {"XX2": {"x": -1.39, "y": 1.31, "width": 53, "height": 55}}, "XX3": {"XX3": {"x": -1.52, "y": 1.21, "width": 51, "height": 51}}}}], "animations": {"animation": {"bones": {"bg_logo9": {"rotate": [{"angle": -7.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -14.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -7.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -14.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -7.26}]}, "bg_logo8": {"rotate": [{"angle": -22.69, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -31.68, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -22.69, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -31.68, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -22.69}]}, "bg_logo7": {"rotate": [{"angle": -0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -3, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -3, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": -0.29}]}, "bg_logo6": {"rotate": [{"angle": -0.85, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.85, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -3, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -0.85}]}, "bg_logo5": {"rotate": [{"angle": 11.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 23.29, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 11.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 23.29, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 11.65}]}, "XX3": {"rotate": [{}, {"time": 1, "angle": 180}, {"time": 2}, {"time": 3, "angle": 180}, {"time": 4}], "translate": [{"y": 23.95, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "y": 84.39, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.8333, "y": 76.25, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "y": 23.95, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "y": 84.39, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2.8333, "y": 76.25, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 3.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "y": 23.95}]}, "XX2": {"rotate": [{}, {"time": 1, "angle": 180}, {"time": 2}, {"time": 3, "angle": 180}, {"time": 4}], "translate": [{"y": 76.25, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "y": 84.39, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "y": 76.25, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "y": 84.39, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "y": 76.25}]}, "bg_logo4": {"rotate": [{"angle": 17.84, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 24.91, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 17.84, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 24.91, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 17.84}]}, "bg_logo3": {"rotate": [{"angle": 0.56, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 5.82, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 0.56, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 5.82, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 0.56}]}, "bg_logo2": {"rotate": [{"angle": 2.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.96, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 2.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 7.96, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 2.26}]}}}}}