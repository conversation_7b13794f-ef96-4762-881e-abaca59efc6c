{"skeleton": {"hash": "RSTVWPYEKTvjg0Xdgcu2HkwuPww", "spine": "3.8.75", "x": -115.69, "y": -72.59, "width": 231.6, "height": 350.4, "images": "C:/Users/<USER>/PSD/out/xvipp15.win/spines/19_skeleton/png/PNG", "audio": "C:/Users/<USER>/OneDrive/M<PERSON>y t<PERSON>h/Backup/2020.12.01/game susi/spine/icon game 2/minipoker"}, "bones": [{"name": "root"}, {"name": "khung", "parent": "root", "x": -16.54, "y": 10.45}, {"name": "A", "parent": "khung", "length": 20.32, "rotation": -98.53, "x": 33.26, "y": 16.64}, {"name": "5", "parent": "khung", "length": 18.47, "rotation": -94.16, "x": -17.33, "y": 20.99}, {"name": "4", "parent": "khung", "length": 14.06, "rotation": -102.38, "x": -49.82, "y": 25.01}, {"name": "can", "parent": "khung", "length": 39.05, "rotation": 107.97, "x": 90.36, "y": 2.51}, {"name": "Mini", "parent": "root", "x": -3.87, "y": -17.07}, {"name": "POKER", "parent": "root", "x": 2.78, "y": -44.02}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "khung", "bone": "khung", "attachment": "khung"}, {"name": "4", "bone": "4", "attachment": "4"}, {"name": "5", "bone": "5", "attachment": "5"}, {"name": "A", "bone": "A", "attachment": "A"}, {"name": "can", "bone": "can", "attachment": "can"}, {"name": "Mini", "bone": "Mini", "attachment": "Mini"}, {"name": "POKER", "bone": "POKER", "attachment": "POKER"}], "skins": [{"name": "default", "attachments": {"A": {"A": {"x": 12.62, "y": -2.76, "rotation": 98.53, "width": 52, "height": 68}}, "bg": {"bg": {"x": 0.11, "y": 102.61, "scaleX": 0.6, "scaleY": 0.6, "width": 386, "height": 584}}, "can": {"can": {"x": 32.5, "y": 4.4, "rotation": -107.97, "width": 43, "height": 67}}, "POKER": {"POKER": {"x": -5.17, "y": 1.03, "width": 133, "height": 42}}, "khung": {"khung": {"x": 19.65, "y": -0.94, "width": 174, "height": 135}}, "4": {"4": {"x": 5.16, "y": -0.43, "rotation": 102.38, "width": 32, "height": 43}}, "5": {"5": {"x": 6.88, "y": 0.98, "rotation": 94.16, "width": 37, "height": 53}}, "Mini": {"Mini": {"x": -3.52, "y": 3.58, "width": 83, "height": 41}}}}], "animations": {"animation": {"bones": {"A": {"translate": [{"time": 0.9}, {"time": 1.1333, "y": -13.76}, {"time": 1.3333}], "scale": [{"time": 1.8667}, {"time": 1.9333, "x": 1.141, "y": 1.141}, {"time": 2}, {"time": 2.0667, "x": 1.141, "y": 1.141}, {"time": 2.1333}]}, "can": {"rotate": [{"time": 1.3333}, {"time": 1.4, "angle": 145, "curve": "stepped"}, {"time": 1.5, "angle": 145}, {"time": 1.6}]}, "Mini": {"scale": [{}, {"time": 0.0667, "x": 1.152, "y": 1.152}, {"time": 0.1667}, {"time": 0.2667, "x": 1.152, "y": 1.152}, {"time": 0.3333}]}, "4": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "y": -5.95, "curve": 0.25, "c3": 0.75}, {"time": 0.4333}], "scale": [{"time": 1.5333}, {"time": 1.6, "x": 1.141, "y": 1.141}, {"time": 1.6667}, {"time": 1.7333, "x": 1.141, "y": 1.141}, {"time": 1.8}]}, "5": {"translate": [{"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 2.87, "y": 6.7, "curve": 0.25, "c3": 0.75}, {"time": 0.9}], "scale": [{"time": 1.7}, {"time": 1.7667, "x": 1.141, "y": 1.141}, {"time": 1.8333}, {"time": 1.9, "x": 1.141, "y": 1.141}, {"time": 1.9667}]}, "khung": {"translate": [{"y": 0.65, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 1, "y": 8.38, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 2.2, "y": 0.65}]}, "POKER": {"scale": [{"time": 0.3333}, {"time": 0.4, "x": 1.152, "y": 1.152}, {"time": 0.5}, {"time": 0.6, "x": 1.152, "y": 1.152}, {"time": 0.6667}]}}}}}