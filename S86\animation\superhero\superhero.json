{"skeleton": {"hash": "Wn6gSWxHPXBwpBkVzmWsF4l+/Yc", "spine": "3.8.75", "x": -179.6, "y": -233.5, "width": 358.52, "height": 467.2}, "bones": [{"name": "root"}, {"name": "bone6", "parent": "root", "length": 72.62, "rotation": 180, "x": -119.32, "y": 3.94}, {"name": "than2", "parent": "bone6", "x": -44.63, "y": -8.7}, {"name": "chan", "parent": "than2", "x": -11.71, "y": -11.11}, {"name": "chan3", "parent": "chan", "length": 34.79, "rotation": 74.48, "x": 2.93, "y": 7.45}, {"name": "chan2", "parent": "chan3", "length": 23.55, "rotation": -11.33, "x": 39.94, "y": 0.33}, {"name": "than4", "parent": "than2", "length": 50.35, "rotation": -117.77, "x": 26.55, "y": -10.82}, {"name": "dau1", "parent": "than4", "length": 48.67, "rotation": 21.82, "x": 32.9, "y": -16.58}, {"name": "toc1", "parent": "dau1", "x": 40.17, "y": -2.92}, {"name": "toc3", "parent": "toc1", "length": 7.85, "rotation": 54.46, "x": 1.79, "y": 2.46}, {"name": "toc2", "parent": "toc3", "length": 12.99, "rotation": 54.57, "x": 7.4, "y": 0.51}, {"name": "toc5", "parent": "toc2", "length": 10.98, "rotation": 23.56, "x": 14.97, "y": -0.46}, {"name": "toc4", "parent": "toc5", "length": 9.34, "rotation": -4.48, "x": 11.66, "y": -0.22}, {"name": "toc7", "parent": "toc4", "length": 9.51, "rotation": -50.16, "x": 9.32, "y": -1.32}, {"name": "toc", "parent": "dau1", "x": 35.99, "y": -2.97}, {"name": "toc8", "parent": "toc", "length": 10.5, "rotation": -2.52, "x": 8, "y": -4.28}, {"name": "toc6", "parent": "toc8", "length": 10.19, "rotation": 12.2, "x": 14.28, "y": 0.12}, {"name": "toc10", "parent": "toc6", "length": 10.83, "rotation": -3.73, "x": 13.89, "y": -0.91}, {"name": "toc9", "parent": "toc10", "length": 8.53, "rotation": -31.22, "x": 12.82, "y": -1.77}, {"name": "toc12", "parent": "toc1", "length": 11.94, "rotation": 7.02, "x": 8.14, "y": 13.9}, {"name": "toc11", "parent": "toc12", "length": 10.84, "rotation": 19.22, "x": 15.03, "y": -0.06}, {"name": "toc14", "parent": "toc11", "length": 6.91, "rotation": -12.93, "x": 13.21, "y": 1.95}, {"name": "tay", "parent": "than4", "x": 60.3, "y": 27.26}, {"name": "tay3", "parent": "tay", "length": 37.82, "rotation": 107.39, "x": -1.52, "y": 5.94}, {"name": "tay2", "parent": "tay3", "length": 18.4, "rotation": 19.26, "x": 39.55, "y": 0.03}, {"name": "tay5", "parent": "tay2", "length": 17.29, "rotation": 5.38, "x": 19.05, "y": 0.47}, {"name": "tay4", "parent": "than4", "length": 107.39, "rotation": -70.31, "x": 59.24, "y": -54.76}, {"name": "sung23", "parent": "tay4", "length": 49.08, "rotation": 24.59, "x": 17.85, "y": -84.32}, {"name": "sung3", "parent": "tay4", "length": 47.06, "rotation": 8.09, "x": 156.15, "y": -62.32}, {"name": "sung1", "parent": "tay4", "length": 46.51, "rotation": -68.92, "x": 114.04, "y": 105.99}, {"name": "sung2", "parent": "than4", "length": 42.03, "rotation": 59.27, "x": 75.84, "y": 1.84}, {"name": "than1", "parent": "root", "x": 96.06, "y": 198.16}, {"name": "canh1", "parent": "than1", "length": 12.59, "rotation": 27.3, "x": 11.91, "y": 0.36}, {"name": "canh2", "parent": "than1", "length": 9.39, "rotation": 112.62, "x": -3.97, "y": 5.41}, {"name": "than", "parent": "root", "length": 28.9, "rotation": 113.39, "x": 31.26, "y": 128.69}, {"name": "than5", "parent": "than", "length": 20.97, "rotation": -156.95, "x": 22.13, "y": -16.39}, {"name": "than6", "parent": "than", "length": 19.52, "rotation": 144.48, "x": 24.71, "y": 17.72}, {"name": "dau", "parent": "than", "length": 39.22, "rotation": -28.84, "x": 33.49, "y": -1.01}, {"name": "fx4", "parent": "dau1", "x": 23.06, "y": 15.92}, {"name": "fx7", "parent": "tay4", "rotation": 8.09, "x": 122.02, "y": -6.88, "scaleX": 0.494, "scaleY": 0.613}, {"name": "fx3", "parent": "fx7"}, {"name": "fx6", "parent": "fx7", "x": 30.67, "scaleY": -1}, {"name": "fx5", "parent": "fx6"}, {"name": "logo", "parent": "root", "x": 6.61, "y": -131.3}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "sung2", "bone": "sung2", "attachment": "sung2"}, {"name": "than", "bone": "than6", "attachment": "than"}, {"name": "dau", "bone": "dau", "attachment": "dau"}, {"name": "canh2", "bone": "canh2", "attachment": "canh2"}, {"name": "canh1", "bone": "canh1", "attachment": "canh1"}, {"name": "than1", "bone": "than1", "attachment": "than1"}, {"name": "sung3", "bone": "sung3", "attachment": "sung3"}, {"name": "chan", "bone": "chan2", "attachment": "chan"}, {"name": "tay", "bone": "tay5", "attachment": "tay"}, {"name": "than2", "bone": "than4", "attachment": "than2"}, {"name": "dau1", "bone": "dau1", "attachment": "dau1"}, {"name": "toc", "bone": "toc14", "attachment": "toc"}, {"name": "toc1", "bone": "toc7", "attachment": "toc1"}, {"name": "tay2", "bone": "tay4", "attachment": "tay2"}, {"name": "sung23", "bone": "sung23", "attachment": "sung23"}, {"name": "sung1", "bone": "sung1", "attachment": "sung1"}, {"name": "logo", "bone": "logo", "attachment": "logo"}, {"name": "logo2", "bone": "logo", "color": "ffffff00", "attachment": "logo", "blend": "additive"}, {"name": "fx4", "bone": "fx4", "attachment": "fx4", "blend": "additive"}, {"name": "fx3", "bone": "fx3", "attachment": "fx3", "blend": "additive"}, {"name": "fx1", "bone": "fx5", "attachment": "fx1", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"bg": {"bg": {"x": -0.1, "y": 0.1, "scaleX": 0.8, "scaleY": 0.8, "width": 386, "height": 584}}, "tay2": {"tay2": {"type": "mesh", "uvs": [0.05514, 0.34693, 0.00959, 0.43354, 0, 0.5009, 0.02911, 0.59071, 0.09256, 0.65807, 0.18203, 0.66127, 0.18854, 0.63561, 0.21457, 0.64042, 0.21782, 0.69816, 0.25849, 0.74788, 0.31543, 0.78637, 0.34796, 0.84731, 0.45371, 0.90986, 0.56433, 0.91788, 0.60012, 0.82806, 0.63103, 0.82646, 0.68634, 0.89703, 0.78069, 0.87778, 0.86691, 0.80561, 0.91734, 0.70457, 0.91734, 0.6308, 0.89619, 0.59712, 0.84414, 0.60193, 0.85715, 0.53137, 0.84576, 0.45439, 0.79208, 0.3758, 0.77256, 0.39986, 0.73677, 0.30524, 0.61638, 0.26675, 0.50576, 0.24269, 0.44232, 0.26675, 0.47811, 0.2042, 0.40978, 0.15929, 0.01935, 0.20259, 0.00634, 0.29561, 0.37569, 0.29995, 0.33282, 0.27278, 0.26852, 0.26825, 0.18584, 0.30448, 0.17971, 0.36033, 0.21034, 0.39353, 0.25627, 0.41165, 0.25474, 0.4524, 0.22412, 0.44184, 0.19349, 0.50372, 0.20268, 0.56712, 0.23024, 0.5958, 0.69416, 0.41919, 0.61761, 0.36636, 0.49512, 0.36636, 0.3711, 0.43429, 0.33282, 0.58674, 0.37569, 0.70297, 0.45531, 0.76787, 0.59311, 0.8041, 0.7156, 0.78297, 0.81359, 0.66976, 0.82584, 0.54448], "triangles": [30, 32, 31, 37, 33, 32, 36, 37, 32, 30, 36, 32, 33, 0, 34, 35, 36, 30, 37, 38, 33, 38, 0, 33, 39, 0, 38, 49, 30, 29, 49, 29, 28, 48, 28, 27, 49, 28, 48, 40, 38, 37, 39, 38, 40, 47, 48, 27, 41, 40, 37, 41, 37, 36, 41, 36, 35, 26, 47, 27, 50, 41, 35, 49, 50, 35, 49, 35, 30, 43, 40, 41, 42, 43, 41, 42, 41, 50, 26, 25, 24, 44, 39, 40, 44, 40, 43, 57, 26, 24, 57, 24, 23, 47, 26, 57, 42, 45, 44, 42, 44, 43, 51, 42, 50, 46, 45, 42, 2, 1, 44, 39, 1, 0, 44, 1, 39, 3, 44, 45, 3, 2, 44, 51, 46, 42, 22, 57, 23, 45, 4, 3, 6, 45, 46, 7, 6, 46, 6, 4, 45, 5, 4, 6, 56, 47, 57, 56, 57, 22, 51, 8, 7, 49, 51, 50, 52, 51, 49, 22, 21, 20, 46, 51, 7, 52, 8, 51, 52, 9, 8, 47, 53, 52, 49, 47, 52, 47, 49, 48, 55, 47, 56, 54, 53, 47, 20, 56, 22, 10, 9, 52, 10, 52, 53, 55, 54, 47, 20, 19, 56, 18, 56, 19, 55, 56, 18, 15, 54, 55, 14, 54, 15, 11, 10, 53, 17, 55, 18, 16, 15, 55, 16, 55, 17, 12, 11, 53, 12, 53, 54, 12, 54, 14, 13, 12, 14], "vertices": [2.72, 27.85, -4.12, 8.33, -4.1, -6.09, 4.6, -24.08, 19.74, -36.36, 38.35, -34.4, 38.93, -28.82, 44.46, -29.07, 46.86, -41.09, 56.75, -50.33, 69.68, -56.73, 78.23, -68.57, 101.98, -78.59, 125.11, -77.02, 129.84, -57.12, 136.18, -55.87, 149.73, -69.06, 168.68, -62.24, 184.37, -44.56, 191.79, -21.87, 189.59, -6.39, 184.21, 0.06, 173.58, -2.48, 174.17, 12.71, 169.52, 28.54, 156.07, 43.45, 152.75, 37.83, 142.52, 56.64, 116.46, 61.18, 92.86, 62.98, 80.44, 56.06, 85.98, 70.24, 70.51, 77.66, -8.99, 57.09, -8.91, 37.19, 67.65, 47.13, 57.97, 51.58, 44.53, 50.64, 28.5, 40.6, 28.9, 28.7, 36.22, 22.63, 46.27, 20.18, 47.17, 11.58, 40.51, 12.9, 36.02, -0.99, 39.82, -14.03, 46.37, -19.24, 137.1, 31.47, 119.69, 40.31, 94.34, 36.7, 70.7, 18.8, 67.33, -14.32, 79.67, -37.46, 98.08, -48.74, 127.67, -52.29, 152.39, -44.26, 169.29, -17.61, 168.09, 9.04], "hull": 35}}, "toc": {"toc": {"type": "mesh", "uvs": [0, 0.50109, 0.04282, 0.68703, 0.18023, 0.74782, 0.32363, 0.78715, 0.44013, 0.88369, 0.46104, 0.97309, 0.56859, 0.98024, 0.65223, 0.95163, 0.75679, 1, 0.78367, 0.94091, 0.88823, 0.81933, 1, 0.72636, 0.98681, 0.65127, 0.92408, 0.66557, 0.90317, 0.50824, 0.84342, 0.56545, 0.79264, 0.40097, 0.70302, 0.29012, 0.70302, 0.12921, 0.72393, 0, 0.61937, 0.03982, 0.54768, 0.23291, 0.38935, 0.06842, 0.36246, 0.22933, 0.31168, 0.16139, 0.28479, 0.03982, 0.20712, 0.14351, 0.23998, 0.28654, 0.12049, 0.1757, 0.10854, 0.35806, 0.14737, 0.4117, 0.01294, 0.39739, 0.52378, 0.79072, 0.57755, 0.52969, 0.59547, 0.32945, 0.63431, 0.16854, 0.72692, 0.6763, 0.73588, 0.43672, 0.33856, 0.5726, 0.3296, 0.29012, 0.26687, 0.19357, 0.15036, 0.52612], "triangles": [18, 35, 19, 19, 35, 20, 21, 34, 39, 34, 17, 37, 17, 34, 35, 34, 21, 35, 35, 18, 17, 20, 35, 21, 36, 33, 37, 37, 16, 15, 33, 34, 37, 37, 17, 16, 8, 7, 9, 9, 7, 36, 7, 32, 36, 9, 36, 10, 10, 13, 11, 10, 36, 13, 32, 33, 36, 13, 12, 11, 36, 15, 13, 36, 37, 15, 13, 15, 14, 7, 6, 32, 32, 6, 5, 5, 4, 32, 4, 3, 32, 30, 29, 27, 29, 28, 27, 27, 40, 39, 23, 40, 24, 27, 26, 40, 24, 40, 25, 40, 26, 25, 21, 39, 23, 39, 34, 38, 39, 30, 27, 39, 40, 23, 21, 23, 22, 3, 38, 32, 32, 38, 33, 3, 2, 38, 1, 41, 2, 2, 41, 38, 1, 0, 41, 41, 0, 31, 33, 38, 34, 38, 30, 39, 31, 30, 41, 30, 38, 41], "vertices": [3, 19, 10.52, 28.76, 0.76504, 20, 5.23, 28.69, 0.01459, 21, -13.76, 24.28, 0.22037, 3, 19, -1.81, 25.61, 0.85947, 20, -7.45, 29.78, 6e-05, 21, -26.36, 22.5, 0.14047, 3, 19, -6.02, 14.83, 0.93088, 21, -31.73, 12.24, 0.0684, 14, 4.53, 27.93, 0.00073, 4, 19, -8.83, 3.55, 0.83613, 21, -35.75, 1.34, 0.00483, 14, 3.13, 16.39, 0.15794, 15, -5.78, 20.44, 0.0011, 3, 19, -15.37, -5.54, 0.15057, 14, -2.26, 6.58, 0.84678, 15, -10.72, 10.39, 0.00265, 2, 19, -21.3, -7.08, 0.00767, 14, -7.95, 4.32, 0.99233, 2, 14, -7.54, -4.18, 0.8969, 15, -15.53, -0.58, 0.1031, 2, 14, -4.98, -10.55, 0.49713, 15, -12.69, -6.84, 0.50287, 3, 14, -7.3, -19.1, 0.2572, 15, -14.63, -15.48, 0.74063, 16, -31.56, -9.13, 0.00217, 3, 14, -3.2, -20.81, 0.20514, 15, -10.46, -17.01, 0.78282, 16, -27.8, -11.51, 0.01204, 3, 14, 5.64, -28.19, 0.03006, 15, -1.31, -23.99, 0.86401, 16, -20.33, -20.27, 0.10593, 3, 14, 12.66, -36.34, 0.00022, 15, 6.06, -31.82, 0.8434, 16, -14.78, -29.48, 0.15638, 2, 15, 10.81, -30.06, 0.83889, 16, -9.77, -28.76, 0.16111, 4, 14, 16.03, -29.95, 0.00086, 15, 9.15, -25.3, 0.81358, 16, -10.39, -23.76, 0.18519, 17, -22.75, -24.38, 0.00037, 3, 15, 19.18, -22.14, 0.70668, 16, 0.08, -22.78, 0.28924, 17, -12.36, -22.73, 0.00408, 3, 15, 14.75, -18.02, 0.63898, 16, -3.38, -17.83, 0.34385, 17, -16.14, -18.01, 0.01717, 3, 15, 24.89, -12.46, 0.12402, 16, 7.71, -14.53, 0.65076, 17, -5.28, -14, 0.22522, 4, 15, 31.08, -4.38, 0.00116, 16, 15.47, -7.94, 0.15345, 17, 2.03, -6.92, 0.81411, 18, -6.55, -9.99, 0.03128, 2, 17, 12.65, -6.92, 0.08411, 18, 2.53, -4.49, 0.91589, 1, 18, 10.68, -1.48, 1, 2, 17, 18.55, -0.31, 0.00751, 18, 4.15, 4.22, 0.99249, 3, 20, 6.84, -18.02, 0.23376, 16, 20.04, 4.06, 0.0486, 17, 5.81, 5.35, 0.71764, 2, 20, 21.36, -10.06, 0.77692, 17, 16.67, 17.86, 0.22308, 4, 20, 12.13, -4.38, 0.87357, 21, 0.37, -6.41, 0.03105, 16, 21.23, 18.64, 0.0076, 17, 6.05, 19.98, 0.08777, 3, 20, 17.73, -2.17, 0.08765, 21, 5.33, -3.01, 0.91128, 17, 10.53, 24, 0.00108, 1, 21, 13.56, -1.93, 1, 1, 21, 7.55, 5.03, 1, 3, 19, 24.33, 9.54, 0.06712, 20, 11.94, 6, 0.18591, 21, -2.14, 3.67, 0.74697, 2, 19, 31.82, 18.85, 0.08623, 21, 6.32, 12.09, 0.91377, 3, 19, 19.8, 20.01, 0.24015, 20, 11.11, 17.38, 0.09138, 21, -5.49, 14.57, 0.66847, 3, 19, 16.21, 17.01, 0.4809, 20, 6.73, 15.73, 0.1176, 21, -9.4, 11.98, 0.4015, 3, 19, 17.35, 27.61, 0.73281, 20, 11.3, 25.36, 0.02219, 21, -7.1, 22.39, 0.245, 3, 19, -9.36, -12.26, 0.08995, 14, 4.53, 0.64, 0.55387, 15, -3.68, 4.76, 0.35618, 4, 19, 7.79, -16.82, 0.06461, 20, -12.35, -13.45, 0.00975, 15, 13.99, 3.1, 0.24071, 16, 0.34, 2.98, 0.68493, 4, 19, 20.98, -18.48, 0.00082, 20, -0.45, -19.36, 0.03018, 16, 13.44, 0.7, 0.20437, 17, -0.56, 1.58, 0.76463, 2, 17, 10.06, -1.49, 0.79444, 18, -2.5, -1.19, 0.20556, 4, 14, 13.71, -14.54, 0.00444, 15, 6.15, -10, 0.852, 16, -10.08, -8.17, 0.14207, 17, -23.45, -8.81, 0.00149, 3, 15, 21.9, -8.37, 0.11264, 16, 5.65, -9.9, 0.69543, 17, -7.64, -9.52, 0.19193, 3, 19, 5.31, 2.11, 0.99235, 20, -8.47, 5.24, 0.00026, 21, -21.86, -1.64, 0.0074, 3, 20, 9.27, -0.56, 0.99491, 16, 17.39, 21.49, 0.0009, 17, 2.03, 22.58, 0.00419, 1, 21, 3.67, 0.77, 1, 3, 19, 8.65, 16.91, 0.75366, 20, -0.43, 18.13, 0.05189, 21, -16.92, 12.71, 0.19445], "hull": 32}}, "chan": {"chan": {"type": "mesh", "uvs": [0.07526, 0.0305, 0, 0.34607, 0.10083, 0.52139, 0.07952, 0.68969, 0.07952, 0.84397, 0.1264, 0.97721, 0.36931, 1, 0.62074, 0.90007, 0.75284, 0.81592, 0.93183, 0.61255, 1, 0.4197, 0.86364, 0.37763, 0.96166, 0.1953, 0.83381, 0.04453, 0.7254, 0.02683, 0.56108, 0, 0.20311, 0, 0.55384, 0.38925, 0.36615, 0.69811, 0.22069, 0.44716, 0.40369, 0.22324], "triangles": [3, 2, 18, 18, 17, 8, 4, 3, 18, 7, 18, 8, 5, 4, 18, 6, 18, 7, 5, 18, 6, 20, 16, 15, 17, 20, 15, 11, 17, 14, 20, 0, 16, 19, 20, 17, 20, 1, 0, 19, 1, 20, 2, 1, 19, 9, 11, 10, 18, 19, 17, 2, 19, 18, 9, 8, 17, 9, 17, 11, 11, 13, 12, 17, 15, 14, 11, 14, 13], "vertices": [2, 3, 31.23, -3.93, 0.11639, 4, -3.39, -30.32, 0.88361, 3, 3, 36.12, 21, 0.0016, 4, 21.94, -28.36, 0.99674, 5, -12.01, -31.66, 0.00165, 2, 4, 33.53, -18.34, 0.81729, 5, -2.61, -19.56, 0.18271, 2, 4, 46.71, -16.11, 0.21596, 5, 9.87, -14.79, 0.78404, 2, 4, 58.46, -12.85, 0.01075, 5, 20.75, -9.28, 0.98925, 1, 5, 28.76, -1.81, 1, 1, 5, 23.24, 13.09, 1, 2, 4, 53.31, 22.23, 0.11437, 5, 8.81, 24.1, 0.88563, 3, 3, -12.81, 58.12, 6e-05, 4, 44.61, 28.73, 0.32989, 5, -1, 28.76, 0.67005, 3, 3, -24.45, 42.05, 0.02378, 4, 26.02, 35.64, 0.7164, 5, -20.59, 31.88, 0.25981, 3, 3, -28.88, 26.82, 0.07493, 4, 10.15, 35.83, 0.79776, 5, -36.18, 28.96, 0.12731, 3, 3, -20.01, 23.49, 0.16858, 4, 9.32, 26.4, 0.75937, 5, -35.14, 19.55, 0.07205, 3, 3, -26.39, 9.09, 0.57013, 4, -6.26, 28.68, 0.42738, 5, -50.87, 18.72, 0.00249, 2, 3, -18.08, -2.82, 0.80797, 4, -15.52, 17.49, 0.19203, 2, 3, -11.03, -4.22, 0.90787, 4, -14.98, 10.32, 0.09213, 1, 3, -0.35, -6.34, 1, 2, 3, 22.92, -6.34, 0.19121, 4, -7.93, -22.96, 0.80879, 3, 3, 0.12, 24.41, 0.01896, 4, 15.59, 7.24, 0.96722, 5, -25.23, 2, 0.01382, 1, 5, 2.05, 2.13, 1, 2, 4, 25.8, -12.4, 0.97237, 5, -11.37, -15.26, 0.02763, 2, 3, 9.88, 11.3, 0.0094, 4, 5.57, -5.67, 0.9906], "hull": 17}}, "dau": {"dau": {"x": 21.51, "y": -1.48, "rotation": -84.55, "width": 65, "height": 54}}, "canh1": {"canh1": {"x": 12.75, "y": 0.25, "rotation": -27.3, "width": 29, "height": 23}}, "canh2": {"canh2": {"x": 9.74, "y": 1.25, "rotation": -112.62, "width": 21, "height": 28}}, "sung23": {"sung23": {"type": "mesh", "uvs": [0.01159, 0.39894, 0.05267, 0.18268, 0.14028, 0.2376, 0.30183, 0.17581, 0.36755, 0.04879, 0.49076, 0, 0.74814, 0.00074, 0.69612, 0.27879, 0.77278, 0.55685, 0.87409, 0.69417, 1, 0.84864, 0.98088, 1, 0.54005, 1, 0.20052, 0.95163, 0.01707, 0.85551], "triangles": [7, 5, 6, 0, 1, 2, 13, 14, 0, 3, 0, 2, 0, 8, 13, 3, 4, 5, 7, 3, 5, 0, 3, 8, 8, 3, 7, 12, 8, 9, 11, 12, 9, 12, 13, 8, 10, 11, 9], "vertices": [-1.96, 27.7, 5.46, 40.61, 11.47, 34.99, 25.66, 35.1, 33.37, 41.7, 44.22, 41.89, 64.94, 35.7, 55.45, 19.08, 56.34, -0.61, 61.88, -11.85, 69.08, -24.78, 64.66, -34.05, 29.16, -23.53, 2.73, -12.32, -10.21, -1.76], "hull": 15}}, "fx1": {"fx1": {"x": -0.48, "y": -5.01, "width": 189, "height": 190}}, "than": {"than": {"type": "mesh", "uvs": [0.23344, 0.43518, 0.3125, 0.53638, 0.3125, 0.61791, 0.37331, 0.6882, 0.47974, 0.6882, 0.54816, 0.62635, 0.56336, 0.51389, 0.53447, 0.38176, 0.51927, 0.2918, 0.58921, 0.45767, 0.66067, 0.60386, 0.85832, 0.73318, 0.99516, 0.75004, 0.95411, 0.42674, 0.78382, 0.14842, 0.59833, 0.02754, 0.48734, 0.00786, 0.44629, 0.03597, 0.38852, 0.00786, 0.29425, 0, 0.22279, 0, 0.19847, 0.0922, 0.13613, 0.09782, 0.08748, 0.20184, 0.04491, 0.35927, 0.02058, 0.5645, 0, 0.97495, 0.00233, 1, 0.15285, 0.98619, 0.23952, 0.60948, 0.20759, 0.33116, 0.18478, 0.24401, 0.45085, 0.14842, 0.50255, 0.24682, 0.30794, 0.2412, 0.34138, 0.40706, 0.39612, 0.57012], "triangles": [11, 13, 12, 11, 10, 13, 10, 9, 13, 13, 9, 14, 14, 9, 8, 33, 15, 14, 15, 32, 16, 23, 22, 31, 27, 26, 28, 29, 28, 25, 28, 26, 25, 25, 24, 0, 24, 23, 31, 25, 0, 29, 24, 30, 0, 32, 17, 16, 35, 33, 8, 34, 32, 33, 5, 4, 36, 4, 3, 36, 3, 2, 36, 5, 36, 6, 2, 1, 36, 1, 35, 36, 36, 7, 6, 36, 35, 7, 1, 0, 35, 0, 30, 35, 30, 34, 35, 35, 8, 7, 35, 34, 33, 31, 21, 34, 34, 21, 19, 21, 20, 19, 18, 32, 19, 32, 34, 19, 8, 33, 14, 33, 32, 15, 32, 18, 17, 30, 31, 34, 24, 31, 30, 31, 22, 21], "vertices": [1, 34, 14.99, 13.64, 1, 1, 34, 6.99, 8.66, 1, 2, 34, 3.03, 10.38, 0.98648, 36, 13.31, 18.62, 0.01352, 2, 34, -2.76, 6.39, 0.99988, 36, 15.68, 25.24, 0.00012, 2, 34, -6.9, -3.19, 0.99336, 35, 21.54, -23.52, 0.00664, 1, 34, -6.55, -10.64, 1, 1, 34, -1.67, -14.37, 1, 1, 34, 5.88, -14.56, 1, 1, 34, 10.85, -15.08, 1, 1, 35, 20.9, -7.27, 1, 1, 35, 31.31, -8.06, 1, 1, 35, 50.07, 0.32, 1, 1, 35, 60.4, 8.92, 1, 1, 35, 45.68, 18.56, 1, 1, 35, 23.42, 17.75, 1, 1, 35, 5.83, 9.87, 1, 1, 35, -2.77, 3.13, 1, 1, 34, 26.13, -13.9, 1, 1, 34, 29.75, -9.3, 1, 1, 34, 33.8, -0.98, 1, 1, 34, 36.58, 5.44, 1, 1, 34, 33.04, 9.57, 1, 1, 36, -9.92, -4.15, 1, 2, 34, 32.02, 21.86, 0.01498, 36, -3.52, -7.63, 0.98502, 1, 36, 5.53, -9.93, 1, 1, 36, 16.66, -9.93, 1, 1, 36, 38.34, -7.25, 1, 1, 36, 39.59, -6.74, 1, 1, 36, 35.73, 7.51, 1, 1, 36, 14.41, 11.54, 1, 1, 34, 21.06, 13.78, 1, 1, 34, 26.19, 14, 1, 1, 34, 20.48, -11.95, 1, 1, 34, 13.69, -14.52, 1, 2, 34, 21.53, 2.86, 0.92685, 36, -6.1, 13.92, 0.07315, 2, 34, 12.16, 3.34, 0.95442, 36, 1.79, 19, 0.04558, 2, 34, 2.1, 1.85, 0.99932, 36, 9.09, 26.09, 0.00068], "hull": 30}}, "fx4": {"fx4": {"x": 0.17, "y": -0.07, "rotation": -84.05, "width": 23, "height": 23}}, "logo2": {"logo": {"x": 1.79, "y": -1.61, "width": 263, "height": 182}}, "toc1": {"toc1": {"type": "mesh", "uvs": [1, 0.37651, 0.94497, 0.62657, 0.88527, 0.89789, 0.80527, 0.83513, 0.77277, 0.66134, 0.70027, 0.66616, 0.57027, 0.79168, 0.35778, 0.9703, 0.20778, 1, 0.06528, 0.92685, 0, 0.62754, 0, 0.52616, 0.11528, 0.65168, 0.24528, 0.65651, 0.37278, 0.40065, 0.50527, 0.14961, 0.65027, 0, 0.86527, 0, 1, 0.20754, 0.81027, 0.34754, 0.58277, 0.41996, 0.34528, 0.69996, 0.21028, 0.86409], "triangles": [9, 10, 22, 22, 11, 12, 11, 22, 10, 8, 22, 7, 8, 9, 22, 22, 21, 7, 7, 21, 6, 22, 13, 21, 22, 12, 13, 21, 14, 20, 21, 13, 14, 20, 14, 15, 6, 21, 5, 21, 20, 5, 5, 20, 4, 4, 20, 19, 19, 20, 15, 4, 19, 1, 1, 19, 0, 19, 18, 0, 15, 16, 19, 19, 17, 18, 19, 16, 17, 2, 3, 1, 3, 4, 1], "vertices": [2, 8, 6.07, -3.46, 0.24331, 9, -2.33, -6.92, 0.75669, 2, 8, -1.47, -1.15, 0.99726, 10, -7.05, 9.98, 0.00274, 3, 8, -9.64, 1.36, 0.8901, 9, -7.54, 8.66, 0.0631, 10, -2.02, 16.89, 0.0468, 3, 8, -8.29, 6, 0.75003, 9, -2.98, 10.26, 0.14496, 10, 1.94, 14.11, 0.10501, 3, 8, -3.47, 8.34, 0.25575, 9, 1.73, 7.69, 0.28039, 10, 2.57, 8.79, 0.46386, 4, 8, -4.03, 12.36, 0.03082, 9, 4.68, 10.49, 0.06469, 10, 6.56, 8, 0.88292, 11, -4.33, 11.12, 0.02156, 2, 10, 14.47, 9.9, 0.48339, 11, 3.68, 9.7, 0.51661, 3, 10, 27.23, 12.26, 0.00377, 11, 16.32, 6.76, 0.43706, 12, 4.1, 7.32, 0.55917, 3, 11, 23.58, 2.44, 0.00725, 12, 11.67, 3.58, 0.93197, 13, -2.25, 4.95, 0.06078, 2, 12, 17.3, -2.46, 0.10839, 13, 5.99, 5.4, 0.89161, 1, 13, 12.15, -1.73, 1, 1, 13, 13.06, -4.53, 1, 1, 13, 5.8, -3.06, 1, 2, 12, 4.59, -3.73, 0.7417, 13, -1.17, -5.18, 0.2583, 2, 11, 5.79, -6, 0.85355, 12, -5.4, -6.22, 0.14645, 3, 9, 22.78, 6.51, 0.08187, 10, 13.81, -9.06, 0.33898, 11, -4.51, -7.41, 0.57915, 3, 9, 19.57, -2.12, 0.52876, 10, 4.91, -11.45, 0.38325, 11, -13.61, -6.05, 0.08799, 2, 8, 16.14, 5.17, 0.0002, 9, 10.55, -10.1, 0.9998, 2, 8, 10.94, -2.95, 0.08858, 9, 0.92, -10.59, 0.91142, 1, 9, 6.18, -0.51, 1, 3, 9, 14.33, 9.5, 0.00209, 10, 11.35, -0.44, 0.97699, 11, -3.31, 1.47, 0.02091, 2, 10, 26.14, 4.46, 0.00035, 12, 0.52, 0.31, 0.99965, 2, 11, 21.11, -0.64, 0.00077, 12, 9.45, 0.32, 0.99923], "hull": 19}}, "logo": {"logo": {"x": 1.79, "y": -1.61, "width": 263, "height": 182}}, "tay": {"tay": {"type": "mesh", "uvs": [0.99254, 0.2927, 1, 0.61895, 0.97546, 0.7848, 0.79213, 0.93977, 0.73621, 1, 0.6461, 0.99686, 0.5793, 0.86364, 0.5793, 0.75489, 0.51405, 0.78208, 0.43481, 0.68964, 0.34781, 0.76305, 0.31519, 0.75489, 0.33538, 0.65974, 0.27014, 0.67061, 0.21887, 0.61624, 0.28256, 0.49389, 0.3618, 0.41505, 0.39131, 0.45855, 0.43947, 0.3797, 0.52803, 0.29814, 0.59328, 0.27639, 0.61658, 0.31717, 0.66474, 0.23561, 0.71135, 0.20571, 0.67096, 0.08064, 0.71446, 0.01811, 0.76572, 0, 0.86826, 0, 0.87758, 0.17308, 0.7787, 0.54964, 0.54946, 0.50901, 0.35214, 0.55471], "triangles": [9, 30, 7, 31, 17, 30, 17, 18, 30, 18, 19, 30, 21, 19, 20, 21, 30, 19, 15, 16, 17, 9, 10, 12, 10, 11, 12, 12, 31, 9, 9, 31, 30, 12, 13, 31, 31, 13, 14, 14, 15, 31, 31, 15, 17, 4, 5, 3, 5, 6, 3, 29, 2, 3, 3, 6, 7, 3, 7, 29, 8, 9, 7, 7, 30, 29, 29, 0, 1, 30, 21, 29, 29, 21, 23, 21, 22, 23, 0, 23, 28, 0, 29, 23, 28, 23, 26, 26, 24, 25, 26, 27, 28, 26, 23, 24, 2, 29, 1], "vertices": [2, 22, 21.34, 6.35, 0.25613, 23, -6.44, -21.94, 0.74387, 2, 22, 3.25, -4.11, 0.98895, 23, -11.02, -1.55, 0.01105, 2, 22, -7.42, -6.63, 0.81354, 23, -10.23, 9.38, 0.18646, 2, 22, -25.76, 6.92, 0.0357, 23, 8.18, 22.84, 0.9643, 2, 22, -32.09, 10.66, 0.00547, 23, 13.65, 27.76, 0.99453, 2, 23, 23.61, 29.38, 0.99999, 25, -21.25, 34.64, 1e-05, 3, 23, 32.5, 22.34, 0.97802, 25, -16.1, 24.54, 0.00523, 24, 0.71, 23.39, 0.01675, 3, 23, 33.76, 15.5, 0.83429, 25, -17.82, 17.79, 0.03498, 24, -0.36, 16.51, 0.13073, 3, 23, 40.63, 18.52, 0.4741, 25, -10.31, 17.68, 0.14116, 24, 7.12, 17.1, 0.38474, 3, 23, 50.43, 14.3, 0.08019, 25, -3.17, 9.76, 0.59163, 24, 14.98, 9.89, 0.32819, 2, 23, 59.17, 20.68, 0.0001, 25, 7.44, 11.91, 0.9999, 1, 25, 10.85, 10.5, 1, 1, 25, 7.16, 5.16, 1, 1, 25, 14.41, 4.03, 1, 1, 25, 19.12, -0.76, 1, 2, 25, 10.27, -6.59, 0.95472, 24, 29.89, -5.12, 0.04528, 2, 25, 0.43, -9.29, 0.38702, 24, 20.35, -8.74, 0.61298, 2, 25, -2.09, -5.78, 0.11444, 24, 17.51, -5.48, 0.88556, 1, 24, 11.4, -9.63, 1, 2, 23, 44.67, -12.22, 0.2017, 24, 0.8, -13.25, 0.7983, 2, 23, 37.74, -14.91, 0.5451, 24, -6.64, -13.5, 0.4549, 2, 23, 34.7, -12.81, 0.75218, 24, -8.82, -10.52, 0.24782, 2, 23, 30.33, -18.92, 0.97089, 24, -14.95, -14.84, 0.02911, 3, 22, 11.59, 36.81, 0.00038, 23, 25.54, -21.74, 0.99708, 24, -20.4, -15.93, 0.00254, 1, 23, 31.44, -28.8, 1, 1, 23, 27.37, -33.61, 1, 2, 22, 26.08, 37.56, 0.00082, 23, 21.93, -35.79, 0.99918, 2, 22, 31.43, 27.4, 0.00634, 23, 10.63, -37.86, 0.99366, 2, 22, 22.11, 21.31, 0.04779, 23, 7.61, -27.15, 0.95221, 1, 23, 14.16, -1.45, 1, 3, 23, 39.88, 0.62, 0.03127, 25, -18.46, 1.72, 0.00058, 24, 0.51, 0.45, 0.96815, 2, 25, 3.68, -0.89, 0.96151, 24, 22.79, -0.07, 0.03849], "hull": 29}}, "than1": {"than1": {"x": 8.13, "y": -7.57, "width": 33, "height": 37}}, "than2": {"than2": {"type": "mesh", "uvs": [0, 0.65789, 0.09506, 0.8394, 0.18597, 0.92318, 0.28675, 0.98368, 0.45671, 1, 0.67211, 1, 0.72547, 1, 0.82428, 1, 0.90728, 0.90922, 0.89147, 0.79286, 0.98632, 0.6672, 1, 0.49965, 0.91913, 0.27159, 0.74301, 0.20354, 0.66618, 0.17385, 0.42113, 0.05517, 0.28082, 0, 0.13459, 0, 0.01404, 0.1948, 0.01404, 0.37864, 0, 0.5904, 0.20192, 0.25823, 0.11314, 0.41505, 0.14447, 0.59338, 0.31942, 0.72867, 0.5257, 0.68255, 0.65364, 0.51037], "triangles": [7, 6, 8, 8, 6, 9, 9, 6, 5, 3, 24, 4, 9, 5, 25, 5, 4, 25, 4, 24, 25, 25, 26, 9, 9, 26, 10, 10, 26, 11, 3, 2, 24, 2, 1, 24, 1, 23, 24, 1, 0, 23, 24, 23, 25, 26, 25, 21, 0, 20, 23, 20, 22, 23, 21, 25, 22, 25, 23, 22, 20, 19, 22, 26, 21, 14, 26, 12, 11, 26, 13, 12, 26, 14, 13, 14, 21, 15, 22, 19, 21, 19, 18, 21, 18, 17, 21, 21, 16, 15, 21, 17, 16], "vertices": [1, 6, 3.67, 30.03, 1, 1, 6, -7.93, 10.38, 1, 2, 6, -10.53, -3.93, 0.87364, 2, 27.98, 0.33, 0.12636, 2, 6, -10.34, -18.18, 0.41539, 2, 15.28, 6.8, 0.58461, 1, 2, -6.13, 8.55, 1, 2, 6, 10.74, -61.96, 0.02321, 2, -33.27, 8.55, 0.97679, 2, 6, 13.88, -67.91, 0.03602, 2, -40, 8.55, 0.96398, 2, 6, 19.68, -78.92, 0.05694, 2, -52.45, 8.55, 0.94306, 2, 6, 33.15, -83.65, 0.09507, 2, -62.9, -1.17, 0.90493, 2, 6, 43.23, -76.09, 0.1873, 2, -60.91, -13.62, 0.8127, 2, 6, 60.7, -80.4, 0.33136, 2, -72.86, -27.06, 0.66864, 2, 6, 77.36, -73.57, 0.42914, 2, -74.59, -44.99, 0.57086, 2, 6, 94.21, -53.18, 0.57513, 2, -64.4, -69.39, 0.42487, 2, 6, 90.31, -30.15, 0.72942, 2, -42.21, -76.67, 0.27058, 2, 6, 88.61, -20.11, 0.82105, 2, -32.53, -79.85, 0.17895, 2, 6, 85.46, 13.13, 0.99306, 2, -1.65, -92.55, 0.00694, 1, 6, 82.44, 31.52, 1, 1, 6, 73.85, 47.83, 1, 1, 6, 48.33, 51.55, 1, 1, 6, 30.93, 42.39, 1, 1, 6, 10.06, 33.39, 1, 1, 6, 53.36, 27.44, 1, 1, 6, 33.3, 29.52, 1, 1, 6, 18.26, 17.14, 1, 2, 6, 15.72, -9.11, 0.82557, 2, 11.17, -20.49, 0.17443, 2, 6, 32.2, -29.81, 0.46307, 2, -14.83, -25.42, 0.53693, 2, 6, 56.01, -35.49, 0.59805, 2, -30.95, -43.84, 0.40195], "hull": 21}}, "dau1": {"dau1": {"type": "mesh", "uvs": [0.04765, 0.64152, 0.16811, 0.81015, 0.30864, 0.95126, 0.40901, 1, 0.5375, 0.99944, 0.64992, 0.88587, 0.79848, 0.73444, 0.86055, 0.64863, 0.92295, 0.56236, 1, 0.30769, 0.88682, 0.35243, 0.85871, 0.19756, 0.73826, 0.03236, 0.41303, 0, 0.20023, 0, 0.01955, 0.14938, 0.00553, 0.24315, 0, 0.28016, 0.20261, 0.3487, 0.37331, 0.44227, 0.46263, 0.50011, 0.47454, 0.54945, 0.58569, 0.55285, 0.66905, 0.46949, 0.83578, 0.42015, 0.70676, 0.71107, 0.53011, 0.77912, 0.39316, 0.76211, 0.29987, 0.71618, 0.18475, 0.63962, 0.34282, 0.84856, 0.51036, 0.86368], "triangles": [18, 14, 13, 15, 14, 18, 16, 15, 18, 17, 16, 18, 13, 12, 23, 24, 11, 10, 24, 10, 9, 19, 18, 13, 23, 19, 13, 12, 11, 23, 24, 23, 11, 20, 19, 23, 22, 21, 20, 23, 22, 20, 8, 24, 9, 18, 0, 17, 29, 18, 19, 29, 0, 18, 7, 24, 8, 23, 24, 7, 25, 23, 7, 22, 23, 25, 28, 29, 19, 21, 28, 19, 21, 19, 20, 6, 25, 7, 27, 28, 21, 26, 21, 22, 26, 22, 25, 27, 21, 26, 1, 0, 29, 1, 29, 28, 30, 28, 27, 1, 28, 30, 31, 27, 26, 30, 27, 31, 5, 26, 25, 31, 26, 5, 5, 25, 6, 2, 1, 30, 4, 31, 5, 3, 30, 31, 3, 31, 4, 2, 30, 3], "vertices": [16.08, 27.53, 3.99, 18.27, -5.85, 7.92, -8.9, 0.94, -7.97, -7.49, 1.49, -13.96, 14.11, -22.5, 21.1, -25.89, 28.14, -29.3, 48.17, -32.32, 43.97, -25.25, 55.64, -22.17, 67.46, -12.94, 67.71, 8.67, 66.26, 22.64, 53.58, 33.3, 46.3, 33.47, 43.43, 33.54, 39.57, 19.69, 33.57, 7.74, 29.75, 1.42, 26.06, 0.24, 26.56, -7.08, 33.51, -11.89, 38.43, -22.44, 15.27, -16.29, 8.85, -5.24, 9.21, 3.88, 12.09, 10.37, 17.17, 18.54, 2.25, 6.5, 2.24, -4.62], "hull": 18}}, "sung1": {"sung1": {"type": "mesh", "uvs": [0, 0.06863, 0.06034, 0.26396, 0, 0.42414, 0.03067, 0.62728, 0.13452, 0.79918, 0.40155, 0.77183, 0.71681, 0.84996, 0.87258, 0.97888, 0.9653, 1, 1, 0.83043, 0.92821, 0.55306, 0.83178, 0.31475, 0.7576, 0.08426, 0.50169, 0, 0.44235, 0.0491, 0.19386, 0], "triangles": [1, 0, 15, 5, 3, 2, 1, 15, 14, 5, 1, 14, 5, 2, 1, 14, 13, 12, 11, 5, 14, 11, 14, 12, 4, 3, 5, 10, 6, 5, 10, 5, 11, 6, 10, 9, 7, 6, 9, 8, 7, 9], "vertices": [-15.01, -35.26, 0.34, -33.91, 10.97, -41.26, 26.36, -42.32, 40.77, -37.23, 43.51, -16.21, 54.82, 6.74, 67.01, 16.56, 70.2, 23.34, 58.42, 28.87, 36.88, 28.02, 17.75, 24.62, -0.41, 22.79, -11.11, 4.51, -8.58, -0.88, -16.58, -19.18], "hull": 16}}, "sung2": {"sung2": {"x": 35.25, "y": -1.52, "rotation": -139.48, "width": 80, "height": 68}}, "sung3": {"sung3": {"type": "mesh", "uvs": [0, 0.42534, 0.24638, 0.25645, 0.43983, 0.21845, 0.64365, 0.22689, 0.89583, 0, 1, 0.02423, 1, 0.39156, 0.92002, 0.64067, 0.9062, 0.932, 0.78529, 1, 0.06674, 1, 0, 0.80956, 0, 0.53512], "triangles": [4, 5, 6, 3, 4, 6, 12, 0, 1, 7, 3, 6, 2, 11, 12, 3, 7, 2, 9, 10, 11, 8, 9, 7, 9, 2, 7, 2, 12, 1, 2, 9, 11], "vertices": [-7.74, 3.43, 8.52, 12.55, 21.28, 14.61, 34.74, 14.15, 51.38, 26.4, 58.26, 25.09, 58.26, 5.26, 52.98, -8.19, 52.06, -23.93, 44.08, -27.6, -3.34, -27.6, -7.74, -17.31, -7.74, -2.49], "hull": 13}}, "fx3": {"fx3": {"x": 12.45, "y": 8.42, "width": 190, "height": 186}}}}], "animations": {"animation": {"slots": {"logo2": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1, "color": "ffffff00"}]}, "fx4": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}]}}, "bones": {"logo": {"scale": [{"time": 1}, {"time": 1.1, "x": 1.073, "y": 1.073}, {"time": 1.1667}, {"time": 1.2667, "x": 1.073, "y": 1.073}, {"time": 1.3333}]}, "fx4": {"translate": [{"x": 3.1, "y": 3.56}, {"time": 0.5, "x": -2.98, "y": -6.8}, {"time": 0.6667, "x": -5.58, "y": -10.22}, {"time": 0.8333, "x": -8.04, "y": -13.7}, {"time": 1, "x": -9.05, "y": -17.17}, {"time": 1.1667, "x": -8.62, "y": -20.19}, {"time": 1.3333, "x": -6.57, "y": -22.94}, {"time": 1.5, "x": -4.49, "y": -25.66}, {"time": 2, "x": -0.49, "y": -34.38}]}, "chan3": {"rotate": [{"angle": -10.07, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": -11.49, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 3.23, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -10.07}]}, "toc8": {"rotate": [{"angle": -4.33, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": -5.66, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 8.17, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -4.33}]}, "toc9": {"rotate": [{"angle": 12.15, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -5.66, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 19.2, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 12.15}]}, "than": {"translate": [{"y": 9.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 34.05, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "y": 9.66}]}, "toc6": {"rotate": [{"angle": -1.74, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -5.66, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 8.17, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.74}]}, "toc7": {"rotate": [{"angle": 11.7, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.4333, "angle": -36.13, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 16.8, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 1, "angle": 11.7, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 1.4333, "angle": -36.13, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "angle": 16.8, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 11.7}]}, "dau": {"rotate": [{"angle": 6.97, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 13.94, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 6.97}]}, "toc4": {"rotate": [{"angle": -7.19, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "angle": -28.16, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 1.12, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": -7.19, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.3333, "angle": -28.16, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 1.12, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -7.19}]}, "toc5": {"rotate": [{"angle": -1.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": -16.28, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 14.16, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": -1.06, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2667, "angle": -16.28, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 14.16, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -1.06}]}, "toc2": {"rotate": [{"angle": -3.09, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": -7.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 8.01, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": -3.09, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.1667, "angle": -7.49, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 8.01, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -3.09}]}, "toc3": {"rotate": [{"angle": -6, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1, "angle": -7.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 8.01, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1, "angle": -6, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 1.1, "angle": -7.49, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 8.01, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -6}]}, "than4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -5.49, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "fx3": {"rotate": [{}, {"time": 1, "angle": 180}, {"time": 2}]}, "fx5": {"rotate": [{}, {"time": 1, "angle": 180}, {"time": 2}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 1.319, "y": 1.319, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "than1": {"translate": [{"y": -12.31, "curve": 0.345, "c2": 0.37, "c3": 0.696, "c4": 0.76}, {"time": 0.3333, "y": -2.97, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "y": -22.81, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "y": -12.31}]}, "fx7": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -11.83, "y": -0.87, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.45, "y": 8.53, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "than6": {"rotate": [{"angle": -12.39, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 15.64, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -12.39}]}, "than5": {"rotate": [{"angle": -8.84, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -17.68, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -8.84}]}, "bone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.74, "curve": 0.25, "c3": 0.75}, {"time": 2}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "y": 32.67, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "tay2": {"rotate": [{"angle": -16.75, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "angle": -18.36, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 22.39, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.7667, "angle": -6.79, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 1.8333, "angle": -10.12, "curve": 0.358, "c2": 0.44, "c3": 0.696, "c4": 0.8}, {"time": 2, "angle": -16.75}]}, "tay3": {"rotate": [{"angle": -17.43, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.9333, "angle": 22.39, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.7667, "angle": -14.43, "curve": 0.346, "c2": 0.4, "c3": 0.68, "c4": 0.74}, {"time": 1.8333, "angle": -16.73, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 1.9333, "angle": -18.36, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 2, "angle": -17.43}]}, "toc10": {"rotate": [{"angle": 6.77, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": -5.66, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 19.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 6.77}]}, "sung3": {"translate": [{"x": -4.11, "y": -3.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "x": -4.47, "y": -20.2, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -3.76, "y": 13.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "x": -4.11, "y": -3.35}]}, "dau1": {"rotate": [{"angle": 3.23, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 13.06, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -6.61, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 3.23}], "translate": [{"x": 0.17, "y": -0.3, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 1.74, "y": -3.08, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "x": 0.17, "y": -0.3}]}, "canh1": {"rotate": [{"angle": -6.57, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.3333, "angle": -25.09, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "angle": -37.27, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 5.65, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -6.57}]}, "canh2": {"rotate": [{"angle": 6.98, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.3333, "angle": 26.42, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "angle": 39.21, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.85, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 6.98}]}, "toc14": {"rotate": [{"angle": 15.51, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.3333, "angle": 10.68, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "angle": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 18.7, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 15.51}]}, "sung23": {"translate": [{"x": -12.51, "y": -6.45, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "x": -19.64, "y": -10.94, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 5.47, "y": 4.88, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -12.51, "y": -6.45}]}, "sung1": {"translate": [{"x": 1.67, "y": 2.95, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "x": 0.01, "y": 12.56, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 2.33, "y": -0.86, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 1.67, "y": 2.95}]}, "tay4": {"translate": [{"x": -3.21, "y": 6.1, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.42, "y": -2.08, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -3.21, "y": 6.1}]}, "toc11": {"rotate": [{"angle": 12.91, "curve": 0.299, "c2": 0.22, "c3": 0.649, "c4": 0.61}, {"time": 0.3333, "angle": 10.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "angle": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 13.49, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 12.91}]}, "tay5": {"rotate": [{"angle": -10.15, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "angle": -18.36, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 22.39, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.7667, "angle": 2.02, "curve": 0.334, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 1.8333, "angle": -1.59, "curve": 0.343, "c2": 0.36, "c3": 0.68, "c4": 0.71}, {"time": 2, "angle": -10.15}]}, "toc12": {"rotate": [{"angle": 10.46, "curve": 0.351, "c2": 0.39, "c3": 0.701, "c4": 0.78}, {"time": 0.3333, "angle": 8.08, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.5, "angle": 7.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 13.49, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 10.46}]}}, "deform": {"default": {"than2": {"than2": [{"offset": 46, "vertices": [-0.21407, -0.69276, -0.46189, 0.55894, -0.19843, -0.76136, -0.52783, 0.5835, -0.17283, -0.9981, -0.73983, 0.69193, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.06632, -1.10438, 0.21584, -1.11987, 0.32798, -1.02755, 0.34584, -0.8339, -0.887, 0.16817, 0.21324, -0.69049, -0.69435, 0.20039, 0.03715, -0.65183, -0.56544, 0.32643], "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "offset": 46, "vertices": [-2.21937, -7.18224, -4.78868, 5.79493, -2.05728, -7.89355, -5.47237, 6.04953, -1.79184, -10.34798, -7.67032, 7.1737, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.68762, -11.44981, 2.65211, -16.00319, 3.81471, -15.04609, 8.09966, -14.84483, -16.85164, 1.2981, 6.72488, -13.35793, -14.85431, 1.63218, 0.3852, -6.7579, -5.86231, 3.38433], "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "offset": 46, "vertices": [-0.21407, -0.69276, -0.46189, 0.55894, -0.19843, -0.76136, -0.52783, 0.5835, -0.17283, -0.9981, -0.73983, 0.69193, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.06632, -1.10438, 0.21584, -1.11987, 0.32798, -1.02755, 0.34584, -0.8339, -0.887, 0.16817, 0.21324, -0.69049, -0.69435, 0.20039, 0.03715, -0.65183, -0.56544, 0.32643]}]}, "tay2": {"tay2": [{"offset": 14, "vertices": [-5.51844, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51843, -1.91166, -5.51843, -1.91166, -5.51844, -1.91167, -5.51844, -1.91166, -5.51843, -1.91166, -5.51845, -1.91166, -5.51843, -1.91167, -5.51843, -1.91166, -5.51843, -1.91166, -5.51843, -1.91166, -5.51845, -1.91166, -5.51843, -1.91166, -5.51842, -1.91166, -5.51845, -1.91167, -5.51843, -1.91166, -5.51843, -1.91166, -5.51842, -1.91166, -5.51845, -1.91167, -5.51844, -1.91167, -5.51844, -1.91166, -5.51844, -1.91167, -5.51844, -1.91166, 0, 0, 0, 0, -5.51845, -1.91166, -5.51844, -1.91166, -5.51844, -1.91167, -5.51844, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51843, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51845, -1.91166, -5.51844, -1.91166, -5.51844, -1.91165, -5.51842, -1.91166, -5.51842, -1.91166, -5.51843, -1.91166], "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "offset": 14, "vertices": [-10.30709, -3.10282, -10.30709, -3.10283, -10.30709, -3.10282, -10.30709, -3.10282, -10.30708, -3.10282, -10.30708, -3.10282, -10.30709, -3.10283, -10.30709, -3.10282, -10.30709, -3.10282, -10.3071, -3.10282, -10.30708, -3.10282, -10.30708, -3.10282, -10.30708, -3.10282, -10.30708, -3.10282, -10.3071, -3.10282, -10.30708, -3.10282, -10.30707, -3.10282, -10.3071, -3.10283, -10.30708, -3.10282, -10.30708, -3.10282, -10.30707, -3.10282, -10.3071, -3.10283, -10.30709, -3.10283, -10.30709, -3.10283, -10.30709, -3.10283, -10.30709, -3.10282, 0, 0, 0, 0, -10.3071, -3.10283, -10.30709, -3.10282, -10.30709, -3.10283, -10.30709, -3.10282, -10.30709, -3.10282, -10.30709, -3.10283, -10.30709, -3.10282, -10.30709, -3.10282, -10.30709, -3.10282, -10.30709, -3.10282, -10.30709, -3.10282, -10.30709, -3.10282, -10.30709, -3.10282, -10.30709, -3.10283, -10.30709, -3.10282, -10.30709, -3.10282, -10.30709, -3.10282, -10.3071, -3.10282, -10.30709, -3.10282, -10.30709, -3.10281, -10.30707, -3.10282, -10.30707, -3.10282, -10.30708, -3.10282], "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 14, "vertices": [6.56671, 1.09447, 6.56671, 1.09447, 6.56671, 1.09447, 6.56671, 1.09447, 6.56672, 1.09447, 6.56672, 1.09447, 6.56671, 1.09446, 6.56671, 1.09447, 6.56671, 1.09447, 6.5667, 1.09447, 6.56671, 1.09447, 6.56671, 1.09447, 6.56671, 1.09447, 6.56671, 1.09447, 6.5667, 1.09447, 6.56671, 1.09447, 6.56673, 1.09447, 6.5667, 1.09446, 6.56671, 1.09447, 6.56671, 1.09447, 6.56673, 1.09447, 6.5667, 1.09446, 6.56671, 1.09446, 6.56671, 1.09447, 6.56671, 1.09446, 6.56671, 1.09447, 0, 0, 0, 0, 6.5667, 1.09447, 6.56671, 1.09447, 6.56671, 1.09446, 6.56671, 1.09447, 6.56671, 1.09447, 6.56671, 1.09446, 6.56671, 1.09447, 6.56671, 1.09447, 6.56671, 1.09447, 6.56671, 1.09447, 6.56671, 1.09447, 6.56671, 1.09447, 6.56671, 1.09447, 6.56671, 1.09447, 6.56671, 1.09447, 6.56671, 1.09447, 6.56671, 1.09447, 6.5667, 1.09447, 6.56671, 1.09447, 6.56671, 1.09448, 6.56673, 1.09447, 6.56673, 1.09447, 6.56671, 1.09447], "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "offset": 14, "vertices": [-5.51844, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51843, -1.91166, -5.51843, -1.91166, -5.51844, -1.91167, -5.51844, -1.91166, -5.51843, -1.91166, -5.51845, -1.91166, -5.51843, -1.91167, -5.51843, -1.91166, -5.51843, -1.91166, -5.51843, -1.91166, -5.51845, -1.91166, -5.51843, -1.91166, -5.51842, -1.91166, -5.51845, -1.91167, -5.51843, -1.91166, -5.51843, -1.91166, -5.51842, -1.91166, -5.51845, -1.91167, -5.51844, -1.91167, -5.51844, -1.91166, -5.51844, -1.91167, -5.51844, -1.91166, 0, 0, 0, 0, -5.51845, -1.91166, -5.51844, -1.91166, -5.51844, -1.91167, -5.51844, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51843, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51844, -1.91166, -5.51845, -1.91166, -5.51844, -1.91166, -5.51844, -1.91165, -5.51842, -1.91166, -5.51842, -1.91166, -5.51843, -1.91166]}]}, "chan": {"chan": [{"offset": 10, "vertices": [-0.35019, 1.99117, -0.73456, 1.88356, -0.16163, 0.919, -0.33903, 0.86933, 0, 0, 0, 0, -0.73457, 1.88357, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.01744, 0.13188, -0.35019, 1.99117, -2.01743, 0.13188, 0, 0, 0, 0, -2.01744, 0.13188, -0.35019, 1.99117, -0.73457, 1.88357, -0.73457, 1.88357, -0.35019, 1.99117, -0.73456, 1.88356, -2.01743, 0.13188, -0.35019, 1.99117], "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "offset": 10, "vertices": [-1.23397, 7.0163, -2.58838, 6.63713, -0.56952, 3.23828, -1.19463, 3.06328, 0, 0, 0, 0, -2.58839, 6.63715, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.10888, 0.46472, -1.23397, 7.01632, -7.10884, 0.46471, 0, 0, 0, 0, -7.10888, 0.46472, -1.23397, 7.01632, -2.58839, 6.63715, -2.58839, 6.63715, -1.23397, 7.0163, -2.58838, 6.63713, -7.10884, 0.46471, -1.23397, 7.0163], "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "offset": 10, "vertices": [-0.35019, 1.99117, -0.73456, 1.88356, -0.16163, 0.919, -0.33903, 0.86933, 0, 0, 0, 0, -0.73457, 1.88357, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.01744, 0.13188, -0.35019, 1.99117, -2.01743, 0.13188, 0, 0, 0, 0, -2.01744, 0.13188, -0.35019, 1.99117, -0.73457, 1.88357, -0.73457, 1.88357, -0.35019, 1.99117, -0.73456, 1.88356, -2.01743, 0.13188, -0.35019, 1.99117]}]}}}}}}