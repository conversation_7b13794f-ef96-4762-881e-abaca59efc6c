{"skeleton": {"hash": "v46PKWFwAwqYbj7nBtpL1ENcktM", "spine": "3.8.75", "x": -130.58, "y": -201.29, "width": 270.2, "height": 413.42}, "bones": [{"name": "root", "x": 4.45, "y": 0.89}, {"name": "Tai", "parent": "root", "x": -47.62, "y": -66.61}, {"name": "<PERSON><PERSON>", "parent": "root", "x": 53.15, "y": -62.5}, {"name": "xx2", "parent": "root", "x": -94.07, "y": 98.54, "scaleX": 0.94, "scaleY": 0.94}, {"name": "xx3", "parent": "root", "x": 94.93, "y": 34.28, "scaleX": 0.8, "scaleY": 0.8}, {"name": "xx4", "parent": "root", "x": 80.62, "y": 150.5, "scaleY": -1}, {"name": "xx1", "parent": "xx4"}, {"name": "cha", "parent": "root", "length": 110.11, "rotation": 85.01, "x": 135.65, "y": -95.9}, {"name": "cha3", "parent": "cha", "x": -5.05, "y": 140.44}, {"name": "cha2", "parent": "cha3", "length": 156.17, "rotation": 5.87, "x": 30.54, "y": -8.39}, {"name": "cha5", "parent": "cha2", "length": 69.57, "rotation": 6.64, "x": 177.73, "y": 0.15}, {"name": "toc1", "parent": "cha5", "x": 71.76, "y": 7.47}, {"name": "toc3", "parent": "toc1", "length": 20.71, "rotation": 149.73, "x": -5.16, "y": 5.65}, {"name": "toc2", "parent": "toc3", "length": 18.76, "rotation": -6.76, "x": 20.59, "y": 0.28}, {"name": "toc5", "parent": "toc2", "length": 18.19, "rotation": 1.21, "x": 18.79, "y": -0.69}, {"name": "toc4", "parent": "toc5", "length": 17.12, "rotation": 20.02, "x": 20.96, "y": -0.6}, {"name": "toc7", "parent": "toc4", "length": 15.29, "rotation": 33.29, "x": 18.95, "y": 0.27}, {"name": "toc6", "parent": "cha5", "x": 77.05, "y": 10.12}, {"name": "toc9", "parent": "toc6", "length": 12.24, "rotation": 136.46, "x": -0.87, "y": 3.75}, {"name": "toc8", "parent": "toc9", "length": 10.24, "rotation": 4.2, "x": 13.04, "y": -0.07}, {"name": "toc11", "parent": "toc8", "length": 8.23, "rotation": 8.77, "x": 10.79, "y": 0.25}, {"name": "toc10", "parent": "toc11", "length": 7.99, "rotation": -1.34, "x": 9.38, "y": -0.41}, {"name": "toc13", "parent": "toc10", "length": 8.59, "rotation": -21.31, "x": 9.94, "y": -0.68}, {"name": "toc12", "parent": "toc13", "length": 10.23, "rotation": -48.5, "x": 10.4, "y": -0.51}, {"name": "toc15", "parent": "toc12", "length": 8.33, "rotation": -38.97, "x": 12.85, "y": -0.93}, {"name": "toc14", "parent": "cha5", "x": 70.86, "y": -0.11}, {"name": "toc17", "parent": "toc14", "length": 11.1, "rotation": -139.5, "x": -2.25, "y": -2.73}, {"name": "toc16", "parent": "toc17", "length": 10.22, "rotation": -12.57, "x": 12.25, "y": -0.38}, {"name": "toc19", "parent": "toc16", "length": 8.24, "rotation": -4.21, "x": 10.59, "y": 0.01}, {"name": "toc18", "parent": "toc19", "length": 7.48, "rotation": -4.41, "x": 9.11, "y": 0.27}, {"name": "toc21", "parent": "toc18", "length": 7.82, "rotation": 13.9, "x": 9.16, "y": 0.33}, {"name": "toc20", "parent": "toc21", "length": 5.48, "rotation": 9.27, "x": 8.75, "y": 0.39}, {"name": "toc23", "parent": "toc20", "length": 6.41, "rotation": 5.01, "x": 7.56, "y": 0.56}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "bg1", "bone": "root", "attachment": "bg1"}, {"name": "bg2", "bone": "root", "attachment": "bg2"}, {"name": "xiu1", "bone": "root", "attachment": "xiu1"}, {"name": "tai1", "bone": "root", "attachment": "tai1"}, {"name": "xx1", "bone": "xx1", "attachment": "xx1"}, {"name": "xx6", "bone": "xx1", "color": "ffffff2d", "attachment": "xx1", "blend": "additive"}, {"name": "cha", "bone": "cha5", "attachment": "cha"}, {"name": "toc3", "bone": "toc23", "attachment": "toc3"}, {"name": "toc2", "bone": "toc15", "attachment": "toc2"}, {"name": "toc1", "bone": "toc7", "attachment": "toc1"}, {"name": "xx2", "bone": "xx2", "attachment": "xx2"}, {"name": "xx4", "bone": "xx2", "color": "ffffff6c", "attachment": "xx2", "blend": "additive"}, {"name": "xx3", "bone": "xx3", "attachment": "xx3"}, {"name": "xx5", "bone": "xx3", "color": "ffffff3c", "attachment": "xx3", "blend": "additive"}, {"name": "<PERSON><PERSON>", "bone": "<PERSON><PERSON>", "attachment": "<PERSON><PERSON>"}, {"name": "Tai", "bone": "Tai", "attachment": "Tai"}], "skins": [{"name": "default", "attachments": {"Xiu": {"Xiu": {"x": 4.24, "y": 6.32, "scaleX": 1.0987, "scaleY": 1.0987, "width": 121, "height": 85}}, "bg": {"bg": {"x": 0.07, "y": 2.22, "scaleX": 0.7, "scaleY": 0.7, "width": 386, "height": 584}}, "bg1": {"bg1": {"x": 0.07, "y": 2.22, "scaleX": 0.7, "scaleY": 0.7, "width": 386, "height": 584}}, "bg2": {"bg2": {"x": -1.43, "y": -142.78, "scaleY": 0.9, "width": 204, "height": 93}}, "xiu1": {"xiu1": {"x": 74.8, "y": -162.05, "scaleX": 0.9, "scaleY": 0.9, "width": 56, "height": 43}}, "tai1": {"tai1": {"x": -76.55, "y": -118.4, "scaleX": 0.9, "scaleY": 0.9, "width": 62, "height": 46}}, "cha": {"cha": {"type": "mesh", "uvs": [0.00239, 0.98374, 0.04746, 0.88831, 0.12629, 0.79154, 0.15495, 0.65713, 0.13345, 0.51332, 0.11196, 0.40311, 0.17406, 0.34128, 0.23138, 0.32112, 0.15734, 0.31171, 0.23377, 0.26198, 0.22422, 0.20956, 0.18122, 0.13967, 0.229, 0.07382, 0.30543, 0.0093, 0.41769, 0, 0.54906, 0.01602, 0.66371, 0.11145, 0.68999, 0.21897, 0.68999, 0.27139, 0.63744, 0.31574, 0.79986, 0.32246, 0.86435, 0.36144, 0.86913, 0.43805, 0.90018, 0.61009, 0.88824, 0.74315, 0.95034, 0.86949, 1, 0.91519, 1, 0.99449, 0.85719, 1, 0.55422, 1, 0.20989, 1, 0, 1, 0.32901, 0.5839, 0.30837, 0.64737, 0.38209, 0.65483, 0.49045, 0.65193, 0.545, 0.64115, 0.54573, 0.59676, 0.54205, 0.54243, 0.50372, 0.5109, 0.43885, 0.48726, 0.31943, 0.48353, 0.24719, 0.51961, 0.23761, 0.55943, 0.24793, 0.60174, 0.5848, 0.50924, 0.63566, 0.47979, 0.71233, 0.46652, 0.77056, 0.47896, 0.79783, 0.52874, 0.81332, 0.56316, 0.80005, 0.60091, 0.76761, 0.63575, 0.72338, 0.65525, 0.63345, 0.65815, 0.73886, 0.58432, 0.65999, 0.57685, 0.67989, 0.62124, 0.74624, 0.61709, 0.77793, 0.58722, 0.77867, 0.55736, 0.71601, 0.54408, 0.37103, 0.54284, 0.40715, 0.57851, 0.39757, 0.61087, 0.33491, 0.62041, 0.28847, 0.60381, 0.27299, 0.56773, 0.30616, 0.53828, 0.52925, 0.7296, 0.50869, 0.81802, 0.51456, 0.92463, 0.48079, 0.3784, 0.30668, 0.20384, 0.31406, 0.19761, 0.32921, 0.1928, 0.35233, 0.19247, 0.36904, 0.19651, 0.37856, 0.20307, 0.36749, 0.20843, 0.34495, 0.21226, 0.32203, 0.21171, 0.30415, 0.20679, 0.2991, 0.20766, 0.31173, 0.19553, 0.32863, 0.19028, 0.35292, 0.19006, 0.37273, 0.19487, 0.38458, 0.20285, 0.36827, 0.2104, 0.34553, 0.21422, 0.32028, 0.21346, 0.45057, 0.19559, 0.45271, 0.18772, 0.4665, 0.17984, 0.48535, 0.17667, 0.50439, 0.17624, 0.52712, 0.18258, 0.5209, 0.1887, 0.50089, 0.19602, 0.47893, 0.19952, 0.45543, 0.19832, 0.44707, 0.19581, 0.45096, 0.18542, 0.46767, 0.17667, 0.50575, 0.17383, 0.52945, 0.18072, 0.52867, 0.18564, 0.51779, 0.19428, 0.49195, 0.20007, 0.46767, 0.20138, 0.45135, 0.19909], "triangles": [90, 72, 7, 108, 19, 72, 72, 90, 89, 89, 88, 72, 72, 111, 110, 72, 88, 111, 88, 102, 111, 7, 91, 90, 91, 7, 9, 91, 9, 83, 108, 72, 109, 19, 108, 107, 72, 110, 109, 7, 8, 9, 18, 19, 17, 19, 107, 17, 9, 10, 83, 107, 106, 17, 106, 16, 17, 91, 81, 90, 81, 80, 90, 90, 79, 89, 90, 80, 79, 83, 82, 91, 91, 82, 81, 81, 75, 80, 80, 76, 79, 80, 75, 76, 82, 73, 81, 73, 74, 81, 81, 74, 75, 89, 78, 88, 89, 79, 78, 83, 10, 84, 79, 77, 78, 79, 76, 77, 73, 82, 83, 84, 73, 83, 73, 84, 74, 84, 10, 12, 77, 87, 78, 78, 87, 88, 102, 88, 103, 111, 101, 110, 110, 100, 109, 110, 101, 100, 109, 99, 108, 109, 100, 99, 101, 94, 100, 100, 95, 99, 100, 94, 95, 102, 92, 111, 111, 92, 101, 92, 93, 101, 101, 93, 94, 75, 74, 85, 87, 77, 86, 108, 99, 98, 99, 95, 96, 93, 92, 102, 93, 102, 103, 87, 103, 88, 74, 84, 85, 85, 84, 12, 77, 76, 86, 103, 87, 14, 108, 98, 107, 98, 99, 96, 75, 85, 76, 76, 85, 86, 13, 86, 85, 11, 12, 10, 85, 12, 13, 87, 86, 14, 86, 13, 14, 98, 97, 107, 98, 96, 97, 93, 103, 94, 107, 97, 106, 94, 103, 104, 96, 105, 97, 97, 105, 106, 16, 106, 15, 94, 104, 95, 104, 103, 14, 96, 95, 105, 105, 95, 104, 15, 105, 104, 106, 105, 15, 15, 104, 14, 30, 71, 29, 31, 0, 30, 0, 1, 30, 1, 2, 30, 28, 29, 53, 53, 71, 54, 28, 53, 24, 27, 28, 26, 28, 24, 25, 24, 53, 52, 71, 30, 70, 34, 70, 33, 70, 34, 69, 53, 29, 71, 69, 54, 70, 70, 30, 33, 33, 65, 34, 30, 2, 33, 33, 2, 3, 28, 25, 26, 54, 57, 53, 54, 71, 70, 69, 34, 35, 33, 44, 66, 33, 3, 44, 52, 51, 24, 24, 51, 23, 69, 36, 54, 69, 35, 36, 36, 37, 54, 54, 56, 57, 54, 37, 56, 3, 43, 44, 3, 4, 43, 53, 58, 52, 53, 57, 58, 34, 64, 35, 34, 65, 64, 64, 63, 35, 36, 35, 37, 39, 37, 63, 63, 37, 35, 33, 66, 65, 39, 38, 37, 52, 59, 51, 52, 58, 59, 57, 55, 58, 57, 56, 55, 66, 32, 65, 65, 32, 64, 58, 55, 59, 64, 32, 63, 63, 32, 62, 51, 50, 23, 50, 22, 23, 44, 67, 66, 66, 67, 32, 44, 43, 67, 51, 59, 50, 37, 45, 56, 37, 38, 45, 59, 60, 50, 59, 55, 60, 56, 61, 55, 55, 61, 60, 67, 68, 32, 32, 68, 62, 63, 40, 39, 63, 62, 40, 45, 46, 56, 56, 46, 61, 68, 67, 42, 60, 49, 50, 50, 49, 22, 67, 43, 42, 43, 4, 42, 49, 60, 48, 46, 47, 61, 60, 61, 48, 61, 47, 48, 68, 41, 62, 62, 41, 40, 38, 39, 45, 68, 42, 41, 49, 48, 22, 40, 41, 72, 42, 5, 6, 41, 42, 7, 42, 6, 7, 42, 4, 5, 45, 39, 72, 39, 40, 72, 45, 72, 46, 46, 72, 19, 72, 41, 7, 46, 19, 47, 48, 21, 22, 48, 20, 21, 48, 47, 20, 47, 19, 20], "vertices": [2, 9, -34.4, 83.88, 0.01537, 8, -12.26, 71.53, 0.98463, 2, 9, -4.86, 75.12, 0.2027, 8, 18.03, 65.83, 0.7973, 2, 9, 25.03, 60.86, 0.65625, 8, 49.21, 54.71, 0.34375, 2, 9, 66.74, 55.21, 0.96766, 8, 91.29, 53.35, 0.03234, 2, 9, 111.52, 58.28, 0.99597, 10, -59.05, 65.4, 0.00403, 2, 9, 145.85, 61.52, 0.90345, 10, -24.57, 64.64, 0.09655, 2, 9, 164.91, 50.36, 0.7324, 10, -6.93, 51.35, 0.2676, 2, 9, 171.03, 40.23, 0.48276, 10, -2.03, 40.59, 0.51724, 2, 9, 174.15, 53.14, 0.34929, 10, 2.57, 53.05, 0.65071, 2, 9, 189.41, 39.53, 0.17807, 10, 16.15, 37.77, 0.82193, 2, 9, 205.73, 40.95, 0.02712, 10, 32.53, 37.29, 0.97288, 1, 10, 55.06, 41.91, 1, 1, 10, 74.28, 30.94, 1, 1, 10, 92.42, 15.05, 1, 1, 10, 92.72, -4.8, 1, 1, 10, 84.77, -26.94, 1, 1, 10, 52.72, -42.95, 1, 2, 9, 201.56, -40.51, 0.01541, 10, 18.97, -43.14, 0.98459, 2, 9, 185.26, -40.26, 0.10258, 10, 2.81, -41, 0.89742, 2, 9, 171.61, -30.85, 0.48941, 10, -9.67, -30.08, 0.51059, 2, 9, 169.08, -59.24, 0.91199, 10, -15.46, -57.99, 0.08801, 2, 9, 156.79, -70.34, 0.9527, 10, -28.95, -67.59, 0.0473, 2, 9, 132.95, -70.81, 0.9906, 10, -52.68, -65.3, 0.0094, 1, 9, 79.37, -75.42, 1, 2, 9, 38.03, -72.7, 0.99934, 8, 75.81, -76.82, 0.00066, 2, 9, -1.43, -82.96, 0.96114, 8, 37.61, -91.06, 0.03886, 2, 9, -15.77, -91.43, 0.94502, 8, 24.21, -100.96, 0.05498, 2, 9, -40.43, -91.05, 0.93184, 8, -0.36, -103.1, 0.06816, 2, 9, -41.76, -66.04, 0.89689, 8, -4.24, -78.35, 0.10311, 2, 9, -40.95, -13.02, 0.39051, 8, -8.86, -25.53, 0.60949, 2, 9, -40.02, 47.23, 0.01892, 8, -14.1, 34.5, 0.98108, 2, 9, -39.46, 83.95, 0.01253, 8, -17.3, 71.09, 0.98747, 2, 9, 89.05, 24.4, 0.9994, 8, 116.63, 24.99, 0.0006, 2, 9, 69.37, 28.31, 0.98918, 8, 96.65, 26.87, 0.01082, 2, 9, 66.85, 15.45, 0.99597, 8, 95.46, 13.81, 0.00403, 1, 9, 67.46, -3.52, 1, 1, 9, 70.67, -13.12, 1, 1, 9, 84.47, -13.46, 1, 1, 9, 101.37, -13.08, 1, 1, 9, 111.28, -6.52, 1, 1, 9, 118.81, 4.72, 1, 2, 9, 120.29, 25.6, 0.99729, 10, -54.12, 31.92, 0.00271, 2, 9, 109.26, 38.41, 0.99911, 10, -63.59, 45.92, 0.00089, 2, 9, 96.9, 40.28, 0.99939, 8, 122.82, 41.58, 0.00061, 2, 9, 83.72, 38.67, 0.99537, 8, 109.87, 38.64, 0.00463, 1, 9, 111.58, -20.72, 1, 2, 9, 120.6, -29.76, 0.99944, 10, -60.21, -23.1, 0.00056, 2, 9, 124.52, -43.23, 0.99613, 10, -57.87, -36.94, 0.00387, 2, 9, 120.5, -53.36, 0.99844, 10, -63.04, -46.54, 0.00156, 1, 9, 104.94, -57.9, 1, 1, 9, 94.2, -60.44, 1, 1, 9, 82.49, -57.94, 1, 1, 9, 71.75, -52.1, 1, 1, 9, 65.8, -44.27, 1, 1, 9, 65.14, -28.52, 1, 1, 9, 87.82, -47.31, 1, 1, 9, 90.35, -33.55, 1, 1, 9, 76.5, -36.82, 1, 1, 9, 77.61, -48.45, 1, 1, 9, 86.81, -54.14, 1, 1, 9, 96.1, -54.41, 1, 1, 9, 100.39, -43.51, 1, 1, 9, 101.7, 16.85, 1, 1, 9, 90.51, 10.7, 1, 2, 9, 80.48, 12.53, 0.99959, 8, 109.32, 12.31, 0.00041, 2, 9, 77.68, 23.54, 0.9968, 8, 105.41, 22.97, 0.0032, 2, 9, 82.96, 31.59, 0.99659, 8, 109.84, 31.52, 0.00341, 2, 9, 94.23, 34.12, 0.9993, 8, 120.79, 35.19, 0.0007, 1, 9, 103.3, 28.18, 1, 1, 9, 43.2, -9.94, 1, 1, 9, 15.76, -5.92, 1, 2, 9, -17.4, -6.44, 0.60018, 8, 13.89, -16.58, 0.39982, 2, 9, 152.55, -3.14, 0.99938, 10, -25.4, -0.35, 0.00062, 2, 9, 207.29, 26.49, 0.0149, 10, 32.41, 22.75, 0.9851, 2, 9, 209.21, 25.17, 0.00981, 10, 34.16, 21.22, 0.99019, 2, 9, 210.67, 22.5, 0.00558, 10, 35.3, 18.39, 0.99442, 2, 9, 210.71, 18.45, 0.00304, 10, 34.87, 14.37, 0.99696, 2, 9, 209.4, 15.55, 0.00238, 10, 33.24, 11.63, 0.99762, 2, 9, 207.34, 13.91, 0.00275, 10, 31, 10.25, 0.99725, 2, 9, 205.7, 15.88, 0.00605, 10, 29.6, 12.39, 0.99395, 2, 9, 204.57, 19.84, 0.01265, 10, 28.94, 16.45, 0.98735, 2, 9, 204.8, 23.85, 0.01808, 10, 29.63, 20.41, 0.98192, 2, 9, 206.38, 26.95, 0.01764, 10, 31.56, 23.31, 0.98236, 2, 9, 206.12, 27.84, 0.01932, 10, 31.4, 24.22, 0.98068, 2, 9, 209.86, 25.57, 0.00893, 10, 34.86, 21.54, 0.99107, 2, 9, 211.45, 22.59, 0.0047, 10, 36.09, 18.39, 0.9953, 2, 9, 211.45, 18.34, 0.00241, 10, 35.6, 14.17, 0.99759, 2, 9, 209.9, 14.89, 0.00175, 10, 33.66, 10.93, 0.99825, 2, 9, 207.39, 12.86, 0.00203, 10, 30.93, 9.2, 0.99797, 2, 9, 205.09, 15.75, 0.00668, 10, 28.98, 12.33, 0.99332, 2, 9, 203.96, 19.74, 0.01375, 10, 28.32, 16.43, 0.98625, 2, 9, 204.27, 24.16, 0.02015, 10, 29.13, 20.78, 0.97985, 1, 10, 31.66, -2.55, 1, 1, 10, 34.04, -3.24, 1, 1, 10, 36.15, -5.95, 1, 1, 10, 36.69, -9.35, 1, 1, 10, 36.39, -12.67, 1, 1, 10, 35.37, -18.61, 1, 1, 10, 32.17, -15.03, 1, 1, 10, 30.37, -11.26, 1, 1, 10, 29.8, -7.31, 1, 1, 10, 30.71, -3.28, 1, 1, 10, 31.67, -1.93, 1, 1, 10, 34.79, -3.03, 1, 1, 10, 37.1, -6.28, 1, 1, 10, 37.1, -13.01, 1, 1, 10, 35.97, -19.82, 1, 1, 10, 33.96, -18.49, 1, 1, 10, 30.53, -14.26, 1, 1, 10, 29.33, -9.55, 1, 1, 10, 29.48, -5.28, 1, 1, 10, 30.56, -2.54, 1], "hull": 32}}, "toc1": {"toc1": {"type": "mesh", "uvs": [0.88327, 0.01031, 0.73932, 0.05823, 0.59955, 0.18522, 0.49733, 0.33377, 0.38467, 0.46555, 0.20734, 0.56139, 0.0321, 0.60332, 0, 0.72312, 0, 0.84053, 0.14893, 0.95314, 0.44934, 1, 0.61207, 1, 0.72681, 0.98429, 0.50984, 0.91001, 0.42222, 0.80339, 0.52653, 0.70156, 0.64336, 0.60452, 0.71846, 0.47394, 0.84781, 0.34335, 0.98549, 0.1972, 1, 0.06781, 0.98132, 0, 0.94168, 0.03906, 0.81234, 0.15766, 0.69343, 0.28225, 0.56826, 0.41643, 0.41805, 0.58056, 0.26159, 0.68598, 0.2595, 0.82256, 0.34712, 0.89564, 0.441, 0.94835], "triangles": [8, 7, 28, 29, 28, 14, 30, 29, 14, 13, 30, 14, 9, 8, 28, 10, 30, 13, 29, 30, 10, 9, 28, 29, 10, 9, 29, 11, 13, 12, 10, 13, 11, 27, 5, 26, 27, 26, 14, 27, 6, 5, 28, 27, 14, 27, 7, 6, 28, 7, 27, 25, 3, 24, 4, 3, 25, 17, 24, 18, 25, 24, 17, 16, 25, 17, 26, 4, 25, 5, 4, 26, 26, 25, 16, 15, 26, 16, 14, 26, 15, 22, 0, 21, 22, 21, 20, 23, 0, 22, 19, 22, 20, 23, 1, 0, 2, 1, 23, 23, 22, 19, 24, 2, 23, 3, 2, 24, 18, 23, 19, 24, 23, 18], "vertices": [2, 12, -9.66, -0.72, 0.04599, 11, 3.55, 1.4, 0.95401, 2, 12, -1.97, -6.55, 0.90424, 11, -0.15, 10.31, 0.09576, 1, 12, 12.99, -9.06, 1, 3, 12, 29.12, -8.73, 0.26886, 14, -9.41, -7.06, 0.23257, 13, 9.54, -7.95, 0.49857, 2, 14, 5.41, -6.5, 0.99666, 15, -16.64, -0.22, 0.00334, 2, 14, 18.81, -10.97, 0.28788, 15, -5.58, -9, 0.71212, 3, 14, 27.36, -17.91, 0.00171, 15, 0.08, -18.45, 0.99561, 16, -26.05, -5.29, 0.00268, 2, 15, 12.32, -18.55, 0.85257, 16, -15.87, -12.09, 0.14743, 2, 15, 24.05, -16.84, 0.41472, 16, -5.12, -17.1, 0.58528, 2, 15, 34.07, -6.66, 0.01545, 16, 8.84, -14.09, 0.98455, 1, 16, 20.49, -0.3, 1, 1, 16, 24.49, 8.25, 1, 1, 16, 25.86, 14.96, 1, 1, 16, 13.74, 6.72, 1, 3, 14, 34.42, 11.59, 0.00993, 15, 16.82, 6.85, 0.50412, 16, 1.83, 6.67, 0.48595, 3, 14, 22.5, 12.04, 0.44088, 13, 41.03, 11.83, 0.02149, 15, 5.77, 11.36, 0.53763, 3, 14, 10.65, 13.36, 0.70644, 13, 29.16, 12.89, 0.27702, 15, -4.91, 16.65, 0.01653, 2, 14, -3.02, 10.94, 0.01959, 13, 15.54, 10.19, 0.98041, 2, 12, 22.15, 10.39, 0.11697, 13, 0.37, 10.22, 0.88303, 3, 12, 5.45, 12.05, 0.88528, 11, -15.94, -2, 0.06267, 13, -16.42, 9.9, 0.05205, 2, 12, -6.92, 7.77, 0.16213, 11, -3.09, -4.55, 0.83787, 2, 12, -12.82, 4.12, 7e-05, 11, 3.84, -4.37, 0.99993, 1, 11, 0.23, -1.58, 1, 3, 12, 5.65, 1.24, 0.99429, 11, -10.67, 7.43, 0.00349, 13, -14.94, -0.81, 0.00222, 1, 12, 19.93, -0.25, 1, 3, 12, 35.23, -1.71, 0.00041, 14, -4.01, 0.52, 0.02888, 13, 14.78, -0.25, 0.97071, 2, 14, 14.72, 0.71, 0.99649, 13, 33.49, 0.33, 0.00351, 2, 15, 6.42, -4.08, 0.99988, 16, -12.85, 3.24, 0.00012, 2, 15, 20.09, -2.21, 0.16329, 16, -0.4, -2.7, 0.83671, 1, 16, 8.44, -1.21, 1, 1, 16, 15.56, 1.47, 1], "hull": 22}}, "toc2": {"toc2": {"type": "mesh", "uvs": [1, 0.10597, 0.85475, 0.18787, 0.71229, 0.35913, 0.63813, 0.52853, 0.57177, 0.69793, 0.47614, 0.85616, 0.27903, 0.95482, 0.13071, 0.99205, 0.04484, 0.80776, 0.00776, 0.58996, 0.11315, 0.56204, 0.25756, 0.6849, 0.39418, 0.62161, 0.24976, 0.53784, 0.15608, 0.43546, 0.21658, 0.26233, 0.32977, 0.18229, 0.40003, 0.36286, 0.34929, 0.50061, 0.47614, 0.49689, 0.56592, 0.28467, 0.69667, 0.09852, 0.82548, 0, 0.93672, 0, 1, 0.00358, 0.94258, 0.04454, 0.79425, 0.13761, 0.67716, 0.29398, 0.53664, 0.5304, 0.39027, 0.77239, 0.26147, 0.80218, 0.13071, 0.75378, 0.3649, 0.55273, 0.27318, 0.44104, 0.3005, 0.28281], "triangles": [31, 10, 11, 9, 10, 31, 8, 9, 31, 8, 31, 7, 30, 11, 29, 31, 11, 30, 6, 30, 29, 6, 29, 5, 7, 31, 30, 7, 30, 6, 13, 32, 12, 29, 11, 12, 4, 29, 12, 5, 29, 4, 34, 15, 16, 17, 34, 16, 33, 15, 34, 33, 34, 17, 14, 15, 33, 18, 33, 17, 28, 19, 20, 3, 28, 20, 13, 14, 33, 13, 33, 18, 32, 18, 19, 13, 18, 32, 12, 32, 19, 4, 28, 3, 12, 19, 28, 12, 28, 4, 27, 20, 21, 2, 27, 1, 2, 3, 20, 2, 20, 27, 25, 23, 24, 25, 24, 0, 25, 22, 23, 1, 25, 0, 26, 21, 22, 22, 25, 26, 1, 26, 25, 27, 21, 26, 27, 26, 1], "vertices": [2, 18, -4.93, 8.57, 0.00057, 17, -3.2, -5.86, 0.99943, 2, 18, 4.67, 4.42, 0.91409, 17, -7.3, 3.77, 0.08591, 2, 20, -4.14, 3.88, 0.09258, 19, 6.11, 3.45, 0.90742, 2, 20, 7.79, 3.96, 0.83059, 21, -1.69, 4.33, 0.16941, 2, 21, 10.03, 5.13, 0.56142, 22, -2.03, 5.44, 0.43858, 2, 22, 9.4, 8.66, 0.69471, 23, -7.53, 5.33, 0.30529, 3, 22, 22.63, 4.72, 0.00111, 23, 4.18, 12.62, 0.96272, 24, -15.26, 5.09, 0.03618, 2, 23, 13.18, 15.71, 0.80665, 24, -10.21, 13.14, 0.19335, 2, 23, 19.37, 4.15, 0.14582, 24, 1.86, 8.05, 0.85418, 1, 24, 13.23, -0.7, 1, 2, 23, 16.31, -12.09, 0.00158, 24, 9.71, -6.49, 0.99842, 3, 22, 11.33, -8.77, 0.08364, 23, 6.8, -4.78, 0.76553, 24, -2.29, -6.8, 0.15083, 3, 21, 10.06, -6.95, 0.3109, 22, 2.39, -5.8, 0.65525, 23, -1.35, -9.5, 0.03385, 2, 21, 8.8, -17.35, 0.5263, 22, 5, -15.95, 0.4737, 2, 21, 5.14, -25.39, 0.50936, 22, 4.51, -24.77, 0.49064, 2, 21, -6.65, -26.62, 0.50054, 22, -6.04, -30.2, 0.49946, 2, 21, -14.29, -22.38, 0.5, 22, -14.69, -29.03, 0.5, 2, 21, -5.4, -13.56, 0.50623, 22, -9.61, -17.58, 0.49377, 2, 21, 4.05, -12.73, 0.54738, 22, -1.11, -13.37, 0.45262, 4, 20, 9.83, -6.09, 0.0785, 19, 21.44, -4.26, 0.00235, 21, 0.58, -5.67, 0.79623, 22, -6.91, -8.05, 0.12292, 1, 19, 6.78, -6.81, 1, 2, 18, 5.74, -6.93, 0.9646, 19, -7.78, -6.3, 0.0354, 2, 18, -4.13, -4.24, 0.64938, 17, 5.05, 3.97, 0.35062, 2, 18, -8.19, 1.34, 0.00246, 17, 4.14, -2.87, 0.99754, 1, 17, 3.4, -6.73, 1, 1, 17, 1.23, -2.85, 1, 1, 18, 4.24, -0.54, 1, 1, 19, 3.66, -0.63, 1, 2, 21, 1.02, -1.35, 0.98407, 22, -8.07, -3.87, 0.01593, 2, 22, 9.41, 1.05, 0.70423, 23, -1.82, 0.29, 0.29577, 1, 23, 6, 2.8, 1, 2, 23, 14.31, 0.26, 0.0405, 24, 0.38, 1.85, 0.9595, 2, 21, 6.74, -10.45, 0.55266, 22, 0.56, -10.27, 0.44734, 2, 21, 2.47, -18.63, 0.51549, 22, -0.44, -19.44, 0.48451, 2, 21, -7.59, -21.33, 0.50088, 22, -8.83, -25.62, 0.49912], "hull": 25}}, "toc3": {"toc3": {"type": "mesh", "uvs": [0, 0.01834, 0.02431, 0.18774, 0.20097, 0.41214, 0.49137, 0.74654, 0.84711, 0.99294, 1, 1, 0.98021, 0.84994, 0.71643, 0.62994, 0.64625, 0.42754, 0.56155, 0.22734, 0.38731, 0, 0.13563, 0, 0.07513, 0.05794, 0.20823, 0.18334, 0.36553, 0.35494, 0.52041, 0.55734, 0.65109, 0.72674, 0.77935, 0.82794, 0.87857, 0.87414], "triangles": [6, 17, 7, 4, 3, 17, 18, 17, 6, 4, 17, 18, 18, 6, 5, 4, 18, 5, 16, 15, 7, 16, 7, 17, 3, 16, 17, 15, 14, 8, 15, 8, 7, 3, 15, 16, 3, 2, 15, 13, 11, 10, 12, 11, 13, 1, 12, 13, 14, 13, 10, 2, 1, 13, 9, 14, 10, 2, 13, 14, 14, 9, 8, 15, 2, 14, 12, 0, 11, 1, 0, 12], "vertices": [1, 25, 2.61, 2.4, 1, 3, 25, -6.78, 2.42, 0.21935, 27, -10.44, -8.96, 0.02728, 26, 0.11, -6.86, 0.75337, 3, 27, 4.74, -8.92, 0.92592, 26, 14.93, -10.12, 0.06348, 28, -5.19, -9.34, 0.0106, 3, 28, 18.07, -6.46, 0.01366, 29, 9.45, -6.02, 0.52194, 30, -1.24, -6.23, 0.46441, 2, 32, 3.46, -4.75, 0.91329, 31, 11.42, -3.87, 0.08671, 1, 32, 9.94, -0.69, 1, 2, 32, 4.4, 5.51, 0.97801, 31, 11.46, 6.43, 0.02199, 5, 28, 18.42, 6.49, 0.00138, 29, 8.81, 6.92, 0.24299, 30, 1.24, 6.48, 0.65862, 32, -13.34, 7.86, 0.00014, 31, -6.42, 7.22, 0.09688, 5, 27, 18.34, 8.72, 0.08426, 26, 32.04, 4.14, 0.00222, 28, 7.08, 9.26, 0.50405, 29, -2.71, 8.81, 0.38851, 30, -9.48, 11.09, 0.02096, 4, 27, 6.91, 11.66, 0.64791, 26, 21.53, 9.5, 0.22164, 28, -4.53, 11.35, 0.11897, 29, -14.45, 10, 0.01148, 2, 27, -8.33, 11.81, 0.02437, 26, 6.69, 12.96, 0.97563, 2, 25, 2.73, -4.45, 0.32444, 26, -2.67, 4.54, 0.67556, 2, 25, -0.04, -1.04, 0.77951, 26, -2.78, 0.15, 0.22049, 2, 27, -5.31, -1.33, 0.00998, 26, 6.78, -0.52, 0.99002, 1, 27, 6.94, -0.4, 1, 3, 27, 20.5, -0.54, 6e-05, 28, 9.92, 0.18, 0.00149, 29, 0.82, -0.03, 0.99845, 3, 29, 12.08, 1.6, 0.00056, 30, 3.15, 0.53, 0.98963, 31, -5.5, 1.05, 0.00981, 2, 32, -4.52, 0.74, 0.03128, 31, 2.99, 0.9, 0.96872, 2, 32, 1, 1.5, 0.96317, 31, 8.42, 2.15, 0.03683], "hull": 12}}, "xx1": {"xx1": {"x": 0.94, "y": 3.23, "width": 64, "height": 64}}, "xx2": {"xx2": {"x": 2.14, "y": 2.18, "width": 71, "height": 70}}, "xx3": {"xx3": {"x": 1.14, "y": 4.44, "width": 93, "height": 84}}, "xx4": {"xx2": {"x": 2.14, "y": 2.18, "width": 71, "height": 70}}, "xx5": {"xx3": {"x": 1.14, "y": 4.44, "width": 93, "height": 84}}, "xx6": {"xx1": {"x": 0.94, "y": 3.23, "width": 64, "height": 64}}, "Tai": {"Tai": {"x": -10.02, "y": 11.12, "scaleX": 1.0987, "scaleY": 1.0987, "width": 121, "height": 85}}}}], "animations": {"animation": {"slots": {"xx5": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 2.5, "color": "ffffff34"}, {"time": 4, "color": "ffffff00"}]}, "xx4": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 2.3333, "color": "ffffff5c"}, {"time": 4, "color": "ffffff00"}]}, "bg1": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.5, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}, {"time": 2.1667, "color": "ffffffff"}, {"time": 2.3, "color": "ffffff00"}, {"time": 2.5, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.3333, "color": "ffffff00"}, {"time": 3.5, "color": "ffffffff"}]}}, "bones": {"xx3": {"rotate": [{}, {"time": 1, "angle": 180}, {"time": 2}, {"time": 3, "angle": 180}, {"time": 4}], "translate": [{"y": 20.96, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": 29.27, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "y": 20.96, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.5, "y": 91.98, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "y": 29.27, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "y": 20.96}]}, "xx2": {"rotate": [{}, {"time": 1, "angle": 180}, {"time": 2}, {"time": 3, "angle": 180}, {"time": 4}], "translate": [{"y": -6, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": -21.14, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "y": -6, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "y": -67.89, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.8333, "y": 62.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333, "y": -21.14, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "y": -6}]}, "Xiu": {"scale": [{"time": 0.3333}, {"time": 0.4333, "x": 1.27, "y": 1.27}, {"time": 0.5}, {"time": 0.5667, "x": 1.27, "y": 1.27}, {"time": 0.6667, "curve": "stepped"}, {"time": 2.3333}, {"time": 2.4333, "x": 1.27, "y": 1.27}, {"time": 2.5}, {"time": 2.5667, "x": 1.27, "y": 1.27}, {"time": 2.6667}]}, "toc8": {"rotate": [{"angle": -0.28, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 1.85, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -5.66, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.28, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 1.85, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -5.66, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -0.28}]}, "toc9": {"rotate": [{"angle": 1.13, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 1.85, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -5.66, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 1.13, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 1.85, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -5.66, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 1.13}]}, "toc20": {"rotate": [{"angle": -23.2, "curve": 0.344, "c2": 0.66, "c3": 0.677}, {"time": 0.0333, "angle": -23.52, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 4.87, "curve": 0.248, "c3": 0.736, "c4": 0.94}, {"time": 2, "angle": -23.2, "curve": 0.344, "c2": 0.66, "c3": 0.677}, {"time": 2.0333, "angle": -23.52, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": 4.87, "curve": 0.248, "c3": 0.736, "c4": 0.94}, {"time": 4, "angle": -23.2}]}, "toc21": {"rotate": [{"angle": 1, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": 4.87, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 0.59, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 1, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "angle": 4.87, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": 0.59, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "angle": 1}]}, "toc7": {"rotate": [{"angle": -8.12, "curve": 0.344, "c2": 0.66, "c3": 0.677}, {"time": 0.0333, "angle": -8.3, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 7.6, "curve": 0.248, "c3": 0.736, "c4": 0.94}, {"time": 2, "angle": -8.12, "curve": 0.344, "c2": 0.66, "c3": 0.677}, {"time": 2.0333, "angle": -8.3, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": 7.6, "curve": 0.248, "c3": 0.736, "c4": 0.94}, {"time": 4, "angle": -8.12}]}, "toc4": {"rotate": [{"angle": -5.31, "curve": 0.322, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 0.1, "angle": -3.46, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.7667, "angle": 7.6, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": -7.84, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 2, "angle": -5.31, "curve": 0.322, "c2": 0.29, "c3": 0.657, "c4": 0.63}, {"time": 2.1, "angle": -3.46, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.7667, "angle": 7.6, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "angle": -7.84, "curve": 0.278, "c3": 0.622, "c4": 0.39}, {"time": 4, "angle": -5.31}]}, "toc5": {"rotate": [{"angle": -2.56, "curve": 0.33, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.1, "angle": -0.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6, "angle": 7.6, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -8.48, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "angle": -2.56, "curve": 0.33, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 2.1, "angle": -0.44, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.6, "angle": 7.6, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "angle": -8.48, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 4, "angle": -2.56}]}, "toc2": {"rotate": [{"angle": 4.77, "curve": 0.338, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.1, "angle": 5.65, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4333, "angle": 7.6, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": 0.72, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": 4.77, "curve": 0.338, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 2.1, "angle": 5.65, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.4333, "angle": 7.6, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "angle": 0.72, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 4, "angle": 4.77}]}, "cha": {"rotate": [{"angle": -0.01}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "y": 13.28, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.25, "c3": 0.75}, {"time": 3, "y": 13.28, "curve": 0.25, "c3": 0.75}, {"time": 4}]}, "toc3": {"rotate": [{"angle": 6.48, "curve": 0.343, "c2": 0.37, "c3": 0.677, "c4": 0.71}, {"time": 0.0667, "angle": 6.94, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.2333, "angle": 7.6, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 0.72, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 2, "angle": 6.48, "curve": 0.343, "c2": 0.37, "c3": 0.677, "c4": 0.71}, {"time": 2.0667, "angle": 6.94, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.2333, "angle": 7.6, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "angle": 0.72, "curve": 0.242, "c3": 0.671, "c4": 0.68}, {"time": 4, "angle": 6.48}]}, "toc23": {"rotate": [{"angle": -39.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": -45.73, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 4.87, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "angle": -39.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.2, "angle": -45.73, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "angle": 4.87, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4, "angle": -39.15}]}, "toc19": {"rotate": [{"angle": 2.91, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "angle": 4.87, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 0.59, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 2.91, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 2.4667, "angle": 4.87, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": 0.59, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4, "angle": 2.91}]}, "xx1": {"rotate": [{}, {"time": 1, "angle": 180}, {"time": 2}, {"time": 3, "angle": 180}, {"time": 4}], "translate": [{"y": -32.57, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0667, "y": -33.33, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 2, "y": -32.57, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 2.3333, "y": -33.33, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 2.6667, "y": 161.91, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 3.3333, "y": -71.06, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 4, "y": -32.57}]}, "toc10": {"rotate": [{"angle": -3.84, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7, "angle": 1.85, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -5.66, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": -3.84, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 2.7, "angle": 1.85, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "angle": -5.66, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 4, "angle": -3.84}]}, "cha5": {"rotate": [{"angle": -0.78, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -8.11, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -0.78, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -8.11, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": -0.78}]}, "toc17": {"rotate": [{"angle": 4.45, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 4.87, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 0.59, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 4.45, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "angle": 4.87, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 0.59, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 4.45}]}, "toc18": {"rotate": [{"angle": 1.8, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 4.87, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 0.59, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.8, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "angle": 4.87, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 0.59, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 1.8}]}, "toc15": {"rotate": [{"angle": -4.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": -5.66, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 1.85, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "angle": -4.69, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.2, "angle": -5.66, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "angle": 1.85, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4, "angle": -4.69}]}, "toc16": {"rotate": [{"angle": 3.65, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 4.87, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.59, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 3.65, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "angle": 4.87, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 0.59, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 3.65}]}, "toc13": {"rotate": [{"angle": -5.14, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.8667, "angle": 1.85, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -5.66, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": -5.14, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 2.8667, "angle": 1.85, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "angle": -5.66, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 4, "angle": -5.14}]}, "Tai": {"scale": [{}, {"time": 0.1, "x": 1.27, "y": 1.27}, {"time": 0.1667}, {"time": 0.2333, "x": 1.27, "y": 1.27}, {"time": 0.3333, "curve": "stepped"}, {"time": 2}, {"time": 2.1, "x": 1.27, "y": 1.27}, {"time": 2.1667}, {"time": 2.2333, "x": 1.27, "y": 1.27}, {"time": 2.3333}]}, "toc11": {"rotate": [{"angle": -1.91, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 1.85, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -5.66, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -1.91, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "angle": 1.85, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -5.66, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -1.91}]}, "toc12": {"rotate": [{"angle": -5.58, "curve": 0.344, "c2": 0.66, "c3": 0.677}, {"time": 0.0333, "angle": -5.66, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 1.85, "curve": 0.248, "c3": 0.736, "c4": 0.94}, {"time": 2, "angle": -5.58, "curve": 0.344, "c2": 0.66, "c3": 0.677}, {"time": 2.0333, "angle": -5.66, "curve": 0.25, "c3": 0.75}, {"time": 3.0333, "angle": 1.85, "curve": 0.248, "c3": 0.736, "c4": 0.94}, {"time": 4, "angle": -5.58}]}}, "deform": {"default": {"cha": {"cha": [{"offset": 114, "vertices": [-1.8249, 0.02764, -1.81816, -0.15915, -2.59199, 0.03924, -2.58241, -0.22605, -2.59199, 0.03924, -2.58241, -0.22605, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.56681, 0.42805, -2.59199, 0.03924, -2.56681, 0.42805, -2.59199, 0.03924, -2.58241, -0.22605, -2.59199, 0.03924, -2.58241, -0.22605, -2.59199, 0.03924, -2.59199, 0.03924, -2.56681, 0.42805, -2.59199, 0.03924, -2.56681, 0.42805, -2.59199, 0.03924, -2.56681, 0.42805, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -1.8249, 0.02764, -2.10554, 0.03188, -2.10554, 0.03188, -2.10554, 0.03188, -2.10554, 0.03188, -2.10554, 0.03188, -2.10554, 0.03188, -2.10554, 0.03188, -2.10554, 0.03188, -2.10554, 0.03188, -2.09775, -0.18363, -2.10554, 0.03188, -2.09775, -0.18363, -2.10554, 0.03188, -2.09775, -0.18363, -2.10554, 0.03188, -2.09775, -0.18363, -2.10554, 0.03188], "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "offset": 114, "vertices": [15.60233, -0.23609, 15.54461, 1.36083, 7.64939, -0.11575, 7.62111, 0.66717, 7.64939, -0.11575, 7.62111, 0.66717, 7.64939, -0.11575, 7.64939, -0.11575, 7.64939, -0.11575, 7.64939, -0.11575, 7.64939, -0.11575, 7.64939, -0.11575, 7.64939, -0.11575, 7.67577, 0.39586, 7.64939, -0.11575, 7.67577, 0.39586, 7.64939, -0.11575, 7.62111, 0.66717, 7.64939, -0.11575, 7.62111, 0.66717, 7.64939, -0.11575, 7.64939, -0.11575, 7.67577, 0.39586, 7.64939, -0.11575, 7.67577, 0.39586, 7.64939, -0.11575, 7.67577, 0.39586, 7.64939, -0.11575, 7.64939, -0.11575, 7.64939, -0.11575, 7.64939, -0.11575, 7.64939, -0.11575, 7.64939, -0.11575, 15.60233, -0.23609, 12.69275, -0.19208, 12.69275, -0.19208, 12.69275, -0.19208, 12.69275, -0.19208, 12.69275, -0.19208, 12.69275, -0.19208, 12.69275, -0.19208, 12.69275, -0.19208, 12.69275, -0.19208, 12.64584, 1.10704, 12.69275, -0.19208, 12.64584, 1.10704, 12.69275, -0.19208, 12.64584, 1.10704, 12.69275, -0.19208, 12.64584, 1.10704, 12.69275, -0.19208], "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "offset": 114, "vertices": [-3.68526, 0.05579, -3.67164, -0.32141, -3.68526, 0.05579, -3.67164, -0.32141, -3.68526, 0.05579, -3.67164, -0.32141, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.66021, 0.43148, -3.68526, 0.05579, -3.66021, 0.43148, -3.68526, 0.05579, -3.67164, -0.32141, -3.68526, 0.05579, -3.67164, -0.32141, -3.68526, 0.05579, -3.68526, 0.05579, -3.66021, 0.43148, -3.68526, 0.05579, -3.66021, 0.43148, -3.68526, 0.05579, -3.66021, 0.43148, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.67164, -0.32141, -3.68526, 0.05579, -3.67164, -0.32141, -3.68526, 0.05579, -3.67164, -0.32141, -3.68526, 0.05579, -3.67164, -0.32141, -3.68526, 0.05579], "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 1.9, "offset": 114, "vertices": [-3.31362, 0.05017, -3.30136, -0.28899, -3.46686, 0.05249, -3.45404, -0.30236, -3.46686, 0.05249, -3.45404, -0.30236, -3.46686, 0.05249, -3.46686, 0.05249, -3.46686, 0.05249, -3.46686, 0.05249, -3.46686, 0.05249, -3.46686, 0.05249, -3.46686, 0.05249, -3.44178, 0.4308, -3.46686, 0.05249, -3.44178, 0.4308, -3.46686, 0.05249, -3.45404, -0.30236, -3.46686, 0.05249, -3.45404, -0.30236, -3.46686, 0.05249, -3.46686, 0.05249, -3.44178, 0.4308, -3.46686, 0.05249, -3.44178, 0.4308, -3.46686, 0.05249, -3.44178, 0.4308, -3.46686, 0.05249, -3.46686, 0.05249, -3.46686, 0.05249, -3.46686, 0.05249, -3.46686, 0.05249, -3.46686, 0.05249, -3.31362, 0.05017, -3.36968, 0.05101, -3.36968, 0.05101, -3.36968, 0.05101, -3.36968, 0.05101, -3.36968, 0.05101, -3.36968, 0.05101, -3.36968, 0.05101, -3.36968, 0.05101, -3.36968, 0.05101, -3.35722, -0.29388, -3.36968, 0.05101, -3.35722, -0.29388, -3.36968, 0.05101, -3.35722, -0.29388, -3.36968, 0.05101, -3.35722, -0.29388, -3.36968, 0.05101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.80864, -0.54779, -0.85399, -0.47484, -2.62482, -0.28029, -2.63962, -0.04934, -4.09141, -0.69814, -4.13718, -0.33715, -3.99822, 1.52076, -3.85002, 1.86508, -2.4066, 0.9166, -2.31732, 1.12384, 0, 0, 0, 0, 1.1926, 0.82211, 1.25981, 0.71456, 1.99891, -0.09022, 1.98311, -0.26489, 1.72775, 0.53396, 1.76741, 0.38069, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.40359, -0.94227, -4.14755, -1.08, -4.26425, -0.22956, -2.87436, -0.33878, 0, 0, 1.31657, 1.94716, 1.95567, 1.65804, 2.01289, 0.99877, 0.90002, -1.01768], "curve": 0.311, "c2": 0.2, "c3": 0.646, "c4": 0.55}, {"time": 2, "offset": 114, "vertices": [-1.8249, 0.02764, -1.81816, -0.15915, -2.59199, 0.03924, -2.58241, -0.22605, -2.59199, 0.03924, -2.58241, -0.22605, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.56681, 0.42805, -2.59199, 0.03924, -2.56681, 0.42805, -2.59199, 0.03924, -2.58241, -0.22605, -2.59199, 0.03924, -2.58241, -0.22605, -2.59199, 0.03924, -2.59199, 0.03924, -2.56681, 0.42805, -2.59199, 0.03924, -2.56681, 0.42805, -2.59199, 0.03924, -2.56681, 0.42805, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -1.8249, 0.02764, -2.10554, 0.03188, -2.10554, 0.03188, -2.10554, 0.03188, -2.10554, 0.03188, -2.10554, 0.03188, -2.10554, 0.03188, -2.10554, 0.03188, -2.10554, 0.03188, -2.10554, 0.03188, -2.09775, -0.18363, -2.10554, 0.03188, -2.09775, -0.18363, -2.10554, 0.03188, -2.09775, -0.18363, -2.10554, 0.03188, -2.09775, -0.18363, -2.10554, 0.03188], "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "offset": 114, "vertices": [15.60233, -0.23609, 15.54461, 1.36083, 7.64939, -0.11575, 7.62111, 0.66717, 7.64939, -0.11575, 7.62111, 0.66717, 7.64939, -0.11575, 7.64939, -0.11575, 7.64939, -0.11575, 7.64939, -0.11575, 7.64939, -0.11575, 7.64939, -0.11575, 7.64939, -0.11575, 7.67577, 0.39586, 7.64939, -0.11575, 7.67577, 0.39586, 7.64939, -0.11575, 7.62111, 0.66717, 7.64939, -0.11575, 7.62111, 0.66717, 7.64939, -0.11575, 7.64939, -0.11575, 7.67577, 0.39586, 7.64939, -0.11575, 7.67577, 0.39586, 7.64939, -0.11575, 7.67577, 0.39586, 7.64939, -0.11575, 7.64939, -0.11575, 7.64939, -0.11575, 7.64939, -0.11575, 7.64939, -0.11575, 7.64939, -0.11575, 15.60233, -0.23609, 12.69275, -0.19208, 12.69275, -0.19208, 12.69275, -0.19208, 12.69275, -0.19208, 12.69275, -0.19208, 12.69275, -0.19208, 12.69275, -0.19208, 12.69275, -0.19208, 12.69275, -0.19208, 12.64584, 1.10704, 12.69275, -0.19208, 12.64584, 1.10704, 12.69275, -0.19208, 12.64584, 1.10704, 12.69275, -0.19208, 12.64584, 1.10704, 12.69275, -0.19208], "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "offset": 114, "vertices": [-3.68526, 0.05579, -3.67164, -0.32141, -3.68526, 0.05579, -3.67164, -0.32141, -3.68526, 0.05579, -3.67164, -0.32141, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.66021, 0.43148, -3.68526, 0.05579, -3.66021, 0.43148, -3.68526, 0.05579, -3.67164, -0.32141, -3.68526, 0.05579, -3.67164, -0.32141, -3.68526, 0.05579, -3.68526, 0.05579, -3.66021, 0.43148, -3.68526, 0.05579, -3.66021, 0.43148, -3.68526, 0.05579, -3.66021, 0.43148, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.68526, 0.05579, -3.67164, -0.32141, -3.68526, 0.05579, -3.67164, -0.32141, -3.68526, 0.05579, -3.67164, -0.32141, -3.68526, 0.05579, -3.67164, -0.32141, -3.68526, 0.05579], "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 3.9, "offset": 114, "vertices": [-3.31362, 0.05017, -3.30136, -0.28899, -3.46686, 0.05249, -3.45404, -0.30236, -3.46686, 0.05249, -3.45404, -0.30236, -3.46686, 0.05249, -3.46686, 0.05249, -3.46686, 0.05249, -3.46686, 0.05249, -3.46686, 0.05249, -3.46686, 0.05249, -3.46686, 0.05249, -3.44178, 0.4308, -3.46686, 0.05249, -3.44178, 0.4308, -3.46686, 0.05249, -3.45404, -0.30236, -3.46686, 0.05249, -3.45404, -0.30236, -3.46686, 0.05249, -3.46686, 0.05249, -3.44178, 0.4308, -3.46686, 0.05249, -3.44178, 0.4308, -3.46686, 0.05249, -3.44178, 0.4308, -3.46686, 0.05249, -3.46686, 0.05249, -3.46686, 0.05249, -3.46686, 0.05249, -3.46686, 0.05249, -3.46686, 0.05249, -3.31362, 0.05017, -3.36968, 0.05101, -3.36968, 0.05101, -3.36968, 0.05101, -3.36968, 0.05101, -3.36968, 0.05101, -3.36968, 0.05101, -3.36968, 0.05101, -3.36968, 0.05101, -3.36968, 0.05101, -3.35722, -0.29388, -3.36968, 0.05101, -3.35722, -0.29388, -3.36968, 0.05101, -3.35722, -0.29388, -3.36968, 0.05101, -3.35722, -0.29388, -3.36968, 0.05101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.80864, -0.54779, -0.85399, -0.47484, -2.62482, -0.28029, -2.63962, -0.04934, -4.09141, -0.69814, -4.13718, -0.33715, -3.99822, 1.52076, -3.85002, 1.86508, -2.4066, 0.9166, -2.31732, 1.12384, 0, 0, 0, 0, 1.1926, 0.82211, 1.25981, 0.71456, 1.99891, -0.09022, 1.98311, -0.26489, 1.72775, 0.53396, 1.76741, 0.38069, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.40359, -0.94227, -4.14755, -1.08, -4.26425, -0.22956, -2.87436, -0.33878, 0, 0, 1.31657, 1.94716, 1.95567, 1.65804, 2.01289, 0.99877, 0.90002, -1.01768], "curve": 0.311, "c2": 0.2, "c3": 0.646, "c4": 0.55}, {"time": 4, "offset": 114, "vertices": [-1.8249, 0.02764, -1.81816, -0.15915, -2.59199, 0.03924, -2.58241, -0.22605, -2.59199, 0.03924, -2.58241, -0.22605, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.56681, 0.42805, -2.59199, 0.03924, -2.56681, 0.42805, -2.59199, 0.03924, -2.58241, -0.22605, -2.59199, 0.03924, -2.58241, -0.22605, -2.59199, 0.03924, -2.59199, 0.03924, -2.56681, 0.42805, -2.59199, 0.03924, -2.56681, 0.42805, -2.59199, 0.03924, -2.56681, 0.42805, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -2.59199, 0.03924, -1.8249, 0.02764, -2.10554, 0.03188, -2.10554, 0.03188, -2.10554, 0.03188, -2.10554, 0.03188, -2.10554, 0.03188, -2.10554, 0.03188, -2.10554, 0.03188, -2.10554, 0.03188, -2.10554, 0.03188, -2.09775, -0.18363, -2.10554, 0.03188, -2.09775, -0.18363, -2.10554, 0.03188, -2.09775, -0.18363, -2.10554, 0.03188, -2.09775, -0.18363, -2.10554, 0.03188]}]}}}}}}