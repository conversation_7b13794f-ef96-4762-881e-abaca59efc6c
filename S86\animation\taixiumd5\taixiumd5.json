{"skeleton": {"hash": "qdlADmCh993LU253wp6bWwvBXOs", "spine": "3.8.75", "x": -148.52, "y": -204.95, "width": 317.66, "height": 430.4}, "bones": [{"name": "root"}, {"name": "xx2", "parent": "root", "x": 61.74, "y": 98.75}, {"name": "xx1", "parent": "root", "x": -83.44, "y": 31.41}, {"name": "xx3", "parent": "root", "x": -51.97, "y": 148.98}, {"name": "tx", "parent": "root", "y": -25.13, "scaleX": 1.2, "scaleY": 1.2}, {"name": "dg2", "parent": "root", "x": 59.78, "y": -75.74}, {"name": "cha", "parent": "root", "length": 125.79, "rotation": 87.9, "x": -4.65, "y": -27, "scaleX": 1.08, "scaleY": 1.08}, {"name": "cha3", "parent": "cha", "length": 61.84, "rotation": -8.38, "x": 136.57, "y": -1.14}, {"name": "bone", "parent": "root", "x": 88.14, "y": 132.21, "scaleX": 0.324, "scaleY": 0.324}, {"name": "bone2", "parent": "root", "x": -89.27, "y": 38.42, "scaleX": 0.237, "scaleY": 0.237}, {"name": "bone3", "parent": "root", "x": -75.71, "y": 167.24, "scaleX": 0.272, "scaleY": 0.272}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "xx2", "bone": "xx2", "attachment": "xx2"}, {"name": "xx3", "bone": "xx3", "attachment": "xx3"}, {"name": "cha", "bone": "cha3", "attachment": "cha"}, {"name": "xx1", "bone": "xx1", "attachment": "xx1"}, {"name": "tx", "bone": "tx", "attachment": "tx"}, {"name": "dg2", "bone": "dg2", "attachment": "dg"}, {"name": "bg2", "bone": "root", "attachment": "bg2"}, {"name": "900", "bone": "bone", "attachment": "915", "blend": "additive"}, {"name": "901", "bone": "bone2", "attachment": "915", "blend": "additive"}, {"name": "902", "bone": "bone3", "attachment": "915", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"tx": {"tx": {"y": 5.41, "width": 176, "height": 66}}, "bg": {"bg": {"x": -1.34, "y": -0.55, "scaleX": 0.7, "scaleY": 0.7, "width": 386, "height": 584}}, "bg2": {"bg2": {"y": -143.27, "width": 215, "height": 99}}, "dg2": {"dg": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [37.39, -37.59, -158.57, -37.58, -158.56, 30.58, 37.4, 30.57], "hull": 4}}, "cha": {"cha": {"type": "mesh", "uvs": [0, 1, 0, 0.74801, 0.04315, 0.66474, 0, 0.6169, 0.06903, 0.49996, 0.10461, 0.37771, 0.234, 0.3405, 0.27606, 0.22596, 0.25988, 0.16926, 0.31164, 0.07182, 0.4281, 0.00095, 0.65454, 0, 0.89391, 0.03992, 0.97479, 0.16041, 0.84216, 0.39605, 0.96832, 0.44035, 0.99096, 0.61044, 1, 0.72915, 0.94244, 0.78053, 1, 0.99314, 0.33666, 0.75969, 0.73209, 0.75798, 0.86286, 0.66418, 0.53281, 0.65054, 0.19966, 0.6369, 0.35845, 0.64372, 0.72275, 0.64372, 0.50479, 0.85348, 0.50479, 0.96945, 0.57952, 0.29753, 0.51278, 0.20975, 0.52653, 0.20495, 0.54228, 0.204, 0.56028, 0.20523, 0.57603, 0.21112, 0.59328, 0.22166, 0.58903, 0.22768, 0.56903, 0.23124, 0.54403, 0.23042, 0.52428, 0.22399, 0.50828, 0.21481, 0.50403, 0.21454, 0.51003, 0.20783, 0.52578, 0.20208, 0.54453, 0.20126, 0.56703, 0.20304, 0.58478, 0.21084, 0.60103, 0.22207, 0.59128, 0.23111, 0.56752, 0.23385, 0.54203, 0.23289, 0.52103, 0.22645, 0.67113, 0.23924, 0.68438, 0.23088, 0.70388, 0.22678, 0.72938, 0.22678, 0.75313, 0.23266, 0.76213, 0.23883, 0.74838, 0.24608, 0.73013, 0.25156, 0.70788, 0.24992, 0.68663, 0.24732, 0.66863, 0.24198, 0.68488, 0.25019, 0.70863, 0.25252, 0.73163, 0.25444, 0.75038, 0.248, 0.76713, 0.23883, 0.75513, 0.23021, 0.72888, 0.22404, 0.70138, 0.22404, 0.68063, 0.2291, 0.66538, 0.23883, 0.36788, 0.54174, 0.69712, 0.55054, 0.40201, 0.37571, 0.5325, 0.41639, 0.67705, 0.3966], "triangles": [44, 10, 11, 43, 9, 10, 7, 8, 9, 10, 44, 43, 45, 44, 11, 32, 43, 44, 31, 43, 32, 33, 44, 45, 32, 44, 33, 43, 42, 9, 30, 42, 43, 31, 30, 43, 46, 45, 11, 34, 45, 46, 33, 45, 34, 42, 41, 9, 40, 41, 42, 40, 42, 30, 47, 35, 46, 34, 46, 35, 47, 46, 71, 39, 30, 31, 40, 30, 39, 69, 70, 11, 12, 69, 11, 11, 71, 46, 12, 68, 69, 41, 7, 9, 51, 40, 39, 41, 40, 51, 69, 56, 55, 54, 70, 69, 54, 69, 55, 36, 34, 35, 11, 70, 71, 68, 12, 67, 38, 32, 33, 39, 31, 32, 38, 39, 32, 53, 71, 70, 53, 70, 54, 48, 36, 35, 48, 35, 47, 37, 33, 34, 37, 34, 36, 38, 33, 37, 68, 56, 69, 50, 39, 38, 51, 39, 50, 49, 38, 37, 48, 49, 37, 48, 37, 36, 50, 38, 49, 13, 67, 12, 72, 47, 71, 57, 68, 67, 56, 68, 57, 52, 72, 71, 52, 71, 53, 62, 72, 52, 58, 55, 56, 58, 56, 57, 61, 53, 54, 52, 53, 61, 66, 58, 57, 66, 57, 67, 60, 54, 55, 59, 60, 55, 61, 54, 60, 63, 52, 61, 62, 52, 63, 58, 59, 55, 64, 60, 59, 61, 60, 64, 63, 61, 64, 65, 59, 58, 64, 59, 65, 65, 58, 66, 29, 49, 48, 75, 7, 41, 75, 41, 51, 6, 7, 75, 14, 67, 13, 66, 67, 14, 63, 29, 62, 77, 63, 64, 77, 64, 65, 51, 29, 75, 50, 29, 51, 29, 50, 49, 48, 47, 72, 29, 48, 72, 29, 72, 62, 77, 29, 63, 65, 66, 14, 65, 14, 77, 76, 75, 29, 73, 6, 75, 73, 75, 76, 14, 74, 77, 6, 4, 5, 24, 6, 73, 24, 4, 6, 2, 3, 4, 25, 24, 73, 76, 29, 77, 74, 22, 26, 74, 23, 76, 74, 76, 77, 23, 74, 26, 73, 76, 23, 25, 73, 23, 22, 14, 15, 22, 15, 16, 22, 74, 14, 24, 2, 4, 22, 16, 17, 21, 26, 22, 20, 24, 25, 18, 22, 17, 21, 22, 18, 23, 20, 25, 27, 20, 23, 27, 21, 28, 20, 27, 28, 1, 20, 0, 24, 1, 2, 20, 1, 24, 0, 20, 28, 23, 21, 27, 26, 21, 23, 19, 28, 21, 19, 21, 18, 0, 28, 19], "vertices": [1, 6, -21.82, 60.94, 1, 1, 6, 38.87, 63.16, 1, 1, 6, 59.13, 58.2, 1, 1, 6, 70.45, 64.31, 1, 2, 6, 98.94, 56.24, 0.98329, 7, -45.59, 51.28, 0.01671, 2, 6, 128.56, 52.62, 0.84447, 7, -15.76, 52.02, 0.15553, 2, 6, 138.14, 35.88, 0.55669, 7, -3.84, 36.85, 0.44331, 2, 6, 165.93, 31.34, 0.03519, 7, 24.31, 36.41, 0.96481, 2, 6, 179.51, 33.98, 0.00144, 7, 37.36, 41, 0.99856, 1, 7, 61.7, 38.55, 1, 1, 7, 81.29, 26.54, 1, 1, 7, 86.95, -2.81, 1, 1, 7, 83.23, -35.63, 1, 2, 6, 185.09, -60.25, 0.00025, 7, 56.62, -51.41, 0.99975, 2, 6, 127.7, -44.83, 0.56627, 7, -2.41, -44.52, 0.43373, 2, 6, 117.64, -61.86, 0.82657, 7, -9.88, -62.83, 0.17343, 2, 6, 76.78, -66.35, 0.98465, 7, -49.65, -73.23, 0.01535, 2, 6, 48.24, -68.59, 0.99999, 7, -77.56, -79.6, 1e-05, 1, 6, 35.58, -61.45, 1, 1, 6, -15.34, -70.91, 1, 1, 6, 37.68, 18.65, 1, 1, 6, 40, -33.5, 1, 2, 6, 63.22, -49.92, 0.99772, 7, -65.46, -58.95, 0.00228, 1, 6, 64.91, -6.27, 1, 1, 6, 66.59, 37.8, 1, 1, 6, 65.72, 16.79, 1, 2, 6, 67.47, -31.26, 0.99864, 7, -63.97, -39.87, 0.00136, 1, 6, 15.9, -4.36, 1, 1, 6, -12.03, -5.38, 1, 2, 6, 150.16, -9.32, 0.00831, 7, 14.64, -6.11, 0.99169, 1, 7, 33.84, 6.4, 1, 1, 7, 35.3, 4.82, 1, 1, 7, 35.91, 2.82, 1, 1, 7, 36.05, 0.43, 1, 1, 7, 35.03, -1.87, 1, 2, 6, 168.5, -10.46, 1e-05, 7, 32.95, -4.57, 0.99999, 2, 6, 167.03, -9.96, 3e-05, 7, 31.42, -4.28, 0.99997, 1, 7, 30.09, -1.84, 1, 1, 7, 29.69, 1.44, 1, 1, 7, 30.74, 4.28, 1, 1, 7, 32.53, 6.76, 1, 1, 7, 32.49, 7.32, 1, 1, 7, 34.23, 6.84, 1, 1, 7, 35.97, 5.05, 1, 1, 7, 36.61, 2.65, 1, 1, 7, 36.73, -0.35, 1, 1, 7, 35.31, -3, 1, 2, 6, 168.44, -11.49, 7e-05, 7, 33.03, -5.6, 0.99993, 2, 6, 166.21, -10.28, 8e-05, 7, 30.66, -4.73, 0.99992, 1, 7, 29.44, -1.76, 1, 1, 7, 29.05, 1.59, 1, 1, 7, 30.08, 4.6, 1, 2, 6, 164.64, -20.89, 0.00704, 7, 30.65, -15.45, 0.99296, 2, 6, 166.72, -22.56, 0.00603, 7, 32.95, -16.8, 0.99397, 2, 6, 167.8, -25.1, 0.00701, 7, 34.39, -19.15, 0.99299, 2, 6, 167.92, -28.46, 0.01003, 7, 35, -22.46, 0.98997, 2, 6, 166.62, -31.65, 0.01598, 7, 34.17, -25.8, 0.98402, 2, 6, 165.18, -32.89, 0.02124, 7, 32.93, -27.24, 0.97876, 2, 6, 163.36, -31.14, 0.02476, 7, 30.88, -25.78, 0.97524, 2, 6, 161.96, -28.78, 0.02595, 7, 29.14, -23.65, 0.97405, 2, 6, 162.24, -25.83, 0.0199, 7, 29, -20.69, 0.9801, 2, 6, 162.77, -23.01, 0.01356, 7, 29.11, -17.81, 0.98644, 2, 6, 163.97, -20.58, 0.00767, 7, 29.94, -15.24, 0.99233, 2, 6, 162.07, -22.8, 0.01481, 7, 28.38, -17.71, 0.98519, 2, 6, 161.62, -25.95, 0.02208, 7, 28.4, -20.9, 0.97792, 2, 6, 161.27, -29, 0.0289, 7, 28.5, -23.97, 0.9711, 2, 6, 162.91, -31.42, 0.02672, 7, 30.47, -26.12, 0.97328, 2, 6, 165.2, -33.55, 0.0219, 7, 33.05, -27.89, 0.9781, 2, 6, 167.22, -31.89, 0.01474, 7, 34.8, -25.96, 0.98526, 2, 6, 168.58, -28.37, 0.0088, 7, 35.64, -22.28, 0.9912, 2, 6, 168.45, -24.75, 0.00584, 7, 34.98, -18.71, 0.99416, 2, 6, 167.13, -22.05, 0.00501, 7, 33.28, -16.24, 0.99499, 2, 6, 164.71, -20.13, 0.00607, 7, 30.61, -14.68, 0.99393, 1, 6, 90.32, 16.45, 1, 2, 6, 89.79, -27.06, 0.97613, 7, -42.5, -32.46, 0.02387, 2, 6, 130.47, 13.41, 0.60988, 7, -8.15, 13.5, 0.39012, 2, 6, 121.31, -4.16, 0.95381, 7, -14.66, -5.22, 0.04619, 2, 6, 126.77, -23.06, 0.55824, 7, -6.5, -23.11, 0.44176], "hull": 20}}, "xx1": {"xx1": {"x": 2.59, "y": -3.95, "width": 99, "height": 95}}, "xx2": {"xx2": {"x": 1.42, "y": -3.3, "width": 83, "height": 87}}, "xx3": {"xx3": {"x": 1.63, "y": -5.03, "width": 72, "height": 68}}, "900": {"900": {"width": 500, "height": 428}, "901": {"width": 500, "height": 428}, "902": {"width": 500, "height": 428}, "903": {"width": 500, "height": 428}, "904": {"width": 500, "height": 428}, "905": {"width": 500, "height": 428}, "906": {"width": 500, "height": 428}, "907": {"width": 500, "height": 428}, "908": {"width": 500, "height": 428}, "909": {"width": 500, "height": 428}, "910": {"width": 500, "height": 428}, "911": {"width": 500, "height": 428}, "912": {"width": 500, "height": 428}, "913": {"width": 500, "height": 428}, "914": {"width": 500, "height": 428}, "915": {"width": 500, "height": 428}}, "901": {"900": {"width": 500, "height": 428}, "901": {"width": 500, "height": 428}, "902": {"width": 500, "height": 428}, "903": {"width": 500, "height": 428}, "904": {"width": 500, "height": 428}, "905": {"width": 500, "height": 428}, "906": {"width": 500, "height": 428}, "907": {"width": 500, "height": 428}, "908": {"width": 500, "height": 428}, "909": {"width": 500, "height": 428}, "910": {"width": 500, "height": 428}, "911": {"width": 500, "height": 428}, "912": {"width": 500, "height": 428}, "913": {"width": 500, "height": 428}, "914": {"width": 500, "height": 428}, "915": {"width": 500, "height": 428}}, "902": {"900": {"width": 500, "height": 428}, "901": {"width": 500, "height": 428}, "902": {"width": 500, "height": 428}, "903": {"width": 500, "height": 428}, "904": {"width": 500, "height": 428}, "905": {"width": 500, "height": 428}, "906": {"width": 500, "height": 428}, "907": {"width": 500, "height": 428}, "908": {"width": 500, "height": 428}, "909": {"width": 500, "height": 428}, "910": {"width": 500, "height": 428}, "911": {"width": 500, "height": 428}, "912": {"width": 500, "height": 428}, "913": {"width": 500, "height": 428}, "914": {"width": 500, "height": 428}, "915": {"width": 500, "height": 428}}}}], "animations": {"animation": {"slots": {"tx": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff"}]}, "900": {"attachment": [{"name": null}, {"time": 0.0333, "name": "900"}, {"time": 0.0667, "name": "901"}, {"time": 0.1, "name": "902"}, {"time": 0.1333, "name": "903"}, {"time": 0.1667, "name": "904"}, {"time": 0.2, "name": "905"}, {"time": 0.2333, "name": "906"}, {"time": 0.2667, "name": "907"}, {"time": 0.3, "name": "908"}, {"time": 0.3333, "name": "909"}, {"time": 0.3667, "name": "910"}, {"time": 0.4, "name": "911"}, {"time": 0.4333, "name": "912"}, {"time": 0.4667, "name": "913"}, {"time": 0.5, "name": "914"}, {"time": 0.5333, "name": "915"}, {"time": 0.5667, "name": null}, {"time": 2.0333, "name": "900"}, {"time": 2.0667, "name": "901"}, {"time": 2.1, "name": "902"}, {"time": 2.1333, "name": "903"}, {"time": 2.1667, "name": "904"}, {"time": 2.2, "name": "905"}, {"time": 2.2333, "name": "906"}, {"time": 2.2667, "name": "907"}, {"time": 2.3, "name": "908"}, {"time": 2.3333, "name": "909"}, {"time": 2.3667, "name": "910"}, {"time": 2.4, "name": "911"}, {"time": 2.4333, "name": "912"}, {"time": 2.4667, "name": "913"}, {"time": 2.5, "name": "914"}, {"time": 2.5333, "name": "915"}, {"time": 2.5667, "name": null}]}, "901": {"attachment": [{"name": null}, {"time": 1.4667, "name": "900"}, {"time": 1.5, "name": "901"}, {"time": 1.5333, "name": "902"}, {"time": 1.5667, "name": "903"}, {"time": 1.6, "name": "904"}, {"time": 1.6333, "name": "905"}, {"time": 1.6667, "name": "906"}, {"time": 1.7, "name": "907"}, {"time": 1.7333, "name": "908"}, {"time": 1.7667, "name": "909"}, {"time": 1.8, "name": "910"}, {"time": 1.8333, "name": "911"}, {"time": 1.8667, "name": "912"}, {"time": 1.9, "name": "913"}, {"time": 1.9333, "name": "914"}, {"time": 1.9667, "name": "915"}, {"time": 2, "name": null}, {"time": 3.4667, "name": "900"}, {"time": 3.5, "name": "901"}, {"time": 3.5333, "name": "902"}, {"time": 3.5667, "name": "903"}, {"time": 3.6, "name": "904"}, {"time": 3.6333, "name": "905"}, {"time": 3.6667, "name": "906"}, {"time": 3.7, "name": "907"}, {"time": 3.7333, "name": "908"}, {"time": 3.7667, "name": "909"}, {"time": 3.8, "name": "910"}, {"time": 3.8333, "name": "911"}, {"time": 3.8667, "name": "912"}, {"time": 3.9, "name": "913"}, {"time": 3.9333, "name": "914"}, {"time": 3.9667, "name": "915"}, {"time": 4, "name": null}]}, "902": {"attachment": [{"name": null}, {"time": 0.7, "name": "900"}, {"time": 0.7333, "name": "901"}, {"time": 0.7667, "name": "902"}, {"time": 0.8, "name": "903"}, {"time": 0.8333, "name": "904"}, {"time": 0.8667, "name": "905"}, {"time": 0.9, "name": "906"}, {"time": 0.9333, "name": "907"}, {"time": 0.9667, "name": "908"}, {"time": 1, "name": "909"}, {"time": 1.0333, "name": "910"}, {"time": 1.0667, "name": "911"}, {"time": 1.1, "name": "912"}, {"time": 1.1333, "name": "913"}, {"time": 1.1667, "name": "914"}, {"time": 1.2, "name": "915"}, {"time": 1.2333, "name": null}, {"time": 2.7, "name": "900"}, {"time": 2.7333, "name": "901"}, {"time": 2.7667, "name": "902"}, {"time": 2.8, "name": "903"}, {"time": 2.8333, "name": "904"}, {"time": 2.8667, "name": "905"}, {"time": 2.9, "name": "906"}, {"time": 2.9333, "name": "907"}, {"time": 2.9667, "name": "908"}, {"time": 3, "name": "909"}, {"time": 3.0333, "name": "910"}, {"time": 3.0667, "name": "911"}, {"time": 3.1, "name": "912"}, {"time": 3.1333, "name": "913"}, {"time": 3.1667, "name": "914"}, {"time": 3.2, "name": "915"}, {"time": 3.2333, "name": null}]}}, "bones": {"tx": {"scale": [{"x": 2, "y": 2}, {"time": 0.3333}, {"time": 0.4, "x": 1.099, "y": 1.099}, {"time": 0.5, "curve": "stepped"}, {"time": 2}, {"time": 2.0667, "x": 1.075, "y": 1.075}, {"time": 2.1667}, {"time": 2.2333, "x": 1.075, "y": 1.075}, {"time": 2.3333}]}, "dg2": {"scale": [{"time": 0.4333}, {"time": 0.5, "x": 1.058, "y": 1.058}, {"time": 0.6}, {"time": 0.6667, "x": 1.058, "y": 1.058}, {"time": 0.7667, "curve": "stepped"}, {"time": 2.6}, {"time": 2.6667, "x": 1.058, "y": 1.058}, {"time": 2.7667}, {"time": 2.8333, "x": 1.058, "y": 1.058}, {"time": 2.9333}]}, "xx1": {"rotate": [{}, {"time": 1, "angle": 180}, {"time": 2}, {"time": 3, "angle": 180}, {"time": 4}], "translate": [{"y": 9.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 34.85, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "y": 9.89, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "y": 34.85, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "y": 9.89}]}, "xx3": {"rotate": [{}, {"time": 1, "angle": 180}, {"time": 2}, {"time": 3, "angle": 180}, {"time": 4}], "translate": [{"y": -23.99, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "y": -26.55, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "y": -23.99, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "y": -26.55, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "y": -23.99}]}, "xx2": {"rotate": [{}, {"time": 1, "angle": 180}, {"time": 2}, {"time": 3, "angle": 180}, {"time": 4}], "translate": [{"x": 5.77, "y": 31.17, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 8.06, "y": 43.52, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 5.77, "y": 31.17, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 8.06, "y": 43.52, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "x": 5.77, "y": 31.17}]}, "cha3": {"rotate": [{"angle": 10.23, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 2.51, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 10.23, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 2.51, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": 10.23}]}, "cha": {"translate": [{"y": 0.94, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "y": 9.76, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "y": 0.94, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "y": 9.76, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "y": 0.94}]}}, "deform": {"default": {"cha": {"cha": [{"offset": 60, "vertices": [0.23689, 0.23646, 0.25772, -0.23196, 0.46631, -0.37949, 0.51664, -0.30749, 0.48152, 0.01267, 0.49659, 0.40852, 0.90897, 0.23519, 0.92477, -0.19647, 0.94354, -0.05961, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.70974, 0.21671, 0.70498, -0.17411, 0.72284, -0.06952], "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "offset": 60, "vertices": [2.45598, 2.45157, 2.67195, -2.40487, 4.83452, -3.93443, 5.35638, -3.1879, 4.99219, 0.13134, 5.14845, 4.23536, 9.42391, 2.43839, 9.58766, -2.03695, 9.78227, -0.61805, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.35835, 2.2468, 7.30902, -1.80513, 7.49417, -0.72076], "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 1.9, "offset": 60, "vertices": [0.04732, 0.04724, 0.05149, -0.04634, 0.09316, -0.07581, 0.10321, -0.06143, 0.09619, 0.00253, 0.0992, 0.08161, 0.18159, 0.04698, 0.18474, -0.03925, 0.18849, -0.01191, 0, 0, 0, 0, 0, 0, 0, 0, -1.13997, -0.43442, -2.35991, -0.43626, -3.06194, -0.70839, -3.24827, -0.03097, -2.26712, 0.27243, 0, 0, 0, 0, 1.5351, 0.71665, 1.41446, 0.93277, 2.61539, 0.7073, 3.08658, 0.46902, 2.11046, 0.24786, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.11781, -0.09748, -2.08099, -0.40502, -3.31598, 0.37903, -3.33576, -0.1082, -3.65203, 0.26669, -3.65179, -0.26827, -2.02186, 0.56648, -2.0827, 0.26586, 0, 0, 0, 0, 1.138, 0.0817, 1.11412, 0.24671, 2.24128, 0.56235, 2.13562, 0.88304, 2.16007, 1.13976, 1.97119, 1.44243, 1.75233, 0.24426, 1.69812, 0.49705, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.14179, 0.04329, 0.14084, -0.03478, 0.1444, -0.01389], "curve": 0.311, "c2": 0.2, "c3": 0.646, "c4": 0.55}, {"time": 2, "offset": 60, "vertices": [0.23689, 0.23646, 0.25772, -0.23196, 0.46631, -0.37949, 0.51664, -0.30749, 0.48152, 0.01267, 0.49659, 0.40852, 0.90897, 0.23519, 0.92477, -0.19647, 0.94354, -0.05961, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.70974, 0.21671, 0.70498, -0.17411, 0.72284, -0.06952], "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "offset": 60, "vertices": [2.45598, 2.45157, 2.67195, -2.40487, 4.83452, -3.93443, 5.35638, -3.1879, 4.99219, 0.13134, 5.14845, 4.23536, 9.42391, 2.43839, 9.58766, -2.03695, 9.78227, -0.61805, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.35835, 2.2468, 7.30902, -1.80513, 7.49417, -0.72076], "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 3.9, "offset": 60, "vertices": [0.04732, 0.04724, 0.05149, -0.04634, 0.09316, -0.07581, 0.10321, -0.06143, 0.09619, 0.00253, 0.0992, 0.08161, 0.18159, 0.04698, 0.18474, -0.03925, 0.18849, -0.01191, 0, 0, 0, 0, 0, 0, 0, 0, -1.13997, -0.43442, -2.35991, -0.43626, -3.06194, -0.70839, -3.24827, -0.03097, -2.26712, 0.27243, 0, 0, 0, 0, 1.5351, 0.71665, 1.41446, 0.93277, 2.61539, 0.7073, 3.08658, 0.46902, 2.11046, 0.24786, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.11781, -0.09748, -2.08099, -0.40502, -3.31598, 0.37903, -3.33576, -0.1082, -3.65203, 0.26669, -3.65179, -0.26827, -2.02186, 0.56648, -2.0827, 0.26586, 0, 0, 0, 0, 1.138, 0.0817, 1.11412, 0.24671, 2.24128, 0.56235, 2.13562, 0.88304, 2.16007, 1.13976, 1.97119, 1.44243, 1.75233, 0.24426, 1.69812, 0.49705, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.14179, 0.04329, 0.14084, -0.03478, 0.1444, -0.01389], "curve": 0.311, "c2": 0.2, "c3": 0.646, "c4": 0.55}, {"time": 4, "offset": 60, "vertices": [0.23689, 0.23646, 0.25772, -0.23196, 0.46631, -0.37949, 0.51664, -0.30749, 0.48152, 0.01267, 0.49659, 0.40852, 0.90897, 0.23519, 0.92477, -0.19647, 0.94354, -0.05961, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.70974, 0.21671, 0.70498, -0.17411, 0.72284, -0.06952]}]}}}}}}