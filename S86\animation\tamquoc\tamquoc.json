{"skeleton": {"hash": "4HKjanihbcMglWvBd02TdOaSSdg", "spine": "3.8.75", "x": -155.03, "y": -235.19, "width": 317.04, "height": 467.2}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 187, "rotation": -1.73, "x": -17.18, "y": -106.5, "scaleX": 0.96, "scaleY": 0.96}, {"name": "CHA", "parent": "bone", "length": 196.48, "rotation": 84.74, "x": -7.47, "y": 26.24}, {"name": "CHA3", "parent": "CHA", "x": 145.35, "y": -112.92}, {"name": "tay2", "parent": "CHA3", "x": -16.21, "y": -5.19}, {"name": "tay4", "parent": "tay2", "length": 102.71, "rotation": 169.39, "x": -12.04, "y": 0.13}, {"name": "gay", "parent": "tay4", "length": 61.45, "rotation": -178.96, "x": -74.66, "y": -9.48}, {"name": "CHA4", "parent": "CHA", "length": 65.28, "rotation": 7.68, "x": 214.84, "y": 0.65}, {"name": "mu1", "parent": "CHA4", "x": 62.75, "y": -18.41}, {"name": "mu3", "parent": "mu1", "length": 14.54, "rotation": -125.16, "x": -0.76, "y": -4.69}, {"name": "mu2", "parent": "mu3", "length": 10.3, "rotation": -10.54, "x": 16.04, "y": -0.11}, {"name": "mu5", "parent": "mu2", "length": 11.01, "rotation": 56.07, "x": 10.8, "y": 1.16}, {"name": "mu4", "parent": "mu5", "length": 8.81, "rotation": 32.85, "x": 12.9, "y": 2.27}, {"name": "mu7", "parent": "mu4", "length": 5.69, "rotation": 7.79, "x": 11.47, "y": -0.28}, {"name": "mu6", "parent": "CHA4", "x": 59.19, "y": -20.95}, {"name": "mu9", "parent": "mu6", "length": 10.44, "rotation": -144.86, "x": -1.67, "y": -2.33}, {"name": "mu8", "parent": "mu9", "length": 8.42, "rotation": -12.85, "x": 11.72, "y": -0.64}, {"name": "mu11", "parent": "mu8", "length": 7.24, "rotation": 31.26, "x": 10.31, "y": 0.22}, {"name": "mu10", "parent": "mu11", "length": 8.21, "rotation": 59.38, "x": 8.49, "y": 1.48}, {"name": "vai1", "parent": "CHA", "x": 223.19, "y": -67.06}, {"name": "tay", "parent": "CHA", "length": 77.76, "rotation": 21.81, "x": 62.18, "y": 87.9}, {"name": "bone2", "parent": "root", "y": -80.38}, {"name": "bone3", "parent": "root", "y": 14.23}, {"name": "LOGO", "parent": "root", "x": 2.79, "y": -160.76}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "mu1", "bone": "mu7", "attachment": "mu1"}, {"name": "mu2", "bone": "mu10", "attachment": "mu2"}, {"name": "gay", "bone": "gay", "attachment": "gay"}, {"name": "CHA", "bone": "CHA4", "attachment": "CHA"}, {"name": "tay2", "bone": "tay4", "attachment": "tay2"}, {"name": "tay", "bone": "tay", "attachment": "tay"}, {"name": "vai1", "bone": "vai1", "attachment": "vai1"}, {"name": "LOGO", "bone": "LOGO", "attachment": "LOGO"}, {"name": "LOGO2", "bone": "LOGO", "attachment": "LOGO", "blend": "additive"}, {"name": "root", "bone": "root", "attachment": "root"}, {"name": "fire2", "bone": "bone3", "attachment": "fire", "blend": "additive"}, {"name": "fire", "bone": "bone2", "attachment": "fire", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"bg": {"bg": {"x": -0.63, "y": -1.59, "scaleX": 0.8, "scaleY": 0.8, "width": 386, "height": 584}}, "tay2": {"tay2": {"type": "mesh", "uvs": [0.26188, 0.12285, 0.15408, 0.32681, 0.13705, 0.61028, 0, 0.78658, 0, 1, 0.66473, 1, 0.66473, 0.71053, 0.85197, 0.5204, 1, 0.34064, 1, 0.03643, 0.79523, 0, 0.34131, 0, 0.51721, 0.33027, 0.29025, 0.70707], "triangles": [10, 9, 8, 12, 11, 10, 4, 13, 5, 13, 6, 5, 13, 3, 2, 13, 4, 3, 13, 12, 6, 6, 12, 7, 2, 1, 13, 13, 1, 12, 7, 12, 8, 12, 10, 8, 1, 0, 12, 0, 11, 12], "vertices": [2, 5, 6.18, -31.64, 0.71613, 4, -12.3, 32.36, 0.28387, 2, 5, 38.54, -31.78, 0.99363, 4, -44.07, 38.46, 0.00637, 1, 5, 79.81, -20.34, 1, 1, 5, 109, -24.31, 1, 1, 5, 139.72, -14.57, 1, 1, 5, 121.24, 43.72, 1, 1, 5, 79.57, 30.51, 1, 1, 5, 47, 38.26, 1, 2, 5, 17.01, 43.03, 0.95394, 4, -36.69, -39.04, 0.04606, 2, 5, -26.78, 29.15, 0.31035, 4, 8.91, -33.45, 0.68965, 2, 5, -26.33, 9.53, 0.1012, 4, 12.08, -14.09, 0.8988, 2, 5, -13.71, -30.28, 0.33779, 4, 7, 27.36, 0.66221, 1, 5, 28.94, 0.22, 1, 1, 5, 89.49, -2.49, 1], "hull": 12}}, "mu1": {"mu1": {"type": "mesh", "uvs": [0.01904, 0.25695, 0.1896, 0.4454, 0.33125, 0.7634, 0.45267, 0.95773, 0.7302, 0.99306, 0.89787, 0.95184, 0.8574, 0.68095, 0.84005, 0.5514, 0.95858, 0.39829, 1, 0.04496, 0.95858, 0, 0.80536, 0.2864, 0.5712, 0.59851, 0.50471, 0.52784, 0.42954, 0.28051, 0.29945, 0.0744, 0.16647, 0, 0.04216, 0, 0, 0.02729, 0.19827, 0.23929, 0.40064, 0.61618, 0.53362, 0.79873, 0.74754, 0.7104, 0.8256, 0.37473], "triangles": [8, 23, 9, 11, 10, 9, 6, 22, 7, 7, 22, 23, 23, 12, 11, 7, 23, 8, 23, 11, 9, 3, 21, 4, 4, 22, 5, 4, 21, 22, 22, 6, 5, 21, 20, 12, 21, 12, 22, 23, 22, 12, 3, 2, 21, 2, 20, 21, 12, 20, 13, 2, 1, 20, 13, 20, 14, 0, 18, 19, 18, 17, 19, 14, 20, 19, 20, 1, 19, 1, 0, 19, 19, 15, 14, 17, 16, 19, 19, 16, 15], "vertices": [2, 9, -3.2, -5.14, 0.18695, 8, -3.12, 0.88, 0.81305, 3, 9, 7.42, -4.02, 0.99964, 8, -8.32, -8.44, 9e-05, 10, -7.76, -5.42, 0.00027, 2, 9, 18.7, -6.69, 0.0602, 10, 3.82, -5.99, 0.9398, 2, 10, 12.25, -4.97, 0.97119, 11, -4.28, -4.63, 0.02881, 2, 11, 10.51, -8.5, 0.98948, 12, -7.84, -7.75, 0.01052, 2, 11, 19.78, -9.18, 0.82954, 12, -0.42, -13.35, 0.17046, 2, 11, 19, -1.57, 0.45757, 12, 3.05, -6.53, 0.54243, 3, 11, 18.73, 2.05, 0.08226, 12, 4.78, -3.35, 0.90886, 13, -7.04, -2.14, 0.00888, 2, 12, 12.35, -4.9, 0.08516, 13, 0.24, -4.69, 0.91484, 1, 13, 9.14, -0.57, 1, 1, 13, 8.68, 1.97, 1, 2, 12, 8.37, 3.12, 0.91296, 13, -2.61, 3.8, 0.08704, 3, 10, 10, 6.49, 0.05192, 11, 3.98, 3.64, 0.91747, 12, -6.75, 5.99, 0.03061, 3, 9, 22.96, 3.95, 0.00338, 10, 6.07, 5.26, 0.63226, 11, 0.75, 6.21, 0.36437, 3, 9, 15.78, 7.11, 0.53753, 10, -1.58, 7.06, 0.45817, 11, -2.02, 13.56, 0.00429, 2, 9, 6.73, 7.65, 0.99709, 10, -10.57, 5.93, 0.00291, 2, 9, -0.44, 5.17, 0.96545, 8, 3.72, -7.31, 0.03455, 2, 9, -6.08, 1.3, 0.14084, 8, 3.8, -0.47, 0.85916, 2, 9, -7.57, -0.62, 0.00094, 8, 3.09, 1.85, 0.99906, 1, 9, 4.66, 0.83, 1, 2, 9, 19.59, -1.26, 0.00069, 10, 3.71, -0.48, 0.99931, 2, 10, 12.37, 1.21, 0.16281, 11, 0.91, -1.27, 0.83719, 2, 11, 12.92, -1.19, 0.75951, 12, -1.86, -2.92, 0.24049, 1, 12, 7.52, 0.63, 1], "hull": 19}}, "mu2": {"mu2": {"type": "mesh", "uvs": [0, 0.06011, 0.12369, 0.31066, 0.23853, 0.40702, 0.30036, 0.63348, 0.37544, 0.81657, 0.50353, 0.9322, 0.69786, 0.97556, 0.92753, 0.92256, 1, 0.72984, 0.91255, 0.71535, 0.68019, 0.67684, 0.56094, 0.77802, 0.49028, 0.48893, 0.28711, 0.10829, 0.08836, 0, 0, 0, 0.17228, 0.18057, 0.34011, 0.47448, 0.42844, 0.72984, 0.56978, 0.85993], "triangles": [19, 10, 9, 7, 9, 8, 6, 19, 9, 7, 6, 9, 11, 18, 12, 4, 3, 18, 19, 11, 10, 18, 11, 19, 19, 4, 18, 5, 4, 19, 5, 19, 6, 13, 12, 17, 3, 2, 17, 17, 18, 3, 12, 18, 17, 16, 14, 13, 1, 0, 16, 17, 2, 16, 1, 16, 2, 17, 16, 13, 16, 0, 15, 16, 15, 14], "vertices": [1, 14, 1.38, 2.46, 1, 2, 14, -6.94, -1.89, 2e-05, 15, 4.06, -3.39, 0.99998, 2, 15, 9.06, -1.9, 0.85243, 16, -2.31, -1.83, 0.14757, 1, 16, 5.44, -2.7, 1, 2, 16, 12.05, -2.57, 0.03719, 17, 0.05, -3.29, 0.96281, 1, 17, 6.02, -3.69, 1, 2, 17, 12.53, -0.77, 0.09637, 18, 0.12, -4.62, 0.90363, 1, 18, 8.4, -6.34, 1, 1, 18, 13.34, -1.55, 1, 1, 18, 10.65, 0.15, 1, 2, 17, 6.26, 6.86, 0.01693, 18, 3.49, 4.66, 0.98307, 3, 16, 13.49, 4.08, 0.01073, 17, 4.72, 1.64, 0.84326, 18, -1.78, 3.32, 0.146, 3, 15, 16.55, 3.86, 0.07708, 16, 3.72, 5.46, 0.9181, 17, -2.91, 7.9, 0.00482, 2, 14, -0.34, -7.86, 0.00045, 15, 2.09, 5.29, 0.99955, 2, 14, 3.32, -0.75, 0.92536, 15, -5, 1.58, 0.07464, 1, 14, 3.36, 2.43, 1, 1, 15, 1.6, 0.54, 1, 2, 15, 13, -0.24, 0.05249, 16, 1.17, 0.67, 0.94751, 2, 16, 10.17, 0.31, 0.04672, 17, -0.08, 0.15, 0.95328, 1, 17, 6.56, -0.37, 1], "hull": 16}}, "fire2": {"fire": {"width": 350, "height": 350}}, "vai1": {"vai1": {"type": "mesh", "uvs": [0.00101, 0.78038, 0.15008, 0.40284, 0.17228, 0.12996, 0.28012, 0.22341, 0.41794, 0.1225, 0.48945, 0.07015, 0.54654, 0, 0.72733, 0.08136, 0.83516, 0.22715, 0.9208, 0.20846, 0.95569, 0.44769, 0.94617, 0.57853, 1, 0.69067, 0.98741, 0.94112, 0.9208, 1, 0.69244, 1, 0.66706, 0.88505, 0.5624, 0.98224, 0.315, 0.98598, 0.08981, 0.92991, 0, 0.84393, 0.71509, 0.32555, 0.81065, 0.66853, 0.53701, 0.46632, 0.62388, 0.77091], "triangles": [6, 21, 5, 21, 7, 8, 7, 21, 6, 1, 2, 3, 8, 9, 10, 21, 8, 10, 21, 4, 5, 23, 4, 21, 3, 4, 23, 1, 3, 23, 10, 22, 21, 11, 22, 10, 23, 21, 22, 22, 11, 12, 24, 23, 22, 16, 24, 22, 19, 0, 18, 1, 18, 0, 1, 23, 18, 20, 0, 19, 13, 22, 12, 14, 16, 22, 18, 23, 24, 17, 24, 16, 17, 18, 24, 14, 15, 16, 22, 13, 14], "vertices": [-31.27, 42.88, 2, 32.09, 25.02, 32.69, 18.53, 21.14, 28.6, 8.63, 33.82, 2.14, 40.36, -2.76, 35.75, -21.35, 24.89, -33.44, 27.48, -41.66, 7.95, -47.53, -3.07, -47.93, -11.77, -54.37, -32.8, -55.69, -38.51, -49.74, -41.26, -27.3, -31.98, -23.64, -41.35, -14.34, -44.64, 9.93, -42.67, 32.63, -36.58, 42.33, 15.24, -22.64, -12.2, -35.54, 1.36, -6.58, -22.99, -18.23], "hull": 21}}, "gay": {"gay": {"x": 58.71, "y": 1.44, "rotation": -73.44, "width": 46, "height": 141}}, "root": {"root": {"type": "clipping", "end": "fire", "vertexCount": 12, "vertices": [-129.33, 186.7, -128.51, -193.26, -116.12, -206.47, -98.77, -208.12, 112.68, -208.95, 122.6, -199.04, 129.2, -183.34, 128.38, 174.31, 122.6, 192.49, 107.73, 202.4, 72.21, 203.22, -109.51, 203.22]}}, "LOGO2": {"LOGO": {"x": 5.72, "y": 4.36, "width": 307, "height": 151}}, "LOGO": {"LOGO": {"x": 5.72, "y": 4.36, "width": 307, "height": 151}}, "tay": {"tay": {"type": "mesh", "uvs": [0, 0.69772, 0.06692, 0.76596, 0.14785, 0.92631, 0.24497, 0.97066, 0.4311, 0.97066, 0.57273, 1, 0.9612, 0.95019, 1, 0.86149, 1, 0.19278, 0.75483, 0, 0.63217, 0, 0.40152, 0, 0.23687, 0, 0, 0.10408, 0, 0.48619, 0.5391, 0.23685, 0.64836, 0.62579, 0.68883, 0.81002, 0.76976, 0.22661, 0.87901, 0.61897, 0.91948, 0.79979], "triangles": [18, 9, 8, 10, 9, 18, 15, 11, 10, 15, 10, 18, 14, 13, 12, 19, 18, 8, 16, 15, 18, 16, 18, 19, 14, 1, 0, 12, 11, 15, 15, 14, 12, 14, 16, 1, 16, 14, 15, 7, 20, 19, 17, 16, 19, 17, 19, 20, 3, 2, 1, 7, 19, 8, 1, 16, 3, 6, 20, 7, 16, 4, 3, 17, 4, 16, 5, 4, 17, 6, 5, 17, 6, 17, 20], "vertices": [22.29, 46.94, 14.09, 43.16, -3.51, 40.62, -10.02, 33.7, -14.11, 18.23, -20.12, 7.22, -23.76, -26.38, -15.87, -31.92, 50.07, -49.37, 74.47, -34.02, 77.17, -23.82, 82.25, -4.65, 85.87, 9.04, 80.82, 31.45, 43.14, 41.42, 55.87, -9.9, 15.11, -8.84, -3.95, -7.39, 51.8, -29.35, 10.71, -28.19, -8.01, -26.83], "hull": 15}}, "fire": {"fire": {"width": 350, "height": 350}}, "CHA": {"CHA": {"type": "mesh", "uvs": [0, 0.96814, 0.09863, 0.74773, 0.15734, 0.73477, 0.16321, 0.67735, 0.17495, 0.6403, 0.13581, 0.57733, 0.12603, 0.44767, 0.19256, 0.32728, 0.33345, 0.26431, 0.3096, 0.21653, 0.3644, 0.18319, 0.33896, 0.11836, 0.35657, 0.05168, 0.39179, 0, 0.4818, 0, 0.54051, 0.00908, 0.56986, 0.14614, 0.60313, 0.16466, 0.60508, 0.20912, 0.6814, 0.21467, 0.81838, 0.2832, 0.96318, 0.42027, 1, 0.46287, 1, 0.51658, 0.9123, 0.59993, 0.89235, 0.57009, 0.7315, 0.62483, 0.71433, 0.60259, 0.67728, 0.61713, 0.70167, 0.6522, 0.68812, 0.69668, 0.66372, 0.71892, 0.65649, 0.7805, 0.68032, 0.84885, 0.66315, 0.87109, 0.749, 1, 0.42074, 1, 0.25588, 1, 0.1118, 1, 0.04764, 1, 0.26974, 0.42068, 0.58699, 0.39839, 0.43044, 0.32233, 0.40007, 0.41152, 0.35979, 0.54526, 0.29329, 0.68426, 0.26419, 0.84424, 0.51079, 0.55706, 0.45538, 0.70261, 0.42628, 0.8403, 0.22817, 0.54001, 0.20185, 0.68163, 0.15891, 0.84686, 0.42108, 0.23189, 0.43215, 0.222, 0.4512, 0.21735, 0.46902, 0.21502, 0.49361, 0.22258, 0.48009, 0.23422, 0.44075, 0.24062], "triangles": [14, 10, 12, 11, 12, 10, 13, 14, 12, 16, 56, 14, 16, 14, 15, 14, 55, 10, 56, 55, 14, 54, 10, 55, 57, 56, 16, 53, 10, 54, 58, 56, 57, 59, 54, 55, 53, 54, 59, 58, 59, 55, 58, 55, 56, 8, 9, 10, 8, 10, 53, 42, 53, 59, 42, 59, 58, 18, 42, 58, 58, 57, 18, 16, 18, 57, 17, 18, 16, 8, 53, 42, 41, 42, 18, 43, 8, 42, 43, 42, 41, 40, 7, 8, 40, 8, 43, 6, 7, 40, 21, 22, 23, 50, 6, 40, 44, 40, 43, 50, 40, 44, 47, 43, 41, 44, 43, 47, 25, 20, 21, 25, 21, 23, 5, 6, 50, 24, 25, 23, 20, 41, 19, 27, 20, 25, 27, 41, 20, 19, 41, 18, 28, 41, 27, 47, 41, 28, 26, 27, 25, 4, 5, 50, 51, 4, 50, 3, 4, 51, 45, 50, 44, 51, 50, 45, 30, 28, 29, 48, 44, 47, 45, 44, 48, 31, 47, 28, 31, 28, 30, 48, 47, 31, 2, 3, 51, 32, 48, 31, 49, 45, 48, 49, 48, 32, 46, 51, 45, 46, 45, 49, 52, 2, 51, 46, 52, 51, 1, 2, 52, 34, 32, 33, 49, 32, 34, 0, 1, 52, 39, 0, 52, 38, 39, 52, 37, 52, 46, 38, 52, 37, 36, 46, 49, 36, 49, 34, 37, 46, 36, 36, 34, 35], "vertices": [1, 2, -31.1, 107.8, 1, 1, 2, 42.08, 86.85, 1, 1, 2, 48.32, 69.81, 1, 1, 2, 66.66, 70.28, 1, 1, 2, 78.78, 68.2, 1, 1, 2, 97.23, 82.33, 1, 1, 2, 137.8, 90.26, 1, 2, 2, 178.23, 75.04, 0.99223, 7, -26.34, 78.62, 0.00777, 2, 2, 203.26, 35.38, 0.58557, 7, -6.83, 35.97, 0.41443, 2, 2, 217.47, 44.35, 0.28585, 7, 8.45, 42.96, 0.71415, 2, 2, 230, 29.27, 0.07682, 7, 18.85, 26.34, 0.92318, 1, 7, 39.55, 33.75, 1, 1, 7, 60.69, 28.19, 1, 1, 7, 77, 17.38, 1, 1, 7, 76.67, -9.71, 1, 1, 7, 73.56, -27.34, 1, 2, 2, 249.21, -30.69, 0.05824, 7, 29.87, -35.64, 0.94176, 2, 2, 244.58, -41.34, 0.13766, 7, 23.86, -45.58, 0.86234, 2, 2, 230.62, -43.64, 0.33695, 7, 9.72, -46, 0.66305, 2, 2, 231.66, -66.66, 0.63011, 7, 7.67, -68.95, 0.36989, 2, 2, 215.04, -110.23, 0.86503, 7, -14.62, -109.91, 0.13497, 2, 2, 177.07, -158.79, 0.9843, 7, -58.74, -152.96, 0.0157, 2, 2, 164.97, -171.44, 0.99343, 7, -72.42, -163.88, 0.00657, 2, 2, 148.02, -173.52, 0.99704, 7, -89.5, -163.67, 0.00296, 1, 2, 118.5, -150.54, 1, 2, 2, 127.19, -143.42, 0.99882, 7, -106.12, -131.06, 0.00118, 1, 2, 104.03, -97.48, 1, 2, 2, 110.42, -91.49, 0.99951, 7, -115.8, -77.35, 0.00049, 2, 2, 104.47, -80.98, 0.99991, 7, -120.29, -66.15, 9e-05, 1, 2, 94.3, -89.63, 1, 1, 2, 79.76, -87.3, 1, 1, 2, 71.85, -80.87, 1, 1, 2, 52.15, -81.09, 1, 1, 2, 31.44, -90.85, 1, 1, 2, 23.8, -86.58, 1, 1, 2, -13.75, -117.21, 1, 1, 2, -25.76, -19.14, 1, 1, 2, -31.79, 30.12, 1, 1, 2, -37.06, 73.16, 1, 1, 2, -39.41, 92.33, 1, 2, 2, 155.09, 47.66, 0.99783, 7, -52.93, 54.57, 0.00217, 2, 2, 173.74, -46.27, 0.91959, 7, -47.01, -41, 0.08041, 1, 2, 192.02, 3.44, 1, 1, 2, 162.75, 9.07, 1, 1, 2, 119.07, 15.94, 1, 1, 2, 72.76, 30.43, 1, 1, 2, 17.68, 33.66, 1, 1, 2, 120.87, -29.63, 1, 1, 2, 72.9, -18.7, 1, 1, 2, 24.85, -14.62, 1, 1, 2, 115.91, 55.46, 1, 1, 2, 70.24, 57.85, 1, 1, 2, 13, 65.01, 1, 2, 2, 216.7, 10.45, 0.09395, 7, 3.15, 9.47, 0.90605, 2, 2, 220.23, 7.53, 0.01721, 7, 6.26, 6.1, 0.98279, 1, 7, 7.67, 0.35, 1, 1, 7, 8.34, -5.03, 1, 2, 2, 222.29, -10.86, 0.06178, 7, 5.85, -12.4, 0.93822, 2, 2, 218.12, -7.27, 0.06655, 7, 2.2, -8.28, 0.93345, 2, 2, 214.67, 4.24, 0.05008, 7, 0.3, 3.58, 0.94992], "hull": 40}}}}], "animations": {"animation": {"slots": {"fire": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3, "color": "ffffffff"}, {"time": 2.6333, "color": "ffffff00"}]}, "LOGO2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.5, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}]}, "fire2": {"color": [{"time": 1, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3667, "color": "ffffff00"}, {"time": 1.7, "color": "ffffffff"}]}}, "bones": {"bone3": {"translate": [{"x": -12.72, "y": 47.73}, {"time": 1.3333, "x": 248.04, "y": 96.69}, {"time": 1.3667, "x": -266.95}, {"time": 2.6667, "x": -12.72, "y": 47.73}]}, "LOGO": {"scale": [{"time": 1.3333}, {"time": 1.4, "x": 1.125, "y": 1.125}, {"time": 1.5}, {"time": 1.5667, "x": 1.125, "y": 1.125}, {"time": 1.6667}]}, "bone2": {"translate": [{"x": -266.95}, {"time": 2.6333, "x": 248.04, "y": 96.69}, {"time": 2.6667, "x": -266.95}]}, "vai1": {"rotate": [{"angle": 0.86, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -4.59, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 0.86}]}, "CHA4": {"rotate": [{"angle": -2.39, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.8333, "angle": 6.1, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": -6.69, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": -2.39}]}, "bone": {"rotate": [{"angle": 0.46, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 1.88, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2.6667, "angle": 0.46}], "translate": [{"y": -1.61, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "y": -16.74, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2.6667, "y": -1.61}]}, "mu3": {"rotate": [{"angle": 1.54, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 24.48, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 1.3333, "angle": 1.54, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "angle": 24.48, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": 1.54}]}, "mu2": {"rotate": [{"angle": 12.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 24.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 12.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 24.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 12.24}]}, "mu5": {"rotate": [{"angle": 16.26, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 24.48, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 1.3333, "angle": 16.26, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": 24.48, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 2.6667, "angle": 16.26}]}, "tay": {"rotate": [{"angle": -8.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -16.05, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": -8.03}]}, "mu4": {"rotate": [{"angle": 8.22, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 24.48, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 1.3333, "angle": 8.22, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 24.48, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": 8.22}]}, "mu7": {"rotate": [{"angle": 4.52, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 24.48, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": 4.52, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 24.48, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": 4.52}]}, "mu9": {"rotate": [{"angle": -6.93, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1, "angle": -8.38, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 14.81, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 1.3333, "angle": -6.93, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.4333, "angle": -8.38, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "angle": 14.81, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "angle": -6.93}]}, "mu8": {"rotate": [{"angle": -4.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "angle": -8.38, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 14.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1.3333, "angle": -4.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 1.5, "angle": -8.38, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "angle": 14.81, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "angle": -4.1}]}, "mu10": {"rotate": [{"angle": 3.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": -8.38, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 14.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.3333, "angle": 3.21, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "angle": -8.38, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "angle": 14.81, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 3.21}]}, "mu11": {"rotate": [{"angle": -0.59, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.2667, "angle": -8.38, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 14.81, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 1.3333, "angle": -0.59, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.6, "angle": -8.38, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "angle": 14.81, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.6667, "angle": -0.59}]}, "tay4": {"rotate": [{"angle": 6.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 12.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "angle": 6.49}]}}, "deform": {"default": {"CHA": {"CHA": [{"offset": 110, "vertices": [1.73675, 0.61193, 1.81391, 0.31729, 1.964, -0.52667, 1.8504, -0.84319, 0.83133, 0.13742, 1.82953, 0.14472, 1.3005, 0.22786, 0.73972, 0.4034, 0, 0, 1.32229, -0.32402, 0.74135, -0.19165, 0, 0, 1.26224, 0.7065, 0.70924, 0.73545], "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1, "offset": 110, "vertices": [9.41499, 3.31729, 9.83329, 1.72005, 10.64697, -2.85511, 10.03109, -4.57098, 4.5067, 0.74495, 9.91798, 0.78453, 7.05007, 1.23525, 4.01007, 2.18685, 0, 0, 7.1682, -1.75651, 4.01892, -1.03892, 0, 0, 6.84267, 3.82997, 3.8448, 3.98691], "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.6667, "offset": 110, "vertices": [1.73675, 0.61193, 1.81391, 0.31729, 1.964, -0.52667, 1.8504, -0.84319, 0.83133, 0.13742, 1.82953, 0.14472, 1.3005, 0.22786, 0.73972, 0.4034, 0, 0, 1.32229, -0.32402, 0.74135, -0.19165, 0, 0, 1.26224, 0.7065, 0.70924, 0.73545]}]}, "vai1": {"vai1": [{"offset": 8, "vertices": [-0.04064, 1.32734, -0.04062, 1.32735, -0.04063, 1.32734, -0.03003, 0.98108, -0.03004, 0.98108, -0.03004, 0.98108, -0.03003, 0.98108, -0.03002, 0.98108, -0.03003, 0.98108, -0.03003, 0.98107, -0.04063, 1.32735, -0.04063, 1.32735, -0.04063, 1.32734, 0, 0, 0, -1e-05, 0, -1e-05, 1e-05, 0, -0.04062, 1.32734, -0.04063, 1.32734, -0.04063, 1.32735, -0.04063, 1.32735], "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "vertices": [0.04292, -5.26781, 0, 0, 0, 0, 0, 0, -0.22029, 7.19562, -0.22027, 7.19562, -0.22028, 7.19561, -0.16279, 5.3185, -0.16281, 5.3185, -0.1628, 5.3185, -0.16279, 5.3185, -0.16278, 5.3185, -0.16279, 5.3185, -0.16279, 5.31849, -0.22028, 7.19562, -0.22028, 7.19562, -0.22028, 7.19561, 0, 0, 0, -1e-05, 0.04292, -5.26782, 0.04293, -5.26781, -0.22027, 7.19561, -0.22028, 7.19561, -0.22028, 7.19562, -0.22028, 7.19562], "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "offset": 8, "vertices": [-0.04064, 1.32734, -0.04062, 1.32735, -0.04063, 1.32734, -0.03003, 0.98108, -0.03004, 0.98108, -0.03004, 0.98108, -0.03003, 0.98108, -0.03002, 0.98108, -0.03003, 0.98108, -0.03003, 0.98107, -0.04063, 1.32735, -0.04063, 1.32735, -0.04063, 1.32734, 0, 0, 0, -1e-05, 0, -1e-05, 1e-05, 0, -0.04062, 1.32734, -0.04063, 1.32734, -0.04063, 1.32735, -0.04063, 1.32735]}]}, "tay": {"tay": [{"offset": 20, "vertices": [0.04466, 0.30886, 0.04466, 0.30886, 0, 1e-05, 0, 0, 0, 1e-05, 0.04466, 0.30886, 0.04466, 0.30886, 0.04466, 0.30886, 0.04465, 0.30885, 0.04466, 0.30886, 0.04466, 0.30886], "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "offset": 20, "vertices": [0.71149, 4.92081, 0.71149, 4.92081, 0, 1e-05, 0, 0, 0, 1e-05, 0.71149, 4.92081, 0.71149, 4.92081, 0.71149, 4.92081, 0.71149, 4.9208, 0.71149, 4.9208, 0.71149, 4.92081], "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 2.6667, "offset": 20, "vertices": [0.04466, 0.30886, 0.04466, 0.30886, 0, 1e-05, 0, 0, 0, 1e-05, 0.04466, 0.30886, 0.04466, 0.30886, 0.04466, 0.30886, 0.04465, 0.30885, 0.04466, 0.30886, 0.04466, 0.30886]}]}}}}}}