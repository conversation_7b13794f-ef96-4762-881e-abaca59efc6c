{"skeleton": {"hash": "I5orkbsRzqP3ej4XRiHUiVkrpZ4", "spine": "3.8.75", "x": -153.46, "y": -232.83, "width": 319.11, "height": 467.2}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 98.5, "rotation": -1.69, "x": 67.03, "y": 11.65, "scaleX": 0.94, "scaleY": 0.94}, {"name": "than", "parent": "bone", "x": -44.32, "y": 21.98}, {"name": "than3", "parent": "than", "length": 32.55, "rotation": -91.72, "x": 0.86, "y": -12.59}, {"name": "chan1", "parent": "than3", "x": 19.12, "y": -11.01}, {"name": "chan3", "parent": "chan1", "length": 41.24, "rotation": -45.4, "x": 6.01, "y": -3.04}, {"name": "chan2", "parent": "chan3", "length": 45.8, "rotation": 43.35, "x": 45.85, "y": 3.06}, {"name": "chan4", "parent": "than3", "x": 18.27, "y": 11.29}, {"name": "chan6", "parent": "chan4", "length": 44.96, "rotation": 46.23, "x": 3.98, "y": 6.55}, {"name": "chan5", "parent": "chan6", "length": 34.95, "rotation": -45.2, "x": 49.07, "y": 0.16}, {"name": "than4", "parent": "than", "length": 60.07, "rotation": 94.77, "x": -1.23, "y": 13.46}, {"name": "than2", "parent": "than4", "x": 21.73, "y": -141.91}, {"name": "than6", "parent": "than4", "x": 26.41, "y": 50.18}, {"name": "mat", "parent": "than4", "length": 48.44, "rotation": -1.49, "x": 36.57, "y": 8.81}, {"name": "mom", "parent": "mat", "length": 23.41, "rotation": -0.27, "x": -40.05, "y": 1.65}, {"name": "tay4", "parent": "than2", "x": 11.56, "y": 35.27}, {"name": "tay6", "parent": "tay4", "length": 35, "rotation": -126.6, "x": -5.87, "y": -10.31}, {"name": "tay5", "parent": "tay6", "length": 49.21, "rotation": -16.17, "x": 35.84, "y": 0.1}, {"name": "tay3", "parent": "tay5", "length": 70.52, "rotation": -62.74, "x": 54.93, "y": -0.88}, {"name": "tay8", "parent": "tay3", "length": 62.53, "rotation": -36.96, "x": 75.47, "y": -2.06}, {"name": "Layer 2", "parent": "than6", "x": 17.7, "y": -19.31}, {"name": "Layer 4", "parent": "Layer 2", "length": 46.39, "rotation": 140.89, "x": -6.86, "y": 6.44}, {"name": "Layer 3", "parent": "Layer 4", "length": 46.05, "rotation": 1.42, "x": 51.33, "y": 0.31}, {"name": "tay1", "parent": "Layer 3", "length": 56.59, "rotation": 12.16, "x": 41.79, "y": -23.2}, {"name": "tay7", "parent": "tay1", "length": 44.29, "rotation": 46.16, "x": 66.25, "y": 4.4}, {"name": "sung1", "parent": "mat", "x": 49.05, "y": 7.67}, {"name": "sung3", "parent": "sung1", "length": 45.25, "rotation": 63.94, "x": -9.67, "y": 11.14}, {"name": "sung2", "parent": "sung3", "length": 56.92, "rotation": 1.83, "x": 47.5, "y": 0.15}, {"name": "sung5", "parent": "sung2", "length": 14.53, "rotation": 110.56, "x": 59.33, "y": 12.73}, {"name": "sung4", "parent": "sung5", "length": 15.05, "rotation": 2.08, "x": 19.78, "y": 0.98}, {"name": "sung7", "parent": "sung4", "length": 13.47, "rotation": 1.12, "x": 19.8, "y": 0.26}, {"name": "sung6", "parent": "mat", "x": 43.72, "y": -3.54}, {"name": "sung9", "parent": "sung6", "length": 52.43, "rotation": -35.92, "x": -7.25, "y": -11.61}, {"name": "sung8", "parent": "sung9", "length": 34.58, "rotation": -62.69, "x": 52.34, "y": -5.49}, {"name": "bone2", "parent": "root", "length": 24.41, "rotation": -169.46, "x": -28.08, "y": -82.65}, {"name": "bone3", "parent": "root", "length": 28.45, "rotation": -11.31, "x": 61.2, "y": -78.19}, {"name": "bone4", "parent": "than", "rotation": 90.06, "x": -26.79, "y": 52.95}, {"name": "la", "parent": "root", "x": 87.23, "y": 199}, {"name": "la2", "parent": "root", "x": -66.07, "y": 199, "scaleX": 0.81, "scaleY": 0.81}, {"name": "la3", "parent": "root", "x": -6.96, "y": 205.01, "scaleX": 0.913, "scaleY": 0.913}, {"name": "txt", "parent": "root", "x": -61.71, "y": -164.08}, {"name": "txt2", "parent": "root", "x": 68.78, "y": -162.65}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "chan1", "bone": "chan2", "attachment": "chan1"}, {"name": "chan2", "bone": "chan5", "attachment": "chan2"}, {"name": "tay4", "bone": "tay5", "attachment": "tay4"}, {"name": "tay3", "bone": "tay8", "attachment": "tay3"}, {"name": "Layer 2", "bone": "Layer 3", "attachment": "Layer 2"}, {"name": "sung1", "bone": "sung7", "attachment": "sung1"}, {"name": "sung2", "bone": "sung8", "attachment": "sung2"}, {"name": "than", "bone": "than6", "attachment": "than"}, {"name": "mom", "bone": "mom", "attachment": "mom"}, {"name": "mat", "bone": "mat", "attachment": "mat"}, {"name": "tay1", "bone": "tay7", "attachment": "tay1"}, {"name": "bg2", "bone": "root", "attachment": "bg2"}, {"name": "la", "bone": "la", "attachment": "la"}, {"name": "la3", "bone": "la3", "attachment": "la"}, {"name": "la2", "bone": "la2", "attachment": "la"}, {"name": "txt", "bone": "txt", "attachment": "txt"}, {"name": "txt2", "bone": "txt2", "attachment": "txt"}], "ik": [{"name": "bone2", "bones": ["chan3", "chan2"], "target": "bone2"}, {"name": "bone3", "order": 1, "bones": ["chan6", "chan5"], "target": "bone3", "bendPositive": false}], "transform": [{"name": "bone4", "order": 2, "bones": ["than2"], "target": "bone4", "rotateMix": 0, "translateMix": 0.5, "scaleMix": 0, "shearMix": 0}, {"name": "bone5", "order": 3, "bones": ["than6"], "target": "bone4", "rotateMix": 0, "translateMix": -0.5, "scaleMix": 0, "shearMix": 0}, {"name": "bone6", "order": 4, "bones": ["sung3"], "target": "bone4", "rotateMix": 0, "translateMix": -0.1, "scaleMix": 0, "shearMix": 0}, {"name": "bone7", "order": 5, "bones": ["sung9"], "target": "bone4", "rotateMix": 0, "translateMix": -0.2, "scaleMix": 0, "shearMix": 0}], "skins": [{"name": "default", "attachments": {"txt2": {"txt": {"type": "mesh", "uvs": [0.48313, 0.01906, 0.49323, 1, 1, 1, 1, 0.02623], "triangles": [1, 0, 3, 1, 3, 2], "vertices": [-71.63, 44.6, -68.76, -53.5, 75.16, -53.5, 75.16, 43.88], "hull": 4}}, "Layer 2": {"Layer 2": {"type": "mesh", "uvs": [0.0371, 0.27327, 0.15834, 0.48102, 0.14567, 0.64412, 0.08234, 0.68101, 0, 0.86352, 0.07329, 1, 0.18005, 1, 0.29044, 0.99943, 0.29224, 0.89458, 0.42434, 0.83439, 0.36101, 0.73343, 0.44244, 0.6946, 0.50034, 0.61499, 0.61796, 0.49461, 0.76815, 0.35288, 0.78082, 0.25774, 0.61253, 0.36258, 0.74824, 0.15677, 0.5691, 0.24609, 0.71567, 0.05775, 0.3972, 0.18978, 0.31396, 0.12183, 0.20901, 0.15483, 0.26329, 0.23832, 0.17824, 0.19366, 0.06244, 0.14318, 0, 0.23638, 0.36644, 0.44996, 0.25605, 0.68101], "triangles": [13, 16, 14, 27, 18, 16, 27, 20, 18, 14, 16, 15, 16, 18, 17, 18, 20, 19, 7, 6, 8, 8, 6, 5, 5, 4, 28, 3, 2, 28, 5, 28, 8, 3, 28, 4, 8, 10, 9, 8, 28, 10, 10, 28, 11, 2, 1, 28, 1, 23, 27, 23, 0, 24, 23, 1, 0, 27, 23, 20, 0, 25, 24, 0, 26, 25, 23, 21, 20, 23, 22, 21, 12, 11, 27, 11, 28, 27, 28, 1, 27, 12, 27, 13, 27, 16, 13], "vertices": [2, 21, 28.17, -55.29, 0.00207, 12, 23.85, 47.8, 0.99793, 3, 21, 40.7, -24.14, 0.41005, 12, -5.53, 31.54, 0.54914, 22, -11.23, -24.17, 0.04081, 3, 21, 59.87, -12.5, 0.10332, 12, -27.74, 34.6, 0.01778, 22, 8.22, -13.02, 0.8789, 3, 21, 69.43, -17.06, 0.00111, 12, -32.29, 44.16, 0.00013, 22, 17.67, -17.81, 0.99877, 1, 22, 45.12, -13.57, 1, 1, 22, 54.39, 5.92, 1, 1, 22, 45.48, 18.83, 1, 1, 22, 36.2, 32.14, 1, 2, 21, 74.95, 25.11, 0.00036, 22, 24.23, 24.21, 0.99964, 2, 21, 56.86, 35.96, 0.03296, 22, 6.41, 35.5, 0.96704, 2, 21, 51.15, 20.29, 0.33798, 22, 0.32, 19.99, 0.66202, 3, 21, 39.8, 26.85, 0.83297, 22, -10.86, 26.82, 0.16691, 20, -54.68, 10.72, 0.00012, 3, 21, 25.98, 27.32, 0.9554, 22, -24.67, 27.63, 0.02202, 20, -44.25, 1.63, 0.02258, 2, 21, 2.47, 31.6, 0.67065, 20, -28.71, -16.52, 0.32935, 2, 21, -26.22, 38.03, 0.39291, 20, -10.51, -39.61, 0.60709, 2, 21, -37.86, 31.87, 0.38369, 20, 2.41, -42.17, 0.61631, 2, 21, -11.69, 20.31, 0.36893, 20, -10.61, -16.69, 0.63107, 1, 20, 16.48, -38.13, 1, 2, 21, -20.84, 5.76, 0.00777, 20, 5.67, -11.18, 0.99223, 1, 20, 30.28, -34.08, 1, 3, 21, -12.22, -19.21, 0.0844, 12, 32.43, -5.67, 0.407, 20, 14.73, 13.64, 0.50861, 3, 21, -12.55, -34.58, 0.01495, 12, 42.38, 6.05, 0.74249, 20, 24.69, 25.36, 0.24257, 2, 12, 38.7, 21.7, 0.80724, 20, 21, 41.01, 0.19276, 3, 21, 4.74, -31.22, 0.00802, 12, 26.85, 14.34, 0.87184, 20, 9.15, 33.66, 0.12014, 2, 12, 33.63, 26.5, 0.98552, 20, 15.93, 45.81, 0.01448, 2, 12, 41.45, 43.13, 0.99999, 20, 23.75, 62.44, 1e-05, 1, 12, 29.19, 52.98, 1, 2, 21, 19.27, -1.9, 0.62647, 12, -2.92, 0.76, 0.37353, 2, 21, 54.41, 3.59, 0.04422, 22, 3.17, 3.21, 0.95578], "hull": 27}}, "bg": {"bg": {"x": 0.94, "y": 0.77, "scaleX": 0.8, "scaleY": 0.8, "width": 386, "height": 584}}, "tay3": {"tay3": {"type": "mesh", "uvs": [0.15719, 0.79188, 0.0828, 0.84735, 0.00016, 0.83579, 0.05801, 0.66015, 0.12413, 0.53997, 0.23984, 0.51224, 0.33351, 0.41055, 0.43544, 0.32966, 0.48227, 0.26033, 0.59798, 0.2557, 0.66686, 0.24415, 0.60074, 0.1933, 0.60349, 0.12397, 0.70543, 0.04308, 0.86246, 0, 0.90378, 0.01304, 0.87348, 0.22335, 0.86797, 0.32735, 0.95613, 0.3805, 0.92307, 0.4637, 0.83766, 0.50993, 0.90654, 0.57233, 1, 0.63242, 1, 0.69019, 0.88174, 0.68557, 0.79909, 0.75722, 0.77154, 0.89357, 0.70543, 0.99064, 0.47125, 1, 0.38705, 1, 0.23157, 1, 0.01669, 1, 0, 0.91899, 0.0081, 0.8789, 0.06556, 0.8607, 0.13541, 0.8269, 0.82103, 0.1752, 0.71506, 0.36993, 0.6074, 0.57031, 0.44929, 0.734, 0.26593, 0.86382, 0.41564, 0.4927, 0.16501, 0.69731, 0.74029, 0.74811, 0.51321, 0.9118], "triangles": [28, 44, 27, 28, 29, 44, 30, 40, 29, 29, 40, 44, 40, 30, 32, 30, 31, 32, 27, 44, 26, 40, 34, 35, 40, 32, 34, 32, 33, 34, 40, 39, 44, 44, 39, 43, 35, 0, 40, 39, 40, 42, 42, 0, 1, 2, 42, 1, 40, 0, 42, 42, 2, 3, 42, 41, 39, 3, 4, 42, 42, 4, 5, 44, 43, 26, 26, 43, 25, 25, 43, 24, 39, 38, 43, 21, 24, 43, 39, 41, 38, 42, 5, 41, 24, 22, 23, 43, 38, 20, 24, 21, 22, 21, 43, 20, 38, 41, 37, 38, 37, 20, 37, 9, 10, 9, 37, 41, 5, 6, 41, 20, 17, 19, 20, 37, 17, 9, 7, 8, 9, 41, 7, 41, 6, 7, 19, 17, 18, 17, 37, 36, 37, 10, 36, 17, 36, 16, 11, 12, 10, 10, 13, 36, 10, 12, 13, 16, 36, 15, 36, 14, 15, 36, 13, 14], "vertices": [2, 18, 102.47, -39.39, 0.01188, 19, 44.02, -13.6, 0.98812, 2, 18, 115.99, -45.97, 0.0004, 19, 58.77, -10.72, 0.9996, 1, 19, 68.46, -18.87, 1, 2, 18, 86.27, -62.28, 0.09839, 19, 44.84, -41.63, 0.90161, 2, 18, 62.46, -61.31, 0.28016, 19, 25.23, -55.17, 0.71984, 2, 18, 51.18, -47.06, 0.53007, 19, 7.65, -50.57, 0.46993, 2, 18, 28.87, -40.97, 0.88008, 19, -13.84, -59.12, 0.11992, 2, 18, 9.53, -32.3, 0.98555, 19, -34.5, -63.82, 0.01445, 2, 18, -4.7, -30.53, 0.99941, 19, -46.94, -70.96, 0.00059, 1, 18, -12.14, -14.69, 1, 1, 18, -18.03, -5.88, 1, 1, 18, -22.68, -18.6, 1, 1, 18, -34.37, -22.97, 1, 1, 18, -53.7, -14.3, 1, 1, 18, -69.92, 4.66, 1, 1, 18, -70.13, 11.32, 1, 1, 18, -33.39, 21.54, 1, 1, 18, -15.77, 27.91, 1, 1, 18, -12.01, 43.87, 1, 1, 18, 3.74, 44.97, 1, 1, 18, 16.35, 36.22, 1, 1, 18, 22.77, 50.12, 1, 1, 18, 27.38, 67.29, 1, 1, 18, 36.99, 71.26, 1, 2, 18, 43.04, 54.44, 0.99086, 19, -59.89, 25.64, 0.00914, 2, 18, 59.72, 47.82, 0.88456, 19, -42.58, 30.38, 0.11544, 2, 18, 83.99, 53.34, 0.56651, 19, -26.5, 49.39, 0.43349, 2, 18, 103.95, 50.78, 0.39535, 19, -9.01, 59.34, 0.60465, 2, 18, 119, 18.74, 0.07819, 19, 22.28, 42.79, 0.92181, 2, 18, 123.86, 6.98, 0.01518, 19, 33.22, 36.32, 0.98482, 1, 19, 53.43, 24.36, 1, 1, 19, 81.36, 7.84, 1, 1, 19, 76.1, -5.99, 1, 1, 19, 71.37, -11.58, 1, 1, 19, 62.24, -9.98, 1, 2, 18, 109.55, -40.03, 0.0012, 19, 50.06, -9.85, 0.9988, 1, 18, -38.38, 10.91, 1, 1, 18, 0.12, 9.49, 1, 1, 18, 39.67, 8.23, 1, 1, 19, 0.76, -0.11, 1, 1, 19, 36.48, 5.91, 1, 2, 18, 37.8, -23.87, 0.89149, 19, -16.99, -40.08, 0.10851, 2, 18, 86.29, -44.8, 0.09418, 19, 34.34, -27.65, 0.90582, 2, 18, 61.59, 38.99, 0.87068, 19, -35.77, 24.45, 0.12932, 2, 18, 101.91, 18.53, 0.15442, 19, 8.74, 32.35, 0.84558], "hull": 36}}, "tay4": {"tay4": {"type": "mesh", "uvs": [0.57714, 0.87221, 0.6289, 0.9003, 0.85423, 1, 0.98069, 1, 1, 0.94175, 0.99909, 0.7887, 0.9623, 0.58781, 0.9462, 0.31677, 0.99679, 0.03936, 0.93011, 0, 0.80824, 0.00747, 0.77145, 0.03298, 0.77145, 0.13183, 0.67488, 0.09676, 0.57601, 0.09038, 0.38747, 0.13183, 0.261, 0.13502, 0.23341, 0.24025, 0.30009, 0.4507, 0.31389, 0.62608, 0.47024, 0.81421, 0.50933, 0.34547, 0.74386, 0.5623, 0.89792, 0.79189, 0.65649, 0.64202, 0.81514, 0.37736, 0.87262, 0.15415, 0.87952, 0.07444], "triangles": [21, 12, 25, 12, 21, 13, 25, 26, 7, 25, 12, 26, 7, 26, 8, 8, 26, 27, 27, 9, 8, 26, 12, 27, 12, 11, 27, 11, 10, 27, 27, 10, 9, 3, 2, 4, 2, 23, 4, 2, 1, 23, 23, 5, 4, 23, 1, 24, 24, 22, 23, 23, 6, 5, 23, 22, 6, 22, 25, 6, 25, 7, 6, 22, 21, 25, 1, 0, 24, 0, 20, 24, 20, 19, 24, 22, 24, 21, 24, 19, 21, 19, 18, 21, 14, 13, 21, 18, 17, 21, 21, 17, 16, 16, 15, 21, 21, 15, 14], "vertices": [3, 15, -62, -15.7, 0.03055, 16, 37.79, -41.85, 0.6476, 17, 13.55, -39.75, 0.32185, 3, 15, -65.38, -23.14, 0.01163, 16, 45.78, -40.13, 0.49153, 17, 20.75, -35.87, 0.49684, 2, 16, 79.23, -30.65, 0.01721, 17, 50.23, -17.45, 0.98279, 1, 17, 62.26, -3.28, 1, 1, 17, 59.39, 2.88, 1, 2, 17, 46.93, 13.27, 0.99805, 11, -44.93, -42.85, 0.00195, 2, 17, 27.2, 22.93, 0.86937, 11, -23.38, -38.59, 0.13063, 2, 17, 3.76, 39.71, 0.31351, 11, 5.44, -37.77, 0.68649, 2, 17, -13.85, 64.41, 0.0905, 11, 34.4, -46.78, 0.9095, 2, 17, -23.37, 59.63, 0.08222, 11, 39.09, -37.21, 0.91778, 2, 17, -34.36, 45.46, 0.06145, 11, 39.27, -19.28, 0.93855, 3, 16, 12.49, 48.09, 0.00037, 17, -35.8, 39.59, 0.05857, 11, 36.86, -13.74, 0.94105, 4, 15, 14.83, -48.44, 0.00247, 16, 18.27, 39.35, 0.01893, 17, -27.81, 32.81, 0.05332, 11, 26.39, -13.18, 0.92529, 4, 15, 19.31, -34.47, 0.05919, 16, 4.38, 34.62, 0.18704, 17, -39.83, 24.39, 0.00271, 11, 30.87, 0.8, 0.75106, 3, 15, 20.76, -19.99, 0.26807, 16, -8.11, 27.15, 0.32111, 11, 32.32, 15.28, 0.41082, 3, 15, 17.87, 7.92, 0.98069, 16, -28.79, 8.19, 0.00756, 11, 29.43, 43.19, 0.01176, 1, 15, 18.53, 26.5, 1, 2, 15, 7.61, 31.15, 0.9998, 16, -41.32, -13.9, 0.0002, 2, 15, -15.19, 22.56, 0.7598, 16, -20.83, -27.09, 0.2402, 2, 15, -33.87, 21.54, 0.43791, 16, -8.88, -41.47, 0.56209, 3, 15, -55.01, -0.34, 0.11212, 16, 21.3, -45.4, 0.80047, 17, -1.3, -47.75, 0.08741, 2, 15, -5.71, -8.75, 0.07562, 16, -1.35, -0.8, 0.92438, 2, 17, 4.36, 0.19, 0.99917, 11, -18.95, -6.67, 0.00083, 2, 17, 37.57, 1.71, 0.99946, 11, -44.47, -27.98, 0.00054, 3, 15, -38.26, -28.66, 0.00738, 16, 34.04, -15.06, 0.73905, 17, 2.49, -15.07, 0.25357, 3, 16, 38, 21.2, 0.00851, 17, -3.81, 20.86, 0.31541, 11, 0.06, -18.19, 0.67609, 2, 17, -16.38, 42.62, 0.11233, 11, 23.23, -27.9, 0.88767, 2, 17, -22.17, 48.86, 0.08295, 11, 31.61, -29.37, 0.91705], "hull": 21}}, "mom": {"mom": {"type": "mesh", "uvs": [0.01217, 0.65793, 0.1281, 0.73555, 0.28347, 0.83957, 0.34317, 0.88318, 0.50309, 1, 0.6129, 1, 0.80992, 0.70573, 0.91004, 0.69617, 1, 0.48108, 0.9165, 0.04132, 0.79054, 0, 0.16074, 0.00308, 0.0715, 0.08269, 0, 0.14648, 0.05093, 0.28032, 0.20596, 0.27554, 0.36421, 0.43328, 0.54508, 0.309, 0.72594, 0.36158], "triangles": [17, 15, 11, 12, 11, 15, 14, 13, 12, 15, 14, 12, 10, 17, 11, 18, 17, 10, 18, 10, 9, 16, 15, 17, 18, 9, 8, 0, 14, 15, 15, 1, 0, 8, 6, 18, 7, 6, 8, 16, 1, 15, 2, 1, 16, 5, 16, 17, 3, 2, 16, 17, 6, 5, 16, 5, 3, 5, 4, 3, 6, 17, 18], "vertices": [2, 14, 7.29, 34.24, 0.864, 36, -40.54, 31.79, 0.136, 2, 14, 3.21, 25.76, 0.864, 36, -44.18, 23.11, 0.136, 2, 14, -2.26, 14.38, 0.864, 36, -49.05, 11.46, 0.136, 2, 14, -4.54, 10.02, 0.864, 36, -51.1, 6.99, 0.136, 2, 14, -10.65, -1.68, 0.864, 36, -56.6, -5.01, 0.136, 2, 14, -10.84, -9.8, 0.864, 36, -56.37, -13.13, 0.136, 2, 14, 3.54, -24.72, 0.864, 36, -41.25, -27.29, 0.136, 2, 14, 3.85, -32.14, 0.864, 36, -40.56, -34.68, 0.136, 2, 14, 14.45, -39.04, 0.864, 36, -29.62, -41.03, 0.136, 2, 14, 36.57, -33.37, 0.864, 36, -7.81, -34.22, 0.136, 2, 14, 38.85, -24.09, 0.864, 36, -6.01, -24.84, 0.136, 2, 14, 39.77, 22.5, 0.864, 36, -7.5, 21.74, 0.136, 2, 14, 35.94, 29.2, 0.864, 36, -11.67, 28.22, 0.136, 2, 14, 32.87, 34.56, 0.864, 36, -15.01, 33.42, 0.136, 2, 14, 26.1, 30.94, 0.864, 36, -21.59, 29.46, 0.136, 2, 14, 26.07, 19.47, 0.656, 36, -21.02, 18, 0.344, 2, 14, 17.92, 7.94, 0.664, 36, -28.57, 6.07, 0.336, 2, 14, 23.82, -5.58, 0.64, 36, -21.98, -7.13, 0.36, 2, 14, 20.89, -18.9, 0.736, 36, -24.22, -20.58, 0.264], "hull": 15}}, "tay1": {"tay1": {"type": "mesh", "uvs": [0.64634, 0.00966, 0.68406, 0.0648, 0.67148, 0.14113, 0.80034, 0.19414, 0.72491, 0.2514, 0.86005, 0.34894, 0.97948, 0.39135, 1, 0.51858, 1, 0.62248, 1, 0.73911, 0.89712, 0.78902, 0.83041, 0.79483, 0.79598, 0.73095, 0.75725, 0.67868, 0.72067, 0.6119, 0.67548, 0.67868, 0.69055, 0.7295, 0.75725, 0.76434, 0.77447, 0.81806, 0.94231, 0.84709, 1, 0.85726, 0.91433, 0.93565, 0.79598, 0.95743, 0.52056, 0.89936, 0.64751, 0.96469, 0.60927, 0.98493, 0.58081, 1, 0.26234, 1, 0.11172, 0.9342, 0.04501, 0.90226, 0.04932, 0.87177, 0.08805, 0.8558, 0.0278, 0.78176, 0.06438, 0.70772, 0.13539, 0.56254, 0.07299, 0.52189, 0, 0.51899, 0, 0.44494, 0.04286, 0.39994, 0.1517, 0.39709, 0.23742, 0.40914, 0.3217, 0.41492, 0.33956, 0.33251, 0.30242, 0.36817, 0.23242, 0.39082, 0.17027, 0.39082, 0.11313, 0.38938, 0.0019, 0.36817, 0.00262, 0.32914, 0.13547, 0.26413, 0.13476, 0.2222, 0.12833, 0.17353, 0.24047, 0.16582, 0.34333, 0.18751, 0.37619, 0.23136, 0.39833, 0.26269, 0.42761, 0.26365, 0.3869, 0.15172, 0.42047, 0.12136, 0.48833, 0.10353, 0.52904, 0.07172, 0.58547, 0, 0.52453, 0.31461, 0.37218, 0.60301, 0.34043, 0.84144, 0.09074, 0.75435, 0.79327, 0.48165], "triangles": [25, 26, 64, 26, 27, 64, 27, 28, 64, 64, 23, 25, 25, 23, 24, 22, 23, 18, 18, 23, 16, 16, 17, 18, 22, 18, 21, 21, 19, 20, 21, 18, 19, 28, 29, 31, 29, 30, 31, 64, 28, 65, 65, 28, 31, 16, 23, 15, 64, 65, 63, 31, 32, 65, 15, 23, 63, 23, 64, 63, 32, 33, 65, 65, 33, 34, 63, 65, 34, 63, 34, 40, 41, 63, 40, 11, 12, 10, 9, 10, 8, 66, 8, 10, 66, 10, 12, 13, 66, 12, 8, 66, 7, 66, 5, 6, 13, 14, 66, 15, 63, 14, 66, 6, 7, 63, 62, 14, 14, 62, 66, 62, 41, 56, 55, 56, 42, 41, 62, 63, 42, 56, 41, 34, 39, 40, 34, 35, 39, 35, 37, 38, 35, 38, 39, 35, 36, 37, 66, 62, 4, 62, 2, 4, 66, 4, 5, 43, 44, 49, 49, 44, 45, 47, 48, 46, 45, 46, 49, 46, 48, 49, 42, 43, 52, 43, 49, 52, 42, 54, 55, 42, 53, 54, 42, 52, 53, 60, 62, 59, 1, 2, 61, 62, 60, 61, 56, 59, 62, 1, 61, 0, 49, 50, 52, 56, 57, 58, 56, 58, 59, 4, 2, 3, 50, 51, 52, 62, 61, 2], "vertices": [1, 23, -46.4, -6.81, 1, 1, 23, -39.56, 0.59, 1, 1, 23, -27.31, 4.12, 1, 1, 23, -24.68, 20.82, 1, 1, 23, -12.67, 16.64, 1, 2, 23, -3.49, 36.81, 0.99999, 24, -24.93, 72.75, 1e-05, 2, 23, -2.09, 51.86, 0.99999, 24, -13.11, 82.16, 1e-05, 2, 23, 16.56, 62.05, 0.99281, 24, 7.15, 75.77, 0.00719, 2, 23, 32.5, 68.63, 0.99048, 24, 22.95, 68.84, 0.00952, 2, 23, 50.39, 76.03, 0.9988, 24, 40.67, 61.05, 0.0012, 2, 23, 62.45, 68.54, 0.99865, 24, 43.62, 47.17, 0.00135, 2, 23, 66.19, 62, 0.99762, 24, 41.5, 39.94, 0.00238, 2, 23, 57.86, 54.39, 0.98601, 24, 30.24, 40.68, 0.01399, 2, 23, 51.5, 47.07, 0.95447, 24, 20.55, 40.19, 0.04553, 2, 23, 42.82, 39.05, 0.81565, 24, 8.76, 40.9, 0.18435, 2, 23, 55, 38.6, 0.38662, 24, 16.87, 31.81, 0.61338, 2, 23, 62.15, 43.38, 0.14466, 24, 25.27, 29.96, 0.85534, 2, 23, 64.64, 52.5, 0.05136, 24, 33.57, 34.48, 0.94864, 2, 23, 72.15, 57.68, 0.014, 24, 42.51, 32.65, 0.986, 1, 24, 54.49, 47.93, 1, 1, 24, 58.63, 53.16, 1, 1, 24, 66.68, 39.15, 1, 1, 24, 64.66, 25.56, 1, 2, 23, 95.48, 36.55, 1e-05, 24, 43.43, 1.19, 0.99999, 1, 24, 59.08, 9.85, 1, 1, 24, 60.43, 4.57, 1, 1, 24, 61.44, 0.65, 1, 1, 24, 47.1, -32.01, 1, 1, 24, 30.31, -43.06, 1, 1, 24, 22.45, -47.77, 1, 1, 24, 18.01, -45.29, 1, 2, 23, 107.29, -10.98, 0.00066, 24, 17.33, -40.25, 0.99934, 2, 23, 98.5, -21.91, 0.02265, 24, 3.36, -41.49, 0.97735, 2, 23, 85.58, -22.82, 0.11718, 24, -6.24, -32.79, 0.88282, 2, 23, 60.27, -24.67, 0.83446, 24, -25.11, -15.82, 0.16554, 2, 23, 56.7, -33.7, 0.98148, 24, -34.1, -19.51, 0.01852, 2, 23, 59.38, -41.44, 0.99825, 24, -37.83, -26.8, 0.00175, 1, 23, 48.02, -46.13, 1, 1, 23, 39.28, -44.55, 1, 1, 23, 34.19, -33.46, 1, 1, 23, 32.37, -23.83, 1, 1, 23, 29.65, -14.73, 1, 1, 23, 16.25, -18.11, 1, 1, 23, 23.31, -19.69, 1, 1, 23, 29.78, -25.5, 1, 1, 23, 32.43, -31.94, 1, 1, 23, 34.65, -37.94, 1, 1, 23, 36.16, -50.8, 1, 1, 23, 30.14, -53.2, 1, 1, 23, 14.48, -43.57, 1, 1, 23, 8.08, -46.3, 1, 1, 23, 0.89, -50.05, 1, 1, 23, -5.09, -38.93, 1, 1, 23, -6.16, -26.91, 1, 1, 23, -0.84, -20.73, 1, 1, 23, 3.02, -16.45, 1, 1, 23, 1.92, -13.36, 1, 1, 23, -13.52, -24.67, 1, 1, 23, -19.61, -23.11, 1, 1, 23, -25.25, -17.22, 1, 1, 23, -31.87, -15.02, 1, 1, 23, -45.28, -13.72, 1, 1, 23, 5.59, -0.09, 1, 2, 23, 56.35, 2.41, 0.90001, 24, -8.29, 5.76, 0.09999, 1, 24, 26.52, -13.41, 1, 2, 23, 91.61, -17.13, 0.04558, 24, 2.03, -33.2, 0.95442, 2, 23, 19.73, 38.31, 0.97465, 24, -7.77, 57.04, 0.02535], "hull": 62}}, "bg2": {"bg2": {"x": 3.94, "y": -115.73, "width": 292, "height": 121}}, "la2": {"la": {"x": 1.71, "y": 1.27, "width": 30, "height": 25}}, "la3": {"la": {"x": 1.71, "y": 1.27, "width": 30, "height": 25}}, "chan1": {"chan1": {"type": "mesh", "uvs": [0.99259, 0.10842, 0.94863, 0.22767, 0.7899, 0.31223, 0.72152, 0.42715, 0.75571, 0.47051, 0.71907, 0.59193, 0.8192, 0.74675, 0.90223, 0.8118, 0.75326, 0.92238, 0.5115, 0.94623, 0.37962, 0.94623, 0.34299, 1, 0.22577, 0.97875, 0.22821, 0.92888, 0.12076, 0.97875, 0, 0.98742, 0, 0.92888, 0.06459, 0.90937, 0.07192, 0.83131, 0.15251, 0.73591, 0.29171, 0.61232, 0.29903, 0.50608, 0.27705, 0.41718, 0.35032, 0.35647, 0.38695, 0.25673, 0.56522, 0.23939, 0.70442, 0.07894, 0.79966, 0.01172, 0.90467, 0, 0.97061, 0, 0.74594, 0.21337, 0.46021, 0.49957, 0.45289, 0.7229], "triangles": [3, 31, 30, 31, 23, 25, 21, 22, 23, 31, 21, 23, 5, 31, 3, 5, 3, 4, 31, 20, 21, 32, 31, 5, 32, 20, 31, 6, 32, 5, 8, 6, 7, 8, 32, 6, 32, 13, 19, 32, 19, 20, 18, 19, 13, 17, 18, 13, 32, 11, 13, 9, 10, 32, 8, 9, 32, 14, 17, 13, 11, 12, 13, 15, 16, 17, 15, 17, 14, 32, 10, 11, 30, 28, 29, 0, 30, 29, 30, 26, 27, 30, 27, 28, 0, 1, 30, 25, 26, 30, 2, 30, 1, 30, 31, 25, 25, 23, 24, 2, 3, 30], "vertices": [2, 5, -8.36, 8.51, 0.47922, 4, 6.2, 8.88, 0.52078, 2, 5, 3.18, 15.36, 0.98144, 4, 19.18, 5.48, 0.01856, 2, 5, 20.49, 12.24, 0.96315, 6, -12.14, 24.09, 0.03685, 2, 5, 33.48, 17.21, 0.45701, 6, 0.72, 18.79, 0.54299, 2, 5, 34.09, 22.85, 0.24573, 6, 5.03, 22.47, 0.75427, 2, 5, 45.26, 30.33, 0.02894, 6, 18.29, 20.24, 0.97106, 1, 6, 33.87, 31.29, 1, 1, 6, 40.05, 39.8, 1, 1, 6, 53.18, 26.84, 1, 1, 6, 57.91, 4.22, 1, 1, 6, 59.1, -8.25, 1, 1, 6, 65.16, -11.16, 1, 1, 6, 63.96, -22.46, 1, 1, 6, 58.62, -22.74, 1, 1, 6, 64.91, -32.4, 1, 1, 6, 66.93, -43.73, 1, 1, 6, 60.69, -44.32, 1, 1, 6, 58.03, -38.41, 1, 1, 6, 49.65, -38.52, 1, 1, 6, 38.76, -31.87, 1, 1, 6, 24.33, -19.97, 1, 2, 5, 69.24, -2.86, 0.01422, 6, 12.95, -20.36, 0.98578, 2, 5, 64.55, -11.39, 0.08967, 6, 3.68, -23.34, 0.91033, 2, 5, 55.04, -11.7, 0.34475, 6, -3.45, -17.03, 0.65525, 2, 5, 45.39, -17.44, 0.81334, 6, -14.4, -14.59, 0.18666, 1, 5, 31.42, -7.68, 1, 2, 5, 10.16, -11.89, 0.95335, 4, 4.68, -18.63, 0.04665, 2, 5, -1.38, -11.35, 0.51783, 4, -3.04, -10.03, 0.48217, 1, 4, -4.89, -0.14, 1, 1, 4, -5.26, 6.11, 1, 2, 5, 16.67, 1.53, 0.99973, 6, -22.27, 18.92, 0.00027, 1, 6, 10.8, -5.18, 1, 1, 6, 34.65, -3.6, 1], "hull": 30}}, "chan2": {"chan2": {"type": "mesh", "uvs": [0, 0.17187, 0.20107, 0.3491, 0.25176, 0.41943, 0.22959, 0.47569, 0.26127, 0.59384, 0.19156, 0.71761, 0.14404, 0.81607, 0.20741, 0.90609, 0.60029, 0.96235, 0.68584, 1, 0.78406, 0.94547, 0.91713, 0.9736, 1, 0.98204, 0.97416, 0.8864, 0.88545, 0.757, 0.74604, 0.66416, 0.67316, 0.57415, 0.6795, 0.45318, 0.67, 0.38286, 0.61613, 0.23095, 0.45454, 0.22814, 0.34048, 0.05935, 0.15988, 0, 0, 0, 0.23592, 0.23095, 0.49573, 0.51226, 0.57811, 0.79638], "triangles": [18, 25, 20, 25, 18, 17, 16, 25, 17, 4, 3, 25, 26, 25, 16, 26, 16, 15, 4, 25, 26, 5, 4, 26, 7, 5, 26, 6, 5, 7, 10, 15, 14, 10, 14, 13, 26, 15, 10, 8, 26, 10, 7, 26, 8, 11, 10, 13, 11, 13, 12, 9, 8, 10, 24, 0, 23, 24, 22, 21, 24, 21, 20, 24, 23, 22, 1, 0, 24, 25, 2, 24, 1, 24, 2, 18, 20, 19, 25, 24, 20, 25, 3, 2], "vertices": [2, 8, -2.14, -13.57, 0.58303, 7, 12.3, -4.38, 0.41697, 2, 8, 24.75, -12.45, 0.99492, 9, -8.19, -26.14, 0.00508, 2, 8, 33.55, -14.03, 0.86486, 9, -0.87, -21.01, 0.13514, 2, 8, 36.53, -19.67, 0.65111, 9, 5.23, -22.87, 0.34889, 2, 8, 47.85, -26.05, 0.19414, 9, 17.74, -19.33, 0.80586, 2, 8, 53.06, -39.91, 0.0151, 9, 31.24, -25.4, 0.9849, 2, 8, 57.72, -50.38, 0.00038, 9, 41.96, -29.47, 0.99962, 1, 9, 51.33, -23.06, 1, 1, 9, 55.79, 14.49, 1, 1, 9, 59.48, 22.77, 1, 1, 9, 53.26, 31.85, 1, 1, 9, 55.74, 44.61, 1, 1, 9, 56.32, 52.51, 1, 1, 9, 46.19, 49.64, 1, 1, 9, 32.71, 40.64, 1, 1, 9, 23.34, 26.99, 1, 1, 9, 14, 19.67, 1, 2, 8, 63.81, 13.32, 0.03869, 9, 1.04, 19.74, 0.96131, 2, 8, 57.68, 17.77, 0.17387, 9, -6.44, 18.52, 0.82613, 2, 8, 42.28, 25.07, 0.59069, 9, -22.46, 12.73, 0.40931, 2, 8, 31.62, 14.01, 0.92276, 9, -22.12, -2.62, 0.07724, 1, 8, 11.01, 18.34, 1, 2, 8, -5.31, 10.07, 0.74059, 7, -6.96, 9.68, 0.25941, 1, 7, -6.05, -5.48, 1, 1, 8, 17.73, -1.43, 1, 1, 9, 8.09, 2.56, 1, 1, 9, 38.14, 11.64, 1], "hull": 24}}, "than": {"than": {"type": "mesh", "uvs": [0.07591, 0.57764, 0.19298, 0.67111, 0.29133, 0.68374, 0.30772, 0.80248, 0.36625, 0.88332, 0.41777, 0.88332, 0.42713, 0.98438, 0.57698, 0.99953, 0.61211, 0.90859, 0.70108, 0.87069, 0.7924, 0.8328, 0.74557, 0.71911, 0.69406, 0.65343, 0.69172, 0.55995, 0.78303, 0.53974, 0.90947, 0.4488, 0.94459, 0.31238, 0.95396, 0.17595, 0.85328, 0.10522, 0.79006, 0.0168, 0.50674, 0, 0.29367, 0.00417, 0.16255, 0.06732, 0.0525, 0.18101, 0.0244, 0.32248, 0.04079, 0.49427], "triangles": [13, 2, 21, 21, 2, 22, 21, 20, 13, 25, 2, 0, 22, 2, 25, 2, 1, 0, 23, 22, 24, 14, 13, 18, 15, 14, 16, 18, 20, 19, 20, 18, 13, 22, 25, 24, 16, 14, 18, 16, 18, 17, 8, 7, 5, 7, 6, 5, 9, 8, 12, 12, 8, 5, 5, 2, 12, 5, 4, 3, 9, 11, 10, 9, 12, 11, 5, 3, 2, 2, 13, 12], "vertices": [4, 3, -12.31, -73.34, 0.03696, 2, -72.08, 1.92, 0.1474, 10, -5.61, 71.56, 0.53564, 12, -32.96, 3.8, 0.28, 3, 3, 0.73, -53.32, 0.15749, 2, -52.46, -11.72, 0.25606, 10, -20.83, 53.15, 0.58645, 3, 3, 1.69, -37.11, 0.39923, 2, -36.29, -13.16, 0.25999, 10, -23.61, 37.15, 0.34078, 3, 3, 19.54, -33.35, 0.81917, 2, -33.07, -31.12, 0.11592, 10, -41.78, 35.43, 0.06491, 3, 3, 31.24, -23.04, 0.94716, 2, -23.11, -43.12, 0.04223, 10, -54.56, 26.51, 0.01061, 3, 3, 30.73, -14.6, 0.98358, 2, -14.66, -42.87, 0.01486, 10, -55.02, 18.07, 0.00157, 1, 3, 45.98, -12.15, 1, 1, 3, 46.81, 12.52, 1, 2, 3, 32.67, 17.44, 0.99843, 2, 17.31, -45.77, 0.00157, 2, 3, 26.05, 31.66, 0.97121, 2, 31.72, -39.58, 0.02879, 2, 3, 19.41, 46.27, 0.95222, 2, 46.52, -33.38, 0.04778, 3, 3, 2.62, 37.57, 0.87421, 2, 38.34, -16.33, 0.11551, 10, -32.98, -36.95, 0.01028, 3, 3, -6.85, 28.55, 0.61666, 2, 29.6, -6.6, 0.26508, 10, -22.56, -29.05, 0.11826, 3, 3, -21.01, 27.32, 0.14631, 2, 28.79, 7.59, 0.26842, 10, -8.35, -29.43, 0.58527, 4, 3, -24.97, 42.08, 0.01057, 2, 43.67, 11.1, 0.08999, 10, -6.09, -44.55, 0.56344, 11, -32.08, 18, 0.336, 3, 2, 63.99, 25.53, 0.04165, 10, 6.6, -66, 0.62235, 11, -19.39, -3.45, 0.336, 3, 2, 69.14, 46.43, 0.01747, 10, 27, -72.87, 0.54253, 11, 1, -10.32, 0.44, 3, 2, 70.06, 67.2, 0.00864, 10, 47.62, -75.52, 0.58336, 11, 21.63, -12.97, 0.408, 3, 2, 53.24, 77.46, 0.00434, 10, 59.25, -59.61, 0.73966, 11, 33.25, 2.95, 0.256, 3, 2, 42.48, 90.59, 0.00042, 10, 73.23, -49.97, 0.71158, 11, 47.23, 12.58, 0.288, 1, 10, 78.27, -3.71, 1, 2, 2, -38.95, 90.1, 0.00847, 10, 79.52, 31.21, 0.99153, 2, 2, -60.16, 79.87, 0.03034, 10, 71.09, 53.2, 0.96966, 3, 2, -77.69, 62.06, 0.04398, 10, 54.8, 72.15, 0.63602, 12, 27.45, 4.39, 0.32, 4, 3, -50.52, -84.08, 0.00119, 2, -81.67, 40.43, 0.06697, 10, 33.58, 77.91, 0.56384, 12, 6.22, 10.15, 0.368, 4, 3, -24.61, -79.84, 0.01782, 2, -78.21, 14.41, 0.12088, 10, 7.36, 76.63, 0.5573, 12, -20, 8.87, 0.304], "hull": 26}}, "txt": {"txt": {"type": "mesh", "uvs": [0.48541, 0, 0.48541, 1, 0, 1, 0.00825, 0], "triangles": [3, 0, 1, 2, 3, 1], "vertices": [59.51, 47.94, 59.51, -52.06, -78.35, -52.06, -76, 47.94], "hull": 4}}, "mat": {"mat": {"type": "mesh", "uvs": [0, 0.83243, 0.34508, 1, 0.45901, 1, 0.57295, 0.95559, 0.75672, 0.97917, 0.97356, 0.89794, 1, 0.70141, 0.9221, 0.45772, 0.80449, 0.21664, 0.59133, 0.09611, 0.48842, 0.00177, 0.17601, 0.01487, 0.26422, 0.28477, 0.17601, 0.23236, 0, 0.35552, 0.06943, 0.52061, 0.03635, 0.69617, 0, 0.80885, 0.33773, 0.44724, 0.36713, 0.649, 0.2532, 0.66735, 0.24952, 0.75906, 0.33773, 0.81147, 0.43329, 0.7774, 0.43696, 0.69355, 0.30833, 0.9058, 0.66116, 0.3529, 0.71261, 0.62018, 0.63175, 0.65424, 0.6244, 0.73548, 0.74569, 0.78002, 0.8633, 0.71451, 0.81185, 0.6228, 0.66851, 0.89794, 0.15244, 0.68431, 0.81156, 0.49002], "triangles": [28, 24, 18, 19, 20, 18, 18, 24, 19, 3, 2, 23, 1, 0, 25, 16, 25, 17, 21, 25, 16, 16, 34, 21, 2, 1, 25, 23, 2, 25, 23, 25, 22, 3, 33, 4, 33, 30, 4, 5, 30, 31, 5, 4, 30, 3, 29, 33, 3, 23, 29, 25, 21, 22, 5, 31, 6, 33, 29, 30, 25, 0, 17, 21, 34, 20, 31, 32, 6, 32, 7, 6, 16, 15, 34, 34, 15, 20, 20, 15, 18, 27, 35, 32, 32, 35, 7, 28, 18, 26, 27, 26, 35, 15, 12, 18, 12, 14, 13, 12, 15, 14, 7, 35, 8, 35, 26, 8, 18, 12, 26, 12, 9, 26, 26, 9, 8, 12, 10, 9, 10, 12, 11, 30, 32, 31, 30, 27, 32, 26, 27, 28, 22, 21, 19, 19, 21, 20, 24, 22, 19, 23, 22, 24, 30, 29, 27, 29, 28, 27, 23, 24, 29, 29, 24, 28], "vertices": [2, 13, -0.46, 40.87, 0.856, 36, -8.73, 38.6, 0.144, 2, 13, -19.28, 14.81, 0.856, 36, -26.06, 11.53, 0.144, 2, 13, -19.53, 6.04, 0.856, 36, -25.81, 2.76, 0.144, 2, 13, -14.98, -2.86, 0.856, 36, -20.77, -5.88, 0.144, 2, 13, -17.92, -16.93, 0.856, 36, -22.91, -20.09, 0.144, 2, 13, -9.61, -33.87, 0.856, 36, -13.66, -36.53, 0.144, 2, 13, 11.55, -36.49, 0.856, 36, 7.61, -37.96, 0.144, 2, 13, 38.03, -31.23, 0.864, 36, 33.75, -31.21, 0.136, 2, 13, 64.3, -22.9, 0.768, 36, 59.52, -21.42, 0.232, 2, 13, 77.77, -6.85, 0.776, 36, 72.06, -4.64, 0.224, 2, 13, 88.18, 0.79, 0.816, 36, 82.02, 3.57, 0.184, 2, 13, 87.43, 24.87, 0.728, 36, 79.92, 27.58, 0.272, 2, 13, 58.1, 18.89, 0.592, 36, 50.97, 19.96, 0.408, 2, 13, 63.95, 25.52, 0.728, 36, 56.44, 26.91, 0.272, 2, 13, 51.03, 39.44, 0.776, 36, 42.75, 40.07, 0.224, 2, 13, 33.06, 34.59, 0.856, 36, 25.09, 34.22, 0.144, 2, 13, 14.18, 37.66, 0.856, 36, 6.06, 36.23, 0.144, 2, 13, 2.09, 40.8, 0.856, 36, -6.18, 38.68, 0.144, 2, 13, 40.41, 13.72, 0.592, 36, 33.6, 13.8, 0.408, 2, 13, 18.56, 12.06, 0.656, 36, 11.88, 10.91, 0.344, 2, 13, 16.82, 20.89, 0.656, 36, 9.65, 19.62, 0.344, 2, 13, 6.93, 21.44, 0.656, 36, -0.26, 19.62, 0.344, 2, 13, 1.09, 14.81, 0.656, 36, -5.72, 12.67, 0.344, 2, 13, 4.56, 7.36, 0.656, 36, -1.84, 5.42, 0.344, 2, 13, 13.6, 6.82, 0.656, 36, 7.22, 5.4, 0.344, 2, 13, -9.04, 17.36, 0.776, 36, -15.97, 14.65, 0.224, 2, 13, 49.9, -11.46, 0.568, 36, 44.49, -10.81, 0.432, 2, 13, 20.93, -14.62, 0.672, 36, 15.75, -15.59, 0.328, 2, 13, 17.43, -8.29, 0.672, 36, 11.9, -9.47, 0.328, 2, 13, 8.68, -7.48, 0.656, 36, 3.11, -9.16, 0.344, 2, 13, 3.61, -16.68, 0.656, 36, -1.43, -18.63, 0.344, 2, 13, 10.43, -25.93, 0.672, 36, 5.9, -27.48, 0.328, 2, 13, 20.44, -22.25, 0.728, 36, 15.69, -23.24, 0.272, 2, 13, -8.96, -10.39, 0.784, 36, -14.33, -13.05, 0.216, 2, 13, 15.21, 28.69, 0.816, 36, 7.59, 27.33, 0.184, 2, 13, 34.78, -22.62, 0.696, 36, 30.02, -22.81, 0.304], "hull": 18}}, "la": {"la": {"x": 1.71, "y": 1.27, "width": 30, "height": 25}}, "sung1": {"sung1": {"type": "mesh", "uvs": [0.49427, 0.67772, 0.55627, 0.88734, 0.62027, 0.97442, 0.74427, 1, 0.96627, 1, 1, 0.91314, 1, 0.71728, 1, 0.50357, 0.87627, 0.40037, 0.81427, 0.24235, 0.80427, 0.13592, 0.62627, 0.1585, 0.54227, 0.19075, 0.50227, 0.13592, 0.45427, 0.14882, 0.42827, 0.05207, 0.37227, 0.03917, 0.37027, 0.1585, 0.22827, 0.03272, 0.14227, 0.07465, 0.09827, 0, 0.03427, 0, 0, 0.13592, 0.02827, 0.28427, 0.03627, 0.50035, 0, 0.63257, 0.00827, 0.81962, 0.06827, 1, 0.13027, 1, 0.10827, 0.74222, 0.10427, 0.53582, 0.12287, 0.46438, 0.09455, 0.37303, 0.09455, 0.28955, 0.13459, 0.26278, 0.13655, 0.36515, 0.1385, 0.50217, 0.1385, 0.63132, 0.16292, 0.73527, 0.21371, 0.7652, 0.32506, 0.71165, 0.36315, 0.68172, 0.43933, 0.71165, 0.07454, 0.81571, 0.07257, 0.58711, 0.06076, 0.25056, 0.08636, 0.13944, 0.30688, 0.25056, 0.60223, 0.47281], "triangles": [29, 43, 44, 26, 25, 43, 27, 26, 43, 28, 43, 29, 28, 27, 43, 45, 44, 24, 44, 32, 30, 29, 44, 30, 25, 24, 44, 25, 44, 43, 23, 22, 45, 33, 45, 34, 45, 24, 23, 45, 32, 44, 45, 33, 32, 31, 30, 32, 46, 21, 20, 46, 20, 19, 22, 21, 46, 17, 16, 15, 17, 15, 14, 45, 22, 46, 47, 18, 17, 19, 18, 47, 46, 19, 47, 34, 45, 46, 47, 34, 46, 35, 34, 47, 14, 13, 12, 14, 47, 17, 14, 12, 47, 48, 47, 12, 36, 35, 47, 41, 36, 47, 36, 41, 37, 41, 47, 48, 40, 37, 41, 42, 41, 0, 38, 37, 40, 39, 38, 40, 11, 10, 9, 48, 12, 11, 48, 11, 9, 48, 9, 8, 48, 0, 41, 8, 7, 6, 48, 8, 6, 6, 1, 0, 6, 0, 48, 3, 2, 1, 1, 6, 3, 5, 3, 6, 4, 3, 5], "vertices": [2, 26, 42.96, 20.76, 0.55218, 27, -3.88, 20.74, 0.44782, 2, 26, 28.73, 32.71, 0.97418, 27, -17.71, 33.14, 0.02582, 2, 26, 18.33, 35.63, 0.99821, 27, -28.02, 36.39, 0.00179, 1, 26, 2.93, 30.87, 1, 1, 26, -23.14, 19.01, 1, 1, 26, -24.22, 10.88, 1, 1, 26, -17.73, -3.38, 1, 1, 26, -10.65, -18.94, 1, 1, 26, 7.3, -19.84, 1, 2, 26, 19.82, -28.04, 0.99949, 27, -28.57, -27.29, 0.00051, 2, 26, 24.52, -35.25, 0.99734, 27, -24.1, -34.65, 0.00266, 2, 26, 44.67, -24.1, 0.7623, 27, -3.6, -24.15, 0.2377, 2, 26, 53.46, -17.26, 0.29427, 27, 5.4, -17.59, 0.70573, 2, 26, 59.98, -19.11, 0.09254, 27, 11.85, -19.66, 0.90746, 2, 26, 65.18, -15.61, 0.02214, 27, 17.17, -16.32, 0.97786, 1, 27, 23.25, -22.17, 1, 1, 27, 30.31, -20.34, 1, 1, 27, 26.88, -11.43, 1, 1, 27, 47.66, -13.67, 1, 1, 27, 56.6, -6.3, 1, 2, 27, 64.14, -9.63, 0.97613, 28, -22.63, 3.35, 0.02387, 2, 27, 71.76, -6.45, 0.89465, 28, -22.33, -4.9, 0.10535, 2, 27, 71.66, 5.29, 0.57462, 28, -11.3, -8.92, 0.42538, 2, 27, 63.72, 14.83, 0.0235, 28, 0.43, -4.84, 0.9765, 2, 28, 17.66, -3.18, 0.41042, 29, -2.26, -4.09, 0.58958, 2, 29, 8.31, -8.77, 0.98079, 30, -11.66, -8.81, 0.01921, 2, 29, 23.28, -7.7, 0.08457, 30, 3.32, -8.03, 0.91543, 1, 30, 17.9, -0.58, 1, 1, 30, 18.06, 7.42, 1, 2, 29, 17.09, 5.2, 0.4369, 30, -2.62, 4.99, 0.5631, 2, 28, 20.18, 5.68, 0.11501, 29, 0.57, 4.68, 0.88499, 2, 28, 14.38, 7.87, 0.72454, 29, -5.14, 7.08, 0.27546, 2, 28, 7.21, 3.96, 0.99694, 29, -12.45, 3.43, 0.00306, 2, 27, 55.67, 11.93, 0.15317, 28, 0.54, 3.71, 0.84683, 2, 27, 51.72, 7.97, 0.77665, 28, -1.79, 8.8, 0.22335, 2, 27, 48.34, 15.43, 0.96806, 28, 6.39, 9.35, 0.03194, 2, 27, 43.89, 25.45, 0.99897, 28, 17.33, 10, 0.00103, 1, 27, 39.91, 34.99, 1, 1, 27, 33.8, 41.45, 1, 1, 27, 26.83, 41.14, 1, 2, 26, 61.7, 32.28, 0.01433, 27, 15.22, 31.65, 0.98567, 2, 26, 58.22, 28.06, 0.05579, 27, 11.61, 27.55, 0.94421, 2, 26, 48.29, 26.17, 0.23782, 27, 1.62, 25.98, 0.76218, 1, 30, 3.18, 0.52, 1, 1, 29, 4.68, 0.59, 1, 2, 27, 60.89, 10.73, 0.12359, 28, -2.42, -0.75, 0.87641, 2, 27, 61.27, 1.26, 0.81599, 28, -11.42, 2.22, 0.18401, 1, 27, 31.59, -1.49, 1, 1, 26, 37.08, 0.07, 1], "hull": 43}}, "sung2": {"sung2": {"type": "mesh", "uvs": [0, 0.65594, 0.00359, 0.91313, 0.12759, 1, 0.26507, 1, 0.39985, 0.87332, 0.52385, 0.6988, 0.53464, 0.49061, 0.57507, 0.54572, 0.53194, 0.83658, 0.64516, 0.83046, 0.82846, 0.74779, 0.90664, 0.58858, 0.95516, 0.3712, 1, 0.25179, 0.91742, 0.0436, 0.69368, 0.07115, 0.66403, 0.13545, 0.63707, 0.02217, 0.55081, 0, 0.34864, 0.05584, 0.11951, 0.17525, 0.10603, 0.34977, 0.05212, 0.60083, 0.27046, 0.57634, 0.42142, 0.30691, 0.70716, 0.30384], "triangles": [10, 9, 7, 8, 7, 9, 10, 7, 11, 7, 25, 11, 11, 25, 12, 7, 6, 25, 6, 24, 25, 12, 25, 13, 24, 16, 25, 16, 18, 17, 25, 16, 14, 25, 14, 13, 14, 16, 15, 3, 2, 4, 2, 23, 4, 2, 1, 23, 23, 0, 22, 23, 1, 0, 4, 23, 5, 5, 23, 6, 22, 21, 23, 23, 24, 6, 23, 21, 24, 21, 20, 24, 16, 24, 18, 20, 19, 24, 24, 19, 18], "vertices": [1, 32, -1.83, 13.96, 1, 1, 32, -18.85, 1.94, 1, 1, 32, -18.23, -11.45, 1, 1, 32, -11.09, -21.9, 1, 2, 32, 4.37, -26.35, 0.9956, 33, -3.48, -52.19, 0.0044, 2, 32, 22.48, -27.8, 0.9234, 33, 6.12, -36.77, 0.0766, 2, 32, 36.97, -19.11, 0.4324, 33, 5.04, -19.91, 0.5676, 2, 32, 35.38, -24.7, 0.10717, 33, 9.28, -23.89, 0.89283, 1, 33, 8.22, -47.75, 1, 1, 33, 18.49, -45.99, 1, 1, 33, 34.41, -37.28, 1, 1, 33, 39.98, -23.61, 1, 1, 33, 42.26, -5.58, 1, 1, 33, 45.17, 4.52, 1, 1, 33, 35.57, 20.33, 1, 2, 32, 73.28, -12.03, 0.01167, 33, 15.41, 15.6, 0.98833, 2, 32, 67.44, -12.71, 0.07632, 33, 13.34, 10.1, 0.92368, 2, 32, 73.62, -5.49, 0.34732, 33, 9.76, 18.9, 0.65268, 2, 32, 70.62, 2.08, 0.53062, 33, 1.66, 19.71, 0.46938, 2, 32, 56.4, 14.88, 0.99847, 33, -16.25, 12.95, 0.00153, 1, 32, 36.52, 26.84, 1, 1, 32, 24.15, 19.89, 1, 1, 32, 4.56, 12.52, 1, 2, 32, 17.53, -2.95, 0.99758, 33, -18.23, -29.77, 0.00242, 2, 32, 43.38, -2.11, 0.90786, 33, -7.12, -6.41, 0.09214, 1, 33, 18.95, -2.96, 1], "hull": 23}}}}], "animations": {"animation": {"slots": {"la": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8, "color": "ffffffff"}, {"time": 1.9667, "color": "ffffff00"}]}, "la3": {"color": [{"time": 1.3333, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5333, "color": "ffffff00"}, {"time": 1.7, "color": "ffffffff"}]}, "la2": {"color": [{"time": 0.8, "color": "ffffffff"}, {"time": 0.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00"}, {"time": 1.1667, "color": "ffffffff"}]}}, "bones": {"la": {"rotate": [{}, {"time": 1, "angle": 180}, {"time": 1.9667}], "translate": [{}, {"time": 1.9667, "x": -103.21, "y": -297.59}, {"time": 2}]}, "la2": {"rotate": [{"angle": 180}, {"time": 0.9667, "curve": "stepped"}, {"time": 1}, {"time": 2, "angle": 180}], "translate": [{"x": 89.16, "y": -151.32}, {"time": 0.9667, "x": 175.35, "y": -297.59}, {"time": 1}, {"time": 2, "x": 89.16, "y": -151.32}]}, "la3": {"rotate": [{"angle": -84}, {"time": 0.5333, "angle": 180}, {"time": 1.5, "curve": "stepped"}, {"time": 1.5333}, {"time": 2, "angle": -84}], "translate": [{"x": -24.49, "y": -70.62}, {"time": 1.5, "x": -103.21, "y": -297.59}, {"time": 1.5333}, {"time": 2, "x": -24.49, "y": -70.62}]}, "than6": {"rotate": [{"angle": -3.47, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 9.47, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -3.47}]}, "bone4": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 2.43, "y": -20.82, "curve": "stepped"}, {"time": 1, "x": 2.43, "y": -20.82, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 27.96, "y": -33.7, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 38.71, "y": -18.84, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "mom": {"translate": [{"time": 0.3333}, {"time": 0.4333, "x": 12.69, "y": -0.35}, {"time": 0.5, "x": 2.64, "y": -0.07}, {"time": 0.5667, "x": 12.69, "y": -0.35}, {"time": 0.6333, "x": 2.64, "y": -0.07}, {"time": 0.7, "x": 12.69, "y": -0.35}, {"time": 0.7667, "x": 2.64, "y": -0.07}, {"time": 0.8333}]}, "tay3": {"rotate": [{"angle": 2.34, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 9.07, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -4.39, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 2.34}]}, "sung5": {"rotate": [{"angle": 21.05, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 25.32, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -18.91, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 21.05}]}, "txt": {"scale": [{}, {"time": 0.0667, "x": 1.063, "y": 1.063}, {"time": 0.1667}, {"time": 0.2333, "x": 1.063, "y": 1.063}, {"time": 0.3333}]}, "sung4": {"rotate": [{"angle": 12.77, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 25.32, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -18.91, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 12.77}]}, "tay1": {"rotate": [{"angle": 5.41, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 9.47, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 5.41}]}, "sung3": {"rotate": [{"angle": 8.42, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 9.73, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -3.9, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 8.42}]}, "txt2": {"scale": [{"time": 0.3333}, {"time": 0.4, "x": 1.063, "y": 1.063}, {"time": 0.5}, {"time": 0.5667, "x": 1.063, "y": 1.063}, {"time": 0.6667}]}, "sung9": {"rotate": [{"angle": 2.19, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 22.71, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 2.19}]}, "sung8": {"rotate": [{"angle": 6.44, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 22.71, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 6.44}]}, "than": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -4.72, "y": -18.17, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "sung7": {"rotate": [{"angle": 3.2, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 25.32, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -18.91, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 3.2}]}, "tay8": {"rotate": [{"angle": -0.57, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": 9.07, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -4.39, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -0.57}]}, "tay6": {"rotate": [{"angle": 7.77, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 9.07, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -4.39, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 7.77}]}, "Layer 3": {"rotate": [{"angle": 2.31, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 9.47, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 2.31}]}, "sung2": {"rotate": [{"angle": 5.86, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 9.73, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.9, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 5.86}]}, "Layer 4": {"rotate": [{"angle": -0.79, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 9.47, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.79}]}, "tay7": {"rotate": [{"angle": 8.09, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "angle": -4.85, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 9.47, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 8.09}]}, "tay5": {"rotate": [{"angle": 5.25, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 9.07, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.39, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 5.25}]}}}}}