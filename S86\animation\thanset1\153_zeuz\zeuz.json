{"skins": {"default": {"1": {"1": {"triangles": [2, 6, 1, 12, 9, 2, 9, 6, 2, 3, 12, 2, 10, 9, 12, 6, 9, 10, 13, 12, 3, 10, 12, 13, 7, 1, 6, 7, 6, 10, 11, 7, 10, 11, 10, 13, 4, 14, 13, 11, 13, 14, 7, 0, 1, 8, 7, 11, 8, 0, 7, 4, 13, 3, 5, 0, 8, 8, 4, 5, 14, 8, 11, 4, 8, 14], "uvs": [0.05671, 0.91907, 0.13491, 0.11212, 0.51901, 0, 0.91691, 0.11212, 0.98361, 0.98144, 0.05901, 1, 0.34421, 0.1667, 0.33731, 0.54873, 0.31661, 0.8489, 0.51671, 0.13941, 0.52361, 0.46297, 0.51441, 0.74754, 0.68921, 0.17059, 0.70761, 0.50585, 0.71681, 0.79432], "vertices": [-6.83, 89.02, 88.39, 73.38, 101.62, -3.44, 88.39, -83.02, -14.19, -96.36, -16.38, 88.56, 81.95, 31.52, 36.87, 32.9, 1.45, 37.04, 85.17, -2.98, 46.99, -4.36, 13.41, -2.52, 81.49, -37.48, 41.93, -41.16, 7.89, -43], "width": 200, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "type": "mesh", "hull": 6, "height": 118}}, "fx2": {"fx3": {"triangles": [0, 11, 1, 1, 11, 12, 11, 10, 12, 12, 10, 9, 1, 12, 2, 2, 12, 13, 12, 9, 13, 13, 9, 8, 2, 13, 3, 3, 13, 14, 13, 8, 14, 14, 8, 7, 3, 14, 4, 4, 14, 5, 14, 7, 5, 5, 7, 6], "path": "fx1", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0.5, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0, 1, 0.5, 0.75, 0.5, 0.5, 0.5, 0.25, 0.5], "vertices": [13.76, -72.66, -36.24, -37.41, -86.24, -72.66, -136.24, -37.6, -186.24, -72.66, -157.44, 6.84, -186.24, 86.34, -136.24, 51.27, -86.24, 86.34, -36.24, 51.09, 13.76, 86.34, 42.07, 6.84, -64.55, 6.84, -86.24, 6.84, -107.44, 6.84], "width": 200, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 0], "type": "mesh", "hull": 12, "height": 159}, "fx2": {"triangles": [0, 11, 1, 1, 11, 12, 11, 10, 12, 12, 10, 9, 1, 12, 2, 2, 12, 13, 12, 9, 13, 13, 9, 8, 2, 13, 3, 3, 13, 14, 13, 8, 14, 14, 8, 7, 3, 14, 4, 4, 14, 5, 14, 7, 5, 5, 7, 6], "path": "fx1", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0.5, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0, 1, 0.5, 0.75, 0.5, 0.5, 0.5, 0.25, 0.5], "vertices": [54.43, -76.14, 9.73, -61.9, -45.57, -76.14, -95.57, -102.28, -145.57, -76.14, -124.05, 32.49, -145.57, 82.86, -95.57, 56.72, -45.57, 82.86, 9.73, 97.1, 54.43, 82.86, 80.58, 16.94, 9.73, 17.6, -44.24, 30.18, -95.57, -22.78], "width": 200, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 0], "type": "mesh", "hull": 12, "height": 159}, "fx4": {"triangles": [0, 11, 1, 1, 11, 12, 11, 10, 12, 12, 10, 9, 1, 12, 2, 2, 12, 13, 12, 9, 13, 13, 9, 8, 2, 13, 3, 3, 13, 14, 13, 8, 14, 14, 8, 7, 3, 14, 4, 4, 14, 5, 14, 7, 5, 5, 7, 6], "path": "fx1", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0.5, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0, 1, 0.5, 0.75, 0.5, 0.5, 0.5, 0.25, 0.5], "vertices": [30.63, -69.19, -19.37, -37.05, -69.37, -25.68, -119.37, -36.37, -169.37, -69.19, -212.4, 10.31, -169.37, 89.81, -119.37, 56.99, -69.37, 46.3, -19.37, 57.67, 30.63, 89.81, 73.66, 10.31, -19.37, 10.31, -69.37, 10.31, -119.37, 10.31], "width": 200, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 0], "type": "mesh", "hull": 12, "height": 159}, "fx1": {"triangles": [0, 11, 1, 1, 11, 12, 11, 10, 12, 12, 10, 9, 1, 12, 2, 2, 12, 13, 12, 9, 13, 13, 9, 8, 2, 13, 3, 3, 13, 14, 13, 8, 14, 14, 8, 7, 3, 14, 4, 4, 14, 5, 14, 7, 5, 5, 7, 6], "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0.5, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0, 1, 0.5, 0.75, 0.5, 0.5, 0.5, 0.25, 0.5], "vertices": [32.61, -76.14, -17.39, -76.14, -67.39, -76.14, -117.39, -76.14, -167.39, -76.14, -167.39, 3.36, -167.39, 82.86, -117.39, 82.86, -67.39, 82.86, -17.39, 82.86, 32.61, 82.86, 32.61, 3.36, -17.39, 3.36, -67.39, 3.36, -117.39, 3.36], "width": 200, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 0], "type": "mesh", "hull": 12, "height": 159}}, "txt2": {"txt2": {"rotation": 79.7, "x": 14, "width": 26, "y": -0.68, "height": 97}}, "bg": {"bg": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [161.61, -53.14, -167.39, -53.14, -167.39, 60.86, 161.61, 60.86], "width": 329, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 114}}, "txt3": {"txt1": {"triangles": [0, 3, 2, 1, 0, 2], "uvs": [0.59274, 0.01025, 0.58443, 1, 1, 1, 1, 0], "vertices": [-49.54, 41.39, -51.53, -43.72, 47.8, -43.72, 47.8, 42.28], "width": 239, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 86}}, "txt1": {"txt1": {"triangles": [1, 2, 3, 0, 1, 3], "uvs": [0.58503, 0, 0.58088, 1, 0, 1, 0, 0], "vertices": [56.08, 40.66, 55.09, -45.34, -83.74, -45.34, -83.74, 40.66], "width": 239, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 86}}, "fx1": {"fx3": {"triangles": [0, 11, 1, 1, 11, 12, 11, 10, 12, 12, 10, 9, 1, 12, 2, 2, 12, 13, 12, 9, 13, 13, 9, 8, 2, 13, 3, 3, 13, 14, 13, 8, 14, 14, 8, 7, 3, 14, 4, 4, 14, 5, 14, 7, 5, 5, 7, 6], "path": "fx1", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0.5, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0, 1, 0.5, 0.75, 0.5, 0.5, 0.5, 0.25, 0.5], "vertices": [13.76, -72.66, -36.24, -37.41, -86.24, -72.66, -136.24, -37.6, -186.24, -72.66, -157.44, 6.84, -186.24, 86.34, -136.24, 51.27, -86.24, 86.34, -36.24, 51.09, 13.76, 86.34, 42.07, 6.84, -64.55, 6.84, -86.24, 6.84, -107.44, 6.84], "width": 200, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 0], "type": "mesh", "hull": 12, "height": 159}, "fx2": {"triangles": [0, 11, 1, 1, 11, 12, 11, 10, 12, 12, 10, 9, 1, 12, 2, 2, 12, 13, 12, 9, 13, 13, 9, 8, 2, 13, 3, 3, 13, 14, 13, 8, 14, 14, 8, 7, 3, 14, 4, 4, 14, 5, 14, 7, 5, 5, 7, 6], "path": "fx1", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0.5, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0, 1, 0.5, 0.75, 0.5, 0.5, 0.5, 0.25, 0.5], "vertices": [54.43, -76.14, 9.73, -61.9, -45.57, -76.14, -95.57, -102.28, -145.57, -76.14, -124.05, 32.49, -145.57, 82.86, -95.57, 56.72, -45.57, 82.86, 9.73, 97.1, 54.43, 82.86, 80.58, 16.94, 9.73, 17.6, -44.24, 30.18, -95.57, -22.78], "width": 200, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 0], "type": "mesh", "hull": 12, "height": 159}, "fx4": {"triangles": [0, 11, 1, 1, 11, 12, 11, 10, 12, 12, 10, 9, 1, 12, 2, 2, 12, 13, 12, 9, 13, 13, 9, 8, 2, 13, 3, 3, 13, 14, 13, 8, 14, 14, 8, 7, 3, 14, 4, 4, 14, 5, 14, 7, 5, 5, 7, 6], "path": "fx1", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0.5, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0, 1, 0.5, 0.75, 0.5, 0.5, 0.5, 0.25, 0.5], "vertices": [30.63, -69.19, -19.37, -37.05, -69.37, -25.68, -119.37, -36.37, -169.37, -69.19, -212.4, 10.31, -169.37, 89.81, -119.37, 56.99, -69.37, 46.3, -19.37, 57.67, 30.63, 89.81, 73.66, 10.31, -19.37, 10.31, -69.37, 10.31, -119.37, 10.31], "width": 200, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 0], "type": "mesh", "hull": 12, "height": 159}, "fx1": {"triangles": [0, 11, 1, 1, 11, 12, 11, 10, 12, 12, 10, 9, 1, 12, 2, 2, 12, 13, 12, 9, 13, 13, 9, 8, 2, 13, 3, 3, 13, 14, 13, 8, 14, 14, 8, 7, 3, 14, 4, 4, 14, 5, 14, 7, 5, 5, 7, 6], "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0.5, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0, 1, 0.5, 0.75, 0.5, 0.5, 0.5, 0.25, 0.5], "vertices": [32.61, -76.14, -17.39, -76.14, -67.39, -76.14, -117.39, -76.14, -167.39, -76.14, -167.39, 3.36, -167.39, 82.86, -117.39, 82.86, -67.39, 82.86, -17.39, 82.86, 32.61, 82.86, 32.61, 3.36, -17.39, 3.36, -67.39, 3.36, -117.39, 3.36], "width": 200, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 0], "type": "mesh", "hull": 12, "height": 159}}}}, "skeleton": {"images": "./images/", "width": 424.81, "spine": "3.7.94", "audio": "C:/Users/<USER>/Desktop/tong hop/amin/logo zeus", "hash": "dumssfBP/EBlMoIRsc2v8hVqnvM", "height": 159.06}, "slots": [{"attachment": "bg", "name": "bg", "bone": "bone"}, {"attachment": "1", "name": "1", "bone": "1"}, {"attachment": "fx4", "blend": "additive", "name": "fx1", "bone": "bone2"}, {"attachment": "fx4", "blend": "additive", "name": "fx2", "bone": "bone3"}, {"attachment": "txt1", "name": "txt1", "bone": "txt1"}, {"attachment": "txt1", "name": "txt3", "bone": "txt3"}, {"name": "txt2", "bone": "txt2"}], "bones": [{"name": "root"}, {"parent": "root", "name": "bone"}, {"parent": "root", "rotation": 90, "name": "1", "length": 90.67, "x": -7.03, "y": -11.76}, {"parent": "root", "name": "txt1", "x": -43.65, "y": 13.2}, {"parent": "root", "name": "txt3", "x": 56.17, "y": 10.82}, {"parent": "root", "rotation": -79.7, "name": "txt2", "length": 32.42, "x": 12.78, "y": 30.27}, {"parent": "root", "name": "bone2"}, {"scaleX": -1, "parent": "root", "name": "bone3"}], "animations": {"animation": {"slots": {"fx2": {"color": [{"color": "ffffffff", "time": 2.1667}, {"color": "ffffff00", "time": 3.3}], "attachment": [{"name": null, "time": 0}, {"name": "fx4", "time": 1.5}, {"name": "fx3", "time": 1.5667}, {"name": "fx2", "time": 1.6667}, {"name": "fx1", "time": 1.7333}, {"name": "fx1", "time": 1.8}, {"name": "fx4", "time": 1.8667}, {"name": "fx3", "time": 1.9667}, {"name": "fx2", "time": 2.0333}, {"name": "fx1", "time": 2.1}, {"name": "fx1", "time": 2.1667}]}, "txt2": {"color": [{"color": "ffffff00", "time": 1.3333}, {"color": "ffffffff", "time": 1.5}], "attachment": [{"name": "txt2", "time": 1.3333}]}, "bg": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffffff", "time": 0.6667}]}, "txt3": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.8333}, {"color": "ffffffff", "time": 1.1667}]}, "txt1": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.6667}, {"color": "ffffffff", "time": 1}]}, "fx1": {"color": [{"color": "ffffffff", "time": 2.1667}, {"color": "ffffff00", "time": 3.3}], "attachment": [{"name": null, "time": 0}, {"name": "fx4", "time": 1.5}, {"name": "fx3", "time": 1.5667}, {"name": "fx2", "time": 1.6667}, {"name": "fx1", "time": 1.7333}, {"name": "fx1", "time": 1.8}, {"name": "fx4", "time": 1.8667}, {"name": "fx3", "time": 1.9667}, {"name": "fx2", "time": 2.0333}, {"name": "fx1", "time": 2.1}, {"name": "fx1", "time": 2.1667}]}}, "bones": {"1": {"scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0.944, "y": 0.944, "time": 0.7667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 1.158, "y": 1, "time": 1.2333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 1.158, "y": 1, "time": 2.0667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 2.4667}, {"curve": [0.25, 0, 0.75, 1], "x": 1.158, "y": 1, "time": 2.9}, {"x": 1, "y": 1, "time": 3.3}]}, "txt2": {"translate": [{"x": -16.06, "y": 187.11, "time": 1.3333}, {"x": 0, "y": 0, "time": 1.5}]}, "txt3": {"scale": [{"curve": "stepped", "x": 2, "y": 2, "time": 0}, {"x": 2, "y": 2, "time": 0.8333}, {"x": 1, "y": 1, "time": 1.1667}, {"x": 1.109, "y": 1.109, "time": 1.2333}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.3333}, {"x": 1, "y": 1, "time": 2.9}, {"x": 1.113, "y": 1.113, "time": 3}, {"x": 1, "y": 1, "time": 3.1}, {"x": 1.113, "y": 1.113, "time": 3.2}, {"x": 1, "y": 1, "time": 3.3}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 1.5}, {"x": 28.35, "y": 0, "time": 1.6667}, {"x": 12.28, "y": 0, "time": 1.7333}]}, "txt1": {"scale": [{"curve": "stepped", "x": 2, "y": 2, "time": 0}, {"x": 2, "y": 2, "time": 0.6667}, {"x": 1, "y": 1, "time": 1}, {"x": 1.109, "y": 1.109, "time": 1.0667}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.1667}, {"x": 1, "y": 1, "time": 2.6}, {"x": 1.113, "y": 1.113, "time": 2.6667}, {"x": 1, "y": 1, "time": 2.7333}, {"x": 1.113, "y": 1.113, "time": 2.8}, {"x": 1, "y": 1, "time": 2.8667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 1.5}, {"x": -7.56, "y": 0, "time": 1.6667}, {"x": 2.84, "y": 0, "time": 1.7333}]}, "bone": {"scale": [{"x": 0, "y": 0, "time": 0}, {"x": 1, "y": 1, "time": 0.3333}, {"x": 0.944, "y": 0.944, "time": 0.4333}, {"x": 1, "y": 1, "time": 0.5}]}}}}}