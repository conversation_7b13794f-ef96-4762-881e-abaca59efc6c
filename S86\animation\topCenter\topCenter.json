{"skeleton": {"hash": "dIdhlT3ZbNyVmus+VwP/vIe98X0", "spine": "3.8.75", "x": -1020.3, "y": -49.46, "width": 2131.1, "height": 84.46, "images": "C:/Users/<USER>/Desktop/UI GAME/DUCUI/out/88zon.icu/spines/74_skeleton/png", "audio": ""}, "bones": [{"name": "root"}, {"name": "f3", "parent": "root", "length": 8.49, "rotation": -0.14, "x": -85.8, "y": 0.69}, {"name": "f4", "parent": "f3", "length": 8.49, "x": 8.49}, {"name": "f5", "parent": "f4", "length": 8.49, "x": 8.49}, {"name": "f6", "parent": "f5", "length": 8.49, "x": 8.49}, {"name": "f7", "parent": "f6", "length": 8.49, "x": 8.49}, {"name": "f8", "parent": "f7", "length": 8.49, "x": 8.49}, {"name": "f9", "parent": "f8", "length": 8.49, "x": 8.49}, {"name": "f10", "parent": "f9", "length": 8.49, "x": 8.49}, {"name": "f11", "parent": "f10", "length": 8.49, "x": 8.49}, {"name": "f12", "parent": "f11", "length": 8.49, "x": 8.49}, {"name": "f13", "parent": "f12", "length": 8.49, "x": 8.49}, {"name": "f14", "parent": "f13", "length": 8.49, "x": 8.49}, {"name": "f15", "parent": "f14", "length": 8.49, "x": 8.49}, {"name": "f16", "parent": "f15", "length": 8.49, "x": 8.49}, {"name": "f17", "parent": "f16", "length": 8.49, "x": 8.49}, {"name": "f18", "parent": "f17", "length": 8.49, "x": 8.49}, {"name": "f19", "parent": "f18", "length": 8.49, "x": 8.49}, {"name": "f20", "parent": "f19", "length": 8.49, "x": 8.49}, {"name": "f21", "parent": "f20", "length": 8.49, "x": 8.49}, {"name": "f22", "parent": "f21", "length": 8.49, "x": 8.49}, {"name": "bone", "parent": "root", "length": 7.28, "rotation": -178.66, "x": 92.55, "y": -36.45}, {"name": "bone2", "parent": "bone", "length": 7.28, "x": 7.28}, {"name": "bone3", "parent": "bone2", "length": 7.28, "x": 7.28}, {"name": "bone4", "parent": "bone3", "length": 7.28, "x": 7.28}, {"name": "bone5", "parent": "bone4", "length": 7.28, "x": 7.28}, {"name": "bone6", "parent": "bone5", "length": 7.28, "x": 7.28}, {"name": "bone7", "parent": "bone6", "length": 7.28, "x": 7.28}, {"name": "bone8", "parent": "bone7", "length": 7.28, "x": 7.28}, {"name": "bone9", "parent": "bone8", "length": 7.28, "x": 7.28}, {"name": "bone10", "parent": "bone9", "length": 7.28, "x": 7.28}, {"name": "bone11", "parent": "bone10", "length": 7.28, "x": 7.28}, {"name": "bone12", "parent": "bone11", "length": 7.28, "x": 7.28}, {"name": "bone13", "parent": "bone12", "length": 7.28, "x": 7.28}, {"name": "bone14", "parent": "bone13", "length": 7.28, "x": 7.28}, {"name": "bone15", "parent": "bone14", "length": 7.28, "x": 7.28}, {"name": "bone16", "parent": "bone15", "length": 7.28, "x": 7.28}, {"name": "bone17", "parent": "bone16", "length": 7.28, "x": 7.28}, {"name": "bone18", "parent": "bone17", "length": 7.28, "x": 7.28}, {"name": "bone19", "parent": "bone18", "length": 7.28, "x": 7.28}, {"name": "bone20", "parent": "bone19", "length": 7.28, "x": 7.28}, {"name": "bone22", "parent": "root"}, {"name": "f1", "parent": "bone22", "x": 163.63, "y": 45.75}, {"name": "bone21", "parent": "bone22", "x": 174.42, "y": 5.11}, {"name": "bone23", "parent": "root", "scaleX": -1}, {"name": "f2", "parent": "bone23", "x": 163.63, "y": 45.75}, {"name": "bone24", "parent": "bone23", "x": 174.42, "y": 5.11}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "root", "bone": "root", "attachment": "root"}, {"name": "root3", "bone": "root", "attachment": "root3"}, {"name": "f1", "bone": "f1", "color": "ffffff73", "attachment": "f1", "blend": "additive"}, {"name": "f5", "bone": "f2", "color": "ffffff73", "attachment": "f1", "blend": "additive"}, {"name": "f2", "bone": "bone21", "attachment": "f2", "blend": "additive"}, {"name": "f6", "bone": "bone24", "attachment": "f2", "blend": "additive"}, {"name": "f3", "bone": "f22", "attachment": "f3", "blend": "additive"}, {"name": "f4", "bone": "bone20", "attachment": "f3", "blend": "additive"}], "path": [{"name": "root", "bones": ["f3", "f4", "f5", "f6", "f7", "f8", "f9", "f10", "f11", "f12", "f13", "f14", "f15", "f16", "f17", "f18", "f19", "f20", "f21", "f22"], "target": "root", "position": -0.1502}, {"name": "root1", "order": 1, "bones": ["bone", "bone2", "bone3", "bone4", "bone5", "bone6", "bone7", "bone8", "bone9", "bone10", "bone11", "bone12", "bone13", "bone14", "bone15", "bone16", "bone17", "bone18", "bone19", "bone20"], "target": "root", "position": 1.1}], "skins": [{"name": "default", "attachments": {"bg": {"bg": {"y": 0.5, "scaleX": 1.22, "width": 1280, "height": 69}}, "f1": {"f1": {"type": "mesh", "uvs": [1, 1, 0.87542, 1, 0.71088, 1, 0.52303, 1, 0.35027, 1, 0.17201, 1, 0, 1, 0, 0, 0.17476, 0, 0.34478, 0, 0.52989, 0, 0.72597, 0, 0.87131, 0, 1, 0], "triangles": [5, 6, 7, 8, 5, 7, 3, 4, 9, 5, 8, 9, 4, 5, 9, 10, 3, 9, 2, 10, 11, 3, 10, 2, 1, 12, 13, 0, 1, 13, 11, 12, 1, 2, 11, 1], "vertices": [287.9, -38.96, 218.76, -38.96, 127.44, -38.96, 23.18, -38.96, -72.71, -38.96, -171.64, -38.96, -267.1, -38.96, -267.1, 46.04, -170.11, 46.04, -75.75, 46.04, 26.99, 46.04, 135.81, 46.04, 216.47, 46.04, 287.9, 46.04], "hull": 14, "edges": [12, 14, 0, 26, 14, 16, 10, 12, 16, 18, 8, 10, 18, 20, 6, 8, 20, 22, 4, 6, 22, 24, 24, 26, 0, 2, 2, 4], "width": 555, "height": 85}}, "f2": {"f2": {"type": "mesh", "uvs": [1, 1, 0.81529, 1, 0.51496, 1, 0.19907, 1, 0, 1, 0, 0, 0.19189, 0, 0.50898, 0, 0.80572, 0, 1, 0], "triangles": [4, 5, 6, 3, 6, 7, 4, 6, 3, 2, 7, 8, 3, 7, 2, 1, 8, 9, 0, 1, 9, 2, 8, 1], "vertices": [307.8, -39.11, 190.32, -39.11, -0.69, -39.11, -201.59, -39.11, -328.2, -39.11, -328.2, 29.89, -206.16, 29.89, -4.49, 29.89, 184.24, 29.89, 307.8, 29.89], "hull": 10, "edges": [8, 10, 0, 18, 10, 12, 6, 8, 12, 14, 4, 6, 14, 16, 16, 18, 0, 2, 2, 4], "width": 636, "height": 69}}, "f3": {"f3": {"type": "mesh", "uvs": [0, 0.49181, 0, 0.31041, 0.12831, 0.24243, 0.20993, 0.22915, 0.28022, 0.21771, 0.36635, 0.22661, 0.45975, 0.23625, 0.55807, 0.23258, 0.62547, 0.23007, 0.75149, 0.21771, 0.80485, 0.2207, 0.86197, 0.22389, 0.96554, 0.20535, 0.96727, 0.73065, 0.86369, 0.75537, 0.74803, 0.74301, 0.62202, 0.73065, 0.46665, 0.73065, 0.30093, 0.72447, 0.14039, 0.71211, 0, 0.64413, 0.05619, 0.37081, 0.05506, 0.59411, 0.12651, 0.37081, 0.13331, 0.61441, 0.28074, 0.34239, 0.29095, 0.61847, 0.45879, 0.40329, 0.46333, 0.63471, 0.6221, 0.33833, 0.62323, 0.62253, 0.74344, 0.34239, 0.74458, 0.60629, 0.86819, 0.34239, 0.86365, 0.59817, 0.92344, 0.31247, 0.93283, 0.67204, 0.92272, 0.51362, 0.87497, 0.49195, 0.75678, 0.49128, 0.81172, 0.44023, 0.80564, 0.67987, 0.69114, 0.33684, 0.68867, 0.49519, 0.68982, 0.60219, 0.62586, 0.48201, 0.56053, 0.38322, 0.56074, 0.48595, 0.56153, 0.63147, 0.45833, 0.50681, 0.37444, 0.44219, 0.37442, 0.62195, 0.28102, 0.48881, 0.20921, 0.50131, 0.21035, 0.62115, 0.13832, 0.47958, 0.06793, 0.4921], "triangles": [35, 11, 12, 12, 37, 35, 12, 13, 37, 37, 13, 36, 14, 36, 13, 33, 11, 35, 10, 33, 9, 33, 10, 11, 37, 38, 33, 40, 33, 38, 35, 37, 33, 34, 38, 37, 34, 39, 38, 34, 37, 36, 41, 34, 36, 14, 41, 36, 15, 41, 14, 42, 8, 9, 31, 42, 9, 42, 43, 45, 9, 40, 31, 33, 40, 9, 39, 31, 40, 31, 43, 42, 31, 39, 43, 38, 39, 40, 32, 44, 43, 39, 32, 43, 34, 32, 39, 44, 45, 43, 41, 32, 34, 15, 32, 41, 44, 32, 15, 15, 16, 44, 29, 7, 8, 29, 8, 42, 46, 7, 29, 45, 29, 42, 46, 29, 45, 30, 47, 45, 44, 30, 45, 48, 47, 30, 16, 48, 30, 17, 48, 16, 16, 30, 44, 6, 7, 46, 27, 5, 6, 27, 6, 46, 25, 5, 27, 50, 25, 27, 47, 46, 45, 27, 46, 47, 49, 50, 27, 49, 27, 47, 50, 49, 51, 49, 47, 48, 28, 51, 49, 48, 28, 49, 17, 28, 48, 51, 28, 17, 18, 51, 17, 25, 4, 5, 3, 4, 25, 50, 52, 25, 25, 52, 53, 52, 51, 26, 50, 51, 52, 53, 52, 26, 26, 54, 53, 18, 26, 51, 54, 26, 18, 23, 21, 2, 23, 3, 25, 3, 23, 2, 23, 25, 53, 23, 53, 55, 56, 23, 55, 24, 56, 55, 24, 55, 53, 54, 24, 53, 19, 24, 54, 22, 24, 19, 19, 54, 18, 2, 21, 1, 0, 1, 21, 56, 21, 23, 0, 21, 56, 22, 0, 56, 22, 56, 24, 20, 0, 22, 20, 22, 19], "vertices": [2, 1, -3.19, -0.29, 0.99702, 2, -11.85, -0.29, 0.00298, 2, 1, -3.22, 8.78, 0.99359, 2, -11.7, 8.78, 0.00641, 5, 1, 19.74, 12.23, 0.00929, 2, 11.25, 12.23, 0.23522, 3, 2.77, 12.23, 0.59951, 4, -5.72, 12.23, 0.14681, 5, -14.2, 12.23, 0.00916, 8, 1, 34.85, 12.93, 0.0043, 2, 26.2, 12.93, 0.10884, 3, 17.44, 12.93, 0.27739, 4, 8.59, 12.93, 0.08433, 5, 0.19, 12.93, 0.12089, 6, -8.13, 12.93, 0.25969, 7, -16.52, 12.93, 0.13065, 8, -24.96, 12.93, 0.01391, 5, 4, 21.47, 13.54, 0.03052, 5, 12.98, 13.54, 0.21712, 6, 4.5, 13.54, 0.48333, 7, -3.99, 13.54, 0.24315, 8, -12.48, 13.54, 0.02589, 9, 4, 36.61, 13.13, 0.01588, 5, 28.21, 13.13, 0.11295, 6, 19.89, 13.13, 0.25145, 7, 11.5, 13.13, 0.1265, 8, 3.06, 13.13, 0.02985, 9, -5.44, 13.13, 0.14938, 10, -13.98, 13.13, 0.22887, 11, -22.57, 13.13, 0.07967, 12, -31.19, 13.13, 0.00545, 5, 8, 19.66, 12.69, 0.03415, 9, 11.18, 12.69, 0.31136, 10, 2.69, 12.69, 0.47706, 11, -5.8, 12.69, 0.16606, 12, -14.28, 12.69, 0.01137, 9, 8, 37.55, 12.92, 0.01389, 9, 29.05, 12.91, 0.12664, 10, 20.51, 12.91, 0.19403, 11, 11.92, 12.91, 0.07051, 12, 3.3, 12.91, 0.08539, 13, -5.35, 12.91, 0.26507, 14, -13.83, 12.91, 0.2223, 15, -22.3, 12.91, 0.02122, 16, -30.79, 12.92, 0.00096, 6, 11, 23.87, 13.07, 0.005, 12, 15.38, 13.07, 0.13613, 13, 6.89, 13.07, 0.44678, 14, -1.59, 13.07, 0.3747, 15, -10.08, 13.07, 0.03576, 16, -18.57, 13.07, 0.00163, 5, 14, 20.96, 13.74, 0.0399, 15, 12.48, 13.74, 0.22239, 16, 3.99, 13.74, 0.48107, 17, -4.5, 13.74, 0.23108, 18, -12.98, 13.74, 0.02556, 7, 14, 30.53, 13.62, 0.02063, 15, 22.05, 13.62, 0.11497, 16, 13.56, 13.62, 0.25094, 17, 5.06, 13.62, 0.2014, 18, -3.44, 13.62, 0.24678, 19, -11.94, 13.62, 0.14277, 20, -20.44, 13.62, 0.02252, 5, 16, 23.77, 13.48, 0.00466, 17, 15.28, 13.48, 0.16964, 18, 6.79, 13.48, 0.48352, 19, -1.69, 13.48, 0.29557, 20, -10.18, 13.48, 0.04662, 3, 18, 25.33, 14.45, 0.00702, 19, 16.85, 14.45, 0.20878, 20, 8.36, 14.45, 0.7842, 3, 18, 25.71, -11.81, 0.00514, 19, 17.22, -11.81, 0.12729, 20, 8.73, -11.81, 0.86757, 5, 16, 24.14, -13.09, 0.0027, 17, 15.65, -13.09, 0.1466, 18, 7.17, -13.09, 0.50603, 19, -1.32, -13.09, 0.31162, 20, -9.8, -13.09, 0.03305, 5, 14, 20.41, -12.52, 0.0345, 15, 11.92, -12.52, 0.23847, 16, 3.44, -12.52, 0.51939, 17, -5.05, -12.52, 0.19466, 18, -13.54, -12.52, 0.01299, 5, 11, 23.31, -11.96, 0.00324, 12, 14.82, -11.96, 0.14431, 13, 6.34, -11.96, 0.47798, 14, -2.15, -11.96, 0.34486, 15, -10.64, -11.96, 0.0296, 5, 8, 20.96, -12.03, 0.01406, 9, 12.47, -12.03, 0.25699, 10, 3.99, -12.03, 0.51634, 11, -4.5, -12.03, 0.19941, 12, -12.99, -12.03, 0.0132, 5, 5, 16.75, -11.79, 0.07305, 6, 8.27, -11.79, 0.45993, 7, -0.22, -11.79, 0.39213, 8, -8.71, -11.79, 0.07309, 9, -17.19, -11.79, 0.0018, 4, 2, 13.48, -11.25, 0.1572, 3, 4.99, -11.25, 0.58475, 4, -3.5, -11.25, 0.23395, 5, -11.98, -11.25, 0.02411, 2, 1, -3.18, -7.91, 0.99991, 2, -11.66, -7.91, 9e-05, 3, 1, 6.85, 5.78, 0.66075, 2, -1.64, 5.78, 0.32158, 3, -10.12, 5.78, 0.01768, 3, 1, 6.67, -5.38, 0.71333, 2, -1.81, -5.38, 0.27901, 3, -10.3, -5.38, 0.00766, 5, 1, 19.43, 5.81, 0.00327, 2, 10.95, 5.81, 0.22725, 3, 2.46, 5.81, 0.7071, 4, -6.03, 5.81, 0.06127, 5, -14.51, 5.81, 0.00111, 4, 2, 12.2, -6.36, 0.18679, 3, 3.71, -6.36, 0.67845, 4, -4.78, -6.36, 0.12986, 5, -13.26, -6.36, 0.0049, 5, 4, 21.58, 7.3, 0.01362, 5, 13.09, 7.3, 0.17167, 6, 4.61, 7.3, 0.60519, 7, -3.88, 7.3, 0.19717, 8, -12.37, 7.3, 0.01235, 4, 5, 14.95, -6.5, 0.08161, 6, 6.47, -6.5, 0.59985, 7, -2.02, -6.5, 0.30004, 8, -10.51, -6.5, 0.0185, 5, 8, 19.51, 4.33, 0.00407, 9, 11.03, 4.33, 0.22104, 10, 2.54, 4.33, 0.72543, 11, -5.95, 4.33, 0.0489, 12, -14.43, 4.33, 0.00057, 5, 8, 20.35, -7.23, 0.0095, 9, 11.87, -7.23, 0.25399, 10, 3.38, -7.23, 0.5977, 11, -5.11, -7.23, 0.13437, 12, -13.59, -7.23, 0.00445, 5, 11, 23.28, 7.65, 0.00223, 12, 14.79, 7.65, 0.11396, 13, 6.3, 7.65, 0.54752, 14, -2.18, 7.65, 0.32251, 15, -10.67, 7.65, 0.01379, 5, 11, 23.51, -6.55, 0.00038, 12, 15.03, -6.55, 0.09456, 13, 6.54, -6.55, 0.5671, 14, -1.95, -6.55, 0.32456, 15, -10.43, -6.55, 0.0134, 5, 14, 19.54, 7.5, 0.03333, 15, 11.05, 7.5, 0.25483, 16, 2.57, 7.5, 0.59473, 17, -5.92, 7.5, 0.11186, 18, -14.41, 7.5, 0.00525, 5, 14, 19.77, -5.69, 0.01809, 15, 11.29, -5.69, 0.17829, 16, 2.8, -5.69, 0.72466, 17, -5.69, -5.69, 0.0781, 18, -14.17, -5.69, 0.00086, 4, 17, 16.41, 7.56, 0.08911, 18, 7.92, 7.56, 0.50741, 19, -0.56, 7.56, 0.37366, 20, -9.05, 7.56, 0.02982, 4, 17, 15.63, -5.23, 0.05803, 18, 7.14, -5.23, 0.64586, 19, -1.34, -5.23, 0.2918, 20, -9.83, -5.23, 0.0043, 4, 17, 26.33, 9.08, 0.03868, 18, 17.82, 9.08, 0.22405, 19, 9.32, 9.08, 0.27296, 20, 0.83, 9.08, 0.46432, 4, 17, 28.06, -8.89, 0.01986, 18, 19.55, -8.89, 0.21597, 19, 11.05, -8.89, 0.18503, 20, 2.55, -8.89, 0.57915, 4, 17, 26.22, -0.98, 0.0312, 18, 17.72, -0.98, 0.25758, 19, 9.22, -0.98, 0.23165, 20, 0.72, -0.98, 0.47957, 7, 14, 43.13, 0.09, 0.00356, 15, 34.65, 0.09, 0.03507, 16, 26.16, 0.09, 0.14254, 17, 17.66, 0.09, 0.05392, 18, 9.16, 0.08, 0.2653, 19, 0.66, 0.08, 0.21918, 20, -7.84, 0.08, 0.28044, 7, 14, 21.94, 0.07, 0.02134, 15, 13.47, 0.07, 0.1857, 16, 4.98, 0.07, 0.60747, 17, -3.52, 0.07, 0.0905, 18, -12.03, 0.07, 0.05391, 19, -20.53, 0.07, 0.03805, 20, -29.03, 0.07, 0.00304, 7, 14, 31.79, 2.64, 0.0115, 15, 23.31, 2.64, 0.10067, 16, 14.82, 2.64, 0.33324, 17, 6.32, 2.64, 0.08486, 18, -2.19, 2.64, 0.2395, 19, -10.69, 2.64, 0.17888, 20, -19.18, 2.64, 0.05135, 7, 14, 30.73, -9.34, 0.01731, 15, 22.25, -9.34, 0.11966, 16, 13.76, -9.34, 0.26077, 17, 5.26, -9.34, 0.13167, 18, -3.24, -9.34, 0.32027, 19, -11.75, -9.34, 0.14652, 20, -20.24, -9.34, 0.00379, 8, 11, 35.92, 7.76, 0.00106, 12, 27.3, 7.76, 0.05001, 13, 18.64, 7.76, 0.23299, 14, 10.17, 7.76, 0.16008, 15, 1.69, 7.76, 0.15147, 16, -6.8, 7.76, 0.33786, 17, -15.3, 7.76, 0.06354, 18, -23.8, 7.76, 0.00298, 10, 11, 35.5, -0.16, 0.0006, 12, 26.88, -0.16, 0.05234, 13, 18.22, -0.16, 0.28384, 14, 9.75, -0.16, 0.17497, 15, 1.27, -0.16, 0.09823, 16, -7.22, -0.16, 0.29878, 17, -15.72, -0.16, 0.04451, 18, -24.22, -0.16, 0.02652, 19, -32.73, -0.16, 0.01872, 20, -41.22, -0.16, 0.00149, 10, 11, 35.72, -5.51, 0.00021, 12, 27.09, -5.51, 0.04351, 13, 18.44, -5.51, 0.25806, 14, 9.97, -5.51, 0.15789, 15, 1.49, -5.51, 0.1035, 16, -7, -5.51, 0.38881, 17, -15.5, -5.51, 0.04309, 18, -24.01, -5.51, 0.00298, 19, -32.51, -5.51, 0.0018, 20, -41, -5.51, 0.00014, 13, 8, 49.78, 0.48, 0.00102, 9, 41.28, 0.47, 0.0555, 10, 32.74, 0.47, 0.18214, 11, 24.15, 0.47, 0.01271, 12, 15.53, 0.47, 0.04262, 13, 6.88, 0.47, 0.2346, 14, -1.6, 0.47, 0.14267, 15, -10.07, 0.47, 0.06697, 16, -18.56, 0.47, 0.20053, 17, -27.06, 0.47, 0.02987, 18, -35.57, 0.47, 0.0178, 19, -44.07, 0.47, 0.01256, 20, -52.57, 0.48, 0.001, 13, 8, 38, 5.38, 0.00169, 9, 29.51, 5.38, 0.09185, 10, 20.96, 5.38, 0.30145, 11, 12.38, 5.38, 0.02145, 12, 3.76, 5.38, 0.06082, 13, -4.89, 5.38, 0.29534, 14, -13.37, 5.38, 0.1746, 15, -21.85, 5.38, 0.01602, 16, -30.34, 5.38, 0.02818, 17, -38.84, 5.38, 0.0042, 18, -47.34, 5.39, 0.0025, 19, -55.84, 5.39, 0.00177, 20, -64.34, 5.39, 0.00014, 13, 8, 38.01, 0.25, 0.00304, 9, 29.51, 0.25, 0.12567, 10, 20.97, 0.25, 0.37683, 11, 12.38, 0.25, 0.03987, 12, 3.76, 0.25, 0.02664, 13, -4.89, 0.25, 0.1422, 14, -13.37, 0.25, 0.08648, 15, -21.84, 0.25, 0.04059, 16, -30.33, 0.25, 0.12155, 17, -38.83, 0.25, 0.01811, 18, -47.34, 0.25, 0.01079, 19, -55.84, 0.25, 0.00761, 20, -64.34, 0.25, 0.00061, 8, 8, 38.23, -7.03, 0.00366, 9, 29.73, -7.03, 0.09792, 10, 21.19, -7.03, 0.23043, 11, 12.61, -7.03, 0.05215, 12, 3.98, -7.03, 0.06177, 13, -4.67, -7.03, 0.34497, 14, -13.14, -7.03, 0.20023, 15, -21.62, -7.03, 0.00887, 8, 5, 44.66, -0.84, 0.00118, 6, 36.34, -0.84, 0.00866, 7, 27.95, -0.84, 0.00433, 8, 19.51, -0.84, 0.00664, 9, 11.01, -0.84, 0.23214, 10, 2.47, -0.84, 0.65953, 11, -6.12, -0.84, 0.08528, 12, -14.74, -0.84, 0.00224, 9, 4, 38.08, 2.35, 0.00581, 5, 29.68, 2.35, 0.07778, 6, 21.35, 2.35, 0.29186, 7, 12.97, 2.35, 0.101, 8, 4.52, 2.35, 0.00965, 9, -3.97, 2.35, 0.12183, 10, -12.52, 2.35, 0.34613, 11, -21.1, 2.35, 0.04475, 12, -29.72, 2.35, 0.00118, 8, 5, 29.69, -6.63, 0.04205, 6, 21.36, -6.63, 0.30906, 7, 12.98, -6.63, 0.15459, 8, 4.53, -6.63, 0.01403, 9, -3.96, -6.63, 0.1225, 10, -12.51, -6.63, 0.29217, 11, -21.09, -6.63, 0.06352, 12, -29.71, -6.63, 0.00208, 8, 1, 47.65, -0.02, 0.00011, 2, 39, -0.02, 0.00751, 3, 30.24, -0.02, 0.02336, 4, 21.39, -0.02, 0.00802, 5, 12.99, -0.02, 0.11858, 6, 4.66, -0.02, 0.58238, 7, -3.72, -0.02, 0.24486, 8, -12.17, -0.02, 0.01518, 8, 1, 34.74, -0.67, 0.00069, 2, 26.09, -0.67, 0.10093, 3, 17.33, -0.67, 0.34152, 4, 8.48, -0.68, 0.0528, 5, 0.08, -0.68, 0.0636, 6, -8.25, -0.68, 0.30449, 7, -16.63, -0.67, 0.12802, 8, -25.08, -0.67, 0.00794, 7, 2, 26.31, -6.67, 0.09446, 3, 17.55, -6.67, 0.34377, 4, 8.7, -6.67, 0.07175, 5, 0.3, -6.67, 0.04316, 6, -8.03, -6.67, 0.29186, 7, -16.41, -6.67, 0.14599, 8, -24.86, -6.67, 0.009, 8, 1, 21.9, 0.38, 0.19276, 2, 13.24, 0.38, 0.19383, 3, 4.49, 0.38, 0.38258, 4, -4.36, 0.38, 0.04166, 5, -12.76, 0.38, 0.02421, 6, -21.09, 0.38, 0.11404, 7, -29.47, 0.38, 0.04795, 8, -37.92, 0.38, 0.00297, 8, 1, 9.06, -0.28, 0.61558, 2, 0.41, -0.28, 0.28247, 3, -8.35, -0.28, 0.06747, 4, -17.2, -0.28, 0.00622, 5, -25.6, -0.28, 0.00362, 6, -33.93, -0.28, 0.01703, 7, -42.31, -0.27, 0.00716, 8, -50.76, -0.27, 0.00044], "hull": 21, "edges": [2, 4, 16, 18, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 18, 20, 20, 22, 12, 14, 14, 16, 8, 10, 10, 12, 4, 6, 6, 8, 2, 0, 0, 40], "width": 179, "height": 50}}, "f4": {"f3": {"type": "mesh", "uvs": [0, 0.49181, 0, 0.31041, 0.12831, 0.24243, 0.20993, 0.22915, 0.28022, 0.21771, 0.36635, 0.22661, 0.45975, 0.23625, 0.55807, 0.23258, 0.62547, 0.23007, 0.75149, 0.21771, 0.80485, 0.2207, 0.86197, 0.22389, 0.96554, 0.20535, 0.96727, 0.73065, 0.86369, 0.75537, 0.74803, 0.74301, 0.62202, 0.73065, 0.46665, 0.73065, 0.30093, 0.72447, 0.14039, 0.71211, 0, 0.64413, 0.05619, 0.37081, 0.05506, 0.59411, 0.12651, 0.37081, 0.13331, 0.61441, 0.28074, 0.34239, 0.29095, 0.61847, 0.45879, 0.40329, 0.46333, 0.63471, 0.6221, 0.33833, 0.62323, 0.62253, 0.74344, 0.34239, 0.74458, 0.60629, 0.86819, 0.34239, 0.86365, 0.59817, 0.92344, 0.31247, 0.93283, 0.67204, 0.92272, 0.51362, 0.87497, 0.49195, 0.75678, 0.49128, 0.81172, 0.44023, 0.80564, 0.67987, 0.69114, 0.33684, 0.68867, 0.49519, 0.68982, 0.60219, 0.62586, 0.48201, 0.56053, 0.38322, 0.56074, 0.48595, 0.56153, 0.63147, 0.45833, 0.50681, 0.37444, 0.44219, 0.37442, 0.62195, 0.28102, 0.48881, 0.20921, 0.50131, 0.21035, 0.62115, 0.13832, 0.47958, 0.06793, 0.4921], "triangles": [9, 40, 31, 33, 40, 9, 15, 41, 14, 33, 11, 35, 10, 33, 9, 33, 10, 11, 37, 38, 33, 40, 33, 38, 35, 37, 33, 34, 38, 37, 34, 39, 38, 34, 37, 36, 41, 34, 36, 14, 41, 36, 35, 11, 12, 12, 37, 35, 12, 13, 37, 37, 13, 36, 14, 36, 13, 31, 42, 9, 39, 31, 40, 31, 39, 43, 38, 39, 40, 39, 32, 43, 34, 32, 39, 41, 32, 34, 15, 32, 41, 44, 32, 15, 29, 7, 8, 29, 8, 42, 45, 29, 42, 46, 29, 45, 30, 47, 45, 44, 30, 45, 16, 48, 30, 16, 30, 44, 42, 8, 9, 42, 43, 45, 31, 43, 42, 32, 44, 43, 44, 45, 43, 15, 16, 44, 6, 7, 46, 47, 46, 45, 27, 46, 47, 49, 47, 48, 46, 7, 29, 48, 47, 30, 17, 48, 16, 27, 5, 6, 27, 6, 46, 25, 5, 27, 49, 50, 27, 49, 27, 47, 28, 51, 49, 48, 28, 49, 17, 28, 48, 51, 28, 17, 50, 51, 52, 50, 25, 27, 50, 49, 51, 18, 51, 17, 18, 26, 51, 54, 26, 18, 3, 23, 2, 23, 53, 55, 24, 56, 55, 24, 55, 53, 19, 24, 54, 19, 54, 18, 23, 25, 53, 54, 24, 53, 26, 54, 53, 23, 3, 25, 25, 4, 5, 3, 4, 25, 50, 52, 25, 25, 52, 53, 52, 51, 26, 53, 52, 26, 2, 21, 1, 0, 1, 21, 56, 21, 23, 0, 21, 56, 22, 0, 56, 22, 56, 24, 20, 0, 22, 20, 22, 19, 23, 21, 2, 56, 23, 55, 22, 24, 19], "vertices": [1, 40, 22.23, -1.36, 1, 1, 40, 22.25, -10.43, 1, 5, 40, -0.72, -13.86, 0.41508, 36, 28.39, -13.86, 0.0007, 37, 21.11, -13.86, 0.02141, 38, 13.83, -13.86, 0.10297, 39, 6.56, -13.86, 0.45984, 7, 40, -15.33, -14.55, 0.00356, 36, 13.78, -14.55, 0.18588, 37, 6.5, -14.55, 0.37996, 38, -0.78, -14.55, 0.25095, 39, -8.05, -14.55, 0.16687, 34, 28.34, -14.55, 0.00107, 35, 21.06, -14.55, 0.01172, 7, 36, 1.2, -15.13, 0.45401, 37, -6.08, -15.13, 0.15294, 38, -13.36, -15.13, 0.01284, 39, -20.63, -15.13, 0.00603, 34, 15.76, -15.13, 0.11259, 35, 8.48, -15.13, 0.24838, 33, 23.03, -15.13, 0.01322, 8, 36, -14.22, -14.71, 0.04147, 37, -21.49, -14.71, 0.00012, 34, 0.34, -14.71, 0.36097, 35, -6.94, -14.71, 0.11794, 33, 7.62, -14.71, 0.35992, 30, 29.45, -14.71, 6e-05, 31, 22.17, -14.71, 0.02321, 32, 14.9, -14.71, 0.09632, 7, 34, -16.38, -14.25, 0.00577, 33, -9.1, -14.25, 0.10219, 30, 12.73, -14.25, 0.15675, 31, 5.45, -14.25, 0.4552, 32, -1.82, -14.25, 0.23219, 28, 27.29, -14.25, 3e-05, 29, 20.01, -14.25, 0.04786, 6, 30, -4.87, -14.46, 0.14741, 31, -12.15, -14.46, 0.05163, 28, 9.69, -14.46, 0.24421, 29, 2.41, -14.46, 0.47487, 26, 24.25, -14.46, 0.00583, 27, 16.97, -14.46, 0.07606, 7, 30, -16.93, -14.61, 0.00217, 28, -2.38, -14.61, 0.2525, 29, -9.65, -14.61, 0.10283, 26, 12.18, -14.61, 0.19603, 27, 4.9, -14.61, 0.40143, 24, 26.74, -14.61, 0.00108, 25, 19.46, -14.61, 0.04397, 7, 26, -10.38, -15.26, 0.08674, 27, -17.65, -15.26, 0.0064, 24, 4.18, -15.26, 0.38217, 25, -3.1, -15.26, 0.2541, 21, 26.01, -15.26, 0.0002, 22, 18.74, -15.26, 0.05393, 23, 11.46, -15.26, 0.21645, 6, 26, -19.93, -15.12, 0.00501, 24, -5.37, -15.12, 0.21229, 25, -12.65, -15.12, 0.03846, 21, 16.46, -15.12, 0.07631, 22, 9.18, -15.12, 0.2884, 23, 1.9, -15.12, 0.37951, 5, 24, -15.6, -14.98, 0.02453, 25, -22.88, -14.98, 1e-05, 21, 6.24, -14.98, 0.52991, 22, -1.04, -14.98, 0.33107, 23, -8.32, -14.98, 0.11447, 2, 21, -12.3, -15.93, 0.99795, 22, -19.58, -15.93, 0.00205, 1, 21, -12.65, 10.34, 1, 4, 24, -15.94, 11.6, 0.00066, 21, 5.89, 11.6, 0.53822, 22, -1.39, 11.6, 0.39938, 23, -8.67, 11.6, 0.06174, 5, 26, -9.8, 11.01, 0.02604, 24, 4.76, 11.01, 0.48966, 25, -2.52, 11.01, 0.29072, 22, 19.32, 11.01, 0.0114, 23, 12.04, 11.01, 0.18218, 5, 28, -1.79, 10.42, 0.33757, 29, -9.07, 10.42, 0.03816, 26, 12.76, 10.42, 0.16135, 27, 5.48, 10.42, 0.46077, 25, 20.04, 10.42, 0.00214, 5, 33, -10.37, 10.46, 0.01725, 30, 11.46, 10.46, 0.17102, 31, 4.18, 10.46, 0.53682, 32, -3.09, 10.46, 0.24971, 29, 18.74, 10.46, 0.02521, 5, 36, -2.54, 10.2, 0.29965, 37, -9.82, 10.2, 0.0288, 34, 12.01, 10.2, 0.1553, 35, 4.73, 10.2, 0.50916, 33, 19.29, 10.2, 0.00709, 4, 40, -2.92, 9.62, 0.20604, 37, 18.91, 9.62, 0.00345, 38, 11.64, 9.62, 0.15701, 39, 4.36, 9.62, 0.6335, 1, 40, 22.22, 6.26, 1, 2, 40, 12.18, -7.42, 0.98361, 39, 19.46, -7.42, 0.01639, 1, 40, 12.37, 3.74, 1, 4, 40, -0.41, -7.44, 0.45506, 37, 21.42, -7.44, 0.00645, 38, 14.15, -7.44, 0.05388, 39, 6.87, -7.44, 0.48461, 3, 40, -1.65, 4.74, 0.27355, 38, 12.91, 4.74, 0.0461, 39, 5.63, 4.74, 0.68035, 7, 36, 1.1, -8.9, 0.52118, 37, -6.18, -8.9, 0.11438, 38, -13.46, -8.9, 0.00581, 39, -20.74, -8.9, 0.0031, 34, 15.65, -8.9, 0.07877, 35, 8.38, -8.9, 0.27063, 33, 22.93, -8.9, 0.00612, 4, 36, -0.75, 4.9, 0.4161, 37, -8.03, 4.9, 0.02649, 34, 13.81, 4.9, 0.04317, 35, 6.53, 4.9, 0.51424, 6, 34, -16.22, -5.9, 0.00064, 33, -8.94, -5.9, 0.04214, 30, 12.89, -5.9, 0.0841, 31, 5.61, -5.9, 0.63206, 32, -1.66, -5.9, 0.22901, 29, 20.17, -5.9, 0.01206, 5, 33, -9.77, 5.67, 0.0095, 30, 12.06, 5.67, 0.09868, 31, 4.79, 5.67, 0.64501, 32, -2.49, 5.67, 0.23754, 29, 19.34, 5.67, 0.00927, 6, 30, -16.34, -9.19, 0.00135, 28, -1.78, -9.19, 0.28065, 29, -9.06, -9.19, 0.08998, 26, 12.78, -9.19, 0.14691, 27, 5.5, -9.19, 0.45757, 25, 20.05, -9.19, 0.02355, 4, 28, -2, 5.02, 0.29398, 29, -9.28, 5.02, 0.00918, 26, 12.55, 5.02, 0.10636, 27, 5.27, 5.02, 0.59048, 6, 26, -8.95, -9.02, 0.08202, 27, -16.22, -9.02, 0.00576, 24, 5.61, -9.02, 0.43791, 25, -1.67, -9.02, 0.31174, 22, 20.17, -9.02, 0.02191, 23, 12.89, -9.02, 0.14065, 5, 26, -9.17, 4.17, 0.00514, 24, 5.39, 4.17, 0.67615, 25, -1.89, 4.17, 0.25539, 22, 19.95, 4.17, 9e-05, 23, 12.67, 4.17, 0.06323, 4, 24, -16.72, -9.05, 0.00967, 21, 5.11, -9.05, 0.61625, 22, -2.16, -9.05, 0.31251, 23, -9.44, -9.05, 0.06157, 3, 21, 5.91, 3.74, 0.67582, 22, -1.37, 3.74, 0.31598, 23, -8.65, 3.74, 0.0082, 3, 21, -4.77, -10.56, 0.96217, 22, -12.05, -10.56, 0.03605, 23, -19.33, -10.56, 0.00177, 2, 21, -6.48, 7.41, 0.991, 22, -13.76, 7.41, 0.009, 1, 21, -4.66, -0.5, 1, 3, 21, 3.89, -1.58, 0.98269, 22, -3.39, -1.58, 0.01611, 23, -10.67, -1.58, 0.0012, 5, 26, -11.34, -1.58, 0.00149, 24, 3.21, -1.58, 0.96729, 25, -4.07, -1.58, 0.00188, 22, 17.77, -1.58, 0.00117, 23, 10.49, -1.58, 0.02816, 5, 24, -6.62, -4.15, 0.05529, 25, -13.9, -4.15, 0.00116, 21, 15.22, -4.15, 0.01146, 22, 7.94, -4.15, 0.37111, 23, 0.66, -4.15, 0.56097, 5, 24, -5.55, 7.84, 0.12648, 25, -12.83, 7.84, 0.00814, 21, 16.29, 7.84, 0.02092, 22, 9.01, 7.84, 0.32591, 23, 1.73, 7.84, 0.51856, 8, 28, -14.14, -9.28, 0.00771, 29, -21.42, -9.28, 0.00067, 26, 0.42, -9.28, 0.42143, 27, -6.86, -9.28, 0.11477, 24, 14.97, -9.28, 0.07979, 25, 7.7, -9.28, 0.36647, 22, 29.53, -9.28, 1e-05, 23, 22.25, -9.28, 0.00915, 3, 26, 0.85, -1.37, 0.79155, 27, -6.43, -1.37, 0.00276, 25, 8.13, -1.37, 0.2057, 5, 28, -13.92, 3.98, 0.00068, 26, 0.63, 3.98, 0.58021, 27, -6.64, 3.98, 0.02936, 24, 15.19, 3.98, 0.02113, 25, 7.91, 3.98, 0.36863, 5, 28, -2.46, -2.01, 0.07451, 29, -9.74, -2.01, 0.0046, 26, 12.09, -2.01, 0.00515, 27, 4.81, -2.01, 0.91475, 25, 19.37, -2.01, 0.00099, 6, 30, -5.32, -6.93, 0.08289, 31, -12.6, -6.93, 0.0191, 28, 9.24, -6.93, 0.25572, 29, 1.96, -6.93, 0.60214, 26, 23.79, -6.93, 0.00147, 27, 16.52, -6.93, 0.03868, 3, 31, -12.64, -1.8, 0.00094, 28, 9.19, -1.8, 0.09414, 29, 1.91, -1.8, 0.90492, 5, 30, -5.52, 5.48, 0.0553, 31, -12.79, 5.48, 0.0005, 28, 9.04, 5.48, 0.26788, 29, 1.76, 5.48, 0.65341, 27, 16.32, 5.48, 0.02291, 4, 33, -8.87, -0.73, 0.0011, 30, 12.97, -0.73, 0.00094, 31, 5.69, -0.73, 0.9979, 29, 20.24, -0.73, 6e-05, 6, 36, -15.68, -3.94, 0.00273, 34, -1.12, -3.94, 0.30183, 35, -8.4, -3.94, 0.01335, 33, 6.15, -3.94, 0.64881, 31, 20.71, -3.94, 0.00353, 32, 13.43, -3.94, 0.02975, 4, 34, -1.13, 5.05, 0.29363, 35, -8.41, 5.05, 0.03323, 33, 6.14, 5.05, 0.61021, 32, 13.42, 5.05, 0.06293, 4, 36, 1.04, -1.58, 0.86667, 37, -6.24, -1.58, 0.00086, 34, 15.59, -1.58, 0.00251, 35, 8.32, -1.58, 0.12996, 4, 36, 13.89, -0.94, 0.00245, 37, 6.61, -0.94, 0.74011, 38, -0.67, -0.94, 0.25167, 39, -7.95, -0.94, 0.00577, 4, 36, 13.68, 5.05, 0.04863, 37, 6.4, 5.06, 0.56746, 38, -0.88, 5.06, 0.35787, 39, -8.16, 5.06, 0.02605, 4, 40, -2.53, -2.01, 0.07845, 37, 19.3, -2.01, 5e-05, 38, 12.03, -2.01, 0.01088, 39, 4.75, -2.01, 0.91062, 1, 40, 10.07, -1.36, 1], "hull": 21, "edges": [2, 4, 16, 18, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 18, 20, 20, 22, 12, 14, 14, 16, 8, 10, 10, 12, 4, 6, 6, 8, 2, 0, 0, 40], "width": 179, "height": 50}}, "f5": {"f1": {"type": "mesh", "uvs": [1, 1, 0.87542, 1, 0.71088, 1, 0.52303, 1, 0.35027, 1, 0.17201, 1, 0, 1, 0, 0, 0.17476, 0, 0.34478, 0, 0.52989, 0, 0.72597, 0, 0.87131, 0, 1, 0], "triangles": [5, 6, 7, 8, 5, 7, 3, 4, 9, 5, 8, 9, 4, 5, 9, 10, 3, 9, 2, 10, 11, 3, 10, 2, 1, 12, 13, 0, 1, 13, 11, 12, 1, 2, 11, 1], "vertices": [287.9, -38.96, 218.76, -38.96, 127.44, -38.96, 23.18, -38.96, -72.71, -38.96, -171.64, -38.96, -267.1, -38.96, -267.1, 46.04, -170.11, 46.04, -75.75, 46.04, 26.99, 46.04, 135.81, 46.04, 216.47, 46.04, 287.9, 46.04], "hull": 14, "edges": [12, 14, 0, 26, 14, 16, 10, 12, 16, 18, 8, 10, 18, 20, 6, 8, 20, 22, 4, 6, 22, 24, 24, 26, 0, 2, 2, 4], "width": 555, "height": 85}}, "f6": {"f2": {"type": "mesh", "uvs": [1, 1, 0.81529, 1, 0.51496, 1, 0.19907, 1, 0, 1, 0, 0, 0.19189, 0, 0.50898, 0, 0.80572, 0, 1, 0], "triangles": [4, 5, 6, 3, 6, 7, 4, 6, 3, 2, 7, 8, 3, 7, 2, 1, 8, 9, 0, 1, 9, 2, 8, 1], "vertices": [307.8, -39.11, 190.32, -39.11, -0.69, -39.11, -201.59, -39.11, -328.2, -39.11, -328.2, 29.89, -206.16, 29.89, -4.49, 29.89, 184.24, 29.89, 307.8, 29.89], "hull": 10, "edges": [8, 10, 0, 18, 10, 12, 6, 8, 12, 14, 4, 6, 14, 16, 16, 18, 0, 2, 2, 4], "width": 636, "height": 69}}, "root3": {"root3": {"type": "clipping", "end": "f2", "vertexCount": 5, "vertices": [-779.44, -28.61, -766.04, -28.99, 780.99, -28.12, 774.08, 31.86, -779.77, 35.54], "color": "ce3a3aff"}}, "root": {"root": {"type": "path", "lengths": [1573.61, 3333.34], "vertexCount": 6, "vertices": [-966.86, -31.79, -780.74, -32.05, -723.44, -32.13, 729.89, -32.47, 792.87, -32.79, 967.13, -33.68]}}}}], "animations": {"animation": {"slots": {"f4": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}]}, "f1": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffff73", "curve": "stepped"}, {"time": 1.6667, "color": "ffffff73"}, {"time": 2, "color": "ffffff00"}]}, "f5": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffff73", "curve": "stepped"}, {"time": 1.6667, "color": "ffffff73"}, {"time": 2, "color": "ffffff00"}]}, "f3": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}]}, "f6": {"color": [{"time": 0.8333, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.5333, "color": "ffffffff"}]}, "f2": {"color": [{"time": 0.8333, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.5333, "color": "ffffffff"}]}}, "bones": {"bone24": {"translate": [{"x": 294.96}, {"time": 1.1667, "x": 954.39}, {"time": 1.2, "x": -157.21}, {"time": 2, "x": 294.96}]}, "f1": {"translate": [{}, {"time": 2, "x": 915.53}], "scale": [{}, {"time": 1, "x": 1.947, "y": 1.947}, {"time": 2}]}, "bone21": {"translate": [{"x": 294.96}, {"time": 1.1667, "x": 954.39}, {"time": 1.2, "x": -157.21}, {"time": 2, "x": 294.96}]}, "f2": {"translate": [{}, {"time": 2, "x": 915.53}], "scale": [{}, {"time": 1, "x": 1.947, "y": 1.947}, {"time": 2}]}}, "path": {"root1": {"position": [{"position": 1}, {"time": 2, "position": 0.4177}]}, "root": {"position": [{}, {"time": 2, "position": 0.4937}]}}, "deform": {"default": {"f3": {"f3": [{"vertices": [-168.88165, -0.23577, -168.8811, -0.5142, -168.88165, -0.23577, -168.8811, -0.5142, -168.88165, -0.23577, -168.8811, -0.5142, -168.88092, -0.65286, -168.88031, -0.73087, -168.88013, -0.76712, -168.88165, -0.23577, -168.8811, -0.5142, -168.88092, -0.65286, -168.88031, -0.73087, -168.88013, -0.76712, -168.88025, -0.7874, -168.88019, -0.80201, -168.87988, -0.81197, -168.88031, -0.73087, -168.88013, -0.76712, -168.88025, -0.7874, -168.88019, -0.80201, -168.87988, -0.81197, -168.88031, -0.73087, -168.88013, -0.76712, -168.88025, -0.7874, -168.88019, -0.80201, -168.87988, -0.81197, -168.87994, -0.81821, -168.87982, -0.8214, -168.88, -0.82198, -168.87982, -0.8212, -168.87988, -0.81197, -168.87994, -0.81821, -168.87982, -0.8214, -168.88, -0.82198, -168.87982, -0.8212, -168.87988, -0.81197, -168.87994, -0.81821, -168.87982, -0.8214, -168.88, -0.82198, -168.87982, -0.8212, -168.88, -0.81917, -168.87988, -0.81594, -168.87988, -0.81168, -168.88, -0.80639, -168.88, -0.82198, -168.87982, -0.8212, -168.88, -0.81917, -168.87988, -0.81594, -168.87988, -0.81168, -168.88, -0.80639, -168.87988, -0.81594, -168.87988, -0.81168, -168.88, -0.80639, -168.88, -0.80015, -168.88, -0.7935, -168.87988, -0.81594, -168.87988, -0.81168, -168.88, -0.80639, -168.88, -0.80015, -168.88, -0.7935, -168.88007, -0.78671, -168.88013, -0.77924, -168.88, -0.80639, -168.88, -0.80015, -168.88, -0.7935, -168.88007, -0.78671, -168.88013, -0.77924, -168.88, -0.7935, -168.88007, -0.78671, -168.88013, -0.77924, -168.88, -0.7935, -168.88007, -0.78671, -168.88013, -0.77924, -168.88, -0.80639, -168.88, -0.80015, -168.88, -0.7935, -168.88007, -0.78671, -168.88013, -0.77924, -168.87988, -0.81594, -168.87988, -0.81168, -168.88, -0.80639, -168.88, -0.80015, -168.88, -0.7935, -168.88, -0.82198, -168.87982, -0.8212, -168.88, -0.81917, -168.87988, -0.81594, -168.87988, -0.81168, -168.87988, -0.81197, -168.87994, -0.81821, -168.87982, -0.8214, -168.88, -0.82198, -168.87982, -0.8212, -168.88013, -0.76712, -168.88025, -0.7874, -168.88019, -0.80201, -168.87988, -0.81197, -168.87994, -0.81821, -168.8811, -0.5142, -168.88092, -0.65286, -168.88031, -0.73087, -168.88013, -0.76712, -168.88165, -0.23577, -168.8811, -0.5142, -168.88165, -0.23577, -168.8811, -0.5142, -168.88092, -0.65286, -168.88165, -0.23577, -168.8811, -0.5142, -168.88092, -0.65286, -168.88165, -0.23577, -168.8811, -0.5142, -168.88092, -0.65286, -168.88031, -0.73087, -168.88013, -0.76712, -168.8811, -0.5142, -168.88092, -0.65286, -168.88031, -0.73087, -168.88013, -0.76712, -168.88031, -0.73087, -168.88013, -0.76712, -168.88025, -0.7874, -168.88019, -0.80201, -168.87988, -0.81197, -168.88013, -0.76712, -168.88025, -0.7874, -168.88019, -0.80201, -168.87988, -0.81197, -168.87988, -0.81197, -168.87994, -0.81821, -168.87982, -0.8214, -168.88, -0.82198, -168.87982, -0.8212, -168.87988, -0.81197, -168.87994, -0.81821, -168.87982, -0.8214, -168.88, -0.82198, -168.87982, -0.8212, -168.88, -0.82198, -168.87982, -0.8212, -168.88, -0.81917, -168.87988, -0.81594, -168.87988, -0.81168, -168.88, -0.82198, -168.87982, -0.8212, -168.88, -0.81917, -168.87988, -0.81594, -168.87988, -0.81168, -168.87988, -0.81594, -168.87988, -0.81168, -168.88, -0.80639, -168.88, -0.80015, -168.88, -0.7935, -168.87988, -0.81594, -168.87988, -0.81168, -168.88, -0.80639, -168.88, -0.80015, -168.88, -0.7935, -168.88, -0.80015, -168.88, -0.7935, -168.88007, -0.78671, -168.88013, -0.77924, -168.88, -0.80015, -168.88, -0.7935, -168.88007, -0.78671, -168.88013, -0.77924, -168.88, -0.80015, -168.88, -0.7935, -168.88007, -0.78671, -168.88013, -0.77924, -168.88, -0.80015, -168.88, -0.7935, -168.88007, -0.78671, -168.88013, -0.77924, -168.88, -0.80015, -168.88, -0.7935, -168.88007, -0.78671, -168.88013, -0.77924, -168.87988, -0.81594, -168.87988, -0.81168, -168.88, -0.80639, -168.88, -0.80015, -168.88, -0.7935, -168.88007, -0.78671, -168.88013, -0.77924, -168.87988, -0.81594, -168.87988, -0.81168, -168.88, -0.80639, -168.88, -0.80015, -168.88, -0.7935, -168.88007, -0.78671, -168.88013, -0.77924, -168.87988, -0.81594, -168.87988, -0.81168, -168.88, -0.80639, -168.88, -0.80015, -168.88, -0.7935, -168.88007, -0.78671, -168.88013, -0.77924, -168.87988, -0.81594, -168.87988, -0.81168, -168.88, -0.80639, -168.88, -0.80015, -168.88, -0.7935, -168.88007, -0.78671, -168.88013, -0.77924, -168.88, -0.82198, -168.87982, -0.8212, -168.88, -0.81917, -168.87988, -0.81594, -168.87988, -0.81168, -168.88, -0.80639, -168.88, -0.80015, -168.88, -0.7935, -168.88, -0.82198, -168.87982, -0.8212, -168.88, -0.81917, -168.87988, -0.81594, -168.87988, -0.81168, -168.88, -0.80639, -168.88, -0.80015, -168.88, -0.7935, -168.88007, -0.78671, -168.88013, -0.77924, -168.88, -0.82198, -168.87982, -0.8212, -168.88, -0.81917, -168.87988, -0.81594, -168.87988, -0.81168, -168.88, -0.80639, -168.88, -0.80015, -168.88, -0.7935, -168.88007, -0.78671, -168.88013, -0.77924, -168.87988, -0.81197, -168.87994, -0.81821, -168.87982, -0.8214, -168.88, -0.82198, -168.87982, -0.8212, -168.88, -0.81917, -168.87988, -0.81594, -168.87988, -0.81168, -168.88, -0.80639, -168.88, -0.80015, -168.88, -0.7935, -168.88007, -0.78671, -168.88013, -0.77924, -168.87988, -0.81197, -168.87994, -0.81821, -168.87982, -0.8214, -168.88, -0.82198, -168.87982, -0.8212, -168.88, -0.81917, -168.87988, -0.81594, -168.87988, -0.81168, -168.88, -0.80639, -168.88, -0.80015, -168.88, -0.7935, -168.88007, -0.78671, -168.88013, -0.77924, -168.87988, -0.81197, -168.87994, -0.81821, -168.87982, -0.8214, -168.88, -0.82198, -168.87982, -0.8212, -168.88, -0.81917, -168.87988, -0.81594, -168.87988, -0.81168, -168.88, -0.80639, -168.88, -0.80015, -168.88, -0.7935, -168.88007, -0.78671, -168.88013, -0.77924, -168.87988, -0.81197, -168.87994, -0.81821, -168.87982, -0.8214, -168.88, -0.82198, -168.87982, -0.8212, -168.88, -0.81917, -168.87988, -0.81594, -168.87988, -0.81168, -168.88013, -0.76712, -168.88025, -0.7874, -168.88019, -0.80201, -168.87988, -0.81197, -168.87994, -0.81821, -168.87982, -0.8214, -168.88, -0.82198, -168.87982, -0.8212, -168.88031, -0.73087, -168.88013, -0.76712, -168.88025, -0.7874, -168.88019, -0.80201, -168.87988, -0.81197, -168.87994, -0.81821, -168.87982, -0.8214, -168.88, -0.82198, -168.87982, -0.8212, -168.88013, -0.76712, -168.88025, -0.7874, -168.88019, -0.80201, -168.87988, -0.81197, -168.87994, -0.81821, -168.87982, -0.8214, -168.88, -0.82198, -168.87982, -0.8212, -168.88165, -0.23577, -168.8811, -0.5142, -168.88092, -0.65286, -168.88031, -0.73087, -168.88013, -0.76712, -168.88025, -0.7874, -168.88019, -0.80201, -168.87988, -0.81197, -168.88165, -0.23577, -168.8811, -0.5142, -168.88092, -0.65286, -168.88031, -0.73087, -168.88013, -0.76712, -168.88025, -0.7874, -168.88019, -0.80201, -168.87988, -0.81197, -168.8811, -0.5142, -168.88092, -0.65286, -168.88031, -0.73087, -168.88013, -0.76712, -168.88025, -0.7874, -168.88019, -0.80201, -168.87988, -0.81197, -168.88165, -0.23577, -168.8811, -0.5142, -168.88092, -0.65286, -168.88031, -0.73087, -168.88013, -0.76712, -168.88025, -0.7874, -168.88019, -0.80201, -168.87988, -0.81197, -168.88165, -0.23577, -168.8811, -0.5142, -168.88092, -0.65286, -168.88031, -0.73087, -168.88013, -0.76712, -168.88025, -0.7874, -168.88019, -0.80201, -168.87988, -0.81197]}, {"time": 0.3333, "vertices": [-146.91345, 0.03975, -146.91351, 0.01237, -146.91345, 0.03975, -146.91351, 0.01237, -146.91345, 0.03975, -146.91351, 0.01237, -146.91357, -0.01031, -146.91345, -0.03072, -146.91351, -0.04985, -146.91345, 0.03975, -146.91351, 0.01237, -146.91357, -0.01031, -146.91345, -0.03072, -146.91351, -0.04985, -146.91345, -0.06784, -146.91345, -0.08478, -146.91345, -0.10073, -146.91345, -0.03072, -146.91351, -0.04985, -146.91345, -0.06784, -146.91345, -0.08478, -146.91345, -0.10073, -146.91345, -0.03072, -146.91351, -0.04985, -146.91345, -0.06784, -146.91345, -0.08478, -146.91345, -0.10073, -146.91345, -0.11575, -146.91327, -0.12995, -146.91339, -0.14329, -146.91333, -0.15506, -146.91345, -0.10073, -146.91345, -0.11575, -146.91327, -0.12995, -146.91339, -0.14329, -146.91333, -0.15506, -146.91345, -0.10073, -146.91345, -0.11575, -146.91327, -0.12995, -146.91339, -0.14329, -146.91333, -0.15506, -146.91345, -0.16621, -146.91333, -0.17683, -146.91351, -0.18691, -146.91345, -0.1965, -146.91339, -0.14329, -146.91333, -0.15506, -146.91345, -0.16621, -146.91333, -0.17683, -146.91351, -0.18691, -146.91345, -0.1965, -146.91333, -0.17683, -146.91351, -0.18691, -146.91345, -0.1965, -146.91333, -0.2056, -146.91327, -0.21428, -146.91333, -0.17683, -146.91351, -0.18691, -146.91345, -0.1965, -146.91333, -0.2056, -146.91327, -0.21428, -146.91339, -0.22253, -146.91327, -0.23036, -146.91345, -0.1965, -146.91333, -0.2056, -146.91327, -0.21428, -146.91339, -0.22253, -146.91327, -0.23036, -146.91327, -0.21428, -146.91339, -0.22253, -146.91327, -0.23036, -146.91327, -0.21428, -146.91339, -0.22253, -146.91327, -0.23036, -146.91345, -0.1965, -146.91333, -0.2056, -146.91327, -0.21428, -146.91339, -0.22253, -146.91327, -0.23036, -146.91333, -0.17683, -146.91351, -0.18691, -146.91345, -0.1965, -146.91333, -0.2056, -146.91327, -0.21428, -146.91339, -0.14329, -146.91333, -0.15506, -146.91345, -0.16621, -146.91333, -0.17683, -146.91351, -0.18691, -146.91345, -0.10073, -146.91345, -0.11575, -146.91327, -0.12995, -146.91339, -0.14329, -146.91333, -0.15506, -146.91351, -0.04985, -146.91345, -0.06784, -146.91345, -0.08478, -146.91345, -0.10073, -146.91345, -0.11575, -146.91351, 0.01237, -146.91357, -0.01031, -146.91345, -0.03072, -146.91351, -0.04985, -146.91345, 0.03975, -146.91351, 0.01237, -146.91345, 0.03975, -146.91351, 0.01237, -146.91357, -0.01031, -146.91345, 0.03975, -146.91351, 0.01237, -146.91357, -0.01031, -146.91345, 0.03975, -146.91351, 0.01237, -146.91357, -0.01031, -146.91345, -0.03072, -146.91351, -0.04985, -146.91351, 0.01237, -146.91357, -0.01031, -146.91345, -0.03072, -146.91351, -0.04985, -146.91345, -0.03072, -146.91351, -0.04985, -146.91345, -0.06784, -146.91345, -0.08478, -146.91345, -0.10073, -146.91351, -0.04985, -146.91345, -0.06784, -146.91345, -0.08478, -146.91345, -0.10073, -146.91345, -0.10073, -146.91345, -0.11575, -146.91327, -0.12995, -146.91339, -0.14329, -146.91333, -0.15506, -146.91345, -0.10073, -146.91345, -0.11575, -146.91327, -0.12995, -146.91339, -0.14329, -146.91333, -0.15506, -146.91339, -0.14329, -146.91333, -0.15506, -146.91345, -0.16621, -146.91333, -0.17683, -146.91351, -0.18691, -146.91339, -0.14329, -146.91333, -0.15506, -146.91345, -0.16621, -146.91333, -0.17683, -146.91351, -0.18691, -146.91333, -0.17683, -146.91351, -0.18691, -146.91345, -0.1965, -146.91333, -0.2056, -146.91327, -0.21428, -146.91333, -0.17683, -146.91351, -0.18691, -146.91345, -0.1965, -146.91333, -0.2056, -146.91327, -0.21428, -146.91333, -0.2056, -146.91327, -0.21428, -146.91339, -0.22253, -146.91327, -0.23036, -146.91333, -0.2056, -146.91327, -0.21428, -146.91339, -0.22253, -146.91327, -0.23036, -146.91333, -0.2056, -146.91327, -0.21428, -146.91339, -0.22253, -146.91327, -0.23036, -146.91333, -0.2056, -146.91327, -0.21428, -146.91339, -0.22253, -146.91327, -0.23036, -146.91333, -0.2056, -146.91327, -0.21428, -146.91339, -0.22253, -146.91327, -0.23036, -146.91333, -0.17683, -146.91351, -0.18691, -146.91345, -0.1965, -146.91333, -0.2056, -146.91327, -0.21428, -146.91339, -0.22253, -146.91327, -0.23036, -146.91333, -0.17683, -146.91351, -0.18691, -146.91345, -0.1965, -146.91333, -0.2056, -146.91327, -0.21428, -146.91339, -0.22253, -146.91327, -0.23036, -146.91333, -0.17683, -146.91351, -0.18691, -146.91345, -0.1965, -146.91333, -0.2056, -146.91327, -0.21428, -146.91339, -0.22253, -146.91327, -0.23036, -146.91333, -0.17683, -146.91351, -0.18691, -146.91345, -0.1965, -146.91333, -0.2056, -146.91327, -0.21428, -146.91339, -0.22253, -146.91327, -0.23036, -146.91339, -0.14329, -146.91333, -0.15506, -146.91345, -0.16621, -146.91333, -0.17683, -146.91351, -0.18691, -146.91345, -0.1965, -146.91333, -0.2056, -146.91327, -0.21428, -146.91339, -0.14329, -146.91333, -0.15506, -146.91345, -0.16621, -146.91333, -0.17683, -146.91351, -0.18691, -146.91345, -0.1965, -146.91333, -0.2056, -146.91327, -0.21428, -146.91339, -0.22253, -146.91327, -0.23036, -146.91339, -0.14329, -146.91333, -0.15506, -146.91345, -0.16621, -146.91333, -0.17683, -146.91351, -0.18691, -146.91345, -0.1965, -146.91333, -0.2056, -146.91327, -0.21428, -146.91339, -0.22253, -146.91327, -0.23036, -146.91345, -0.10073, -146.91345, -0.11575, -146.91327, -0.12995, -146.91339, -0.14329, -146.91333, -0.15506, -146.91345, -0.16621, -146.91333, -0.17683, -146.91351, -0.18691, -146.91345, -0.1965, -146.91333, -0.2056, -146.91327, -0.21428, -146.91339, -0.22253, -146.91327, -0.23036, -146.91345, -0.10073, -146.91345, -0.11575, -146.91327, -0.12995, -146.91339, -0.14329, -146.91333, -0.15506, -146.91345, -0.16621, -146.91333, -0.17683, -146.91351, -0.18691, -146.91345, -0.1965, -146.91333, -0.2056, -146.91327, -0.21428, -146.91339, -0.22253, -146.91327, -0.23036, -146.91345, -0.10073, -146.91345, -0.11575, -146.91327, -0.12995, -146.91339, -0.14329, -146.91333, -0.15506, -146.91345, -0.16621, -146.91333, -0.17683, -146.91351, -0.18691, -146.91345, -0.1965, -146.91333, -0.2056, -146.91327, -0.21428, -146.91339, -0.22253, -146.91327, -0.23036, -146.91345, -0.10073, -146.91345, -0.11575, -146.91327, -0.12995, -146.91339, -0.14329, -146.91333, -0.15506, -146.91345, -0.16621, -146.91333, -0.17683, -146.91351, -0.18691, -146.91351, -0.04985, -146.91345, -0.06784, -146.91345, -0.08478, -146.91345, -0.10073, -146.91345, -0.11575, -146.91327, -0.12995, -146.91339, -0.14329, -146.91333, -0.15506, -146.91345, -0.03072, -146.91351, -0.04985, -146.91345, -0.06784, -146.91345, -0.08478, -146.91345, -0.10073, -146.91345, -0.11575, -146.91327, -0.12995, -146.91339, -0.14329, -146.91333, -0.15506, -146.91351, -0.04985, -146.91345, -0.06784, -146.91345, -0.08478, -146.91345, -0.10073, -146.91345, -0.11575, -146.91327, -0.12995, -146.91339, -0.14329, -146.91333, -0.15506, -146.91345, 0.03975, -146.91351, 0.01237, -146.91357, -0.01031, -146.91345, -0.03072, -146.91351, -0.04985, -146.91345, -0.06784, -146.91345, -0.08478, -146.91345, -0.10073, -146.91345, 0.03975, -146.91351, 0.01237, -146.91357, -0.01031, -146.91345, -0.03072, -146.91351, -0.04985, -146.91345, -0.06784, -146.91345, -0.08478, -146.91345, -0.10073, -146.91351, 0.01237, -146.91357, -0.01031, -146.91345, -0.03072, -146.91351, -0.04985, -146.91345, -0.06784, -146.91345, -0.08478, -146.91345, -0.10073, -146.91345, 0.03975, -146.91351, 0.01237, -146.91357, -0.01031, -146.91345, -0.03072, -146.91351, -0.04985, -146.91345, -0.06784, -146.91345, -0.08478, -146.91345, -0.10073, -146.91345, 0.03975, -146.91351, 0.01237, -146.91357, -0.01031, -146.91345, -0.03072, -146.91351, -0.04985, -146.91345, -0.06784, -146.91345, -0.08478, -146.91345, -0.10073]}, {"time": 1.6, "vertices": [-58.33862, -3.09125, -58.33573, -3.14189, -58.33862, -3.09125, -58.33573, -3.14189, -58.33862, -3.09125, -58.33573, -3.14189, -58.3349, -3.15633, -58.33472, -3.15987, -58.3346, -3.16127, -58.33862, -3.09125, -58.33573, -3.14189, -58.3349, -3.15633, -58.33472, -3.15987, -58.3346, -3.16127, -58.33466, -3.16119, -58.33469, -3.16026, -58.33472, -3.15889, -58.33472, -3.15987, -58.3346, -3.16127, -58.33466, -3.16119, -58.33469, -3.16026, -58.33472, -3.15889, -58.33472, -3.15987, -58.3346, -3.16127, -58.33466, -3.16119, -58.33469, -3.16026, -58.33472, -3.15889, -58.33488, -3.15704, -58.33502, -3.15479, -58.33516, -3.15212, -58.33532, -3.14952, -58.33472, -3.15889, -58.33488, -3.15704, -58.33502, -3.15479, -58.33516, -3.15212, -58.33532, -3.14952, -58.33472, -3.15889, -58.33488, -3.15704, -58.33502, -3.15479, -58.33516, -3.15212, -58.33532, -3.14952, -58.33548, -3.14663, -58.33564, -3.14349, -58.33587, -3.14006, -58.33604, -3.13634, -58.33516, -3.15212, -58.33532, -3.14952, -58.33548, -3.14663, -58.33564, -3.14349, -58.33587, -3.14006, -58.33604, -3.13634, -58.33564, -3.14349, -58.33587, -3.14006, -58.33604, -3.13634, -58.33627, -3.13247, -58.33649, -3.12848, -58.33564, -3.14349, -58.33587, -3.14006, -58.33604, -3.13634, -58.33627, -3.13247, -58.33649, -3.12848, -58.33673, -3.12417, -58.337, -3.11956, -58.33604, -3.13634, -58.33627, -3.13247, -58.33649, -3.12848, -58.33673, -3.12417, -58.337, -3.11956, -58.33649, -3.12848, -58.33673, -3.12417, -58.337, -3.11956, -58.33649, -3.12848, -58.33673, -3.12417, -58.337, -3.11956, -58.33604, -3.13634, -58.33627, -3.13247, -58.33649, -3.12848, -58.33673, -3.12417, -58.337, -3.11956, -58.33564, -3.14349, -58.33587, -3.14006, -58.33604, -3.13634, -58.33627, -3.13247, -58.33649, -3.12848, -58.33516, -3.15212, -58.33532, -3.14952, -58.33548, -3.14663, -58.33564, -3.14349, -58.33587, -3.14006, -58.33472, -3.15889, -58.33488, -3.15704, -58.33502, -3.15479, -58.33516, -3.15212, -58.33532, -3.14952, -58.3346, -3.16127, -58.33466, -3.16119, -58.33469, -3.16026, -58.33472, -3.15889, -58.33488, -3.15704, -58.33573, -3.14189, -58.3349, -3.15633, -58.33472, -3.15987, -58.3346, -3.16127, -58.33862, -3.09125, -58.33573, -3.14189, -58.33862, -3.09125, -58.33573, -3.14189, -58.3349, -3.15633, -58.33862, -3.09125, -58.33573, -3.14189, -58.3349, -3.15633, -58.33862, -3.09125, -58.33573, -3.14189, -58.3349, -3.15633, -58.33472, -3.15987, -58.3346, -3.16127, -58.33573, -3.14189, -58.3349, -3.15633, -58.33472, -3.15987, -58.3346, -3.16127, -58.33472, -3.15987, -58.3346, -3.16127, -58.33466, -3.16119, -58.33469, -3.16026, -58.33472, -3.15889, -58.3346, -3.16127, -58.33466, -3.16119, -58.33469, -3.16026, -58.33472, -3.15889, -58.33472, -3.15889, -58.33488, -3.15704, -58.33502, -3.15479, -58.33516, -3.15212, -58.33532, -3.14952, -58.33472, -3.15889, -58.33488, -3.15704, -58.33502, -3.15479, -58.33516, -3.15212, -58.33532, -3.14952, -58.33516, -3.15212, -58.33532, -3.14952, -58.33548, -3.14663, -58.33564, -3.14349, -58.33587, -3.14006, -58.33516, -3.15212, -58.33532, -3.14952, -58.33548, -3.14663, -58.33564, -3.14349, -58.33587, -3.14006, -58.33564, -3.14349, -58.33587, -3.14006, -58.33604, -3.13634, -58.33627, -3.13247, -58.33649, -3.12848, -58.33564, -3.14349, -58.33587, -3.14006, -58.33604, -3.13634, -58.33627, -3.13247, -58.33649, -3.12848, -58.33627, -3.13247, -58.33649, -3.12848, -58.33673, -3.12417, -58.337, -3.11956, -58.33627, -3.13247, -58.33649, -3.12848, -58.33673, -3.12417, -58.337, -3.11956, -58.33627, -3.13247, -58.33649, -3.12848, -58.33673, -3.12417, -58.337, -3.11956, -58.33627, -3.13247, -58.33649, -3.12848, -58.33673, -3.12417, -58.337, -3.11956, -58.33627, -3.13247, -58.33649, -3.12848, -58.33673, -3.12417, -58.337, -3.11956, -58.33564, -3.14349, -58.33587, -3.14006, -58.33604, -3.13634, -58.33627, -3.13247, -58.33649, -3.12848, -58.33673, -3.12417, -58.337, -3.11956, -58.33564, -3.14349, -58.33587, -3.14006, -58.33604, -3.13634, -58.33627, -3.13247, -58.33649, -3.12848, -58.33673, -3.12417, -58.337, -3.11956, -58.33564, -3.14349, -58.33587, -3.14006, -58.33604, -3.13634, -58.33627, -3.13247, -58.33649, -3.12848, -58.33673, -3.12417, -58.337, -3.11956, -58.33564, -3.14349, -58.33587, -3.14006, -58.33604, -3.13634, -58.33627, -3.13247, -58.33649, -3.12848, -58.33673, -3.12417, -58.337, -3.11956, -58.33516, -3.15212, -58.33532, -3.14952, -58.33548, -3.14663, -58.33564, -3.14349, -58.33587, -3.14006, -58.33604, -3.13634, -58.33627, -3.13247, -58.33649, -3.12848, -58.33516, -3.15212, -58.33532, -3.14952, -58.33548, -3.14663, -58.33564, -3.14349, -58.33587, -3.14006, -58.33604, -3.13634, -58.33627, -3.13247, -58.33649, -3.12848, -58.33673, -3.12417, -58.337, -3.11956, -58.33516, -3.15212, -58.33532, -3.14952, -58.33548, -3.14663, -58.33564, -3.14349, -58.33587, -3.14006, -58.33604, -3.13634, -58.33627, -3.13247, -58.33649, -3.12848, -58.33673, -3.12417, -58.337, -3.11956, -58.33472, -3.15889, -58.33488, -3.15704, -58.33502, -3.15479, -58.33516, -3.15212, -58.33532, -3.14952, -58.33548, -3.14663, -58.33564, -3.14349, -58.33587, -3.14006, -58.33604, -3.13634, -58.33627, -3.13247, -58.33649, -3.12848, -58.33673, -3.12417, -58.337, -3.11956, -58.33472, -3.15889, -58.33488, -3.15704, -58.33502, -3.15479, -58.33516, -3.15212, -58.33532, -3.14952, -58.33548, -3.14663, -58.33564, -3.14349, -58.33587, -3.14006, -58.33604, -3.13634, -58.33627, -3.13247, -58.33649, -3.12848, -58.33673, -3.12417, -58.337, -3.11956, -58.33472, -3.15889, -58.33488, -3.15704, -58.33502, -3.15479, -58.33516, -3.15212, -58.33532, -3.14952, -58.33548, -3.14663, -58.33564, -3.14349, -58.33587, -3.14006, -58.33604, -3.13634, -58.33627, -3.13247, -58.33649, -3.12848, -58.33673, -3.12417, -58.337, -3.11956, -58.33472, -3.15889, -58.33488, -3.15704, -58.33502, -3.15479, -58.33516, -3.15212, -58.33532, -3.14952, -58.33548, -3.14663, -58.33564, -3.14349, -58.33587, -3.14006, -58.3346, -3.16127, -58.33466, -3.16119, -58.33469, -3.16026, -58.33472, -3.15889, -58.33488, -3.15704, -58.33502, -3.15479, -58.33516, -3.15212, -58.33532, -3.14952, -58.33472, -3.15987, -58.3346, -3.16127, -58.33466, -3.16119, -58.33469, -3.16026, -58.33472, -3.15889, -58.33488, -3.15704, -58.33502, -3.15479, -58.33516, -3.15212, -58.33532, -3.14952, -58.3346, -3.16127, -58.33466, -3.16119, -58.33469, -3.16026, -58.33472, -3.15889, -58.33488, -3.15704, -58.33502, -3.15479, -58.33516, -3.15212, -58.33532, -3.14952, -58.33862, -3.09125, -58.33573, -3.14189, -58.3349, -3.15633, -58.33472, -3.15987, -58.3346, -3.16127, -58.33466, -3.16119, -58.33469, -3.16026, -58.33472, -3.15889, -58.33862, -3.09125, -58.33573, -3.14189, -58.3349, -3.15633, -58.33472, -3.15987, -58.3346, -3.16127, -58.33466, -3.16119, -58.33469, -3.16026, -58.33472, -3.15889, -58.33573, -3.14189, -58.3349, -3.15633, -58.33472, -3.15987, -58.3346, -3.16127, -58.33466, -3.16119, -58.33469, -3.16026, -58.33472, -3.15889, -58.33862, -3.09125, -58.33573, -3.14189, -58.3349, -3.15633, -58.33472, -3.15987, -58.3346, -3.16127, -58.33466, -3.16119, -58.33469, -3.16026, -58.33472, -3.15889, -58.33862, -3.09125, -58.33573, -3.14189, -58.3349, -3.15633, -58.33472, -3.15987, -58.3346, -3.16127, -58.33466, -3.16119, -58.33469, -3.16026, -58.33472, -3.15889]}, {"time": 2, "vertices": [-58.3387, -2.96935, -58.3358, -3.01999, -58.3387, -2.96935, -58.3358, -3.01999, -58.3387, -2.96935, -58.3358, -3.01999, -58.33498, -3.03443, -58.3348, -3.03797, -58.33468, -3.03937, -58.3387, -2.96935, -58.3358, -3.01999, -58.33498, -3.03443, -58.3348, -3.03797, -58.33468, -3.03937, -58.33474, -3.03929, -58.33476, -3.03835, -58.3348, -3.03699, -58.3348, -3.03797, -58.33468, -3.03937, -58.33474, -3.03929, -58.33476, -3.03835, -58.3348, -3.03699, -58.3348, -3.03797, -58.33468, -3.03937, -58.33474, -3.03929, -58.33476, -3.03835, -58.3348, -3.03699, -58.33495, -3.03514, -58.33509, -3.03289, -58.33524, -3.03022, -58.33539, -3.02762, -58.3348, -3.03699, -58.33495, -3.03514, -58.33509, -3.03289, -58.33524, -3.03022, -58.33539, -3.02762, -58.3348, -3.03699, -58.33495, -3.03514, -58.33509, -3.03289, -58.33524, -3.03022, -58.33539, -3.02762, -58.33556, -3.02473, -58.33572, -3.02159, -58.33594, -3.01815, -58.33613, -3.01444, -58.33524, -3.03022, -58.33539, -3.02762, -58.33556, -3.02473, -58.33572, -3.02159, -58.33594, -3.01815, -58.33613, -3.01444, -58.33572, -3.02159, -58.33594, -3.01815, -58.33613, -3.01444, -58.33634, -3.01057, -58.33656, -3.00658, -58.33572, -3.02159, -58.33594, -3.01815, -58.33613, -3.01444, -58.33634, -3.01057, -58.33656, -3.00658, -58.33683, -3.00227, -58.33709, -2.99765, -58.33613, -3.01444, -58.33634, -3.01057, -58.33656, -3.00658, -58.33683, -3.00227, -58.33709, -2.99765, -58.33656, -3.00658, -58.33683, -3.00227, -58.33709, -2.99765, -58.33656, -3.00658, -58.33683, -3.00227, -58.33709, -2.99765, -58.33613, -3.01444, -58.33634, -3.01057, -58.33656, -3.00658, -58.33683, -3.00227, -58.33709, -2.99765, -58.33572, -3.02159, -58.33594, -3.01815, -58.33613, -3.01444, -58.33634, -3.01057, -58.33656, -3.00658, -58.33524, -3.03022, -58.33539, -3.02762, -58.33556, -3.02473, -58.33572, -3.02159, -58.33594, -3.01815, -58.3348, -3.03699, -58.33495, -3.03514, -58.33509, -3.03289, -58.33524, -3.03022, -58.33539, -3.02762, -58.33468, -3.03937, -58.33474, -3.03929, -58.33476, -3.03835, -58.3348, -3.03699, -58.33495, -3.03514, -58.3358, -3.01999, -58.33498, -3.03443, -58.3348, -3.03797, -58.33468, -3.03937, -58.3387, -2.96935, -58.3358, -3.01999, -58.3387, -2.96935, -58.3358, -3.01999, -58.33498, -3.03443, -58.3387, -2.96935, -58.3358, -3.01999, -58.33498, -3.03443, -58.3387, -2.96935, -58.3358, -3.01999, -58.33498, -3.03443, -58.3348, -3.03797, -58.33468, -3.03937, -58.3358, -3.01999, -58.33498, -3.03443, -58.3348, -3.03797, -58.33468, -3.03937, -58.3348, -3.03797, -58.33468, -3.03937, -58.33474, -3.03929, -58.33476, -3.03835, -58.3348, -3.03699, -58.33468, -3.03937, -58.33474, -3.03929, -58.33476, -3.03835, -58.3348, -3.03699, -58.3348, -3.03699, -58.33495, -3.03514, -58.33509, -3.03289, -58.33524, -3.03022, -58.33539, -3.02762, -58.3348, -3.03699, -58.33495, -3.03514, -58.33509, -3.03289, -58.33524, -3.03022, -58.33539, -3.02762, -58.33524, -3.03022, -58.33539, -3.02762, -58.33556, -3.02473, -58.33572, -3.02159, -58.33594, -3.01815, -58.33524, -3.03022, -58.33539, -3.02762, -58.33556, -3.02473, -58.33572, -3.02159, -58.33594, -3.01815, -58.33572, -3.02159, -58.33594, -3.01815, -58.33613, -3.01444, -58.33634, -3.01057, -58.33656, -3.00658, -58.33572, -3.02159, -58.33594, -3.01815, -58.33613, -3.01444, -58.33634, -3.01057, -58.33656, -3.00658, -58.33634, -3.01057, -58.33656, -3.00658, -58.33683, -3.00227, -58.33709, -2.99765, -58.33634, -3.01057, -58.33656, -3.00658, -58.33683, -3.00227, -58.33709, -2.99765, -58.33634, -3.01057, -58.33656, -3.00658, -58.33683, -3.00227, -58.33709, -2.99765, -58.33634, -3.01057, -58.33656, -3.00658, -58.33683, -3.00227, -58.33709, -2.99765, -58.33634, -3.01057, -58.33656, -3.00658, -58.33683, -3.00227, -58.33709, -2.99765, -58.33572, -3.02159, -58.33594, -3.01815, -58.33613, -3.01444, -58.33634, -3.01057, -58.33656, -3.00658, -58.33683, -3.00227, -58.33709, -2.99765, -58.33572, -3.02159, -58.33594, -3.01815, -58.33613, -3.01444, -58.33634, -3.01057, -58.33656, -3.00658, -58.33683, -3.00227, -58.33709, -2.99765, -58.33572, -3.02159, -58.33594, -3.01815, -58.33613, -3.01444, -58.33634, -3.01057, -58.33656, -3.00658, -58.33683, -3.00227, -58.33709, -2.99765, -58.33572, -3.02159, -58.33594, -3.01815, -58.33613, -3.01444, -58.33634, -3.01057, -58.33656, -3.00658, -58.33683, -3.00227, -58.33709, -2.99765, -58.33524, -3.03022, -58.33539, -3.02762, -58.33556, -3.02473, -58.33572, -3.02159, -58.33594, -3.01815, -58.33613, -3.01444, -58.33634, -3.01057, -58.33656, -3.00658, -58.33524, -3.03022, -58.33539, -3.02762, -58.33556, -3.02473, -58.33572, -3.02159, -58.33594, -3.01815, -58.33613, -3.01444, -58.33634, -3.01057, -58.33656, -3.00658, -58.33683, -3.00227, -58.33709, -2.99765, -58.33524, -3.03022, -58.33539, -3.02762, -58.33556, -3.02473, -58.33572, -3.02159, -58.33594, -3.01815, -58.33613, -3.01444, -58.33634, -3.01057, -58.33656, -3.00658, -58.33683, -3.00227, -58.33709, -2.99765, -58.3348, -3.03699, -58.33495, -3.03514, -58.33509, -3.03289, -58.33524, -3.03022, -58.33539, -3.02762, -58.33556, -3.02473, -58.33572, -3.02159, -58.33594, -3.01815, -58.33613, -3.01444, -58.33634, -3.01057, -58.33656, -3.00658, -58.33683, -3.00227, -58.33709, -2.99765, -58.3348, -3.03699, -58.33495, -3.03514, -58.33509, -3.03289, -58.33524, -3.03022, -58.33539, -3.02762, -58.33556, -3.02473, -58.33572, -3.02159, -58.33594, -3.01815, -58.33613, -3.01444, -58.33634, -3.01057, -58.33656, -3.00658, -58.33683, -3.00227, -58.33709, -2.99765, -58.3348, -3.03699, -58.33495, -3.03514, -58.33509, -3.03289, -58.33524, -3.03022, -58.33539, -3.02762, -58.33556, -3.02473, -58.33572, -3.02159, -58.33594, -3.01815, -58.33613, -3.01444, -58.33634, -3.01057, -58.33656, -3.00658, -58.33683, -3.00227, -58.33709, -2.99765, -58.3348, -3.03699, -58.33495, -3.03514, -58.33509, -3.03289, -58.33524, -3.03022, -58.33539, -3.02762, -58.33556, -3.02473, -58.33572, -3.02159, -58.33594, -3.01815, -58.33468, -3.03937, -58.33474, -3.03929, -58.33476, -3.03835, -58.3348, -3.03699, -58.33495, -3.03514, -58.33509, -3.03289, -58.33524, -3.03022, -58.33539, -3.02762, -58.3348, -3.03797, -58.33468, -3.03937, -58.33474, -3.03929, -58.33476, -3.03835, -58.3348, -3.03699, -58.33495, -3.03514, -58.33509, -3.03289, -58.33524, -3.03022, -58.33539, -3.02762, -58.33468, -3.03937, -58.33474, -3.03929, -58.33476, -3.03835, -58.3348, -3.03699, -58.33495, -3.03514, -58.33509, -3.03289, -58.33524, -3.03022, -58.33539, -3.02762, -58.3387, -2.96935, -58.3358, -3.01999, -58.33498, -3.03443, -58.3348, -3.03797, -58.33468, -3.03937, -58.33474, -3.03929, -58.33476, -3.03835, -58.3348, -3.03699, -58.3387, -2.96935, -58.3358, -3.01999, -58.33498, -3.03443, -58.3348, -3.03797, -58.33468, -3.03937, -58.33474, -3.03929, -58.33476, -3.03835, -58.3348, -3.03699, -58.3358, -3.01999, -58.33498, -3.03443, -58.3348, -3.03797, -58.33468, -3.03937, -58.33474, -3.03929, -58.33476, -3.03835, -58.3348, -3.03699, -58.3387, -2.96935, -58.3358, -3.01999, -58.33498, -3.03443, -58.3348, -3.03797, -58.33468, -3.03937, -58.33474, -3.03929, -58.33476, -3.03835, -58.3348, -3.03699, -58.3387, -2.96935, -58.3358, -3.01999, -58.33498, -3.03443, -58.3348, -3.03797, -58.33468, -3.03937, -58.33474, -3.03929, -58.33476, -3.03835, -58.3348, -3.03699]}]}}}}}}