{"skeleton": {"hash": "wsYUXLa2b9n+PkZl1WZQWQlWIR4", "spine": "3.8.99", "x": -129.48, "y": -187.03, "width": 254.56, "height": 366.57, "images": "./images/", "audio": "E:/work/inspire lad/xoc dia/anim/icon"}, "bones": [{"name": "root"}, {"name": "AllText", "parent": "root"}, {"name": "bone", "parent": "AllText", "x": -55.75, "y": -87.83, "scaleX": 1.1, "scaleY": 1.1}, {"name": "bone18", "parent": "AllText", "x": 60.5, "y": -87.83, "scaleX": 1.1, "scaleY": 1.1}, {"name": "bone2", "parent": "bone18"}, {"name": "All", "parent": "root", "x": -0.15, "y": -12.95}, {"name": "bone3", "parent": "All", "x": -2.67, "y": 3.36}, {"name": "bone4", "parent": "All", "x": -39.35, "y": 26.08}, {"name": "bone5", "parent": "All", "x": -13.05, "y": 63.08}, {"name": "bone6", "parent": "All", "x": 30.77, "y": 23.81}, {"name": "bone7", "parent": "All", "x": 82.4, "y": 173.07}, {"name": "bone8", "parent": "All", "x": -94.61, "y": 129.36}, {"name": "bone9", "parent": "All", "x": -73.9, "y": -147.41}, {"name": "bone14", "parent": "All", "x": -1.03, "y": 17.69}, {"name": "bone19", "parent": "All", "scaleY": 0.3585}, {"name": "bone15", "parent": "bone19"}, {"name": "<PERSON>", "parent": "root", "x": 62.07, "y": 112.12}, {"name": "bone17", "parent": "bone"}, {"name": "bone22", "parent": "All", "x": 37.06, "y": 167.07}, {"name": "bone23", "parent": "All", "x": -76.07, "y": -52.22}, {"name": "bone24", "parent": "All", "rotation": 30.71, "x": -14.06, "y": -96.33}, {"name": "bone25", "parent": "All", "rotation": 30.71, "x": 18.14, "y": -66.06}, {"name": "bone26", "parent": "All", "rotation": 30.71, "x": 95.01, "y": -53.06}, {"name": "bone27", "parent": "All", "rotation": 30.71, "x": 103.6, "y": -95.79}, {"name": "bone28", "parent": "All", "rotation": 30.71, "x": 64.32, "y": 92.84}], "slots": [{"name": "bg", "bone": "bone14", "attachment": "bg"}, {"name": "chip", "bone": "bone7", "attachment": "chip"}, {"name": "table", "bone": "All", "attachment": "table"}, {"name": "fx_xoay2", "bone": "bone15", "attachment": "fx_xoay2"}, {"name": "chip2", "bone": "bone9", "attachment": "chip2"}, {"name": "dia", "bone": "All", "attachment": "dia"}, {"name": "sd_chip2", "bone": "All", "attachment": "sd_chip2"}, {"name": "sd_chip1", "bone": "All", "attachment": "sd_chip1"}, {"name": "sd_chip3", "bone": "All", "attachment": "sd_chip3"}, {"name": "xx1", "bone": "bone6", "attachment": "xx1"}, {"name": "xx2", "bone": "bone4", "attachment": "xx2"}, {"name": "xx4", "bone": "bone5", "attachment": "xx4"}, {"name": "xx3", "bone": "bone3", "attachment": "xx3"}, {"name": "chen", "bone": "<PERSON>", "attachment": "chen"}, {"name": "chip3", "bone": "bone8", "attachment": "chip3"}, {"name": "text_dia", "bone": "bone18", "attachment": "text_dia"}, {"name": "text_xoc", "bone": "bone", "attachment": "text_xoc"}, {"name": "dia_light", "bone": "bone2", "color": "ffffff00", "attachment": "dia_light"}, {"name": "xoc_light", "bone": "bone17", "color": "ffffff00", "attachment": "xoc_light"}, {"name": "sao1", "bone": "bone22", "attachment": "sao1"}, {"name": "sao3", "bone": "bone24", "attachment": "sao1"}, {"name": "sao4", "bone": "bone25", "attachment": "sao1"}, {"name": "sao6", "bone": "bone27", "attachment": "sao1"}, {"name": "sao7", "bone": "bone28", "attachment": "sao1"}, {"name": "sao2", "bone": "bone23", "attachment": "sao1"}, {"name": "sao5", "bone": "bone26", "attachment": "sao1"}], "skins": [{"name": "default", "attachments": {"bg": {"bg": {"x": 1.77, "y": -4.26, "width": 192, "height": 353}}, "chen": {"chen": {"type": "mesh", "uvs": [0.30834, 0, 0.13515, 0.04171, 0.03837, 0.0952, 0, 0.17444, 0, 0.29924, 0, 0.38629, 0, 0.45771, 0.04359, 0.49984, 0.07644, 0.53159, 0.11478, 0.56865, 0.17733, 0.61782, 0.23801, 0.66264, 0.30959, 0.71763, 0.38155, 0.76906, 0.43918, 0.80651, 0.5119, 0.85377, 0.58673, 0.88983, 0.65753, 0.92394, 0.70656, 0.94757, 0.74, 0.96368, 0.77528, 0.98069, 0.90772, 1, 0.98923, 0.90541, 1, 0.7608, 1, 0.57718, 1, 0.48347, 1, 0.2299, 0.94848, 0.05558, 0.70906, 0, 0.52229, 0, 0.07063, 0.32895, 0.20284, 0.36388, 0.35501, 0.44372, 0.50021, 0.53695, 0.63568, 0.63505, 0.74302, 0.7291, 0.8483, 0.85192, 0.87207, 0.93909, 0.06766, 0.39112, 0.17819, 0.41772, 0.33496, 0.50475, 0.47586, 0.59903, 0.60019, 0.69572, 0.7183, 0.79001, 0.81983, 0.90363, 0.83019, 0.94956, 0.17629, 0.17764, 0.26849, 0.24789, 0.43219, 0.31594, 0.56014, 0.41912, 0.72384, 0.50913, 0.84615, 0.64743, 0.92706, 0.76816, 0.30988, 0.09862, 0.47359, 0.18203, 0.62224, 0.26765, 0.79723, 0.41693, 0.92706, 0.54205, 0.61095, 0.11837, 0.75959, 0.19521, 0.87437, 0.33131, 0.28288, 0.40207, 0.25501, 0.45406, 0.12794, 0.3386, 0.11328, 0.39562, 0.43217, 0.49682, 0.41017, 0.54956, 0.56576, 0.5818, 0.53071, 0.64207, 0.68937, 0.68081, 0.6534, 0.73785, 0.78992, 0.77982, 0.75579, 0.83148, 0.82405, 0.81748, 0.78807, 0.87022], "triangles": [53, 0, 29, 1, 0, 53, 58, 29, 28, 46, 1, 53, 2, 1, 46, 54, 53, 29, 54, 29, 58, 59, 28, 27, 58, 28, 59, 59, 27, 26, 47, 46, 53, 47, 53, 54, 55, 58, 59, 54, 58, 55, 3, 30, 4, 46, 3, 2, 48, 47, 54, 48, 54, 55, 46, 30, 3, 60, 59, 26, 63, 30, 46, 31, 63, 46, 47, 31, 46, 5, 4, 30, 38, 5, 30, 64, 38, 30, 63, 64, 30, 61, 47, 48, 31, 47, 61, 56, 59, 60, 55, 59, 56, 39, 63, 31, 64, 63, 39, 49, 48, 55, 50, 49, 55, 32, 61, 48, 62, 31, 61, 39, 31, 62, 6, 5, 38, 60, 26, 25, 65, 32, 48, 65, 48, 49, 7, 6, 38, 32, 62, 61, 40, 32, 65, 40, 62, 32, 56, 50, 55, 64, 7, 38, 8, 7, 64, 33, 65, 49, 57, 60, 25, 56, 60, 57, 66, 40, 65, 66, 65, 33, 39, 8, 64, 9, 8, 39, 57, 25, 24, 67, 49, 50, 33, 49, 67, 41, 66, 33, 41, 33, 67, 62, 10, 9, 62, 9, 39, 34, 67, 50, 68, 41, 67, 51, 56, 57, 50, 56, 51, 69, 34, 50, 11, 10, 62, 11, 62, 40, 51, 69, 50, 42, 67, 34, 70, 42, 34, 68, 67, 42, 12, 11, 40, 12, 40, 66, 35, 69, 51, 69, 70, 34, 70, 69, 35, 51, 57, 24, 24, 52, 51, 23, 52, 24, 13, 12, 66, 13, 66, 41, 14, 13, 41, 71, 35, 51, 71, 51, 52, 43, 70, 35, 43, 35, 71, 68, 14, 41, 15, 14, 68, 73, 71, 52, 72, 43, 71, 72, 71, 73, 36, 73, 52, 42, 15, 68, 16, 15, 42, 74, 72, 73, 74, 73, 36, 70, 16, 42, 16, 70, 43, 44, 74, 36, 22, 52, 23, 36, 52, 22, 17, 16, 43, 17, 43, 72, 37, 36, 22, 44, 36, 37, 18, 17, 72, 18, 72, 74, 45, 44, 37, 19, 18, 74, 20, 19, 74, 44, 20, 74, 45, 20, 44, 21, 37, 22, 45, 37, 21, 20, 45, 21], "vertices": [-38.67, 53.58, -64.13, 48.33, -78.35, 41.59, -83.99, 31.6, -83.99, 15.88, -83.99, 4.91, -83.99, -4.09, -77.59, -9.4, -72.76, -13.4, -67.12, -18.07, -57.93, -24.26, -49.01, -29.91, -38.48, -36.84, -27.91, -43.32, -19.43, -48.04, -8.74, -53.99, 2.26, -58.53, 12.66, -62.83, 19.87, -65.81, 24.79, -67.84, 29.97, -69.98, 49.44, -72.42, 61.42, -60.5, 63.01, -42.28, 63.01, -19.14, 63.01, -7.33, 63.01, 24.62, 55.43, 46.58, 20.24, 53.58, -7.22, 53.58, -73.61, 12.14, -54.18, 7.74, -31.81, -2.33, -10.46, -14.07, 9.45, -26.43, 25.23, -38.28, 40.71, -53.76, 44.2, -64.74, -74.05, 4.3, -57.8, 0.95, -34.76, -10.01, -14.04, -21.89, 4.23, -34.08, 21.6, -45.96, 36.52, -60.27, 38.04, -66.06, -58.08, 31.2, -44.53, 22.35, -20.46, 13.77, -1.65, 0.77, 22.41, -10.57, 40.39, -27.99, 52.28, -43.2, -38.44, 41.16, -14.38, 30.65, 7.47, 19.86, 33.2, 1.05, 52.28, -14.72, 5.82, 38.67, 27.67, 28.99, 44.54, 11.84, -42.41, 2.92, -46.51, -3.63, -65.19, 10.92, -67.34, 3.74, -20.47, -9.02, -23.7, -15.66, -0.83, -19.72, -5.98, -27.32, 17.34, -32.2, 12.06, -39.38, 32.12, -44.67, 27.11, -51.18, 37.14, -49.42, 31.85, -56.06], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 40, 42, 42, 44, 44, 46, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58, 8, 10, 10, 12, 46, 48, 48, 50, 22, 24, 12, 14, 18, 20, 20, 22, 14, 16, 16, 18, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40], "width": 147, "height": 126}}, "chip": {"chip": {"x": -0.16, "y": -3.58, "width": 49, "height": 40}}, "chip2": {"chip2": {"x": 1.14, "y": -0.17, "width": 67, "height": 53}}, "chip3": {"chip3": {"x": -3.72, "y": -2.44, "width": 62, "height": 54}}, "dia": {"dia": {"x": 4.74, "y": -7.01, "width": 216, "height": 73}}, "dia_light": {"dia_light": {"x": 0.47, "y": 8.28, "width": 103, "height": 62}}, "fx_xoay2": {"fx_xoay2": {"path": "fx_xoay", "width": 243, "height": 260}}, "sao1": {"sao1": {"x": -0.32, "y": -0.08, "width": 52, "height": 51}}, "sao2": {"sao1": {"x": -0.32, "y": -0.08, "scaleX": 0.6045, "scaleY": 0.6045, "width": 52, "height": 51}}, "sao3": {"sao1": {"x": -0.46, "y": -0.09, "scaleX": 0.6045, "scaleY": 0.6045, "width": 52, "height": 51}}, "sao4": {"sao1": {"x": -0.46, "y": -0.09, "scaleX": 0.6045, "scaleY": 0.6045, "width": 52, "height": 51}}, "sao5": {"sao1": {"x": -0.46, "y": -0.09, "scaleX": 0.6045, "scaleY": 0.6045, "width": 52, "height": 51}}, "sao6": {"sao1": {"x": -0.46, "y": -0.09, "scaleX": 0.6045, "scaleY": 0.6045, "width": 52, "height": 51}}, "sao7": {"sao1": {"x": -0.46, "y": -0.09, "scaleX": 0.6045, "scaleY": 0.6045, "width": 52, "height": 51}}, "sd_chip1": {"sd_chip1": {"x": -1.76, "y": -5.51, "width": 45, "height": 12}}, "sd_chip2": {"sd_chip2": {"x": 24.24, "y": 5.99, "width": 31, "height": 9}}, "sd_chip3": {"sd_chip3": {"x": -37.76, "y": 8.49, "width": 19, "height": 8}}, "table": {"table": {"x": -6.76, "y": -61.51, "scaleX": 0.9526, "scaleY": 0.9526, "width": 239, "height": 136}}, "text_dia": {"text_dia": {"x": 1.59, "y": 1.39, "width": 100, "height": 68}}, "text_xoc": {"text_xoc": {"x": 3.14, "y": 2.39, "width": 118, "height": 72}}, "xoc_light": {"xoc_light": {"x": 6.36, "y": 8.04, "width": 117, "height": 66}}, "xx1": {"xx1": {"x": -1.03, "y": -1.82, "width": 36, "height": 25}}, "xx2": {"xx2": {"x": 0.09, "y": 0.41, "width": 34, "height": 24}}, "xx3": {"xx3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [18.41, -13.87, -19.59, -13.87, -19.59, 12.13, 18.41, 12.13], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 38, "height": 26}}, "xx4": {"xx4": {"x": -2.21, "y": -1.6, "width": 36, "height": 28}}}}], "animations": {"animation": {"slots": {"dia_light": {"color": [{"color": "ffffff52"}, {"time": 0.7333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffff52"}]}, "xoc_light": {"color": [{"color": "ffffff52"}, {"time": 0.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1, "color": "ffffff00"}, {"time": 1.5, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2333, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffff52"}]}}, "bones": {"bone15": {"rotate": [{}, {"time": 1.6667, "angle": 180}, {"time": 3.3333}]}, "bone4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -26.79, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": -26.79, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}], "translate": [{"y": 7.53, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": -5.18, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": 7.53, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "y": 1.18, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "y": 7.53}]}, "bone5": {"rotate": [{"angle": 7, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 16.41, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "angle": 16.41, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 3.3333, "angle": 7}], "translate": [{"y": 15.67, "curve": 0.379, "c2": 0.53, "c3": 0.746}, {"time": 0.3667, "y": 12.51, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "y": 19.92, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "y": 12.51, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "y": 19.92, "curve": 0.247, "c3": 0.631, "c4": 0.53}, {"time": 3.3333, "y": 15.67}]}, "bone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -9, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "angle": -9, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}], "translate": [{"y": 4.84, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": 16.49, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": 4.84, "curve": 0.25, "c3": 0.75}, {"time": 2.5333, "y": 16.49, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "y": 4.84}]}, "bone6": {"rotate": [{"angle": 6.62}, {"time": 0.3667}, {"time": 1.1, "angle": 14.9}, {"time": 2.0333}, {"time": 2.8667, "angle": 14.9}, {"time": 3.3333, "angle": 6.62}], "translate": [{"y": -2.12}, {"time": 0.3667}, {"time": 1.1, "y": -4.77}, {"time": 2.0333}, {"time": 2.8667, "y": -4.77}, {"time": 3.3333, "y": -2.12}]}, "bone8": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -1.83, "y": 9.46, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -1.83, "y": 9.46, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "x": -1.83, "y": 9.46, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 1.037, "y": 1.037, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.037, "y": 1.037, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "x": 1.037, "y": 1.037, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "bone9": {"translate": [{"x": 1.85, "y": -4.87}, {"time": 0.9}, {"time": 1.5667, "x": -1.08, "y": -4.87}, {"time": 2.4}, {"time": 3.3333, "x": 1.85, "y": -4.87}], "scale": [{"x": 1.099, "y": 1.099}, {"time": 0.9}, {"time": 1.5667, "x": 1.099, "y": 1.099}, {"time": 2.4}, {"time": 3.3333, "x": 1.099, "y": 1.099}]}, "bone": {"translate": [{"time": 1.1}, {"time": 1.3, "y": 15.23}, {"time": 1.4333}]}, "bone18": {"translate": [{"time": 1.3}, {"time": 1.5, "y": 13.2}, {"time": 1.6}]}, "bone7": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -2.35, "y": -8.63, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "x": -2.35, "y": -8.63, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 0.891, "y": 0.891, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "x": 0.891, "y": 0.891, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "bone22": {"rotate": [{"time": 0.9333}, {"time": 2.8, "angle": -49.34}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9333, "x": 0, "y": 0}, {"time": 1.0333, "curve": "stepped"}, {"time": 1.6667}, {"time": 2.8, "x": 0, "y": 0}]}, "bone23": {"rotate": [{"time": 0.3667}, {"time": 1.1, "angle": -49.34}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3667, "x": 0, "y": 0}, {"time": 0.4667, "x": 2, "y": 2}, {"time": 1.1, "x": 0, "y": 0}]}, "bone24": {"rotate": [{"time": 1.7}, {"time": 2.8, "angle": -49.34}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 1.7, "x": 0, "y": 0}, {"time": 1.8333, "x": 2, "y": 2}, {"time": 2.8, "x": 0, "y": 0}]}, "bone25": {"rotate": [{"time": 0.1}, {"time": 0.9, "angle": -49.34}, {"time": 1.8667}, {"time": 2.8, "angle": -49.34}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1, "x": 0, "y": 0}, {"time": 0.2333, "x": 2, "y": 2}, {"time": 0.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8667, "x": 0, "y": 0}, {"time": 1.9667, "x": 2, "y": 2}, {"time": 2.8, "x": 0, "y": 0}]}, "bone26": {"rotate": [{"time": 2.0333}, {"time": 2.9667, "angle": -49.34}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 2.0333, "x": 0, "y": 0}, {"time": 2.1667, "x": 2, "y": 2}, {"time": 2.9667, "x": 0, "y": 0}]}, "bone27": {"rotate": [{"time": 2.4}, {"time": 3.3333, "angle": -49.34}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 2.4, "x": 0, "y": 0}, {"time": 2.5333}, {"time": 3.3333, "x": 0, "y": 0}]}, "bone28": {"rotate": [{"time": 1.4333}, {"time": 2.8, "angle": -49.34}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4333, "x": 0, "y": 0}, {"time": 1.5333, "curve": "stepped"}, {"time": 2.1667}, {"time": 2.8, "x": 0, "y": 0}]}, "Chen": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -3.03, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -3.03, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": 5.09, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "y": 5.09, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "bone19": {"scale": [{"x": 0.959, "y": 0.959}]}}}}}