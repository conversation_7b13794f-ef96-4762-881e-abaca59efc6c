<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>BG_PanelS_half.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{237,416}</string>
                <key>spriteSourceSize</key>
                <string>{237,416}</string>
                <key>textureRect</key>
                <string>{{0,908},{237,416}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>BoardG_70.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{362,364}</string>
                <key>spriteSourceSize</key>
                <string>{362,364}</string>
                <key>textureRect</key>
                <string>{{0,544},{362,364}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Btn_Home_hover.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{73,73}</string>
                <key>spriteSourceSize</key>
                <string>{73,73}</string>
                <key>textureRect</key>
                <string>{{0,70},{73,73}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Btn_Info_normal.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{73,73}</string>
                <key>spriteSourceSize</key>
                <string>{73,73}</string>
                <key>textureRect</key>
                <string>{{73,70},{73,73}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Btn_Minus_Normal.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{68,68}</string>
                <key>spriteSourceSize</key>
                <string>{68,68}</string>
                <key>textureRect</key>
                <string>{{30,0},{68,68}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Btn_Plus_Hover.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{68,68}</string>
                <key>spriteSourceSize</key>
                <string>{68,68}</string>
                <key>textureRect</key>
                <string>{{98,0},{68,68}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Btn_Setting_normal.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{73,73}</string>
                <key>spriteSourceSize</key>
                <string>{73,73}</string>
                <key>textureRect</key>
                <string>{{146,70},{73,73}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Btn_Turbo_off.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{140,80}</string>
                <key>spriteSourceSize</key>
                <string>{140,80}</string>
                <key>textureRect</key>
                <string>{{0,147},{140,80}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Btn_Turbo_on.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{30,30}</string>
                <key>spriteSourceSize</key>
                <string>{30,30}</string>
                <key>textureRect</key>
                <string>{{0,0},{30,30}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Frame_JP_Major.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{215,108}</string>
                <key>spriteSourceSize</key>
                <string>{215,108}</string>
                <key>textureRect</key>
                <string>{{0,430},{215,108}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Frame_JP_Mini.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{210,95}</string>
                <key>spriteSourceSize</key>
                <string>{210,95}</string>
                <key>textureRect</key>
                <string>{{209,240},{210,95}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Frame_Money.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{158,77}</string>
                <key>spriteSourceSize</key>
                <string>{158,77}</string>
                <key>textureRect</key>
                <string>{{219,70},{158,77}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Frame_Title_half.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{300,95}</string>
                <key>spriteSourceSize</key>
                <string>{300,95}</string>
                <key>textureRect</key>
                <string>{{0,335},{300,95}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Frame_TotalWin_half.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{160,114}</string>
                <key>spriteSourceSize</key>
                <string>{160,114}</string>
                <key>textureRect</key>
                <string>{{215,430},{160,114}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Frame_Wallet.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{218,70}</string>
                <key>spriteSourceSize</key>
                <string>{218,70}</string>
                <key>textureRect</key>
                <string>{{166,0},{218,70}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>JP_Grand.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{209,93}</string>
                <key>spriteSourceSize</key>
                <string>{209,93}</string>
                <key>textureRect</key>
                <string>{{140,147},{209,93}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>JP_Minor.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{209,93}</string>
                <key>spriteSourceSize</key>
                <string>{209,93}</string>
                <key>textureRect</key>
                <string>{{0,240},{209,93}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>UI_main.png</string>
            <key>size</key>
            <string>{419,1324}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:d45c77f2cbc0df9b96e34ad7266e6154:20c851bf23f3710b60f9de3dc8498c16:acbe9b6c07b16272c141c31cab483b27$</string>
            <key>textureFileName</key>
            <string>UI_main.png</string>
        </dict>
    </dict>
</plist>
