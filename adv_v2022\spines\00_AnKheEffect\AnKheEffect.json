{"skeleton": {"hash": "gIOsC/6ZtqB4OwBpmoO/KpIfUjE", "spine": "3.7.93", "width": 309.24, "height": 449.54, "images": "", "audio": ""}, "bones": [{"name": "root", "scaleX": 0.475, "scaleY": 0.475}, {"name": "bone", "parent": "root"}, {"name": "bone2", "parent": "root", "x": 25.47, "y": 119.71}, {"name": "bone4", "parent": "root", "x": 67.91, "y": 252.85}, {"name": "TAY", "parent": "root", "x": -124.01, "y": 6.54}, {"name": "bone6", "parent": "TAY", "x": 93.45, "y": 100.7}, {"name": "bone7", "parent": "TAY", "x": 93.45, "y": 105.23}, {"name": "bone8", "parent": "TAY", "rotation": 4.8, "x": 104.78, "y": 107.5}, {"name": "bone9", "parent": "root", "x": -2.55, "y": -119.71, "scaleX": 0.925, "scaleY": 0.847, "shearY": -0.06}, {"name": "bone10", "parent": "root", "y": -277.81}, {"name": "bone11", "parent": "root", "x": -48.39, "y": 170.65}, {"name": "bone12", "parent": "root", "x": -61.13}, {"name": "bone13", "parent": "root", "x": 28.02, "y": -109.52}, {"name": "bone14", "parent": "root", "x": -68.77, "y": 387.14}, {"name": "bone17", "parent": "root", "x": -338.75, "y": -127.35}, {"name": "bone16", "parent": "root", "x": -239.57, "y": 20.61}, {"name": "bone20", "parent": "root", "x": 205.71, "y": -76.19}, {"name": "bone21", "parent": "root", "x": -246.83, "y": 252.93, "scaleX": 0.5, "scaleY": 0.5}, {"name": "bone18", "parent": "root", "x": -230.86, "y": 327.6, "scaleX": 0.5, "scaleY": 0.5}, {"name": "bone19", "parent": "root", "x": 243.05, "y": 311.06, "scaleX": 0.5, "scaleY": 0.5}, {"name": "bone22", "parent": "root", "x": -151.47, "y": 403.68, "scaleX": 0.5, "scaleY": 0.5}], "slots": [{"name": "6", "bone": "bone", "attachment": "6"}, {"name": "5", "bone": "bone14", "attachment": "5"}, {"name": "16", "bone": "bone18", "attachment": "5"}, {"name": "17", "bone": "bone19", "attachment": "5"}, {"name": "20", "bone": "bone22", "attachment": "5"}, {"name": "10", "bone": "bone2", "attachment": "10"}, {"name": "11", "bone": "bone4", "attachment": "11"}, {"name": "8", "bone": "bone6", "attachment": "8"}, {"name": "14", "bone": "bone8", "attachment": "14"}, {"name": "9", "bone": "bone7", "attachment": "9"}, {"name": "7", "bone": "TAY", "attachment": "7"}, {"name": "1", "bone": "bone9", "attachment": "1"}, {"name": "13", "bone": "bone10", "attachment": "13"}, {"name": "2", "bone": "bone12", "attachment": "2"}, {"name": "3", "bone": "bone13", "attachment": "3"}, {"name": "4", "bone": "bone11", "attachment": "4"}, {"name": "15", "bone": "bone16", "attachment": "4"}, {"name": "18", "bone": "bone20", "attachment": "4"}, {"name": "19", "bone": "bone21", "attachment": "4"}], "skins": {"default": {"1": {"1": {"scaleX": 0.927, "scaleY": 0.947, "width": 609, "height": 219}}, "10": {"10": {"scaleX": 1.8, "scaleY": 1.8, "rotation": 0.41, "width": 265, "height": 327}}, "11": {"11": {"width": 161, "height": 43}}, "13": {"13": {"scaleX": 2.08, "scaleY": 2.08, "width": 254, "height": 117}}, "14": {"14": {"x": -139.61, "y": 59.37, "scaleX": 2, "scaleY": 2, "width": 107, "height": 81}}, "15": {"4": {"width": 102, "height": 82}}, "16": {"5": {"width": 75, "height": 77}}, "17": {"5": {"width": 75, "height": 77}}, "18": {"4": {"width": 102, "height": 82}}, "19": {"4": {"width": 102, "height": 82}}, "2": {"2": {"width": 416, "height": 147}}, "20": {"5": {"width": 75, "height": 77}}, "3": {"3": {"width": 473, "height": 140}}, "4": {"4": {"width": 102, "height": 82}}, "5": {"5": {"width": 75, "height": 77}}, "6": {"6": {"scaleX": 2.08, "scaleY": 2.08, "width": 313, "height": 455}}, "7": {"7": {"scaleX": 1.8, "scaleY": 1.8, "width": 100, "height": 179}}, "8": {"8": {"x": -116.69, "y": 60.27, "scaleX": 1.8, "scaleY": 1.8, "width": 121, "height": 113}}, "9": {"9": {"x": -116.69, "y": 60.27, "scaleX": 1.8, "scaleY": 1.8, "width": 121, "height": 113}}}}, "animations": {"animation": {"slots": {"11": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}, {"time": 1.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7667, "color": "ffffff00"}, {"time": 1.9, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}]}, "14": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 1, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff"}]}, "15": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffff33"}]}, "16": {"color": [{"time": 0, "color": "ffffff00"}]}, "17": {"color": [{"time": 0, "color": "ffffff00"}]}, "18": {"color": [{"time": 0, "color": "ffffff00"}]}, "19": {"color": [{"time": 0, "color": "ffffff00"}]}, "20": {"color": [{"time": 0, "color": "ffffff00"}]}, "4": {"color": [{"time": 0, "color": "ffffff33"}, {"time": 0.3333, "color": "ffffffff"}, {"time": 1, "color": "ffffff34"}, {"time": 1.5667, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff34"}]}, "5": {"color": [{"time": 0, "color": "ffffff00"}]}, "8": {"color": [{"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}]}, "9": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00"}]}}, "bones": {"bone11": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3333, "angle": -90}, {"time": 1, "angle": 131.7}, {"time": 1.5667, "angle": -90}, {"time": 2.2333, "angle": 131.7}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 1.2667, "x": -107.79, "y": 37.49}, {"time": 1.5667, "x": -107.8, "y": 37.49}, {"time": 2.2333, "x": -107.79, "y": 37.49}], "scale": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": 1.1, "y": 1.1}, {"time": 1, "x": 0, "y": 0}, {"time": 1.5667, "x": 1.1, "y": 1.1}, {"time": 2.2333, "x": 0, "y": 0}]}, "bone16": {"scale": [{"time": 0.8333, "x": 0, "y": 0}]}, "bone14": {"translate": [{"time": 0, "x": 0, "y": 92.62}]}, "bone18": {"translate": [{"time": 0, "x": 0, "y": 92.62}]}, "bone19": {"translate": [{"time": 0, "x": 0, "y": 92.62}]}, "bone22": {"translate": [{"time": 0, "x": 0, "y": 92.62}]}, "TAY": {"rotate": [{"time": 0, "angle": -14.2}, {"time": 1.9, "angle": -24.7}, {"time": 2.2333, "angle": -14.2}], "translate": [{"time": 0, "x": -3.31, "y": -3.31}, {"time": 1, "x": -19.85, "y": -3.31, "curve": "stepped"}, {"time": 1.3333, "x": -19.85, "y": -3.31}, {"time": 1.9, "x": -26.64, "y": 1.22}, {"time": 2.2333, "x": -3.31, "y": -3.31}], "scale": [{"time": 0, "x": 1, "y": 0.934}, {"time": 1, "x": 1, "y": 1}, {"time": 2.2333, "x": 1, "y": 0.934}]}}}}}