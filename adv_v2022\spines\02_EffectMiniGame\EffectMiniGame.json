{"skeleton": {"hash": "8WyhskZXxm4P8Nnx5Ahqy6/bmNo", "spine": "3.7.94", "width": 137.47, "height": 134.67, "images": "", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root"}, {"name": "bone2", "parent": "root"}, {"name": "bone3", "parent": "root", "x": -110.37, "y": 20.9}, {"name": "bone4", "parent": "root", "x": -37.81, "y": 36}, {"name": "MASK", "parent": "root", "x": -4.68, "y": 89.2}, {"name": "bone5", "parent": "root", "x": -47.48, "y": 19.7}, {"name": "bone6", "parent": "root", "x": -52.02, "y": 0.9}, {"name": "bone7", "parent": "root", "x": -47.61, "y": -17.73}, {"name": "bone8", "parent": "root", "x": -37.38, "y": -34.02}, {"name": "bone9", "parent": "root", "x": -21.02, "y": -44.85}, {"name": "bone10", "parent": "root", "x": -2.88, "y": -48.14}, {"name": "bone11", "parent": "root", "x": 16.8, "y": -44.07}, {"name": "bone12", "parent": "root", "x": 31.83, "y": -33.01}, {"name": "bone13", "parent": "root", "x": 41.4, "y": -16.97}, {"name": "bone14", "parent": "root", "x": 46.15, "y": 1.3}, {"name": "bone15", "parent": "root", "x": 42.8, "y": 20.02}, {"name": "bone16", "parent": "root", "x": 32.3, "y": 35.91}, {"name": "bone17", "parent": "root", "x": 42.8, "y": 20.02}, {"name": "bone18", "parent": "root", "x": 15.19, "y": 46.7}, {"name": "bone19", "parent": "root", "x": -2.73, "y": 50.53}, {"name": "bone20", "parent": "root", "x": -21.7, "y": 46.7}], "slots": [{"name": "1", "bone": "bone", "attachment": "1"}, {"name": "2", "bone": "bone2", "attachment": "2"}, {"name": "Light", "bone": "bone4", "attachment": "Light"}, {"name": "Light2", "bone": "bone5", "attachment": "Light"}, {"name": "Light3", "bone": "bone6", "attachment": "Light"}, {"name": "Light4", "bone": "bone7", "attachment": "Light"}, {"name": "Light5", "bone": "bone8", "attachment": "Light"}, {"name": "Light6", "bone": "bone9", "attachment": "Light"}, {"name": "Light7", "bone": "bone10", "attachment": "Light"}, {"name": "Light8", "bone": "bone11", "attachment": "Light"}, {"name": "Light9", "bone": "bone12", "attachment": "Light"}, {"name": "Light10", "bone": "bone13", "attachment": "Light"}, {"name": "Light11", "bone": "bone14", "attachment": "Light"}, {"name": "Light13", "bone": "bone16", "attachment": "Light"}, {"name": "Light16", "bone": "bone19", "attachment": "Light"}, {"name": "Light12", "bone": "bone15", "attachment": "Light"}, {"name": "Light15", "bone": "bone18", "attachment": "Light"}, {"name": "Light17", "bone": "bone20", "attachment": "Light"}, {"name": "Light14", "bone": "bone17", "attachment": "Light"}, {"name": "MASK", "bone": "MASK", "attachment": "MASK"}, {"name": "Blur", "bone": "bone3", "attachment": "Blur"}], "skins": {"default": {"1": {"1": {"width": 119, "height": 119}}, "2": {"2": {"width": 105, "height": 105}}, "Blur": {"Blur": {"x": 5.75, "scaleX": 0.482, "rotation": -7.16, "width": 178, "height": 134}}, "Light": {"Light": {"x": 3.6, "y": 3.6, "scaleX": 0.3, "scaleY": 0.3, "width": 131, "height": 120}}, "Light10": {"Light": {"x": 3.6, "y": 3.6, "scaleX": 0.3, "scaleY": 0.3, "width": 131, "height": 120}}, "Light11": {"Light": {"x": 3.6, "y": 3.6, "scaleX": 0.3, "scaleY": 0.3, "width": 131, "height": 120}}, "Light12": {"Light": {"x": 3.6, "y": 3.6, "scaleX": 0.3, "scaleY": 0.3, "width": 131, "height": 120}}, "Light13": {"Light": {"x": 3.6, "y": 3.6, "scaleX": 0.3, "scaleY": 0.3, "width": 131, "height": 120}}, "Light14": {"Light": {"x": 3.6, "y": 3.6, "scaleX": 0.3, "scaleY": 0.3, "width": 131, "height": 120}}, "Light15": {"Light": {"x": 3.6, "y": 3.6, "scaleX": 0.3, "scaleY": 0.3, "width": 131, "height": 120}}, "Light16": {"Light": {"x": 3.6, "y": 3.6, "scaleX": 0.3, "scaleY": 0.3, "width": 131, "height": 120}}, "Light17": {"Light": {"x": 3.6, "y": 3.6, "scaleX": 0.3, "scaleY": 0.3, "width": 131, "height": 120}}, "Light2": {"Light": {"x": 3.6, "y": 3.6, "scaleX": 0.3, "scaleY": 0.3, "width": 131, "height": 120}}, "Light3": {"Light": {"x": 3.6, "y": 3.6, "scaleX": 0.3, "scaleY": 0.3, "width": 131, "height": 120}}, "Light4": {"Light": {"x": 3.6, "y": 3.6, "scaleX": 0.3, "scaleY": 0.3, "width": 131, "height": 120}}, "Light5": {"Light": {"x": 3.6, "y": 3.6, "scaleX": 0.3, "scaleY": 0.3, "width": 131, "height": 120}}, "Light6": {"Light": {"x": 3.6, "y": 3.6, "scaleX": 0.3, "scaleY": 0.3, "width": 131, "height": 120}}, "Light7": {"Light": {"x": 3.6, "y": 3.6, "scaleX": 0.3, "scaleY": 0.3, "width": 131, "height": 120}}, "Light8": {"Light": {"x": 3.6, "y": 3.6, "scaleX": 0.3, "scaleY": 0.3, "width": 131, "height": 120}}, "Light9": {"Light": {"x": 3.6, "y": 3.6, "scaleX": 0.3, "scaleY": 0.3, "width": 131, "height": 120}}, "MASK": {"MASK": {"type": "clipping", "end": "MASK", "vertexCount": 34, "vertices": [-42.31, -78.98, -40.59, -70.63, -36.23, -61.31, -31.26, -53.99, -23.73, -47.28, -15.62, -43.06, -4.74, -39.53, 4.85, -39.17, 10.71, -39.55, 14.76, -40.12, 19.04, -41.17, 25.19, -43.39, 33.22, -48.74, 40.13, -54.77, 44.67, -61.16, 49.72, -71.33, 51.14, -80.08, 52.18, -88.94, 50.1, -98.54, 46.83, -106.67, 43.65, -112.96, 36.99, -120.36, 30.32, -125.38, 22.36, -129.71, 13.87, -132.09, 3.76, -133.82, -7.07, -132.22, -14.88, -129.06, -22.06, -125.52, -28.23, -120.23, -34.01, -113.77, -38.29, -106.5, -40.55, -98.58, -42.2, -88.94], "color": "ce3a3a00"}}}}, "animations": {"animation": {"slots": {"2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}, {"time": 1.3, "color": "ffffff00"}]}, "Blur": {"color": [{"time": 0.9667, "color": "ffffff76"}]}, "Light": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff"}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5667, "color": "ffffff00"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 1.0333, "color": "ffffff00"}]}, "Light2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.0333, "color": "ffffff00"}, {"time": 0.2, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}, {"time": 0.7667, "color": "ffffffff"}, {"time": 1.0667, "color": "ffffff00"}]}, "Light3": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.0667, "color": "ffffff00"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6333, "color": "ffffff00"}, {"time": 0.8, "color": "ffffffff"}, {"time": 1.1, "color": "ffffff00"}]}, "Light4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1, "color": "ffffff00"}, {"time": 0.2667, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00"}]}, "Light5": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00"}, {"time": 0.3, "color": "ffffffff"}, {"time": 0.6, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7, "color": "ffffff00"}, {"time": 0.8667, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00"}]}, "Light6": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff"}, {"time": 0.6333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7333, "color": "ffffff00"}, {"time": 0.9, "color": "ffffffff"}, {"time": 1.2, "color": "ffffff00"}]}, "Light7": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7667, "color": "ffffff00"}, {"time": 0.9333, "color": "ffffffff"}, {"time": 1.2333, "color": "ffffff00"}]}, "Light8": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffff00"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.7, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}, {"time": 1.2667, "color": "ffffff00"}]}, "Light9": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2667, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}, {"time": 1.3, "color": "ffffff00"}]}, "Light10": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8667, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}]}, "Light11": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9, "color": "ffffff00"}, {"time": 1.0667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}]}, "Light12": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3667, "color": "ffffff00"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9333, "color": "ffffff00"}, {"time": 1.1, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}]}, "Light13": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9667, "color": "ffffff00"}, {"time": 1.1333, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}]}, "Light14": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4333, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff"}, {"time": 0.9, "color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}]}, "Light15": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4667, "color": "ffffff00"}, {"time": 0.6333, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0333, "color": "ffffff00"}, {"time": 1.2, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}]}, "Light16": {"color": [{"time": 0.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0667, "color": "ffffff00"}, {"time": 1.2333, "color": "ffffffff"}]}, "Light17": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffff00"}, {"time": 0.7, "color": "ffffffff"}, {"time": 1, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1, "color": "ffffff00"}, {"time": 1.2667, "color": "ffffffff"}]}}, "bones": {"bone3": {"translate": [{"time": 0.8, "x": 0, "y": 0}, {"time": 1.1333, "x": 186.19, "y": 0}]}, "bone4": {"scale": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1667, "x": 1, "y": 1}, {"time": 0.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5667, "x": 0, "y": 0}, {"time": 0.7333, "x": 1, "y": 1}, {"time": 1.0333, "x": 0, "y": 0}]}, "bone5": {"scale": [{"time": 0.0333, "x": 0, "y": 0}, {"time": 0.2, "x": 1, "y": 1}, {"time": 0.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0}, {"time": 0.7667, "x": 1, "y": 1}, {"time": 1.0667, "x": 0, "y": 0}]}, "bone6": {"scale": [{"time": 0.0667, "x": 0, "y": 0}, {"time": 0.2333, "x": 1, "y": 1}, {"time": 0.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6333, "x": 0, "y": 0}, {"time": 0.8, "x": 1, "y": 1}, {"time": 1.1, "x": 0, "y": 0}]}, "bone7": {"scale": [{"time": 0.1, "x": 0, "y": 0}, {"time": 0.2667, "x": 1, "y": 1}, {"time": 0.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 0.8333, "x": 1, "y": 1}, {"time": 1.1333, "x": 0, "y": 0}]}, "bone8": {"scale": [{"time": 0.1333, "x": 0, "y": 0}, {"time": 0.3, "x": 1, "y": 1}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 0}, {"time": 0.8667, "x": 1, "y": 1}, {"time": 1.1667, "x": 0, "y": 0}]}, "bone9": {"scale": [{"time": 0.1667, "x": 0, "y": 0}, {"time": 0.3333, "x": 1, "y": 1}, {"time": 0.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7333, "x": 0, "y": 0}, {"time": 0.9, "x": 1, "y": 1}, {"time": 1.2, "x": 0, "y": 0}]}, "bone10": {"scale": [{"time": 0.2, "x": 0, "y": 0}, {"time": 0.3667, "x": 1, "y": 1}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7667, "x": 0, "y": 0}, {"time": 0.9333, "x": 1, "y": 1}, {"time": 1.2333, "x": 0, "y": 0}]}, "bone11": {"scale": [{"time": 0.2333, "x": 0, "y": 0}, {"time": 0.4, "x": 1, "y": 1}, {"time": 0.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}, {"time": 0.9667, "x": 1, "y": 1}, {"time": 1.2667, "x": 0, "y": 0}]}, "bone12": {"scale": [{"time": 0.2667, "x": 0, "y": 0}, {"time": 0.4333, "x": 1, "y": 1}, {"time": 0.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1, "x": 1, "y": 1}, {"time": 1.3, "x": 0, "y": 0}]}, "bone13": {"scale": [{"time": 0.3, "x": 0, "y": 0}, {"time": 0.4667, "x": 1, "y": 1}, {"time": 0.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8667, "x": 0, "y": 0}, {"time": 1.0333, "x": 1, "y": 1}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone14": {"scale": [{"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5, "x": 1, "y": 1}, {"time": 0.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9, "x": 0, "y": 0}, {"time": 1.0667, "x": 1, "y": 1}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone15": {"scale": [{"time": 0.3667, "x": 0, "y": 0}, {"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9333, "x": 0, "y": 0}, {"time": 1.1, "x": 1, "y": 1}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone16": {"scale": [{"time": 0.4, "x": 0, "y": 0}, {"time": 0.5667, "x": 1, "y": 1}, {"time": 0.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.1333, "x": 1, "y": 1}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone17": {"scale": [{"time": 0.4333, "x": 0, "y": 0}, {"time": 0.6, "x": 1, "y": 1}, {"time": 0.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}, {"time": 1.1667, "x": 1, "y": 1}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone18": {"scale": [{"time": 0.4667, "x": 0, "y": 0}, {"time": 0.6333, "x": 1, "y": 1}, {"time": 0.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0333, "x": 0, "y": 0}, {"time": 1.2, "x": 1, "y": 1}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone19": {"scale": [{"time": 0.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}, {"time": 0.6667, "x": 1, "y": 1}, {"time": 0.9667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0667, "x": 0, "y": 0}, {"time": 1.2333, "x": 1, "y": 1}]}, "bone20": {"scale": [{"time": 0.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.7, "x": 1, "y": 1}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1, "x": 0, "y": 0}, {"time": 1.2667, "x": 1, "y": 1}]}}}}}