{"skeleton": {"hash": "ZCuFx0l/ExX4irE4SN6VzralMxs", "spine": "3.7.93", "width": 279.48, "height": 421.8, "images": "", "audio": "/Users/<USER>/Trung/longshin/file Ve/tayduky/MaingameItems/wildexpand"}, "bones": [{"name": "root", "scaleX": 0.6, "scaleY": 0.6}, {"name": "bone", "parent": "root", "length": 35.85, "rotation": 92.16, "x": -6.5, "y": 0.94}, {"name": "bone2", "parent": "bone", "length": 106.19, "rotation": -7.19, "x": 35.85}, {"name": "bone3", "parent": "bone2", "length": 71.33, "rotation": -5.77, "x": 157.22, "y": 5.07}, {"name": "bone4", "parent": "bone2", "length": 77.36, "rotation": 123.23, "x": 109.8, "y": 43.46}, {"name": "bone5", "parent": "bone4", "length": 56.61, "rotation": -81.17, "x": 85.51, "y": 0.21}, {"name": "bone6", "parent": "bone5", "length": 28.66, "rotation": 11.08, "x": 67.73, "y": -1.62}, {"name": "bone7", "parent": "bone6", "length": 72.5, "rotation": -132.39, "x": -27.9, "y": -44.46}, {"name": "bone8", "parent": "bone2", "length": 96.77, "rotation": -127.73, "x": 117.72, "y": -64.26}, {"name": "bone9", "parent": "bone8", "length": 40.8, "rotation": -144.23, "x": 114.62, "y": -13.67}, {"name": "bone10", "parent": "bone9", "length": 49.4, "rotation": -24.73, "x": 40.8}, {"name": "bone11", "parent": "bone10", "length": 17.08, "rotation": 21.42, "x": 53.8, "y": 4.51}, {"name": "bone58", "parent": "bone2", "length": 69.7, "rotation": -148.62, "x": 50.92, "y": -52.3}, {"name": "bone59", "parent": "bone58", "length": 72.77, "rotation": 10.97, "x": 69.7}, {"name": "bone60", "parent": "bone59", "length": 92.89, "rotation": 13.69, "x": 72.77}, {"name": "bone61", "parent": "bone60", "length": 126.51, "rotation": 4.51, "x": 92.8, "y": -0.81}, {"name": "bone62", "parent": "bone2", "length": 38.78, "rotation": 166.06, "x": 100.42, "y": 58.47}, {"name": "bone63", "parent": "bone62", "length": 58.16, "rotation": 24.62, "x": 41.86, "y": -0.15}, {"name": "bone64", "parent": "bone63", "length": 86.58, "rotation": 24.98, "x": 58.16}, {"name": "bone65", "parent": "bone64", "length": 87.7, "rotation": 7.73, "x": 87.85, "y": -0.09}, {"name": "bone66", "parent": "bone65", "length": 84.57, "rotation": -10.93, "x": 87.7}, {"name": "bone67", "parent": "bone", "length": 138.08, "rotation": -170.08, "x": -38.08, "y": -7.06}, {"name": "bone68", "parent": "bone67", "length": 115.38, "rotation": 6.65, "x": 138.08}, {"name": "bone69", "parent": "bone", "length": 139.25, "rotation": 173.18, "x": -27.44, "y": 8.25}, {"name": "bone70", "parent": "bone69", "length": 105.16, "rotation": 12.21, "x": 141.13, "y": 0.15}, {"name": "bone71", "parent": "bone", "length": 12.58, "rotation": 174.98, "x": -15.42, "y": 43.62}, {"name": "bone72", "parent": "bone71", "length": 42.87, "rotation": -2.18, "x": 12.58}, {"name": "bone73", "parent": "bone72", "length": 58.42, "rotation": 3.81, "x": 42.93, "y": -0.63}, {"name": "bone74", "parent": "bone73", "length": 67.21, "rotation": 0.16, "x": 59.67, "y": 0.03}, {"name": "bone75", "parent": "bone74", "length": 58.43, "rotation": 2.92, "x": 67.21}, {"name": "bone76", "parent": "bone", "length": 59.25, "rotation": -152.71, "x": -14.54, "y": -31.84}, {"name": "bone77", "parent": "bone76", "length": 125.8, "rotation": 8.69, "x": 60.08, "y": 0.23}, {"name": "bone78", "parent": "bone", "length": 45.64, "rotation": 163.99, "x": -24.48, "y": 59.04}, {"name": "bone79", "parent": "bone78", "length": 51.9, "rotation": 3.07, "x": 45.05, "y": -0.15}, {"name": "bone80", "parent": "bone3", "x": 39.8, "y": 0.8}, {"name": "bone81", "parent": "bone3", "length": 6.46, "rotation": 52.94, "x": 33.63, "y": 6.35}, {"name": "bone82", "parent": "bone81", "length": 8.16, "rotation": -6.23, "x": 6.46}, {"name": "bone83", "parent": "bone82", "length": 7.14, "rotation": 27.53, "x": 8.16}, {"name": "bone84", "parent": "bone3", "length": 7.69, "rotation": -67.22, "x": 37.18, "y": -7.36}, {"name": "bone85", "parent": "bone84", "length": 8.32, "rotation": -2.51, "x": 7.69}, {"name": "bone86", "parent": "bone85", "length": 5.63, "rotation": -41.22, "x": 8.28, "y": -0.22}, {"name": "bone87", "parent": "bone2", "length": 4.03, "rotation": -129.97, "x": 104.31, "y": 24.49}, {"name": "bone88", "parent": "bone87", "length": 21.9, "rotation": -37.53, "x": 4.53, "y": -0.5}, {"name": "bone89", "parent": "bone88", "length": 26.7, "rotation": -8.24, "x": 21.9}, {"name": "bone90", "parent": "bone89", "length": 37.74, "rotation": 0.22, "x": 26.7}, {"name": "bone91", "parent": "bone2", "length": 3.83, "rotation": 116.83, "x": 107.18, "y": 36.54}, {"name": "bone92", "parent": "bone91", "length": 25.82, "rotation": 53.01, "x": 3.83}, {"name": "bone93", "parent": "bone92", "length": 32.2, "rotation": 9.48, "x": 26.6, "y": -0.16}, {"name": "bone94", "parent": "bone93", "length": 30.72, "rotation": 1.06, "x": 33.62, "y": 0.14}, {"name": "bone95", "parent": "bone3", "length": 1.68, "rotation": 169, "x": 58.88, "y": 1.07}, {"name": "bone96", "parent": "bone95", "length": 7.34, "rotation": 9.54, "x": 2.46, "y": 0.14}, {"name": "bone97", "parent": "bone96", "length": 9.37, "rotation": 14.17, "x": 7.34}, {"name": "bone98", "parent": "bone10", "length": 15.57, "rotation": -1.97, "x": 48.69, "y": -13.31}, {"name": "bone99", "parent": "bone98", "length": 14.44, "rotation": -0.56, "x": 15.57}, {"name": "bone100", "parent": "bone3", "length": 20.82, "rotation": -1.73, "x": 53.64, "y": 43.69}, {"name": "bone101", "parent": "bone3", "length": 12.49, "rotation": -10.17, "x": 42.07, "y": -42.82}, {"name": "bone105", "parent": "root", "scaleX": 1.6, "scaleY": 1.6}, {"name": "bone106", "parent": "root", "scaleX": 1.598, "scaleY": 1.598}, {"name": "bone12", "parent": "root", "length": 17.32, "rotation": 90, "x": -0.75, "y": -90.68, "scaleX": 1.6, "scaleY": 1.6}, {"name": "bone13", "parent": "root", "length": 9.41, "rotation": 90, "x": 1.52, "y": -87.29, "scaleX": 1.7, "scaleY": 1.7}], "slots": [{"name": "BGfire0", "bone": "bone105", "attachment": "BGfire0"}, {"name": "khungicongameGo", "bone": "root", "attachment": "khungicongameGo"}, {"name": "bannertayduky2", "bone": "root", "attachment": "bannertayduky1"}, {"name": "domdom2", "bone": "bone13"}, {"name": "path", "bone": "bone106", "attachment": "path"}, {"name": "Monkeyking25", "bone": "root", "attachment": "Monkeyking25"}, {"name": "Monkeyking23", "bone": "root", "attachment": "Monkeyking23"}, {"name": "Monkeyking24", "bone": "root", "attachment": "Monkeyking24"}, {"name": "Monkeyking19", "bone": "root", "attachment": "Monkeyking19"}, {"name": "Monkeyking18", "bone": "root", "attachment": "Monkeyking18"}, {"name": "Monkeyking6", "bone": "root", "attachment": "Monkeyking6"}, {"name": "Monkeyking17", "bone": "root", "attachment": "Monkeyking17"}, {"name": "khoi", "bone": "root", "color": "2727bbff", "attachment": "khoi", "blend": "screen"}, {"name": "Monkeyking2", "bone": "root", "attachment": "Monkeyking2"}, {"name": "Monkeyking22", "bone": "root", "attachment": "Monkeyking22"}, {"name": "Monkeyking3", "bone": "root", "attachment": "Monkeyking3"}, {"name": "Monkeyking1", "bone": "root", "attachment": "Monkeyking1"}, {"name": "Monkeyking16", "bone": "root", "attachment": "Monkeyking16"}, {"name": "Monkeyking15", "bone": "root", "attachment": "Monkeyking15"}, {"name": "Monkeyking14", "bone": "root", "attachment": "Monkeyking14"}, {"name": "Monkeyking13", "bone": "root", "attachment": "Monkeyking13"}, {"name": "Monkeyking11", "bone": "root", "attachment": "Monkeyking11"}, {"name": "khungicongameGo2", "bone": "root", "attachment": "khungicongameGo"}, {"name": "Monkeyking10", "bone": "root", "attachment": "Monkeyking10"}, {"name": "Monkeyking12", "bone": "root", "attachment": "Monkeyking12"}, {"name": "Monkeyking21", "bone": "root", "attachment": "Monkeyking21"}, {"name": "Monkeyking7", "bone": "root", "attachment": "Monkeyking7"}, {"name": "Monkeyking8", "bone": "root", "attachment": "Monkeyking8"}, {"name": "Monkeyking5", "bone": "root", "attachment": "Monkeyking5"}, {"name": "Monkeyking4", "bone": "root", "attachment": "Monkeyking4"}, {"name": "Monkeyking9", "bone": "root", "attachment": "Monkeyking9"}, {"name": "bannertayduky1", "bone": "root", "attachment": "bannertayduky1"}, {"name": "do<PERSON><PERSON>", "bone": "bone13"}], "skins": {"default": {"BGfire0": {"BGfire0": {"type": "mesh", "uvs": [0, 0.07129, 0.0937, 0.05196, 0.49463, 0, 0.77762, 0.01611, 1, 0.07093, 1, 0.71871, 0, 0.72073], "triangles": [3, 4, 5, 6, 0, 1, 6, 1, 2, 5, 6, 2, 5, 2, 3], "vertices": [-130.29, 157.87, -104.12, 191.17, -2.99, 209.46, 101.09, 194.01, 129.99, 157.97, 134.55, -156.84, -131.18, -157.86], "hull": 7, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 0, 12, 0, 2, 2, 4], "width": 82, "height": 200}}, "Monkeyking1": {"Monkeyking1": {"type": "mesh", "uvs": [0.30995, 0.00102, 0.28224, 0.09855, 0.16584, 0.15057, 0.10487, 0.20258, 0, 0.37294, 1e-05, 0.5628, 0.04021, 0.69545, 0.00141, 0.80988, 0.05868, 1, 0.24159, 1, 0.36722, 0.89441, 0.42501, 0.8898, 0.74228, 0.8645, 0.70348, 0.72015, 0.84205, 0.59661, 0.8365, 0.52639, 1, 0.36124, 1, 0.19868, 0.879, 0.11155, 0.70533, 0.02963, 0.65729, 0, 0.71087, 0.15447, 0.6111, 0.19738, 0.55013, 0.2442, 0.47069, 0.41455, 0.42635, 0.60832, 0.41711, 0.75266], "triangles": [26, 25, 13, 26, 6, 25, 14, 13, 15, 6, 5, 25, 25, 5, 24, 3, 24, 4, 24, 3, 2, 15, 13, 24, 13, 25, 24, 24, 2, 1, 24, 5, 4, 24, 23, 15, 23, 22, 15, 22, 21, 15, 16, 21, 18, 16, 15, 21, 24, 1, 23, 18, 17, 16, 22, 23, 0, 23, 1, 0, 21, 20, 19, 21, 22, 20, 22, 0, 20, 21, 19, 18, 10, 9, 7, 9, 8, 7, 7, 6, 10, 10, 26, 11, 10, 6, 26, 11, 13, 12, 11, 26, 13], "vertices": [1, 2, 166.47, 31.99, 1, 1, 2, 143.43, 34.53, 1, 1, 2, 129.68, 52.48, 1, 1, 2, 116.73, 61.38, 1, 1, 2, 75.69, 75.03, 1, 2, 1, 76.13, 66.63, 0.04228, 2, 31.62, 71.15, 0.95772, 2, 1, 45, 61.21, 0.32065, 2, 1.41, 61.87, 0.67935, 2, 1, 18.59, 68.58, 0.69127, 2, -25.71, 65.88, 0.30873, 2, 1, -26.03, 60.86, 0.97316, 2, -69.01, 52.64, 0.02684, 2, 1, -27.16, 30.88, 0.99942, 2, -66.38, 22.75, 0.00058, 2, 1, -3.35, 9.37, 0.99902, 2, -40.07, 4.39, 0.00098, 1, 1, -2.64, -0.14, 1, 2, 1, 1.29, -52.36, 0.81667, 2, -27.74, -56.27, 0.18333, 2, 1, 35.14, -47.27, 0.39485, 2, 5.21, -46.99, 0.60515, 2, 1, 63.05, -71.06, 0.04138, 2, 35.88, -67.1, 0.95862, 2, 1, 79.43, -70.77, 0.01085, 2, 52.1, -64.76, 0.98915, 1, 2, 92.78, -88.1, 1, 1, 2, 130.51, -84.78, 1, 1, 2, 148.99, -63.23, 1, 1, 2, 165.51, -33.19, 1, 1, 2, 171.7, -24.73, 1, 1, 2, 136.61, -36.64, 1, 1, 2, 125.22, -21.22, 1, 1, 2, 113.48, -12.21, 1, 1, 2, 72.79, -2.72, 1, 1, 2, 27.18, 0.57, 1, 2, 1, 29.34, -0.05, 0.99943, 2, -6.45, -0.87, 0.00057], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 36, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 20, 22, 22, 24, 52, 22], "width": 164, "height": 233}}, "Monkeyking10": {"Monkeyking10": {"type": "mesh", "uvs": [0, 0.45147, 0.26653, 0.17848, 0.59875, 0, 0.87319, 0, 1, 0.42547, 1, 1, 0.90689, 1, 0.878, 0.76347, 0.80097, 0.63347, 0.60356, 0.64647, 0.30023, 0.78947, 0, 0.93247], "triangles": [4, 8, 2, 7, 8, 4, 5, 6, 7, 5, 7, 4, 4, 2, 3, 9, 1, 2, 8, 9, 2, 10, 0, 1, 9, 10, 1, 11, 0, 10], "vertices": [1, 38, -4.05, 2.18, 1, 1, 38, 3.56, 3.36, 1, 1, 39, 4.87, 3.46, 1, 2, 39, 12.17, 2.24, 0.72651, 40, 1.3, 4.42, 0.27349, 2, 39, 14.85, -2.52, 0.037, 40, 6.45, 2.6, 0.963, 1, 40, 9.48, -2.28, 1, 1, 40, 7.34, -3.61, 1, 1, 40, 5.43, -2.01, 1, 1, 40, 2.98, -2, 1, 3, 38, 11.49, -3.11, 0.00739, 39, 3.93, -2.94, 0.8206, 40, -1.48, -4.91, 0.17201, 1, 38, 3.18, -2.81, 1, 1, 38, -5.05, -2.53, 1], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22], "width": 27, "height": 10}}, "Monkeyking11": {"Monkeyking11": {"type": "mesh", "uvs": [0.86319, 1, 1, 0.88838, 0.82605, 0.63522, 0.58462, 0.24523, 0.37415, 0, 0.10177, 0, 0, 0.15628, 0, 0.2726, 0.17605, 0.31365, 0.43605, 0.60786, 0.64033, 0.8268], "triangles": [8, 7, 6, 8, 5, 4, 8, 4, 3, 8, 6, 5, 9, 8, 3, 9, 3, 2, 10, 9, 2, 0, 10, 2, 1, 0, 2], "vertices": [1, 35, -1.61, 1.78, 1, 1, 35, -1.97, -1.77, 1, 2, 35, 4.05, -2.29, 0.9181, 36, -2.14, -2.54, 0.0819, 2, 36, 6.83, -2.78, 0.91903, 37, -2.46, -1.85, 0.08097, 1, 37, 3.57, -4.04, 1, 1, 37, 8.69, -1.48, 1, 1, 37, 9.27, 2.13, 1, 1, 37, 8.28, 4.11, 1, 2, 36, 10.81, 4.93, 0.00453, 37, 4.63, 3.15, 0.99547, 3, 35, 9.93, 3.43, 0.00024, 36, 3.08, 3.79, 0.93287, 37, -2.75, 5.71, 0.06689, 2, 35, 3.97, 3.04, 0.89354, 36, -2.8, 2.75, 0.10646], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "width": 21, "height": 19}}, "Monkeyking12": {"Monkeyking12": {"type": "mesh", "uvs": [0, 0, 1, 0.36691, 1, 0.52977, 0.88455, 0.8012, 0.61011, 1, 0.35677, 1, 0.09816, 0.57727, 0, 0.20406], "triangles": [2, 6, 1, 3, 6, 2, 4, 5, 6, 3, 4, 6, 1, 7, 0, 6, 7, 1], "vertices": [3, 49, 2.38, -19.67, 0.89829, 50, -3.37, -19.53, 0.02926, 51, -15.17, -16.31, 0.07245, 3, 49, -1.45, 17.57, 0.30396, 50, -0.98, 17.83, 0.39527, 51, -3.7, 19.32, 0.30077, 3, 49, 2.78, 19.26, 0.27245, 50, 3.48, 18.8, 0.37648, 51, 0.86, 19.17, 0.35107, 3, 49, 11.38, 18.22, 0.14339, 50, 11.79, 16.35, 0.26338, 51, 8.31, 14.77, 0.59323, 3, 49, 20.22, 11.12, 0.01077, 50, 19.33, 7.88, 0.02908, 51, 13.55, 4.71, 0.96016, 2, 49, 23.6, 2.65, 0.00155, 51, 13.24, -4.41, 0.99845, 3, 49, 16.07, -10.39, 0.36391, 50, 11.68, -12.64, 0.14633, 51, 1.1, -13.32, 0.48975, 3, 49, 7.68, -17.55, 0.82974, 50, 2.22, -18.32, 0.05865, 51, -9.46, -16.5, 0.11161], "hull": 8, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 0, 14], "width": 36, "height": 28}}, "Monkeyking13": {"Monkeyking13": {"type": "mesh", "uvs": [0.1682, 0.94912, 0.20985, 1, 0.29317, 1, 0.51577, 0.93912, 0.66808, 0.82037, 0.74358, 0.50912, 0.685, 0.35412, 0.64855, 0.32662, 0.73317, 0.29662, 0.9037, 0.24663, 1, 0.22663, 1, 0.12413, 0.85814, 0.11538, 0.65506, 0.16788, 0.58476, 0.08038, 0.41553, 0.09538, 0.32831, 0.05288, 0.27624, 0, 0.22938, 0.00163, 0.22287, 0.12538, 0.31399, 0.23038, 0.27885, 0.26163, 0.19423, 0.24538, 0.08879, 0.35287, 0, 0.52912, 0, 0.71537, 0.05755, 0.88912, 0.05755, 0.97162, 0.10701, 0.99787, 0.36743, 0.75995, 0.38834, 0.74991, 0.4109, 0.74885, 0.43457, 0.7515, 0.45493, 0.76259, 0.46869, 0.77844, 0.45163, 0.79535, 0.43182, 0.80381, 0.4087, 0.80117, 0.39109, 0.79271, 0.37569, 0.77632, 0.26398, 0.73829, 0.24637, 0.72138, 0.22656, 0.70764, 0.2084, 0.69496, 0.18914, 0.68914, 0.17318, 0.68492, 0.17428, 0.70922, 0.18694, 0.72712, 0.20785, 0.73714, 0.23096, 0.74296, 0.25022, 0.74299, 0.25682, 0.73142, 0.27773, 0.7367, 0.26508, 0.71768, 0.24362, 0.68967, 0.22601, 0.66854, 0.19684, 0.65427, 0.16272, 0.65585, 0.13741, 0.67488, 0.13851, 0.71926, 0.15887, 0.76465, 0.21115, 0.78711, 0.26067, 0.77035, 0.27718, 0.75196, 0.33606, 0.76941, 0.34762, 0.74674, 0.36798, 0.72666, 0.4109, 0.71292, 0.45383, 0.70764, 0.48354, 0.72085, 0.5006, 0.76788, 0.48134, 0.80539, 0.43952, 0.84185, 0.37514, 0.83868, 0.34102, 0.8036, 0.20179, 0.86488, 0.2128, 0.8872, 0.23866, 0.90268, 0.26453, 0.90915, 0.28269, 0.91983, 0.308, 0.92259, 0.32781, 0.91626, 0.1449, 0.87275, 0.17075, 0.92647, 0.20198, 0.97466, 0.22728, 0.99054, 0.26286, 0.99583, 0.31363, 0.98071, 0.37357, 0.94616, 0.39635, 0.93367, 0.18155, 0.53433, 0.24339, 0.56808, 0.43149, 0.63058, 0.5109, 0.6337, 0.5428, 0.63683, 0.16987, 0.56351, 0.17831, 0.59594, 0.14714, 0.6134, 0.12051, 0.67389, 0.09843, 0.64833, 0.07505, 0.6502, 0.05492, 0.67202, 0.05362, 0.70819, 0.07115, 0.76058, 0.09778, 0.78604, 0.14519, 0.80341, 0.15623, 0.83814, 0.51982, 0.66065, 0.52525, 0.70964, 0.52188, 0.76692, 0.55406, 0.7657, 0.56983, 0.78085, 0.56542, 0.80993, 0.52567, 0.85052, 0.49539, 0.86384, 0.44618, 0.86505, 0.40328, 0.89472], "triangles": [24, 23, 90, 23, 22, 21, 9, 12, 11, 17, 19, 18, 20, 16, 15, 17, 16, 19, 20, 19, 16, 10, 9, 11, 8, 13, 12, 8, 12, 9, 13, 15, 14, 7, 13, 8, 90, 23, 21, 95, 24, 90, 91, 90, 21, 95, 90, 91, 96, 95, 91, 97, 24, 95, 97, 95, 96, 15, 7, 20, 13, 7, 15, 93, 92, 7, 6, 93, 7, 92, 20, 7, 21, 20, 92, 91, 21, 92, 6, 94, 93, 5, 94, 6, 97, 100, 24, 99, 100, 97, 56, 96, 91, 57, 97, 96, 56, 57, 96, 98, 99, 97, 107, 93, 94, 55, 56, 91, 54, 55, 91, 101, 24, 100, 97, 58, 98, 57, 58, 97, 45, 57, 56, 58, 57, 45, 44, 45, 56, 43, 44, 56, 92, 54, 91, 55, 43, 56, 43, 55, 54, 42, 43, 54, 68, 92, 93, 68, 93, 107, 54, 66, 53, 101, 25, 24, 46, 45, 44, 108, 107, 94, 69, 68, 107, 68, 67, 92, 102, 25, 101, 54, 92, 66, 41, 42, 54, 59, 58, 45, 59, 45, 46, 108, 69, 107, 53, 41, 54, 67, 66, 92, 43, 47, 46, 43, 46, 44, 48, 47, 43, 51, 41, 53, 52, 53, 66, 40, 51, 53, 42, 48, 43, 49, 48, 42, 52, 40, 53, 41, 49, 42, 50, 49, 41, 51, 50, 41, 50, 51, 40, 65, 52, 66, 67, 30, 66, 31, 30, 67, 29, 65, 66, 32, 67, 68, 33, 32, 68, 31, 67, 32, 63, 40, 52, 64, 63, 52, 30, 29, 66, 101, 98, 102, 102, 59, 103, 100, 99, 98, 100, 98, 101, 69, 33, 68, 47, 60, 59, 47, 59, 46, 111, 110, 94, 108, 94, 110, 109, 69, 108, 109, 108, 110, 70, 33, 69, 109, 70, 69, 65, 64, 52, 64, 65, 29, 63, 62, 50, 63, 50, 40, 39, 29, 30, 34, 33, 70, 94, 4, 111, 59, 104, 103, 102, 98, 59, 59, 98, 58, 61, 48, 49, 60, 47, 48, 61, 60, 48, 62, 61, 49, 62, 49, 50, 38, 30, 31, 39, 30, 38, 35, 32, 33, 35, 33, 34, 37, 38, 31, 37, 31, 32, 36, 37, 32, 60, 104, 59, 105, 60, 61, 105, 104, 60, 39, 74, 64, 39, 64, 29, 35, 36, 32, 71, 34, 70, 35, 34, 71, 112, 110, 111, 5, 4, 94, 112, 111, 4, 106, 105, 61, 38, 73, 74, 38, 74, 39, 73, 38, 37, 72, 36, 35, 72, 35, 71, 37, 36, 72, 73, 37, 72, 112, 113, 109, 112, 109, 110, 113, 114, 71, 72, 71, 114, 71, 70, 109, 109, 113, 71, 75, 106, 61, 115, 72, 114, 106, 82, 104, 106, 104, 105, 82, 106, 75, 62, 75, 61, 77, 76, 75, 103, 25, 102, 26, 103, 104, 26, 104, 82, 26, 25, 103, 116, 73, 72, 116, 72, 115, 62, 77, 75, 62, 74, 78, 64, 62, 63, 74, 62, 64, 81, 78, 74, 73, 81, 74, 77, 62, 78, 116, 81, 73, 80, 79, 78, 81, 80, 78, 83, 82, 75, 83, 75, 76, 116, 88, 81, 3, 114, 113, 4, 3, 113, 4, 113, 112, 89, 88, 116, 82, 83, 26, 83, 27, 26, 0, 27, 83, 77, 84, 83, 77, 83, 76, 0, 83, 84, 87, 80, 81, 87, 81, 88, 85, 84, 77, 85, 77, 78, 86, 85, 78, 79, 86, 78, 87, 86, 79, 87, 79, 80, 28, 27, 0, 1, 84, 85, 0, 84, 1, 2, 86, 87, 1, 85, 86, 1, 86, 2, 2, 87, 88, 3, 2, 88, 89, 116, 115, 3, 89, 115, 3, 115, 114, 3, 88, 89], "vertices": [1, 3, -8.42, 17.91, 1, 1, 3, -15.68, 9.36, 1, 1, 3, -13.04, -4.47, 1, 2, 3, 4.53, -39.41, 0.616, 55, -37.55, -3.28, 0.384, 2, 3, 29.89, -60.78, 0.344, 55, -8.82, -19.83, 0.656, 2, 3, 86.09, -63.05, 0.264, 55, 46.9, -12.14, 0.736, 2, 3, 111.03, -48.22, 0.752, 55, 68.82, 6.87, 0.248, 1, 3, 114.63, -41.26, 1, 1, 3, 122.49, -54.31, 1, 1, 3, 136.54, -80.98, 1, 1, 3, 143.05, -96.3, 1, 1, 3, 160.77, -92.92, 1, 1, 3, 157.79, -69.08, 1, 1, 3, 142.28, -37.1, 1, 1, 3, 155.18, -22.55, 1, 1, 3, 147.23, 5.05, 1, 1, 3, 151.81, 20.93, 1, 1, 3, 159.31, 31.32, 1, 1, 3, 157.54, 39.05, 1, 1, 3, 135.94, 36.04, 1, 1, 3, 120.67, 17.46, 1, 2, 3, 114.16, 22.26, 0.768, 54, 67.46, -21.29, 0.232, 2, 3, 114.29, 36.84, 0.152, 54, 67.15, -6.71, 0.848, 2, 3, 92.36, 50.8, 0.216, 54, 44.82, 6.58, 0.784, 2, 3, 59.08, 59.73, 0.152, 54, 11.28, 14.5, 0.848, 2, 3, 26.88, 53.59, 0.728, 54, -20.72, 7.39, 0.272, 2, 3, -1.33, 38.3, 0.656, 54, -48.46, -8.74, 0.344, 2, 3, -15.6, 35.58, 0.568, 54, -62.63, -11.89, 0.432, 2, 3, -18.57, 26.51, 0.736, 54, -65.33, -21.05, 0.264, 1, 3, 30.81, -8.88, 1, 1, 3, 33.21, -12.02, 1, 1, 3, 34.11, -15.73, 1, 1, 3, 34.4, -19.75, 1, 1, 3, 33.13, -23.49, 1, 1, 3, 30.82, -26.3, 1, 1, 3, 27.36, -24.02, 1, 1, 3, 25.27, -21.01, 1, 1, 3, 24.99, -17.09, 1, 1, 3, 25.9, -13.89, 1, 1, 3, 28.24, -10.79, 1, 1, 3, 31.28, 9.01, 1, 1, 3, 33.65, 12.49, 1, 1, 3, 35.39, 16.23, 1, 1, 3, 37.01, 19.66, 1, 1, 3, 37.41, 23.05, 1, 1, 3, 37.63, 25.84, 1, 1, 3, 33.46, 24.86, 1, 1, 3, 30.76, 22.16, 1, 1, 3, 29.69, 18.36, 1, 1, 3, 29.41, 14.33, 1, 1, 3, 30.02, 11.14, 1, 1, 3, 32.24, 10.42, 1, 1, 3, 31.99, 6.78, 1, 1, 3, 34.88, 9.51, 1, 1, 3, 39.04, 13.99, 1, 1, 3, 42.14, 17.61, 1, 1, 3, 43.68, 22.92, 1, 1, 3, 42.33, 28.54, 1, 1, 3, 38.24, 32.11, 1, 1, 3, 30.6, 30.46, 1, 1, 3, 23.38, 25.59, 1, 1, 3, 21.11, 16.16, 1, 1, 3, 25.6, 8.49, 1, 1, 3, 29.32, 6.36, 1, 1, 3, 28.17, -3.99, 1, 1, 3, 32.47, -5.16, 1, 1, 3, 36.58, -7.87, 1, 1, 3, 40.32, -14.55, 1, 1, 3, 42.59, -21.5, 1, 1, 3, 41.25, -26.87, 1, 1, 3, 33.66, -31.25, 1, 1, 3, 26.56, -29.29, 1, 1, 3, 18.93, -23.55, 1, 1, 3, 17.44, -12.76, 1, 1, 3, 22.39, -5.94, 1, 1, 3, 7.3, 15.13, 1, 1, 3, 3.81, 12.57, 1, 1, 3, 1.98, 7.77, 1, 1, 3, 1.7, 3.27, 1, 1, 3, 0.45, -0.09, 1, 1, 3, 0.79, -4.38, 1, 1, 3, 2.52, -7.46, 1, 1, 3, 4.19, 24.33, 1, 1, 3, -4.38, 18.25, 1, 1, 3, -11.62, 11.49, 1, 1, 3, -13.5, 6.78, 1, 1, 3, -13.28, 0.7, 1, 1, 3, -9.06, -7.23, 1, 1, 3, -1.19, -16.04, 1, 1, 3, 1.69, -19.41, 1, 1, 3, 63.93, 29.42, 1, 1, 3, 60.05, 18.04, 1, 1, 3, 55.21, -15.25, 1, 1, 3, 57.18, -28.53, 1, 1, 3, 57.65, -33.93, 1, 1, 3, 58.52, 30.39, 1, 1, 3, 53.18, 27.92, 1, 1, 3, 49.17, 32.52, 1, 1, 3, 37.87, 34.95, 1, 1, 3, 41.59, 39.46, 1, 1, 3, 40.53, 43.28, 1, 1, 3, 36.12, 45.9, 1, 1, 3, 29.82, 44.92, 1, 1, 3, 21.32, 40.28, 1, 1, 3, 17.74, 35.02, 1, 1, 3, 16.23, 26.57, 1, 1, 3, 10.54, 23.59, 1, 1, 3, 52.8, -30.9, 1, 1, 3, 44.51, -33.42, 1, 1, 3, 34.5, -34.75, 1, 1, 3, 35.73, -40.05, 1, 1, 3, 33.61, -43.17, 1, 1, 3, 28.44, -43.39, 1, 1, 3, 20.17, -38.14, 1, 1, 3, 16.9, -33.55, 1, 1, 3, 15.13, -25.42, 1, 1, 3, 8.64, -19.28, 1], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 58, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 80, 80, 102, 102, 82, 100, 102, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 104, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 128, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 180, 182, 182, 184, 184, 186, 186, 188, 180, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 164, 188, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 232, 178], "width": 169, "height": 176}}, "Monkeyking14": {"Monkeyking14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 34, -13.77, -22.97, 1, 1, 34, -15.64, -13.15, 1, 1, 34, -6.8, -11.46, 1, 1, 34, -4.93, -21.28, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 10, "height": 9}}, "Monkeyking15": {"Monkeyking15": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 34, -9.77, 10.48, 1, 1, 34, -11.45, 19.32, 1, 1, 34, -3.6, 20.82, 1, 1, 34, -1.91, 11.98, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 9, "height": 8}}, "Monkeyking16": {"Monkeyking16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, 18.55, -33.7, 1, 1, 3, 5.43, 35.06, 1, 1, 3, 40.79, 41.8, 1, 1, 3, 53.91, -26.96, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 70, "height": 36}}, "Monkeyking17": {"Monkeyking17": {"type": "mesh", "uvs": [0.53185, 0, 1, 0, 1, 0.06662, 0.77295, 0.33773, 0.55377, 0.56452, 0.56473, 0.79652, 0.71816, 1, 0, 1, 0, 0.76264, 0, 0.52542, 0.1154, 0.12918], "triangles": [7, 5, 6, 7, 8, 5, 8, 4, 5, 8, 9, 4, 9, 10, 4, 10, 0, 4, 4, 0, 3, 3, 0, 2, 0, 1, 2], "vertices": [1, 32, -31.73, -9.45, 1, 1, 32, -37.23, 12.82, 1, 1, 32, -23.9, 16.11, 1, 2, 32, 32.99, 18.68, 0.99875, 33, -11.04, 19.44, 0.00125, 2, 32, 80.92, 19.43, 0.00333, 33, 36.87, 17.63, 0.99667, 1, 33, 83.71, 27.1, 1, 1, 33, 123.48, 42.33, 1, 1, 33, 130.07, 7.76, 1, 1, 33, 82.03, -1.39, 1, 1, 33, 34.03, -10.53, 1, 2, 32, -1.01, -22.89, 0.99998, 33, -47.21, -20.25, 2e-05], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "width": 49, "height": 206}}, "Monkeyking18": {"Monkeyking18": {"type": "mesh", "uvs": [0, 0, 0.98916, 0, 1, 1, 0, 1], "triangles": [3, 0, 1, 3, 1, 2], "vertices": [-56.77, -19.15, 71.81, -20.84, 71.73, -134.85, -58.26, -133.14], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 130, "height": 114}}, "Monkeyking19": {"Monkeyking19": {"type": "mesh", "uvs": [0.78769, 0, 0, 0, 0, 1, 1, 1, 1, 0], "triangles": [2, 1, 0, 0, 4, 3, 2, 0, 3], "vertices": [1, 0, -38.18, -12.61, 1, 1, 0, -69.65, -11.03, 1, 1, 0, -75.56, -128.88, 1, 1, 0, -35.61, -130.89, 1, 1, 0, -29.7, -13.04, 1], "hull": 5, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 0, 8], "width": 40, "height": 118}}, "Monkeyking2": {"Monkeyking2": {"type": "mesh", "uvs": [0.00764, 0.20783, 0, 0.01971, 0.39663, 0.00231, 0.51479, 0.09181, 0.66121, 0.28823, 0.75882, 0.49459, 0.89418, 0.70167, 0.99771, 0.86006, 1, 1, 0.78964, 1, 0.43773, 0.92222, 0.19627, 0.76061, 0.01646, 0.42497], "triangles": [9, 7, 8, 9, 6, 7, 9, 10, 6, 10, 5, 6, 10, 11, 5, 11, 4, 5, 4, 12, 3, 2, 3, 0, 12, 4, 11, 3, 12, 0, 0, 1, 2], "vertices": [1, 30, -17.56, -66.33, 1, 1, 30, -54.06, -47.57, 1, 2, 30, -16.22, 27.17, 0.97633, 31, -71.35, 38.15, 0.02367, 2, 30, 13.03, 39.28, 0.81249, 31, -40.61, 45.71, 0.18751, 2, 30, 65.51, 45.13, 0.05186, 31, 12.15, 43.56, 0.94814, 1, 31, 60.25, 31.98, 1, 1, 31, 113.39, 26.56, 1, 1, 31, 154.04, 22.41, 1, 1, 31, 178.33, 3.95, 1, 2, 30, 213.95, -7.57, 0.00178, 31, 150.92, -30.96, 0.99822, 2, 30, 162.67, -63.89, 0.16082, 31, 91.72, -78.88, 0.83918, 2, 30, 106.94, -90.93, 0.56058, 31, 32.55, -97.19, 0.43942, 2, 30, 24.57, -87.99, 0.99319, 31, -48.43, -81.84, 0.00681], "hull": 13, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 10, 12, 12, 14, 2, 0, 0, 24], "width": 211, "height": 218}}, "Monkeyking21": {"Monkeyking21": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 6, -20.38, -1.48, 1, 1, 6, 28.46, 51.43, 1, 1, 6, 67.4, 15.48, 1, 1, 6, 18.57, -37.43, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 72, "height": 53}}, "Monkeyking22": {"Monkeyking22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 7, 583.44, -19.55, 1, 1, 7, -307.54, -14.57, 1, 1, 7, -307.36, 17.43, 1, 1, 7, 583.62, 12.45, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 891, "height": 32}}, "Monkeyking23": {"Monkeyking23": {"type": "mesh", "uvs": [0.10244, 0.02367, 0.23926, 0.00418, 0.25481, 0.03566, 0.25481, 0.14808, 0.31078, 0.259, 0.42584, 0.37442, 0.59376, 0.49134, 0.77412, 0.60376, 0.90784, 0.73866, 1, 0.88406, 1, 1, 0.70882, 0.95601, 0.36987, 0.92603, 0.10555, 0.87957, 0.20506, 0.79712, 0.26414, 0.6952, 0.24548, 0.60526, 0.13664, 0.50933, 0.03402, 0.39091, 0, 0.27849, 0, 0.16907, 0.04646, 0.06864], "triangles": [12, 15, 6, 11, 12, 6, 14, 15, 12, 13, 14, 12, 7, 11, 6, 11, 7, 8, 11, 8, 9, 11, 9, 10, 16, 17, 5, 6, 15, 16, 6, 16, 5, 3, 18, 19, 4, 18, 3, 17, 4, 5, 4, 17, 18, 3, 19, 20, 0, 1, 2, 3, 0, 2, 3, 21, 0, 20, 21, 3], "vertices": [1, 16, -25.13, -10.88, 1, 1, 16, -43.98, 15.63, 1, 1, 16, -31.06, 23.82, 1, 3, 16, 19.23, 41.1, 0.92601, 17, -3.38, 46.93, 0.03939, 18, -35.96, 68.53, 0.0346, 3, 16, 64.7, 70.23, 0.20888, 17, 50.09, 54.46, 0.1844, 18, 15.69, 52.78, 0.60673, 3, 16, 107.8, 112.78, 0.00485, 18, 76.03, 47.53, 0.956, 19, -5.31, 48.77, 0.03915, 3, 18, 143.12, 52.28, 0.09426, 19, 61.81, 44.47, 0.84646, 20, -33.85, 38.75, 0.05928, 2, 19, 129.03, 43.7, 0.04897, 20, 32.29, 50.75, 0.95103, 1, 20, 102.97, 48.41, 1, 1, 20, 173.69, 35.37, 1, 1, 20, 222.36, 10.11, 1, 1, 20, 173.31, -39.23, 1, 1, 20, 125.12, -101.29, 1, 2, 19, 136.69, -156.79, 0.00055, 20, 77.85, -144.65, 0.99945, 2, 19, 120.2, -114.8, 0.03359, 20, 53.69, -106.55, 0.96641, 2, 19, 90.76, -74.31, 0.27908, 20, 17.1, -72.38, 0.72092, 2, 19, 54.77, -51.24, 0.81161, 20, -22.61, -56.56, 0.18839, 3, 18, 97.33, -41.73, 0.2761, 19, 3.79, -42.53, 0.72386, 20, -74.32, -57.67, 3e-05, 1, 18, 37.21, -33.31, 1, 2, 17, 52.28, -16.96, 0.83785, 18, -12.49, -12.89, 0.16215, 1, 17, 0.77, -11.86, 1, 1, 16, -0.86, -16.03, 1], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42], "width": 228, "height": 473}}, "Monkeyking24": {"Monkeyking24": {"type": "mesh", "uvs": [0.80274, 0.04487, 0.68591, 0.83694, 0.26673, 1, 0.17739, 1, 0.08806, 0.88423, 0, 0.6872, 0, 0.4941, 0.0599, 0.36931, 0.02193, 0.11699, 0.04912, 0, 0.18198, 0, 0.24153, 0.06457, 0.2603, 0.16677, 0.28966, 0.25011, 0.29961, 0.27494, 0.31931, 0.26839, 0.45594, 0.19514, 0.59574, 0.06395, 0.08023, 0.49935, 0.13004, 0.60891, 0.20129, 0.65881, 0.28011, 0.62192, 0.32677, 0.53731, 0.3274, 0.39413], "triangles": [2, 4, 20, 2, 21, 1, 2, 20, 21, 2, 3, 4, 20, 5, 19, 20, 4, 5, 21, 22, 1, 22, 23, 16, 19, 6, 18, 19, 5, 6, 20, 19, 21, 21, 19, 22, 19, 18, 22, 22, 18, 23, 6, 7, 18, 18, 7, 23, 7, 14, 23, 14, 15, 23, 7, 8, 12, 7, 13, 14, 7, 12, 13, 10, 11, 8, 10, 8, 9, 12, 8, 11, 1, 22, 16, 1, 16, 0, 0, 16, 17, 23, 15, 16], "vertices": [1, 4, -27.93, -17.03, 1, 2, 4, 33.18, 57.56, 0.9993, 5, -64.7, -42.91, 0.0007, 2, 4, 118.41, 38.9, 0.09755, 5, -33.18, 38.44, 0.90245, 2, 4, 134.83, 31.21, 0.01774, 5, -23.06, 53.49, 0.98226, 1, 5, -1.61, 60.92, 1, 1, 5, 27.66, 62.78, 1, 1, 5, 46.57, 50.07, 1, 1, 5, 52, 31.76, 1, 1, 5, 81.01, 21.54, 1, 1, 5, 89.39, 9.26, 1, 1, 5, 74.34, -13.12, 1, 1, 5, 61.27, -18.9, 1, 2, 4, 77.9, -50.7, 0.00484, 5, 49.14, -15.34, 0.99516, 2, 4, 76.67, -39.26, 0.07194, 5, 37.65, -14.79, 0.92806, 2, 4, 76.08, -35.75, 0.15484, 5, 34.09, -14.84, 0.84516, 2, 4, 72.13, -34.76, 0.29795, 5, 32.5, -18.59, 0.70205, 2, 4, 43.35, -30.83, 0.92074, 5, 24.2, -46.42, 0.07926, 1, 4, 11.08, -32.81, 1, 1, 5, 36.97, 36.9, 1, 1, 5, 20.6, 35.72, 1, 1, 5, 7.64, 27, 1, 2, 4, 97.03, -0.35, 0.00019, 5, 2.32, 11.3, 0.99981, 2, 4, 84.22, -5.38, 0.13065, 5, 5.32, -2.13, 0.86935, 2, 4, 76.94, -20.62, 0.29866, 5, 19.27, -11.67, 0.70134], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 14, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 28], "width": 203, "height": 118}}, "Monkeyking25": {"Monkeyking25": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 4, 16.64, 40.3, 1, 1, 4, 89.1, 6.38, 1, 1, 4, 50.51, -76.04, 1, 1, 4, -21.94, -42.11, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 91}}, "Monkeyking3": {"Monkeyking3": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 1e-05, 0.99999, 0.02907, 0.86064, 0.07268, 0.73351, 0.06783, 0.58366, 0.11629, 0.43905, 0.14052, 0.2962, 0.2035, 0.1446, 0.24711, 0, 0.5, 0, 0.81588, 0.00175, 0.83526, 0.14111, 0.84979, 0.28047, 0.87402, 0.42508, 0.89825, 0.57143, 0.96608, 0.71429, 0.99999, 0.86413, 0.5, 0.85714, 0.5, 0.71429, 0.5, 0.57143, 0.5, 0.42857, 0.5, 0.28571, 0.5, 0.14286], "triangles": [16, 18, 19, 4, 19, 18, 18, 16, 17, 1, 18, 17, 3, 4, 18, 1, 3, 18, 2, 3, 1, 1, 17, 0, 20, 21, 14, 20, 14, 15, 5, 6, 20, 19, 20, 15, 5, 20, 19, 4, 5, 19, 16, 19, 15, 7, 22, 21, 6, 7, 21, 6, 21, 20, 23, 11, 12, 8, 9, 23, 22, 23, 12, 8, 23, 22, 22, 12, 13, 7, 8, 22, 21, 22, 13, 21, 13, 14, 23, 10, 11, 9, 10, 23], "vertices": [1, 29, 71.58, 55.29, 1, 1, 29, 70.02, 6.82, 1, 1, 29, 68.45, -41.65, 1, 2, 28, 100.16, -36, 0.00873, 29, 31.08, -37.63, 0.99127, 2, 28, 65.89, -32.41, 0.53134, 29, -2.97, -32.3, 0.46866, 3, 27, 85.36, -33.53, 0.00416, 28, 25.59, -33.63, 0.99542, 29, -43.27, -31.47, 0.00042, 2, 27, 46.37, -29.67, 0.74201, 28, -13.38, -29.66, 0.25799, 1, 27, 7.9, -28.15, 1, 3, 25, 23.11, -26.1, 0.1354, 26, 11.52, -25.68, 0.66801, 27, -33, -22.91, 0.19659, 2, 25, -15.95, -23.82, 0.99927, 26, -27.6, -24.89, 0.00073, 1, 25, -17.17, 0.68, 1, 2, 25, -18.23, 31.31, 0.92575, 26, -31.98, 30.11, 0.07425, 2, 25, 19.11, 35.06, 0.3108, 26, 5.2, 35.28, 0.6892, 4, 25, 56.48, 38.34, 0.00291, 26, 42.42, 39.98, 0.93643, 27, 2.19, 40.55, 0.02507, 28, -57.37, 40.68, 0.03559, 4, 26, 80.96, 45.74, 0.29171, 27, 41.03, 43.73, 0.25363, 28, -18.52, 43.76, 0.45362, 29, -83.39, 48.07, 0.00105, 4, 26, 119.97, 51.54, 0.02311, 27, 80.34, 46.93, 0.00336, 28, 20.8, 46.84, 0.87858, 29, -43.96, 49.15, 0.09495, 2, 28, 59.1, 54.14, 0.45399, 29, -5.34, 54.48, 0.54601, 2, 28, 99.34, 58.18, 0.04963, 29, 35.05, 56.47, 0.95037, 2, 28, 98.37, 9.66, 0.00188, 29, 31.61, 8.06, 0.99812, 2, 28, 59.94, 8.94, 0.69261, 29, -6.8, 9.3, 0.30739, 3, 26, 123.37, 13.06, 0.00215, 28, 21.52, 8.22, 0.98932, 29, -45.21, 10.54, 0.00853, 3, 26, 85.09, 9.68, 0.04196, 27, 42.75, 7.48, 0.8669, 28, -16.9, 7.5, 0.09114, 2, 26, 46.81, 6.3, 0.99741, 28, -55.32, 6.78, 0.00259, 2, 25, 21.21, 2.6, 0.02785, 26, 8.53, 2.93, 0.97215], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 0], "width": 97, "height": 269}}, "Monkeyking4": {"Monkeyking4": {"type": "mesh", "uvs": [0.81201, 0.39814, 0.88542, 0.41785, 1, 0.59797, 1, 0.78935, 0.87395, 0.99762, 0.72713, 1, 0.63536, 0.9554, 0.60783, 0.86815, 0.43577, 0.74713, 0.38301, 0.70492, 0.03201, 0.54731, 0, 0.46006, 0.01136, 0.36156, 0.06871, 0.2912, 0.1536, 0.25742, 0.12112, 0.21907, 0.05789, 0.18381, 0, 0.10801, 0, 0, 0.06364, 0, 0.10962, 0.06923, 0.14986, 0.10801, 0.2059, 0.15561, 0.26481, 0.18558, 0.44731, 0.35305, 0.48898, 0.34071, 0.52778, 0.35658, 0.60825, 0.41828, 0.76201, 0.42709], "triangles": [16, 20, 21, 16, 17, 20, 17, 19, 20, 17, 18, 19, 14, 22, 23, 14, 15, 22, 15, 21, 22, 15, 16, 21, 24, 14, 23, 26, 27, 24, 24, 27, 9, 24, 25, 26, 9, 27, 8, 24, 9, 10, 14, 11, 13, 14, 24, 10, 14, 10, 11, 13, 11, 12, 4, 5, 7, 3, 4, 7, 7, 5, 6, 3, 7, 2, 2, 7, 28, 28, 0, 1, 7, 27, 28, 7, 8, 27, 28, 1, 2], "vertices": [2, 9, 10.29, -24.92, 0.99979, 10, -17.28, -35.4, 0.00021, 1, 9, 1.39, -24.09, 1, 1, 9, -14.27, -8.4, 1, 1, 9, -16.53, 10.02, 1, 1, 9, -4.1, 31.9, 1, 2, 9, 13.21, 34.26, 1, 10, -39.38, 19.57, 0, 1, 9, 24.58, 31.29, 1, 2, 9, 28.86, 23.29, 0.99193, 10, -20.58, 16.16, 0.00807, 2, 9, 50.61, 14.13, 0.27057, 10, 3.01, 16.94, 0.72943, 2, 9, 57.34, 10.83, 0.04843, 10, 10.5, 16.76, 0.95157, 1, 10, 54.07, 25.72, 1, 1, 10, 61.76, 20.52, 1, 1, 10, 65.63, 11.68, 1, 2, 10, 63.41, 2.29, 0.96118, 52, 14.18, 16.09, 0.03882, 3, 10, 56.55, -5.81, 0.5188, 52, 7.6, 7.77, 0.48, 53, -8.05, 7.69, 0.0012, 1, 52, 12.88, 6.81, 1, 3, 10, 69.99, -5.89, 0.00031, 52, 21.03, 8.14, 0.10768, 53, 5.39, 8.2, 0.89201, 1, 53, 15.22, 5.99, 1, 1, 53, 21.11, -2.67, 1, 1, 53, 14.85, -6.93, 1, 2, 52, 22.08, -4.52, 0.00548, 53, 6.55, -4.46, 0.99452, 2, 52, 16.01, -4.05, 0.46397, 53, 0.48, -4.04, 0.53603, 1, 52, 7.9, -3.9, 1, 2, 10, 48.95, -18.69, 0.00195, 52, 0.45, -5.37, 0.99805, 3, 9, 53.9, -23.98, 0.05624, 10, 21.94, -16.3, 0.87898, 52, -26.63, -3.9, 0.06478, 3, 9, 49.13, -25.77, 0.13071, 10, 18.35, -19.92, 0.84455, 52, -30.09, -7.65, 0.02473, 3, 9, 44.36, -24.81, 0.21476, 10, 13.61, -21.04, 0.77401, 52, -34.79, -8.93, 0.01123, 3, 9, 34.12, -20.03, 0.63326, 10, 2.32, -20.98, 0.36654, 52, -46.07, -9.26, 0.00019, 2, 9, 15.86, -21.41, 0.9913, 10, -13.69, -29.88, 0.0087], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56], "width": 119, "height": 97}}, "Monkeyking5": {"Monkeyking5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 11, -7.75, 7.14, 1, 1, 11, 20.79, 12.33, 1, 1, 11, 24.01, -5.38, 1, 1, 11, -4.53, -10.57, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 29, "height": 18}}, "Monkeyking6": {"Monkeyking6": {"type": "mesh", "uvs": [0.0045, 0, 0.14314, 0.09569, 0.18175, 0.18575, 0.26072, 0.27908, 0.35022, 0.35768, 0.48535, 0.43955, 0.65032, 0.52797, 0.8223, 0.6475, 0.93813, 0.7523, 1, 0.89475, 0.86618, 0.80469, 0.75737, 0.76048, 0.70823, 0.76376, 0.65032, 0.80961, 0.57837, 0.82107, 0.46254, 0.86364, 0.44324, 0.78013, 0.38357, 0.69171, 0.28705, 0.60657, 0.19403, 0.52633, 0.11681, 0.42809, 0.0396, 0.29055, 0.01327, 0.17429], "triangles": [10, 8, 9, 15, 16, 14, 12, 13, 14, 6, 12, 14, 8, 10, 7, 16, 6, 14, 7, 11, 12, 6, 16, 17, 10, 11, 7, 7, 12, 6, 17, 5, 6, 17, 18, 5, 18, 4, 5, 18, 19, 4, 4, 19, 20, 4, 20, 3, 3, 21, 2, 3, 20, 21, 21, 22, 2, 22, 1, 2, 22, 0, 1], "vertices": [1, 12, -50.65, 8.51, 1, 1, 12, -3.77, 20.75, 1, 2, 12, 53.56, 29.45, 0.79471, 13, -10.25, 31.98, 0.20529, 3, 12, 103.93, 40.09, 0.02714, 13, 41.23, 32.85, 0.87328, 14, -22.87, 39.38, 0.09959, 3, 13, 90.22, 40.97, 0.14418, 14, 26.65, 35.68, 0.8541, 15, -63.09, 41.58, 0.00172, 2, 14, 91.39, 42.48, 0.41172, 15, 1.99, 43.26, 0.58828, 1, 15, 78.6, 49.43, 1, 1, 15, 165.17, 46.11, 1, 1, 15, 229.44, 35.19, 1, 1, 15, 284.96, -1.5, 1, 1, 15, 218.32, 0.04, 1, 1, 15, 171.25, -9.07, 1, 1, 15, 155.68, -21.48, 1, 1, 15, 147.64, -51.09, 1, 1, 15, 126.49, -71.64, 1, 1, 15, 98.35, -113.33, 1, 1, 15, 71.45, -87.93, 1, 2, 14, 128.12, -68.26, 0.07673, 15, 29.9, -70.02, 0.92327, 2, 14, 74.62, -64.14, 0.73081, 15, -23.11, -61.71, 0.26919, 3, 13, 110.04, -53.48, 0.06406, 14, 23.55, -60.78, 0.93428, 15, -73.76, -54.35, 0.00166, 2, 13, 57.3, -52.5, 0.80817, 14, -27.46, -47.35, 0.19183, 2, 12, 68.72, -42.16, 0.58, 13, -8.98, -41.2, 0.42, 1, 12, 20.51, -26.07, 1], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44], "width": 404, "height": 433}}, "Monkeyking7": {"Monkeyking7": {"type": "mesh", "uvs": [0.35181, 0.0004, 0.58941, 0.33281, 0.79281, 0.49969, 1, 0.69778, 1, 0.89452, 0.84861, 1, 0.65241, 1, 0.46161, 0.90944, 0.09981, 0.72763, 0, 0.36809, 0, 0.18763, 0.20421, 0], "triangles": [9, 10, 11, 1, 8, 9, 6, 7, 2, 0, 9, 11, 7, 1, 2, 2, 3, 6, 9, 0, 1, 7, 8, 1, 3, 5, 6, 4, 5, 3], "vertices": [1, 8, -39.62, 52.92, 1, 1, 8, 31.46, 28.55, 1, 1, 8, 76.41, 24.88, 1, 1, 8, 125.99, 17.04, 1, 1, 8, 152.57, -11.7, 1, 1, 8, 150.15, -42.53, 1, 1, 8, 128.54, -62.51, 1, 1, 8, 95.29, -68.71, 1, 1, 8, 30.88, -78.99, 1, 1, 8, -28.69, -36.63, 1, 1, 8, -53.07, -10.26, 1, 1, 8, -55.93, 37.95, 1], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22], "width": 150, "height": 199}}, "Monkeyking8": {"Monkeyking8": {"type": "mesh", "uvs": [0.30649, 0, 0.52386, 0, 0.70171, 0.11971, 0.74123, 0.34771, 0.84003, 0.68671, 1, 0.90871, 0.58314, 1, 0.23403, 1, 0, 0.92971, 0.22086, 0.64171, 0.31308, 0.35371, 0.2538, 0.09571], "triangles": [4, 6, 9, 6, 4, 5, 9, 3, 4, 8, 9, 7, 6, 7, 9, 3, 9, 10, 10, 2, 3, 10, 11, 1, 10, 1, 2, 1, 11, 0], "vertices": [2, 41, -7.09, 0.81, 0.94892, 42, -10.02, -6.04, 0.05108, 1, 41, -0.02, 7.88, 1, 2, 41, 14.31, 5.11, 0.44972, 42, 4.34, 10.41, 0.55028, 3, 41, 31.88, -9.89, 5e-05, 42, 27.4, 9.22, 0.1168, 43, 4.13, 9.91, 0.88315, 2, 43, 38.3, 14.91, 0.00482, 44, 11.66, 14.87, 0.99518, 1, 44, 34.01, 22.44, 1, 1, 44, 43.41, 3.35, 1, 1, 44, 43.56, -12.71, 1, 1, 44, 36.56, -23.54, 1, 2, 43, 34.14, -13.63, 0.05066, 44, 7.38, -13.66, 0.94934, 3, 42, 25.44, -10.39, 0.21095, 43, 4.99, -9.77, 0.78893, 44, -21.75, -9.69, 0.00012, 2, 41, -1.97, -7.74, 0.2371, 42, -0.75, -9.7, 0.7629], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22], "width": 46, "height": 101}}, "Monkeyking9": {"Monkeyking9": {"type": "mesh", "uvs": [0.79375, 0, 1, 0, 1, 0.18104, 0.90485, 0.44839, 0.93515, 0.68901, 0.82405, 1, 0.31905, 1, 0, 0.93854, 0.22815, 0.67119, 0.36955, 0.41572, 0.65235, 0.14836], "triangles": [5, 6, 4, 8, 9, 4, 7, 8, 6, 4, 6, 8, 4, 9, 3, 9, 10, 3, 3, 10, 2, 2, 10, 0, 0, 1, 2], "vertices": [2, 45, 3.96, -4.7, 0.91576, 46, -3.68, -2.93, 0.08424, 1, 45, -1.78, -2.4, 1, 2, 45, 5.07, 14.74, 0.00373, 46, 12.52, 7.88, 0.99627, 3, 46, 39.59, 12.27, 0.04807, 47, 14.85, 10.12, 0.94524, 48, -18.58, 10.32, 0.00669, 2, 47, 39.18, 13.46, 0.02076, 48, 5.81, 13.22, 0.97924, 1, 48, 37.7, 12.47, 1, 1, 48, 38.93, -2.63, 1, 1, 48, 33.46, -12.68, 1, 2, 47, 39.49, -7.82, 0.0339, 48, 5.72, -8.07, 0.9661, 1, 47, 13.13, -6.19, 1, 1, 46, 12.04, -3.06, 1], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "width": 30, "height": 102}}, "bannertayduky1": {"bannertayduky1": {"type": "mesh", "uvs": [0, 0, 1, 0, 1, 1, 0, 1], "triangles": [3, 1, 2, 3, 0, 1], "vertices": [1, 58, 51.29, 143.4, 1, 1, 58, 51.29, -146.6, 1, 1, 58, -18.71, -146.6, 1, 1, 58, -18.71, 143.4, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 464, "height": 112}}, "bannertayduky2": {"bannertayduky1": {"type": "mesh", "uvs": [0, 0, 1, 0, 1, 1, 0, 1], "triangles": [3, 1, 2, 3, 0, 1], "vertices": [1, 58, 51.29, 143.4, 1, 1, 58, 51.29, -146.6, 1, 1, 58, -18.71, -146.6, 1, 1, 58, -18.71, 143.4, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 464, "height": 112}}, "domdom": {"domdom": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-22.08, -165.29, -22.08, 154.71, 34.92, 154.71, 34.92, -165.29], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 320, "height": 57}}, "domdom2": {"domdom": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-22.08, -165.29, -22.08, 154.71, 34.92, 154.71, 34.92, -165.29], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 320, "height": 57}}, "khoi": {"khoi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [265.87, -229.87, -283.74, -229.87, -283.74, 99.02, 265.87, 99.02], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 391, "height": 170}}, "khungicongameGo": {"khungicongameGo": {"x": 0.5, "y": 0.5, "width": 465, "height": 703}}, "khungicongameGo2": {"khungicongameGo": {"x": 0.5, "y": 0.5, "width": 465, "height": 703}}, "path": {"path": {"type": "clipping", "end": "path", "vertexCount": 10, "vertices": [-127.46, 157.99, -104.7, 190.44, -52.89, 199.33, -2.6, 205.21, 35.7, 202.64, 71.19, 198.67, 105.18, 187.77, 127.36, 158.18, 127.72, -153.5, -127.11, -153.06], "color": "ce3a3aff"}}}}, "animations": {"MonkeyKingWild2": {"slots": {"domdom": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00", "curve": "stepped"}, {"time": 3.3, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffffff"}, {"time": 4, "color": "ffffff00"}]}, "domdom2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00", "curve": "stepped"}, {"time": 3.3, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffffff"}, {"time": 4, "color": "ffffff00"}]}}, "bones": {"bone2": {"rotate": [{"time": 0, "angle": 0}, {"time": 2.6667, "angle": -1.06}, {"time": 5, "angle": 0}]}, "bone3": {"rotate": [{"time": 0.5, "angle": 0}, {"time": 0.8333, "angle": 10.78}, {"time": 2.6667, "angle": 15.32}, {"time": 3, "angle": 0}, {"time": 3.6333, "angle": 1.97}, {"time": 5, "angle": 0}], "translate": [{"time": 3.6333, "x": 0, "y": 0}, {"time": 3.7333, "x": -1.49, "y": 0.24}, {"time": 3.8333, "x": 0, "y": 0}, {"time": 3.9333, "x": -1.49, "y": 0.24}, {"time": 4.0333, "x": 0, "y": 0}, {"time": 4.1333, "x": -1.49, "y": 0.24}, {"time": 4.2333, "x": 0, "y": 0}]}, "bone98": {"rotate": [{"time": 0, "angle": 20.52}, {"time": 0.6667, "angle": -8.75, "curve": "stepped"}, {"time": 1.1667, "angle": -8.75}, {"time": 1.3333, "angle": -24.55}, {"time": 1.6667, "angle": -21.13}, {"time": 1.8333, "angle": -7.25}, {"time": 2.1667, "angle": -7.85}, {"time": 2.3333, "angle": -21.78}, {"time": 2.6667, "angle": -8.75}, {"time": 3.3333, "angle": 20.52}, {"time": 4.1667, "angle": 15.71}, {"time": 5, "angle": 20.52}]}, "bone99": {"rotate": [{"time": 0, "angle": 59.3}, {"time": 0.6667, "angle": -0.7, "curve": "stepped"}, {"time": 2.6667, "angle": -0.7}, {"time": 3.3333, "angle": 59.3}, {"time": 4.1667, "angle": 54.49}, {"time": 5, "angle": 59.3}]}, "bone9": {"rotate": [{"time": 0, "angle": 62.9}, {"time": 0.4333, "angle": 39.96}, {"time": 0.6667, "angle": -21.23}, {"time": 1.1667, "angle": -35.08}, {"time": 1.6667, "angle": -21.23}, {"time": 2.1667, "angle": -35.08}, {"time": 2.6667, "angle": -21.23}, {"time": 3.3333, "angle": 62.9}, {"time": 4.1667, "angle": 67.53}, {"time": 5, "angle": 62.9}], "translate": [{"time": 0, "x": -6.46, "y": -6.57}, {"time": 0.4333, "x": -4.22, "y": -5.18}, {"time": 0.6667, "x": 6.46, "y": -5.65, "curve": "stepped"}, {"time": 2.6667, "x": 6.46, "y": -5.65}, {"time": 3.3333, "x": -6.46, "y": -6.57}]}, "bone10": {"rotate": [{"time": 0, "angle": -27.34}, {"time": 0.6667, "angle": -5.92, "curve": "stepped"}, {"time": 2.6667, "angle": -5.92}, {"time": 3.3333, "angle": -3.28}, {"time": 4.1667, "angle": 6.89}, {"time": 5, "angle": -27.34}]}, "bone8": {"rotate": [{"time": 0, "angle": -7.35}, {"time": 0.1667, "angle": -0.35}, {"time": 0.6667, "angle": 5.15}, {"time": 1.6667, "angle": 1.87, "curve": "stepped"}, {"time": 2.6667, "angle": 1.87}, {"time": 3.3333, "angle": -7.35}, {"time": 4.1667, "angle": -12.16}, {"time": 5, "angle": -7.35}]}, "bone11": {"rotate": [{"time": 4.1667, "angle": -4.81}]}, "bone76": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 1.63}, {"time": 0.5333, "angle": -2.85}, {"time": 0.7667, "angle": -0.65}, {"time": 1, "angle": 0}, {"time": 1.2667, "angle": 1.63}, {"time": 1.5333, "angle": -2.85}, {"time": 1.7667, "angle": -0.65}, {"time": 2.0333, "angle": 0}, {"time": 2.3, "angle": 1.63}, {"time": 2.5667, "angle": -2.85}, {"time": 2.8, "angle": -0.65}, {"time": 3.1, "angle": 0}, {"time": 3.3667, "angle": 1.63}, {"time": 3.6333, "angle": -2.85}, {"time": 3.8667, "angle": -0.65}, {"time": 4.0333, "angle": 0}, {"time": 4.3, "angle": 1.63}, {"time": 4.5667, "angle": -2.85}, {"time": 4.8, "angle": -0.65}, {"time": 5, "angle": 0}]}, "bone77": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 10.52}, {"time": 0.5333, "angle": 0.7}, {"time": 0.7667, "angle": -4.12}, {"time": 1, "angle": 0}, {"time": 1.2667, "angle": 10.52}, {"time": 1.5333, "angle": 0.7}, {"time": 1.7667, "angle": -4.12}, {"time": 2.0333, "angle": 0}, {"time": 2.3, "angle": 10.52}, {"time": 2.5667, "angle": 0.7}, {"time": 2.8, "angle": -4.12}, {"time": 3.1, "angle": 0}, {"time": 3.3667, "angle": 10.52}, {"time": 3.6333, "angle": 0.7}, {"time": 3.8667, "angle": -4.12}, {"time": 4.0333, "angle": 0}, {"time": 4.3, "angle": 10.52}, {"time": 4.5667, "angle": 0.7}, {"time": 4.8, "angle": -4.12}, {"time": 5, "angle": 0}]}, "bone72": {"rotate": [{"time": 0, "angle": 4.65}, {"time": 0.4333, "angle": -4.82}, {"time": 0.9, "angle": -3.4}, {"time": 1.2667, "angle": 6.52}, {"time": 1.6, "angle": 7.04}, {"time": 1.9667, "angle": -0.24}, {"time": 2.3667, "angle": -4.16}, {"time": 2.8, "angle": -3.11}, {"time": 3.1667, "angle": 4.91}, {"time": 3.5, "angle": 6.44}, {"time": 3.8, "angle": 1.34}, {"time": 4.1, "angle": -8.05}, {"time": 4.3333, "angle": -3.67}, {"time": 4.6333, "angle": -0.42}, {"time": 5, "angle": 4.65}]}, "bone75": {"rotate": [{"time": 0, "angle": 0.56}, {"time": 0.4333, "angle": -4.53}, {"time": 0.9, "angle": -3.48}, {"time": 1.2667, "angle": -6.43}, {"time": 1.6, "angle": -6.19}, {"time": 1.9667, "angle": -4}, {"time": 2.3667, "angle": -1.83}, {"time": 2.8, "angle": -11.07}, {"time": 3.1667, "angle": -12.26}, {"time": 3.5, "angle": -4.01}, {"time": 3.8, "angle": -3.33}, {"time": 4.1, "angle": 5.02}, {"time": 4.3333, "angle": -1.87}, {"time": 4.6333, "angle": -8.46}, {"time": 5, "angle": 0.56}]}, "bone74": {"rotate": [{"time": 0, "angle": 8.24}, {"time": 0.4333, "angle": 7.12}, {"time": 0.9, "angle": -6.64}, {"time": 1.2667, "angle": -10.3}, {"time": 1.6, "angle": 2.46}, {"time": 1.9667, "angle": 5.62}, {"time": 2.3667, "angle": 5.1}, {"time": 2.8, "angle": -3.46}, {"time": 3.1667, "angle": -10.45}, {"time": 3.5, "angle": 2.73}, {"time": 3.8, "angle": 6.3}, {"time": 4.1, "angle": 0.78}, {"time": 4.3333, "angle": 2.84}, {"time": 4.6333, "angle": -6.15}, {"time": 5, "angle": 8.24}]}, "bone73": {"rotate": [{"time": 0, "angle": -0.8}, {"time": 0.4333, "angle": 2.92}, {"time": 0.9, "angle": -5.28}, {"time": 1.2667, "angle": -8.54}, {"time": 1.6, "angle": -2.07}, {"time": 1.9667, "angle": -1.84}, {"time": 2.3667, "angle": -5.47}, {"time": 2.8, "angle": -6.08}, {"time": 3.1667, "angle": -1.99}, {"time": 3.5, "angle": -0.4}, {"time": 3.8, "angle": -0.64}, {"time": 4.1, "angle": 8.74}, {"time": 4.3333, "angle": -5.34}, {"time": 4.6333, "angle": -6.01}, {"time": 5, "angle": -0.8}]}, "bone5": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3333, "angle": 6.95}, {"time": 2.3333, "angle": 6.12}, {"time": 2.6667, "angle": 0}]}, "bone6": {"rotate": [{"time": 0.6667, "angle": 0}, {"time": 1, "angle": 4.44}, {"time": 1.1667, "angle": -3.55}, {"time": 1.6667, "angle": 4.44}, {"time": 1.8333, "angle": -3.55}, {"time": 2.6667, "angle": -0.75}, {"time": 5, "angle": 0}], "scale": [{"time": 0, "x": 0.863, "y": 0.863}]}, "bone4": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3333, "angle": -31.19, "curve": "stepped"}, {"time": 2.5, "angle": -31.19}, {"time": 2.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": 12.81, "y": -7.01, "curve": "stepped"}, {"time": 2.5, "x": 12.81, "y": -7.01}, {"time": 2.8333, "x": 0, "y": 0}, {"time": 3.9, "x": -4.51, "y": -1.36}, {"time": 5, "x": 0, "y": 0}]}, "bone58": {"rotate": [{"time": 0, "angle": -0.92}, {"time": 0.3667, "angle": 11.9}, {"time": 0.7333, "angle": -3.4}, {"time": 1.1, "angle": 1.41}, {"time": 1.4333, "angle": 13.35}, {"time": 1.7667, "angle": 14.22}, {"time": 2.1333, "angle": 28.45}, {"time": 2.4667, "angle": -0.55}, {"time": 2.8333, "angle": 12.08}, {"time": 3.2, "angle": -3.52}, {"time": 3.5667, "angle": 1.41}, {"time": 3.9, "angle": 13.35}, {"time": 4.2333, "angle": 20.3}, {"time": 4.6, "angle": 28.45}, {"time": 5, "angle": -0.92}], "scale": [{"time": 0, "x": 0.931, "y": 1}, {"time": 1.1, "x": 0.96, "y": 0.893}, {"time": 2.1333, "x": 0.876, "y": 1}, {"time": 3.2, "x": 0.928, "y": 0.888}, {"time": 4.1667, "x": 0.887, "y": 0.93}, {"time": 5, "x": 0.931, "y": 1}]}, "bone61": {"rotate": [{"time": 0, "angle": 18.72}, {"time": 0.3667, "angle": 5.63}, {"time": 0.7333, "angle": 15.06}, {"time": 1.1, "angle": -13.03}, {"time": 1.4333, "angle": -16.67}, {"time": 1.7667, "angle": -1.2}, {"time": 2.1333, "angle": 4.54}, {"time": 2.4667, "angle": 20.64}, {"time": 2.8333, "angle": -16.06}, {"time": 3.2, "angle": 1.25}, {"time": 3.5667, "angle": -13.03}, {"time": 3.9, "angle": -16.67}, {"time": 4.2333, "angle": -10.69}, {"time": 4.6, "angle": 4.54}, {"time": 5, "angle": 18.72}], "scale": [{"time": 0, "x": 0.931, "y": 1}, {"time": 1.1, "x": 0.96, "y": 0.893}, {"time": 2.1333, "x": 0.876, "y": 1}, {"time": 3.2, "x": 0.928, "y": 0.888}, {"time": 4.1667, "x": 0.887, "y": 0.93}, {"time": 5, "x": 0.931, "y": 1}]}, "bone60": {"rotate": [{"time": 0, "angle": -1.87}, {"time": 0.3667, "angle": -8.17}, {"time": 0.7333, "angle": -6.74}, {"time": 1.1, "angle": -12.35}, {"time": 1.4333, "angle": -23.59}, {"time": 1.7667, "angle": -14.88}, {"time": 2.1333, "angle": -9.4}, {"time": 2.4667, "angle": 0.04}, {"time": 2.8333, "angle": -17.5}, {"time": 3.2, "angle": -7.25}, {"time": 3.5667, "angle": -12.35}, {"time": 3.9, "angle": -23.59}, {"time": 4.2333, "angle": -18.79}, {"time": 4.6, "angle": -9.4}, {"time": 5, "angle": -1.87}], "scale": [{"time": 0, "x": 0.931, "y": 1}, {"time": 1.1, "x": 0.96, "y": 0.893}, {"time": 2.1333, "x": 0.876, "y": 1}, {"time": 3.2, "x": 0.928, "y": 0.888}, {"time": 4.1667, "x": 0.887, "y": 0.93}, {"time": 5, "x": 0.931, "y": 1}]}, "bone59": {"rotate": [{"time": 0, "angle": -1.12}, {"time": 0.3667, "angle": -6.35}, {"time": 0.7333, "angle": 0.15}, {"time": 1.1, "angle": 1.32}, {"time": 1.4333, "angle": -2.17}, {"time": 1.7667, "angle": -1.51}, {"time": 2.1333, "angle": -4.89}, {"time": 2.4667, "angle": -0.56}, {"time": 2.8333, "angle": 4.25}, {"time": 3.2, "angle": 0.02}, {"time": 3.5667, "angle": 1.32}, {"time": 3.9, "angle": -2.17}, {"time": 4.2333, "angle": -2.3}, {"time": 4.6, "angle": -4.89}, {"time": 5, "angle": -1.12}], "scale": [{"time": 0, "x": 0.931, "y": 1}, {"time": 1.1, "x": 0.96, "y": 0.893}, {"time": 2.1333, "x": 0.876, "y": 1}, {"time": 3.2, "x": 0.928, "y": 0.888}, {"time": 4.1667, "x": 0.887, "y": 0.93}, {"time": 5, "x": 0.931, "y": 1}]}, "bone63": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 32.72}, {"time": 0.8, "angle": 31.59}, {"time": 1.1, "angle": 14.57}, {"time": 1.4333, "angle": 28.39}, {"time": 1.7333, "angle": 30.84}, {"time": 2.1333, "angle": 8.12}, {"time": 2.5667, "angle": 0}, {"time": 2.9333, "angle": 32.72}, {"time": 3.3333, "angle": 31.83}, {"time": 3.6667, "angle": 10.94}, {"time": 4, "angle": 28.67}, {"time": 4.3, "angle": 30.84}, {"time": 4.7, "angle": 8.12}, {"time": 5, "angle": 0}]}, "bone66": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 11.08}, {"time": 0.8, "angle": 50.75}, {"time": 1.1, "angle": 3.2}, {"time": 1.4333, "angle": 22.9}, {"time": 1.7333, "angle": -1.72}, {"time": 2.1333, "angle": 16.76}, {"time": 2.5667, "angle": 0}, {"time": 2.9333, "angle": 11.08}, {"time": 3.3333, "angle": 14.5}, {"time": 3.6667, "angle": 29.48}, {"time": 4, "angle": 29.26}, {"time": 4.3, "angle": -1.72}, {"time": 4.7, "angle": 16.76}, {"time": 5, "angle": 0}]}, "bone65": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": -11.12}, {"time": 0.8, "angle": -12.77}, {"time": 1.1, "angle": 10.51}, {"time": 1.4333, "angle": -8.89}, {"time": 1.7333, "angle": -12.81}, {"time": 2.1333, "angle": 0.45}, {"time": 2.5667, "angle": 0}, {"time": 2.9333, "angle": -11.12}, {"time": 3.3333, "angle": -4.11}, {"time": 3.6667, "angle": 9.13}, {"time": 4, "angle": -6.52}, {"time": 4.3, "angle": -12.81}, {"time": 4.7, "angle": 0.45}, {"time": 5, "angle": 0}]}, "bone64": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": -20.31}, {"time": 0.8, "angle": -17.91}, {"time": 1.1, "angle": -1.21}, {"time": 1.4333, "angle": -22.91}, {"time": 1.7333, "angle": -19.8}, {"time": 2.1333, "angle": -22.13}, {"time": 2.5667, "angle": 0}, {"time": 2.9333, "angle": -20.31}, {"time": 3.3333, "angle": -13.13}, {"time": 3.6667, "angle": -5.94}, {"time": 4, "angle": -22.06}, {"time": 4.3, "angle": -19.8}, {"time": 4.7, "angle": -22.13}, {"time": 5, "angle": 0}]}, "bone78": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -4.45}, {"time": 0.5, "angle": 0}, {"time": 0.7333, "angle": -4.45}, {"time": 1, "angle": 0}, {"time": 1.2333, "angle": -4.45}, {"time": 1.5, "angle": 0}, {"time": 1.7333, "angle": -4.45}, {"time": 2, "angle": 0}, {"time": 2.2333, "angle": -4.45}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": -4.45}, {"time": 3, "angle": 0}, {"time": 3.2333, "angle": -4.45}, {"time": 3.5, "angle": 0}, {"time": 3.7333, "angle": -4.45}, {"time": 4, "angle": 0}, {"time": 4.2333, "angle": -4.45}, {"time": 4.5, "angle": 0}, {"time": 4.7333, "angle": -4.45}, {"time": 5, "angle": 0}]}, "root": {"scale": [{"time": 0, "x": 1.038, "y": 1.038}]}, "bone84": {"rotate": [{"time": 0, "angle": -9.75, "curve": "stepped"}, {"time": 0.7667, "angle": -9.75}, {"time": 0.9333, "angle": 4.7, "curve": "stepped"}, {"time": 2.3333, "angle": 4.7}, {"time": 3, "angle": -9.75}], "translate": [{"time": 0, "x": -3.44, "y": 0.81, "curve": "stepped"}, {"time": 0.7667, "x": -3.44, "y": 0.81}, {"time": 0.9333, "x": -1.27, "y": 0.5, "curve": "stepped"}, {"time": 2.3333, "x": -1.27, "y": 0.5}, {"time": 3, "x": -3.44, "y": 0.81}]}, "bone88": {"rotate": [{"time": 0, "angle": 20.8}, {"time": 0.3, "angle": 8.8}, {"time": 0.6333, "angle": -5.74}, {"time": 0.9333, "angle": -0.94}, {"time": 1.2, "angle": -2.21}, {"time": 1.4667, "angle": 6.04}, {"time": 1.7333, "angle": 7.22}, {"time": 2, "angle": 20.8}, {"time": 2.3, "angle": 8.8}, {"time": 2.6333, "angle": -5.74}, {"time": 2.9333, "angle": -0.94}, {"time": 3.2, "angle": -2.21}, {"time": 3.4667, "angle": 6.04}, {"time": 3.7333, "angle": 7.22}, {"time": 4.0333, "angle": -5.74}, {"time": 4.3333, "angle": -0.94}, {"time": 4.6, "angle": -2.21}, {"time": 5, "angle": 20.8}]}, "bone89": {"rotate": [{"time": 0, "angle": -8.33}, {"time": 0.3, "angle": -5.85}, {"time": 0.6333, "angle": 9.42}, {"time": 0.9333, "angle": 14.85}, {"time": 1.2, "angle": 10.04}, {"time": 1.4667, "angle": 6.63}, {"time": 1.7333, "angle": 5.83}, {"time": 2, "angle": -8.33}, {"time": 2.3, "angle": -5.85}, {"time": 2.6333, "angle": 9.42}, {"time": 2.9333, "angle": 14.85}, {"time": 3.2, "angle": 10.04}, {"time": 3.4667, "angle": 6.63}, {"time": 3.7333, "angle": 5.83}, {"time": 4.0333, "angle": 9.42}, {"time": 4.3333, "angle": 14.85}, {"time": 4.6, "angle": 10.04}, {"time": 5, "angle": -8.33}]}, "bone90": {"rotate": [{"time": 0, "angle": 2.64}, {"time": 0.3, "angle": 21.14}, {"time": 0.6333, "angle": 1.1}, {"time": 0.9333, "angle": 16.08}, {"time": 1.2, "angle": 3.78}, {"time": 1.4667, "angle": -4.4}, {"time": 1.7333, "angle": 17.2}, {"time": 2, "angle": 2.64}, {"time": 2.3, "angle": 21.14}, {"time": 2.6333, "angle": 1.1}, {"time": 2.9333, "angle": 16.08}, {"time": 3.2, "angle": 3.78}, {"time": 3.4667, "angle": -4.4}, {"time": 3.7333, "angle": 17.2}, {"time": 4.0333, "angle": 1.1}, {"time": 4.3333, "angle": 16.08}, {"time": 4.6, "angle": 3.78}, {"time": 5, "angle": 2.64}]}, "bone94": {"rotate": [{"time": 0, "angle": -0.85}, {"time": 0.2333, "angle": -16.33}, {"time": 0.5, "angle": -8.15}, {"time": 0.7667, "angle": 14.26}, {"time": 1, "angle": 21.87}, {"time": 1.3333, "angle": -0.85}, {"time": 1.6333, "angle": -16.33}, {"time": 1.9, "angle": -8.15}, {"time": 2.1667, "angle": 14.26}, {"time": 2.4, "angle": 21.87}, {"time": 2.6, "angle": -0.85}, {"time": 2.8, "angle": -16.33}, {"time": 3.0667, "angle": -8.15}, {"time": 3.3333, "angle": 14.26}, {"time": 3.5667, "angle": 21.87}, {"time": 3.9, "angle": -0.85}, {"time": 4.2, "angle": -16.33}, {"time": 4.4667, "angle": -8.15}, {"time": 4.7333, "angle": 14.26}, {"time": 5, "angle": -0.85}]}, "bone92": {"rotate": [{"time": 0, "angle": -28.11}, {"time": 0.2333, "angle": -15.82}, {"time": 0.5, "angle": -2.84}, {"time": 0.7667, "angle": -6.74}, {"time": 1, "angle": -8.21}, {"time": 1.3333, "angle": -28.11}, {"time": 1.6333, "angle": -15.82}, {"time": 1.9, "angle": -2.84}, {"time": 2.1667, "angle": -6.74}, {"time": 2.4, "angle": -8.21}, {"time": 2.6, "angle": -28.11}, {"time": 2.8, "angle": -15.82}, {"time": 3.0667, "angle": -2.84}, {"time": 3.3333, "angle": -6.74}, {"time": 3.5667, "angle": -8.21}, {"time": 3.9, "angle": -28.11}, {"time": 4.2, "angle": -15.82}, {"time": 4.4667, "angle": -2.84}, {"time": 4.7333, "angle": -6.74}, {"time": 5, "angle": -28.11}]}, "bone93": {"rotate": [{"time": 0, "angle": 24.2}, {"time": 0.2333, "angle": 20.19}, {"time": 0.5, "angle": 4.79}, {"time": 0.7667, "angle": 2.21}, {"time": 1, "angle": -0.88}, {"time": 1.3333, "angle": 24.2}, {"time": 1.6333, "angle": 20.19}, {"time": 1.9, "angle": 4.79}, {"time": 2.1667, "angle": 2.21}, {"time": 2.4, "angle": -0.88}, {"time": 2.6, "angle": 24.2}, {"time": 2.8, "angle": 20.19}, {"time": 3.0667, "angle": 4.79}, {"time": 3.3333, "angle": 2.21}, {"time": 3.5667, "angle": -0.88}, {"time": 3.9, "angle": 24.2}, {"time": 4.2, "angle": 20.19}, {"time": 4.4667, "angle": 4.79}, {"time": 4.7333, "angle": 2.21}, {"time": 5, "angle": 24.2}]}, "bone100": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -5.01}, {"time": 0.3333, "angle": 0}, {"time": 0.5, "angle": -5.01}, {"time": 0.6667, "angle": 0}, {"time": 0.8333, "angle": -5.01}, {"time": 1, "angle": 0}, {"time": 1.1667, "angle": -5.01}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": -5.01}, {"time": 1.6667, "angle": 0}, {"time": 1.8333, "angle": -5.01}, {"time": 2, "angle": 0}, {"time": 2.1667, "angle": -5.01}, {"time": 2.3333, "angle": 0}, {"time": 2.5, "angle": -5.01}, {"time": 2.6667, "angle": 0}, {"time": 2.8333, "angle": -5.01}, {"time": 3, "angle": 0}, {"time": 3.1667, "angle": -5.01}, {"time": 3.3333, "angle": 0}, {"time": 3.5, "angle": -5.01}, {"time": 3.6667, "angle": 0}, {"time": 3.8333, "angle": -5.01}, {"time": 4, "angle": 0}, {"time": 4.1667, "angle": -5.01}, {"time": 4.3333, "angle": 0}, {"time": 4.5, "angle": -5.01}, {"time": 4.6667, "angle": 0}, {"time": 4.8333, "angle": -5.01}, {"time": 5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1667, "x": 1.51, "y": -2.7}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5, "x": 1.51, "y": -2.7}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 0.8333, "x": 1.51, "y": -2.7}, {"time": 1, "x": 0, "y": 0}, {"time": 1.1667, "x": 1.51, "y": -2.7}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 1.5, "x": 1.51, "y": -2.7}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.8333, "x": 1.51, "y": -2.7}, {"time": 2, "x": 0, "y": 0}, {"time": 2.1667, "x": 1.51, "y": -2.7}, {"time": 2.3333, "x": 0, "y": 0}, {"time": 2.5, "x": 1.51, "y": -2.7}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 2.8333, "x": 1.51, "y": -2.7}, {"time": 3, "x": 0, "y": 0}, {"time": 3.1667, "x": 1.51, "y": -2.7}, {"time": 3.3333, "x": 0, "y": 0}, {"time": 3.5, "x": 1.51, "y": -2.7}, {"time": 3.6667, "x": 0, "y": 0}, {"time": 3.8333, "x": 1.51, "y": -2.7}, {"time": 4, "x": 0, "y": 0}, {"time": 4.1667, "x": 1.51, "y": -2.7}, {"time": 4.3333, "x": 0, "y": 0}, {"time": 4.5, "x": 1.51, "y": -2.7}, {"time": 4.6667, "x": 0, "y": 0}, {"time": 4.8333, "x": 1.51, "y": -2.7}, {"time": 5, "x": 0, "y": 0}]}, "bone101": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -7.16}, {"time": 0.3333, "angle": 0}, {"time": 0.5, "angle": -7.16}, {"time": 0.6667, "angle": 0}, {"time": 0.8333, "angle": -7.16}, {"time": 1, "angle": 0}, {"time": 1.1667, "angle": -7.16}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": -7.16}, {"time": 1.6667, "angle": 0}, {"time": 1.8333, "angle": -7.16}, {"time": 2, "angle": 0}, {"time": 2.1667, "angle": -7.16}, {"time": 2.3333, "angle": 0}, {"time": 2.5, "angle": -7.16}, {"time": 2.6667, "angle": 0}, {"time": 2.8333, "angle": -7.16}, {"time": 3, "angle": 0}, {"time": 3.1667, "angle": -7.16}, {"time": 3.3333, "angle": 0}, {"time": 3.5, "angle": -7.16}, {"time": 3.6667, "angle": 0}, {"time": 3.8333, "angle": -7.16}, {"time": 4, "angle": 0}, {"time": 4.1667, "angle": -7.16}, {"time": 4.3333, "angle": 0}, {"time": 4.5, "angle": -7.16}, {"time": 4.6667, "angle": 0}, {"time": 4.8333, "angle": -7.16}, {"time": 5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1667, "x": -2.28, "y": -5.42}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5, "x": -2.28, "y": -5.42}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 0.8333, "x": -2.28, "y": -5.42}, {"time": 1, "x": 0, "y": 0}, {"time": 1.1667, "x": -2.28, "y": -5.42}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 1.5, "x": -2.28, "y": -5.42}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.8333, "x": -2.28, "y": -5.42}, {"time": 2, "x": 0, "y": 0}, {"time": 2.1667, "x": -2.28, "y": -5.42}, {"time": 2.3333, "x": 0, "y": 0}, {"time": 2.5, "x": -2.28, "y": -5.42}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 2.8333, "x": -2.28, "y": -5.42}, {"time": 3, "x": 0, "y": 0}, {"time": 3.1667, "x": -2.28, "y": -5.42}, {"time": 3.3333, "x": 0, "y": 0}, {"time": 3.5, "x": -2.28, "y": -5.42}, {"time": 3.6667, "x": 0, "y": 0}, {"time": 3.8333, "x": -2.28, "y": -5.42}, {"time": 4, "x": 0, "y": 0}, {"time": 4.1667, "x": -2.28, "y": -5.42}, {"time": 4.3333, "x": 0, "y": 0}, {"time": 4.5, "x": -2.28, "y": -5.42}, {"time": 4.6667, "x": 0, "y": 0}, {"time": 4.8333, "x": -2.28, "y": -5.42}, {"time": 5, "x": 0, "y": 0}]}, "bone13": {"translate": [{"time": 0.8, "x": 0, "y": 0}, {"time": 1.6667, "x": 0, "y": 33.05}, {"time": 2.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3, "x": 0, "y": 0}, {"time": 4.1667, "x": 0, "y": 33.05}], "scale": [{"time": 0.8333, "x": 1, "y": 1}, {"time": 1.6667, "x": 1.394, "y": 1.394}, {"time": 2.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1}, {"time": 4.1667, "x": 1.394, "y": 1.394}]}}, "deform": {"default": {"Monkeyking1": {"Monkeyking1": [{"time": 0}, {"time": 1.2333, "offset": 4, "vertices": [1.69043, 2.02614, 1.64249, 2.55708, 1.6185, 2.82257, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.28943, -0.24149, 0.82041, -0.19355, 2.677, -2.96982]}, {"time": 2.5}, {"time": 3.7333, "offset": 4, "vertices": [1.69043, 2.02614, 1.64249, 2.55708, 1.6185, 2.82257, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.28943, -0.24149, 0.82041, -0.19355, 2.677, -2.96982]}, {"time": 5}]}, "Monkeyking12": {"Monkeyking12": [{"time": 0}, {"time": 0.1667, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 0.3333}, {"time": 0.5, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 0.6667}, {"time": 0.8333, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 1}, {"time": 1.1667, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 1.3333}, {"time": 1.5, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 1.6667}, {"time": 1.8333, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 2}, {"time": 2.1667, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 2.3333}, {"time": 2.5, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 2.6667}, {"time": 2.8333, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 3}, {"time": 3.1667, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 3.3333}, {"time": 3.5, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 3.6667}, {"time": 3.8333, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 4}, {"time": 4.1667, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 4.3333}, {"time": 4.5, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 4.6667}, {"time": 4.8333, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 5}]}, "Monkeyking13": {"Monkeyking13": [{"time": 0, "offset": 84, "vertices": [-1.82471, 0.1947, -1.86423, -0.10015, -2.28961, -0.21322, -1.21742, 0.0232, -0.36963, -0.0705, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.0945, 0.27097, -0.76857, 0.01305, 0, 0, 0, 0, 0, 0, -0.56473, 0.11579, -2.25432, -0.398], "curve": "stepped"}, {"time": 0.7333, "offset": 84, "vertices": [-1.82471, 0.1947, -1.86423, -0.10015, -2.28961, -0.21322, -1.21742, 0.0232, -0.36963, -0.0705, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.0945, 0.27097, -0.76857, 0.01305, 0, 0, 0, 0, 0, 0, -0.56473, 0.11579, -2.25432, -0.398]}, {"time": 0.9333, "offset": 174, "vertices": [0.19453, 0.37917, -0.23415, -0.17181, 0, 0, 0, 0, 0, 0, -0.4686, -0.16582, -0.21234, -0.88715], "curve": "stepped"}, {"time": 2.6667, "offset": 174, "vertices": [0.19453, 0.37917, -0.23415, -0.17181, 0, 0, 0, 0, 0, 0, -0.4686, -0.16582, -0.21234, -0.88715]}, {"time": 3.3333, "offset": 84, "vertices": [-1.82471, 0.1947, -1.86423, -0.10015, -2.28961, -0.21322, -1.21742, 0.0232, -0.36963, -0.0705, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.0945, 0.27097, -0.76857, 0.01305, 0, 0, 0, 0, 0, 0, -0.56473, 0.11579, -2.25432, -0.398]}]}, "Monkeyking24": {"Monkeyking24": [{"time": 0, "offset": 2, "vertices": [-13.01288, 0.63295, -4.1468, -12.35062]}]}}}}}}