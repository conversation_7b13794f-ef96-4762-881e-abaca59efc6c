{"skeleton": {"hash": "abOHf37jhuOHobgNc+1qPm4+JZU", "spine": "3.7.93", "width": 289, "height": 436, "images": "", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 68.1, "rotation": -90.45, "x": -0.59, "y": 153.59}, {"name": "bone2", "parent": "bone", "length": 9.85, "rotation": -1.1, "x": 90.51, "y": 0.5}, {"name": "bone3", "parent": "bone", "length": 40.73, "rotation": 2.69, "x": 65.44, "y": -1.47}, {"name": "bone4", "parent": "bone3", "length": 5.59, "rotation": -2.25, "x": 44.44, "y": -0.41}, {"name": "bone5", "parent": "bone4", "length": 8.58, "rotation": 7.13, "x": 8.78, "y": 0.27}, {"name": "bone6", "parent": "bone", "x": 71.75, "y": 0.08}, {"name": "bone7", "parent": "bone6", "length": 6.73, "rotation": 137.42, "x": -1.56, "y": 10.65}, {"name": "bone8", "parent": "bone7", "length": 7.05, "rotation": -11.44, "x": 6.73}, {"name": "bone9", "parent": "bone8", "length": 6.85, "rotation": -24.5, "x": 7.05}, {"name": "bone10", "parent": "bone9", "length": 6.09, "rotation": -25.08, "x": 6.85}, {"name": "bone11", "parent": "bone10", "length": 5.13, "rotation": 21.39, "x": 6.09}, {"name": "bone12", "parent": "bone11", "length": 8.29, "rotation": 17.19, "x": 5.31, "y": 0.14}, {"name": "bone13", "parent": "bone12", "length": 7.79, "rotation": 5.81, "x": 8.44, "y": -0.07}, {"name": "bone14", "parent": "bone13", "length": 9.22, "rotation": 8.16, "x": 8.89, "y": -0.07}, {"name": "bone15", "parent": "bone14", "length": 9.89, "rotation": 1.79, "x": 9.32, "y": 0.13}, {"name": "bone16", "parent": "bone15", "length": 12.06, "rotation": 3.61, "x": 10.48, "y": -0.07}, {"name": "bone17", "parent": "bone16", "length": 11.95, "rotation": 2.77, "x": 12.64, "y": -0.1}, {"name": "bone18", "parent": "bone17", "length": 12.93, "rotation": 7.62, "x": 11.95}, {"name": "bone19", "parent": "bone18", "length": 12.07, "rotation": -1.01, "x": 13.02, "y": -0.13}, {"name": "bone20", "parent": "bone19", "length": 11.26, "rotation": -11.24, "x": 12.07}, {"name": "bone21", "parent": "bone6", "length": 5.71, "rotation": -144.24, "x": 0.84, "y": -10.46}, {"name": "bone22", "parent": "bone21", "length": 5.79, "rotation": 15.13, "x": 5.71}, {"name": "bone23", "parent": "bone22", "length": 5.66, "rotation": 17.39, "x": 5.79}, {"name": "bone24", "parent": "bone23", "length": 6.09, "rotation": 31.33, "x": 5.66}, {"name": "bone25", "parent": "bone24", "length": 5.81, "rotation": 16.55, "x": 6.09}, {"name": "bone26", "parent": "bone25", "length": 6.89, "rotation": -35.44, "x": 5.81}, {"name": "bone27", "parent": "bone26", "length": 9.54, "rotation": -16.84, "x": 6.89}, {"name": "bone28", "parent": "bone27", "length": 8.86, "rotation": -2.25, "x": 9.54}, {"name": "bone29", "parent": "bone28", "length": 11.41, "rotation": -6.5, "x": 9.46, "y": 0.11}, {"name": "bone30", "parent": "bone29", "length": 9.97, "rotation": -1.78, "x": 11.84, "y": 0.07}, {"name": "bone31", "parent": "bone30", "length": 12.18, "rotation": -0.14, "x": 10.4, "y": 0.08}, {"name": "bone32", "parent": "bone31", "length": 11.03, "rotation": 2.99, "x": 13.85, "y": 0.05}, {"name": "bone33", "parent": "bone32", "length": 12, "rotation": -0.21, "x": 12.48, "y": 0.28}, {"name": "bone34", "parent": "bone33", "length": 11.02, "rotation": 8.35, "x": 12.7, "y": 0.01}, {"name": "bone35", "parent": "bone34", "length": 12.1, "rotation": 4.48, "x": 11.71, "y": -0.09}, {"name": "bone36", "parent": "bone", "length": 17.83, "rotation": 1.94, "x": 22.48, "y": -0.8}, {"name": "bone37", "parent": "bone36", "length": 5, "rotation": 72.32, "x": 28.35, "y": 23.13}, {"name": "bone38", "parent": "bone37", "length": 10.55, "rotation": 13.66, "x": 5}, {"name": "bone39", "parent": "bone38", "length": 7.9, "rotation": -13.42, "x": 10.55}, {"name": "bone40", "parent": "bone39", "length": 14.07, "rotation": 8.35, "x": 7.9}, {"name": "bone41", "parent": "bone36", "length": 6.81, "rotation": 76.68, "x": 19.87, "y": 30.94}, {"name": "bone42", "parent": "bone41", "length": 14.3, "rotation": 21.18, "x": 6.96, "y": 0.03}, {"name": "bone43", "parent": "bone42", "length": 11.94, "rotation": -10.1, "x": 14.3}, {"name": "bone44", "parent": "bone43", "length": 9.5, "rotation": -4.87, "x": 11.94}, {"name": "bone45", "parent": "bone44", "length": 11.6, "rotation": 28.41, "x": 9.5}, {"name": "bone46", "parent": "bone36", "length": 12.93, "rotation": 102.38, "x": 7.02, "y": 31.75}, {"name": "bone47", "parent": "bone46", "length": 17.68, "rotation": 13.82, "x": 12.93}, {"name": "bone48", "parent": "bone47", "length": 21.95, "rotation": 20.17, "x": 18.44, "y": 0.12}, {"name": "bone49", "parent": "bone48", "length": 21.92, "rotation": -4.01, "x": 22.16, "y": -0.23}, {"name": "bone50", "parent": "bone49", "length": 23.34, "rotation": -9.95, "x": 22.14}, {"name": "bone51", "parent": "bone50", "length": 23.47, "rotation": -3.78, "x": 25.1, "y": -0.44}, {"name": "bone52", "parent": "bone36", "length": 8.55, "rotation": -75.75, "x": 18.54, "y": -34.15}, {"name": "bone53", "parent": "bone52", "length": 13.56, "rotation": 3.06, "x": 9.52, "y": -0.13}, {"name": "bone54", "parent": "bone53", "length": 14.44, "rotation": 18.26, "x": 13.56}, {"name": "bone55", "parent": "bone54", "length": 10.26, "rotation": -4.06, "x": 15.26}, {"name": "bone56", "parent": "bone55", "length": 10.96, "rotation": -27.56, "x": 10.99, "y": -0.08}, {"name": "bone57", "parent": "bone36", "length": 8.21, "rotation": -98.34, "x": 5.03, "y": -43.27}, {"name": "bone58", "parent": "bone57", "length": 12.74, "rotation": 3.18, "x": 8.21}, {"name": "bone59", "parent": "bone58", "length": 11.95, "rotation": 9.15, "x": 12.74}, {"name": "bone60", "parent": "bone59", "length": 10.51, "rotation": 24.27, "x": 12.12, "y": -0.02}, {"name": "bone61", "parent": "bone60", "length": 15.46, "rotation": -2.1, "x": 10.51}, {"name": "bone62", "parent": "bone36", "length": 11.03, "rotation": -104.3, "x": -5.58, "y": -37.45}, {"name": "bone63", "parent": "bone62", "length": 15.31, "rotation": -12.4, "x": 11.19, "y": 0.04}, {"name": "bone64", "parent": "bone63", "length": 20.34, "rotation": -12.31, "x": 15.31}, {"name": "bone65", "parent": "bone64", "length": 20.92, "rotation": 11.15, "x": 20.34}, {"name": "bone66", "parent": "bone65", "length": 24.39, "rotation": 15.58, "x": 22.53, "y": 0.43}, {"name": "bone67", "parent": "bone36", "length": 11.6, "rotation": -115.54, "x": -22.79, "y": -34.39}, {"name": "bone68", "parent": "bone67", "length": 18.03, "rotation": -13.24, "x": 12.18, "y": -0.1}, {"name": "bone69", "parent": "bone68", "length": 18.72, "rotation": 4.01, "x": 18.03}, {"name": "bone70", "parent": "bone69", "length": 21.2, "rotation": 17.21, "x": 19.21, "y": 0.13}, {"name": "bone71", "parent": "bone70", "length": 19.04, "rotation": -15.99, "x": 21.2}, {"name": "bone72", "parent": "bone36", "length": 11.66, "rotation": 136.34, "x": -27.06, "y": 33.22}, {"name": "bone73", "parent": "bone36", "length": 12.22, "rotation": -136.49, "x": -31.85, "y": -25.35}, {"name": "bone74", "parent": "root", "length": 13.62, "rotation": 90, "x": 8.31, "y": -35.81}, {"name": "bone75", "parent": "bone74", "length": 13.62, "x": -19.5, "y": -23.55}, {"name": "bone76", "parent": "bone74", "length": 13.62, "x": 29.81, "y": 32.38}, {"name": "bone77", "parent": "bone", "length": 40.44, "rotation": 0.19, "x": -5.69, "y": 0.77}, {"name": "bone78", "parent": "root", "length": 24.31, "rotation": 90, "x": 4.9, "y": -44.06}, {"name": "bone79", "parent": "bone", "length": 10.72, "rotation": -179.55, "x": 26.37, "y": 21.91, "scaleX": 0.634, "scaleY": 0.634}, {"name": "bone80", "parent": "bone", "length": 10.72, "rotation": -179.55, "x": 27.29, "y": -21.52, "scaleX": 0.634, "scaleY": 0.634}, {"name": "bone81", "parent": "root", "length": 6.05, "rotation": 90, "x": -108.59, "y": 8.01}, {"name": "bone82", "parent": "root", "length": 6.05, "rotation": 90, "x": 101.81, "y": -27.06}, {"name": "bone83", "parent": "root", "length": 6.05, "rotation": 90, "x": 60.84, "y": 8.72}, {"name": "bone84", "parent": "root", "length": 19.44, "rotation": 90, "x": 88.4, "y": 171.14, "scaleX": 0.591, "scaleY": 0.591}, {"name": "bone85", "parent": "root", "length": 19.44, "rotation": 90, "x": -74.08, "y": 171.14, "scaleX": 0.442, "scaleY": 0.442}, {"name": "bone86", "parent": "root", "length": 19.44, "rotation": 90, "x": 17.99, "y": 189.42, "scaleX": 0.423, "scaleY": 0.423}, {"name": "bone87", "parent": "root", "length": 19.44, "rotation": 90, "x": -36.84, "y": 189.42, "scaleX": 0.423, "scaleY": 0.423}], "slots": [{"name": "border", "bone": "root", "attachment": "border"}, {"name": "mask", "bone": "root", "attachment": "mask"}, {"name": "kho bau tu linh0", "bone": "root", "attachment": "kho bau tu linh0"}, {"name": "kho bau tu linh10", "bone": "root", "attachment": "kho bau tu linh10"}, {"name": "goldcoin3", "bone": "bone86", "attachment": "goldcoin7"}, {"name": "goldcoin4", "bone": "bone87", "attachment": "goldcoin7"}, {"name": "kho bau tu linh11", "bone": "root", "attachment": "kho bau tu linh11"}, {"name": "kho bau tu linh12", "bone": "root", "attachment": "kho bau tu linh12"}, {"name": "kho bau tu linh3", "bone": "root", "attachment": "kho bau tu linh3"}, {"name": "kho bau tu linh24", "bone": "root", "attachment": "kho bau tu linh3", "blend": "additive"}, {"name": "kho bau tu linh2", "bone": "root", "attachment": "kho bau tu linh2"}, {"name": "kho bau tu linh13", "bone": "root", "attachment": "kho bau tu linh13"}, {"name": "kho bau tu linh16", "bone": "bone75", "attachment": "kho bau tu linh16"}, {"name": "kho bau tu linh17", "bone": "bone76", "attachment": "kho bau tu linh17"}, {"name": "kho bau tu linh20", "bone": "bone81", "attachment": "kho bau tu linh20", "blend": "additive"}, {"name": "kho bau tu linh22", "bone": "bone82", "attachment": "kho bau tu linh20", "blend": "additive"}, {"name": "kho bau tu linh23", "bone": "bone83", "attachment": "kho bau tu linh20", "blend": "additive"}, {"name": "kho bau tu linh4", "bone": "root", "attachment": "kho bau tu linh4"}, {"name": "kho bau tu linh5", "bone": "root", "attachment": "kho bau tu linh5"}, {"name": "kho bau tu linh6", "bone": "root", "attachment": "kho bau tu linh6"}, {"name": "kho bau tu linh7", "bone": "root", "attachment": "kho bau tu linh7"}, {"name": "kho bau tu linh8", "bone": "root", "attachment": "kho bau tu linh8"}, {"name": "kho bau tu linh21", "bone": "root", "attachment": "kho bau tu linh8"}, {"name": "kho bau tu linh9", "bone": "root", "attachment": "kho bau tu linh9"}, {"name": "kho bau tu linh1", "bone": "root", "attachment": "kho bau tu linh1"}, {"name": "kho bau tu linh15", "bone": "root", "attachment": "kho bau tu linh15"}, {"name": "kho bau tu linh14", "bone": "root", "attachment": "kho bau tu linh14"}, {"name": "kho bau tu linh_textani0", "bone": "bone78"}, {"name": "kho bau tu linh_textani1", "bone": "bone78", "color": "ffffff53", "blend": "additive"}, {"name": "fire0", "bone": "bone79", "color": "ffffff55", "blend": "additive"}, {"name": "fire1", "bone": "bone80", "color": "ffffff55", "blend": "additive"}, {"name": "goldcoin1", "bone": "bone84", "attachment": "goldcoin7"}, {"name": "goldcoin2", "bone": "bone85", "attachment": "goldcoin7"}, {"name": "border2", "bone": "root", "attachment": "border"}], "skins": {"default": {"border": {"border": {"x": 0.5, "width": 289, "height": 436}}, "border2": {"border": {"x": 0.5, "width": 289, "height": 436}}, "fire0": {"fire0": {"x": 19.88, "y": -15.72, "rotation": -90, "width": 97, "height": 105}, "fire1": {"x": 19.88, "y": -15.72, "rotation": -90, "width": 97, "height": 105}, "fire2": {"x": 19.88, "y": -15.72, "rotation": -90, "width": 97, "height": 105}, "fire3": {"x": 19.88, "y": -15.72, "rotation": -90, "width": 97, "height": 105}, "fire4": {"x": 19.88, "y": -15.72, "rotation": -90, "width": 97, "height": 105}, "fire5": {"x": 19.88, "y": -15.72, "rotation": -90, "width": 97, "height": 105}, "fire6": {"x": 19.88, "y": -15.72, "rotation": -90, "width": 97, "height": 105}, "fire7": {"x": 19.88, "y": -15.72, "rotation": -90, "width": 97, "height": 105}, "fire8": {"x": 19.88, "y": -15.72, "rotation": -90, "width": 97, "height": 105}, "fire9": {"x": 19.88, "y": -15.72, "rotation": -90, "width": 97, "height": 105}}, "fire1": {"fire0": {"x": 19.88, "y": -15.72, "rotation": -90, "width": 97, "height": 105}, "fire1": {"x": 19.88, "y": -15.72, "rotation": -90, "width": 97, "height": 105}, "fire2": {"x": 19.88, "y": -15.72, "rotation": -90, "width": 97, "height": 105}, "fire3": {"x": 19.88, "y": -15.72, "rotation": -90, "width": 97, "height": 105}, "fire4": {"x": 19.88, "y": -15.72, "rotation": -90, "width": 97, "height": 105}, "fire5": {"x": 19.88, "y": -15.72, "rotation": -90, "width": 97, "height": 105}, "fire6": {"x": 19.88, "y": -15.72, "rotation": -90, "width": 97, "height": 105}, "fire7": {"x": 19.88, "y": -15.72, "rotation": -90, "width": 97, "height": 105}, "fire8": {"x": 19.88, "y": -15.72, "rotation": -90, "width": 97, "height": 105}, "fire9": {"x": 19.88, "y": -15.72, "rotation": -90, "width": 97, "height": 105}}, "goldcoin1": {"goldcoin1": {"x": 1.14, "y": 0.6, "rotation": -90, "width": 53, "height": 49}, "goldcoin2": {"x": 0.64, "y": 1.1, "rotation": -90, "width": 28, "height": 52}, "goldcoin3": {"x": 1.14, "y": 0.6, "rotation": -90, "width": 27, "height": 51}, "goldcoin4": {"x": 1.14, "y": 1.1, "rotation": -90, "width": 50, "height": 39}, "goldcoin5": {"x": 1.14, "y": 0.6, "rotation": -90, "width": 53, "height": 35}, "goldcoin6": {"x": 1.14, "y": 1.1, "rotation": -90, "width": 46, "height": 43}, "goldcoin7": {"x": 1.14, "y": 0.6, "rotation": -90, "width": 19, "height": 51}}, "goldcoin2": {"goldcoin1": {"x": 1.14, "y": 0.6, "rotation": -90, "width": 53, "height": 49}, "goldcoin2": {"x": 0.64, "y": 1.1, "rotation": -90, "width": 28, "height": 52}, "goldcoin3": {"x": 1.14, "y": 0.6, "rotation": -90, "width": 27, "height": 51}, "goldcoin4": {"x": 1.14, "y": 1.1, "rotation": -90, "width": 50, "height": 39}, "goldcoin5": {"x": 1.14, "y": 0.6, "rotation": -90, "width": 53, "height": 35}, "goldcoin6": {"x": 1.14, "y": 1.1, "rotation": -90, "width": 46, "height": 43}, "goldcoin7": {"x": 1.14, "y": 0.6, "rotation": -90, "width": 19, "height": 51}}, "goldcoin3": {"goldcoin1": {"x": 1.14, "y": 0.6, "rotation": -90, "width": 53, "height": 49}, "goldcoin2": {"x": 0.64, "y": 1.1, "rotation": -90, "width": 28, "height": 52}, "goldcoin3": {"x": 1.14, "y": 0.6, "rotation": -90, "width": 27, "height": 51}, "goldcoin4": {"x": 1.14, "y": 1.1, "rotation": -90, "width": 50, "height": 39}, "goldcoin5": {"x": 1.14, "y": 0.6, "rotation": -90, "width": 53, "height": 35}, "goldcoin6": {"x": 1.14, "y": 1.1, "rotation": -90, "width": 46, "height": 43}, "goldcoin7": {"x": 1.14, "y": 0.6, "rotation": -90, "width": 19, "height": 51}}, "goldcoin4": {"goldcoin1": {"x": 1.14, "y": 0.6, "rotation": -90, "width": 53, "height": 49}, "goldcoin2": {"x": 0.64, "y": 1.1, "rotation": -90, "width": 28, "height": 52}, "goldcoin3": {"x": 1.14, "y": 0.6, "rotation": -90, "width": 27, "height": 51}, "goldcoin4": {"x": 1.14, "y": 1.1, "rotation": -90, "width": 50, "height": 39}, "goldcoin5": {"x": 1.14, "y": 0.6, "rotation": -90, "width": 53, "height": 35}, "goldcoin6": {"x": 1.14, "y": 1.1, "rotation": -90, "width": 46, "height": 43}, "goldcoin7": {"x": 1.14, "y": 0.6, "rotation": -90, "width": 19, "height": 51}}, "kho bau tu linh0": {"kho bau tu linh0": {"x": -0.5, "y": 2, "width": 290, "height": 436}}, "kho bau tu linh1": {"kho bau tu linh1": {"type": "mesh", "uvs": [0.21774, 0, 0.23881, 0.12246, 0.26936, 0.18081, 0.2662, 0.23453, 0.23565, 0.19656, 0.17455, 0.1734, 0.08711, 0.14284, 0, 0.0706, 0, 0.11783, 0.04814, 0.21971, 0.09554, 0.31233, 0.16823, 0.37809, 0.11029, 0.44014, 0.12714, 0.51886, 0.19103, 0.64464, 0.24013, 0.69995, 0.26622, 0.80922, 0.24627, 0.8416, 0.25087, 0.9765, 0.33066, 1, 0.68969, 1, 0.77102, 0.96166, 0.77715, 0.8362, 0.73573, 0.79708, 0.748, 0.67972, 0.83392, 0.61901, 0.8554, 0.51919, 0.88302, 0.4396, 0.87075, 0.41127, 0.94593, 0.33438, 0.99196, 0.22106, 1, 0.06728, 0.93059, 0.15901, 0.80937, 0.18464, 0.75874, 0.26423, 0.77715, 0.1752, 0.81244, 0.1037, 0.82472, 0, 0.61869, 0.52478, 0.64016, 0.50988, 0.66332, 0.48952, 0.6893, 0.46668, 0.70964, 0.45029, 0.70343, 0.49002, 0.68422, 0.51386, 0.65485, 0.52876, 0.63056, 0.52776, 0.55995, 0.50939, 0.56842, 0.47462, 0.61813, 0.42248, 0.68704, 0.36983, 0.73336, 0.37679, 0.77121, 0.43688, 0.76443, 0.52578, 0.67123, 0.58686, 0.59045, 0.55955, 0.38015, 0.5237, 0.3651, 0.5091, 0.34745, 0.49358, 0.3298, 0.47852, 0.29918, 0.45753, 0.30385, 0.48901, 0.32305, 0.51001, 0.3433, 0.5237, 0.36562, 0.52689, 0.43829, 0.51183, 0.43881, 0.47578, 0.38898, 0.4233, 0.32046, 0.37264, 0.26284, 0.38633, 0.24, 0.46118, 0.26544, 0.5415, 0.3324, 0.57892, 0.40299, 0.56203], "triangles": [37, 1, 0, 7, 6, 8, 1, 37, 2, 36, 35, 37, 35, 2, 37, 9, 8, 6, 30, 32, 31, 34, 2, 35, 68, 3, 67, 5, 9, 6, 10, 9, 5, 29, 32, 30, 33, 32, 29, 34, 33, 29, 34, 50, 2, 67, 3, 2, 51, 50, 34, 28, 51, 34, 10, 3, 11, 4, 10, 5, 3, 10, 4, 69, 11, 3, 68, 69, 3, 29, 28, 34, 50, 49, 2, 2, 49, 67, 52, 51, 28, 42, 50, 51, 42, 51, 52, 49, 50, 42, 60, 69, 68, 60, 68, 67, 70, 11, 69, 70, 69, 60, 12, 11, 70, 41, 49, 42, 48, 67, 49, 66, 67, 48, 59, 60, 67, 61, 60, 59, 40, 49, 41, 39, 48, 49, 43, 41, 42, 40, 41, 43, 58, 59, 67, 57, 58, 67, 66, 57, 67, 47, 66, 48, 40, 39, 49, 38, 47, 48, 62, 61, 59, 62, 59, 58, 66, 56, 57, 65, 66, 47, 44, 40, 43, 45, 39, 40, 13, 12, 70, 27, 26, 52, 27, 52, 28, 63, 62, 58, 63, 58, 57, 65, 56, 66, 39, 38, 48, 52, 43, 42, 53, 52, 26, 53, 43, 52, 64, 57, 56, 63, 57, 64, 46, 38, 39, 44, 45, 40, 46, 39, 45, 61, 71, 70, 61, 70, 60, 71, 61, 62, 13, 70, 71, 55, 47, 38, 73, 56, 65, 72, 62, 63, 71, 62, 72, 72, 63, 64, 73, 72, 64, 73, 64, 56, 54, 45, 44, 44, 43, 53, 54, 44, 53, 46, 45, 54, 55, 38, 46, 54, 55, 46, 25, 53, 26, 14, 13, 71, 24, 54, 53, 24, 53, 25, 72, 15, 14, 72, 14, 71, 23, 54, 24, 16, 15, 72, 22, 20, 23, 19, 18, 17, 73, 19, 16, 19, 17, 16, 65, 47, 55, 73, 65, 55, 23, 73, 55, 54, 23, 55, 21, 20, 22, 23, 19, 73, 73, 16, 72, 23, 20, 19], "vertices": [1, 77, -42.61, -38.81, 1, 1, 77, -24.38, -35.96, 1, 1, 77, -15.7, -31.92, 1, 1, 77, -7.7, -32.3, 1, 1, 77, -13.34, -36.33, 1, 1, 77, -16.75, -44.35, 1, 1, 77, -21.25, -55.82, 1, 1, 77, -31.96, -67.28, 1, 1, 77, -24.93, -67.25, 1, 1, 77, -9.78, -60.88, 1, 1, 77, 4, -54.6, 1, 1, 77, 13.75, -45.04, 1, 1, 77, 23.03, -52.59, 1, 1, 77, 34.75, -50.32, 1, 1, 77, 53.45, -41.87, 1, 1, 77, 61.67, -35.4, 1, 1, 77, 77.93, -31.91, 1, 1, 77, 82.77, -34.5, 1, 1, 77, 102.86, -33.81, 1, 1, 77, 106.32, -23.34, 1, 1, 77, 106.11, 23.69, 1, 1, 77, 100.34, 34.32, 1, 1, 77, 81.65, 35.04, 1, 1, 77, 75.84, 29.59, 1, 1, 77, 58.35, 31.11, 1, 1, 77, 49.25, 42.33, 1, 1, 77, 34.37, 45.08, 1, 1, 77, 22.49, 48.64, 1, 1, 77, 18.28, 47.01, 1, 1, 77, 6.78, 56.81, 1, 1, 77, -10.13, 62.76, 1, 1, 77, -33.05, 63.71, 1, 1, 77, -19.34, 54.68, 1, 1, 77, -15.45, 38.82, 1, 1, 77, -3.56, 32.24, 1, 1, 77, -16.84, 34.59, 1, 1, 77, -27.51, 39.17, 1, 1, 77, -42.97, 40.71, 1, 1, 77, 35.34, 14.07, 1, 1, 77, 33.11, 16.87, 1, 1, 77, 30.06, 19.89, 1, 1, 77, 26.64, 23.28, 1, 1, 77, 24.19, 25.94, 1, 1, 77, 30.11, 25.15, 1, 1, 77, 33.67, 22.65, 1, 1, 77, 35.91, 18.81, 1, 1, 77, 35.78, 15.63, 1, 1, 77, 33.08, 6.37, 1, 1, 77, 27.9, 7.45, 1, 1, 77, 20.1, 13.93, 1, 1, 77, 12.21, 22.92, 1, 1, 77, 13.22, 28.99, 1, 1, 77, 22.15, 33.99, 1, 1, 77, 35.4, 33.16, 1, 1, 77, 44.56, 21, 1, 1, 77, 40.54, 10.4, 1, 1, 77, 35.32, -17.18, 1, 1, 77, 33.15, -19.16, 1, 1, 77, 30.85, -21.48, 1, 1, 77, 28.62, -23.8, 1, 1, 77, 25.51, -27.83, 1, 1, 77, 30.2, -27.2, 1, 1, 77, 33.32, -24.67, 1, 1, 77, 35.34, -22.01, 1, 1, 77, 35.81, -19.08, 1, 1, 77, 33.52, -9.57, 1, 1, 77, 28.15, -9.53, 1, 1, 77, 20.36, -16.09, 1, 1, 77, 12.85, -25.1, 1, 1, 77, 14.92, -32.64, 1, 1, 77, 26.09, -35.58, 1, 1, 77, 38.04, -32.19, 1, 1, 77, 43.58, -23.4, 1, 1, 77, 41.02, -14.16, 1], "hull": 38, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 0, 74, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 76, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 94, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 112, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 130], "width": 131, "height": 149}}, "kho bau tu linh10": {"kho bau tu linh10": {"type": "mesh", "uvs": [1, 0.72908, 0.92538, 0.68508, 0.85233, 0.62108, 0.78176, 0.52108, 0.70871, 0.42508, 0.63195, 0.35108, 0.523, 0.30108, 0.42271, 0.27309, 0.34967, 0.23709, 0.26176, 0.16709, 0.18995, 0.09109, 0.10576, 0.01509, 0.04757, 0, 0, 0, 0, 0.03509, 0.05252, 0.06709, 0.10329, 0.12109, 0.13919, 0.21109, 0.17757, 0.30508, 0.24071, 0.37908, 0.31624, 0.43308, 0.40043, 0.48508, 0.4809, 0.53308, 0.56138, 0.58508, 0.62947, 0.64108, 0.69757, 0.73108, 0.73842, 0.82908, 0.80652, 0.92708, 0.89566, 1, 0.96623, 1], "triangles": [14, 13, 12, 15, 12, 11, 14, 12, 15, 16, 15, 11, 16, 11, 10, 17, 16, 10, 17, 10, 9, 18, 17, 9, 18, 9, 8, 19, 18, 8, 20, 19, 8, 20, 8, 7, 21, 20, 7, 21, 7, 6, 22, 21, 6, 22, 6, 5, 23, 22, 5, 4, 23, 5, 24, 23, 4, 3, 24, 4, 24, 3, 2, 25, 24, 2, 1, 26, 25, 1, 25, 2, 27, 26, 1, 27, 1, 0, 29, 28, 27, 0, 29, 27], "vertices": [1, 67, -2.65, -9.29, 1, 2, 67, 5.67, -8.7, 0.92485, 68, -4.37, -9.87, 0.07515, 2, 67, 14.37, -9.38, 0.1925, 68, 4.25, -8.53, 0.8075, 2, 68, 14.09, -9.22, 0.81925, 69, -4.58, -8.92, 0.18075, 2, 68, 23.97, -9.53, 0.09013, 69, 5.26, -9.93, 0.90987, 2, 69, 14.64, -9.53, 0.9585, 70, -7.23, -7.87, 0.0415, 2, 69, 25.99, -5.97, 0.05021, 70, 4.66, -7.83, 0.94979, 2, 70, 15.29, -6.66, 0.96577, 71, -3.85, -8.04, 0.03423, 2, 70, 23.3, -6.79, 0.23448, 71, 3.89, -5.95, 0.76552, 1, 71, 14.13, -4.9, 1, 1, 71, 23.14, -5.09, 1, 1, 71, 33.25, -4.58, 1, 1, 71, 38.95, -2.17, 1, 1, 71, 43.19, 0.48, 1, 1, 71, 41.98, 2.41, 1, 1, 71, 36.2, 1.25, 1, 1, 71, 29.82, 1.39, 1, 1, 71, 23.52, 4.35, 1, 1, 71, 16.86, 7.39, 1, 1, 71, 8.69, 7.95, 1, 2, 70, 23.15, 6.42, 0.36015, 71, 0.1, 6.71, 0.63985, 2, 70, 13.72, 7.23, 0.99992, 71, -9.18, 4.89, 8e-05, 2, 69, 21.41, 9.07, 0.0738, 70, 4.74, 7.89, 0.9262, 2, 69, 12.49, 7.26, 0.8114, 70, -4.32, 8.8, 0.1886, 3, 68, 22.08, 6.68, 0.07341, 69, 4.51, 6.38, 0.92526, 70, -12.2, 10.31, 0.00133, 3, 67, 26.29, 3.77, 0.0069, 68, 12.85, 7, 0.93661, 69, -4.67, 7.34, 0.05649, 2, 67, 19.78, 7.84, 0.1868, 68, 5.58, 9.47, 0.8132, 2, 67, 10.66, 10.74, 0.7932, 68, -3.97, 10.21, 0.2068, 2, 67, 0.18, 11.26, 0.99959, 68, -14.29, 8.31, 0.00041, 1, 67, -6.59, 8.24, 1], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58], "width": 105, "height": 65}}, "kho bau tu linh11": {"kho bau tu linh11": {"type": "mesh", "uvs": [1, 0.69997, 1, 0.907, 0.96134, 1, 0.89398, 1, 0.80416, 0.93349, 0.71789, 0.81071, 0.63398, 0.65664, 0.56071, 0.51701, 0.47089, 0.38701, 0.36098, 0.27627, 0.26289, 0.19923, 0.12816, 0.16793, 0.05607, 0.17275, 0, 0.20164, 0, 0.14386, 0.0608, 0.07405, 0.15298, 0.01868, 0.26644, 0, 0.39762, 0, 0.5158, 0.01386, 0.61743, 0.06923, 0.70607, 0.15831, 0.7758, 0.27627, 0.81361, 0.4159, 0.86443, 0.55312, 0.94598, 0.65182], "triangles": [11, 15, 16, 12, 14, 15, 11, 12, 15, 10, 16, 17, 11, 16, 10, 13, 14, 12, 18, 10, 17, 9, 10, 18, 19, 9, 18, 20, 9, 19, 8, 20, 21, 8, 21, 22, 20, 8, 9, 7, 8, 22, 23, 7, 22, 6, 7, 23, 24, 6, 23, 5, 6, 24, 5, 24, 25, 5, 25, 0, 0, 4, 5, 1, 4, 0, 3, 4, 1, 2, 3, 1], "vertices": [1, 62, 2.44, -2.09, 1, 2, 62, -0.03, 8.81, 0.96442, 63, -12.85, 6.16, 0.03558, 2, 62, 3, 14.65, 0.85883, 63, -11.14, 12.52, 0.14117, 2, 62, 10.22, 16.3, 0.61362, 63, -4.43, 15.67, 0.38638, 3, 62, 20.66, 14.98, 0.16313, 63, 6.03, 16.63, 0.78016, 64, -12.61, 14.27, 0.05671, 3, 62, 31.38, 10.62, 0.00265, 63, 17.44, 14.67, 0.50776, 64, -1.05, 14.79, 0.48959, 3, 63, 29.34, 11.07, 0.03437, 64, 11.34, 13.81, 0.87856, 65, -6.16, 15.29, 0.08707, 3, 64, 22.33, 12.74, 0.36302, 65, 4.41, 12.11, 0.63624, 66, -14.31, 16.12, 0.00074, 3, 64, 34.44, 13.18, 0.0061, 65, 16.38, 10.21, 0.75256, 66, -3.3, 11.07, 0.24134, 2, 65, 29.87, 10.22, 0.00739, 66, 9.7, 7.46, 0.99261, 1, 66, 21.08, 5.39, 1, 1, 66, 35.95, 6.5, 1, 1, 66, 43.69, 8.24, 1, 1, 66, 49.46, 10.93, 1, 1, 66, 50.04, 7.87, 1, 1, 66, 44.18, 2.91, 1, 1, 66, 34.78, -1.92, 1, 1, 66, 22.71, -5.25, 1, 2, 65, 32.88, -4.93, 0.0136, 66, 8.53, -7.95, 0.9864, 2, 65, 20.9, -10.04, 0.82357, 66, -4.38, -9.65, 0.17643, 2, 64, 32.1, -10.24, 0.02118, 65, 9.56, -12.32, 0.97882, 2, 64, 21.44, -12.36, 0.49427, 65, -1.32, -12.34, 0.50573, 2, 64, 11.48, -11.98, 0.9613, 65, -11.02, -10.04, 0.0387, 2, 63, 16.99, -9.1, 0.12426, 64, 3.58, -8.53, 0.87574, 2, 63, 8.78, -4.78, 0.9551, 64, -5.36, -6.06, 0.0449, 2, 62, 8.81, -3.31, 0.85005, 63, -1.6, -3.77, 0.14995], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50], "width": 110, "height": 54}}, "kho bau tu linh12": {"kho bau tu linh12": {"type": "mesh", "uvs": [0.99421, 0.00935, 0.84484, 0, 0.65474, 0, 0.48274, 0, 0.3379, 0.16639, 0.23605, 0.57769, 0.10253, 0.77213, 0, 0.73474, 0, 0.85439, 0.09121, 1, 0.19984, 0.97404, 0.34469, 0.80204, 0.46463, 0.59265, 0.60269, 0.55526, 0.77469, 0.61509, 0.93537, 0.63004, 1, 0.61509], "triangles": [2, 4, 3, 13, 4, 2, 12, 5, 4, 13, 12, 4, 1, 13, 2, 14, 13, 1, 16, 15, 1, 16, 1, 0, 14, 1, 15, 11, 5, 12, 6, 5, 11, 8, 7, 6, 10, 6, 11, 9, 8, 6, 10, 9, 6], "vertices": [1, 57, -6.07, -8.67, 1, 2, 57, 5.23, -7.53, 0.84964, 58, -3.39, -7.36, 0.15036, 3, 58, 11.03, -6.43, 0.79411, 59, -2.72, -6.08, 0.20435, 60, -16.01, 0.57, 0.00154, 2, 59, 10.3, -7.33, 0.35434, 60, -4.66, -5.92, 0.64566, 2, 60, 6.79, -8.06, 0.90957, 61, -3.42, -8.19, 0.09043, 1, 61, 7.82, -3.4, 1, 1, 61, 18.89, -4.15, 1, 1, 61, 25.39, -8.52, 1, 1, 61, 26.67, -6.09, 1, 1, 61, 22.08, 0.1, 1, 1, 61, 14.49, 3.4, 1, 3, 59, 22.5, 10.03, 0.00042, 60, 13.6, 4.89, 0.11071, 61, 2.9, 5, 0.88887, 2, 59, 12.97, 6.11, 0.52147, 60, 3.29, 5.23, 0.47853, 2, 58, 14.16, 6.56, 0.32691, 59, 2.44, 6.26, 0.67309, 2, 57, 8.84, 7.15, 0.39674, 58, 1.02, 7.1, 0.60326, 1, 57, -3.33, 6.03, 1, 1, 57, -8.17, 5.11, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32], "width": 76, "height": 23}}, "kho bau tu linh13": {"kho bau tu linh13": {"type": "mesh", "uvs": [0.95474, 0, 0.90274, 0, 0.80074, 0.12267, 0.68074, 0.2463, 0.54074, 0.35176, 0.42674, 0.45721, 0.33874, 0.66449, 0.25474, 0.78449, 0.15074, 0.85358, 0, 0.87176, 0, 0.94085, 0.12874, 1, 0.25874, 1, 0.36474, 0.92994, 0.47474, 0.79903, 0.57874, 0.64267, 0.70874, 0.5663, 0.84474, 0.49358, 0.96274, 0.37721, 1, 0.28994, 1, 0.13721], "triangles": [20, 1, 0, 1, 20, 2, 2, 18, 3, 17, 3, 18, 16, 4, 3, 2, 20, 19, 19, 18, 2, 17, 16, 3, 15, 5, 4, 16, 15, 4, 6, 5, 15, 14, 6, 15, 7, 6, 14, 13, 7, 14, 10, 9, 8, 11, 10, 8, 12, 7, 13, 8, 7, 12, 11, 8, 12], "vertices": [2, 52, -2.23, -8.14, 0.99991, 53, -12.16, -7.37, 9e-05, 2, 52, 0.77, -8.98, 0.98994, 53, -9.21, -8.37, 0.01006, 2, 52, 7.76, -6.75, 0.63786, 53, -2.11, -6.51, 0.36214, 1, 53, 6.02, -4.97, 1, 2, 53, 15.09, -4.39, 0.37515, 54, 0.08, -4.64, 0.62485, 1, 54, 7.64, -5.99, 1, 2, 54, 15.97, -3.71, 0.34548, 55, 0.98, -3.66, 0.65452, 2, 55, 7.36, -3.08, 0.98379, 56, -1.83, -4.34, 0.01621, 2, 55, 13.84, -4.57, 0.03515, 56, 4.6, -2.66, 0.96485, 1, 56, 13.66, -2.92, 1, 1, 56, 13.88, -0.65, 1, 1, 56, 6.37, 2.02, 1, 2, 55, 11.03, 3.01, 0.60179, 56, -1.39, 2.76, 0.39821, 2, 54, 20.01, 4.22, 0.00363, 55, 4.44, 4.54, 0.99637, 2, 54, 12.14, 4.75, 0.87639, 55, -3.45, 4.51, 0.12361, 2, 53, 16.03, 5.44, 0.09968, 54, 4.05, 4.39, 0.90032, 3, 52, 17.05, 5.85, 0.00982, 53, 7.83, 5.57, 0.96038, 54, -3.69, 7.08, 0.02981, 2, 52, 8.54, 5.75, 0.68226, 53, -0.67, 5.92, 0.31774, 1, 52, 0.68, 3.97, 1, 1, 52, -2.25, 1.81, 1, 1, 52, -3.62, -3.04, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40], "width": 60, "height": 33}}, "kho bau tu linh14": {"kho bau tu linh14": {"type": "mesh", "uvs": [0.98378, 1, 1, 1, 1, 0.95527, 0.97486, 0.89158, 0.94214, 0.82977, 0.90941, 0.79044, 0.86677, 0.7792, 0.83009, 0.79419, 0.7934, 0.8073, 0.74679, 0.79793, 0.69424, 0.76609, 0.63474, 0.70803, 0.5802, 0.64247, 0.51972, 0.56568, 0.45923, 0.48701, 0.40072, 0.41209, 0.33925, 0.33343, 0.28173, 0.2585, 0.22719, 0.18546, 0.16869, 0.11241, 0.11316, 0.0506, 0.05961, 0.00752, 0.02293, 0, 0, 0, 0, 0.03936, 0.04672, 0.08057, 0.09928, 0.12739, 0.1439, 0.17422, 0.18555, 0.22854, 0.23314, 0.29222, 0.28471, 0.35965, 0.32834, 0.41771, 0.37296, 0.48139, 0.41758, 0.5507, 0.47113, 0.62187, 0.52467, 0.69866, 0.58318, 0.76609, 0.63672, 0.82228, 0.68829, 0.87473, 0.73291, 0.91593, 0.77654, 0.92904, 0.81323, 0.91593, 0.84298, 0.88971, 0.87471, 0.88035, 0.91338, 0.89346, 0.94412, 0.92904], "triangles": [24, 23, 22, 21, 24, 22, 25, 21, 20, 25, 24, 21, 26, 25, 20, 26, 20, 19, 27, 26, 19, 27, 19, 18, 28, 27, 18, 28, 18, 17, 29, 28, 17, 30, 17, 16, 29, 17, 30, 31, 30, 16, 31, 16, 15, 32, 31, 15, 32, 15, 14, 33, 32, 14, 33, 14, 13, 34, 33, 13, 34, 13, 12, 35, 34, 12, 35, 12, 11, 36, 35, 11, 36, 11, 10, 37, 36, 10, 38, 37, 10, 38, 10, 9, 43, 6, 5, 43, 5, 4, 7, 6, 43, 42, 7, 43, 8, 7, 42, 44, 43, 4, 3, 44, 4, 39, 38, 9, 39, 9, 8, 8, 40, 39, 42, 41, 8, 41, 40, 8, 45, 44, 3, 45, 3, 2, 0, 45, 2, 0, 2, 1], "vertices": [1, 21, -0.3, 1.05, 1, 1, 21, -1.41, -0.52, 1, 1, 21, 0.88, -2.15, 1, 2, 21, 5.89, -2.03, 0.53483, 22, -0.35, -2.01, 0.46517, 2, 22, 5.13, -2.53, 0.75479, 23, -1.38, -2.22, 0.24521, 1, 23, 3.16, -3.04, 1, 2, 23, 8.12, -1.78, 0.08258, 24, 1.18, -2.81, 0.91742, 2, 24, 5.64, -2.57, 0.73646, 25, -1.16, -2.33, 0.26354, 2, 25, 3.13, -3.48, 0.72726, 26, -0.17, -4.4, 0.27274, 3, 25, 7.87, -6.42, 0.02805, 26, 5.4, -4.04, 0.69451, 27, -0.26, -4.3, 0.27744, 3, 26, 11.9, -4.96, 0.001, 27, 6.23, -3.3, 0.97686, 28, -3.18, -3.42, 0.02213, 1, 28, 4.79, -3.22, 1, 2, 28, 12.47, -3.71, 0.13018, 29, 3.42, -3.45, 0.86982, 2, 29, 12.09, -3.24, 0.46849, 30, 0.35, -3.3, 0.53151, 2, 30, 9.08, -2.91, 0.84155, 31, -1.31, -2.99, 0.15845, 1, 31, 7.09, -2.54, 1, 1, 32, 1.95, -2.21, 1, 2, 32, 10.26, -2.26, 0.98582, 33, -2.21, -2.55, 0.01418, 1, 33, 5.75, -2.67, 1, 2, 33, 14.09, -2.53, 0.00485, 34, 1.01, -2.71, 0.99515, 2, 34, 8.66, -3.3, 0.93796, 35, -3.3, -2.96, 0.06204, 1, 35, 3.63, -3.13, 1, 1, 35, 7.86, -1.97, 1, 1, 35, 10.4, -0.96, 1, 1, 35, 9.48, 1.35, 1, 1, 35, 3.36, 1.71, 1, 2, 34, 8.01, 1.77, 0.9956, 35, -3.54, 2.15, 0.0044, 2, 33, 14.32, 2.35, 0.04131, 34, 1.94, 2.08, 0.95868, 1, 33, 8.3, 2.37, 1, 1, 33, 1.36, 2.47, 1, 1, 32, 6.38, 2.81, 1, 2, 31, 13.73, 2.96, 0.30423, 32, 0.03, 2.91, 0.69577, 1, 31, 7.08, 2.94, 1, 2, 30, 10.62, 3.28, 0.58343, 31, 0.21, 3.2, 0.41657, 1, 30, 2.83, 3.01, 1, 1, 29, 6.76, 3.25, 1, 2, 28, 8.4, 2.95, 0.60321, 29, -1.37, 2.7, 0.39679, 2, 27, 10.77, 2.93, 0.22181, 28, 1.11, 2.98, 0.77819, 1, 27, 3.81, 3.14, 1, 2, 26, 5.77, 3.57, 0.87691, 27, -2.1, 3.09, 0.12309, 2, 25, 8.26, 2.56, 0.16432, 26, 0.51, 3.5, 0.83568, 3, 24, 8.84, 4.68, 0.02675, 25, 3.97, 3.71, 0.91018, 26, -3.65, 1.95, 0.06307, 3, 23, 8.12, 5.73, 0.01119, 24, 5.08, 3.62, 0.59881, 25, 0.06, 3.75, 0.39, 3, 23, 4.84, 3.76, 0.44153, 24, 1.26, 3.64, 0.55761, 25, -3.59, 4.86, 0.00087, 3, 22, 5.21, 2.74, 0.47854, 23, 0.27, 2.79, 0.51734, 24, -3.15, 5.18, 0.00413, 2, 21, 6.08, 2.32, 0.29288, 22, 0.96, 2.14, 0.70712], "hull": 46, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 0, 90], "width": 119, "height": 63}}, "kho bau tu linh15": {"kho bau tu linh15": {"type": "mesh", "uvs": [0.00298, 0.94154, 0.03848, 0.89539, 0.07398, 0.85102, 0.10948, 0.82439, 0.16602, 0.81729, 0.20809, 0.82972, 0.25411, 0.83682, 0.29224, 0.82439, 0.34746, 0.78534, 0.40137, 0.73919, 0.44344, 0.69659, 0.49603, 0.63447, 0.55257, 0.57234, 0.59859, 0.50844, 0.64329, 0.44987, 0.688, 0.38242, 0.72613, 0.31497, 0.77214, 0.23332, 0.81685, 0.16232, 0.85103, 0.11617, 0.89179, 0.0647, 0.92861, 0.02387, 0.96542, 0, 1, 0, 0.99829, 0.04162, 0.94438, 0.08245, 0.90757, 0.13037, 0.86944, 0.1783, 0.84051, 0.228, 0.81422, 0.27415, 0.77872, 0.33982, 0.74453, 0.40194, 0.70377, 0.46584, 0.66038, 0.53152, 0.61568, 0.59542, 0.56572, 0.66109, 0.50918, 0.72322, 0.45922, 0.77824, 0.41057, 0.82617, 0.34615, 0.87764, 0.29224, 0.91492, 0.24622, 0.92734, 0.191, 0.92557, 0.16339, 0.89539, 0.1292, 0.89894, 0.08187, 0.93977, 0.04111, 0.97882, 0.02665, 1, 0, 1], "triangles": [47, 48, 46, 48, 0, 46, 46, 0, 45, 0, 1, 45, 44, 45, 2, 42, 5, 41, 41, 6, 40, 41, 5, 6, 42, 43, 5, 6, 7, 40, 40, 7, 39, 45, 1, 2, 2, 3, 44, 44, 3, 43, 43, 4, 5, 43, 3, 4, 39, 8, 38, 39, 7, 8, 8, 9, 38, 38, 9, 37, 9, 10, 37, 37, 10, 36, 10, 11, 36, 36, 11, 35, 11, 12, 35, 35, 12, 34, 12, 13, 34, 34, 13, 33, 13, 14, 33, 33, 14, 32, 14, 15, 32, 32, 15, 31, 15, 16, 31, 31, 16, 30, 30, 16, 29, 16, 17, 29, 29, 17, 28, 17, 18, 28, 28, 18, 27, 18, 19, 27, 27, 19, 26, 19, 20, 26, 26, 20, 25, 20, 21, 25, 24, 21, 22, 24, 25, 21, 24, 22, 23], "vertices": [1, 7, 1.52, 3.36, 1, 2, 7, 6.84, 3.07, 0.55341, 8, -0.5, 3.03, 0.44659, 2, 8, 4.68, 3.69, 0.95524, 9, -3.69, 2.38, 0.04476, 2, 8, 9.04, 3.2, 0.23385, 9, 0.48, 3.73, 0.76615, 2, 9, 6.58, 3.12, 0.73936, 10, -1.57, 2.71, 0.26064, 3, 10, 3.08, 2.85, 0.96202, 11, -1.76, 3.75, 0.0358, 12, -5.68, 5.53, 0.00218, 3, 10, 8.04, 3.5, 0.04159, 11, 3.1, 2.55, 0.58777, 12, -1.4, 2.95, 0.37064, 1, 12, 2.76, 2.15, 1, 2, 12, 9.48, 2.51, 0.17643, 13, 1.3, 2.46, 0.82357, 2, 13, 8.19, 2.71, 0.5527, 14, -0.3, 2.85, 0.4473, 1, 14, 5.38, 2.69, 1, 1, 15, 3.68, 2.8, 1, 2, 15, 11.55, 2.64, 0.09588, 16, 1.24, 2.64, 0.90412, 1, 16, 8.37, 2.88, 1, 2, 16, 15.1, 2.91, 0.09058, 17, 2.6, 2.89, 0.90942, 2, 17, 9.83, 3.08, 0.89579, 18, -1.69, 3.33, 0.10421, 1, 18, 5.1, 3.14, 1, 2, 18, 13.3, 2.91, 0.4471, 19, 0.23, 3.05, 0.5529, 1, 19, 7.66, 2.58, 1, 2, 19, 12.83, 1.82, 0.28879, 20, 0.39, 1.94, 0.71121, 1, 20, 6.42, 2.05, 1, 1, 20, 11.56, 1.81, 1, 1, 20, 15.79, 0.57, 1, 1, 20, 18.56, -1.94, 1, 1, 20, 16.2, -4.29, 1, 1, 20, 9.68, -2.81, 1, 2, 19, 15.57, -3.75, 0.00865, 20, 4.16, -2.99, 0.99135, 2, 19, 10.04, -2.74, 0.88869, 20, -1.46, -3.08, 0.11131, 1, 19, 4.98, -2.61, 1, 2, 18, 13.3, -2.68, 0.40596, 19, 0.32, -2.54, 0.59404, 1, 18, 6.8, -2.63, 1, 2, 17, 12.89, -2.43, 0.29259, 18, 0.61, -2.54, 0.70741, 1, 17, 6.15, -2.74, 1, 2, 16, 11.9, -3.08, 0.42507, 17, -0.89, -2.94, 0.57493, 2, 16, 4.87, -3.42, 0.99993, 17, -7.92, -2.93, 7e-05, 2, 15, 8.04, -3.69, 0.77906, 16, -2.66, -3.46, 0.22094, 2, 14, 9.61, -3.4, 0.44685, 15, 0.17, -3.53, 0.55315, 2, 13, 12, -3.15, 0.00697, 14, 2.64, -3.48, 0.99303, 2, 13, 5.53, -3.8, 0.95269, 14, -3.86, -3.21, 0.04731, 3, 11, 12.54, -1.96, 0.09488, 12, 6.29, -4.15, 0.78623, 13, -2.56, -3.84, 0.11889, 2, 11, 6.38, -4.17, 0.99247, 12, -0.25, -4.44, 0.00753, 2, 10, 8.97, -3.73, 0.17958, 11, 1.33, -4.52, 0.82042, 3, 9, 7.57, -5.9, 0.1137, 10, 3.15, -5.04, 0.83802, 11, -4.57, -3.62, 0.04827, 3, 8, 10.47, -4.81, 0.00073, 9, 5.11, -2.96, 0.66812, 10, -0.33, -3.42, 0.33116, 3, 8, 7.31, -2.89, 0.34027, 9, 1.43, -2.53, 0.6578, 10, -3.84, -4.59, 0.00193, 2, 7, 7.44, -2.78, 0.21022, 8, 1.25, -2.58, 0.78978, 1, 7, 2.15, -1.69, 1, 1, 7, -0.15, -1.7, 1, 1, 7, -2.12, 0.4, 1], "hull": 49, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 0, 96], "width": 108, "height": 80}}, "kho bau tu linh16": {"kho bau tu linh16": {"x": 4.85, "y": 3.59, "rotation": -90, "width": 186, "height": 61}}, "kho bau tu linh17": {"kho bau tu linh17": {"x": 6.66, "y": -0.25, "rotation": -90, "width": 186, "height": 49}}, "kho bau tu linh2": {"kho bau tu linh2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, 12.95, 17.55, 1, 1, 2, 13.92, -18.43, 1, 1, 2, -3.07, -18.89, 1, 1, 2, -4.04, 17.09, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 36, "height": 17}}, "kho bau tu linh20": {"kho bau tu linh20": {"x": 0.08, "y": 0.33, "rotation": -90, "width": 64, "height": 52}}, "kho bau tu linh21": {"kho bau tu linh8": {"type": "mesh", "uvs": [0.78607, 0.3831, 0.50291, 0.26293, 0.26357, 0.05393, 0, 0.29428, 0.06806, 0.63652, 0.40515, 0.93173, 0.67146, 1, 1, 1, 1, 0.86642, 0.79618, 0.60778], "triangles": [1, 4, 3, 1, 3, 2, 5, 4, 1, 9, 5, 1, 9, 1, 0, 6, 5, 9, 8, 6, 9, 6, 8, 7], "vertices": [1, 1, 7.89, 34.92, 1, 1, 1, -1.86, 52.44, 1, 1, 1, -18.7, 67.18, 1, 1, 1, 0.4, 83.7, 1, 1, 1, 27.81, 79.69, 1, 1, 1, 51.59, 58.93, 1, 1, 1, 57.19, 42.42, 1, 1, 1, 57.34, 22.01, 1, 1, 1, 46.66, 21.93, 1, 1, 1, 25.87, 34.43, 1], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 62, "height": 80}}, "kho bau tu linh22": {"kho bau tu linh20": {"x": 0.08, "y": 0.33, "rotation": -90, "width": 64, "height": 52}}, "kho bau tu linh23": {"kho bau tu linh20": {"x": 0.08, "y": 0.33, "rotation": -90, "width": 64, "height": 52}}, "kho bau tu linh24": {"kho bau tu linh3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [3, 3, 65.81, 27.74, 0.04613, 4, 20.25, 28.97, 0.03301, 5, 14.94, 27.06, 0.92086, 3, 3, 63.53, -30.22, 0.1757, 4, 20.25, -29.03, 0.36776, 5, 7.75, -30.5, 0.45654, 3, 3, 2.58, -27.83, 0.9917, 4, -40.75, -29.03, 0.0043, 5, -52.78, -22.93, 0.004, 2, 3, 4.85, 30.13, 0.98441, 5, -45.59, 34.62, 0.01559], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 61}}, "kho bau tu linh3": {"kho bau tu linh3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [3, 3, 65.81, 27.74, 0.04613, 4, 20.25, 28.97, 0.03301, 5, 14.94, 27.06, 0.92086, 3, 3, 63.53, -30.22, 0.1757, 4, 20.25, -29.03, 0.36776, 5, 7.75, -30.5, 0.45654, 3, 3, 2.58, -27.83, 0.9917, 4, -40.75, -29.03, 0.0043, 5, -52.78, -22.93, 0.004, 2, 3, 4.85, 30.13, 0.98441, 5, -45.59, 34.62, 0.01559], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "height": 61}}, "kho bau tu linh4": {"kho bau tu linh4": {"type": "mesh", "uvs": [0, 1, 0.07548, 0.99788, 0.22006, 0.92658, 0.32637, 0.82217, 0.40291, 0.68975, 0.43268, 0.55224, 0.45182, 0.40455, 0.51135, 0.30523, 0.61128, 0.23902, 0.71972, 0.19573, 0.84729, 0.14989, 0.94722, 0.08623, 1, 0.0353, 1, 0, 0.90683, 0.03021, 0.79839, 0.06077, 0.67932, 0.08369, 0.54962, 0.1117, 0.4263, 0.16008, 0.35614, 0.18555, 0.09461, 0.18045, 0, 0.27467, 1e-05, 0.66174], "triangles": [14, 13, 12, 11, 14, 12, 10, 15, 14, 10, 14, 11, 9, 16, 15, 9, 15, 10, 8, 17, 16, 8, 16, 9, 7, 18, 17, 7, 17, 8, 7, 19, 18, 6, 19, 7, 20, 19, 6, 21, 6, 22, 20, 6, 21, 5, 22, 6, 4, 22, 5, 3, 22, 4, 2, 22, 3, 1, 22, 2, 0, 22, 1], "vertices": [1, 72, -15.5, -11.57, 1, 1, 72, -10.14, -17.21, 1, 1, 72, 4.4, -24.14, 1, 1, 72, 18.4, -26.22, 1, 1, 72, 32.14, -24.42, 1, 1, 72, 42.96, -18.76, 1, 1, 72, 53.7, -11.69, 1, 1, 72, 64.14, -10.5, 1, 1, 72, 75.27, -14.31, 1, 1, 72, 85.53, -20.09, 1, 1, 72, 97.27, -27.18, 1, 1, 72, 108.24, -31.14, 1, 1, 72, 115.14, -32.23, 1, 1, 72, 117.39, -30.19, 1, 1, 72, 109.02, -24.82, 1, 1, 72, 99.57, -18.3, 1, 1, 72, 89.88, -10.54, 1, 1, 72, 79.13, -2.25, 1, 1, 72, 67.52, 4.37, 1, 1, 72, 61.04, 8.26, 1, 1, 72, 43.28, 28.52, 1, 1, 72, 30.74, 30.3, 1, 1, 72, 6.06, 7.95, 1], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44], "width": 103, "height": 86}}, "kho bau tu linh5": {"kho bau tu linh5": {"type": "mesh", "uvs": [0.03614, 0.7001, 0.15421, 0.63095, 0.26085, 0.53412, 0.36559, 0.37967, 0.46271, 0.27824, 0.60173, 0.18373, 0.74075, 0.1284, 0.89119, 0.05924, 1, 0, 1, 0.0408, 0.91785, 0.14223, 0.79788, 0.25058, 0.68362, 0.36123, 0.5865, 0.49263, 0.52175, 0.63786, 0.42844, 0.76926, 0.30846, 0.8753, 0.1504, 0.9606, 0, 1], "triangles": [18, 0, 17, 16, 0, 1, 16, 17, 0, 16, 1, 15, 1, 2, 15, 15, 2, 14, 2, 3, 14, 14, 3, 13, 3, 4, 13, 13, 4, 12, 4, 5, 12, 11, 5, 6, 11, 12, 5, 11, 6, 10, 6, 7, 10, 10, 7, 9, 9, 7, 8], "vertices": [2, 46, -0.51, 10.87, 0.94902, 47, -10.45, 13.77, 0.05098, 3, 46, 14.25, 13.99, 0.26999, 47, 4.62, 13.27, 0.69127, 48, -8.44, 17.11, 0.03874, 3, 46, 28.36, 19.98, 0.00055, 47, 19.76, 15.72, 0.34875, 48, 6.61, 14.19, 0.6507, 3, 47, 37.24, 23.12, 0.00401, 48, 25.57, 15.1, 0.72017, 49, 2.34, 15.53, 0.27582, 3, 48, 40.21, 13.28, 0.12428, 49, 17.07, 14.74, 0.7212, 50, -7.55, 13.64, 0.15452, 2, 49, 34.82, 10.14, 0.0181, 50, 10.73, 12.17, 0.9819, 2, 50, 26.93, 7.62, 0.53755, 51, 1.29, 8.16, 0.46245, 1, 51, 19.55, 5.16, 1, 1, 51, 33.2, 3.75, 1, 1, 51, 31.25, 0.4, 1, 1, 51, 18.25, -3.2, 1, 2, 50, 25.91, -5.68, 0.06296, 51, 1.15, -5.18, 0.93704, 2, 49, 29.92, -8.54, 0.15934, 50, 9.14, -7.08, 0.84066, 1, 49, 13.22, -9.81, 1, 2, 48, 19.43, -14.67, 0.16864, 49, -1.71, -14.6, 0.83136, 3, 47, 26.44, -13.01, 0.23031, 48, 2.98, -15.09, 0.63847, 49, -18.09, -16.16, 0.13122, 4, 46, 25.91, -12.8, 0.04695, 47, 9.54, -15.52, 0.93875, 48, -13.75, -11.62, 0.01366, 49, -35.02, -13.87, 0.00065, 2, 46, 6.32, -16.3, 0.88431, 47, -10.32, -14.25, 0.11569, 1, 46, -11.37, -15.79, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36], "width": 115, "height": 95}}, "kho bau tu linh6": {"kho bau tu linh6": {"type": "mesh", "uvs": [0.03967, 0.34475, 0, 0.87275, 0.13308, 1, 0.30268, 0.954, 0.45773, 0.80932, 0.6098, 0.8576, 0.76422, 0.85075, 0.9056, 0.48775, 1, 0, 0.83744, 0.24575, 0.69353, 0.42175, 0.51475, 0.29604, 0.31905, 0.32904, 0.18065, 0.46975], "triangles": [7, 9, 8, 10, 9, 7, 4, 12, 11, 4, 11, 10, 13, 12, 4, 6, 10, 7, 5, 4, 10, 6, 5, 10, 1, 0, 13, 3, 1, 13, 4, 3, 13, 2, 1, 3], "vertices": [2, 41, 0.54, 4.58, 0.99666, 42, -4.34, 6.56, 0.00334, 1, 41, -0.31, -3.15, 1, 2, 41, 8, -3.23, 0.35554, 42, -0.21, -3.42, 0.64446, 2, 42, 10.1, -4.47, 0.98347, 43, -3.35, -5.13, 0.01653, 1, 43, 6.08, -2.99, 1, 2, 43, 15.37, -3.54, 0.00496, 44, 3.72, -3.24, 0.99504, 2, 44, 13.09, -2.22, 0.00215, 45, 2.1, -3.66, 0.99785, 1, 45, 12.02, -2.32, 1, 1, 45, 19.97, 1.75, 1, 1, 45, 9.5, 2.42, 1, 2, 44, 8.21, 3.34, 0.64833, 45, 0.45, 3.55, 0.35167, 2, 43, 9.47, 4.24, 0.90063, 44, -2.82, 4.02, 0.09937, 2, 42, 12.51, 4, 0.86491, 43, -2.46, 3.63, 0.13509, 2, 41, 9.32, 4.63, 0.11726, 42, 3.86, 3.43, 0.88274], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 61, "height": 14}}, "kho bau tu linh7": {"kho bau tu linh7": {"type": "mesh", "uvs": [0.05416, 0, 0.28307, 0.05236, 0.46853, 0.12853, 0.6288, 0.42562, 0.80471, 0.62889, 1, 0.61325, 0.87116, 1, 0.73044, 1, 0.53889, 1, 0.3708, 0.80089, 0.17926, 0.87907, 0.0268, 1], "triangles": [7, 4, 6, 6, 4, 5, 7, 8, 4, 8, 9, 4, 10, 11, 0, 10, 1, 9, 10, 0, 1, 9, 3, 4, 9, 2, 3, 9, 1, 2], "vertices": [2, 37, -0.45, 2.69, 0.99994, 38, -4.66, 3.9, 6e-05, 2, 37, 9.38, 4.94, 0.00092, 38, 5.42, 3.77, 0.99908, 2, 38, 13.61, 3.29, 0.02523, 39, 2.21, 3.91, 0.97477, 3, 38, 20.8, 0.34, 0, 39, 9.89, 2.71, 0.0813, 40, 2.37, 2.39, 0.9187, 1, 40, 10.33, 1.2, 1, 2, 38, 37.21, -1, 0, 40, 18.83, 2.5, 1, 1, 40, 13.77, -2.46, 1, 1, 40, 7.63, -3.28, 1, 2, 39, 7.82, -4.45, 0.59124, 40, -0.72, -4.4, 0.40876, 2, 38, 9.64, -4.29, 0.48391, 39, 0.11, -4.38, 0.51609, 2, 37, 7.53, -5.06, 0.29013, 38, 1.26, -5.52, 0.70987, 2, 37, 1.46, -8.21, 0.91382, 38, -5.38, -7.14, 0.08618], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22], "width": 44, "height": 11}}, "kho bau tu linh8": {"kho bau tu linh8": {"type": "mesh", "uvs": [0.78607, 0.3831, 0.50291, 0.26293, 0.26357, 0.05393, 0, 0.29428, 0.06806, 0.63652, 0.40515, 0.93173, 0.67146, 1, 1, 1, 1, 0.86642, 0.79618, 0.60778], "triangles": [1, 4, 3, 1, 3, 2, 5, 4, 1, 9, 5, 1, 9, 1, 0, 6, 5, 9, 8, 6, 9, 6, 8, 7], "vertices": [1, 1, 8.45, -36.08, 1, 1, 1, -1.03, -53.71, 1, 1, 1, -17.63, -68.68, 1, 1, 1, 1.72, -84.87, 1, 1, 1, 29.07, -80.44, 1, 1, 1, 52.52, -59.35, 1, 1, 1, 57.85, -42.8, 1, 1, 1, 57.69, -22.43, 1, 1, 1, 47.01, -22.52, 1, 1, 1, 26.41, -35.31, 1], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 62, "height": 80}}, "kho bau tu linh9": {"kho bau tu linh9": {"type": "mesh", "uvs": [0.84908, 1, 0.82427, 0.89999, 0.76639, 0.81032, 0.68866, 0.75998, 0.5795, 0.7238, 0.49681, 0.68132, 0.43066, 0.60738, 0.35293, 0.52086, 0.24212, 0.45793, 0.1247, 0.41231, 0.04862, 0.35253, 0.00727, 0.27073, 0, 0.1433, 0.06516, 0.21252, 0.17597, 0.28331, 0.28843, 0.34152, 0.23551, 0.2487, 0.23881, 0.10712, 0.32316, 0, 0.82097, 0.14645, 1, 0.46895, 1, 0.84493, 0.91689, 1], "triangles": [11, 12, 13, 18, 16, 17, 15, 18, 19, 15, 16, 18, 10, 11, 13, 10, 13, 14, 9, 10, 14, 9, 14, 15, 8, 9, 15, 7, 15, 19, 6, 7, 19, 8, 15, 7, 19, 4, 6, 4, 5, 6, 19, 20, 4, 3, 4, 20, 2, 3, 20, 2, 20, 21, 1, 2, 21, 22, 0, 1, 21, 22, 1], "vertices": [1, 73, -1.49, 11.76, 1, 1, 73, 5.68, 7.33, 1, 1, 73, 14.07, 5.32, 1, 1, 73, 21.28, 6.69, 1, 1, 73, 29.39, 10.61, 1, 1, 73, 36.42, 12.71, 1, 1, 73, 44.35, 12.07, 1, 1, 73, 53.66, 11.34, 1, 1, 73, 63.42, 13.8, 1, 1, 73, 72.54, 17.64, 1, 1, 73, 80.2, 18.36, 1, 1, 73, 87.23, 15.9, 1, 1, 73, 95.02, 8.91, 1, 1, 73, 87.41, 9.33, 1, 1, 73, 77.19, 7.33, 1, 1, 73, 67.61, 4.5, 1, 1, 73, 75.91, 2.04, 1, 1, 73, 83.94, -6.35, 1, 1, 73, 85.5, -17.22, 1, 1, 73, 49.55, -36.18, 1, 1, 73, 20.98, -27.36, 1, 1, 73, -0.82, -5.56, 1, 1, 73, -5.23, 8.02, 1], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44], "width": 78, "height": 82}}, "kho bau tu linh_textani0": {"kho bau tu linh_textani0": {"x": 12.96, "y": 5.47, "rotation": -90, "width": 130, "height": 101}, "kho bau tu linh_textani1": {"x": 12.96, "y": 5.47, "rotation": -90, "width": 130, "height": 101}, "kho bau tu linh_textani2": {"x": 12.96, "y": 5.47, "rotation": -90, "width": 130, "height": 101}, "kho bau tu linh_textani3": {"x": 12.96, "y": 5.47, "rotation": -90, "width": 130, "height": 101}}, "kho bau tu linh_textani1": {"kho bau tu linh_textani0": {"x": 12.96, "y": 5.47, "rotation": -90, "width": 130, "height": 101}, "kho bau tu linh_textani1": {"x": 12.96, "y": 5.47, "rotation": -90, "width": 130, "height": 101}, "kho bau tu linh_textani2": {"x": 12.96, "y": 5.47, "rotation": -90, "width": 130, "height": 101}, "kho bau tu linh_textani3": {"x": 12.96, "y": 5.47, "rotation": -90, "width": 130, "height": 101}}, "mask": {"mask": {"type": "clipping", "end": "mask", "vertexCount": 16, "vertices": [-106.91, 189.23, -114.62, 169.95, -131.98, 158.87, -131.49, -158.77, -112.21, -168.89, -104.98, -184.8, -27.38, -199.74, 48.77, -198.3, 105.65, -185.28, 115.29, -166.48, 131.2, -155.88, 128.79, 160.79, 114.81, 170.92, 106.61, 188.27, 39.62, 203.69, -47.63, 201.76], "color": "ce3a3aff"}}}}, "animations": {"Effect_khobautulinh": {"slots": {"fire0": {"color": [{"time": 0, "color": "ffffff7b"}, {"time": 1.5, "color": "ffffff7a"}, {"time": 1.8, "color": "ffffffff", "curve": "stepped"}, {"time": 3.8, "color": "ffffffff"}, {"time": 4.5, "color": "ffffff64"}], "attachment": [{"time": 0, "name": "fire0"}, {"time": 0.1, "name": "fire1"}, {"time": 0.2, "name": "fire2"}, {"time": 0.3, "name": "fire3"}, {"time": 0.4, "name": "fire4"}, {"time": 0.5333, "name": "fire5"}, {"time": 0.6, "name": "fire6"}, {"time": 0.7, "name": "fire7"}, {"time": 0.8333, "name": "fire8"}, {"time": 0.9333, "name": "fire9"}, {"time": 1.0333, "name": "fire0"}, {"time": 1.1333, "name": "fire1"}, {"time": 1.2667, "name": "fire2"}, {"time": 1.3333, "name": "fire3"}, {"time": 1.4333, "name": "fire4"}, {"time": 1.5667, "name": "fire5"}, {"time": 1.6667, "name": "fire6"}, {"time": 1.7333, "name": "fire7"}, {"time": 1.8667, "name": "fire8"}, {"time": 1.9667, "name": "fire9"}, {"time": 2.0667, "name": "fire0"}, {"time": 2.1333, "name": "fire1"}, {"time": 2.2667, "name": "fire2"}, {"time": 2.3667, "name": "fire3"}, {"time": 2.4333, "name": "fire4"}, {"time": 2.5667, "name": "fire5"}, {"time": 2.6667, "name": "fire6"}, {"time": 2.7667, "name": "fire7"}, {"time": 2.8667, "name": "fire8"}, {"time": 2.9667, "name": "fire9"}, {"time": 3.1, "name": "fire0"}, {"time": 3.2, "name": "fire1"}, {"time": 3.3, "name": "fire2"}, {"time": 3.4, "name": "fire3"}, {"time": 3.5, "name": "fire4"}, {"time": 3.6, "name": "fire5"}, {"time": 3.7, "name": "fire6"}, {"time": 3.8, "name": "fire7"}, {"time": 3.9333, "name": "fire8"}, {"time": 4, "name": "fire9"}, {"time": 4.1, "name": "fire0"}, {"time": 4.2, "name": "fire1"}, {"time": 4.3333, "name": "fire2"}, {"time": 4.4, "name": "fire3"}, {"time": 4.5, "name": "fire4"}, {"time": 4.6333, "name": "fire5"}, {"time": 4.7, "name": "fire6"}, {"time": 4.8, "name": "fire7"}, {"time": 4.9333, "name": "fire8"}, {"time": 5.0333, "name": "fire9"}, {"time": 5.0667, "name": "fire0"}, {"time": 5.1667, "name": "fire1"}, {"time": 5.3, "name": "fire2"}, {"time": 5.4, "name": "fire3"}, {"time": 5.4667, "name": "fire4"}, {"time": 5.6, "name": "fire5"}, {"time": 5.7, "name": "fire6"}, {"time": 5.8, "name": "fire7"}, {"time": 5.9, "name": "fire8"}, {"time": 6, "name": "fire9"}]}, "fire1": {"color": [{"time": 0, "color": "ffffff7b"}, {"time": 1.5, "color": "ffffff7a"}, {"time": 1.8, "color": "ffffffff", "curve": "stepped"}, {"time": 3.8, "color": "ffffffff"}, {"time": 4.5, "color": "ffffff64"}], "attachment": [{"time": 0, "name": "fire4"}, {"time": 0.1333, "name": "fire5"}, {"time": 0.2333, "name": "fire6"}, {"time": 0.3333, "name": "fire7"}, {"time": 0.4, "name": "fire8"}, {"time": 0.5, "name": "fire9"}, {"time": 0.6, "name": "fire0"}, {"time": 0.7, "name": "fire1"}, {"time": 0.8, "name": "fire2"}, {"time": 0.9, "name": "fire3"}, {"time": 1, "name": "fire4"}, {"time": 1.1333, "name": "fire5"}, {"time": 1.2, "name": "fire6"}, {"time": 1.3, "name": "fire7"}, {"time": 1.4333, "name": "fire8"}, {"time": 1.5333, "name": "fire9"}, {"time": 1.6333, "name": "fire0"}, {"time": 1.7333, "name": "fire1"}, {"time": 1.8667, "name": "fire2"}, {"time": 1.9333, "name": "fire3"}, {"time": 2.0333, "name": "fire4"}, {"time": 2.1667, "name": "fire5"}, {"time": 2.2667, "name": "fire6"}, {"time": 2.3333, "name": "fire7"}, {"time": 2.4667, "name": "fire8"}, {"time": 2.5667, "name": "fire9"}, {"time": 2.6667, "name": "fire0"}, {"time": 2.7333, "name": "fire1"}, {"time": 2.8667, "name": "fire2"}, {"time": 2.9667, "name": "fire3"}, {"time": 3.0333, "name": "fire4"}, {"time": 3.1667, "name": "fire5"}, {"time": 3.2667, "name": "fire6"}, {"time": 3.3667, "name": "fire7"}, {"time": 3.4667, "name": "fire8"}, {"time": 3.5667, "name": "fire9"}, {"time": 3.7, "name": "fire0"}, {"time": 3.8, "name": "fire1"}, {"time": 3.9, "name": "fire2"}, {"time": 4, "name": "fire3"}, {"time": 4.1, "name": "fire4"}, {"time": 4.2, "name": "fire5"}, {"time": 4.3, "name": "fire6"}, {"time": 4.4, "name": "fire7"}, {"time": 4.5333, "name": "fire8"}, {"time": 4.6, "name": "fire9"}, {"time": 4.7, "name": "fire0"}, {"time": 4.8, "name": "fire1"}, {"time": 4.9333, "name": "fire2"}, {"time": 5, "name": "fire3"}, {"time": 5.1, "name": "fire4"}, {"time": 5.2333, "name": "fire5"}, {"time": 5.3, "name": "fire6"}, {"time": 5.4, "name": "fire7"}, {"time": 5.5333, "name": "fire8"}, {"time": 5.6333, "name": "fire9"}, {"time": 5.6667, "name": "fire0"}, {"time": 5.7667, "name": "fire1"}, {"time": 5.9, "name": "fire2"}, {"time": 6, "name": "fire3"}]}, "goldcoin1": {"color": [{"time": 1.6667, "color": "ffffff00"}, {"time": 1.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.9333, "color": "ffffffff"}, {"time": 3.1333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 1.7333, "name": "goldcoin1"}, {"time": 1.8, "name": "goldcoin2"}, {"time": 1.8667, "name": "goldcoin3"}, {"time": 1.9333, "name": "goldcoin4"}, {"time": 2, "name": "goldcoin5"}, {"time": 2.0667, "name": "goldcoin6"}, {"time": 2.1333, "name": "goldcoin7"}, {"time": 2.2, "name": "goldcoin1"}, {"time": 2.2667, "name": "goldcoin2"}, {"time": 2.3333, "name": "goldcoin3"}, {"time": 2.4, "name": "goldcoin4"}, {"time": 2.4667, "name": "goldcoin5"}, {"time": 2.5333, "name": "goldcoin6"}, {"time": 2.6, "name": "goldcoin7"}, {"time": 2.6667, "name": "goldcoin1"}, {"time": 2.7333, "name": "goldcoin2"}, {"time": 2.8, "name": "goldcoin3"}, {"time": 2.8667, "name": "goldcoin4"}, {"time": 2.9333, "name": "goldcoin5"}, {"time": 3, "name": "goldcoin6"}, {"time": 3.0667, "name": "goldcoin7"}, {"time": 3.1333, "name": null}]}, "goldcoin2": {"color": [{"time": 2, "color": "ffffff00"}, {"time": 2.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.2667, "color": "ffffffff"}, {"time": 3.4667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 2.0667, "name": "goldcoin1"}, {"time": 2.1333, "name": "goldcoin2"}, {"time": 2.2, "name": "goldcoin3"}, {"time": 2.2667, "name": "goldcoin4"}, {"time": 2.3333, "name": "goldcoin5"}, {"time": 2.4, "name": "goldcoin6"}, {"time": 2.4667, "name": "goldcoin7"}, {"time": 2.5333, "name": "goldcoin1"}, {"time": 2.6, "name": "goldcoin2"}, {"time": 2.6667, "name": "goldcoin3"}, {"time": 2.7333, "name": "goldcoin4"}, {"time": 2.8, "name": "goldcoin5"}, {"time": 2.8667, "name": "goldcoin6"}, {"time": 2.9333, "name": "goldcoin7"}, {"time": 3, "name": "goldcoin1"}, {"time": 3.0667, "name": "goldcoin2"}, {"time": 3.1333, "name": "goldcoin3"}, {"time": 3.2, "name": "goldcoin4"}, {"time": 3.2667, "name": "goldcoin5"}, {"time": 3.3333, "name": "goldcoin6"}, {"time": 3.4, "name": "goldcoin7"}, {"time": 3.4667, "name": null}]}, "goldcoin3": {"color": [{"time": 2.2, "color": "ffffff00"}, {"time": 2.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.4667, "color": "ffffffff"}, {"time": 3.6667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 2.2667, "name": "goldcoin1"}, {"time": 2.3333, "name": "goldcoin2"}, {"time": 2.4, "name": "goldcoin3"}, {"time": 2.4667, "name": "goldcoin4"}, {"time": 2.5333, "name": "goldcoin5"}, {"time": 2.6, "name": "goldcoin6"}, {"time": 2.6667, "name": "goldcoin7"}, {"time": 2.7333, "name": "goldcoin1"}, {"time": 2.8, "name": "goldcoin2"}, {"time": 2.8667, "name": "goldcoin3"}, {"time": 2.9333, "name": "goldcoin4"}, {"time": 3, "name": "goldcoin5"}, {"time": 3.0667, "name": "goldcoin6"}, {"time": 3.1333, "name": "goldcoin7"}, {"time": 3.2, "name": "goldcoin1"}, {"time": 3.2667, "name": "goldcoin2"}, {"time": 3.3333, "name": "goldcoin3"}, {"time": 3.4, "name": "goldcoin4"}, {"time": 3.4667, "name": "goldcoin5"}, {"time": 3.5333, "name": "goldcoin6"}, {"time": 3.6, "name": "goldcoin7"}, {"time": 3.6667, "name": null}]}, "goldcoin4": {"color": [{"time": 1.8, "color": "ffffff00"}, {"time": 1.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.0667, "color": "ffffffff"}, {"time": 3.2667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 1.8667, "name": "goldcoin1"}, {"time": 1.9333, "name": "goldcoin2"}, {"time": 2, "name": "goldcoin3"}, {"time": 2.0667, "name": "goldcoin4"}, {"time": 2.1333, "name": "goldcoin5"}, {"time": 2.2, "name": "goldcoin6"}, {"time": 2.2667, "name": "goldcoin7"}, {"time": 2.3333, "name": "goldcoin1"}, {"time": 2.4, "name": "goldcoin2"}, {"time": 2.4667, "name": "goldcoin3"}, {"time": 2.5333, "name": "goldcoin4"}, {"time": 2.6, "name": "goldcoin5"}, {"time": 2.6667, "name": "goldcoin6"}, {"time": 2.7333, "name": "goldcoin7"}, {"time": 2.8, "name": "goldcoin1"}, {"time": 2.8667, "name": "goldcoin2"}, {"time": 2.9333, "name": "goldcoin3"}, {"time": 3, "name": "goldcoin4"}, {"time": 3.0667, "name": "goldcoin5"}, {"time": 3.1333, "name": "goldcoin6"}, {"time": 3.2, "name": "goldcoin7"}, {"time": 3.2667, "name": null}]}, "kho bau tu linh24": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6667, "color": "ffffff00"}, {"time": 1.9667, "color": "ffffff88"}, {"time": 3.7, "color": "ffffffd6"}, {"time": 4.6333, "color": "ffffff00"}]}, "kho bau tu linh_textani0": {"attachment": [{"time": 1.8333, "name": "kho bau tu linh_textani0"}, {"time": 1.9333, "name": "kho bau tu linh_textani1"}, {"time": 2.0333, "name": "kho bau tu linh_textani2"}, {"time": 2.1333, "name": "kho bau tu linh_textani3"}, {"time": 2.2333, "name": "kho bau tu linh_textani0"}, {"time": 2.3333, "name": "kho bau tu linh_textani1"}, {"time": 2.4333, "name": "kho bau tu linh_textani2"}, {"time": 2.5333, "name": "kho bau tu linh_textani3"}, {"time": 2.6333, "name": "kho bau tu linh_textani0"}, {"time": 2.7333, "name": "kho bau tu linh_textani1"}, {"time": 2.8333, "name": "kho bau tu linh_textani2"}, {"time": 2.9333, "name": "kho bau tu linh_textani3"}, {"time": 3.0333, "name": "kho bau tu linh_textani0"}, {"time": 3.1333, "name": "kho bau tu linh_textani1"}, {"time": 3.2333, "name": "kho bau tu linh_textani2"}, {"time": 3.3333, "name": "kho bau tu linh_textani3"}, {"time": 3.4333, "name": "kho bau tu linh_textani0"}, {"time": 3.5333, "name": "kho bau tu linh_textani1"}, {"time": 3.6333, "name": "kho bau tu linh_textani2"}, {"time": 3.7333, "name": "kho bau tu linh_textani3"}, {"time": 3.8, "name": null}]}, "kho bau tu linh_textani1": {"attachment": [{"time": 1.8333, "name": "kho bau tu linh_textani0"}, {"time": 1.9333, "name": "kho bau tu linh_textani1"}, {"time": 2.0333, "name": "kho bau tu linh_textani2"}, {"time": 2.1333, "name": "kho bau tu linh_textani3"}, {"time": 2.2333, "name": "kho bau tu linh_textani0"}, {"time": 2.3333, "name": "kho bau tu linh_textani1"}, {"time": 2.4333, "name": "kho bau tu linh_textani2"}, {"time": 2.5333, "name": "kho bau tu linh_textani3"}, {"time": 2.6333, "name": "kho bau tu linh_textani0"}, {"time": 2.7333, "name": "kho bau tu linh_textani1"}, {"time": 2.8333, "name": "kho bau tu linh_textani2"}, {"time": 2.9333, "name": "kho bau tu linh_textani3"}, {"time": 3.0333, "name": "kho bau tu linh_textani0"}, {"time": 3.1333, "name": "kho bau tu linh_textani1"}, {"time": 3.2333, "name": "kho bau tu linh_textani2"}, {"time": 3.3333, "name": "kho bau tu linh_textani3"}, {"time": 3.4333, "name": "kho bau tu linh_textani0"}, {"time": 3.5333, "name": "kho bau tu linh_textani1"}, {"time": 3.6333, "name": "kho bau tu linh_textani2"}, {"time": 3.7333, "name": "kho bau tu linh_textani3"}, {"time": 3.8, "name": null}]}}, "bones": {"bone41": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": -4.11}, {"time": 1.0333, "angle": 1.45}, {"time": 1.5, "angle": -0.21}, {"time": 2, "angle": 0}, {"time": 2.5, "angle": -4.11}, {"time": 3.0333, "angle": 1.45}, {"time": 3.5, "angle": -0.21}, {"time": 4, "angle": 0}, {"time": 4.5, "angle": -4.11}, {"time": 5.0333, "angle": 1.45}, {"time": 5.5, "angle": -0.21}, {"time": 6, "angle": 0}]}, "bone45": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": -6.73}, {"time": 1.0333, "angle": -22.59}, {"time": 1.5, "angle": -51.23}, {"time": 2, "angle": 0}, {"time": 2.5, "angle": -6.73}, {"time": 3.0333, "angle": -22.59}, {"time": 3.5, "angle": -51.23}, {"time": 4, "angle": 0}, {"time": 4.5, "angle": -6.73}, {"time": 5.0333, "angle": -22.59}, {"time": 5.5, "angle": -51.23}, {"time": 6, "angle": 0}]}, "bone44": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 23.56}, {"time": 1.0333, "angle": -11.47}, {"time": 1.5, "angle": -2.42}, {"time": 2, "angle": 0}, {"time": 2.5, "angle": 23.56}, {"time": 3.0333, "angle": -11.47}, {"time": 3.5, "angle": -2.42}, {"time": 4, "angle": 0}, {"time": 4.5, "angle": 23.56}, {"time": 5.0333, "angle": -11.47}, {"time": 5.5, "angle": -2.42}, {"time": 6, "angle": 0}]}, "bone43": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 15.07}, {"time": 1.0333, "angle": 6.77}, {"time": 1.5, "angle": 6.9}, {"time": 2, "angle": 0}, {"time": 2.5, "angle": 15.07}, {"time": 3.0333, "angle": 6.77}, {"time": 3.5, "angle": 6.9}, {"time": 4, "angle": 0}, {"time": 4.5, "angle": 15.07}, {"time": 5.0333, "angle": 6.77}, {"time": 5.5, "angle": 6.9}, {"time": 6, "angle": 0}]}, "bone42": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": -18.4}, {"time": 1.0333, "angle": -8.35}, {"time": 1.5, "angle": -0.4}, {"time": 2, "angle": 0}, {"time": 2.5, "angle": -18.4}, {"time": 3.0333, "angle": -8.35}, {"time": 3.5, "angle": -0.4}, {"time": 4, "angle": 0}, {"time": 4.5, "angle": -18.4}, {"time": 5.0333, "angle": -8.35}, {"time": 5.5, "angle": -0.4}, {"time": 6, "angle": 0}]}, "bone37": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 1.6}, {"time": 0.9667, "angle": -11.31}, {"time": 1.4, "angle": -10.21}, {"time": 1.9, "angle": 19.17}, {"time": 2.4667, "angle": 14.08}, {"time": 3, "angle": 0}, {"time": 3.4667, "angle": 1.6}, {"time": 3.9333, "angle": -11.31}, {"time": 4.4, "angle": -10.21}, {"time": 4.9, "angle": 19.17}, {"time": 5.4667, "angle": 14.08}, {"time": 6, "angle": 0}]}, "bone40": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 9.96}, {"time": 0.9667, "angle": 3.12}, {"time": 1.4, "angle": -27.11}, {"time": 1.9, "angle": -19.72}, {"time": 2.4667, "angle": 9.44}, {"time": 3, "angle": 0}, {"time": 3.4667, "angle": 9.96}, {"time": 3.9333, "angle": 3.12}, {"time": 4.4, "angle": -27.11}, {"time": 4.9, "angle": -19.72}, {"time": 5.4667, "angle": 9.44}, {"time": 6, "angle": 0}]}, "bone39": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 23.37}, {"time": 0.9667, "angle": 27.81}, {"time": 1.4, "angle": 21.78}, {"time": 1.9, "angle": -5.54}, {"time": 2.4667, "angle": 14.24}, {"time": 3, "angle": 0}, {"time": 3.4667, "angle": 23.37}, {"time": 3.9333, "angle": 27.81}, {"time": 4.4, "angle": 21.78}, {"time": 4.9, "angle": -5.54}, {"time": 5.4667, "angle": 14.24}, {"time": 6, "angle": 0}]}, "bone38": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": -3.07}, {"time": 0.9667, "angle": -14.61}, {"time": 1.4, "angle": -13.35}, {"time": 1.9, "angle": -19.98}, {"time": 2.4667, "angle": -20.25}, {"time": 3, "angle": 0}, {"time": 3.4667, "angle": -3.07}, {"time": 3.9333, "angle": -14.61}, {"time": 4.4, "angle": -13.35}, {"time": 4.9, "angle": -19.98}, {"time": 5.4667, "angle": -20.25}, {"time": 6, "angle": 0}]}, "bone7": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": -7.84}, {"time": 0.7, "angle": -6.58}, {"time": 1, "angle": -7}, {"time": 1.3667, "angle": -7.42}, {"time": 1.7333, "angle": -7.16}, {"time": 2.3667, "angle": -10.94}, {"time": 2.7, "angle": -10.67}, {"time": 3, "angle": 0}, {"time": 3.3667, "angle": -7.84}, {"time": 3.7, "angle": -6.58}, {"time": 4, "angle": -7}, {"time": 4.3667, "angle": -7.42}, {"time": 4.7333, "angle": -7.16}, {"time": 5.3667, "angle": -10.94}, {"time": 5.7, "angle": -10.67}, {"time": 6, "angle": 0}]}, "bone20": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 1.63}, {"time": 0.7, "angle": 13.83}, {"time": 1, "angle": 26.31}, {"time": 1.3667, "angle": -16.22}, {"time": 1.7333, "angle": 0.7}, {"time": 2.3667, "angle": 13.86}, {"time": 2.7, "angle": -13.13}, {"time": 3, "angle": 0}, {"time": 3.3667, "angle": 1.63}, {"time": 3.7, "angle": 13.83}, {"time": 4, "angle": 26.31}, {"time": 4.3667, "angle": -16.22}, {"time": 4.7333, "angle": 0.7}, {"time": 5.3667, "angle": 13.86}, {"time": 5.7, "angle": -13.13}, {"time": 6, "angle": 0}]}, "bone19": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": -9.97}, {"time": 0.7, "angle": 9.12}, {"time": 1, "angle": 11.51}, {"time": 1.3667, "angle": -12.46}, {"time": 1.7333, "angle": -18.39}, {"time": 2.3667, "angle": 16.12}, {"time": 2.7, "angle": 4.65}, {"time": 3, "angle": 0}, {"time": 3.3667, "angle": -9.97}, {"time": 3.7, "angle": 9.12}, {"time": 4, "angle": 11.51}, {"time": 4.3667, "angle": -12.46}, {"time": 4.7333, "angle": -18.39}, {"time": 5.3667, "angle": 16.12}, {"time": 5.7, "angle": 4.65}, {"time": 6, "angle": 0}]}, "bone18": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 13.68}, {"time": 0.7, "angle": 9.48}, {"time": 1, "angle": 8.65}, {"time": 1.3667, "angle": -21.82}, {"time": 1.7333, "angle": -18.35}, {"time": 2.3667, "angle": 10.2}, {"time": 2.7, "angle": 11.21}, {"time": 3, "angle": 0}, {"time": 3.3667, "angle": 13.68}, {"time": 3.7, "angle": 9.48}, {"time": 4, "angle": 8.65}, {"time": 4.3667, "angle": -21.82}, {"time": 4.7333, "angle": -18.35}, {"time": 5.3667, "angle": 10.2}, {"time": 5.7, "angle": 11.21}, {"time": 6, "angle": 0}]}, "bone17": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 21.5}, {"time": 0.7, "angle": 9.79}, {"time": 1, "angle": 1.48}, {"time": 1.3667, "angle": -9.3}, {"time": 1.7333, "angle": -11.51}, {"time": 2.3667, "angle": 7.05}, {"time": 2.7, "angle": 10.23}, {"time": 3, "angle": 0}, {"time": 3.3667, "angle": 21.5}, {"time": 3.7, "angle": 9.79}, {"time": 4, "angle": 1.48}, {"time": 4.3667, "angle": -9.3}, {"time": 4.7333, "angle": -11.51}, {"time": 5.3667, "angle": 7.05}, {"time": 5.7, "angle": 10.23}, {"time": 6, "angle": 0}]}, "bone16": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 11.74}, {"time": 0.7, "angle": 7.87}, {"time": 1, "angle": 3.81}, {"time": 1.3667, "angle": 3.95}, {"time": 1.7333, "angle": -2.4}, {"time": 2.3667, "angle": 2.98}, {"time": 2.7, "angle": -0.87}, {"time": 3, "angle": 0}, {"time": 3.3667, "angle": 11.74}, {"time": 3.7, "angle": 7.87}, {"time": 4, "angle": 3.81}, {"time": 4.3667, "angle": 3.95}, {"time": 4.7333, "angle": -2.4}, {"time": 5.3667, "angle": 2.98}, {"time": 5.7, "angle": -0.87}, {"time": 6, "angle": 0}]}, "bone15": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 0.15}, {"time": 0.7, "angle": 9.81}, {"time": 1, "angle": -6.17}, {"time": 1.3667, "angle": 14.16}, {"time": 1.7333, "angle": 4.5}, {"time": 2.3667, "angle": 1.19}, {"time": 2.7, "angle": -6.78}, {"time": 3, "angle": 0}, {"time": 3.3667, "angle": 0.15}, {"time": 3.7, "angle": 9.81}, {"time": 4, "angle": -6.17}, {"time": 4.3667, "angle": 14.16}, {"time": 4.7333, "angle": 4.5}, {"time": 5.3667, "angle": 1.19}, {"time": 5.7, "angle": -6.78}, {"time": 6, "angle": 0}]}, "bone14": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": -7.38}, {"time": 0.7, "angle": 3.35}, {"time": 1, "angle": -6.73}, {"time": 1.3667, "angle": 15.17}, {"time": 1.7333, "angle": 8.44}, {"time": 2.3667, "angle": 3.54}, {"time": 2.7, "angle": -13.89}, {"time": 3, "angle": 0}, {"time": 3.3667, "angle": -7.38}, {"time": 3.7, "angle": 3.35}, {"time": 4, "angle": -6.73}, {"time": 4.3667, "angle": 15.17}, {"time": 4.7333, "angle": 8.44}, {"time": 5.3667, "angle": 3.54}, {"time": 5.7, "angle": -13.89}, {"time": 6, "angle": 0}]}, "bone13": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": -5.38}, {"time": 0.7, "angle": 4.53}, {"time": 1, "angle": -15.44}, {"time": 1.3667, "angle": -3.06}, {"time": 1.7333, "angle": 10.14}, {"time": 2.3667, "angle": 6.62}, {"time": 2.7, "angle": -9.73}, {"time": 3, "angle": 0}, {"time": 3.3667, "angle": -5.38}, {"time": 3.7, "angle": 4.53}, {"time": 4, "angle": -15.44}, {"time": 4.3667, "angle": -3.06}, {"time": 4.7333, "angle": 10.14}, {"time": 5.3667, "angle": 6.62}, {"time": 5.7, "angle": -9.73}, {"time": 6, "angle": 0}]}, "bone12": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": -3.65}, {"time": 0.7, "angle": -18.53}, {"time": 1, "angle": -27.89}, {"time": 1.3667, "angle": -11.8}, {"time": 1.7333, "angle": -10.5}, {"time": 2.3667, "angle": -10.9}, {"time": 2.7, "angle": -7.26}, {"time": 3, "angle": 0}, {"time": 3.3667, "angle": -3.65}, {"time": 3.7, "angle": -18.53}, {"time": 4, "angle": -27.89}, {"time": 4.3667, "angle": -11.8}, {"time": 4.7333, "angle": -10.5}, {"time": 5.3667, "angle": -10.9}, {"time": 5.7, "angle": -7.26}, {"time": 6, "angle": 0}]}, "bone11": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 1.33}, {"time": 0.7, "angle": -13.69}, {"time": 1, "angle": -34.02}, {"time": 1.3667, "angle": -19.88}, {"time": 1.7333, "angle": -18.47}, {"time": 2.3667, "angle": -29.32}, {"time": 2.7, "angle": 4.24}, {"time": 3, "angle": 0}, {"time": 3.3667, "angle": 1.33}, {"time": 3.7, "angle": -13.69}, {"time": 4, "angle": -34.02}, {"time": 4.3667, "angle": -19.88}, {"time": 4.7333, "angle": -18.47}, {"time": 5.3667, "angle": -29.32}, {"time": 5.7, "angle": 4.24}, {"time": 6, "angle": 0}]}, "bone10": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 7.86}, {"time": 0.7, "angle": 6.34}, {"time": 1, "angle": 14.95}, {"time": 1.3667, "angle": 10.59}, {"time": 1.7333, "angle": 13.34}, {"time": 2.3667, "angle": 20.37}, {"time": 2.7, "angle": 19.08}, {"time": 3, "angle": 0}, {"time": 3.3667, "angle": 7.86}, {"time": 3.7, "angle": 6.34}, {"time": 4, "angle": 14.95}, {"time": 4.3667, "angle": 10.59}, {"time": 4.7333, "angle": 13.34}, {"time": 5.3667, "angle": 20.37}, {"time": 5.7, "angle": 19.08}, {"time": 6, "angle": 0}]}, "bone9": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 0.5}, {"time": 0.7, "angle": -0.71}, {"time": 1, "angle": 22.9}, {"time": 1.3667, "angle": 2.07}, {"time": 1.7333, "angle": 4.48}, {"time": 2.3667, "angle": 9.05}, {"time": 2.7, "angle": 8.77}, {"time": 3, "angle": 0}, {"time": 3.3667, "angle": 0.5}, {"time": 3.7, "angle": -0.71}, {"time": 4, "angle": 22.9}, {"time": 4.3667, "angle": 2.07}, {"time": 4.7333, "angle": 4.48}, {"time": 5.3667, "angle": 9.05}, {"time": 5.7, "angle": 8.77}, {"time": 6, "angle": 0}]}, "bone8": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": -4.58}, {"time": 0.7, "angle": -5.15}, {"time": 1, "angle": 8.87}, {"time": 1.3667, "angle": -4.04}, {"time": 1.7333, "angle": -3.02}, {"time": 2.3667, "angle": -2.91}, {"time": 2.7, "angle": -2.26}, {"time": 3, "angle": 0}, {"time": 3.3667, "angle": -4.58}, {"time": 3.7, "angle": -5.15}, {"time": 4, "angle": 8.87}, {"time": 4.3667, "angle": -4.04}, {"time": 4.7333, "angle": -3.02}, {"time": 5.3667, "angle": -2.91}, {"time": 5.7, "angle": -2.26}, {"time": 6, "angle": 0}]}, "bone21": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3, "angle": 2.44}, {"time": 0.6, "angle": 4.48}, {"time": 0.9333, "angle": 9.76}, {"time": 1.2667, "angle": 9.23}, {"time": 1.6, "angle": 8.21}, {"time": 1.9333, "angle": 9.71}, {"time": 2.3, "angle": 11.6}, {"time": 2.6, "angle": 7.6}, {"time": 3, "angle": 0}, {"time": 3.3, "angle": 2.44}, {"time": 3.6, "angle": 4.48}, {"time": 3.9333, "angle": 9.76}, {"time": 4.2667, "angle": 9.23}, {"time": 4.6, "angle": 8.21}, {"time": 4.9333, "angle": 9.71}, {"time": 5.3, "angle": 11.6}, {"time": 5.6, "angle": 7.6}, {"time": 6, "angle": 0}]}, "bone35": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3, "angle": -5.78}, {"time": 0.6, "angle": -4.74}, {"time": 0.9333, "angle": -11.63}, {"time": 1.2667, "angle": 8.66}, {"time": 1.6, "angle": -5.23}, {"time": 1.9333, "angle": -5.13}, {"time": 2.3, "angle": -25.29}, {"time": 2.6, "angle": 9.64}, {"time": 2.7667, "angle": 20.41}, {"time": 3, "angle": 0}, {"time": 3.3, "angle": -5.78}, {"time": 3.6, "angle": -4.74}, {"time": 3.9333, "angle": -11.63}, {"time": 4.2667, "angle": 8.66}, {"time": 4.6, "angle": -5.23}, {"time": 4.9333, "angle": -5.13}, {"time": 5.3, "angle": -25.29}, {"time": 5.6, "angle": 3.66}, {"time": 6, "angle": 0}]}, "bone34": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3, "angle": -11.51}, {"time": 0.6, "angle": -9.72}, {"time": 0.9333, "angle": -18.02}, {"time": 1.2667, "angle": -0.26}, {"time": 1.6, "angle": 3.97}, {"time": 1.9333, "angle": -23.91}, {"time": 2.3, "angle": -6.64}, {"time": 2.6, "angle": -0.94}, {"time": 2.7667, "angle": 12.7}, {"time": 3, "angle": 0}, {"time": 3.3, "angle": -11.51}, {"time": 3.6, "angle": -9.72}, {"time": 3.9333, "angle": -2.67}, {"time": 4.2667, "angle": -15.09}, {"time": 4.6, "angle": 3.97}, {"time": 4.9333, "angle": -10.69}, {"time": 5.3, "angle": -6.64}, {"time": 5.6, "angle": -14.56}, {"time": 6, "angle": 0}]}, "bone33": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3, "angle": 1.42}, {"time": 0.6, "angle": -4.39}, {"time": 0.9333, "angle": -13.74}, {"time": 1.2667, "angle": -6.19}, {"time": 1.6, "angle": 7.66}, {"time": 1.9333, "angle": -10.76}, {"time": 2.3, "angle": -4.83}, {"time": 2.6, "angle": 6}, {"time": 2.7667, "angle": 9.03}, {"time": 3, "angle": 0}, {"time": 3.3, "angle": 1.42}, {"time": 3.6, "angle": -4.39}, {"time": 3.9333, "angle": -0.89}, {"time": 4.2667, "angle": -6.19}, {"time": 4.6, "angle": 7.66}, {"time": 4.9333, "angle": -0.77}, {"time": 5.3, "angle": -4.83}, {"time": 5.6, "angle": -8.54}, {"time": 6, "angle": 0}]}, "bone32": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3, "angle": 3.44}, {"time": 0.6, "angle": -3.19}, {"time": 0.9333, "angle": -13.27}, {"time": 1.2667, "angle": -7.14}, {"time": 1.6, "angle": -7.64}, {"time": 1.9333, "angle": -10.25}, {"time": 2.3, "angle": -11.84}, {"time": 2.6, "angle": 8.09}, {"time": 2.7667, "angle": -2.22}, {"time": 3, "angle": 0}, {"time": 3.3, "angle": 3.44}, {"time": 3.6, "angle": -3.19}, {"time": 3.9333, "angle": -13.27}, {"time": 4.2667, "angle": -7.14}, {"time": 4.6, "angle": -7.64}, {"time": 4.9333, "angle": -10.25}, {"time": 5.3, "angle": -11.84}, {"time": 5.6, "angle": 8.09}, {"time": 5.7667, "angle": -2.22}, {"time": 6, "angle": 0}]}, "bone31": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3, "angle": 8.53}, {"time": 0.6, "angle": 8.7}, {"time": 0.9333, "angle": -4.56}, {"time": 1.2667, "angle": -0.05}, {"time": 1.6, "angle": -6.93}, {"time": 1.9333, "angle": 6.42}, {"time": 2.3, "angle": -1.67}, {"time": 2.6, "angle": 7.68}, {"time": 3, "angle": 0}, {"time": 3.3, "angle": 8.53}, {"time": 3.6, "angle": 8.7}, {"time": 3.9333, "angle": -4.56}, {"time": 4.2667, "angle": -7.86}, {"time": 4.6, "angle": -6.93}, {"time": 4.9333, "angle": 6.42}, {"time": 5.3, "angle": -1.67}, {"time": 5.6, "angle": -4.41}, {"time": 6, "angle": 0}]}, "bone30": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3, "angle": 6.01}, {"time": 0.6, "angle": 10.28}, {"time": 0.9333, "angle": 6.68}, {"time": 1.2667, "angle": -16.44}, {"time": 1.6, "angle": 0.64}, {"time": 1.9333, "angle": 6.49}, {"time": 2.3, "angle": 5.58}, {"time": 2.6, "angle": 1.34}, {"time": 3, "angle": 0}, {"time": 3.3, "angle": 6.01}, {"time": 3.6, "angle": 10.28}, {"time": 3.9333, "angle": 6.68}, {"time": 4.2667, "angle": 3.11}, {"time": 4.6, "angle": 0.64}, {"time": 4.9333, "angle": 6.49}, {"time": 5.3, "angle": 5.58}, {"time": 5.6, "angle": 1.34}, {"time": 6, "angle": 0}]}, "bone29": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3, "angle": 4.84}, {"time": 0.6, "angle": 10.79}, {"time": 0.9333, "angle": 15.39}, {"time": 1.2667, "angle": 11.54}, {"time": 1.6, "angle": 8.02}, {"time": 1.9333, "angle": 11.47}, {"time": 2.3, "angle": 13.21}, {"time": 2.6, "angle": 6.47}, {"time": 3, "angle": 0}, {"time": 3.3, "angle": 4.84}, {"time": 3.6, "angle": 10.79}, {"time": 3.9333, "angle": 15.39}, {"time": 4.2667, "angle": 11.54}, {"time": 4.6, "angle": 8.02}, {"time": 4.9333, "angle": 11.47}, {"time": 5.3, "angle": 13.21}, {"time": 5.6, "angle": 6.47}, {"time": 6, "angle": 0}]}, "bone28": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3, "angle": 1.17}, {"time": 0.6, "angle": 4.93}, {"time": 0.9333, "angle": 11.91}, {"time": 1.2667, "angle": 9.4}, {"time": 1.6, "angle": 7.01}, {"time": 1.9333, "angle": 8.91}, {"time": 2.3, "angle": 11.44}, {"time": 2.6, "angle": 5.74}, {"time": 3, "angle": 0}, {"time": 3.3, "angle": 1.17}, {"time": 3.6, "angle": 4.93}, {"time": 3.9333, "angle": 11.91}, {"time": 4.2667, "angle": 9.4}, {"time": 4.6, "angle": 7.01}, {"time": 4.9333, "angle": 8.91}, {"time": 5.3, "angle": 11.44}, {"time": 5.6, "angle": 5.74}, {"time": 6, "angle": 0}]}, "bone27": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3, "angle": 0.32}, {"time": 0.6, "angle": 3.39}, {"time": 0.9333, "angle": 12.32}, {"time": 1.2667, "angle": 10.47}, {"time": 1.6, "angle": 8.48}, {"time": 1.9333, "angle": 11.02}, {"time": 2.3, "angle": 15.3}, {"time": 2.6, "angle": 8.42}, {"time": 3, "angle": 0}, {"time": 3.3, "angle": 0.32}, {"time": 3.6, "angle": 3.39}, {"time": 3.9333, "angle": 12.32}, {"time": 4.2667, "angle": 10.47}, {"time": 4.6, "angle": 8.48}, {"time": 4.9333, "angle": 11.02}, {"time": 5.3, "angle": 15.3}, {"time": 5.6, "angle": 8.42}, {"time": 6, "angle": 0}]}, "bone26": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3, "angle": -2.23}, {"time": 0.6, "angle": -2.27}, {"time": 0.9333, "angle": 0.34}, {"time": 1.2667, "angle": -0.04}, {"time": 1.6, "angle": -0.43}, {"time": 1.9333, "angle": -0.03}, {"time": 2.3, "angle": 2.38}, {"time": 2.6, "angle": 0.79}, {"time": 3, "angle": 0}, {"time": 3.3, "angle": -2.23}, {"time": 3.6, "angle": -2.27}, {"time": 3.9333, "angle": 0.34}, {"time": 4.2667, "angle": -0.04}, {"time": 4.6, "angle": -0.43}, {"time": 4.9333, "angle": -0.03}, {"time": 5.3, "angle": 2.38}, {"time": 5.6, "angle": 0.79}, {"time": 6, "angle": 0}]}, "bone25": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3, "angle": -5.7}, {"time": 0.6, "angle": -9.57}, {"time": 0.9333, "angle": -14.84}, {"time": 1.2667, "angle": -13.41}, {"time": 1.6, "angle": -12.68}, {"time": 1.9333, "angle": -16.1}, {"time": 2.3, "angle": -16.37}, {"time": 2.6, "angle": -10.42}, {"time": 3, "angle": 0}, {"time": 3.3, "angle": -5.7}, {"time": 3.6, "angle": -9.57}, {"time": 3.9333, "angle": -14.84}, {"time": 4.2667, "angle": -13.41}, {"time": 4.6, "angle": -12.68}, {"time": 4.9333, "angle": -16.1}, {"time": 5.3, "angle": -16.37}, {"time": 5.6, "angle": -10.42}, {"time": 6, "angle": 0}]}, "bone24": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3, "angle": -4.04}, {"time": 0.6, "angle": -6.83}, {"time": 0.9333, "angle": -10.74}, {"time": 1.2667, "angle": -9.89}, {"time": 1.6, "angle": -9.43}, {"time": 1.9333, "angle": -12.62}, {"time": 2.3, "angle": -12.9}, {"time": 2.6, "angle": -8.2}, {"time": 3, "angle": 0}, {"time": 3.3, "angle": -4.04}, {"time": 3.6, "angle": -6.83}, {"time": 3.9333, "angle": -10.74}, {"time": 4.2667, "angle": -9.89}, {"time": 4.6, "angle": -9.43}, {"time": 4.9333, "angle": -12.62}, {"time": 5.3, "angle": -12.9}, {"time": 5.6, "angle": -8.2}, {"time": 6, "angle": 0}]}, "bone23": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3, "angle": -0.47}, {"time": 0.6, "angle": -0.89}, {"time": 0.9333, "angle": -0.46}, {"time": 1.2667, "angle": -0.95}, {"time": 1.6, "angle": -1.17}, {"time": 1.9333, "angle": -1.87}, {"time": 2.3, "angle": -1.19}, {"time": 2.6, "angle": -1.3}, {"time": 3, "angle": 0}, {"time": 3.3, "angle": -0.47}, {"time": 3.6, "angle": -0.89}, {"time": 3.9333, "angle": -0.46}, {"time": 4.2667, "angle": -0.95}, {"time": 4.6, "angle": -1.17}, {"time": 4.9333, "angle": -1.87}, {"time": 5.3, "angle": -1.19}, {"time": 5.6, "angle": -1.3}, {"time": 6, "angle": 0}]}, "bone22": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3, "angle": 1.05}, {"time": 0.6, "angle": 2.2}, {"time": 0.9333, "angle": 5.34}, {"time": 1.2667, "angle": 5.17}, {"time": 1.6, "angle": 4.5}, {"time": 1.9333, "angle": 4.5}, {"time": 2.3, "angle": 5.39}, {"time": 2.6, "angle": 3.84}, {"time": 3, "angle": 0}, {"time": 3.3, "angle": 1.05}, {"time": 3.6, "angle": 2.2}, {"time": 3.9333, "angle": 5.34}, {"time": 4.2667, "angle": 5.17}, {"time": 4.6, "angle": 4.5}, {"time": 4.9333, "angle": 4.5}, {"time": 5.3, "angle": 5.39}, {"time": 5.6, "angle": 3.84}, {"time": 6, "angle": 0}]}, "bone52": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -0.88}, {"time": 0.5, "angle": -1.91}, {"time": 0.7667, "angle": 0.24}, {"time": 1, "angle": 0}, {"time": 1.2333, "angle": -0.88}, {"time": 1.5, "angle": -1.91}, {"time": 1.7667, "angle": 0.24}, {"time": 2, "angle": 0}, {"time": 2.2333, "angle": -0.88}, {"time": 2.5, "angle": -1.91}, {"time": 2.7667, "angle": 0.24}, {"time": 3, "angle": 0}, {"time": 3.2333, "angle": -0.88}, {"time": 3.5, "angle": -1.91}, {"time": 3.7667, "angle": 0.24}, {"time": 4, "angle": 0}, {"time": 4.2333, "angle": -0.88}, {"time": 4.5, "angle": -1.91}, {"time": 4.7667, "angle": 0.24}, {"time": 5, "angle": 0}, {"time": 5.2333, "angle": -0.88}, {"time": 5.5, "angle": -1.91}, {"time": 5.7667, "angle": 0.24}, {"time": 6, "angle": 0}]}, "bone56": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 34.13}, {"time": 0.5, "angle": 60.99}, {"time": 0.7667, "angle": 24.56}, {"time": 1, "angle": 0}, {"time": 1.2333, "angle": 34.13}, {"time": 1.5, "angle": 60.99}, {"time": 1.7667, "angle": 24.56}, {"time": 2, "angle": 0}, {"time": 2.2333, "angle": 34.13}, {"time": 2.5, "angle": 60.99}, {"time": 2.7667, "angle": 24.56}, {"time": 3, "angle": 0}, {"time": 3.2333, "angle": 34.13}, {"time": 3.5, "angle": 60.99}, {"time": 3.7667, "angle": 24.56}, {"time": 4, "angle": 0}, {"time": 4.2333, "angle": 34.13}, {"time": 4.5, "angle": 60.99}, {"time": 4.7667, "angle": 24.56}, {"time": 5, "angle": 0}, {"time": 5.2333, "angle": 34.13}, {"time": 5.5, "angle": 60.99}, {"time": 5.7667, "angle": 24.56}, {"time": 6, "angle": 0}]}, "bone55": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 20.62}, {"time": 0.5, "angle": 7.62}, {"time": 0.7667, "angle": -4.11}, {"time": 1, "angle": 0}, {"time": 1.2333, "angle": 20.62}, {"time": 1.5, "angle": 7.62}, {"time": 1.7667, "angle": -4.11}, {"time": 2, "angle": 0}, {"time": 2.2333, "angle": 20.62}, {"time": 2.5, "angle": 7.62}, {"time": 2.7667, "angle": -4.11}, {"time": 3, "angle": 0}, {"time": 3.2333, "angle": 20.62}, {"time": 3.5, "angle": 7.62}, {"time": 3.7667, "angle": -4.11}, {"time": 4, "angle": 0}, {"time": 4.2333, "angle": 20.62}, {"time": 4.5, "angle": 7.62}, {"time": 4.7667, "angle": -4.11}, {"time": 5, "angle": 0}, {"time": 5.2333, "angle": 20.62}, {"time": 5.5, "angle": 7.62}, {"time": 5.7667, "angle": -4.11}, {"time": 6, "angle": 0}]}, "bone54": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 2.52}, {"time": 0.5, "angle": -3.17}, {"time": 0.7667, "angle": -8.26}, {"time": 1, "angle": 0}, {"time": 1.2333, "angle": 2.52}, {"time": 1.5, "angle": -3.17}, {"time": 1.7667, "angle": -8.26}, {"time": 2, "angle": 0}, {"time": 2.2333, "angle": 2.52}, {"time": 2.5, "angle": -3.17}, {"time": 2.7667, "angle": -8.26}, {"time": 3, "angle": 0}, {"time": 3.2333, "angle": 2.52}, {"time": 3.5, "angle": -3.17}, {"time": 3.7667, "angle": -8.26}, {"time": 4, "angle": 0}, {"time": 4.2333, "angle": 2.52}, {"time": 4.5, "angle": -3.17}, {"time": 4.7667, "angle": -8.26}, {"time": 5, "angle": 0}, {"time": 5.2333, "angle": 2.52}, {"time": 5.5, "angle": -3.17}, {"time": 5.7667, "angle": -8.26}, {"time": 6, "angle": 0}]}, "bone53": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -3.26}, {"time": 0.5, "angle": -6.86}, {"time": 0.7667, "angle": -0.91}, {"time": 1, "angle": 0}, {"time": 1.2333, "angle": -3.26}, {"time": 1.5, "angle": -6.86}, {"time": 1.7667, "angle": -0.91}, {"time": 2, "angle": 0}, {"time": 2.2333, "angle": -3.26}, {"time": 2.5, "angle": -6.86}, {"time": 2.7667, "angle": -0.91}, {"time": 3, "angle": 0}, {"time": 3.2333, "angle": -3.26}, {"time": 3.5, "angle": -6.86}, {"time": 3.7667, "angle": -0.91}, {"time": 4, "angle": 0}, {"time": 4.2333, "angle": -3.26}, {"time": 4.5, "angle": -6.86}, {"time": 4.7667, "angle": -0.91}, {"time": 5, "angle": 0}, {"time": 5.2333, "angle": -3.26}, {"time": 5.5, "angle": -6.86}, {"time": 5.7667, "angle": -0.91}, {"time": 6, "angle": 0}]}, "bone57": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 1.39}, {"time": 0.5333, "angle": 0.36}, {"time": 0.8, "angle": 2.04}, {"time": 1.0667, "angle": 17.41}, {"time": 1.4667, "angle": 17.54}, {"time": 2, "angle": 0}, {"time": 2.2667, "angle": 1.39}, {"time": 2.5333, "angle": 0.36}, {"time": 2.8, "angle": 2.04}, {"time": 3.0667, "angle": 17.41}, {"time": 3.4667, "angle": 17.54}, {"time": 4, "angle": 0}, {"time": 4.2667, "angle": 1.39}, {"time": 4.5, "angle": 0.36}, {"time": 4.8, "angle": 2.04}, {"time": 5.0667, "angle": 17.41}, {"time": 5.4667, "angle": 17.54}, {"time": 6, "angle": 0}]}, "bone61": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 23.66}, {"time": 0.5333, "angle": 28.44}, {"time": 0.8, "angle": -3.44}, {"time": 1.0667, "angle": -14.73}, {"time": 1.4667, "angle": -10.35}, {"time": 2, "angle": 0}, {"time": 2.2667, "angle": 23.66}, {"time": 2.5333, "angle": 28.44}, {"time": 2.8, "angle": -3.44}, {"time": 3.0667, "angle": -14.73}, {"time": 3.4667, "angle": -10.35}, {"time": 4, "angle": 0}, {"time": 4.2667, "angle": 23.66}, {"time": 4.5, "angle": 28.44}, {"time": 4.8, "angle": -3.44}, {"time": 5.0667, "angle": -14.73}, {"time": 5.4667, "angle": -10.35}, {"time": 6, "angle": 0}]}, "bone60": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": -8.52}, {"time": 0.5333, "angle": -23.53}, {"time": 0.8, "angle": -19.31}, {"time": 1.0667, "angle": -39.82}, {"time": 1.4667, "angle": -28.72}, {"time": 2, "angle": 0}, {"time": 2.2667, "angle": -8.52}, {"time": 2.5333, "angle": -23.53}, {"time": 2.8, "angle": -19.31}, {"time": 3.0667, "angle": -39.82}, {"time": 3.4667, "angle": -28.72}, {"time": 4, "angle": 0}, {"time": 4.2667, "angle": -8.52}, {"time": 4.5, "angle": -23.53}, {"time": 4.8, "angle": -19.31}, {"time": 5.0667, "angle": -39.82}, {"time": 5.4667, "angle": -28.72}, {"time": 6, "angle": 0}]}, "bone59": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 2.53}, {"time": 0.5333, "angle": -4.14}, {"time": 0.8, "angle": -0.61}, {"time": 1.0667, "angle": -14.19}, {"time": 1.4667, "angle": -8.18}, {"time": 2, "angle": 0}, {"time": 2.2667, "angle": 2.53}, {"time": 2.5333, "angle": -4.14}, {"time": 2.8, "angle": -0.61}, {"time": 3.0667, "angle": -14.19}, {"time": 3.4667, "angle": -8.18}, {"time": 4, "angle": 0}, {"time": 4.2667, "angle": 2.53}, {"time": 4.5, "angle": -4.14}, {"time": 4.8, "angle": -0.61}, {"time": 5.0667, "angle": -14.19}, {"time": 5.4667, "angle": -8.18}, {"time": 6, "angle": 0}]}, "bone58": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 2.52}, {"time": 0.5333, "angle": -0.83}, {"time": 0.8, "angle": 2.45}, {"time": 1.0667, "angle": 12.14}, {"time": 1.4667, "angle": 14.85}, {"time": 2, "angle": 0}, {"time": 2.2667, "angle": 2.52}, {"time": 2.5333, "angle": -0.83}, {"time": 2.8, "angle": 2.45}, {"time": 3.0667, "angle": 12.14}, {"time": 3.4667, "angle": 14.85}, {"time": 4, "angle": 0}, {"time": 4.2667, "angle": 2.52}, {"time": 4.5, "angle": -0.83}, {"time": 4.8, "angle": 2.45}, {"time": 5.0667, "angle": 12.14}, {"time": 5.4667, "angle": 14.85}, {"time": 6, "angle": 0}]}, "bone62": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1333, "angle": -0.17}, {"time": 0.2667, "angle": 0.61}, {"time": 0.4667, "angle": -10.73}, {"time": 0.7, "angle": -10.66}, {"time": 0.9, "angle": -1.11}, {"time": 1.0333, "angle": 0}, {"time": 1.2667, "angle": 0.61}, {"time": 1.4667, "angle": -10.73}, {"time": 1.6667, "angle": -10.66}, {"time": 1.9, "angle": -1.11}, {"time": 2.0667, "angle": 0}, {"time": 2.2, "angle": -0.17}, {"time": 2.3333, "angle": 0.61}, {"time": 2.5333, "angle": -10.73}, {"time": 2.7333, "angle": -10.66}, {"time": 2.9333, "angle": -1.11}, {"time": 3.1, "angle": 0}, {"time": 3.3333, "angle": 0.61}, {"time": 3.5333, "angle": -10.73}, {"time": 3.7333, "angle": -10.66}, {"time": 3.9333, "angle": -1.11}, {"time": 4.1, "angle": 0}, {"time": 4.2667, "angle": -0.17}, {"time": 4.4, "angle": 0.61}, {"time": 4.6, "angle": -10.73}, {"time": 4.8, "angle": -10.66}, {"time": 5, "angle": -1.11}, {"time": 5.1333, "angle": 0}, {"time": 5.3667, "angle": 0.61}, {"time": 5.6, "angle": -10.73}, {"time": 5.8, "angle": -10.66}, {"time": 6, "angle": -1.11}]}, "bone66": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1333, "angle": 2.4}, {"time": 0.2667, "angle": 6.85}, {"time": 0.4667, "angle": 7.87}, {"time": 0.7, "angle": 3.38}, {"time": 0.7667, "angle": -13.11}, {"time": 0.9, "angle": -14.5}, {"time": 1.0333, "angle": 0}, {"time": 1.2667, "angle": 6.85}, {"time": 1.4667, "angle": 7.87}, {"time": 1.6667, "angle": 3.38}, {"time": 1.7333, "angle": -13.11}, {"time": 1.9, "angle": -14.5}, {"time": 2.0667, "angle": 0}, {"time": 2.2, "angle": 2.4}, {"time": 2.3333, "angle": 6.85}, {"time": 2.5333, "angle": 7.87}, {"time": 2.7333, "angle": 3.38}, {"time": 2.8, "angle": -13.11}, {"time": 2.9333, "angle": -14.5}, {"time": 3.1, "angle": 0}, {"time": 3.3333, "angle": 6.85}, {"time": 3.5333, "angle": 7.87}, {"time": 3.7333, "angle": 3.38}, {"time": 3.8, "angle": -13.11}, {"time": 3.9333, "angle": -14.5}, {"time": 4.1, "angle": 0}, {"time": 4.2667, "angle": 2.4}, {"time": 4.4, "angle": 6.85}, {"time": 4.6, "angle": 7.87}, {"time": 4.8, "angle": 3.38}, {"time": 4.8667, "angle": -13.11}, {"time": 5, "angle": -14.5}, {"time": 5.1333, "angle": 0}, {"time": 5.3667, "angle": 6.85}, {"time": 5.6, "angle": 7.87}, {"time": 5.8, "angle": 3.38}, {"time": 5.8667, "angle": -13.11}, {"time": 6, "angle": -14.5}]}, "bone65": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1333, "angle": -0.31}, {"time": 0.2667, "angle": 6.24}, {"time": 0.4667, "angle": 2.94}, {"time": 0.7, "angle": -17.29}, {"time": 0.9, "angle": -3.76}, {"time": 1.0333, "angle": 0}, {"time": 1.2667, "angle": 6.24}, {"time": 1.4667, "angle": 2.94}, {"time": 1.6667, "angle": -17.29}, {"time": 1.9, "angle": -3.76}, {"time": 2.0667, "angle": 0}, {"time": 2.2, "angle": -0.31}, {"time": 2.3333, "angle": 6.24}, {"time": 2.5333, "angle": 2.94}, {"time": 2.7333, "angle": -17.29}, {"time": 2.9333, "angle": -3.76}, {"time": 3.1, "angle": 0}, {"time": 3.3333, "angle": 6.24}, {"time": 3.5333, "angle": 2.94}, {"time": 3.7333, "angle": -17.29}, {"time": 3.9333, "angle": -3.76}, {"time": 4.1, "angle": 0}, {"time": 4.2667, "angle": -0.31}, {"time": 4.4, "angle": 6.24}, {"time": 4.6, "angle": 2.94}, {"time": 4.8, "angle": -17.29}, {"time": 5, "angle": -3.76}, {"time": 5.1333, "angle": 0}, {"time": 5.3667, "angle": 6.24}, {"time": 5.6, "angle": 2.94}, {"time": 5.8, "angle": -17.29}, {"time": 6, "angle": -3.76}]}, "bone64": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1333, "angle": 5.49}, {"time": 0.2667, "angle": -1.46}, {"time": 0.4667, "angle": 10.28}, {"time": 0.7, "angle": 1.18}, {"time": 0.9, "angle": -3.17}, {"time": 1.0333, "angle": 0}, {"time": 1.2667, "angle": -1.46}, {"time": 1.4667, "angle": 10.28}, {"time": 1.6667, "angle": 1.18}, {"time": 1.9, "angle": -3.17}, {"time": 2.0667, "angle": 0}, {"time": 2.2, "angle": 5.49}, {"time": 2.3333, "angle": -1.46}, {"time": 2.5333, "angle": 10.28}, {"time": 2.7333, "angle": 1.18}, {"time": 2.9333, "angle": -3.17}, {"time": 3.1, "angle": 0}, {"time": 3.3333, "angle": -1.46}, {"time": 3.5333, "angle": 10.28}, {"time": 3.7333, "angle": 1.18}, {"time": 3.9333, "angle": -3.17}, {"time": 4.1, "angle": 0}, {"time": 4.2667, "angle": 5.49}, {"time": 4.4, "angle": -1.46}, {"time": 4.6, "angle": 10.28}, {"time": 4.8, "angle": 1.18}, {"time": 5, "angle": -3.17}, {"time": 5.1333, "angle": 0}, {"time": 5.3667, "angle": -1.46}, {"time": 5.6, "angle": 10.28}, {"time": 5.8, "angle": 1.18}, {"time": 6, "angle": -3.17}]}, "bone63": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1333, "angle": 0.67}, {"time": 0.2667, "angle": -0.43}, {"time": 0.4667, "angle": -5.11}, {"time": 0.7, "angle": 9.74}, {"time": 0.9, "angle": 7.05}, {"time": 1.0333, "angle": 0}, {"time": 1.2667, "angle": -0.43}, {"time": 1.4667, "angle": -5.11}, {"time": 1.6667, "angle": 9.74}, {"time": 1.9, "angle": 7.05}, {"time": 2.0667, "angle": 0}, {"time": 2.2, "angle": 0.67}, {"time": 2.3333, "angle": -0.43}, {"time": 2.5333, "angle": -5.11}, {"time": 2.7333, "angle": 9.74}, {"time": 2.9333, "angle": 7.05}, {"time": 3.1, "angle": 0}, {"time": 3.3333, "angle": -0.43}, {"time": 3.5333, "angle": -5.11}, {"time": 3.7333, "angle": 9.74}, {"time": 3.9333, "angle": 7.05}, {"time": 4.1, "angle": 0}, {"time": 4.2667, "angle": 0.67}, {"time": 4.4, "angle": -0.43}, {"time": 4.6, "angle": -5.11}, {"time": 4.8, "angle": 9.74}, {"time": 5, "angle": 7.05}, {"time": 5.1333, "angle": 0}, {"time": 5.3667, "angle": -0.43}, {"time": 5.6, "angle": -5.11}, {"time": 5.8, "angle": 9.74}, {"time": 6, "angle": 7.05}]}, "bone46": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": 0.05}, {"time": 0.3, "angle": 1.11}, {"time": 0.4667, "angle": 1.22}, {"time": 0.8333, "angle": 9.53}, {"time": 1.0333, "angle": 0}, {"time": 1.2, "angle": 0.05}, {"time": 1.3667, "angle": 1.11}, {"time": 1.5, "angle": 1.22}, {"time": 1.8667, "angle": 9.53}, {"time": 2.0667, "angle": 0}, {"time": 2.2333, "angle": 0.05}, {"time": 2.4, "angle": 1.11}, {"time": 2.5333, "angle": 1.22}, {"time": 2.9, "angle": 9.53}, {"time": 3.1, "angle": 0}, {"time": 3.2667, "angle": 0.05}, {"time": 3.4333, "angle": 1.11}, {"time": 3.6, "angle": 1.22}, {"time": 3.9333, "angle": 9.53}, {"time": 4.1667, "angle": 0}, {"time": 4.3, "angle": 0.05}, {"time": 4.4667, "angle": 1.11}, {"time": 4.6333, "angle": 1.22}, {"time": 4.9667, "angle": 9.53}, {"time": 5.2, "angle": 0}, {"time": 5.3333, "angle": 0.05}, {"time": 5.5, "angle": 1.11}, {"time": 5.6667, "angle": 1.22}, {"time": 5.9667, "angle": 0}]}, "bone51": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -10.16}, {"time": 0.3, "angle": 3.04}, {"time": 0.4667, "angle": -17.71}, {"time": 0.8333, "angle": -21.85}, {"time": 1.0333, "angle": 0}, {"time": 1.2, "angle": -10.16}, {"time": 1.3667, "angle": 3.04}, {"time": 1.5, "angle": -17.71}, {"time": 1.8667, "angle": -21.85}, {"time": 2.0667, "angle": 0}, {"time": 2.2333, "angle": -10.16}, {"time": 2.4, "angle": 3.04}, {"time": 2.5333, "angle": -17.71}, {"time": 2.9, "angle": -21.85}, {"time": 3.1, "angle": 0}, {"time": 3.2667, "angle": -10.16}, {"time": 3.4333, "angle": 3.04}, {"time": 3.6, "angle": -17.71}, {"time": 3.9333, "angle": -21.85}, {"time": 4.1667, "angle": 0}, {"time": 4.3, "angle": -10.16}, {"time": 4.4667, "angle": 3.04}, {"time": 4.6333, "angle": -17.71}, {"time": 4.9667, "angle": -21.85}, {"time": 5.2, "angle": 0}, {"time": 5.3333, "angle": -10.16}, {"time": 5.5, "angle": 3.04}, {"time": 5.6667, "angle": -17.71}, {"time": 5.9667, "angle": 0}]}, "bone50": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": 5.2}, {"time": 0.3, "angle": -9.83}, {"time": 0.4667, "angle": -12.57}, {"time": 0.8333, "angle": -20.05}, {"time": 1.0333, "angle": 0}, {"time": 1.2, "angle": 5.2}, {"time": 1.3667, "angle": -9.83}, {"time": 1.5, "angle": -12.57}, {"time": 1.8667, "angle": -20.05}, {"time": 2.0667, "angle": 0}, {"time": 2.2333, "angle": 5.2}, {"time": 2.4, "angle": -9.83}, {"time": 2.5333, "angle": -12.57}, {"time": 2.9, "angle": -20.05}, {"time": 3.1, "angle": 0}, {"time": 3.2667, "angle": 5.2}, {"time": 3.4333, "angle": -9.83}, {"time": 3.6, "angle": -12.57}, {"time": 3.9333, "angle": -20.05}, {"time": 4.1667, "angle": 0}, {"time": 4.3, "angle": 5.2}, {"time": 4.4667, "angle": -9.83}, {"time": 4.6333, "angle": -12.57}, {"time": 4.9667, "angle": -20.05}, {"time": 5.2, "angle": 0}, {"time": 5.3333, "angle": 5.2}, {"time": 5.5, "angle": -9.83}, {"time": 5.6667, "angle": -12.57}, {"time": 5.9667, "angle": 0}]}, "bone49": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": 21.76}, {"time": 0.3, "angle": 9.05}, {"time": 0.4667, "angle": 6.97}, {"time": 0.8333, "angle": -4.68}, {"time": 1.0333, "angle": 0}, {"time": 1.2, "angle": 21.76}, {"time": 1.3667, "angle": 9.05}, {"time": 1.5, "angle": 6.97}, {"time": 1.8667, "angle": -4.68}, {"time": 2.0667, "angle": 0}, {"time": 2.2333, "angle": 21.76}, {"time": 2.4, "angle": 9.05}, {"time": 2.5333, "angle": 6.97}, {"time": 2.9, "angle": -4.68}, {"time": 3.1, "angle": 0}, {"time": 3.2667, "angle": 21.76}, {"time": 3.4333, "angle": 9.05}, {"time": 3.6, "angle": 6.97}, {"time": 3.9333, "angle": -4.68}, {"time": 4.1667, "angle": 0}, {"time": 4.3, "angle": 21.76}, {"time": 4.4667, "angle": 9.05}, {"time": 4.6333, "angle": 6.97}, {"time": 4.9667, "angle": -4.68}, {"time": 5.2, "angle": 0}, {"time": 5.3333, "angle": 21.76}, {"time": 5.5, "angle": 9.05}, {"time": 5.6667, "angle": 6.97}, {"time": 5.9667, "angle": 0}]}, "bone48": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -8.71}, {"time": 0.3, "angle": -9.65}, {"time": 0.4667, "angle": -10.23}, {"time": 0.8333, "angle": -8}, {"time": 1.0333, "angle": 0}, {"time": 1.2, "angle": -8.71}, {"time": 1.3667, "angle": -9.65}, {"time": 1.5, "angle": -10.23}, {"time": 1.8667, "angle": -8}, {"time": 2.0667, "angle": 0}, {"time": 2.2333, "angle": -8.71}, {"time": 2.4, "angle": -9.65}, {"time": 2.5333, "angle": -10.23}, {"time": 2.9, "angle": -8}, {"time": 3.1, "angle": 0}, {"time": 3.2667, "angle": -8.71}, {"time": 3.4333, "angle": -9.65}, {"time": 3.6, "angle": -10.23}, {"time": 3.9333, "angle": -8}, {"time": 4.1667, "angle": 0}, {"time": 4.3, "angle": -8.71}, {"time": 4.4667, "angle": -9.65}, {"time": 4.6333, "angle": -10.23}, {"time": 4.9667, "angle": -8}, {"time": 5.2, "angle": 0}, {"time": 5.3333, "angle": -8.71}, {"time": 5.5, "angle": -9.65}, {"time": 5.6667, "angle": -10.23}, {"time": 5.9667, "angle": 0}]}, "bone47": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -9.32}, {"time": 0.3, "angle": -8.06}, {"time": 0.4667, "angle": -8.3}, {"time": 0.8333, "angle": 1.56}, {"time": 1.0333, "angle": 0}, {"time": 1.2, "angle": -9.32}, {"time": 1.3667, "angle": -8.06}, {"time": 1.5, "angle": -8.3}, {"time": 1.8667, "angle": 1.56}, {"time": 2.0667, "angle": 0}, {"time": 2.2333, "angle": -9.32}, {"time": 2.4, "angle": -8.06}, {"time": 2.5333, "angle": -8.3}, {"time": 2.9, "angle": 1.56}, {"time": 3.1, "angle": 0}, {"time": 3.2667, "angle": -9.32}, {"time": 3.4333, "angle": -8.06}, {"time": 3.6, "angle": -8.3}, {"time": 3.9333, "angle": 1.56}, {"time": 4.1667, "angle": 0}, {"time": 4.3, "angle": -9.32}, {"time": 4.4667, "angle": -8.06}, {"time": 4.6333, "angle": -8.3}, {"time": 4.9667, "angle": 1.56}, {"time": 5.2, "angle": 0}, {"time": 5.3333, "angle": -9.32}, {"time": 5.5, "angle": -8.06}, {"time": 5.6667, "angle": -8.3}, {"time": 5.9667, "angle": 0}]}, "bone67": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -7.68}, {"time": 0.4667, "angle": -8.65}, {"time": 0.7, "angle": -11.33}, {"time": 0.8667, "angle": 0}, {"time": 1.1, "angle": -7.68}, {"time": 1.3333, "angle": -8.65}, {"time": 1.5667, "angle": -11.33}, {"time": 1.8333, "angle": 0}, {"time": 2, "angle": -7.68}, {"time": 2.3, "angle": -8.65}, {"time": 2.4667, "angle": -11.33}, {"time": 2.7, "angle": 0}, {"time": 2.9, "angle": -7.68}, {"time": 3.1667, "angle": -8.65}, {"time": 3.4, "angle": -11.33}, {"time": 3.5667, "angle": 0}, {"time": 3.7667, "angle": -7.68}, {"time": 4.0333, "angle": -8.65}, {"time": 4.2667, "angle": -11.33}, {"time": 4.4667, "angle": 0}, {"time": 4.7, "angle": -7.68}, {"time": 4.9667, "angle": -8.65}, {"time": 5.1667, "angle": -11.33}, {"time": 5.3, "angle": 0}, {"time": 5.5, "angle": -7.68}, {"time": 5.7667, "angle": -8.65}, {"time": 6, "angle": 0}]}, "bone71": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 17.16}, {"time": 0.4667, "angle": 14.03}, {"time": 0.7, "angle": 19.09}, {"time": 0.8667, "angle": 0}, {"time": 1.1, "angle": 17.16}, {"time": 1.3333, "angle": 14.03}, {"time": 1.5667, "angle": 19.09}, {"time": 1.8333, "angle": 0}, {"time": 2, "angle": 17.16}, {"time": 2.3, "angle": 14.03}, {"time": 2.4667, "angle": 19.09}, {"time": 2.7, "angle": 0}, {"time": 2.9, "angle": 17.16}, {"time": 3.1667, "angle": 14.03}, {"time": 3.4, "angle": 19.09}, {"time": 3.5667, "angle": 0}, {"time": 3.7667, "angle": 17.16}, {"time": 4.0333, "angle": 14.03}, {"time": 4.2667, "angle": 19.09}, {"time": 4.4667, "angle": 0}, {"time": 4.7, "angle": 17.16}, {"time": 4.9667, "angle": 14.03}, {"time": 5.1667, "angle": 19.09}, {"time": 5.3, "angle": 0}, {"time": 5.5, "angle": 17.16}, {"time": 5.7667, "angle": 14.03}, {"time": 6, "angle": 0}]}, "bone70": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -19.2}, {"time": 0.4667, "angle": -14.22}, {"time": 0.7, "angle": -16.22}, {"time": 0.8667, "angle": 0}, {"time": 1.1, "angle": -19.2}, {"time": 1.3333, "angle": -14.22}, {"time": 1.5667, "angle": -16.22}, {"time": 1.8333, "angle": 0}, {"time": 2, "angle": -19.2}, {"time": 2.3, "angle": -14.22}, {"time": 2.4667, "angle": -16.22}, {"time": 2.7, "angle": 0}, {"time": 2.9, "angle": -19.2}, {"time": 3.1667, "angle": -14.22}, {"time": 3.4, "angle": -16.22}, {"time": 3.5667, "angle": 0}, {"time": 3.7667, "angle": -19.2}, {"time": 4.0333, "angle": -14.22}, {"time": 4.2667, "angle": -16.22}, {"time": 4.4667, "angle": 0}, {"time": 4.7, "angle": -19.2}, {"time": 4.9667, "angle": -14.22}, {"time": 5.1667, "angle": -16.22}, {"time": 5.3, "angle": 0}, {"time": 5.5, "angle": -19.2}, {"time": 5.7667, "angle": -14.22}, {"time": 6, "angle": 0}]}, "bone69": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -3.86}, {"time": 0.4667, "angle": 0.9}, {"time": 0.7, "angle": -5.04}, {"time": 0.8667, "angle": 0}, {"time": 1.1, "angle": -3.86}, {"time": 1.3333, "angle": 0.9}, {"time": 1.5667, "angle": -5.04}, {"time": 1.8333, "angle": 0}, {"time": 2, "angle": -3.86}, {"time": 2.3, "angle": 0.9}, {"time": 2.4667, "angle": -5.04}, {"time": 2.7, "angle": 0}, {"time": 2.9, "angle": -3.86}, {"time": 3.1667, "angle": 0.9}, {"time": 3.4, "angle": -5.04}, {"time": 3.5667, "angle": 0}, {"time": 3.7667, "angle": -3.86}, {"time": 4.0333, "angle": 0.9}, {"time": 4.2667, "angle": -5.04}, {"time": 4.4667, "angle": 0}, {"time": 4.7, "angle": -3.86}, {"time": 4.9667, "angle": 0.9}, {"time": 5.1667, "angle": -5.04}, {"time": 5.3, "angle": 0}, {"time": 5.5, "angle": -3.86}, {"time": 5.7667, "angle": 0.9}, {"time": 6, "angle": 0}]}, "bone68": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 4.36}, {"time": 0.4667, "angle": 8.54}, {"time": 0.7, "angle": 7.22}, {"time": 0.8667, "angle": 0}, {"time": 1.1, "angle": 4.36}, {"time": 1.3333, "angle": 8.54}, {"time": 1.5667, "angle": 7.22}, {"time": 1.8333, "angle": 0}, {"time": 2, "angle": 4.36}, {"time": 2.3, "angle": 8.54}, {"time": 2.4667, "angle": 7.22}, {"time": 2.7, "angle": 0}, {"time": 2.9, "angle": 4.36}, {"time": 3.1667, "angle": 8.54}, {"time": 3.4, "angle": 7.22}, {"time": 3.5667, "angle": 0}, {"time": 3.7667, "angle": 4.36}, {"time": 4.0333, "angle": 8.54}, {"time": 4.2667, "angle": 7.22}, {"time": 4.4667, "angle": 0}, {"time": 4.7, "angle": 4.36}, {"time": 4.9667, "angle": 8.54}, {"time": 5.1667, "angle": 7.22}, {"time": 5.3, "angle": 0}, {"time": 5.5, "angle": 4.36}, {"time": 5.7667, "angle": 8.54}, {"time": 6, "angle": 0}]}, "bone": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.5, "x": 0.03, "y": 3.59}, {"time": 3, "x": 0, "y": 0}, {"time": 4.5, "x": 0.03, "y": 3.59}, {"time": 6, "x": 0, "y": 0}], "scale": [{"time": 1.5, "x": 1, "y": 1}, {"time": 1.8, "x": 1.228, "y": 1.228, "curve": "stepped"}, {"time": 3.6667, "x": 1.228, "y": 1.228}, {"time": 4, "x": 1, "y": 1}]}, "bone2": {"translate": [{"time": 1.5, "x": 0, "y": 0}, {"time": 1.7667, "x": -7.13, "y": 0.14, "curve": "stepped"}, {"time": 3.6667, "x": -7.13, "y": 0.14}, {"time": 4, "x": 0, "y": 0}]}, "bone3": {"translate": [{"time": 1.5, "x": 0, "y": 0}, {"time": 1.7667, "x": 8.21, "y": 0.39, "curve": "stepped"}, {"time": 3.6667, "x": 8.21, "y": 0.39}, {"time": 4, "x": 0, "y": 0}]}, "bone6": {"translate": [{"time": 1.5, "x": 0, "y": 0}, {"time": 1.7667, "x": -6.52, "y": 0, "curve": "stepped"}, {"time": 3.6667, "x": -6.52, "y": 0}, {"time": 4, "x": 0, "y": 0}]}, "bone36": {"translate": [{"time": 1.5, "x": 0, "y": 0}, {"time": 1.7667, "x": 9.13, "y": 0.31, "curve": "stepped"}, {"time": 3.6667, "x": 9.13, "y": 0.31}, {"time": 4, "x": 0, "y": 0}]}, "bone79": {"scale": [{"time": 0, "x": 0.602, "y": 0.602, "curve": "stepped"}, {"time": 1.5, "x": 0.602, "y": 0.602}, {"time": 1.8, "x": 0.734, "y": 0.734, "curve": "stepped"}, {"time": 3.8, "x": 0.734, "y": 0.734}, {"time": 4.5, "x": 0.602, "y": 0.602}]}, "bone80": {"scale": [{"time": 0, "x": 0.602, "y": -0.592, "curve": "stepped"}, {"time": 1.5, "x": 0.602, "y": -0.592}, {"time": 1.8, "x": 0.756, "y": -0.743, "curve": "stepped"}, {"time": 3.8, "x": 0.756, "y": -0.743}, {"time": 4.5, "x": 0.602, "y": -0.592}]}, "bone81": {"scale": [{"time": 0, "x": 0, "y": 0}, {"time": 1, "x": 1, "y": 1}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0}, {"time": 5, "x": 1, "y": 1}, {"time": 6, "x": 0, "y": 0}]}, "bone82": {"scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 1.3333, "x": 1, "y": 1}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.6667, "x": 0, "y": 0}, {"time": 4.6667, "x": 1, "y": 1}, {"time": 5.6667, "x": 0, "y": 0}]}, "bone83": {"scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0}, {"time": 3.3333, "x": 1, "y": 1}, {"time": 4.3333, "x": 0, "y": 0}]}, "bone84": {"translate": [{"time": 1.6667, "x": 0, "y": 0}, {"time": 3.1333, "x": 0, "y": -233.02}]}, "bone85": {"translate": [{"time": 2, "x": 0, "y": 0}, {"time": 3.4667, "x": 0, "y": -233.02}]}, "bone86": {"translate": [{"time": 2.2, "x": 0, "y": 0}, {"time": 3.6667, "x": 0, "y": -251.98}]}, "bone87": {"translate": [{"time": 1.8, "x": 0, "y": 0}, {"time": 3.2667, "x": 0, "y": -251.98}]}}, "deform": {"default": {"kho bau tu linh1": {"kho bau tu linh1": [{"time": 1.5}, {"time": 1.7667, "vertices": [4.41284, 2.02998, 0, 0, 0.20552, -1.00409, 0, 0, 1.80989, -0.19282, 4.19058, 0.98897, 2.18423, 0.1759, 4.83124, -1.58616, 6.03265, -0.57571, 5.83351, -0.9786, 2.21185, -0.19098, 4.34875, -1.24733, 4.40398, 0.01993, 2.39922, 0.17085, -1.03571, -0.96471, -3.11888, -0.25412, -1.68106, 0.23242, -4.31564, -0.97956, -9.35558, -1.00233, -9.36211, 0.43765, -9.36211, 0.43765, -10.3264, 1.39329, 0, 0, -7.67778, -0.51473, -2.5611, 0.22841, -1.11754, -0.56508, 0, 0, 3.61981, -0.38563, 2.41106, 0.21191, 0.40198, 0.00182, 4.22002, 0.2201, 3.0177, -0.58935, 1.60359, -2.77179, 3.16817, -0.92064, 1.20871, -0.59753, 0.0009, -0.20099, 0.00456, -1.00498, 4.43384, -2.59295, -2.11967, -0.08259, -3.72566, -0.08986, -3.14099, -0.23321, -1.68201, -0.00761, -0.29533, 0.07166, -0.66034, 0.07001, -1.463, -0.00662, -1.82802, -0.00827, -1.463, -0.00662, -1.33, -0.00602, -1.33, -0.00602, -0.11891, 0.20147, -0.11984, 0.40346, 0.38104, 1.31473, 0.28143, 1.01129, -1.33, -0.00602, -2.93169, -0.96227, -2.35036, -0.37564, -2.65999, -0.01204, -3.65221, 0.47848, -4.24532, 0.27781, -3.65042, 0.08248, -1.37254, -0.10521, -2.65999, -0.01204, -2.65999, -0.01204, -2.65999, -0.01204, -2.65999, -0.01204, -1.995, -0.00903, -1.995, -0.00903, -1.99632, 0.28797, 0.18298, 0.00083, 0.28333, -0.29572, -0.30934, -0.59541, -1.50809, -0.20482, -3.91245, 0.0843, -2.94536, 0.27868], "curve": "stepped"}, {"time": 3.6667, "vertices": [4.41284, 2.02998, 0, 0, 0.20552, -1.00409, 0, 0, 1.80989, -0.19282, 4.19058, 0.98897, 2.18423, 0.1759, 4.83124, -1.58616, 6.03265, -0.57571, 5.83351, -0.9786, 2.21185, -0.19098, 4.34875, -1.24733, 4.40398, 0.01993, 2.39922, 0.17085, -1.03571, -0.96471, -3.11888, -0.25412, -1.68106, 0.23242, -4.31564, -0.97956, -9.35558, -1.00233, -9.36211, 0.43765, -9.36211, 0.43765, -10.3264, 1.39329, 0, 0, -7.67778, -0.51473, -2.5611, 0.22841, -1.11754, -0.56508, 0, 0, 3.61981, -0.38563, 2.41106, 0.21191, 0.40198, 0.00182, 4.22002, 0.2201, 3.0177, -0.58935, 1.60359, -2.77179, 3.16817, -0.92064, 1.20871, -0.59753, 0.0009, -0.20099, 0.00456, -1.00498, 4.43384, -2.59295, -2.11967, -0.08259, -3.72566, -0.08986, -3.14099, -0.23321, -1.68201, -0.00761, -0.29533, 0.07166, -0.66034, 0.07001, -1.463, -0.00662, -1.82802, -0.00827, -1.463, -0.00662, -1.33, -0.00602, -1.33, -0.00602, -0.11891, 0.20147, -0.11984, 0.40346, 0.38104, 1.31473, 0.28143, 1.01129, -1.33, -0.00602, -2.93169, -0.96227, -2.35036, -0.37564, -2.65999, -0.01204, -3.65221, 0.47848, -4.24532, 0.27781, -3.65042, 0.08248, -1.37254, -0.10521, -2.65999, -0.01204, -2.65999, -0.01204, -2.65999, -0.01204, -2.65999, -0.01204, -1.995, -0.00903, -1.995, -0.00903, -1.99632, 0.28797, 0.18298, 0.00083, 0.28333, -0.29572, -0.30934, -0.59541, -1.50809, -0.20482, -3.91245, 0.0843, -2.94536, 0.27868]}, {"time": 4}]}, "kho bau tu linh4": {"kho bau tu linh4": [{"time": 0}, {"time": 0.2333, "offset": 2, "vertices": [1.50352, 1.1107, 0.98338, 3.90174, 2.44002, 6.72669, 1.45419, 8.09232, 0.9756, 6.40413, 0.81943, 3.25148, 1.31113, 5.20235, 1.08223, 3.23848]}, {"time": 0.4333}, {"time": 0.7, "offset": 2, "vertices": [1.50352, 1.1107, 0.98338, 3.90174, 2.44002, 6.72669, 1.45419, 8.09232, 0.9756, 6.40413, 0.81943, 3.25148, 1.31113, 5.20235, 1.08223, 3.23848]}, {"time": 0.9}, {"time": 1.1333, "offset": 2, "vertices": [1.50352, 1.1107, 0.98338, 3.90174, 2.44002, 6.72669, 1.45419, 8.09232, 0.9756, 6.40413, 0.81943, 3.25148, 1.31113, 5.20235, 1.08223, 3.23848]}, {"time": 1.3333}, {"time": 1.5667, "offset": 2, "vertices": [1.50352, 1.1107, 0.98338, 3.90174, 2.44002, 6.72669, 1.45419, 8.09232, 0.9756, 6.40413, 0.81943, 3.25148, 1.31113, 5.20235, 1.08223, 3.23848]}, {"time": 1.7667}, {"time": 2.0333, "offset": 2, "vertices": [1.50352, 1.1107, 0.98338, 3.90174, 2.44002, 6.72669, 1.45419, 8.09232, 0.9756, 6.40413, 0.81943, 3.25148, 1.31113, 5.20235, 1.08223, 3.23848]}, {"time": 2.2333}, {"time": 2.4667, "offset": 2, "vertices": [1.50352, 1.1107, 0.98338, 3.90174, 2.44002, 6.72669, 1.45419, 8.09232, 0.9756, 6.40413, 0.81943, 3.25148, 1.31113, 5.20235, 1.08223, 3.23848]}, {"time": 2.6667}, {"time": 2.9, "offset": 2, "vertices": [1.50352, 1.1107, 0.98338, 3.90174, 2.44002, 6.72669, 1.45419, 8.09232, 0.9756, 6.40413, 0.81943, 3.25148, 1.31113, 5.20235, 1.08223, 3.23848]}, {"time": 3.1333}, {"time": 3.3667, "offset": 2, "vertices": [1.50352, 1.1107, 0.98338, 3.90174, 2.44002, 6.72669, 1.45419, 8.09232, 0.9756, 6.40413, 0.81943, 3.25148, 1.31113, 5.20235, 1.08223, 3.23848]}, {"time": 3.5667}, {"time": 3.8, "offset": 2, "vertices": [1.50352, 1.1107, 0.98338, 3.90174, 2.44002, 6.72669, 1.45419, 8.09232, 0.9756, 6.40413, 0.81943, 3.25148, 1.31113, 5.20235, 1.08223, 3.23848]}, {"time": 4}, {"time": 4.2333, "offset": 2, "vertices": [1.50352, 1.1107, 0.98338, 3.90174, 2.44002, 6.72669, 1.45419, 8.09232, 0.9756, 6.40413, 0.81943, 3.25148, 1.31113, 5.20235, 1.08223, 3.23848]}, {"time": 4.4667}, {"time": 4.7, "offset": 2, "vertices": [1.50352, 1.1107, 0.98338, 3.90174, 2.44002, 6.72669, 1.45419, 8.09232, 0.9756, 6.40413, 0.81943, 3.25148, 1.31113, 5.20235, 1.08223, 3.23848]}, {"time": 4.9}, {"time": 5.1333, "offset": 2, "vertices": [1.50352, 1.1107, 0.98338, 3.90174, 2.44002, 6.72669, 1.45419, 8.09232, 0.9756, 6.40413, 0.81943, 3.25148, 1.31113, 5.20235, 1.08223, 3.23848], "curve": "stepped"}, {"time": 5.3333, "offset": 2, "vertices": [1.50352, 1.1107, 0.98338, 3.90174, 2.44002, 6.72669, 1.45419, 8.09232, 0.9756, 6.40413, 0.81943, 3.25148, 1.31113, 5.20235, 1.08223, 3.23848]}, {"time": 5.5333}, {"time": 5.8, "offset": 2, "vertices": [1.50352, 1.1107, 0.98338, 3.90174, 2.44002, 6.72669, 1.45419, 8.09232, 0.9756, 6.40413, 0.81943, 3.25148, 1.31113, 5.20235, 1.08223, 3.23848]}, {"time": 6}]}, "kho bau tu linh8": {"kho bau tu linh8": [{"time": 1.7667}, {"time": 1.8667, "offset": 2, "vertices": [2.95633, -0.65793, 5.71043, -4.49552, 4.55597, -2.00745, 4.75809, 1.17218, 3.16379, 1.84078, 3.39966, 0.70758]}, {"time": 1.9667}, {"time": 2.0667, "offset": 2, "vertices": [2.95633, -0.65793, 5.71043, -4.49552, 4.55597, -2.00745, 4.75809, 1.17218, 3.16379, 1.84078, 3.39966, 0.70758]}, {"time": 2.1667}, {"time": 2.3, "offset": 2, "vertices": [2.95633, -0.65793, 5.71043, -4.49552, 4.55597, -2.00745, 4.75809, 1.17218, 3.16379, 1.84078, 3.39966, 0.70758]}, {"time": 2.3667}, {"time": 2.4667, "offset": 2, "vertices": [2.95633, -0.65793, 5.71043, -4.49552, 4.55597, -2.00745, 4.75809, 1.17218, 3.16379, 1.84078, 3.39966, 0.70758]}, {"time": 2.6}, {"time": 2.6667, "offset": 2, "vertices": [2.95633, -0.65793, 5.71043, -4.49552, 4.55597, -2.00745, 4.75809, 1.17218, 3.16379, 1.84078, 3.39966, 0.70758]}, {"time": 2.8}, {"time": 2.9, "offset": 2, "vertices": [2.95633, -0.65793, 5.71043, -4.49552, 4.55597, -2.00745, 4.75809, 1.17218, 3.16379, 1.84078, 3.39966, 0.70758]}, {"time": 3}, {"time": 3.0667, "offset": 2, "vertices": [2.95633, -0.65793, 5.71043, -4.49552, 4.55597, -2.00745, 4.75809, 1.17218, 3.16379, 1.84078, 3.39966, 0.70758]}, {"time": 3.2}, {"time": 3.3, "offset": 2, "vertices": [2.95633, -0.65793, 5.71043, -4.49552, 4.55597, -2.00745, 4.75809, 1.17218, 3.16379, 1.84078, 3.39966, 0.70758]}, {"time": 3.4}, {"time": 3.5667, "offset": 2, "vertices": [2.95633, -0.65793, 5.71043, -4.49552, 4.55597, -2.00745, 4.75809, 1.17218, 3.16379, 1.84078, 3.39966, 0.70758]}, {"time": 3.7667}]}, "kho bau tu linh9": {"kho bau tu linh9": [{"time": 0}, {"time": 0.2333, "offset": 2, "vertices": [0.51964, -1.55916, 1.03923, -3.53404, 1.87091, -4.5735, 1.66301, -6.0287, 1.87073, -4.98928, 1.76697, -4.67748, 1.3512, -3.63805, 0.72754, -3.43015, 1.14331, -3.63805]}, {"time": 0.4333}, {"time": 0.7, "offset": 2, "vertices": [0.51964, -1.55916, 1.03923, -3.53404, 1.87091, -4.5735, 1.66301, -6.0287, 1.87073, -4.98928, 1.76697, -4.67748, 1.3512, -3.63805, 0.72754, -3.43015, 1.14331, -3.63805]}, {"time": 0.9}, {"time": 1.1333, "offset": 2, "vertices": [0.51964, -1.55916, 1.03923, -3.53404, 1.87091, -4.5735, 1.66301, -6.0287, 1.87073, -4.98928, 1.76697, -4.67748, 1.3512, -3.63805, 0.72754, -3.43015, 1.14331, -3.63805]}, {"time": 1.3333}, {"time": 1.6, "offset": 2, "vertices": [0.51964, -1.55916, 1.03923, -3.53404, 1.87091, -4.5735, 1.66301, -6.0287, 1.87073, -4.98928, 1.76697, -4.67748, 1.3512, -3.63805, 0.72754, -3.43015, 1.14331, -3.63805]}, {"time": 1.7667}, {"time": 2, "offset": 2, "vertices": [0.51964, -1.55916, 1.03923, -3.53404, 1.87091, -4.5735, 1.66301, -6.0287, 1.87073, -4.98928, 1.76697, -4.67748, 1.3512, -3.63805, 0.72754, -3.43015, 1.14331, -3.63805]}, {"time": 2.2}, {"time": 2.4667, "offset": 2, "vertices": [0.51964, -1.55916, 1.03923, -3.53404, 1.87091, -4.5735, 1.66301, -6.0287, 1.87073, -4.98928, 1.76697, -4.67748, 1.3512, -3.63805, 0.72754, -3.43015, 1.14331, -3.63805]}, {"time": 2.6667}, {"time": 2.9, "offset": 2, "vertices": [0.51964, -1.55916, 1.03923, -3.53404, 1.87091, -4.5735, 1.66301, -6.0287, 1.87073, -4.98928, 1.76697, -4.67748, 1.3512, -3.63805, 0.72754, -3.43015, 1.14331, -3.63805]}, {"time": 3.1}, {"time": 3.3667, "offset": 2, "vertices": [0.51964, -1.55916, 1.03923, -3.53404, 1.87091, -4.5735, 1.66301, -6.0287, 1.87073, -4.98928, 1.76697, -4.67748, 1.3512, -3.63805, 0.72754, -3.43015, 1.14331, -3.63805]}, {"time": 3.5667}, {"time": 3.8, "offset": 2, "vertices": [0.51964, -1.55916, 1.03923, -3.53404, 1.87091, -4.5735, 1.66301, -6.0287, 1.87073, -4.98928, 1.76697, -4.67748, 1.3512, -3.63805, 0.72754, -3.43015, 1.14331, -3.63805]}, {"time": 4}, {"time": 4.2667, "offset": 2, "vertices": [0.51964, -1.55916, 1.03923, -3.53404, 1.87091, -4.5735, 1.66301, -6.0287, 1.87073, -4.98928, 1.76697, -4.67748, 1.3512, -3.63805, 0.72754, -3.43015, 1.14331, -3.63805]}, {"time": 4.4667}, {"time": 4.7, "offset": 2, "vertices": [0.51964, -1.55916, 1.03923, -3.53404, 1.87091, -4.5735, 1.66301, -6.0287, 1.87073, -4.98928, 1.76697, -4.67748, 1.3512, -3.63805, 0.72754, -3.43015, 1.14331, -3.63805]}, {"time": 4.8667}, {"time": 5.1333, "offset": 2, "vertices": [0.51964, -1.55916, 1.03923, -3.53404, 1.87091, -4.5735, 1.66301, -6.0287, 1.87073, -4.98928, 1.76697, -4.67748, 1.3512, -3.63805, 0.72754, -3.43015, 1.14331, -3.63805], "curve": "stepped"}, {"time": 5.3333, "offset": 2, "vertices": [0.51964, -1.55916, 1.03923, -3.53404, 1.87091, -4.5735, 1.66301, -6.0287, 1.87073, -4.98928, 1.76697, -4.67748, 1.3512, -3.63805, 0.72754, -3.43015, 1.14331, -3.63805]}, {"time": 5.5333}, {"time": 5.8, "offset": 2, "vertices": [0.51964, -1.55916, 1.03923, -3.53404, 1.87091, -4.5735, 1.66301, -6.0287, 1.87073, -4.98928, 1.76697, -4.67748, 1.3512, -3.63805, 0.72754, -3.43015, 1.14331, -3.63805]}, {"time": 6}]}, "kho bau tu linh21": {"kho bau tu linh8": [{"time": 1.7667}, {"time": 1.8667, "offset": 2, "vertices": [2.20125, 1.12223, 4.39902, 2.68645, 4.18668, 1.57977, 3.74985, 0.91331, 2.20985, 0.01726, 2.87622, -0.41955, -0.442]}, {"time": 1.9667}, {"time": 2.0667, "offset": 2, "vertices": [2.20125, 1.12223, 4.39902, 2.68645, 4.18668, 1.57977, 3.74985, 0.91331, 2.20985, 0.01726, 2.87622, -0.41955, -0.442]}, {"time": 2.1667}, {"time": 2.2667, "offset": 2, "vertices": [2.20125, 1.12223, 4.39902, 2.68645, 4.18668, 1.57977, 3.74985, 0.91331, 2.20985, 0.01726, 2.87622, -0.41955, -0.442]}, {"time": 2.3667}, {"time": 2.4667, "offset": 2, "vertices": [2.20125, 1.12223, 4.39902, 2.68645, 4.18668, 1.57977, 3.74985, 0.91331, 2.20985, 0.01726, 2.87622, -0.41955, -0.442]}, {"time": 2.5667}, {"time": 2.6667, "offset": 2, "vertices": [2.20125, 1.12223, 4.39902, 2.68645, 4.18668, 1.57977, 3.74985, 0.91331, 2.20985, 0.01726, 2.87622, -0.41955, -0.442]}, {"time": 2.7667}, {"time": 2.8667, "offset": 2, "vertices": [2.20125, 1.12223, 4.39902, 2.68645, 4.18668, 1.57977, 3.74985, 0.91331, 2.20985, 0.01726, 2.87622, -0.41955, -0.442]}, {"time": 2.9667}, {"time": 3.0667, "offset": 2, "vertices": [2.20125, 1.12223, 4.39902, 2.68645, 4.18668, 1.57977, 3.74985, 0.91331, 2.20985, 0.01726, 2.87622, -0.41955, -0.442]}, {"time": 3.1667}, {"time": 3.2667, "offset": 2, "vertices": [2.20125, 1.12223, 4.39902, 2.68645, 4.18668, 1.57977, 3.74985, 0.91331, 2.20985, 0.01726, 2.87622, -0.41955, -0.442]}, {"time": 3.4}, {"time": 3.6, "offset": 2, "vertices": [2.20125, 1.12223, 4.39902, 2.68645, 4.18668, 1.57977, 3.74985, 0.91331, 2.20985, 0.01726, 2.87622, -0.41955, -0.442]}, {"time": 3.8}]}}}}}}