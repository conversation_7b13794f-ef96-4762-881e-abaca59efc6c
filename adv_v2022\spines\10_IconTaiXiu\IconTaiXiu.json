{"skeleton": {"hash": "RgQXx2BQb9faOHPHAaRlXHEX7zk", "spine": "3.7.93", "width": 358.63, "height": 449.93, "images": "", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": 13.37, "y": 57.21}, {"name": "bone2", "parent": "root"}, {"name": "bone3", "parent": "root", "x": 36.15, "y": -38.13}, {"name": "bone4", "parent": "root", "x": 34.52, "y": -110.7}, {"name": "bone5", "parent": "root", "x": -1.7, "y": 1.7}, {"name": "bone6", "parent": "root", "x": 9.04, "y": 61.75}, {"name": "bone7", "parent": "root", "x": -36.08, "y": -107.08}, {"name": "bone8", "parent": "root", "x": -88.76, "y": -33.76, "scaleX": 0.761, "scaleY": 0.8}, {"name": "bone9", "parent": "root", "x": 84.44, "y": -105.56, "scaleX": 0.8, "scaleY": 0.882}, {"name": "bone10", "parent": "root", "x": 64.4, "y": -124.04}, {"name": "bone11", "parent": "root", "x": -12.04, "y": 63.84}, {"name": "bone12", "parent": "root", "x": 19.32, "y": 101.92}, {"name": "bone13", "parent": "root", "x": 33.88, "y": 7.84}, {"name": "bone14", "parent": "root", "x": -48.72, "y": 47.32}, {"name": "bone15", "parent": "root", "x": -218.76, "y": -68.02}, {"name": "bone16", "parent": "root", "x": 210.28, "y": -30.27}, {"name": "bone17", "parent": "root", "x": 13.37, "y": 57.21}, {"name": "bone18", "parent": "root", "x": 13.37, "y": 57.21}, {"name": "bone19", "parent": "root", "x": 13.37, "y": 57.21}, {"name": "bone20", "parent": "root", "x": 13.37, "y": 57.21}], "slots": [{"name": "11", "bone": "bone5", "attachment": "11"}, {"name": "12", "bone": "bone6", "attachment": "12"}, {"name": "10", "bone": "bone4", "attachment": "10"}, {"name": "6", "bone": "bone11", "attachment": "6"}, {"name": "light", "bone": "bone", "attachment": "light"}, {"name": "light2", "bone": "bone17", "attachment": "light"}, {"name": "light3", "bone": "bone18", "attachment": "light"}, {"name": "light4", "bone": "bone19", "attachment": "light"}, {"name": "light5", "bone": "bone20", "attachment": "light"}, {"name": "9", "bone": "bone14", "attachment": "9"}, {"name": "8", "bone": "bone13", "attachment": "8"}, {"name": "7", "bone": "bone12", "attachment": "7"}, {"name": "13", "bone": "bone2", "attachment": "13"}, {"name": "1", "bone": "bone3", "attachment": "1"}, {"name": "2", "bone": "bone7", "attachment": "2"}, {"name": "3", "bone": "bone8", "attachment": "3"}, {"name": "4", "bone": "bone9", "attachment": "4"}, {"name": "5", "bone": "bone10", "attachment": "5"}, {"name": "bone16", "bone": "bone16", "attachment": "bone16"}, {"name": "Blur", "bone": "bone15", "attachment": "Blur"}], "skins": {"default": {"1": {"1": {"width": 175, "height": 38}}, "10": {"10": {"width": 357, "height": 242}}, "11": {"11": {"width": 265, "height": 408}}, "12": {"12": {"x": -9.04, "y": -61.75, "width": 265, "height": 408}}, "13": {"13": {"x": -0.6, "y": 0.24, "width": 290, "height": 436}}, "2": {"2": {"x": -4.31, "scaleX": 0.963, "width": 175, "height": 38}}, "3": {"3": {"width": 98, "height": 65}}, "4": {"4": {"width": 94, "height": 58}}, "5": {"5": {"width": 103, "height": 58}}, "6": {"6": {"width": 198, "height": 214}}, "7": {"7": {"width": 103, "height": 106}}, "8": {"8": {"width": 66, "height": 70}}, "9": {"9": {"width": 92, "height": 95}}, "Blur": {"Blur": {"width": 178, "height": 134}}, "bone16": {"bone16": {"type": "clipping", "end": "bone16", "vertexCount": 116, "vertices": [-332.97, 10.75, -334.61, 9.85, -335.55, 8.16, -335.59, -0.85, -334.63, -2.3, -333.13, -3.12, -326.43, -3.09, -326.54, -18.25, -325.23, -20.1, -323.23, -21.04, -313.75, -21.27, -311.72, -20.37, -310.81, -19.42, -310.13, -18.66, -309.87, -13.59, -310.12, -10.77, -311.19, -9.14, -312.29, -8.65, -312.3, -4.09, -307.77, -4.01, -307.56, -18.14, -307.07, -18.9, -306.25, -20.05, -305.39, -20.92, -304.14, -21.36, -294.68, -21.26, -293.06, -20.3, -291.92, -18.88, -290.99, -20.37, -289.54, -21.21, -278.52, -21.09, -276.99, -19.79, -274.93, -21.05, -264.53, -20.94, -263.19, -20, -262.35, -18.52, -160.93, -62.53, -161.04, -64.85, -156.81, -74.98, -162.43, -87.95, -162.21, -90.88, -160.93, -92.29, -159.68, -93.15, -150.06, -93.1, -148.38, -91.84, -145.67, -85.92, -143.06, -91.9, -141.82, -93.06, -132.01, -93.01, -130.61, -92.23, -128.86, -92.99, -119.04, -92.94, -116.85, -90.83, -115.54, -92.15, -113.44, -92.91, -90.57, -92.79, -89.23, -91.64, -88.17, -90.39, -88.18, -82.82, -89.1, -81.17, -90.35, -79.82, -90.55, -62.34, -91.74, -60.87, -93.08, -59.82, -103.53, -59.81, -105.24, -60.76, -105.99, -62.19, -107.02, -60.73, -108.79, -59.8, -115.97, -59.79, -118.34, -60.45, -118.46, -57.02, -118.76, -54.38, -119.96, -51.9, -122.45, -50.3, -127.59, -50.29, -130.3, -51.49, -131.57, -54.18, -132.16, -58.44, -132.3, -60.24, -141.9, -60.03, -144.16, -60.65, -145.75, -64.25, -147.12, -61.23, -148.64, -59.9, -157.84, -59.72, -159.72, -60.83, -160.91, -62.53, -262.33, -18.52, -262.31, -10.54, -262.9, -9.45, -263.84, -8.81, -264.03, 7.79, -264.33, 8.91, -265.04, 9.68, -265.72, 10.31, -266.69, 10.76, -275.69, 10.8, -277.45, 9.92, -279.06, 10.84, -286.2, 10.83, -285.81, 11.61, -285.92, 12.9, -291.78, 21.95, -293.47, 22.24, -295.73, 21.84, -297.38, 20.71, -298.53, 17.64, -298.81, 15.01, -298.98, 13.06, -296.35, 10.81, -299.44, 10.72, -301.39, 10.17, -303.42, 8.36, -304.45, 9.74, -305.55, 10.54], "color": "ce3a3a00"}}, "light": {"light": {"scaleX": 0.181, "scaleY": 0.2, "width": 103, "height": 114}}, "light2": {"light": {"scaleX": 0.181, "scaleY": 0.2, "width": 103, "height": 114}}, "light3": {"light": {"scaleX": 0.181, "scaleY": 0.2, "width": 103, "height": 114}}, "light4": {"light": {"scaleX": 0.181, "scaleY": 0.2, "width": 103, "height": 114}}, "light5": {"light": {"scaleX": 0.181, "scaleY": 0.2, "width": 103, "height": 114}}}}, "animations": {"AnimCoboder": {"slots": {"12": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 1.0667, "color": "ffffff91"}]}, "3": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3, "color": "ffffffff"}]}, "4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffff00"}, {"time": 0.4667, "color": "ffffffff"}]}, "5": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.8, "color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}, {"time": 1.3, "color": "ffffff00"}]}, "6": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00"}]}, "light": {"color": [{"time": 0.4667, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00"}]}, "light2": {"color": [{"time": 0.4667, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00"}]}, "light3": {"color": [{"time": 0.4667, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00"}]}, "light4": {"color": [{"time": 0.4667, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00"}]}, "light5": {"color": [{"time": 0.4667, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00"}]}}, "bones": {"bone12": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 10.2}, {"time": 1, "angle": 0}, {"time": 1.7667, "angle": -3.9}, {"time": 2.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 9.21}, {"time": 1, "x": 0, "y": 0}, {"time": 1.7667, "x": 0, "y": -9.28}, {"time": 2.3333, "x": 0, "y": 9.21}]}, "bone13": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4667, "angle": -18}, {"time": 1, "angle": 0}, {"time": 1.8, "angle": 24.6}, {"time": 2.3333, "angle": 0}], "translate": [{"time": 0, "x": 2.69, "y": -8.07}, {"time": 1, "x": 0, "y": 0}, {"time": 2.3333, "x": 2.69, "y": -8.07}]}, "bone14": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5667, "angle": -15.9}, {"time": 1, "angle": 0}, {"time": 1.7667, "angle": 26.4}, {"time": 2.3333, "angle": 0}], "translate": [{"time": 0, "x": -16.08, "y": -12.86}, {"time": 0.5667, "x": -6.97, "y": 8.9}, {"time": 1, "x": 0, "y": 0}, {"time": 1.7667, "x": 12.46, "y": -21.06}, {"time": 2.3333, "x": -16.08, "y": -12.86}]}, "bone6": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.0667, "angle": -174}, {"time": 2.3333, "angle": 13.8}]}, "bone4": {"translate": [{"time": 0, "x": -8.04, "y": 0}, {"time": 1, "x": -10.45, "y": 9.65}, {"time": 2.3333, "x": -8.04, "y": 0}], "scale": [{"time": 0, "x": 1.116, "y": 1.116}, {"time": 1, "x": 1.116, "y": 1.116}, {"time": 2.3333, "x": 1.116, "y": 1.116}]}, "bone15": {"translate": [{"time": 0.8333, "x": 0, "y": 0}, {"time": 1.9333, "x": 405.22, "y": 0}]}, "bone8": {"scale": [{"time": 0, "x": 2, "y": 2}, {"time": 0.3, "x": 1, "y": 1}]}, "bone9": {"scale": [{"time": 0, "x": 2, "y": 2, "curve": "stepped"}, {"time": 0.2, "x": 2, "y": 2}, {"time": 0.4667, "x": 1, "y": 1}]}, "bone10": {"scale": [{"time": 0.8, "x": 1, "y": 1}, {"time": 1, "x": 1.3, "y": 1.3}, {"time": 1.3, "x": 1, "y": 1}, {"time": 2.0667, "x": 0.4, "y": 0.4}]}, "bone": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.8, "x": 102.53, "y": -178.32}]}, "bone17": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.8, "x": -109.39, "y": -125.98}]}, "bone18": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.8, "x": -109.39, "y": 115.12}]}, "bone19": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.8, "x": 78.51, "y": 115.12}]}, "bone20": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.8, "x": -138.63, "y": -36.57}]}}}, "AnimKhongBoder": {"slots": {"12": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 1.0667, "color": "ffffff91"}]}, "3": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3, "color": "ffffffff"}]}, "4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffff00"}, {"time": 0.4667, "color": "ffffffff"}]}, "5": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.8, "color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}, {"time": 1.3, "color": "ffffff00"}]}, "6": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00"}]}, "light": {"color": [{"time": 0.4667, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00"}]}, "light2": {"color": [{"time": 0.4667, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00"}]}, "light3": {"color": [{"time": 0.4667, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00"}]}, "light4": {"color": [{"time": 0.4667, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00"}]}, "light5": {"color": [{"time": 0.4667, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00"}]}}, "bones": {"bone12": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 10.2}, {"time": 1, "angle": 0}, {"time": 1.7667, "angle": -3.9}, {"time": 2.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 9.21}, {"time": 1, "x": 0, "y": 0}, {"time": 1.7667, "x": 0, "y": -9.28}, {"time": 2.3333, "x": 0, "y": 9.21}]}, "bone13": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4667, "angle": -18}, {"time": 1, "angle": 0}, {"time": 1.8, "angle": 24.6}, {"time": 2.3333, "angle": 0}], "translate": [{"time": 0, "x": 2.69, "y": -8.07}, {"time": 1, "x": 0, "y": 0}, {"time": 2.3333, "x": 2.69, "y": -8.07}]}, "bone14": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5667, "angle": -15.9}, {"time": 1, "angle": 0}, {"time": 1.7667, "angle": 26.4}, {"time": 2.3333, "angle": 0}], "translate": [{"time": 0, "x": -16.08, "y": -12.86}, {"time": 0.5667, "x": -6.97, "y": 8.9}, {"time": 1, "x": 0, "y": 0}, {"time": 1.7667, "x": 12.46, "y": -21.06}, {"time": 2.3333, "x": -16.08, "y": -12.86}]}, "bone6": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.0667, "angle": -174}, {"time": 2.3333, "angle": 13.8}]}, "bone4": {"translate": [{"time": 0, "x": -8.04, "y": 0}, {"time": 1, "x": -10.45, "y": 9.65}, {"time": 2.3333, "x": -8.04, "y": 0}], "scale": [{"time": 0, "x": 1.116, "y": 1.116}, {"time": 1, "x": 1.116, "y": 1.116}, {"time": 2.3333, "x": 1.116, "y": 1.116}]}, "bone15": {"translate": [{"time": 0.8333, "x": 0, "y": 0}, {"time": 1.9333, "x": 405.22, "y": 0}]}, "bone8": {"scale": [{"time": 0, "x": 2, "y": 2}, {"time": 0.3, "x": 1, "y": 1}]}, "bone9": {"scale": [{"time": 0, "x": 2, "y": 2, "curve": "stepped"}, {"time": 0.2, "x": 2, "y": 2}, {"time": 0.4667, "x": 1, "y": 1}]}, "bone10": {"scale": [{"time": 0.8, "x": 1, "y": 1}, {"time": 1, "x": 1.3, "y": 1.3}, {"time": 1.3, "x": 1, "y": 1}, {"time": 2.0667, "x": 0.4, "y": 0.4}]}, "bone": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.8, "x": 102.53, "y": -178.32}]}, "bone17": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.8, "x": -109.39, "y": -125.98}]}, "bone18": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.8, "x": -109.39, "y": 115.12}]}, "bone19": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.8, "x": 78.51, "y": 115.12}]}, "bone20": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.8, "x": -138.63, "y": -36.57}]}}}}}