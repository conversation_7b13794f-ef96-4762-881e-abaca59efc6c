{"skeleton": {"hash": "IrdUqvZ7mx5hBwH7rqUgL2RlEnE", "spine": "3.7.93", "width": 166, "height": 222, "images": "", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root"}, {"name": "bone2", "parent": "root"}, {"name": "bone3", "parent": "root"}, {"name": "bone4", "parent": "root"}, {"name": "bone5", "parent": "root"}, {"name": "bone6", "parent": "root"}, {"name": "bone7", "parent": "root"}, {"name": "bone8", "parent": "root"}, {"name": "bone10", "parent": "root", "x": -6, "y": 77.5}, {"name": "Mask", "parent": "root", "x": 38, "y": 7.5}], "slots": [{"name": "8", "bone": "bone8", "attachment": "8"}, {"name": "7", "bone": "bone7", "attachment": "7"}, {"name": "6", "bone": "bone6", "attachment": "6"}, {"name": "5", "bone": "bone5", "attachment": "5"}, {"name": "4", "bone": "bone4", "attachment": "4"}, {"name": "3", "bone": "bone3", "attachment": "3"}, {"name": "2", "bone": "bone2", "attachment": "2"}, {"name": "1", "bone": "bone", "attachment": "1"}, {"name": "light", "bone": "bone10", "attachment": "light"}, {"name": "Mask", "bone": "Mask", "attachment": "Mask"}], "skins": {"default": {"1": {"1": {"width": 166, "height": 173}}, "2": {"2": {"width": 166, "height": 173}}, "3": {"3": {"width": 166, "height": 173}}, "4": {"4": {"width": 166, "height": 173}}, "5": {"5": {"width": 166, "height": 173}}, "6": {"6": {"width": 166, "height": 173}}, "7": {"7": {"width": 166, "height": 173}}, "8": {"8": {"width": 166, "height": 173}}, "Mask": {"Mask": {"type": "clipping", "end": "Mask", "vertexCount": 81, "vertices": [-57.64, 74.07, -48.83, 75.78, -40.32, 76.01, -31.62, 75.82, -24.03, 75.41, -19.25, 74.44, -19.16, 73.11, -13.47, 71.35, -5.59, 68.4, 3.55, 63.45, 8.67, 60.21, 12.56, 57.69, 20.66, 49.87, 22.78, 49.87, 28.27, 43.21, 34.03, 34.3, 37.33, 27.4, 41.12, 17.12, 40.06, 15.21, 42.27, 5, 43.08, -3.28, 42.81, -9.72, 42.69, -16.19, 41.68, -24.48, 40.42, -29.45, 40.69, -31.45, 40.8, -33.39, 39.13, -38.85, 37.48, -43.2, 32.86, -51.41, 27.86, -58.31, 21.82, -65.4, 19.6, -65.3, 15.59, -68.94, 11.12, -72.65, 5.92, -76.23, 0.92, -79.11, -8.66, -83.78, -19.55, -87.19, -21.24, -89.1, -28.7, -90.75, -34.1, -90.89, -39.11, -91.23, -44.69, -91.13, -49.55, -90.67, -54.68, -89.7, -59.08, -88.68, -59.83, -86.87, -68.96, -83.7, -78.66, -79.1, -88.27, -72.9, -98.42, -64.28, -101.18, -64.39, -104.65, -61.2, -107.81, -57.25, -109.93, -54.33, -112.8, -49.63, -116.49, -43.04, -118.87, -37.6, -120.24, -32.02, -118.23, -29.58, -120.04, -21.13, -120.81, -14.83, -121.06, -8.12, -121.08, -1.63, -120.73, 3.97, -120.07, 9.34, -118.44, 15.07, -119.39, 17.62, -115.72, 28.13, -111.92, 36.38, -107.34, 42.47, -100.31, 50.04, -97.98, 49.83, -91.87, 56.15, -86.19, 60.56, -80.66, 64, -75.08, 66.98, -70.91, 68.93, -64.27, 71.08, -59.21, 72.34], "color": "ce3a3a00"}}, "light": {"light": {"x": 2, "y": 1, "width": 103, "height": 114}}}}, "animations": {"animation": {"slots": {"1": {"color": [{"time": 0.0667, "color": "ffffffff"}, {"time": 0.1, "color": "ffffff00"}, {"time": 0.5333, "color": "ffffff00"}, {"time": 0.5667, "color": "ffffffff"}]}, "2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.0667, "color": "ffffff00"}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.1667, "color": "ffffff00"}]}, "3": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff"}, {"time": 0.2333, "color": "ffffff00"}]}, "4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffff00"}, {"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2667, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00"}]}, "5": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2667, "color": "ffffff00"}, {"time": 0.3, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00"}]}, "6": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.4333, "color": "ffffff00"}]}, "7": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00"}]}, "8": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4667, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00"}]}, "light": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.0667, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff"}, {"time": 0.4, "color": "ffffff00"}]}}, "bones": {"bone10": {"scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0667, "x": 0, "y": 0}, {"time": 0.1667, "x": 1, "y": 1}]}}}}}