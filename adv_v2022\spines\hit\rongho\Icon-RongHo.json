{"skeleton": {"hash": "hFje+vEbk2jD3GL8dLj1O/YQamY", "spine": "3.7.94", "width": 192, "height": 192, "images": "./Icon-RongHo/Icon-RongHo-P/", "audio": "C:/Users/<USER>/Desktop/Project_X/Cong-2/Icon-Cong-NoHu/Icon-RongHo"}, "bones": [{"name": "root"}, {"name": "bone21", "parent": "root", "length": 17.82, "x": 7.09, "y": -61.18}, {"name": "bone", "parent": "bone21", "length": 20.33, "x": -32.21, "y": 0.54}, {"name": "bone2", "parent": "root", "length": 39.87, "x": 63.68, "y": 55.33}, {"name": "bone3", "parent": "bone2", "length": 36.41, "rotation": -101.77, "x": -11.58, "y": -13.53}, {"name": "bone4", "parent": "bone3", "length": 42, "rotation": 7.31, "x": 36.99, "y": 0.12}, {"name": "bone13", "parent": "bone2", "length": 16.5, "rotation": 23.33, "x": -10.39, "y": 9.34}, {"name": "bone5", "parent": "bone13", "length": 53.51, "rotation": 154.12, "x": -0.35, "y": -0.82}, {"name": "bone6", "parent": "bone5", "length": 38.98, "rotation": 57.16, "x": 10.4, "y": 0.16}, {"name": "bone7", "parent": "bone5", "length": 16.16, "rotation": 162.11, "x": 8.54, "y": -11.51}, {"name": "bone8", "parent": "bone6", "length": 16.95, "rotation": 114.27, "x": 8.28, "y": -3.96}, {"name": "bone9", "parent": "bone6", "length": 16.55, "rotation": 104.35, "x": 22.4, "y": -2.3}, {"name": "bone10", "parent": "bone6", "length": 16.66, "rotation": 57.38, "x": 41.73, "y": -3.87}, {"name": "bone11", "parent": "bone5", "length": 23.95, "rotation": 165.23, "x": 6.42, "y": -17.26}, {"name": "bone12", "parent": "bone11", "length": 21.5, "rotation": -15.28, "x": 23.95}, {"name": "bone14", "parent": "bone13", "length": 14.58, "rotation": -56.7, "x": 0.19, "y": -3.32}, {"name": "bone15", "parent": "bone13", "length": 19.61, "rotation": -78.2, "x": -11.11, "y": -15.26}, {"name": "bone16", "parent": "bone13", "length": 18.91, "rotation": -67.06, "x": 6.17, "y": -12.69}, {"name": "bone17", "parent": "bone16", "length": 20.73, "rotation": -8.25, "x": 18.91}, {"name": "bone18", "parent": "bone13", "length": 19.41, "rotation": -84.02, "x": -7.89, "y": -22.8}, {"name": "bone19", "parent": "bone18", "length": 19.39, "rotation": -1.96, "x": 19.41}, {"name": "bone20", "parent": "bone13", "length": 15.75, "rotation": -67.57, "x": 16.66, "y": 1.87}, {"name": "bone22", "parent": "bone5", "length": 7.77, "rotation": 106.58, "x": 41.34, "y": -14.32}, {"name": "bone23", "parent": "bone2", "length": 9.39, "rotation": -175.39, "x": 27.54, "y": -25.71}, {"name": "bone24", "parent": "root", "length": 22.54, "x": -93.05, "y": 32.28}, {"name": "bone25", "parent": "bone24", "length": 26.05, "rotation": -16.39, "x": 1.72, "y": 23.76}, {"name": "bone26", "parent": "bone25", "length": 61.54, "rotation": 8.84, "x": 6.08, "y": 5.36}, {"name": "bone27", "parent": "bone25", "length": 46.53, "rotation": -34.81, "x": 7.2, "y": 4.16}, {"name": "bone28", "parent": "bone27", "length": 22.5, "rotation": -0.43, "x": 21.96, "y": 12.84}, {"name": "bone29", "parent": "bone28", "length": 19.07, "rotation": -12.46, "x": 22.5}, {"name": "bone30", "parent": "bone29", "length": 25.15, "rotation": 16.33, "x": 19.07}, {"name": "bone31", "parent": "bone26", "length": 19.12, "rotation": 95.35, "x": 61, "y": -1.55}, {"name": "bone32", "parent": "bone31", "length": 17, "rotation": -12.84, "x": 19.12}, {"name": "bone33", "parent": "bone26", "length": 6.79, "rotation": -108.09, "x": 19.97, "y": 11.54}, {"name": "bone34", "parent": "bone27", "length": 21.82, "rotation": -99.93, "x": 70.84, "y": -8.86}, {"name": "bone35", "parent": "bone34", "length": 20.07, "rotation": -8.88, "x": 21.82}, {"name": "bone36", "parent": "bone25", "length": 21.66, "rotation": -119.07, "x": 26.68, "y": -3.9}, {"name": "bone37", "parent": "bone36", "length": 19.03, "rotation": 10.95, "x": 21.83, "y": -0.17}, {"name": "bone38", "parent": "bone26", "length": 14.07, "rotation": 46.92, "x": 78.12, "y": 2.33}, {"name": "bone39", "parent": "bone38", "length": 15.5, "rotation": -9.11, "x": 14.07}, {"name": "bone40", "parent": "bone21", "length": 20.33, "x": 40.79, "y": 0.54}, {"name": "fx-1", "parent": "root", "length": 20.68, "x": 9.73, "y": -97.93, "scaleX": 1.671, "scaleY": 1.263}], "slots": [{"name": "1 1", "bone": "root", "attachment": "1 1"}, {"name": "2 1", "bone": "bone3", "attachment": "2 1"}, {"name": "2 2", "bone": "bone16", "attachment": "2 2"}, {"name": "2 3", "bone": "bone20", "attachment": "2 3"}, {"name": "2 4", "bone": "bone18", "attachment": "2 4"}, {"name": "2 5", "bone": "bone23", "attachment": "2 5"}, {"name": "2 6", "bone": "bone11", "attachment": "2 6"}, {"name": "2 7", "bone": "bone14", "attachment": "2 7"}, {"name": "2 8", "bone": "bone15", "attachment": "2 8"}, {"name": "2 9", "bone": "bone10", "attachment": "2 9"}, {"name": "2 10", "bone": "bone9", "attachment": "2 10"}, {"name": "2 11", "bone": "bone8", "attachment": "2 11"}, {"name": "2 12", "bone": "bone7", "attachment": "2 12"}, {"name": "2 13", "bone": "bone6", "attachment": "2 13"}, {"name": "2 14", "bone": "bone5", "attachment": "2 14"}, {"name": "2 15", "bone": "bone22"}, {"name": "3 1", "bone": "bone36", "attachment": "3 1"}, {"name": "3 2", "bone": "bone34", "attachment": "3 2"}, {"name": "3 3", "bone": "bone27", "attachment": "3 3"}, {"name": "3 4", "bone": "bone28", "attachment": "3 4"}, {"name": "3 5", "bone": "bone38", "attachment": "3 5"}, {"name": "3 6", "bone": "bone26", "attachment": "3 6"}, {"name": "3 7", "bone": "bone33"}, {"name": "3 8", "bone": "bone31", "attachment": "3 8"}, {"name": "fx-1", "bone": "fx-1", "color": "ffffff3c"}, {"name": "4 1", "bone": "bone", "attachment": "4 1"}, {"name": "4 2", "bone": "bone40", "attachment": "4 2"}], "skins": {"default": {"1 1": {"1 1": {"x": 0.04, "y": 0.09, "width": 192, "height": 192}}, "2 1": {"2 1": {"type": "mesh", "uvs": [0.94693, 0.36756, 0.93786, 0.43042, 0.94952, 0.50322, 0.9737, 0.65419, 0.93503, 0.93343, 0.55723, 0.96448, 0.12515, 1, 0, 0.84641, 0.21714, 0.4733, 0.25864, 0.41052, 0.27275, 0.3429, 0.34428, 0, 0.66689, 0, 1, 0, 0.60275, 0.35497, 0.58697, 0.42014, 0.56759, 0.48762, 0.46013, 0.75558], "triangles": [1, 14, 0, 15, 14, 1, 16, 9, 15, 8, 9, 16, 15, 1, 2, 16, 15, 2, 17, 8, 16, 3, 17, 16, 3, 16, 2, 7, 8, 17, 3, 5, 17, 4, 5, 3, 6, 7, 17, 6, 17, 5, 14, 11, 12, 10, 11, 14, 0, 12, 13, 14, 12, 0, 15, 10, 14, 9, 10, 15], "vertices": [2, 4, 25.21, 24.19, 0.36702, 5, -8.62, 25.37, 0.63298, 2, 4, 32.35, 25.08, 0.13884, 5, -1.43, 25.35, 0.86116, 2, 4, 40.32, 27.51, 0.01653, 5, 6.79, 26.74, 0.98347, 1, 5, 23.83, 29.62, 1, 1, 5, 55.76, 29.63, 1, 1, 5, 61.17, 5.8, 1, 1, 5, 67.35, -21.46, 1, 2, 4, 91.02, -24.01, 4e-05, 5, 50.52, -30.8, 0.99996, 2, 4, 46.54, -19.08, 0.48031, 5, 7.03, -20.26, 0.51969, 2, 4, 38.99, -17.94, 0.79173, 5, -0.31, -18.17, 0.20827, 2, 4, 31.26, -18.62, 0.97775, 5, -8.06, -17.87, 0.02225, 1, 4, -7.94, -22.12, 1, 1, 4, -12.15, -1.9, 1, 1, 4, -16.5, 18.97, 1, 2, 4, 28.3, 2.33, 0.86575, 5, -8.33, 3.3, 0.13425, 2, 4, 35.78, 2.86, 0.06176, 5, -0.85, 2.87, 0.93824, 1, 5, 6.92, 2.23, 1, 2, 4, 74.87, 2.71, 0.00017, 5, 37.91, -2.25, 0.99983], "hull": 14, "edges": [22, 20, 20, 18, 2, 0, 0, 26, 2, 4, 16, 18, 4, 6, 6, 8, 12, 14, 14, 16, 22, 24, 24, 26, 0, 28, 28, 20, 24, 28, 2, 30, 30, 18, 28, 30, 4, 32, 32, 16, 30, 32, 6, 34, 34, 14, 32, 34, 8, 10, 10, 12, 34, 10], "width": 64, "height": 114}}, "2 10": {"2 10": {"x": 17.71, "y": -2.9, "rotation": 21.04, "width": 41, "height": 36}}, "2 11": {"2 11": {"x": 10.84, "y": -2.49, "rotation": 11.11, "width": 32, "height": 28}}, "2 12": {"2 12": {"x": 11.04, "y": -0.23, "rotation": 20.43, "width": 29, "height": 25}}, "2 13": {"2 13": {"x": 30.81, "y": -7.99, "rotation": 125.39, "width": 43, "height": 49}}, "2 14": {"2 14": {"type": "mesh", "uvs": [0.96873, 0.29319, 0.92984, 0.57236, 0.74624, 0.88692, 0.622, 0.89772, 0.51784, 0.90678, 0.44504, 0.9292, 0.35554, 0.95676, 0.21512, 1, 0.0414, 0.89991, 0, 0.71024, 0.01905, 0.48017, 0.12904, 0.31981, 0.24327, 0.10312, 0.37895, 0, 0.67271, 0, 1, 0, 0.1327, 0.63625, 0.22414, 0.45796, 0.40665, 0.52245, 0.32352, 0.60983, 0.75385, 0.5375, 0.70808, 0.25737, 0.58493, 0.50817, 0.53618, 0.27901, 0.36018, 0.34501, 0.25489, 0.78611], "triangles": [21, 14, 15, 23, 13, 14, 23, 14, 21, 12, 13, 23, 0, 21, 15, 24, 11, 12, 23, 24, 12, 17, 11, 24, 10, 11, 17, 22, 23, 21, 18, 24, 23, 22, 18, 23, 17, 24, 18, 20, 21, 0, 22, 21, 20, 1, 20, 0, 19, 17, 18, 16, 10, 17, 16, 17, 19, 9, 10, 16, 25, 16, 19, 20, 3, 22, 2, 20, 1, 2, 3, 20, 16, 25, 8, 16, 8, 9, 4, 19, 18, 18, 22, 4, 3, 4, 22, 5, 25, 19, 4, 5, 19, 6, 25, 5, 7, 8, 25, 7, 25, 6], "vertices": [-1.66, -13.98, 0.72, 1.78, 14.43, 20.02, 24.2, 21.06, 32.4, 21.93, 38.09, 23.44, 45.09, 25.3, 56.06, 28.21, 70.02, 23.22, 73.76, 12.75, 72.83, -0.18, 64.55, -9.54, 56.07, -22.06, 45.62, -28.31, 22.43, -29.34, -3.4, -30.49, 63.47, 8.15, 56.7, -2.15, 42.13, 0.82, 48.48, 6, 14.69, 0.45, 19, -15.07, 28.1, -0.6, 32.52, -13.25, 46.24, -8.94, 53.45, 16.1], "hull": 16, "edges": [16, 18, 16, 14, 16, 32, 32, 18, 34, 32, 34, 22, 36, 8, 34, 38, 36, 38, 40, 4, 2, 40, 4, 2, 42, 0, 30, 0, 0, 2, 40, 42, 28, 30, 42, 28, 4, 6, 6, 8, 44, 36, 6, 44, 44, 46, 36, 48, 48, 24, 22, 24, 34, 48, 48, 46, 46, 26, 24, 26, 26, 28, 32, 50, 50, 14, 50, 38, 18, 20, 32, 20, 20, 22, 8, 10, 38, 10, 10, 12, 12, 14, 50, 12, 46, 42, 44, 40], "width": 79, "height": 56}}, "2 15": {"2 15": {"x": 3.94, "y": -0.04, "rotation": 75.96, "width": 19, "height": 12}}, "2 2": {"2 2": {"type": "mesh", "uvs": [0.74277, 0.21159, 0.84454, 0.25937, 0.90238, 0.33763, 1, 0.46971, 1, 1, 0.90933, 1, 0.36414, 0.69638, 0.27673, 0.64769, 0.21331, 0.59154, 0, 0.40268, 0, 0, 0.29198, 0], "triangles": [9, 10, 11, 8, 9, 11, 8, 11, 0, 7, 8, 0, 7, 0, 1, 6, 7, 1, 6, 1, 2, 3, 6, 2, 5, 6, 3, 5, 3, 4], "vertices": [2, 18, -7.41, 14.52, 0.0946, 17, 13.65, 15.44, 0.9054, 2, 18, -2.8, 15.69, 0.23383, 17, 18.39, 15.93, 0.76617, 2, 18, 2.28, 14.44, 0.46459, 17, 23.24, 13.96, 0.53541, 2, 18, 10.85, 12.32, 0.87295, 17, 31.42, 10.64, 0.12705, 1, 18, 36.34, -7.6, 1, 1, 18, 34.27, -10.24, 1, 2, 18, 7.26, -14.73, 0.6602, 17, 23.97, -15.62, 0.3398, 2, 18, 2.92, -15.45, 0.4558, 17, 19.58, -15.71, 0.5442, 2, 18, -1.22, -15.19, 0.26334, 17, 15.52, -14.85, 0.73666, 2, 18, -15.16, -14.31, 0.00035, 17, 1.85, -11.98, 0.99965, 1, 17, -15.13, 5.77, 1, 1, 17, -7.32, 13.23, 1], "hull": 12, "edges": [18, 20, 8, 10, 20, 22, 8, 6, 2, 14, 14, 16, 16, 18, 2, 0, 0, 22, 16, 0, 10, 12, 12, 14, 2, 4, 4, 6, 12, 4], "width": 37, "height": 61}}, "2 3": {"2 3": {"x": 18.07, "y": -2.51, "rotation": 44.24, "width": 30, "height": 37}}, "2 4": {"2 4": {"type": "mesh", "uvs": [0.62184, 0.1905, 0.70469, 0.26172, 0.76873, 0.38006, 1, 0.80743, 1, 1, 0.39571, 1, 0.16936, 0.63864, 0.1386, 0.58954, 0.08813, 0.54951, 0, 0.47961, 0, 0, 0.40022, 0], "triangles": [7, 0, 1, 6, 7, 1, 6, 1, 2, 5, 6, 2, 5, 2, 3, 5, 3, 4, 9, 10, 11, 8, 9, 11, 0, 8, 11, 7, 8, 0], "vertices": [2, 19, 12.34, 16.23, 0.6363, 20, -7.63, 15.98, 0.3637, 2, 19, 17.6, 17.74, 0.40454, 20, -2.42, 17.67, 0.59546, 2, 19, 24.64, 17.24, 0.14838, 20, 4.64, 17.41, 0.85162, 1, 20, 30.13, 16.46, 1, 1, 20, 39.36, 11.68, 1, 1, 20, 26.32, -13.55, 1, 2, 19, 23.03, -14.16, 0.2139, 20, 4.1, -14.03, 0.7861, 2, 19, 20.01, -14.12, 0.39224, 20, 1.08, -14.1, 0.60776, 2, 19, 16.96, -15.13, 0.58773, 20, -1.93, -15.21, 0.41227, 2, 19, 11.64, -16.9, 0.78194, 20, -7.19, -17.15, 0.21806, 1, 19, -10.94, -4.22, 1, 2, 19, -1.73, 12.18, 0.9836, 20, -21.55, 11.45, 0.0164], "hull": 12, "edges": [20, 22, 8, 6, 2, 14, 18, 20, 8, 10, 14, 16, 16, 18, 2, 0, 0, 22, 16, 0, 10, 12, 12, 14, 2, 4, 4, 6, 12, 4], "width": 47, "height": 54}}, "2 5": {"2 5": {"x": 7.37, "y": 1.95, "rotation": 175.39, "width": 20, "height": 36}}, "2 6": {"2 6": {"type": "mesh", "uvs": [0.56505, 0.09337, 0.67151, 0.15734, 0.78352, 0.25175, 1, 0.63952, 1, 1, 0.49452, 0.67531, 0.42605, 0.63133, 0.35231, 0.61926, 0.11748, 0.58081, 0, 0, 0.40966, 0], "triangles": [8, 9, 10, 7, 8, 10, 0, 7, 10, 6, 7, 0, 1, 6, 0, 2, 6, 1, 5, 6, 2, 3, 5, 2, 5, 3, 4], "vertices": [2, 14, -7.35, 13.17, 0.00619, 13, 20.34, 14.64, 0.99381, 2, 14, -0.89, 13.35, 0.14347, 13, 26.61, 13.12, 0.85653, 2, 14, 6.66, 12.35, 0.54823, 13, 33.63, 10.16, 0.45177, 1, 14, 27, 1.43, 1, 1, 14, 37.1, -14.37, 1, 2, 14, 5.86, -14.3, 0.67438, 13, 25.84, -15.34, 0.32562, 2, 14, 1.63, -14.29, 0.38419, 13, 21.76, -14.21, 0.61581, 2, 14, -1.94, -15.83, 0.1597, 13, 17.91, -14.76, 0.8403, 2, 14, -13.3, -20.72, 4e-05, 13, 5.66, -16.48, 0.99996, 1, 13, -9.16, 10.54, 1, 1, 13, 11.18, 16.87, 1], "hull": 11, "edges": [18, 20, 8, 10, 10, 12, 8, 6, 16, 18, 0, 20, 12, 14, 14, 16, 0, 14, 0, 2, 2, 12, 10, 4, 4, 6, 2, 4], "width": 52, "height": 52}}, "2 7": {"2 7": {"x": 12.34, "y": -0.99, "rotation": 33.37, "width": 29, "height": 38}}, "2 8": {"2 8": {"x": 13.85, "y": 1.14, "rotation": 54.87, "width": 28, "height": 49}}, "2 9": {"2 9": {"x": 10.96, "y": 2.55, "rotation": 68.01, "width": 42, "height": 34}}, "3 1": {"3 1": {"x": 19.25, "y": 8.68, "rotation": 135.46, "width": 43, "height": 58}}, "3 2": {"3 2": {"type": "mesh", "uvs": [0.53512, 0.08954, 0.77417, 0.31065, 1, 0.51953, 1, 0.83974, 0.79957, 0.95145, 0.52435, 1, 0.15759, 0.8484, 0, 0.64066, 0.05656, 0.3555, 0.1923, 0.08754, 0.53701, 0.47471, 0.34708, 0.62548], "triangles": [11, 8, 10, 7, 8, 11, 6, 7, 11, 6, 11, 5, 10, 0, 1, 9, 0, 10, 10, 8, 9, 10, 1, 2, 3, 10, 2, 4, 10, 3, 11, 10, 4, 5, 11, 4], "vertices": [2, 34, -0.24, -20.75, 0.91123, 35, -18.59, -23.91, 0.08877, 1, 34, -7.86, -0.9, 1, 1, 34, -15.05, 17.86, 1, 2, 34, -5.47, 35.24, 0.99835, 35, -32.4, 30.6, 0.00165, 2, 34, 9.81, 34.73, 0.94732, 35, -17.23, 32.46, 0.05268, 2, 34, 27.65, 28.33, 0.68198, 35, 1.39, 28.89, 0.31802, 2, 34, 44.96, 8.06, 0.04309, 35, 21.61, 11.53, 0.95691, 1, 35, 27.28, -4.23, 1, 2, 34, 36.22, -22.02, 0.11425, 35, 17.63, -19.53, 0.88575, 2, 34, 20.12, -32.12, 0.4859, 35, 3.27, -31.99, 0.5141, 1, 34, 11.18, 0.22, 1, 2, 34, 27, 2.17, 0.11266, 35, 4.78, 2.95, 0.88734], "hull": 10, "edges": [0, 18, 18, 20, 4, 6, 20, 6, 0, 2, 2, 4, 2, 20, 20, 22, 6, 8, 22, 8, 8, 10, 22, 16, 16, 18, 22, 12, 12, 14, 14, 16, 10, 12], "width": 68, "height": 62}}, "3 3": {"3 3": {"x": 41.42, "y": -3.42, "rotation": 51.2, "width": 68, "height": 89}}, "3 4": {"3 4": {"type": "mesh", "uvs": [0.44673, 0.18651, 0.50678, 0.25135, 0.53433, 0.32874, 0.57473, 0.44219, 0.58838, 0.48053, 0.66319, 0.48016, 1, 0.4785, 1, 1, 0.75158, 1, 0.55059, 0.71379, 0.38104, 0.69862, 0.30287, 0.60804, 0.30942, 0.41001, 0.31177, 0.33375, 0.29212, 0.27679, 0, 0.0946, 0, 0, 0.27398, 0], "triangles": [13, 0, 1, 13, 14, 0, 15, 17, 14, 14, 17, 0, 15, 16, 17, 10, 11, 4, 11, 3, 4, 11, 12, 3, 12, 2, 3, 12, 13, 2, 13, 1, 2, 8, 6, 7, 8, 9, 6, 6, 9, 5, 9, 4, 5, 9, 10, 4], "vertices": [2, 29, -6.22, 6.76, 0.0509, 28, 17.89, 7.94, 0.9491, 2, 29, -0.5, 7.99, 0.47691, 28, 23.73, 7.91, 0.52309, 2, 29, 5.16, 7.07, 0.93649, 28, 29.07, 5.79, 0.06351, 2, 30, -3.76, 7.08, 0.04551, 29, 13.47, 5.73, 0.95449, 2, 30, -1.19, 5.85, 0.28363, 29, 16.27, 5.28, 0.71637, 2, 30, 1.8, 9.19, 0.77962, 29, 18.21, 9.33, 0.22038, 1, 30, 15.3, 24.23, 1, 1, 30, 42.71, -0.65, 1, 1, 30, 32.7, -11.69, 1, 2, 30, 9.54, -6.96, 0.98268, 29, 30.18, -3.99, 0.01732, 2, 30, 1.91, -13.77, 0.58621, 29, 24.77, -12.67, 0.41379, 2, 30, -6.01, -12.92, 0.24493, 29, 16.93, -14.08, 0.75507, 2, 29, 4.46, -7.59, 0.9452, 28, 25.21, -8.37, 0.0548, 2, 29, -0.35, -5.09, 0.43132, 28, 21.06, -4.9, 0.56868, 2, 29, -4.5, -4.39, 0.01117, 28, 17.15, -3.31, 0.98883, 1, 28, -3.87, -9.03, 1, 1, 28, -9.13, -4.86, 1, 1, 28, 1.07, 8.03, 1], "hull": 18, "edges": [32, 34, 14, 12, 14, 16, 12, 10, 10, 8, 6, 8, 2, 4, 4, 6, 34, 0, 0, 2, 30, 32, 28, 0, 30, 28, 26, 2, 28, 26, 24, 4, 26, 24, 22, 6, 24, 22, 20, 8, 22, 20, 18, 10, 20, 18, 18, 16], "width": 60, "height": 71}}, "3 5": {"3 5": {"x": 16.3, "y": 2.88, "rotation": -39.37, "width": 31, "height": 32}}, "3 6": {"3 6": {"x": 41.41, "y": 3.58, "rotation": 7.55, "width": 103, "height": 73}}, "3 7": {"3 7": {"x": 3.22, "y": -0.44, "rotation": 115.64, "width": 15, "height": 13}}, "3 8": {"3 8": {"type": "mesh", "uvs": [0.87767, 0.42522, 0.69638, 0.57596, 0.4205, 0.6974, 0.82249, 1, 0, 1, 0, 0.67507, 0.23526, 0.49221, 0.34196, 0.36502, 0.44808, 0, 1, 0], "triangles": [0, 8, 9, 7, 8, 0, 1, 7, 0, 6, 7, 1, 2, 6, 1, 5, 6, 2, 4, 5, 2, 4, 2, 3], "vertices": [1, 32, 6.55, -6.46, 1, 2, 31, 16.72, -4.95, 0.43466, 32, -1.24, -5.36, 0.56534, 2, 31, 10.72, -0.49, 0.99987, 32, -8.09, -2.35, 0.00013, 1, 31, -3.53, -7.88, 1, 1, 31, -4.07, 6.1, 1, 1, 31, 11.51, 6.69, 1, 2, 31, 20.44, 3.04, 0.38925, 32, 0.61, 3.25, 0.61075, 1, 32, 6.97, 3.08, 1, 1, 32, 24.36, 5.89, 1, 1, 32, 26.8, -3.17, 1], "hull": 10, "edges": [16, 18, 6, 8, 6, 4, 4, 2, 2, 0, 18, 0, 2, 12, 16, 14, 0, 14, 12, 14, 8, 10, 4, 10, 10, 12], "width": 17, "height": 48}}, "4 1": {"4 1": {"x": -0.34, "y": -2.28, "width": 105, "height": 58}}, "4 2": {"4 2": {"x": 4.65, "y": 2.22, "width": 67, "height": 51}}, "fx-1": {"fx-14-2/fx-14-2_0000_16": {"x": -9.73, "y": 76.06, "width": 128, "height": 161}, "fx-14-2/fx-14-2_0001_15": {"x": -9.73, "y": 76.06, "width": 128, "height": 161}, "fx-14-2/fx-14-2_0002_14": {"x": -9.73, "y": 76.06, "width": 128, "height": 161}, "fx-14-2/fx-14-2_0003_13": {"x": -9.73, "y": 76.06, "width": 128, "height": 161}, "fx-14-2/fx-14-2_0004_12": {"x": -9.73, "y": 76.06, "width": 128, "height": 161}, "fx-14-2/fx-14-2_0005_11": {"x": -9.73, "y": 76.06, "width": 128, "height": 161}, "fx-14-2/fx-14-2_0006_10": {"x": -9.73, "y": 76.06, "width": 128, "height": 161}, "fx-14-2/fx-14-2_0007_9": {"x": -9.73, "y": 76.06, "width": 128, "height": 161}, "fx-14-2/fx-14-2_0008_8": {"x": -9.73, "y": 76.06, "width": 128, "height": 161}, "fx-14-2/fx-14-2_0009_7": {"x": -9.73, "y": 76.06, "width": 128, "height": 161}, "fx-14-2/fx-14-2_0010_6": {"x": -9.73, "y": 76.06, "width": 128, "height": 161}, "fx-14-2/fx-14-2_0011_5": {"x": -9.73, "y": 76.06, "width": 128, "height": 161}, "fx-14-2/fx-14-2_0012_4": {"x": -9.73, "y": 76.06, "width": 128, "height": 161}, "fx-14-2/fx-14-2_0013_3": {"x": -9.73, "y": 76.06, "width": 128, "height": 161}, "fx-14-2/fx-14-2_0014_2": {"x": -9.73, "y": 76.06, "width": 128, "height": 161}, "fx-14-2/fx-14-2_0015_1": {"x": -9.73, "y": 76.06, "width": 128, "height": 161}}}}, "animations": {"Idle": {"slots": {"2 15": {"attachment": [{"time": 0, "name": "2 15"}]}, "3 7": {"attachment": [{"time": 0, "name": "3 7"}]}, "4 1": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.0333, "color": "ffffff21"}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 15.9, "color": "ffffffff"}, {"time": 15.9333, "color": "ffffff00"}]}, "4 2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1, "color": "ffffff00"}, {"time": 1.2, "color": "ffffffff", "curve": "stepped"}, {"time": 15.9, "color": "ffffffff"}, {"time": 15.9333, "color": "ffffff00"}]}, "fx-1": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 1.0667, "color": "ffffff18"}, {"time": 3.2333, "color": "ffffff45"}, {"time": 6.1333, "color": "ffffff18", "curve": "stepped"}, {"time": 10.4, "color": "ffffff18"}, {"time": 12.5, "color": "ffffff26"}, {"time": 14.7, "color": "ffffff18"}, {"time": 16, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "fx-14-2/fx-14-2_0015_1"}, {"time": 0.0667, "name": "fx-14-2/fx-14-2_0014_2"}, {"time": 0.1333, "name": "fx-14-2/fx-14-2_0013_3"}, {"time": 0.2, "name": "fx-14-2/fx-14-2_0012_4"}, {"time": 0.2667, "name": "fx-14-2/fx-14-2_0011_5"}, {"time": 0.3333, "name": "fx-14-2/fx-14-2_0010_6"}, {"time": 0.4, "name": "fx-14-2/fx-14-2_0009_7"}, {"time": 0.4667, "name": "fx-14-2/fx-14-2_0008_8"}, {"time": 0.5333, "name": "fx-14-2/fx-14-2_0007_9"}, {"time": 0.6, "name": "fx-14-2/fx-14-2_0006_10"}, {"time": 0.6667, "name": "fx-14-2/fx-14-2_0005_11"}, {"time": 0.7333, "name": "fx-14-2/fx-14-2_0004_12"}, {"time": 0.8, "name": "fx-14-2/fx-14-2_0003_13"}, {"time": 0.8667, "name": "fx-14-2/fx-14-2_0002_14"}, {"time": 0.9333, "name": "fx-14-2/fx-14-2_0001_15"}, {"time": 1, "name": "fx-14-2/fx-14-2_0000_16"}, {"time": 1.0667, "name": "fx-14-2/fx-14-2_0015_1"}, {"time": 1.1333, "name": "fx-14-2/fx-14-2_0014_2"}, {"time": 1.2, "name": "fx-14-2/fx-14-2_0013_3"}, {"time": 1.2667, "name": "fx-14-2/fx-14-2_0012_4"}, {"time": 1.3333, "name": "fx-14-2/fx-14-2_0011_5"}, {"time": 1.4, "name": "fx-14-2/fx-14-2_0010_6"}, {"time": 1.4667, "name": "fx-14-2/fx-14-2_0009_7"}, {"time": 1.5333, "name": "fx-14-2/fx-14-2_0008_8"}, {"time": 1.6, "name": "fx-14-2/fx-14-2_0007_9"}, {"time": 1.6667, "name": "fx-14-2/fx-14-2_0006_10"}, {"time": 1.7333, "name": "fx-14-2/fx-14-2_0005_11"}, {"time": 1.8, "name": "fx-14-2/fx-14-2_0004_12"}, {"time": 1.8667, "name": "fx-14-2/fx-14-2_0003_13"}, {"time": 1.9333, "name": "fx-14-2/fx-14-2_0002_14"}, {"time": 2, "name": "fx-14-2/fx-14-2_0001_15"}, {"time": 2.0667, "name": "fx-14-2/fx-14-2_0000_16"}, {"time": 2.1333, "name": "fx-14-2/fx-14-2_0015_1"}, {"time": 2.2, "name": "fx-14-2/fx-14-2_0014_2"}, {"time": 2.2667, "name": "fx-14-2/fx-14-2_0013_3"}, {"time": 2.3333, "name": "fx-14-2/fx-14-2_0012_4"}, {"time": 2.4, "name": "fx-14-2/fx-14-2_0011_5"}, {"time": 2.4667, "name": "fx-14-2/fx-14-2_0010_6"}, {"time": 2.5333, "name": "fx-14-2/fx-14-2_0009_7"}, {"time": 2.6, "name": "fx-14-2/fx-14-2_0008_8"}, {"time": 2.6667, "name": "fx-14-2/fx-14-2_0007_9"}, {"time": 2.7333, "name": "fx-14-2/fx-14-2_0006_10"}, {"time": 2.8, "name": "fx-14-2/fx-14-2_0005_11"}, {"time": 2.8667, "name": "fx-14-2/fx-14-2_0004_12"}, {"time": 2.9333, "name": "fx-14-2/fx-14-2_0003_13"}, {"time": 3, "name": "fx-14-2/fx-14-2_0002_14"}, {"time": 3.0667, "name": "fx-14-2/fx-14-2_0001_15"}, {"time": 3.1333, "name": "fx-14-2/fx-14-2_0000_16"}, {"time": 3.2, "name": "fx-14-2/fx-14-2_0015_1"}, {"time": 3.2667, "name": "fx-14-2/fx-14-2_0014_2"}, {"time": 3.3333, "name": "fx-14-2/fx-14-2_0013_3"}, {"time": 3.4, "name": "fx-14-2/fx-14-2_0012_4"}, {"time": 3.4667, "name": "fx-14-2/fx-14-2_0011_5"}, {"time": 3.5333, "name": "fx-14-2/fx-14-2_0010_6"}, {"time": 3.6, "name": "fx-14-2/fx-14-2_0009_7"}, {"time": 3.6667, "name": "fx-14-2/fx-14-2_0008_8"}, {"time": 3.7333, "name": "fx-14-2/fx-14-2_0007_9"}, {"time": 3.8, "name": "fx-14-2/fx-14-2_0006_10"}, {"time": 3.8667, "name": "fx-14-2/fx-14-2_0005_11"}, {"time": 3.9333, "name": "fx-14-2/fx-14-2_0004_12"}, {"time": 4, "name": "fx-14-2/fx-14-2_0003_13"}, {"time": 4.0667, "name": "fx-14-2/fx-14-2_0002_14"}, {"time": 4.1333, "name": "fx-14-2/fx-14-2_0001_15"}, {"time": 4.2, "name": "fx-14-2/fx-14-2_0000_16"}, {"time": 4.2667, "name": "fx-14-2/fx-14-2_0015_1"}, {"time": 4.3333, "name": "fx-14-2/fx-14-2_0014_2"}, {"time": 4.4, "name": "fx-14-2/fx-14-2_0013_3"}, {"time": 4.4667, "name": "fx-14-2/fx-14-2_0012_4"}, {"time": 4.5333, "name": "fx-14-2/fx-14-2_0011_5"}, {"time": 4.6, "name": "fx-14-2/fx-14-2_0010_6"}, {"time": 4.6667, "name": "fx-14-2/fx-14-2_0009_7"}, {"time": 4.7333, "name": "fx-14-2/fx-14-2_0008_8"}, {"time": 4.8, "name": "fx-14-2/fx-14-2_0007_9"}, {"time": 4.8667, "name": "fx-14-2/fx-14-2_0006_10"}, {"time": 4.9333, "name": "fx-14-2/fx-14-2_0005_11"}, {"time": 5, "name": "fx-14-2/fx-14-2_0004_12"}, {"time": 5.0667, "name": "fx-14-2/fx-14-2_0003_13"}, {"time": 5.1333, "name": "fx-14-2/fx-14-2_0002_14"}, {"time": 5.2, "name": "fx-14-2/fx-14-2_0001_15"}, {"time": 5.2667, "name": "fx-14-2/fx-14-2_0000_16"}, {"time": 5.3333, "name": "fx-14-2/fx-14-2_0015_1"}, {"time": 5.4, "name": "fx-14-2/fx-14-2_0014_2"}, {"time": 5.4667, "name": "fx-14-2/fx-14-2_0013_3"}, {"time": 5.5333, "name": "fx-14-2/fx-14-2_0012_4"}, {"time": 5.6, "name": "fx-14-2/fx-14-2_0011_5"}, {"time": 5.6667, "name": "fx-14-2/fx-14-2_0010_6"}, {"time": 5.7333, "name": "fx-14-2/fx-14-2_0009_7"}, {"time": 5.8, "name": "fx-14-2/fx-14-2_0008_8"}, {"time": 5.8667, "name": "fx-14-2/fx-14-2_0007_9"}, {"time": 5.9333, "name": "fx-14-2/fx-14-2_0006_10"}, {"time": 6, "name": "fx-14-2/fx-14-2_0005_11"}, {"time": 6.0667, "name": "fx-14-2/fx-14-2_0004_12"}, {"time": 6.1333, "name": "fx-14-2/fx-14-2_0003_13"}, {"time": 6.2, "name": "fx-14-2/fx-14-2_0002_14"}, {"time": 6.2667, "name": "fx-14-2/fx-14-2_0001_15"}, {"time": 6.3333, "name": "fx-14-2/fx-14-2_0000_16"}, {"time": 6.4, "name": "fx-14-2/fx-14-2_0015_1"}, {"time": 6.4667, "name": "fx-14-2/fx-14-2_0014_2"}, {"time": 6.5333, "name": "fx-14-2/fx-14-2_0013_3"}, {"time": 6.6, "name": "fx-14-2/fx-14-2_0012_4"}, {"time": 6.6667, "name": "fx-14-2/fx-14-2_0011_5"}, {"time": 6.7333, "name": "fx-14-2/fx-14-2_0010_6"}, {"time": 6.8, "name": "fx-14-2/fx-14-2_0009_7"}, {"time": 6.8667, "name": "fx-14-2/fx-14-2_0008_8"}, {"time": 6.9333, "name": "fx-14-2/fx-14-2_0007_9"}, {"time": 7, "name": "fx-14-2/fx-14-2_0006_10"}, {"time": 7.0667, "name": "fx-14-2/fx-14-2_0005_11"}, {"time": 7.1333, "name": "fx-14-2/fx-14-2_0004_12"}, {"time": 7.2, "name": "fx-14-2/fx-14-2_0003_13"}, {"time": 7.2667, "name": "fx-14-2/fx-14-2_0002_14"}, {"time": 7.3333, "name": "fx-14-2/fx-14-2_0001_15"}, {"time": 7.4, "name": "fx-14-2/fx-14-2_0000_16"}, {"time": 7.4667, "name": "fx-14-2/fx-14-2_0015_1"}, {"time": 7.5333, "name": "fx-14-2/fx-14-2_0014_2"}, {"time": 7.6, "name": "fx-14-2/fx-14-2_0013_3"}, {"time": 7.6667, "name": "fx-14-2/fx-14-2_0012_4"}, {"time": 7.7333, "name": "fx-14-2/fx-14-2_0011_5"}, {"time": 7.8, "name": "fx-14-2/fx-14-2_0010_6"}, {"time": 7.8667, "name": "fx-14-2/fx-14-2_0009_7"}, {"time": 7.9333, "name": "fx-14-2/fx-14-2_0008_8"}, {"time": 8, "name": "fx-14-2/fx-14-2_0007_9"}, {"time": 8.0667, "name": "fx-14-2/fx-14-2_0006_10"}, {"time": 8.1333, "name": "fx-14-2/fx-14-2_0005_11"}, {"time": 8.2, "name": "fx-14-2/fx-14-2_0004_12"}, {"time": 8.2667, "name": "fx-14-2/fx-14-2_0003_13"}, {"time": 8.3333, "name": "fx-14-2/fx-14-2_0002_14"}, {"time": 8.4, "name": "fx-14-2/fx-14-2_0001_15"}, {"time": 8.4667, "name": "fx-14-2/fx-14-2_0000_16"}, {"time": 8.5333, "name": "fx-14-2/fx-14-2_0015_1"}, {"time": 8.6, "name": "fx-14-2/fx-14-2_0014_2"}, {"time": 8.6667, "name": "fx-14-2/fx-14-2_0013_3"}, {"time": 8.7333, "name": "fx-14-2/fx-14-2_0012_4"}, {"time": 8.8, "name": "fx-14-2/fx-14-2_0011_5"}, {"time": 8.8667, "name": "fx-14-2/fx-14-2_0010_6"}, {"time": 8.9333, "name": "fx-14-2/fx-14-2_0009_7"}, {"time": 9, "name": "fx-14-2/fx-14-2_0008_8"}, {"time": 9.0667, "name": "fx-14-2/fx-14-2_0007_9"}, {"time": 9.1333, "name": "fx-14-2/fx-14-2_0006_10"}, {"time": 9.2, "name": "fx-14-2/fx-14-2_0005_11"}, {"time": 9.2667, "name": "fx-14-2/fx-14-2_0004_12"}, {"time": 9.3333, "name": "fx-14-2/fx-14-2_0003_13"}, {"time": 9.4, "name": "fx-14-2/fx-14-2_0002_14"}, {"time": 9.4667, "name": "fx-14-2/fx-14-2_0001_15"}, {"time": 9.5333, "name": "fx-14-2/fx-14-2_0000_16"}, {"time": 9.6, "name": "fx-14-2/fx-14-2_0015_1"}, {"time": 9.6667, "name": "fx-14-2/fx-14-2_0014_2"}, {"time": 9.7333, "name": "fx-14-2/fx-14-2_0013_3"}, {"time": 9.8, "name": "fx-14-2/fx-14-2_0012_4"}, {"time": 9.8667, "name": "fx-14-2/fx-14-2_0011_5"}, {"time": 9.9333, "name": "fx-14-2/fx-14-2_0010_6"}, {"time": 10, "name": "fx-14-2/fx-14-2_0009_7"}, {"time": 10.0667, "name": "fx-14-2/fx-14-2_0008_8"}, {"time": 10.1333, "name": "fx-14-2/fx-14-2_0007_9"}, {"time": 10.2, "name": "fx-14-2/fx-14-2_0006_10"}, {"time": 10.2667, "name": "fx-14-2/fx-14-2_0005_11"}, {"time": 10.3333, "name": "fx-14-2/fx-14-2_0004_12"}, {"time": 10.4, "name": "fx-14-2/fx-14-2_0003_13"}, {"time": 10.4667, "name": "fx-14-2/fx-14-2_0002_14"}, {"time": 10.5333, "name": "fx-14-2/fx-14-2_0001_15"}, {"time": 10.6, "name": "fx-14-2/fx-14-2_0000_16"}, {"time": 10.6667, "name": "fx-14-2/fx-14-2_0015_1"}, {"time": 10.7333, "name": "fx-14-2/fx-14-2_0014_2"}, {"time": 10.8, "name": "fx-14-2/fx-14-2_0013_3"}, {"time": 10.8667, "name": "fx-14-2/fx-14-2_0012_4"}, {"time": 10.9333, "name": "fx-14-2/fx-14-2_0011_5"}, {"time": 11, "name": "fx-14-2/fx-14-2_0010_6"}, {"time": 11.0667, "name": "fx-14-2/fx-14-2_0009_7"}, {"time": 11.1333, "name": "fx-14-2/fx-14-2_0008_8"}, {"time": 11.2, "name": "fx-14-2/fx-14-2_0007_9"}, {"time": 11.2667, "name": "fx-14-2/fx-14-2_0006_10"}, {"time": 11.3333, "name": "fx-14-2/fx-14-2_0005_11"}, {"time": 11.4, "name": "fx-14-2/fx-14-2_0004_12"}, {"time": 11.4667, "name": "fx-14-2/fx-14-2_0003_13"}, {"time": 11.5333, "name": "fx-14-2/fx-14-2_0002_14"}, {"time": 11.6, "name": "fx-14-2/fx-14-2_0001_15"}, {"time": 11.6667, "name": "fx-14-2/fx-14-2_0000_16"}, {"time": 11.7333, "name": "fx-14-2/fx-14-2_0015_1"}, {"time": 11.8, "name": "fx-14-2/fx-14-2_0014_2"}, {"time": 11.8667, "name": "fx-14-2/fx-14-2_0013_3"}, {"time": 11.9333, "name": "fx-14-2/fx-14-2_0012_4"}, {"time": 12, "name": "fx-14-2/fx-14-2_0011_5"}, {"time": 12.0667, "name": "fx-14-2/fx-14-2_0010_6"}, {"time": 12.1333, "name": "fx-14-2/fx-14-2_0009_7"}, {"time": 12.2, "name": "fx-14-2/fx-14-2_0008_8"}, {"time": 12.2667, "name": "fx-14-2/fx-14-2_0007_9"}, {"time": 12.3333, "name": "fx-14-2/fx-14-2_0006_10"}, {"time": 12.4, "name": "fx-14-2/fx-14-2_0005_11"}, {"time": 12.4667, "name": "fx-14-2/fx-14-2_0004_12"}, {"time": 12.5333, "name": "fx-14-2/fx-14-2_0003_13"}, {"time": 12.6, "name": "fx-14-2/fx-14-2_0002_14"}, {"time": 12.6667, "name": "fx-14-2/fx-14-2_0001_15"}, {"time": 12.7333, "name": "fx-14-2/fx-14-2_0000_16"}, {"time": 12.8, "name": "fx-14-2/fx-14-2_0015_1"}, {"time": 12.8667, "name": "fx-14-2/fx-14-2_0014_2"}, {"time": 12.9333, "name": "fx-14-2/fx-14-2_0013_3"}, {"time": 13, "name": "fx-14-2/fx-14-2_0012_4"}, {"time": 13.0667, "name": "fx-14-2/fx-14-2_0011_5"}, {"time": 13.1333, "name": "fx-14-2/fx-14-2_0010_6"}, {"time": 13.2, "name": "fx-14-2/fx-14-2_0009_7"}, {"time": 13.2667, "name": "fx-14-2/fx-14-2_0008_8"}, {"time": 13.3333, "name": "fx-14-2/fx-14-2_0007_9"}, {"time": 13.4, "name": "fx-14-2/fx-14-2_0006_10"}, {"time": 13.4667, "name": "fx-14-2/fx-14-2_0005_11"}, {"time": 13.5333, "name": "fx-14-2/fx-14-2_0004_12"}, {"time": 13.6, "name": "fx-14-2/fx-14-2_0003_13"}, {"time": 13.6667, "name": "fx-14-2/fx-14-2_0002_14"}, {"time": 13.7333, "name": "fx-14-2/fx-14-2_0001_15"}, {"time": 13.8, "name": "fx-14-2/fx-14-2_0000_16"}, {"time": 13.8667, "name": "fx-14-2/fx-14-2_0015_1"}, {"time": 13.9333, "name": "fx-14-2/fx-14-2_0014_2"}, {"time": 14, "name": "fx-14-2/fx-14-2_0013_3"}, {"time": 14.0667, "name": "fx-14-2/fx-14-2_0012_4"}, {"time": 14.1333, "name": "fx-14-2/fx-14-2_0011_5"}, {"time": 14.2, "name": "fx-14-2/fx-14-2_0010_6"}, {"time": 14.2667, "name": "fx-14-2/fx-14-2_0009_7"}, {"time": 14.3333, "name": "fx-14-2/fx-14-2_0008_8"}, {"time": 14.4, "name": "fx-14-2/fx-14-2_0007_9"}, {"time": 14.4667, "name": "fx-14-2/fx-14-2_0006_10"}, {"time": 14.5333, "name": "fx-14-2/fx-14-2_0005_11"}, {"time": 14.6, "name": "fx-14-2/fx-14-2_0004_12"}, {"time": 14.6667, "name": "fx-14-2/fx-14-2_0003_13"}, {"time": 14.7333, "name": "fx-14-2/fx-14-2_0002_14"}, {"time": 14.8, "name": "fx-14-2/fx-14-2_0001_15"}, {"time": 14.8667, "name": "fx-14-2/fx-14-2_0000_16"}, {"time": 14.9333, "name": "fx-14-2/fx-14-2_0015_1"}, {"time": 15, "name": "fx-14-2/fx-14-2_0014_2"}, {"time": 15.0667, "name": "fx-14-2/fx-14-2_0013_3"}, {"time": 15.1333, "name": "fx-14-2/fx-14-2_0012_4"}, {"time": 15.2, "name": "fx-14-2/fx-14-2_0011_5"}, {"time": 15.2667, "name": "fx-14-2/fx-14-2_0010_6"}, {"time": 15.3333, "name": "fx-14-2/fx-14-2_0009_7"}, {"time": 15.4, "name": "fx-14-2/fx-14-2_0008_8"}, {"time": 15.4667, "name": "fx-14-2/fx-14-2_0007_9"}, {"time": 15.5333, "name": "fx-14-2/fx-14-2_0006_10"}, {"time": 15.6, "name": "fx-14-2/fx-14-2_0005_11"}, {"time": 15.6667, "name": "fx-14-2/fx-14-2_0004_12"}, {"time": 15.7333, "name": "fx-14-2/fx-14-2_0003_13"}, {"time": 15.8, "name": "fx-14-2/fx-14-2_0002_14"}, {"time": 15.8667, "name": "fx-14-2/fx-14-2_0001_15"}, {"time": 15.9333, "name": "fx-14-2/fx-14-2_0000_16"}, {"time": 16, "name": "fx-14-2/fx-14-2_0015_1"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 14.9667, "angle": 0, "curve": "stepped"}, {"time": 15.0333, "angle": 0, "curve": "stepped"}, {"time": 15.1, "angle": 0, "curve": "stepped"}, {"time": 15.1667, "angle": 0, "curve": "stepped"}, {"time": 15.2333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14.9667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.2333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 14.9667, "x": 1, "y": 1}, {"time": 15, "x": 1.013, "y": 1.013}, {"time": 15.0333, "x": 1, "y": 1}, {"time": 15.0667, "x": 1.013, "y": 1.013}, {"time": 15.1, "x": 1, "y": 1}, {"time": 15.1333, "x": 1.013, "y": 1.013}, {"time": 15.1667, "x": 1, "y": 1}, {"time": 15.2, "x": 1.013, "y": 1.013}, {"time": 15.2333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14.9667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.2333, "x": 0, "y": 0}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.0667, "angle": 0, "curve": "stepped"}, {"time": 0.8667, "angle": 0, "curve": "stepped"}, {"time": 0.9333, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 1.0667, "angle": 0, "curve": "stepped"}, {"time": 1.3667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 3.121, "y": 3.121}, {"time": 0.0667, "x": 0.748, "y": 0.748}, {"time": 0.1333, "x": 1.084, "y": 1.084}, {"time": 0.2333, "x": 0.957, "y": 0.957}, {"time": 0.4, "x": 1.025, "y": 1.025}, {"time": 0.8667, "x": 1, "y": 1}, {"time": 0.9, "x": 1.095, "y": 1.095}, {"time": 0.9333, "x": 1, "y": 1}, {"time": 0.9667, "x": 1.095, "y": 1.095}, {"time": 1, "x": 1, "y": 1}, {"time": 1.0333, "x": 1.095, "y": 1.095}, {"time": 1.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3667, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 4, "angle": 0, "curve": "stepped"}, {"time": 6, "angle": 0, "curve": "stepped"}, {"time": 8, "angle": 0, "curve": "stepped"}, {"time": 10, "angle": 0, "curve": "stepped"}, {"time": 12, "angle": 0, "curve": "stepped"}, {"time": 14, "angle": 0, "curve": "stepped"}, {"time": 16, "angle": 0}], "translate": [{"time": 0, "x": 0.02, "y": 1.4, "curve": [0.389, 0, 0.623, 1]}, {"time": 1, "x": 2.48, "y": 1.07, "curve": [0.389, 0, 0.623, 1]}, {"time": 2, "x": 0.02, "y": 1.4, "curve": [0.389, 0, 0.623, 1]}, {"time": 3, "x": 2.48, "y": 1.07, "curve": [0.389, 0, 0.623, 1]}, {"time": 4, "x": 0.02, "y": 1.4, "curve": [0.389, 0, 0.623, 1]}, {"time": 5, "x": 2.48, "y": 1.07, "curve": [0.389, 0, 0.623, 1]}, {"time": 6, "x": 0.02, "y": 1.4, "curve": [0.389, 0, 0.623, 1]}, {"time": 7, "x": 2.48, "y": 1.07, "curve": [0.389, 0, 0.623, 1]}, {"time": 8, "x": 0.02, "y": 1.4, "curve": [0.389, 0, 0.623, 1]}, {"time": 9, "x": 2.48, "y": 1.07, "curve": [0.389, 0, 0.623, 1]}, {"time": 10, "x": 0.02, "y": 1.4, "curve": [0.389, 0, 0.623, 1]}, {"time": 11, "x": 2.48, "y": 1.07, "curve": [0.389, 0, 0.623, 1]}, {"time": 12, "x": 0.02, "y": 1.4, "curve": [0.389, 0, 0.623, 1]}, {"time": 13, "x": 2.48, "y": 1.07, "curve": [0.389, 0, 0.623, 1]}, {"time": 14, "x": 0.02, "y": 1.4, "curve": [0.389, 0, 0.623, 1]}, {"time": 15, "x": 2.48, "y": 1.07, "curve": [0.389, 0, 0.623, 1]}, {"time": 16, "x": 0.02, "y": 1.4}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.377, 0.02, 0.683, 1]}, {"time": 1, "x": 0.946, "y": 0.946, "curve": [0.377, 0.02, 0.683, 1]}, {"time": 2, "x": 1, "y": 1, "curve": [0.377, 0.02, 0.683, 1]}, {"time": 3, "x": 0.946, "y": 0.946, "curve": [0.377, 0.02, 0.683, 1]}, {"time": 4, "x": 1, "y": 1, "curve": [0.377, 0.02, 0.683, 1]}, {"time": 5, "x": 0.946, "y": 0.946, "curve": [0.377, 0.02, 0.683, 1]}, {"time": 6, "x": 1, "y": 1, "curve": [0.377, 0.02, 0.683, 1]}, {"time": 7, "x": 0.946, "y": 0.946, "curve": [0.377, 0.02, 0.683, 1]}, {"time": 8, "x": 1, "y": 1, "curve": [0.377, 0.02, 0.683, 1]}, {"time": 9, "x": 0.946, "y": 0.946, "curve": [0.377, 0.02, 0.683, 1]}, {"time": 10, "x": 1, "y": 1, "curve": [0.377, 0.02, 0.683, 1]}, {"time": 11, "x": 0.946, "y": 0.946, "curve": [0.377, 0.02, 0.683, 1]}, {"time": 12, "x": 1, "y": 1, "curve": [0.377, 0.02, 0.683, 1]}, {"time": 13, "x": 0.946, "y": 0.946, "curve": [0.377, 0.02, 0.683, 1]}, {"time": 14, "x": 1, "y": 1, "curve": [0.377, 0.02, 0.683, 1]}, {"time": 15, "x": 0.946, "y": 0.946, "curve": [0.377, 0.02, 0.683, 1]}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.388, 0, 0.639, 1]}, {"time": 0.9667, "angle": -4.65, "curve": [0.388, 0, 0.639, 1]}, {"time": 2, "angle": 0, "curve": [0.388, 0, 0.639, 1]}, {"time": 2.9667, "angle": -4.65, "curve": [0.388, 0, 0.639, 1]}, {"time": 4, "angle": 0, "curve": [0.388, 0, 0.639, 1]}, {"time": 4.9667, "angle": -4.65, "curve": [0.388, 0, 0.639, 1]}, {"time": 6, "angle": 0, "curve": [0.388, 0, 0.639, 1]}, {"time": 6.9667, "angle": -4.65, "curve": [0.388, 0, 0.639, 1]}, {"time": 8, "angle": 0, "curve": [0.388, 0, 0.639, 1]}, {"time": 8.9667, "angle": -4.65, "curve": [0.388, 0, 0.639, 1]}, {"time": 10, "angle": 0, "curve": [0.388, 0, 0.639, 1]}, {"time": 10.9667, "angle": -4.65, "curve": [0.388, 0, 0.639, 1]}, {"time": 12, "angle": 0, "curve": [0.388, 0, 0.639, 1]}, {"time": 12.9667, "angle": -4.65, "curve": [0.388, 0, 0.639, 1]}, {"time": 14, "angle": 0, "curve": [0.388, 0, 0.639, 1]}, {"time": 14.9667, "angle": -4.65, "curve": [0.388, 0, 0.639, 1]}, {"time": 16, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.388, 0, 0.639, 1]}, {"time": 0.9667, "x": 0, "y": -4.5, "curve": [0.388, 0, 0.639, 1]}, {"time": 2, "x": 0, "y": 0, "curve": [0.388, 0, 0.639, 1]}, {"time": 2.9667, "x": 0, "y": -4.5, "curve": [0.388, 0, 0.639, 1]}, {"time": 4, "x": 0, "y": 0, "curve": [0.388, 0, 0.639, 1]}, {"time": 4.9667, "x": 0, "y": -4.5, "curve": [0.388, 0, 0.639, 1]}, {"time": 6, "x": 0, "y": 0, "curve": [0.388, 0, 0.639, 1]}, {"time": 6.9667, "x": 0, "y": -4.5, "curve": [0.388, 0, 0.639, 1]}, {"time": 8, "x": 0, "y": 0, "curve": [0.388, 0, 0.639, 1]}, {"time": 8.9667, "x": 0, "y": -4.5, "curve": [0.388, 0, 0.639, 1]}, {"time": 10, "x": 0, "y": 0, "curve": [0.388, 0, 0.639, 1]}, {"time": 10.9667, "x": 0, "y": -4.5, "curve": [0.388, 0, 0.639, 1]}, {"time": 12, "x": 0, "y": 0, "curve": [0.388, 0, 0.639, 1]}, {"time": 12.9667, "x": 0, "y": -4.5, "curve": [0.388, 0, 0.639, 1]}, {"time": 14, "x": 0, "y": 0, "curve": [0.388, 0, 0.639, 1]}, {"time": 14.9667, "x": 0, "y": -4.5, "curve": [0.388, 0, 0.639, 1]}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.388, 0, 0.639, 1]}, {"time": 0.9667, "x": 1.028, "y": 1.028, "curve": [0.388, 0, 0.639, 1]}, {"time": 2, "x": 1, "y": 1, "curve": [0.388, 0, 0.639, 1]}, {"time": 2.9667, "x": 1.028, "y": 1.028, "curve": [0.388, 0, 0.639, 1]}, {"time": 4, "x": 1, "y": 1, "curve": [0.388, 0, 0.639, 1]}, {"time": 4.9667, "x": 1.028, "y": 1.028, "curve": [0.388, 0, 0.639, 1]}, {"time": 6, "x": 1, "y": 1, "curve": [0.388, 0, 0.639, 1]}, {"time": 6.9667, "x": 1.028, "y": 1.028, "curve": [0.388, 0, 0.639, 1]}, {"time": 8, "x": 1, "y": 1, "curve": [0.388, 0, 0.639, 1]}, {"time": 8.9667, "x": 1.028, "y": 1.028, "curve": [0.388, 0, 0.639, 1]}, {"time": 10, "x": 1, "y": 1, "curve": [0.388, 0, 0.639, 1]}, {"time": 10.9667, "x": 1.028, "y": 1.028, "curve": [0.388, 0, 0.639, 1]}, {"time": 12, "x": 1, "y": 1, "curve": [0.388, 0, 0.639, 1]}, {"time": 12.9667, "x": 1.028, "y": 1.028, "curve": [0.388, 0, 0.639, 1]}, {"time": 14, "x": 1, "y": 1, "curve": [0.388, 0, 0.639, 1]}, {"time": 14.9667, "x": 1.028, "y": 1.028, "curve": [0.388, 0, 0.639, 1]}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": 0, "curve": [0.366, 0.01, 0.692, 1]}, {"time": 0.9667, "angle": 4.46, "curve": [0.366, 0.01, 0.692, 1]}, {"time": 2, "angle": 0, "curve": [0.366, 0.01, 0.692, 1]}, {"time": 2.9667, "angle": 4.46, "curve": [0.366, 0.01, 0.692, 1]}, {"time": 4, "angle": 0, "curve": [0.366, 0.01, 0.692, 1]}, {"time": 4.9667, "angle": 4.46, "curve": [0.366, 0.01, 0.692, 1]}, {"time": 6, "angle": 0, "curve": [0.366, 0.01, 0.692, 1]}, {"time": 6.9667, "angle": 4.46, "curve": [0.366, 0.01, 0.692, 1]}, {"time": 8, "angle": 0, "curve": [0.366, 0.01, 0.692, 1]}, {"time": 8.9667, "angle": 4.46, "curve": [0.366, 0.01, 0.692, 1]}, {"time": 10, "angle": 0, "curve": [0.366, 0.01, 0.692, 1]}, {"time": 10.9667, "angle": 4.46, "curve": [0.366, 0.01, 0.692, 1]}, {"time": 12, "angle": 0, "curve": [0.366, 0.01, 0.692, 1]}, {"time": 12.9667, "angle": 4.46, "curve": [0.366, 0.01, 0.692, 1]}, {"time": 14, "angle": 0, "curve": [0.366, 0.01, 0.692, 1]}, {"time": 14.9667, "angle": 4.46, "curve": [0.366, 0.01, 0.692, 1]}, {"time": 16, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12, "x": 1, "y": 1, "curve": "stepped"}, {"time": 14, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 4, "angle": 0, "curve": "stepped"}, {"time": 6, "angle": 0, "curve": "stepped"}, {"time": 8, "angle": 0, "curve": "stepped"}, {"time": 10, "angle": 0, "curve": "stepped"}, {"time": 12, "angle": 0, "curve": "stepped"}, {"time": 14, "angle": 0, "curve": "stepped"}, {"time": 16, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.377, 0, 0.667, 0.99]}, {"time": 1.0333, "x": 0.32, "y": 0.76, "curve": [0.377, 0, 0.667, 0.99]}, {"time": 2, "x": 0, "y": 0, "curve": [0.377, 0, 0.667, 0.99]}, {"time": 3.0333, "x": 0.32, "y": 0.76, "curve": [0.377, 0, 0.667, 0.99]}, {"time": 4, "x": 0, "y": 0, "curve": [0.377, 0, 0.667, 0.99]}, {"time": 5.0333, "x": 0.32, "y": 0.76, "curve": [0.377, 0, 0.667, 0.99]}, {"time": 6, "x": 0, "y": 0, "curve": [0.377, 0, 0.667, 0.99]}, {"time": 7.0333, "x": 0.32, "y": 0.76, "curve": [0.377, 0, 0.667, 0.99]}, {"time": 8, "x": 0, "y": 0, "curve": [0.377, 0, 0.667, 0.99]}, {"time": 9.0333, "x": 0.32, "y": 0.76, "curve": [0.377, 0, 0.667, 0.99]}, {"time": 10, "x": 0, "y": 0, "curve": [0.377, 0, 0.667, 0.99]}, {"time": 11.0333, "x": 0.32, "y": 0.76, "curve": [0.377, 0, 0.667, 0.99]}, {"time": 12, "x": 0, "y": 0, "curve": [0.377, 0, 0.667, 0.99]}, {"time": 13.0333, "x": 0.32, "y": 0.76, "curve": [0.377, 0, 0.667, 0.99]}, {"time": 14, "x": 0, "y": 0, "curve": [0.377, 0, 0.667, 0.99]}, {"time": 15.0333, "x": 0.32, "y": 0.76, "curve": [0.377, 0, 0.667, 0.99]}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12, "x": 1, "y": 1, "curve": "stepped"}, {"time": 14, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone6": {"rotate": [{"time": 0, "angle": -0.4, "curve": [0.336, 0.34, 0.656, 1]}, {"time": 0.7667, "angle": -1.92, "curve": [0.349, 0.01, 0.659, 1]}, {"time": 1.7333, "angle": -0.32, "curve": [0.342, 0.02, 0.674, 0.42]}, {"time": 2, "angle": -0.4, "curve": [0.336, 0.34, 0.656, 1]}, {"time": 2.7667, "angle": -1.92, "curve": [0.349, 0.01, 0.659, 1]}, {"time": 3.7333, "angle": -0.32, "curve": [0.342, 0.02, 0.674, 0.42]}, {"time": 4, "angle": -0.4, "curve": [0.336, 0.34, 0.656, 1]}, {"time": 4.7667, "angle": -1.92, "curve": [0.349, 0.01, 0.659, 1]}, {"time": 5.7333, "angle": -0.32, "curve": [0.342, 0.02, 0.674, 0.42]}, {"time": 6, "angle": -0.4, "curve": [0.336, 0.34, 0.656, 1]}, {"time": 6.7667, "angle": -1.92, "curve": [0.349, 0.01, 0.659, 1]}, {"time": 7.7333, "angle": -0.32, "curve": [0.342, 0.02, 0.674, 0.42]}, {"time": 8, "angle": -0.4, "curve": [0.336, 0.34, 0.656, 1]}, {"time": 8.7667, "angle": -1.92, "curve": [0.349, 0.01, 0.659, 1]}, {"time": 9.7333, "angle": -0.32, "curve": [0.342, 0.02, 0.674, 0.42]}, {"time": 10, "angle": -0.4, "curve": [0.336, 0.34, 0.656, 1]}, {"time": 10.7667, "angle": -1.92, "curve": [0.349, 0.01, 0.659, 1]}, {"time": 11.7333, "angle": -0.32, "curve": [0.342, 0.02, 0.674, 0.42]}, {"time": 12, "angle": -0.4, "curve": [0.336, 0.34, 0.656, 1]}, {"time": 12.7667, "angle": -1.92, "curve": [0.349, 0.01, 0.659, 1]}, {"time": 13.7333, "angle": -0.32, "curve": [0.342, 0.02, 0.674, 0.42]}, {"time": 14, "angle": -0.4, "curve": [0.336, 0.34, 0.656, 1]}, {"time": 14.7667, "angle": -1.92, "curve": [0.349, 0.01, 0.659, 1]}, {"time": 15.7333, "angle": -0.32, "curve": [0.342, 0.02, 0.674, 0.42]}, {"time": 16, "angle": -0.4}], "translate": [{"time": 0, "x": -0.14, "y": 0.19, "curve": [0.336, 0.34, 0.656, 1]}, {"time": 0.7667, "x": 0.25, "y": -0.8, "curve": [0.349, 0.01, 0.659, 1]}, {"time": 1.7333, "x": -0.21, "y": 0.33, "curve": [0.342, 0.02, 0.674, 0.42]}, {"time": 2, "x": -0.14, "y": 0.19, "curve": [0.336, 0.34, 0.656, 1]}, {"time": 2.7667, "x": 0.25, "y": -0.8, "curve": [0.349, 0.01, 0.659, 1]}, {"time": 3.7333, "x": -0.21, "y": 0.33, "curve": [0.342, 0.02, 0.674, 0.42]}, {"time": 4, "x": -0.14, "y": 0.19, "curve": [0.336, 0.34, 0.656, 1]}, {"time": 4.7667, "x": 0.25, "y": -0.8, "curve": [0.349, 0.01, 0.659, 1]}, {"time": 5.7333, "x": -0.21, "y": 0.33, "curve": [0.342, 0.02, 0.674, 0.42]}, {"time": 6, "x": -0.14, "y": 0.19, "curve": [0.336, 0.34, 0.656, 1]}, {"time": 6.7667, "x": 0.25, "y": -0.8, "curve": [0.349, 0.01, 0.659, 1]}, {"time": 7.7333, "x": -0.21, "y": 0.33, "curve": [0.342, 0.02, 0.674, 0.42]}, {"time": 8, "x": -0.14, "y": 0.19, "curve": [0.336, 0.34, 0.656, 1]}, {"time": 8.7667, "x": 0.25, "y": -0.8, "curve": [0.349, 0.01, 0.659, 1]}, {"time": 9.7333, "x": -0.21, "y": 0.33, "curve": [0.342, 0.02, 0.674, 0.42]}, {"time": 10, "x": -0.14, "y": 0.19, "curve": [0.336, 0.34, 0.656, 1]}, {"time": 10.7667, "x": 0.25, "y": -0.8, "curve": [0.349, 0.01, 0.659, 1]}, {"time": 11.7333, "x": -0.21, "y": 0.33, "curve": [0.342, 0.02, 0.674, 0.42]}, {"time": 12, "x": -0.14, "y": 0.19, "curve": [0.336, 0.34, 0.656, 1]}, {"time": 12.7667, "x": 0.25, "y": -0.8, "curve": [0.349, 0.01, 0.659, 1]}, {"time": 13.7333, "x": -0.21, "y": 0.33, "curve": [0.342, 0.02, 0.674, 0.42]}, {"time": 14, "x": -0.14, "y": 0.19, "curve": [0.336, 0.34, 0.656, 1]}, {"time": 14.7667, "x": 0.25, "y": -0.8, "curve": [0.349, 0.01, 0.659, 1]}, {"time": 15.7333, "x": -0.21, "y": 0.33, "curve": [0.342, 0.02, 0.674, 0.42]}, {"time": 16, "x": -0.14, "y": 0.19}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12, "x": 1, "y": 1, "curve": "stepped"}, {"time": 14, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone7": {"rotate": [{"time": 0, "angle": 2, "curve": [0.34, 0.33, 0.644, 1]}, {"time": 0.6333, "angle": -5.55, "curve": [0.366, 0.02, 0.65, 1]}, {"time": 1.8, "angle": 3.27, "curve": [0.35, 0.03, 0.68, 0.42]}, {"time": 2, "angle": 2, "curve": [0.34, 0.33, 0.644, 1]}, {"time": 2.6333, "angle": -5.55, "curve": [0.366, 0.02, 0.65, 1]}, {"time": 3.8, "angle": 3.27, "curve": [0.35, 0.03, 0.68, 0.42]}, {"time": 4, "angle": 2, "curve": [0.34, 0.33, 0.644, 1]}, {"time": 4.6333, "angle": -5.55, "curve": [0.366, 0.02, 0.65, 1]}, {"time": 5.8, "angle": 3.27, "curve": [0.35, 0.03, 0.68, 0.42]}, {"time": 6, "angle": 2, "curve": [0.34, 0.33, 0.644, 1]}, {"time": 6.6333, "angle": -5.55, "curve": [0.366, 0.02, 0.65, 1]}, {"time": 7.8, "angle": 3.27, "curve": [0.35, 0.03, 0.68, 0.42]}, {"time": 8, "angle": 2, "curve": [0.34, 0.33, 0.644, 1]}, {"time": 8.6333, "angle": -5.55, "curve": [0.366, 0.02, 0.65, 1]}, {"time": 9.8, "angle": 3.27, "curve": [0.35, 0.03, 0.68, 0.42]}, {"time": 10, "angle": 2, "curve": [0.34, 0.33, 0.644, 1]}, {"time": 10.6333, "angle": -5.55, "curve": [0.366, 0.02, 0.65, 1]}, {"time": 11.8, "angle": 3.27, "curve": [0.35, 0.03, 0.68, 0.42]}, {"time": 12, "angle": 2, "curve": [0.34, 0.33, 0.644, 1]}, {"time": 12.6333, "angle": -5.55, "curve": [0.366, 0.02, 0.65, 1]}, {"time": 13.8, "angle": 3.27, "curve": [0.35, 0.03, 0.68, 0.42]}, {"time": 14, "angle": 2, "curve": [0.34, 0.33, 0.644, 1]}, {"time": 14.6333, "angle": -5.55, "curve": [0.366, 0.02, 0.65, 1]}, {"time": 15.8, "angle": 3.27, "curve": [0.35, 0.03, 0.68, 0.42]}, {"time": 16, "angle": 2}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 1.06, "y": 0.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.0333, "x": 1.06, "y": 0.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0333, "x": 1.06, "y": 0.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.0333, "x": 1.06, "y": 0.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.0333, "x": 1.06, "y": 0.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 10, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.0333, "x": 1.06, "y": 0.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 12, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 13.0333, "x": 1.06, "y": 0.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 14, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 15.0333, "x": 1.06, "y": 0.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12, "x": 1, "y": 1, "curve": "stepped"}, {"time": 14, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone8": {"rotate": [{"time": 0, "angle": 2.88, "curve": [0.356, 0.26, 0.665, 1]}, {"time": 0.8333, "angle": -3.73, "curve": [0.372, 0.02, 0.671, 1]}, {"time": 1.8333, "angle": 3.42, "curve": [0.344, 0.04, 0.676, 0.41]}, {"time": 2, "angle": 2.88, "curve": [0.356, 0.26, 0.665, 1]}, {"time": 2.8333, "angle": -3.73, "curve": [0.372, 0.02, 0.671, 1]}, {"time": 3.8333, "angle": 3.42, "curve": [0.344, 0.04, 0.676, 0.41]}, {"time": 4, "angle": 2.88, "curve": [0.356, 0.26, 0.665, 1]}, {"time": 4.8333, "angle": -3.73, "curve": [0.372, 0.02, 0.671, 1]}, {"time": 5.8333, "angle": 3.42, "curve": [0.344, 0.04, 0.676, 0.41]}, {"time": 6, "angle": 2.88, "curve": [0.356, 0.26, 0.665, 1]}, {"time": 6.8333, "angle": -3.73, "curve": [0.372, 0.02, 0.671, 1]}, {"time": 7.8333, "angle": 3.42, "curve": [0.344, 0.04, 0.676, 0.41]}, {"time": 8, "angle": 2.88, "curve": [0.356, 0.26, 0.665, 1]}, {"time": 8.8333, "angle": -3.73, "curve": [0.372, 0.02, 0.671, 1]}, {"time": 9.8333, "angle": 3.42, "curve": [0.344, 0.04, 0.676, 0.41]}, {"time": 10, "angle": 2.88, "curve": [0.356, 0.26, 0.665, 1]}, {"time": 10.8333, "angle": -3.73, "curve": [0.372, 0.02, 0.671, 1]}, {"time": 11.8333, "angle": 3.42, "curve": [0.344, 0.04, 0.676, 0.41]}, {"time": 12, "angle": 2.88, "curve": [0.356, 0.26, 0.665, 1]}, {"time": 12.8333, "angle": -3.73, "curve": [0.372, 0.02, 0.671, 1]}, {"time": 13.8333, "angle": 3.42, "curve": [0.344, 0.04, 0.676, 0.41]}, {"time": 14, "angle": 2.88, "curve": [0.356, 0.26, 0.665, 1]}, {"time": 14.8333, "angle": -3.73, "curve": [0.372, 0.02, 0.671, 1]}, {"time": 15.8333, "angle": 3.42, "curve": [0.344, 0.04, 0.676, 0.41]}, {"time": 16, "angle": 2.88}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0.47, "y": -0.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 0.47, "y": -0.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5, "x": 0.47, "y": -0.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7, "x": 0.47, "y": -0.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9, "x": 0.47, "y": -0.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 10, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11, "x": 0.47, "y": -0.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 12, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 13, "x": 0.47, "y": -0.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 14, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 15, "x": 0.47, "y": -0.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12, "x": 1, "y": 1, "curve": "stepped"}, {"time": 14, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone9": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -7.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": -7.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5, "angle": -7.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 6, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7, "angle": -7.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 8, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9, "angle": -7.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 10, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11, "angle": -7.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 12, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 13, "angle": -7.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 14, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 15, "angle": -7.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 16, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0.83, "y": -0.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 0.83, "y": -0.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5, "x": 0.83, "y": -0.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7, "x": 0.83, "y": -0.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9, "x": 0.83, "y": -0.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 10, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 11, "x": 0.83, "y": -0.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 12, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 13, "x": 0.83, "y": -0.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 14, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 15, "x": 0.83, "y": -0.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0.928, "y": 0.928, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 0.928, "y": 0.928, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5, "x": 0.928, "y": 0.928, "curve": [0.25, 0, 0.75, 1]}, {"time": 6, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7, "x": 0.928, "y": 0.928, "curve": [0.25, 0, 0.75, 1]}, {"time": 8, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9, "x": 0.928, "y": 0.928, "curve": [0.25, 0, 0.75, 1]}, {"time": 10, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 11, "x": 0.928, "y": 0.928, "curve": [0.25, 0, 0.75, 1]}, {"time": 12, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 13, "x": 0.928, "y": 0.928, "curve": [0.25, 0, 0.75, 1]}, {"time": 14, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 15, "x": 0.928, "y": 0.928, "curve": [0.25, 0, 0.75, 1]}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone10": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -4.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.9667, "angle": -4.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.9667, "angle": -4.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 6, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9667, "angle": -4.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 8, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.9667, "angle": -4.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 10, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.9667, "angle": -4.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 12, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 12.9667, "angle": -4.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 14, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 14.9667, "angle": -4.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 16, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12, "x": 1, "y": 1, "curve": "stepped"}, {"time": 14, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone11": {"rotate": [{"time": 0, "angle": 0.85, "curve": [0.33, 0.57, 0.658, 1]}, {"time": 0.3, "angle": 1.8, "curve": [0.381, 0, 0.671, 1]}, {"time": 1.4, "angle": -1.59, "curve": [0.374, 0, 0.688, 0.6]}, {"time": 2, "angle": 0.85, "curve": [0.33, 0.57, 0.658, 1]}, {"time": 2.3, "angle": 1.8, "curve": [0.381, 0, 0.671, 1]}, {"time": 3.4, "angle": -1.59, "curve": [0.374, 0, 0.688, 0.6]}, {"time": 4, "angle": 0.85, "curve": [0.33, 0.57, 0.658, 1]}, {"time": 4.3, "angle": 1.8, "curve": [0.381, 0, 0.671, 1]}, {"time": 5.4, "angle": -1.59, "curve": [0.374, 0, 0.688, 0.6]}, {"time": 6, "angle": 0.85, "curve": [0.33, 0.57, 0.658, 1]}, {"time": 6.3, "angle": 1.8, "curve": [0.381, 0, 0.671, 1]}, {"time": 7.4, "angle": -1.59, "curve": [0.374, 0, 0.688, 0.6]}, {"time": 8, "angle": 0.85, "curve": [0.33, 0.57, 0.658, 1]}, {"time": 8.3, "angle": 1.8, "curve": [0.381, 0, 0.671, 1]}, {"time": 9.4, "angle": -1.59, "curve": [0.374, 0, 0.688, 0.6]}, {"time": 10, "angle": 0.85, "curve": [0.33, 0.57, 0.658, 1]}, {"time": 10.3, "angle": 1.8, "curve": [0.381, 0, 0.671, 1]}, {"time": 11.4, "angle": -1.59, "curve": [0.374, 0, 0.688, 0.6]}, {"time": 12, "angle": 0.85, "curve": [0.33, 0.57, 0.658, 1]}, {"time": 12.3, "angle": 1.8, "curve": [0.381, 0, 0.671, 1]}, {"time": 13.4, "angle": -1.59, "curve": [0.374, 0, 0.688, 0.6]}, {"time": 14, "angle": 0.85, "curve": [0.33, 0.57, 0.658, 1]}, {"time": 14.3, "angle": 1.8, "curve": [0.381, 0, 0.671, 1]}, {"time": 15.4, "angle": -1.59, "curve": [0.374, 0, 0.688, 0.6]}, {"time": 16, "angle": 0.85}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12, "x": 1, "y": 1, "curve": "stepped"}, {"time": 14, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone12": {"rotate": [{"time": 0, "angle": -3.37, "curve": [0.332, 0.31, 0.689, 1]}, {"time": 0.8333, "angle": 0.7, "curve": [0.313, 0, 0.687, 1]}, {"time": 1.8, "angle": -4.46, "curve": [0.322, 0, 0.657, 0.39]}, {"time": 2, "angle": -3.37, "curve": [0.332, 0.31, 0.689, 1]}, {"time": 2.8333, "angle": 0.7, "curve": [0.313, 0, 0.687, 1]}, {"time": 3.8, "angle": -4.46, "curve": [0.322, 0, 0.657, 0.39]}, {"time": 4, "angle": -3.37, "curve": [0.332, 0.31, 0.689, 1]}, {"time": 4.8333, "angle": 0.7, "curve": [0.313, 0, 0.687, 1]}, {"time": 5.8, "angle": -4.46, "curve": [0.322, 0, 0.657, 0.39]}, {"time": 6, "angle": -3.37, "curve": [0.332, 0.31, 0.689, 1]}, {"time": 6.8333, "angle": 0.7, "curve": [0.313, 0, 0.687, 1]}, {"time": 7.8, "angle": -4.46, "curve": [0.322, 0, 0.657, 0.39]}, {"time": 8, "angle": -3.37, "curve": [0.332, 0.31, 0.689, 1]}, {"time": 8.8333, "angle": 0.7, "curve": [0.313, 0, 0.687, 1]}, {"time": 9.8, "angle": -4.46, "curve": [0.322, 0, 0.657, 0.39]}, {"time": 10, "angle": -3.37, "curve": [0.332, 0.31, 0.689, 1]}, {"time": 10.8333, "angle": 0.7, "curve": [0.313, 0, 0.687, 1]}, {"time": 11.8, "angle": -4.46, "curve": [0.322, 0, 0.657, 0.39]}, {"time": 12, "angle": -3.37, "curve": [0.332, 0.31, 0.689, 1]}, {"time": 12.8333, "angle": 0.7, "curve": [0.313, 0, 0.687, 1]}, {"time": 13.8, "angle": -4.46, "curve": [0.322, 0, 0.657, 0.39]}, {"time": 14, "angle": -3.37, "curve": [0.332, 0.31, 0.689, 1]}, {"time": 14.8333, "angle": 0.7, "curve": [0.313, 0, 0.687, 1]}, {"time": 15.8, "angle": -4.46, "curve": [0.322, 0, 0.657, 0.39]}, {"time": 16, "angle": -3.37}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12, "x": 1, "y": 1, "curve": "stepped"}, {"time": 14, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone13": {"rotate": [{"time": 0, "angle": -0.28, "curve": [0.323, 0.45, 0.628, 1]}, {"time": 0.5667, "angle": -1.46, "curve": [0.381, 0, 0.635, 1]}, {"time": 1.6, "angle": 0.36, "curve": [0.371, 0, 0.691, 0.46]}, {"time": 2, "angle": -0.28, "curve": [0.323, 0.45, 0.628, 1]}, {"time": 2.5667, "angle": -1.46, "curve": [0.381, 0, 0.635, 1]}, {"time": 3.6, "angle": 0.36, "curve": [0.371, 0, 0.691, 0.46]}, {"time": 4, "angle": -0.28, "curve": [0.323, 0.45, 0.628, 1]}, {"time": 4.5667, "angle": -1.46, "curve": [0.381, 0, 0.635, 1]}, {"time": 5.6, "angle": 0.36, "curve": [0.371, 0, 0.691, 0.46]}, {"time": 6, "angle": -0.28, "curve": [0.323, 0.45, 0.628, 1]}, {"time": 6.5667, "angle": -1.46, "curve": [0.381, 0, 0.635, 1]}, {"time": 7.6, "angle": 0.36, "curve": [0.371, 0, 0.691, 0.46]}, {"time": 8, "angle": -0.28, "curve": [0.323, 0.45, 0.628, 1]}, {"time": 8.5667, "angle": -1.46, "curve": [0.381, 0, 0.635, 1]}, {"time": 9.6, "angle": 0.36, "curve": [0.371, 0, 0.691, 0.46]}, {"time": 10, "angle": -0.28, "curve": [0.323, 0.45, 0.628, 1]}, {"time": 10.5667, "angle": -1.46, "curve": [0.381, 0, 0.635, 1]}, {"time": 11.6, "angle": 0.36, "curve": [0.371, 0, 0.691, 0.46]}, {"time": 12, "angle": -0.28, "curve": [0.323, 0.45, 0.628, 1]}, {"time": 12.5667, "angle": -1.46, "curve": [0.381, 0, 0.635, 1]}, {"time": 13.6, "angle": 0.36, "curve": [0.371, 0, 0.691, 0.46]}, {"time": 14, "angle": -0.28, "curve": [0.323, 0.45, 0.628, 1]}, {"time": 14.5667, "angle": -1.46, "curve": [0.381, 0, 0.635, 1]}, {"time": 15.6, "angle": 0.36, "curve": [0.371, 0, 0.691, 0.46]}, {"time": 16, "angle": -0.28}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12, "x": 1, "y": 1, "curve": "stepped"}, {"time": 14, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone14": {"rotate": [{"time": 0, "angle": 3.68, "curve": [0.355, 0.14, 0.658, 1]}, {"time": 0.9, "angle": -5.62, "curve": [0.361, 0.02, 0.66, 1]}, {"time": 1.9333, "angle": 3.88, "curve": [0.337, 0.08, 0.67, 0.43]}, {"time": 2, "angle": 3.68, "curve": [0.355, 0.14, 0.658, 1]}, {"time": 2.9, "angle": -5.62, "curve": [0.361, 0.02, 0.66, 1]}, {"time": 3.9333, "angle": 3.88, "curve": [0.337, 0.08, 0.67, 0.43]}, {"time": 4, "angle": 3.68, "curve": [0.355, 0.14, 0.658, 1]}, {"time": 4.9, "angle": -5.62, "curve": [0.361, 0.02, 0.66, 1]}, {"time": 5.9333, "angle": 3.88, "curve": [0.337, 0.08, 0.67, 0.43]}, {"time": 6, "angle": 3.68, "curve": [0.355, 0.14, 0.658, 1]}, {"time": 6.9, "angle": -5.62, "curve": [0.361, 0.02, 0.66, 1]}, {"time": 7.9333, "angle": 3.88, "curve": [0.337, 0.08, 0.67, 0.43]}, {"time": 8, "angle": 3.68, "curve": [0.355, 0.14, 0.658, 1]}, {"time": 8.9, "angle": -5.62, "curve": [0.361, 0.02, 0.66, 1]}, {"time": 9.9333, "angle": 3.88, "curve": [0.337, 0.08, 0.67, 0.43]}, {"time": 10, "angle": 3.68, "curve": [0.355, 0.14, 0.658, 1]}, {"time": 10.9, "angle": -5.62, "curve": [0.361, 0.02, 0.66, 1]}, {"time": 11.9333, "angle": 3.88, "curve": [0.337, 0.08, 0.67, 0.43]}, {"time": 12, "angle": 3.68, "curve": [0.355, 0.14, 0.658, 1]}, {"time": 12.9, "angle": -5.62, "curve": [0.361, 0.02, 0.66, 1]}, {"time": 13.9333, "angle": 3.88, "curve": [0.337, 0.08, 0.67, 0.43]}, {"time": 14, "angle": 3.68, "curve": [0.355, 0.14, 0.658, 1]}, {"time": 14.9, "angle": -5.62, "curve": [0.361, 0.02, 0.66, 1]}, {"time": 15.9333, "angle": 3.88, "curve": [0.337, 0.08, 0.67, 0.43]}, {"time": 16, "angle": 3.68}], "translate": [{"time": 0, "x": 0.6, "y": -0.27, "curve": [0.345, 0.26, 0.662, 1]}, {"time": 0.8, "x": -1.07, "y": 0.43, "curve": [0.356, 0, 0.665, 1]}, {"time": 1.8333, "x": 0.74, "y": -0.32, "curve": [0.34, 0, 0.673, 0.38]}, {"time": 2, "x": 0.6, "y": -0.27, "curve": [0.345, 0.26, 0.662, 1]}, {"time": 2.8, "x": -1.07, "y": 0.43, "curve": [0.356, 0, 0.665, 1]}, {"time": 3.8333, "x": 0.74, "y": -0.32, "curve": [0.34, 0, 0.673, 0.38]}, {"time": 4, "x": 0.6, "y": -0.27, "curve": [0.345, 0.26, 0.662, 1]}, {"time": 4.8, "x": -1.07, "y": 0.43, "curve": [0.356, 0, 0.665, 1]}, {"time": 5.8333, "x": 0.74, "y": -0.32, "curve": [0.34, 0, 0.673, 0.38]}, {"time": 6, "x": 0.6, "y": -0.27, "curve": [0.345, 0.26, 0.662, 1]}, {"time": 6.8, "x": -1.07, "y": 0.43, "curve": [0.356, 0, 0.665, 1]}, {"time": 7.8333, "x": 0.74, "y": -0.32, "curve": [0.34, 0, 0.673, 0.38]}, {"time": 8, "x": 0.6, "y": -0.27, "curve": [0.345, 0.26, 0.662, 1]}, {"time": 8.8, "x": -1.07, "y": 0.43, "curve": [0.356, 0, 0.665, 1]}, {"time": 9.8333, "x": 0.74, "y": -0.32, "curve": [0.34, 0, 0.673, 0.38]}, {"time": 10, "x": 0.6, "y": -0.27, "curve": [0.345, 0.26, 0.662, 1]}, {"time": 10.8, "x": -1.07, "y": 0.43, "curve": [0.356, 0, 0.665, 1]}, {"time": 11.8333, "x": 0.74, "y": -0.32, "curve": [0.34, 0, 0.673, 0.38]}, {"time": 12, "x": 0.6, "y": -0.27, "curve": [0.345, 0.26, 0.662, 1]}, {"time": 12.8, "x": -1.07, "y": 0.43, "curve": [0.356, 0, 0.665, 1]}, {"time": 13.8333, "x": 0.74, "y": -0.32, "curve": [0.34, 0, 0.673, 0.38]}, {"time": 14, "x": 0.6, "y": -0.27, "curve": [0.345, 0.26, 0.662, 1]}, {"time": 14.8, "x": -1.07, "y": 0.43, "curve": [0.356, 0, 0.665, 1]}, {"time": 15.8333, "x": 0.74, "y": -0.32, "curve": [0.34, 0, 0.673, 0.38]}, {"time": 16, "x": 0.6, "y": -0.27}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12, "x": 1, "y": 1, "curve": "stepped"}, {"time": 14, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone15": {"rotate": [{"time": 0, "angle": 0, "curve": [0.35, 0, 0.676, 1]}, {"time": 1, "angle": -5.99, "curve": [0.35, 0, 0.676, 1]}, {"time": 2, "angle": 0, "curve": [0.35, 0, 0.676, 1]}, {"time": 3, "angle": -5.99, "curve": [0.35, 0, 0.676, 1]}, {"time": 4, "angle": 0, "curve": [0.35, 0, 0.676, 1]}, {"time": 5, "angle": -5.99, "curve": [0.35, 0, 0.676, 1]}, {"time": 6, "angle": 0, "curve": [0.35, 0, 0.676, 1]}, {"time": 7, "angle": -5.99, "curve": [0.35, 0, 0.676, 1]}, {"time": 8, "angle": 0, "curve": [0.35, 0, 0.676, 1]}, {"time": 9, "angle": -5.99, "curve": [0.35, 0, 0.676, 1]}, {"time": 10, "angle": 0, "curve": [0.35, 0, 0.676, 1]}, {"time": 11, "angle": -5.99, "curve": [0.35, 0, 0.676, 1]}, {"time": 12, "angle": 0, "curve": [0.35, 0, 0.676, 1]}, {"time": 13, "angle": -5.99, "curve": [0.35, 0, 0.676, 1]}, {"time": 14, "angle": 0, "curve": [0.35, 0, 0.676, 1]}, {"time": 15, "angle": -5.99, "curve": [0.35, 0, 0.676, 1]}, {"time": 16, "angle": 0}], "translate": [{"time": 0, "x": 0.88, "y": -0.28, "curve": [0.344, 0.32, 0.672, 1]}, {"time": 0.6667, "x": -1.39, "y": 1.18, "curve": [0.35, 0, 0.676, 1]}, {"time": 1.8, "x": 1.22, "y": -0.5, "curve": [0.339, 0, 0.671, 0.39]}, {"time": 2, "x": 0.88, "y": -0.28, "curve": [0.344, 0.32, 0.672, 1]}, {"time": 2.6667, "x": -1.39, "y": 1.18, "curve": [0.35, 0, 0.676, 1]}, {"time": 3.8, "x": 1.22, "y": -0.5, "curve": [0.339, 0, 0.671, 0.39]}, {"time": 4, "x": 0.88, "y": -0.28, "curve": [0.344, 0.32, 0.672, 1]}, {"time": 4.6667, "x": -1.39, "y": 1.18, "curve": [0.35, 0, 0.676, 1]}, {"time": 5.8, "x": 1.22, "y": -0.5, "curve": [0.339, 0, 0.671, 0.39]}, {"time": 6, "x": 0.88, "y": -0.28, "curve": [0.344, 0.32, 0.672, 1]}, {"time": 6.6667, "x": -1.39, "y": 1.18, "curve": [0.35, 0, 0.676, 1]}, {"time": 7.8, "x": 1.22, "y": -0.5, "curve": [0.339, 0, 0.671, 0.39]}, {"time": 8, "x": 0.88, "y": -0.28, "curve": [0.344, 0.32, 0.672, 1]}, {"time": 8.6667, "x": -1.39, "y": 1.18, "curve": [0.35, 0, 0.676, 1]}, {"time": 9.8, "x": 1.22, "y": -0.5, "curve": [0.339, 0, 0.671, 0.39]}, {"time": 10, "x": 0.88, "y": -0.28, "curve": [0.344, 0.32, 0.672, 1]}, {"time": 10.6667, "x": -1.39, "y": 1.18, "curve": [0.35, 0, 0.676, 1]}, {"time": 11.8, "x": 1.22, "y": -0.5, "curve": [0.339, 0, 0.671, 0.39]}, {"time": 12, "x": 0.88, "y": -0.28, "curve": [0.344, 0.32, 0.672, 1]}, {"time": 12.6667, "x": -1.39, "y": 1.18, "curve": [0.35, 0, 0.676, 1]}, {"time": 13.8, "x": 1.22, "y": -0.5, "curve": [0.339, 0, 0.671, 0.39]}, {"time": 14, "x": 0.88, "y": -0.28, "curve": [0.344, 0.32, 0.672, 1]}, {"time": 14.6667, "x": -1.39, "y": 1.18, "curve": [0.35, 0, 0.676, 1]}, {"time": 15.8, "x": 1.22, "y": -0.5, "curve": [0.339, 0, 0.671, 0.39]}, {"time": 16, "x": 0.88, "y": -0.28}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12, "x": 1, "y": 1, "curve": "stepped"}, {"time": 14, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone16": {"rotate": [{"time": 0, "angle": 0.65, "curve": [0.34, 0.27, 0.635, 1]}, {"time": 0.5667, "angle": -10.47, "curve": [0.361, 0.02, 0.639, 1]}, {"time": 1.8333, "angle": 1.34, "curve": [0.346, 0.04, 0.678, 0.41]}, {"time": 2, "angle": 0.65, "curve": [0.34, 0.27, 0.635, 1]}, {"time": 2.5667, "angle": -10.47, "curve": [0.361, 0.02, 0.639, 1]}, {"time": 3.8333, "angle": 1.34, "curve": [0.346, 0.04, 0.678, 0.41]}, {"time": 4, "angle": 0.65, "curve": [0.34, 0.27, 0.635, 1]}, {"time": 4.5667, "angle": -10.47, "curve": [0.361, 0.02, 0.639, 1]}, {"time": 5.8333, "angle": 1.34, "curve": [0.346, 0.04, 0.678, 0.41]}, {"time": 6, "angle": 0.65, "curve": [0.34, 0.27, 0.635, 1]}, {"time": 6.5667, "angle": -10.47, "curve": [0.361, 0.02, 0.639, 1]}, {"time": 7.8333, "angle": 1.34, "curve": [0.346, 0.04, 0.678, 0.41]}, {"time": 8, "angle": 0.65, "curve": [0.34, 0.27, 0.635, 1]}, {"time": 8.5667, "angle": -10.47, "curve": [0.361, 0.02, 0.639, 1]}, {"time": 9.8333, "angle": 1.34, "curve": [0.346, 0.04, 0.678, 0.41]}, {"time": 10, "angle": 0.65, "curve": [0.34, 0.27, 0.635, 1]}, {"time": 10.5667, "angle": -10.47, "curve": [0.361, 0.02, 0.639, 1]}, {"time": 11.8333, "angle": 1.34, "curve": [0.346, 0.04, 0.678, 0.41]}, {"time": 12, "angle": 0.65, "curve": [0.34, 0.27, 0.635, 1]}, {"time": 12.5667, "angle": -10.47, "curve": [0.361, 0.02, 0.639, 1]}, {"time": 13.8333, "angle": 1.34, "curve": [0.346, 0.04, 0.678, 0.41]}, {"time": 14, "angle": 0.65, "curve": [0.34, 0.27, 0.635, 1]}, {"time": 14.5667, "angle": -10.47, "curve": [0.361, 0.02, 0.639, 1]}, {"time": 15.8333, "angle": 1.34, "curve": [0.346, 0.04, 0.678, 0.41]}, {"time": 16, "angle": 0.65}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12, "x": 1, "y": 1, "curve": "stepped"}, {"time": 14, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone17": {"rotate": [{"time": 0, "angle": 0, "curve": [0.345, 0.01, 0.665, 1]}, {"time": 1.0333, "angle": -16.27, "curve": [0.345, 0.01, 0.665, 1]}, {"time": 2, "angle": 0, "curve": [0.345, 0.01, 0.665, 1]}, {"time": 3.0333, "angle": -16.27, "curve": [0.345, 0.01, 0.665, 1]}, {"time": 4, "angle": 0, "curve": [0.345, 0.01, 0.665, 1]}, {"time": 5.0333, "angle": -16.27, "curve": [0.345, 0.01, 0.665, 1]}, {"time": 6, "angle": 0, "curve": [0.345, 0.01, 0.665, 1]}, {"time": 7.0333, "angle": -16.27, "curve": [0.345, 0.01, 0.665, 1]}, {"time": 8, "angle": 0, "curve": [0.345, 0.01, 0.665, 1]}, {"time": 9.0333, "angle": -16.27, "curve": [0.345, 0.01, 0.665, 1]}, {"time": 10, "angle": 0, "curve": [0.345, 0.01, 0.665, 1]}, {"time": 11.0333, "angle": -16.27, "curve": [0.345, 0.01, 0.665, 1]}, {"time": 12, "angle": 0, "curve": [0.345, 0.01, 0.665, 1]}, {"time": 13.0333, "angle": -16.27, "curve": [0.345, 0.01, 0.665, 1]}, {"time": 14, "angle": 0, "curve": [0.345, 0.01, 0.665, 1]}, {"time": 15.0333, "angle": -16.27, "curve": [0.345, 0.01, 0.665, 1]}, {"time": 16, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12, "x": 1, "y": 1, "curve": "stepped"}, {"time": 14, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone18": {"rotate": [{"time": 0, "angle": -1.43, "curve": [0.353, 0.42, 0.657, 1]}, {"time": 0.4667, "angle": -8.18, "curve": [0.409, 0.01, 0.644, 0.98]}, {"time": 1.7, "angle": 0.79, "curve": [0.371, 0.01, 0.697, 0.45]}, {"time": 2, "angle": -1.43, "curve": [0.353, 0.42, 0.657, 1]}, {"time": 2.4667, "angle": -8.18, "curve": [0.409, 0.01, 0.644, 0.98]}, {"time": 3.7, "angle": 0.79, "curve": [0.371, 0.01, 0.697, 0.45]}, {"time": 4, "angle": -1.43, "curve": [0.353, 0.42, 0.657, 1]}, {"time": 4.4667, "angle": -8.18, "curve": [0.409, 0.01, 0.644, 0.98]}, {"time": 5.7, "angle": 0.79, "curve": [0.371, 0.01, 0.697, 0.45]}, {"time": 6, "angle": -1.43, "curve": [0.353, 0.42, 0.657, 1]}, {"time": 6.4667, "angle": -8.18, "curve": [0.409, 0.01, 0.644, 0.98]}, {"time": 7.7, "angle": 0.79, "curve": [0.371, 0.01, 0.697, 0.45]}, {"time": 8, "angle": -1.43, "curve": [0.353, 0.42, 0.657, 1]}, {"time": 8.4667, "angle": -8.18, "curve": [0.409, 0.01, 0.644, 0.98]}, {"time": 9.7, "angle": 0.79, "curve": [0.371, 0.01, 0.697, 0.45]}, {"time": 10, "angle": -1.43, "curve": [0.353, 0.42, 0.657, 1]}, {"time": 10.4667, "angle": -8.18, "curve": [0.409, 0.01, 0.644, 0.98]}, {"time": 11.7, "angle": 0.79, "curve": [0.371, 0.01, 0.697, 0.45]}, {"time": 12, "angle": -1.43, "curve": [0.353, 0.42, 0.657, 1]}, {"time": 12.4667, "angle": -8.18, "curve": [0.409, 0.01, 0.644, 0.98]}, {"time": 13.7, "angle": 0.79, "curve": [0.371, 0.01, 0.697, 0.45]}, {"time": 14, "angle": -1.43, "curve": [0.353, 0.42, 0.657, 1]}, {"time": 14.4667, "angle": -8.18, "curve": [0.409, 0.01, 0.644, 0.98]}, {"time": 15.7, "angle": 0.79, "curve": [0.371, 0.01, 0.697, 0.45]}, {"time": 16, "angle": -1.43}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12, "x": 1, "y": 1, "curve": "stepped"}, {"time": 14, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone19": {"rotate": [{"time": 0, "angle": 8.94, "curve": [0.365, 0.26, 0.629, 1]}, {"time": 0.8333, "angle": -0.9, "curve": [0.403, 0.03, 0.575, 0.98]}, {"time": 1.8, "angle": 9.76, "curve": [0.356, 0.06, 0.687, 0.43]}, {"time": 2, "angle": 8.94, "curve": [0.365, 0.26, 0.629, 1]}, {"time": 2.8333, "angle": -0.9, "curve": [0.403, 0.03, 0.575, 0.98]}, {"time": 3.8, "angle": 9.76, "curve": [0.356, 0.06, 0.687, 0.43]}, {"time": 4, "angle": 8.94, "curve": [0.365, 0.26, 0.629, 1]}, {"time": 4.8333, "angle": -0.9, "curve": [0.403, 0.03, 0.575, 0.98]}, {"time": 5.8, "angle": 9.76, "curve": [0.356, 0.06, 0.687, 0.43]}, {"time": 6, "angle": 8.94, "curve": [0.365, 0.26, 0.629, 1]}, {"time": 6.8333, "angle": -0.9, "curve": [0.403, 0.03, 0.575, 0.98]}, {"time": 7.8, "angle": 9.76, "curve": [0.356, 0.06, 0.687, 0.43]}, {"time": 8, "angle": 8.94, "curve": [0.365, 0.26, 0.629, 1]}, {"time": 8.8333, "angle": -0.9, "curve": [0.403, 0.03, 0.575, 0.98]}, {"time": 9.8, "angle": 9.76, "curve": [0.356, 0.06, 0.687, 0.43]}, {"time": 10, "angle": 8.94, "curve": [0.365, 0.26, 0.629, 1]}, {"time": 10.8333, "angle": -0.9, "curve": [0.403, 0.03, 0.575, 0.98]}, {"time": 11.8, "angle": 9.76, "curve": [0.356, 0.06, 0.687, 0.43]}, {"time": 12, "angle": 8.94, "curve": [0.365, 0.26, 0.629, 1]}, {"time": 12.8333, "angle": -0.9, "curve": [0.403, 0.03, 0.575, 0.98]}, {"time": 13.8, "angle": 9.76, "curve": [0.356, 0.06, 0.687, 0.43]}, {"time": 14, "angle": 8.94, "curve": [0.365, 0.26, 0.629, 1]}, {"time": 14.8333, "angle": -0.9, "curve": [0.403, 0.03, 0.575, 0.98]}, {"time": 15.8, "angle": 9.76, "curve": [0.356, 0.06, 0.687, 0.43]}, {"time": 16, "angle": 8.94}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12, "x": 1, "y": 1, "curve": "stepped"}, {"time": 14, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone20": {"rotate": [{"time": 0, "angle": 0, "curve": [0.382, 0.03, 0.655, 1]}, {"time": 1.2333, "angle": -6.26, "curve": [0.382, 0.03, 0.655, 1]}, {"time": 2, "angle": 0, "curve": [0.382, 0.03, 0.655, 1]}, {"time": 3.2333, "angle": -6.26, "curve": [0.382, 0.03, 0.655, 1]}, {"time": 4, "angle": 0, "curve": [0.382, 0.03, 0.655, 1]}, {"time": 5.2333, "angle": -6.26, "curve": [0.382, 0.03, 0.655, 1]}, {"time": 6, "angle": 0, "curve": [0.382, 0.03, 0.655, 1]}, {"time": 7.2333, "angle": -6.26, "curve": [0.382, 0.03, 0.655, 1]}, {"time": 8, "angle": 0, "curve": [0.382, 0.03, 0.655, 1]}, {"time": 9.2333, "angle": -6.26, "curve": [0.382, 0.03, 0.655, 1]}, {"time": 10, "angle": 0, "curve": [0.382, 0.03, 0.655, 1]}, {"time": 11.2333, "angle": -6.26, "curve": [0.382, 0.03, 0.655, 1]}, {"time": 12, "angle": 0, "curve": [0.382, 0.03, 0.655, 1]}, {"time": 13.2333, "angle": -6.26, "curve": [0.382, 0.03, 0.655, 1]}, {"time": 14, "angle": 0, "curve": [0.382, 0.03, 0.655, 1]}, {"time": 15.2333, "angle": -6.26, "curve": [0.382, 0.03, 0.655, 1]}, {"time": 16, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12, "x": 1, "y": 1, "curve": "stepped"}, {"time": 14, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone22": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4667, "angle": 0, "curve": "stepped"}, {"time": 1.5333, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": "stepped"}, {"time": 1.8333, "angle": 0, "curve": "stepped"}, {"time": 3.5667, "angle": 0, "curve": "stepped"}, {"time": 3.6333, "angle": 0, "curve": "stepped"}, {"time": 3.7667, "angle": 0, "curve": "stepped"}, {"time": 3.9333, "angle": 0, "curve": "stepped"}, {"time": 6.5667, "angle": 0, "curve": "stepped"}, {"time": 6.6333, "angle": 0, "curve": "stepped"}, {"time": 6.7667, "angle": 0, "curve": "stepped"}, {"time": 6.9333, "angle": 0, "curve": "stepped"}, {"time": 10.7, "angle": 0, "curve": "stepped"}, {"time": 10.7667, "angle": 0, "curve": "stepped"}, {"time": 10.9, "angle": 0, "curve": "stepped"}, {"time": 11.0667, "angle": 0, "curve": "stepped"}, {"time": 13.5, "angle": 0, "curve": "stepped"}, {"time": 13.5667, "angle": 0, "curve": "stepped"}, {"time": 13.7, "angle": 0, "curve": "stepped"}, {"time": 13.8667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4667, "x": 0, "y": 0}, {"time": 1.5333, "x": 0.02, "y": -0.5}, {"time": 1.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.5667, "x": 0, "y": 0}, {"time": 3.6333, "x": 0.02, "y": -0.5}, {"time": 3.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.5667, "x": 0, "y": 0}, {"time": 6.6333, "x": 0.02, "y": -0.5}, {"time": 6.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.7, "x": 0, "y": 0}, {"time": 10.7667, "x": 0.02, "y": -0.5}, {"time": 10.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.5, "x": 0, "y": 0}, {"time": 13.5667, "x": 0.02, "y": -0.5}, {"time": 13.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.8667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0, "y": 1, "curve": "stepped"}, {"time": 1.4667, "x": 0, "y": 1}, {"time": 1.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}, {"time": 1.8333, "x": 0, "y": 1, "curve": "stepped"}, {"time": 3.5667, "x": 0, "y": 1}, {"time": 3.6333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.7667, "x": 1, "y": 1}, {"time": 3.9333, "x": 0, "y": 1, "curve": "stepped"}, {"time": 6.5667, "x": 0, "y": 1}, {"time": 6.6333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6.7667, "x": 1, "y": 1}, {"time": 6.9333, "x": 0, "y": 1, "curve": "stepped"}, {"time": 10.7, "x": 0, "y": 1}, {"time": 10.7667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10.9, "x": 1, "y": 1}, {"time": 11.0667, "x": 0, "y": 1, "curve": "stepped"}, {"time": 13.5, "x": 0, "y": 1}, {"time": 13.5667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 13.7, "x": 1, "y": 1}, {"time": 13.8667, "x": 0, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.8667, "x": 0, "y": 0}]}, "bone23": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 4, "angle": 0, "curve": "stepped"}, {"time": 6, "angle": 0, "curve": "stepped"}, {"time": 8, "angle": 0, "curve": "stepped"}, {"time": 10, "angle": 0, "curve": "stepped"}, {"time": 12, "angle": 0, "curve": "stepped"}, {"time": 14, "angle": 0, "curve": "stepped"}, {"time": 16, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 1, "x": -0.08, "y": 0.56, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 2, "x": 0, "y": 0, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 3, "x": -0.08, "y": 0.56, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 4, "x": 0, "y": 0, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 5, "x": -0.08, "y": 0.56, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 6, "x": 0, "y": 0, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 7, "x": -0.08, "y": 0.56, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 8, "x": 0, "y": 0, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 9, "x": -0.08, "y": 0.56, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 10, "x": 0, "y": 0, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 11, "x": -0.08, "y": 0.56, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 12, "x": 0, "y": 0, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 13, "x": -0.08, "y": 0.56, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 14, "x": 0, "y": 0, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 15, "x": -0.08, "y": 0.56, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 1, "x": 1.122, "y": 1.122, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 2, "x": 1, "y": 1, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 3, "x": 1.122, "y": 1.122, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 4, "x": 1, "y": 1, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 5, "x": 1.122, "y": 1.122, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 6, "x": 1, "y": 1, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 7, "x": 1.122, "y": 1.122, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 8, "x": 1, "y": 1, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 9, "x": 1.122, "y": 1.122, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 10, "x": 1, "y": 1, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 11, "x": 1.122, "y": 1.122, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 12, "x": 1, "y": 1, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 13, "x": 1.122, "y": 1.122, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 14, "x": 1, "y": 1, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 15, "x": 1.122, "y": 1.122, "curve": [0.345, 0.01, 0.676, 1]}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone24": {"rotate": [{"time": 0, "angle": 1.42, "curve": [0.366, 0, 0.687, 1]}, {"time": 1.3333, "angle": -1.2, "curve": [0.366, 0, 0.687, 1]}, {"time": 2.6667, "angle": 1.42, "curve": [0.366, 0, 0.687, 1]}, {"time": 4, "angle": -1.2, "curve": [0.366, 0, 0.687, 1]}, {"time": 5.3333, "angle": 1.42, "curve": [0.366, 0, 0.687, 1]}, {"time": 6.6667, "angle": -1.2, "curve": [0.366, 0, 0.687, 1]}, {"time": 8, "angle": 1.42, "curve": [0.366, 0, 0.687, 1]}, {"time": 9.3333, "angle": -1.2, "curve": [0.366, 0, 0.687, 1]}, {"time": 10.6667, "angle": 1.42, "curve": [0.366, 0, 0.687, 1]}, {"time": 12, "angle": -1.2, "curve": [0.366, 0, 0.687, 1]}, {"time": 13.3333, "angle": 1.42, "curve": [0.366, 0, 0.687, 1]}, {"time": 14.6667, "angle": -1.2, "curve": [0.366, 0, 0.687, 1]}, {"time": 16, "angle": 1.42}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.366, 0, 0.687, 1]}, {"time": 1.3333, "x": 0, "y": -1.33, "curve": [0.366, 0, 0.687, 1]}, {"time": 2.6667, "x": 0, "y": 0, "curve": [0.366, 0, 0.687, 1]}, {"time": 4, "x": 0, "y": -1.33, "curve": [0.366, 0, 0.687, 1]}, {"time": 5.3333, "x": 0, "y": 0, "curve": [0.366, 0, 0.687, 1]}, {"time": 6.6667, "x": 0, "y": -1.33, "curve": [0.366, 0, 0.687, 1]}, {"time": 8, "x": 0, "y": 0, "curve": [0.366, 0, 0.687, 1]}, {"time": 9.3333, "x": 0, "y": -1.33, "curve": [0.366, 0, 0.687, 1]}, {"time": 10.6667, "x": 0, "y": 0, "curve": [0.366, 0, 0.687, 1]}, {"time": 12, "x": 0, "y": -1.33, "curve": [0.366, 0, 0.687, 1]}, {"time": 13.3333, "x": 0, "y": 0, "curve": [0.366, 0, 0.687, 1]}, {"time": 14.6667, "x": 0, "y": -1.33, "curve": [0.366, 0, 0.687, 1]}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.366, 0, 0.687, 1]}, {"time": 1.3333, "x": 0.986, "y": 0.986, "curve": [0.366, 0, 0.687, 1]}, {"time": 2.6667, "x": 1, "y": 1, "curve": [0.366, 0, 0.687, 1]}, {"time": 4, "x": 0.986, "y": 0.986, "curve": [0.366, 0, 0.687, 1]}, {"time": 5.3333, "x": 1, "y": 1, "curve": [0.366, 0, 0.687, 1]}, {"time": 6.6667, "x": 0.986, "y": 0.986, "curve": [0.366, 0, 0.687, 1]}, {"time": 8, "x": 1, "y": 1, "curve": [0.366, 0, 0.687, 1]}, {"time": 9.3333, "x": 0.986, "y": 0.986, "curve": [0.366, 0, 0.687, 1]}, {"time": 10.6667, "x": 1, "y": 1, "curve": [0.366, 0, 0.687, 1]}, {"time": 12, "x": 0.986, "y": 0.986, "curve": [0.366, 0, 0.687, 1]}, {"time": 13.3333, "x": 1, "y": 1, "curve": [0.366, 0, 0.687, 1]}, {"time": 14.6667, "x": 0.986, "y": 0.986, "curve": [0.366, 0, 0.687, 1]}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone25": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 5.3333, "angle": 0, "curve": "stepped"}, {"time": 8, "angle": 0, "curve": "stepped"}, {"time": 10.6667, "angle": 0, "curve": "stepped"}, {"time": 13.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 13.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0}]}, "bone26": {"rotate": [{"time": 0, "angle": 0.37, "curve": [0.332, 0.46, 0.643, 1]}, {"time": 0.7, "angle": 2.2, "curve": [0.388, 0, 0.655, 1]}, {"time": 2.1333, "angle": -0.71, "curve": [0.371, 0, 0.693, 0.47]}, {"time": 2.6667, "angle": 0.37, "curve": [0.332, 0.46, 0.643, 1]}, {"time": 3.3667, "angle": 2.2, "curve": [0.388, 0, 0.655, 1]}, {"time": 4.8, "angle": -0.71, "curve": [0.371, 0, 0.693, 0.47]}, {"time": 5.3333, "angle": 0.37, "curve": [0.332, 0.46, 0.643, 1]}, {"time": 6.0333, "angle": 2.2, "curve": [0.388, 0, 0.655, 1]}, {"time": 7.4667, "angle": -0.71, "curve": [0.371, 0, 0.693, 0.47]}, {"time": 8, "angle": 0.37, "curve": [0.332, 0.46, 0.643, 1]}, {"time": 8.7, "angle": 2.2, "curve": [0.388, 0, 0.655, 1]}, {"time": 10.1333, "angle": -0.71, "curve": [0.371, 0, 0.693, 0.47]}, {"time": 10.6667, "angle": 0.37, "curve": [0.332, 0.46, 0.643, 1]}, {"time": 11.3667, "angle": 2.2, "curve": [0.388, 0, 0.655, 1]}, {"time": 12.8, "angle": -0.71, "curve": [0.371, 0, 0.693, 0.47]}, {"time": 13.3333, "angle": 0.37, "curve": [0.332, 0.46, 0.643, 1]}, {"time": 14.0333, "angle": 2.2, "curve": [0.388, 0, 0.655, 1]}, {"time": 15.4667, "angle": -0.71, "curve": [0.371, 0, 0.693, 0.47]}, {"time": 16, "angle": 0.37}], "translate": [{"time": 0, "x": 0.26, "y": -0.2, "curve": [0.314, 0.41, 0.566, 1]}, {"time": 0.9, "x": -0.88, "y": 1.23, "curve": [0.43, 0, 0.581, 1]}, {"time": 2.2, "x": 0.61, "y": -0.64, "curve": [0.403, 0, 0.716, 0.43]}, {"time": 2.6667, "x": 0.26, "y": -0.2, "curve": [0.314, 0.41, 0.566, 1]}, {"time": 3.5667, "x": -0.88, "y": 1.23, "curve": [0.43, 0, 0.581, 1]}, {"time": 4.8667, "x": 0.61, "y": -0.64, "curve": [0.403, 0, 0.716, 0.43]}, {"time": 5.3333, "x": 0.26, "y": -0.2, "curve": [0.314, 0.41, 0.566, 1]}, {"time": 6.2333, "x": -0.88, "y": 1.23, "curve": [0.43, 0, 0.581, 1]}, {"time": 7.5333, "x": 0.61, "y": -0.64, "curve": [0.403, 0, 0.716, 0.43]}, {"time": 8, "x": 0.26, "y": -0.2, "curve": [0.314, 0.41, 0.566, 1]}, {"time": 8.9, "x": -0.88, "y": 1.23, "curve": [0.43, 0, 0.581, 1]}, {"time": 10.2, "x": 0.61, "y": -0.64, "curve": [0.403, 0, 0.716, 0.43]}, {"time": 10.6667, "x": 0.26, "y": -0.2, "curve": [0.314, 0.41, 0.566, 1]}, {"time": 11.5667, "x": -0.88, "y": 1.23, "curve": [0.43, 0, 0.581, 1]}, {"time": 12.8667, "x": 0.61, "y": -0.64, "curve": [0.403, 0, 0.716, 0.43]}, {"time": 13.3333, "x": 0.26, "y": -0.2, "curve": [0.314, 0.41, 0.566, 1]}, {"time": 14.2333, "x": -0.88, "y": 1.23, "curve": [0.43, 0, 0.581, 1]}, {"time": 15.5333, "x": 0.61, "y": -0.64, "curve": [0.403, 0, 0.716, 0.43]}, {"time": 16, "x": 0.26, "y": -0.2}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 13.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone27": {"rotate": [{"time": 0, "angle": 0.87, "curve": [0.322, 0.46, 0.638, 0.99]}, {"time": 0.7333, "angle": 4.46, "curve": [0.356, 0.02, 0.639, 0.99]}, {"time": 2.1333, "angle": -1.39, "curve": [0.355, 0.02, 0.679, 0.48]}, {"time": 2.6667, "angle": 0.87, "curve": [0.322, 0.46, 0.638, 0.99]}, {"time": 3.4, "angle": 4.46, "curve": [0.356, 0.02, 0.639, 0.99]}, {"time": 4.8, "angle": -1.39, "curve": [0.355, 0.02, 0.679, 0.48]}, {"time": 5.3333, "angle": 0.87, "curve": [0.322, 0.46, 0.638, 0.99]}, {"time": 6.0667, "angle": 4.46, "curve": [0.356, 0.02, 0.639, 0.99]}, {"time": 7.4667, "angle": -1.39, "curve": [0.355, 0.02, 0.679, 0.48]}, {"time": 8, "angle": 0.87, "curve": [0.322, 0.46, 0.638, 0.99]}, {"time": 8.7333, "angle": 4.46, "curve": [0.356, 0.02, 0.639, 0.99]}, {"time": 10.1333, "angle": -1.39, "curve": [0.355, 0.02, 0.679, 0.48]}, {"time": 10.6667, "angle": 0.87, "curve": [0.322, 0.46, 0.638, 0.99]}, {"time": 11.4, "angle": 4.46, "curve": [0.356, 0.02, 0.639, 0.99]}, {"time": 12.8, "angle": -1.39, "curve": [0.355, 0.02, 0.679, 0.48]}, {"time": 13.3333, "angle": 0.87, "curve": [0.322, 0.46, 0.638, 0.99]}, {"time": 14.0667, "angle": 4.46, "curve": [0.356, 0.02, 0.639, 0.99]}, {"time": 15.4667, "angle": -1.39, "curve": [0.355, 0.02, 0.679, 0.48]}, {"time": 16, "angle": 0.87}], "translate": [{"time": 0, "x": 0.61, "y": -0.41, "curve": [0.31, 0.44, 0.593, 0.98]}, {"time": 0.8333, "x": -0.1, "y": 0.34, "curve": [0.409, 0, 0.602, 0.98]}, {"time": 2.1, "x": 0.97, "y": -0.78, "curve": [0.396, 0, 0.707, 0.45]}, {"time": 2.6667, "x": 0.61, "y": -0.41, "curve": [0.31, 0.44, 0.593, 0.98]}, {"time": 3.5, "x": -0.1, "y": 0.34, "curve": [0.409, 0, 0.602, 0.98]}, {"time": 4.7667, "x": 0.97, "y": -0.78, "curve": [0.396, 0, 0.707, 0.45]}, {"time": 5.3333, "x": 0.61, "y": -0.41, "curve": [0.31, 0.44, 0.593, 0.98]}, {"time": 6.1667, "x": -0.1, "y": 0.34, "curve": [0.409, 0, 0.602, 0.98]}, {"time": 7.4333, "x": 0.97, "y": -0.78, "curve": [0.396, 0, 0.707, 0.45]}, {"time": 8, "x": 0.61, "y": -0.41, "curve": [0.31, 0.44, 0.593, 0.98]}, {"time": 8.8333, "x": -0.1, "y": 0.34, "curve": [0.409, 0, 0.602, 0.98]}, {"time": 10.1, "x": 0.97, "y": -0.78, "curve": [0.396, 0, 0.707, 0.45]}, {"time": 10.6667, "x": 0.61, "y": -0.41, "curve": [0.31, 0.44, 0.593, 0.98]}, {"time": 11.5, "x": -0.1, "y": 0.34, "curve": [0.409, 0, 0.602, 0.98]}, {"time": 12.7667, "x": 0.97, "y": -0.78, "curve": [0.396, 0, 0.707, 0.45]}, {"time": 13.3333, "x": 0.61, "y": -0.41, "curve": [0.31, 0.44, 0.593, 0.98]}, {"time": 14.1667, "x": -0.1, "y": 0.34, "curve": [0.409, 0, 0.602, 0.98]}, {"time": 15.4333, "x": 0.97, "y": -0.78, "curve": [0.396, 0, 0.707, 0.45]}, {"time": 16, "x": 0.61, "y": -0.41}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 13.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone28": {"rotate": [{"time": 0, "angle": 0, "curve": [0.366, 0.02, 0.66, 1]}, {"time": 1.3, "angle": 8.4, "curve": [0.366, 0.02, 0.66, 1]}, {"time": 2.6667, "angle": 0, "curve": [0.366, 0.02, 0.66, 1]}, {"time": 3.9667, "angle": 8.4, "curve": [0.366, 0.02, 0.66, 1]}, {"time": 5.3333, "angle": 0, "curve": [0.366, 0.02, 0.66, 1]}, {"time": 6.6333, "angle": 8.4, "curve": [0.366, 0.02, 0.66, 1]}, {"time": 8, "angle": 0, "curve": [0.366, 0.02, 0.66, 1]}, {"time": 9.3, "angle": 8.4, "curve": [0.366, 0.02, 0.66, 1]}, {"time": 10.6667, "angle": 0, "curve": [0.366, 0.02, 0.66, 1]}, {"time": 11.9667, "angle": 8.4, "curve": [0.366, 0.02, 0.66, 1]}, {"time": 13.3333, "angle": 0, "curve": [0.366, 0.02, 0.66, 1]}, {"time": 14.6333, "angle": 8.4, "curve": [0.366, 0.02, 0.66, 1]}, {"time": 16, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 13.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone29": {"rotate": [{"time": 0, "angle": -4.59, "curve": [0.306, 0.54, 0.624, 0.99]}, {"time": 0.5333, "angle": -6.85, "curve": [0.388, 0.02, 0.618, 0.99]}, {"time": 1.8333, "angle": 0.01, "curve": [0.391, 0.02, 0.686, 0.57]}, {"time": 2.6667, "angle": -4.59, "curve": [0.306, 0.54, 0.624, 0.99]}, {"time": 3.2, "angle": -6.85, "curve": [0.388, 0.02, 0.618, 0.99]}, {"time": 4.5, "angle": 0.01, "curve": [0.391, 0.02, 0.686, 0.57]}, {"time": 5.3333, "angle": -4.59, "curve": [0.306, 0.54, 0.624, 0.99]}, {"time": 5.8667, "angle": -6.85, "curve": [0.388, 0.02, 0.618, 0.99]}, {"time": 7.1667, "angle": 0.01, "curve": [0.391, 0.02, 0.686, 0.57]}, {"time": 8, "angle": -4.59, "curve": [0.306, 0.54, 0.624, 0.99]}, {"time": 8.5333, "angle": -6.85, "curve": [0.388, 0.02, 0.618, 0.99]}, {"time": 9.8333, "angle": 0.01, "curve": [0.391, 0.02, 0.686, 0.57]}, {"time": 10.6667, "angle": -4.59, "curve": [0.306, 0.54, 0.624, 0.99]}, {"time": 11.2, "angle": -6.85, "curve": [0.388, 0.02, 0.618, 0.99]}, {"time": 12.5, "angle": 0.01, "curve": [0.391, 0.02, 0.686, 0.57]}, {"time": 13.3333, "angle": -4.59, "curve": [0.306, 0.54, 0.624, 0.99]}, {"time": 13.8667, "angle": -6.85, "curve": [0.388, 0.02, 0.618, 0.99]}, {"time": 15.1667, "angle": 0.01, "curve": [0.391, 0.02, 0.686, 0.57]}, {"time": 16, "angle": -4.59}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 13.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone30": {"rotate": [{"time": 0, "angle": 0.59, "curve": [0.338, 0.41, 0.643, 0.98]}, {"time": 0.8333, "angle": -7.96, "curve": [0.388, 0.02, 0.655, 0.98]}, {"time": 2.2, "angle": 3.79, "curve": [0.366, 0.03, 0.691, 0.46]}, {"time": 2.6667, "angle": 0.59, "curve": [0.338, 0.41, 0.643, 0.98]}, {"time": 3.5, "angle": -7.96, "curve": [0.388, 0.02, 0.655, 0.98]}, {"time": 4.8667, "angle": 3.79, "curve": [0.366, 0.03, 0.691, 0.46]}, {"time": 5.3333, "angle": 0.59, "curve": [0.338, 0.41, 0.643, 0.98]}, {"time": 6.1667, "angle": -7.96, "curve": [0.388, 0.02, 0.655, 0.98]}, {"time": 7.5333, "angle": 3.79, "curve": [0.366, 0.03, 0.691, 0.46]}, {"time": 8, "angle": 0.59, "curve": [0.338, 0.41, 0.643, 0.98]}, {"time": 8.8333, "angle": -7.96, "curve": [0.388, 0.02, 0.655, 0.98]}, {"time": 10.2, "angle": 3.79, "curve": [0.366, 0.03, 0.691, 0.46]}, {"time": 10.6667, "angle": 0.59, "curve": [0.338, 0.41, 0.643, 0.98]}, {"time": 11.5, "angle": -7.96, "curve": [0.388, 0.02, 0.655, 0.98]}, {"time": 12.8667, "angle": 3.79, "curve": [0.366, 0.03, 0.691, 0.46]}, {"time": 13.3333, "angle": 0.59, "curve": [0.338, 0.41, 0.643, 0.98]}, {"time": 14.1667, "angle": -7.96, "curve": [0.388, 0.02, 0.655, 0.98]}, {"time": 15.5333, "angle": 3.79, "curve": [0.366, 0.03, 0.691, 0.46]}, {"time": 16, "angle": 0.59}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 13.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone31": {"rotate": [{"time": 0, "angle": -0.65, "curve": [0.325, 0.42, 0.645, 0.99]}, {"time": 0.8, "angle": 6.02, "curve": [0.345, 0, 0.644, 0.99]}, {"time": 2.2, "angle": -3.61, "curve": [0.346, 0, 0.674, 0.44]}, {"time": 2.6667, "angle": -0.65, "curve": [0.325, 0.42, 0.645, 0.99]}, {"time": 3.4667, "angle": 6.02, "curve": [0.345, 0, 0.644, 0.99]}, {"time": 4.8667, "angle": -3.61, "curve": [0.346, 0, 0.674, 0.44]}, {"time": 5.3333, "angle": -0.65, "curve": [0.325, 0.42, 0.645, 0.99]}, {"time": 6.1333, "angle": 6.02, "curve": [0.345, 0, 0.644, 0.99]}, {"time": 7.5333, "angle": -3.61, "curve": [0.346, 0, 0.674, 0.44]}, {"time": 8, "angle": -0.65, "curve": [0.325, 0.42, 0.645, 0.99]}, {"time": 8.8, "angle": 6.02, "curve": [0.345, 0, 0.644, 0.99]}, {"time": 10.2, "angle": -3.61, "curve": [0.346, 0, 0.674, 0.44]}, {"time": 10.6667, "angle": -0.65, "curve": [0.325, 0.42, 0.645, 0.99]}, {"time": 11.4667, "angle": 6.02, "curve": [0.345, 0, 0.644, 0.99]}, {"time": 12.8667, "angle": -3.61, "curve": [0.346, 0, 0.674, 0.44]}, {"time": 13.3333, "angle": -0.65, "curve": [0.325, 0.42, 0.645, 0.99]}, {"time": 14.1333, "angle": 6.02, "curve": [0.345, 0, 0.644, 0.99]}, {"time": 15.5333, "angle": -3.61, "curve": [0.346, 0, 0.674, 0.44]}, {"time": 16, "angle": -0.65}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 13.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone32": {"rotate": [{"time": 0, "angle": -6.55, "curve": [0.379, 0.18, 0.594, 1]}, {"time": 1.2667, "angle": 11.25, "curve": [0.414, 0.01, 0.602, 1]}, {"time": 2.5333, "angle": -7.05, "curve": [0.352, 0.03, 0.684, 0.39]}, {"time": 2.6667, "angle": -6.55, "curve": [0.379, 0.18, 0.594, 1]}, {"time": 3.9333, "angle": 11.25, "curve": [0.414, 0.01, 0.602, 1]}, {"time": 5.2, "angle": -7.05, "curve": [0.352, 0.03, 0.684, 0.39]}, {"time": 5.3333, "angle": -6.55, "curve": [0.379, 0.18, 0.594, 1]}, {"time": 6.6, "angle": 11.25, "curve": [0.414, 0.01, 0.602, 1]}, {"time": 7.8667, "angle": -7.05, "curve": [0.352, 0.03, 0.684, 0.39]}, {"time": 8, "angle": -6.55, "curve": [0.379, 0.18, 0.594, 1]}, {"time": 9.2667, "angle": 11.25, "curve": [0.414, 0.01, 0.602, 1]}, {"time": 10.5333, "angle": -7.05, "curve": [0.352, 0.03, 0.684, 0.39]}, {"time": 10.6667, "angle": -6.55, "curve": [0.379, 0.18, 0.594, 1]}, {"time": 11.9333, "angle": 11.25, "curve": [0.414, 0.01, 0.602, 1]}, {"time": 13.2, "angle": -7.05, "curve": [0.352, 0.03, 0.684, 0.39]}, {"time": 13.3333, "angle": -6.55, "curve": [0.379, 0.18, 0.594, 1]}, {"time": 14.6, "angle": 11.25, "curve": [0.414, 0.01, 0.602, 1]}, {"time": 15.8667, "angle": -7.05, "curve": [0.352, 0.03, 0.684, 0.39]}, {"time": 16, "angle": -6.55}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 13.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone33": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.265, "angle": 0, "curve": "stepped"}, {"time": 1.6316, "angle": 0, "curve": "stepped"}, {"time": 4.8667, "angle": 0, "curve": "stepped"}, {"time": 5.2333, "angle": 0, "curve": "stepped"}, {"time": 8.5604, "angle": 0, "curve": "stepped"}, {"time": 8.9271, "angle": 0, "curve": "stepped"}, {"time": 11.8315, "angle": 0, "curve": "stepped"}, {"time": 12.1982, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.265, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6316, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.5604, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.9271, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.8315, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.1982, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0, "y": 1, "curve": "stepped"}, {"time": 1.265, "x": 0, "y": 1}, {"time": 1.3316, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4316, "x": 1, "y": 1}, {"time": 1.6316, "x": 0, "y": 1, "curve": "stepped"}, {"time": 4.8667, "x": 0, "y": 1}, {"time": 4.9333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.0333, "x": 1, "y": 1}, {"time": 5.2333, "x": 0, "y": 1, "curve": "stepped"}, {"time": 8.5604, "x": 0, "y": 1}, {"time": 8.6271, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8.7271, "x": 1, "y": 1}, {"time": 8.9271, "x": 0, "y": 1, "curve": "stepped"}, {"time": 11.8315, "x": 0, "y": 1}, {"time": 11.8982, "x": 1, "y": 1, "curve": "stepped"}, {"time": 11.9982, "x": 1, "y": 1}, {"time": 12.1982, "x": 0, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.265, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6316, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.5604, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.9271, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.8315, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.1982, "x": 0, "y": 0}]}, "bone34": {"rotate": [{"time": 0, "angle": -3.01, "curve": [0.379, 0.15, 0.591, 1]}, {"time": 1.0667, "angle": 6.5, "curve": [0.403, 0.02, 0.597, 1]}, {"time": 2.5667, "angle": -3.28, "curve": [0.347, 0.08, 0.679, 0.42]}, {"time": 2.6667, "angle": -3.01, "curve": [0.379, 0.15, 0.591, 1]}, {"time": 3.7333, "angle": 6.5, "curve": [0.403, 0.02, 0.597, 1]}, {"time": 5.2333, "angle": -3.28, "curve": [0.347, 0.08, 0.679, 0.42]}, {"time": 5.3333, "angle": -3.01, "curve": [0.379, 0.15, 0.591, 1]}, {"time": 6.4, "angle": 6.5, "curve": [0.403, 0.02, 0.597, 1]}, {"time": 7.9, "angle": -3.28, "curve": [0.347, 0.08, 0.679, 0.42]}, {"time": 8, "angle": -3.01, "curve": [0.379, 0.15, 0.591, 1]}, {"time": 9.0667, "angle": 6.5, "curve": [0.403, 0.02, 0.597, 1]}, {"time": 10.5667, "angle": -3.28, "curve": [0.347, 0.08, 0.679, 0.42]}, {"time": 10.6667, "angle": -3.01, "curve": [0.379, 0.15, 0.591, 1]}, {"time": 11.7333, "angle": 6.5, "curve": [0.403, 0.02, 0.597, 1]}, {"time": 13.2333, "angle": -3.28, "curve": [0.347, 0.08, 0.679, 0.42]}, {"time": 13.3333, "angle": -3.01, "curve": [0.379, 0.15, 0.591, 1]}, {"time": 14.4, "angle": 6.5, "curve": [0.403, 0.02, 0.597, 1]}, {"time": 15.9, "angle": -3.28, "curve": [0.347, 0.08, 0.679, 0.42]}, {"time": 16, "angle": -3.01}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 13.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone35": {"rotate": [{"time": 0, "angle": -7.21, "curve": [0.346, 0.18, 0.647, 1]}, {"time": 1.1, "angle": 11.99, "curve": [0.356, 0, 0.65, 1]}, {"time": 2.5333, "angle": -7.81, "curve": [0.339, 0, 0.672, 0.36]}, {"time": 2.6667, "angle": -7.21, "curve": [0.346, 0.18, 0.647, 1]}, {"time": 3.7667, "angle": 11.99, "curve": [0.356, 0, 0.65, 1]}, {"time": 5.2, "angle": -7.81, "curve": [0.339, 0, 0.672, 0.36]}, {"time": 5.3333, "angle": -7.21, "curve": [0.346, 0.18, 0.647, 1]}, {"time": 6.4333, "angle": 11.99, "curve": [0.356, 0, 0.65, 1]}, {"time": 7.8667, "angle": -7.81, "curve": [0.339, 0, 0.672, 0.36]}, {"time": 8, "angle": -7.21, "curve": [0.346, 0.18, 0.647, 1]}, {"time": 9.1, "angle": 11.99, "curve": [0.356, 0, 0.65, 1]}, {"time": 10.5333, "angle": -7.81, "curve": [0.339, 0, 0.672, 0.36]}, {"time": 10.6667, "angle": -7.21, "curve": [0.346, 0.18, 0.647, 1]}, {"time": 11.7667, "angle": 11.99, "curve": [0.356, 0, 0.65, 1]}, {"time": 13.2, "angle": -7.81, "curve": [0.339, 0, 0.672, 0.36]}, {"time": 13.3333, "angle": -7.21, "curve": [0.346, 0.18, 0.647, 1]}, {"time": 14.4333, "angle": 11.99, "curve": [0.356, 0, 0.65, 1]}, {"time": 15.8667, "angle": -7.81, "curve": [0.339, 0, 0.672, 0.36]}, {"time": 16, "angle": -7.21}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 13.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone36": {"rotate": [{"time": 0, "angle": -0.73, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 1.3667, "angle": 0.43, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 2.6667, "angle": -0.73, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 4.0333, "angle": 0.43, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 5.3333, "angle": -0.73, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 6.7, "angle": 0.43, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 8, "angle": -0.73, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 9.3667, "angle": 0.43, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 10.6667, "angle": -0.73, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 12.0333, "angle": 0.43, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 13.3333, "angle": -0.73, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 14.7, "angle": 0.43, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 16, "angle": -0.73}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 1.3667, "x": 0.79, "y": -0.01, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 2.6667, "x": 0, "y": 0, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 4.0333, "x": 0.79, "y": -0.01, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 5.3333, "x": 0, "y": 0, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 6.7, "x": 0.79, "y": -0.01, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 8, "x": 0, "y": 0, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 9.3667, "x": 0.79, "y": -0.01, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 10.6667, "x": 0, "y": 0, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 12.0333, "x": 0.79, "y": -0.01, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 13.3333, "x": 0, "y": 0, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 14.7, "x": 0.79, "y": -0.01, "curve": [0.345, 0.02, 0.708, 1]}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 13.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone37": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 5.3333, "angle": 0, "curve": "stepped"}, {"time": 8, "angle": 0, "curve": "stepped"}, {"time": 10.6667, "angle": 0, "curve": "stepped"}, {"time": 13.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 13.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0}]}, "bone38": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 22.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.0667, "angle": 22.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.7333, "angle": 22.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 8, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.4, "angle": 22.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 12.0667, "angle": 22.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 13.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 14.7333, "angle": 22.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 16, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 13.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 16, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 16, "x": 0, "y": 0}]}, "bone39": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 5.3333, "angle": 0, "curve": "stepped"}, {"time": 8, "angle": 0, "curve": "stepped"}, {"time": 10.6667, "angle": 0, "curve": "stepped"}, {"time": 13.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 13.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.3333, "x": 0, "y": 0}]}, "bone40": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.1, "angle": 0, "curve": "stepped"}, {"time": 1.1667, "angle": 0, "curve": "stepped"}, {"time": 1.9667, "angle": 0, "curve": "stepped"}, {"time": 2.0333, "angle": 0, "curve": "stepped"}, {"time": 2.1, "angle": 0, "curve": "stepped"}, {"time": 2.1667, "angle": 0, "curve": "stepped"}, {"time": 2.4667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.4667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 3.121, "y": 3.121, "curve": "stepped"}, {"time": 1.1, "x": 3.121, "y": 3.121}, {"time": 1.1667, "x": 0.748, "y": 0.748}, {"time": 1.2333, "x": 1.084, "y": 1.084}, {"time": 1.3333, "x": 0.957, "y": 0.957}, {"time": 1.5, "x": 1.025, "y": 1.025}, {"time": 1.9667, "x": 1, "y": 1}, {"time": 2, "x": 1.095, "y": 1.095}, {"time": 2.0333, "x": 1, "y": 1}, {"time": 2.0667, "x": 1.095, "y": 1.095}, {"time": 2.1, "x": 1, "y": 1}, {"time": 2.1333, "x": 1.095, "y": 1.095}, {"time": 2.1667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.4667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.4667, "x": 0, "y": 0}]}, "bone21": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.5, "angle": 0, "curve": "stepped"}, {"time": 2.7, "angle": 0, "curve": "stepped"}, {"time": 2.9, "angle": 0, "curve": "stepped"}, {"time": 3.1, "angle": 0, "curve": "stepped"}, {"time": 3.3, "angle": 0, "curve": "stepped"}, {"time": 3.5, "angle": 0, "curve": "stepped"}, {"time": 4.5, "angle": 0, "curve": "stepped"}, {"time": 4.7, "angle": 0, "curve": "stepped"}, {"time": 4.9, "angle": 0, "curve": "stepped"}, {"time": 5.1, "angle": 0, "curve": "stepped"}, {"time": 5.3, "angle": 0, "curve": "stepped"}, {"time": 5.5, "angle": 0, "curve": "stepped"}, {"time": 6.6, "angle": 0, "curve": "stepped"}, {"time": 6.8, "angle": 0, "curve": "stepped"}, {"time": 7, "angle": 0, "curve": "stepped"}, {"time": 7.2, "angle": 0, "curve": "stepped"}, {"time": 7.4, "angle": 0, "curve": "stepped"}, {"time": 7.6, "angle": 0, "curve": "stepped"}, {"time": 8.8333, "angle": 0, "curve": "stepped"}, {"time": 9.0333, "angle": 0, "curve": "stepped"}, {"time": 9.2333, "angle": 0, "curve": "stepped"}, {"time": 9.4333, "angle": 0, "curve": "stepped"}, {"time": 9.6333, "angle": 0, "curve": "stepped"}, {"time": 9.8333, "angle": 0, "curve": "stepped"}, {"time": 10.4333, "angle": 0, "curve": "stepped"}, {"time": 10.6333, "angle": 0, "curve": "stepped"}, {"time": 10.8333, "angle": 0, "curve": "stepped"}, {"time": 11.0333, "angle": 0, "curve": "stepped"}, {"time": 11.2333, "angle": 0, "curve": "stepped"}, {"time": 11.4333, "angle": 0, "curve": "stepped"}, {"time": 12.1, "angle": 0, "curve": "stepped"}, {"time": 12.3, "angle": 0, "curve": "stepped"}, {"time": 12.5, "angle": 0, "curve": "stepped"}, {"time": 12.7, "angle": 0, "curve": "stepped"}, {"time": 12.9, "angle": 0, "curve": "stepped"}, {"time": 13.1, "angle": 0, "curve": "stepped"}, {"time": 13.8, "angle": 0, "curve": "stepped"}, {"time": 14, "angle": 0, "curve": "stepped"}, {"time": 14.2, "angle": 0, "curve": "stepped"}, {"time": 14.4, "angle": 0, "curve": "stepped"}, {"time": 14.6, "angle": 0, "curve": "stepped"}, {"time": 14.8, "angle": 0, "curve": "stepped"}, {"time": 15.7, "angle": 0, "curve": "stepped"}, {"time": 15.7667, "angle": 0, "curve": "stepped"}, {"time": 15.8333, "angle": 0, "curve": "stepped"}, {"time": 15.9, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.9, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.5, "x": 1, "y": 1}, {"time": 2.6, "x": 1.135, "y": 1.135}, {"time": 2.7, "x": 1, "y": 1}, {"time": 2.8, "x": 1.135, "y": 1.135}, {"time": 2.9, "x": 1, "y": 1}, {"time": 3, "x": 1.135, "y": 1.135}, {"time": 3.1, "x": 1, "y": 1}, {"time": 3.2, "x": 1.135, "y": 1.135}, {"time": 3.3, "x": 1, "y": 1}, {"time": 3.4, "x": 1.135, "y": 1.135}, {"time": 3.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.5, "x": 1, "y": 1}, {"time": 4.6, "x": 1.135, "y": 1.135}, {"time": 4.7, "x": 1, "y": 1}, {"time": 4.8, "x": 1.135, "y": 1.135}, {"time": 4.9, "x": 1, "y": 1}, {"time": 5, "x": 1.135, "y": 1.135}, {"time": 5.1, "x": 1, "y": 1}, {"time": 5.2, "x": 1.135, "y": 1.135}, {"time": 5.3, "x": 1, "y": 1}, {"time": 5.4, "x": 1.135, "y": 1.135}, {"time": 5.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6.6, "x": 1, "y": 1}, {"time": 6.7, "x": 1.135, "y": 1.135}, {"time": 6.8, "x": 1, "y": 1}, {"time": 6.9, "x": 1.135, "y": 1.135}, {"time": 7, "x": 1, "y": 1}, {"time": 7.1, "x": 1.135, "y": 1.135}, {"time": 7.2, "x": 1, "y": 1}, {"time": 7.3, "x": 1.135, "y": 1.135}, {"time": 7.4, "x": 1, "y": 1}, {"time": 7.5, "x": 1.135, "y": 1.135}, {"time": 7.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8.8333, "x": 1, "y": 1}, {"time": 8.9333, "x": 1.135, "y": 1.135}, {"time": 9.0333, "x": 1, "y": 1}, {"time": 9.1333, "x": 1.135, "y": 1.135}, {"time": 9.2333, "x": 1, "y": 1}, {"time": 9.3333, "x": 1.135, "y": 1.135}, {"time": 9.4333, "x": 1, "y": 1}, {"time": 9.5333, "x": 1.135, "y": 1.135}, {"time": 9.6333, "x": 1, "y": 1}, {"time": 9.7333, "x": 1.135, "y": 1.135}, {"time": 9.8333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10.4333, "x": 1, "y": 1}, {"time": 10.5333, "x": 1.135, "y": 1.135}, {"time": 10.6333, "x": 1, "y": 1}, {"time": 10.7333, "x": 1.135, "y": 1.135}, {"time": 10.8333, "x": 1, "y": 1}, {"time": 10.9333, "x": 1.135, "y": 1.135}, {"time": 11.0333, "x": 1, "y": 1}, {"time": 11.1333, "x": 1.135, "y": 1.135}, {"time": 11.2333, "x": 1, "y": 1}, {"time": 11.3333, "x": 1.135, "y": 1.135}, {"time": 11.4333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12.1, "x": 1, "y": 1}, {"time": 12.2, "x": 1.135, "y": 1.135}, {"time": 12.3, "x": 1, "y": 1}, {"time": 12.4, "x": 1.135, "y": 1.135}, {"time": 12.5, "x": 1, "y": 1}, {"time": 12.6, "x": 1.135, "y": 1.135}, {"time": 12.7, "x": 1, "y": 1}, {"time": 12.8, "x": 1.135, "y": 1.135}, {"time": 12.9, "x": 1, "y": 1}, {"time": 13, "x": 1.135, "y": 1.135}, {"time": 13.1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 13.8, "x": 1, "y": 1}, {"time": 13.9, "x": 1.135, "y": 1.135}, {"time": 14, "x": 1, "y": 1}, {"time": 14.1, "x": 1.135, "y": 1.135}, {"time": 14.2, "x": 1, "y": 1}, {"time": 14.3, "x": 1.135, "y": 1.135}, {"time": 14.4, "x": 1, "y": 1}, {"time": 14.5, "x": 1.135, "y": 1.135}, {"time": 14.6, "x": 1, "y": 1}, {"time": 14.7, "x": 1.135, "y": 1.135}, {"time": 14.8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 15.7, "x": 1, "y": 1}, {"time": 15.7333, "x": 1.102, "y": 1.102}, {"time": 15.7667, "x": 1, "y": 1}, {"time": 15.8, "x": 1.102, "y": 1.102}, {"time": 15.8333, "x": 1, "y": 1}, {"time": 15.8667, "x": 1.102, "y": 1.102}, {"time": 15.9, "x": 1, "y": 1}, {"time": 15.9667, "x": 1.801, "y": 1.801}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 11.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 13.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 14.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.9, "x": 0, "y": 0}]}, "fx-1": {"scale": [{"time": 12.5, "x": 1.017, "y": 1.017}]}}, "deform": {"default": {"2 1": {"2 1": [{"time": 0, "offset": 16, "vertices": [-0.08991, 1.1525, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.23577, 1.13171, 0, 0, -0.23577, 1.13171, -0.08991, 1.1525, -0.23577, 1.13171, -0.08991, 1.1525, -0.08991, 1.1525, -0.41261, 1.98049, -0.15735, 2.01688], "curve": [0.388, 0, 0.639, 1]}, {"time": 0.9667, "offset": 16, "vertices": [0.06019, -0.91152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.55628, -2.00052, 0, 0, 0.53542, -1.92536, 0.13153, -1.99405, 0.53542, -1.92536, 0.13153, -1.99405, 0.13153, -1.99405, -0.06828, 0.24625, -0.01672, 0.25506], "curve": [0.388, 0, 0.639, 1]}, {"time": 2, "offset": 16, "vertices": [-0.08991, 1.1525, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.23577, 1.13171, 0, 0, -0.23577, 1.13171, -0.08991, 1.1525, -0.23577, 1.13171, -0.08991, 1.1525, -0.08991, 1.1525, -0.41261, 1.98049, -0.15735, 2.01688], "curve": [0.388, 0, 0.639, 1]}, {"time": 2.9667, "offset": 16, "vertices": [0.06019, -0.91152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.55628, -2.00052, 0, 0, 0.53542, -1.92536, 0.13153, -1.99405, 0.53542, -1.92536, 0.13153, -1.99405, 0.13153, -1.99405, -0.06828, 0.24625, -0.01672, 0.25506], "curve": [0.388, 0, 0.639, 1]}, {"time": 4, "offset": 16, "vertices": [-0.08991, 1.1525, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.23577, 1.13171, 0, 0, -0.23577, 1.13171, -0.08991, 1.1525, -0.23577, 1.13171, -0.08991, 1.1525, -0.08991, 1.1525, -0.41261, 1.98049, -0.15735, 2.01688], "curve": [0.388, 0, 0.639, 1]}, {"time": 4.9667, "offset": 16, "vertices": [0.06019, -0.91152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.55628, -2.00052, 0, 0, 0.53542, -1.92536, 0.13153, -1.99405, 0.53542, -1.92536, 0.13153, -1.99405, 0.13153, -1.99405, -0.06828, 0.24625, -0.01672, 0.25506], "curve": [0.388, 0, 0.639, 1]}, {"time": 6, "offset": 16, "vertices": [-0.08991, 1.1525, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.23577, 1.13171, 0, 0, -0.23577, 1.13171, -0.08991, 1.1525, -0.23577, 1.13171, -0.08991, 1.1525, -0.08991, 1.1525, -0.41261, 1.98049, -0.15735, 2.01688], "curve": [0.388, 0, 0.639, 1]}, {"time": 6.9667, "offset": 16, "vertices": [0.06019, -0.91152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.55628, -2.00052, 0, 0, 0.53542, -1.92536, 0.13153, -1.99405, 0.53542, -1.92536, 0.13153, -1.99405, 0.13153, -1.99405, -0.06828, 0.24625, -0.01672, 0.25506], "curve": [0.388, 0, 0.639, 1]}, {"time": 8, "offset": 16, "vertices": [-0.08991, 1.1525, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.23577, 1.13171, 0, 0, -0.23577, 1.13171, -0.08991, 1.1525, -0.23577, 1.13171, -0.08991, 1.1525, -0.08991, 1.1525, -0.41261, 1.98049, -0.15735, 2.01688], "curve": [0.388, 0, 0.639, 1]}, {"time": 8.9667, "offset": 16, "vertices": [0.06019, -0.91152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.55628, -2.00052, 0, 0, 0.53542, -1.92536, 0.13153, -1.99405, 0.53542, -1.92536, 0.13153, -1.99405, 0.13153, -1.99405, -0.06828, 0.24625, -0.01672, 0.25506], "curve": [0.388, 0, 0.639, 1]}, {"time": 10, "offset": 16, "vertices": [-0.08991, 1.1525, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.23577, 1.13171, 0, 0, -0.23577, 1.13171, -0.08991, 1.1525, -0.23577, 1.13171, -0.08991, 1.1525, -0.08991, 1.1525, -0.41261, 1.98049, -0.15735, 2.01688], "curve": [0.388, 0, 0.639, 1]}, {"time": 10.9667, "offset": 16, "vertices": [0.06019, -0.91152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.55628, -2.00052, 0, 0, 0.53542, -1.92536, 0.13153, -1.99405, 0.53542, -1.92536, 0.13153, -1.99405, 0.13153, -1.99405, -0.06828, 0.24625, -0.01672, 0.25506], "curve": [0.388, 0, 0.639, 1]}, {"time": 12, "offset": 16, "vertices": [-0.08991, 1.1525, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.23577, 1.13171, 0, 0, -0.23577, 1.13171, -0.08991, 1.1525, -0.23577, 1.13171, -0.08991, 1.1525, -0.08991, 1.1525, -0.41261, 1.98049, -0.15735, 2.01688], "curve": [0.388, 0, 0.639, 1]}, {"time": 12.9667, "offset": 16, "vertices": [0.06019, -0.91152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.55628, -2.00052, 0, 0, 0.53542, -1.92536, 0.13153, -1.99405, 0.53542, -1.92536, 0.13153, -1.99405, 0.13153, -1.99405, -0.06828, 0.24625, -0.01672, 0.25506], "curve": [0.388, 0, 0.639, 1]}, {"time": 14, "offset": 16, "vertices": [-0.08991, 1.1525, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.23577, 1.13171, 0, 0, -0.23577, 1.13171, -0.08991, 1.1525, -0.23577, 1.13171, -0.08991, 1.1525, -0.08991, 1.1525, -0.41261, 1.98049, -0.15735, 2.01688], "curve": [0.388, 0, 0.639, 1]}, {"time": 14.9667, "offset": 16, "vertices": [0.06019, -0.91152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.55628, -2.00052, 0, 0, 0.53542, -1.92536, 0.13153, -1.99405, 0.53542, -1.92536, 0.13153, -1.99405, 0.13153, -1.99405, -0.06828, 0.24625, -0.01672, 0.25506], "curve": [0.388, 0, 0.639, 1]}, {"time": 16, "offset": 16, "vertices": [-0.08991, 1.1525, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.23577, 1.13171, 0, 0, -0.23577, 1.13171, -0.08991, 1.1525, -0.23577, 1.13171, -0.08991, 1.1525, -0.08991, 1.1525, -0.41261, 1.98049, -0.15735, 2.01688]}]}, "2 14": {"2 14": [{"time": 0, "curve": [0.35, 0.02, 0.708, 1]}, {"time": 1.0333, "vertices": [0.1947, -0.96754, 0.1947, -0.96754, 0, 0, 0.06775, -1.19382, 0.12194, -2.14886, 0.12194, -2.14886, 0.06775, -1.19382, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.99433, -2.73953, 0.99433, -2.73953, 0.81542, -2.0506, 1.22559, -1.8875, 0.40525, -2.21377, 0.99433, -2.73953, 1.10996, -2.31351, 0.99433, -2.73953, 0.99433, -2.73953, 1.22559, -1.8875], "curve": [0.35, 0.02, 0.708, 1]}, {"time": 2, "curve": [0.35, 0.02, 0.708, 1]}, {"time": 3.0333, "vertices": [0.1947, -0.96754, 0.1947, -0.96754, 0, 0, 0.06775, -1.19382, 0.12194, -2.14886, 0.12194, -2.14886, 0.06775, -1.19382, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.99433, -2.73953, 0.99433, -2.73953, 0.81542, -2.0506, 1.22559, -1.8875, 0.40525, -2.21377, 0.99433, -2.73953, 1.10996, -2.31351, 0.99433, -2.73953, 0.99433, -2.73953, 1.22559, -1.8875], "curve": [0.35, 0.02, 0.708, 1]}, {"time": 4, "curve": [0.35, 0.02, 0.708, 1]}, {"time": 5.0333, "vertices": [0.1947, -0.96754, 0.1947, -0.96754, 0, 0, 0.06775, -1.19382, 0.12194, -2.14886, 0.12194, -2.14886, 0.06775, -1.19382, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.99433, -2.73953, 0.99433, -2.73953, 0.81542, -2.0506, 1.22559, -1.8875, 0.40525, -2.21377, 0.99433, -2.73953, 1.10996, -2.31351, 0.99433, -2.73953, 0.99433, -2.73953, 1.22559, -1.8875], "curve": [0.35, 0.02, 0.708, 1]}, {"time": 6, "curve": [0.35, 0.02, 0.708, 1]}, {"time": 7.0333, "vertices": [0.1947, -0.96754, 0.1947, -0.96754, 0, 0, 0.06775, -1.19382, 0.12194, -2.14886, 0.12194, -2.14886, 0.06775, -1.19382, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.99433, -2.73953, 0.99433, -2.73953, 0.81542, -2.0506, 1.22559, -1.8875, 0.40525, -2.21377, 0.99433, -2.73953, 1.10996, -2.31351, 0.99433, -2.73953, 0.99433, -2.73953, 1.22559, -1.8875], "curve": [0.35, 0.02, 0.708, 1]}, {"time": 8, "curve": [0.35, 0.02, 0.708, 1]}, {"time": 9.0333, "vertices": [0.1947, -0.96754, 0.1947, -0.96754, 0, 0, 0.06775, -1.19382, 0.12194, -2.14886, 0.12194, -2.14886, 0.06775, -1.19382, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.99433, -2.73953, 0.99433, -2.73953, 0.81542, -2.0506, 1.22559, -1.8875, 0.40525, -2.21377, 0.99433, -2.73953, 1.10996, -2.31351, 0.99433, -2.73953, 0.99433, -2.73953, 1.22559, -1.8875], "curve": [0.35, 0.02, 0.708, 1]}, {"time": 10, "curve": [0.35, 0.02, 0.708, 1]}, {"time": 11.0333, "vertices": [0.1947, -0.96754, 0.1947, -0.96754, 0, 0, 0.06775, -1.19382, 0.12194, -2.14886, 0.12194, -2.14886, 0.06775, -1.19382, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.99433, -2.73953, 0.99433, -2.73953, 0.81542, -2.0506, 1.22559, -1.8875, 0.40525, -2.21377, 0.99433, -2.73953, 1.10996, -2.31351, 0.99433, -2.73953, 0.99433, -2.73953, 1.22559, -1.8875], "curve": [0.35, 0.02, 0.708, 1]}, {"time": 12, "curve": [0.35, 0.02, 0.708, 1]}, {"time": 13.0333, "vertices": [0.1947, -0.96754, 0.1947, -0.96754, 0, 0, 0.06775, -1.19382, 0.12194, -2.14886, 0.12194, -2.14886, 0.06775, -1.19382, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.99433, -2.73953, 0.99433, -2.73953, 0.81542, -2.0506, 1.22559, -1.8875, 0.40525, -2.21377, 0.99433, -2.73953, 1.10996, -2.31351, 0.99433, -2.73953, 0.99433, -2.73953, 1.22559, -1.8875], "curve": [0.35, 0.02, 0.708, 1]}, {"time": 14, "curve": [0.35, 0.02, 0.708, 1]}, {"time": 15.0333, "vertices": [0.1947, -0.96754, 0.1947, -0.96754, 0, 0, 0.06775, -1.19382, 0.12194, -2.14886, 0.12194, -2.14886, 0.06775, -1.19382, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.99433, -2.73953, 0.99433, -2.73953, 0.81542, -2.0506, 1.22559, -1.8875, 0.40525, -2.21377, 0.99433, -2.73953, 1.10996, -2.31351, 0.99433, -2.73953, 0.99433, -2.73953, 1.22559, -1.8875], "curve": [0.35, 0.02, 0.708, 1]}, {"time": 16}]}}}}}}