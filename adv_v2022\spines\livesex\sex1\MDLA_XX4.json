{"skeleton": {"hash": "hjjLSNv7ftrPDjJtoAGC75us97E", "spine": "3.7.94", "width": 855.17, "height": 538.39, "images": "./images/", "audio": "D:/work"}, "bones": [{"name": "root", "scaleX": 0.33, "scaleY": 0.33}, {"name": "main", "parent": "root", "color": "fa0000ff"}, {"name": "bone2", "parent": "root", "x": 449.03, "y": 143.78, "color": "fa0000ff"}, {"name": "bone3", "parent": "bone2", "length": 264.51, "rotation": 114.12, "x": -11.01, "y": 18.88}, {"name": "bone4", "parent": "bone3", "length": 113.54, "rotation": 23.79, "x": 264.51}, {"name": "bone5", "parent": "bone2", "length": 210.35, "rotation": -62.86, "x": 14.5, "y": -39.86, "color": "fa0000ff"}, {"name": "bone6", "parent": "bone5", "length": 532.52, "rotation": -4.74, "x": 209.47, "y": 51.09, "scaleX": 1.059, "color": "fa0000ff"}, {"name": "bone7", "parent": "bone6", "length": 425.69, "rotation": 90.66, "x": 541.24, "y": 17.13, "scaleX": 1.098, "color": "fa0000ff"}, {"name": "bone8", "parent": "bone7", "length": 167.69, "rotation": -97.2, "x": 447.18, "y": -5.73, "color": "fa0000ff"}, {"name": "bone9", "parent": "bone5", "length": 295.68, "rotation": -12.66, "x": 242.03, "y": -144.33, "color": "fa0000ff"}, {"name": "bone10", "parent": "bone9", "length": 421.25, "rotation": 82.17, "x": 272.59, "y": 23.83, "color": "fa0000ff"}, {"name": "bone11", "parent": "bone3", "length": 319.09, "rotation": -167.37, "x": 297.63, "y": -144.6, "color": "39ff00ff"}, {"name": "bone12", "parent": "bone11", "length": 357.16, "rotation": -57.98, "x": 321.28, "y": 0.36, "color": "39ff00ff"}, {"name": "bone13", "parent": "bone12", "length": 189.21, "rotation": -25.02, "x": 368.14, "y": -14.19, "color": "39ff00ff"}, {"name": "bone14", "parent": "bone4", "length": 143.51, "rotation": -40.6, "x": 103.05, "y": 57.04}, {"name": "bone15", "parent": "bone14", "x": 203.76, "y": 40.01}, {"name": "bone16", "parent": "bone15", "length": 102.2, "rotation": -88.61, "x": 10.61, "y": -32.02}, {"name": "bone17", "parent": "bone16", "length": 61.62, "rotation": 19.94, "x": 101.32, "y": 4.69}, {"name": "bone18", "parent": "bone15", "length": 53.89, "rotation": -162.78, "x": -49.17, "y": -49.09, "color": "ff00ebff"}, {"name": "bone19", "parent": "bone18", "length": 61.74, "rotation": -32.81, "x": 53.89, "color": "ff00ebff"}, {"name": "bone20", "parent": "bone19", "length": 39.15, "rotation": -9.46, "x": 65.2, "y": -1.31, "color": "ff00ebff"}, {"name": "bone21", "parent": "bone15", "length": 60.96, "rotation": -163.02, "x": -84.54, "y": -83.88, "color": "0051ffff"}, {"name": "bone22", "parent": "bone21", "length": 52.98, "rotation": -10.17, "x": 61.6, "y": -0.33, "color": "0051ffff"}, {"name": "bone23", "parent": "bone22", "length": 64.36, "rotation": 5.82, "x": 53.26, "y": -1.6, "color": "0051ffff"}, {"name": "bone24", "parent": "bone15", "length": 72.24, "rotation": -110.3, "x": -36.4, "y": -116.55}, {"name": "bone25", "parent": "bone15", "length": 74.59, "rotation": -127.7, "x": -91.67, "y": -159.17}, {"name": "g3", "parent": "main", "length": 254.56, "rotation": 93.31, "x": 103.8, "y": 39.6, "color": "fa0000ff"}, {"name": "bone27", "parent": "g3", "length": 82.58, "rotation": -21.51, "x": 284.99, "y": 17.37, "color": "fa0000ff"}, {"name": "bone28", "parent": "g3", "x": 182.61, "y": -147.21, "color": "fa0000ff"}, {"name": "bone29", "parent": "g3", "x": 184.6, "y": 29.52, "color": "fa0000ff"}, {"name": "bone30", "parent": "bone27", "length": 98.79, "rotation": 62.16, "x": 99.71, "y": -29.81}, {"name": "bone31", "parent": "bone30", "length": 94.08, "rotation": 70.52, "x": 201.3, "y": 5.42}, {"name": "bone32", "parent": "bone31", "length": 75.64, "rotation": 34.33, "x": 29.74, "y": 37.54}, {"name": "bone33", "parent": "bone30", "length": 40.94, "rotation": 132.68, "x": 16.42, "y": -32.37, "color": "48fa00ff"}, {"name": "bone34", "parent": "bone33", "length": 35.74, "rotation": 19.49, "x": 40.94, "color": "48fa00ff"}, {"name": "bone35", "parent": "bone34", "length": 35.82, "rotation": 17.63, "x": 37.38, "y": 0.56, "color": "48fa00ff"}, {"name": "bone36", "parent": "bone27", "length": 334.87, "rotation": 167.17, "x": -43.48, "y": 103.61, "color": "fa0000ff"}, {"name": "bone37", "parent": "g3", "length": 204.52, "rotation": 155.17, "x": -24, "y": 12.01, "color": "fa0000ff"}, {"name": "bone38", "parent": "g3", "length": 86.84, "rotation": -154.1, "x": 134.12, "y": -109.09, "color": "004bffff"}, {"name": "bone39", "parent": "bone38", "length": 144.53, "rotation": 129.29, "x": 95.54, "y": -1.13, "color": "004bffff"}, {"name": "g_nau", "parent": "main", "length": 249.88, "rotation": -135.85, "x": 393.32, "y": -104.36, "scaleX": 0.982, "scaleY": 0.982, "color": "ff00e8ff"}, {"name": "bone41", "parent": "g_nau", "length": 216.84, "rotation": -69.31, "x": 351.03, "y": -43.19, "scaleX": 1.018, "scaleY": 1.018, "color": "ff00e8ff"}, {"name": "bone42", "parent": "bone41", "length": 102.95, "rotation": 8.83, "x": 225.11, "y": 21.49, "color": "ff00e8ff"}, {"name": "bone43", "parent": "g_nau", "rotation": -4.19, "x": 26.69, "y": -76.58, "scaleX": 1.018, "scaleY": 1.018, "color": "ff00e8ff"}, {"name": "bone44", "parent": "g_nau", "length": 507.42, "rotation": 30.8, "x": 21.51, "y": 67.04, "scaleX": 1.029, "scaleY": 1.008, "color": "ff00e8ff"}, {"name": "bone45", "parent": "bone44", "length": 438.37, "rotation": 123.77, "x": 467.32, "y": 55.01, "scaleX": 1.073, "color": "fa0000ff"}, {"name": "bone46", "parent": "bone45", "length": 142.91, "rotation": -95.36, "x": 464.63, "y": -2.65, "scaleX": 1.218, "scaleY": 0.954, "color": "fa0000ff"}, {"name": "bone47", "parent": "bone41", "length": 300.36, "rotation": 82.37, "x": 213.38, "y": -32.17, "scaleX": 0.975, "scaleY": 0.975, "color": "ff00e8ff"}, {"name": "bone48", "parent": "bone47", "length": 254.92, "rotation": -100.15, "x": 327.45, "y": -18.66, "scaleX": 1.025, "scaleY": 1.025, "color": "ff00e8ff"}, {"name": "bone49", "parent": "bone48", "length": 83.86, "rotation": -45.7, "x": 256.33, "y": -0.05, "color": "ff00e8ff"}, {"name": "bone50", "parent": "bone49", "length": 41.98, "rotation": 60.24, "x": 83.86, "color": "ff00e8ff"}, {"name": "bone51", "parent": "bone42", "length": 94.43, "rotation": -19.05, "x": 102.73, "y": 95.62, "color": "ff00e8ff"}, {"name": "bone52", "parent": "bone51", "length": 88.23, "rotation": -45.08, "x": 132.38, "y": -33.5, "color": "ff00e8ff"}, {"name": "bone53", "parent": "bone52", "length": 91.25, "rotation": -14.87, "x": 88.23, "color": "ff00e8ff"}, {"name": "bone54", "parent": "bone51", "length": 74.41, "rotation": 170.37, "x": 108.15, "y": -55.16, "color": "0022ffff"}, {"name": "bone55", "parent": "bone54", "length": 77.74, "rotation": -38.61, "x": 74.41, "color": "0022ffff"}, {"name": "bone56", "parent": "bone55", "length": 74.79, "rotation": -15.1, "x": 77.67, "y": -0.66, "color": "0022ffff"}, {"name": "bone57", "parent": "bone54", "length": 52.72, "rotation": -18.76, "x": -30.61, "y": -50.39, "color": "0022ffff"}, {"name": "bone58", "parent": "bone54", "length": 37.8, "rotation": -30.72, "x": -48.98, "y": -95.13, "color": "0022ffff"}, {"name": "bone59", "parent": "bone51", "length": 78.01, "rotation": 156.76, "x": 48.29, "y": -112.36, "color": "17ff00ff"}, {"name": "bone60", "parent": "bone59", "length": 71.45, "rotation": -19.55, "x": 79.69, "y": -4.92, "color": "17ff00ff"}, {"name": "bone61", "parent": "bone60", "length": 77.18, "rotation": 9.42, "x": 73.13, "y": -1.71, "color": "17ff00ff"}, {"name": "bone62", "parent": "bone61", "length": 77.38, "rotation": 27.24, "x": 77.18, "color": "17ff00ff"}, {"name": "bone63", "parent": "bone51", "length": 58.34, "rotation": -163.27, "x": 62.01, "y": -148.73, "color": "00ff14ff"}, {"name": "bone64", "parent": "bone63", "length": 78.84, "rotation": 9.9, "x": 65.4, "y": 4.87, "color": "00ff14ff"}, {"name": "bone65", "parent": "bone64", "length": 82.38, "rotation": -14.09, "x": 83.85, "y": -1.92, "color": "00ff14ff"}, {"name": "bone66", "parent": "bone41", "rotation": 5.81, "x": 134.01, "y": 106.79, "color": "ffde00ff"}, {"name": "bone67", "parent": "bone66", "x": -16.24, "y": 116.59, "color": "ffde00ff"}, {"name": "bone68", "parent": "g_nau", "length": 215.61, "rotation": 16.44, "x": 330.24, "y": 32.22, "scaleX": 1.114, "scaleY": 1.018, "color": "ff00e8ff"}, {"name": "main2", "parent": "main", "length": 189.22, "rotation": 141.7, "x": -424.01, "y": -152.16, "color": "46ff00ff"}, {"name": "main3", "parent": "main2", "length": 160.94, "rotation": -10.76, "x": 189.22, "color": "46ff00ff"}, {"name": "main4", "parent": "main3", "length": 61.6, "rotation": -13.92, "x": 160.94, "color": "46ff00ff"}, {"name": "main5", "parent": "main3", "rotation": -13.92, "x": 14.06, "y": -149.43, "color": "fa0000ff"}, {"name": "main6", "parent": "main3", "x": 98.6, "y": 75.22, "color": "fa0000ff"}, {"name": "main7", "parent": "main4", "length": 79.21, "rotation": 10.52, "x": 67.84, "y": -34.05, "color": "46ff00ff"}, {"name": "main8", "parent": "main7", "length": 70.63, "rotation": 179.09, "x": 141.05, "y": -94.27, "color": "ff5d00ff"}, {"name": "main9", "parent": "main8", "length": 73.17, "rotation": -17.05, "x": 70.63, "color": "ff5d00ff"}, {"name": "main10", "parent": "main9", "length": 65.99, "rotation": -16.25, "x": 73.17, "color": "ff5d00ff"}, {"name": "main11", "parent": "main10", "length": 51.05, "rotation": -31.05, "x": 66.75, "y": -0.04, "color": "ff5d00ff"}, {"name": "main12", "parent": "main7", "length": 69.09, "rotation": 146.28, "x": 186.03, "y": -10.59, "color": "001bffff"}, {"name": "main13", "parent": "main12", "length": 76.94, "rotation": 5.93, "x": 69.09, "color": "001bffff"}, {"name": "main14", "parent": "main13", "length": 63.37, "rotation": -17.38, "x": 78.32, "y": -1.01, "color": "001bffff"}, {"name": "main15", "parent": "main14", "length": 36.62, "rotation": -8.14, "x": 63.37, "color": "001bffff"}, {"name": "main16", "parent": "main7", "length": 62.82, "rotation": -179.97, "x": 8.96, "y": 85.85, "color": "004effff"}, {"name": "main17", "parent": "main16", "length": 81.79, "rotation": 7.81, "x": 62.68, "y": 1.07, "color": "004effff"}, {"name": "main18", "parent": "main17", "length": 60.61, "rotation": -29.24, "x": 81.79, "color": "004effff"}, {"name": "main19", "parent": "main18", "length": 56.05, "rotation": -12.23, "x": 60.61, "color": "004effff"}, {"name": "main20", "parent": "main2", "length": 502.97, "rotation": 3.48, "x": -249.18, "y": 88.28, "color": "001bffff"}, {"name": "main21", "parent": "main20", "length": 468.91, "rotation": 160.39, "x": 512.47, "y": 25.17, "color": "001bffff"}, {"name": "main22", "parent": "main21", "length": 213.37, "rotation": -13.41, "x": 487.1, "y": -0.57, "color": "001bffff"}, {"name": "main23", "parent": "main2", "length": 413.35, "rotation": -39.28, "x": -190.08, "y": -63.05, "color": "0018ffff"}, {"name": "main24", "parent": "main23", "length": 412.03, "rotation": -141.76, "x": 433.53, "y": 2.62, "color": "0018ffff"}, {"name": "main25", "parent": "main24", "length": 155.82, "rotation": 7.61, "x": 414.99, "y": 2.43, "color": "0018ffff"}, {"name": "bone26", "parent": "bone5", "length": 173.11, "rotation": -105.14, "x": 173.89, "y": -105.92, "color": "00ffe3ff"}, {"name": "bone40", "parent": "bone3", "length": 304.19, "rotation": 142.57, "x": 93.92, "y": 75.66}, {"name": "boy1", "parent": "root", "x": 800.91, "y": -592.72, "color": "ff3f00ff"}, {"name": "boy2", "parent": "root", "x": 551.23, "y": -456.02, "color": "ff3f00ff"}, {"name": "boy3", "parent": "root", "x": 1010.58, "y": -352.55, "color": "ff3f00ff"}, {"name": "boy4", "parent": "root", "x": 1264.77, "y": -402.13, "color": "ff3f00ff"}, {"name": "nau1", "parent": "root", "x": 211.68, "y": -707.96, "color": "ff3f00ff"}, {"name": "nau2", "parent": "root", "x": 64.52, "y": -583.38, "color": "ff3f00ff"}, {"name": "main26", "parent": "main7", "length": 89.8, "rotation": 148.86, "x": -17.71, "y": 134.54, "color": "e200ffff"}, {"name": "main27", "parent": "main26", "length": 114.5, "rotation": 4.09, "x": 89.8, "color": "e200ffff"}, {"name": "main28", "parent": "main27", "length": 78.7, "rotation": 2.37, "x": 114.5, "color": "e200ffff"}, {"name": "main29", "parent": "main28", "length": 88.44, "rotation": 3.01, "x": 82.56, "y": 0.83, "color": "e200ffff"}, {"name": "main30", "parent": "main29", "length": 74.03, "rotation": -1.52, "x": 88.44, "color": "e200ffff"}, {"name": "main31", "parent": "main30", "length": 74.34, "rotation": -11.13, "x": 74.03, "color": "e200ffff"}, {"name": "main32", "parent": "main3", "length": 196.55, "rotation": -19.7, "x": -123.27, "y": -116.54, "color": "000effff"}, {"name": "main33", "parent": "main32", "length": 107.53, "rotation": 21.95, "x": 198.29, "y": 4.47, "color": "000effff"}, {"name": "boy5", "parent": "root", "x": 263.13, "y": -76.63, "color": "ff3f00ff"}, {"name": "nau3", "parent": "root", "x": 760.83, "y": -491.05, "color": "ff3f00ff"}, {"name": "nau4", "parent": "root", "x": -452.76, "y": -269.55, "color": "ff3f00ff"}, {"name": "bone", "parent": "bone2", "x": -346.37, "y": -803.18, "color": "0011ffff"}, {"name": "bone69", "parent": "root", "x": 189.29, "y": 45.85, "scaleX": 0.841, "scaleY": 0.841}, {"name": "bone71", "parent": "root", "x": -557.31, "y": 441.98, "scaleX": 0.502, "scaleY": 0.502}, {"name": "bone70", "parent": "root", "x": -640.64, "y": 34.05, "scaleX": 0.582, "scaleY": 0.582}, {"name": "g4", "parent": "g3", "length": 108.39, "rotation": 90.28, "x": 284.11, "y": 17.56, "color": "fa0000ff"}, {"name": "bone72", "parent": "bone44", "length": 438.37, "rotation": 131.82, "x": 408.18, "y": -148.33, "scaleX": 0.884, "scaleY": 0.907, "color": "fa0000ff"}, {"name": "bone73", "parent": "bone72", "length": 142.91, "rotation": -55.95, "x": 438.37, "scaleX": 1.218, "scaleY": 0.954, "color": "fa0000ff"}], "slots": [{"name": "light2", "bone": "bone69", "color": "ffffffbc", "attachment": "light2"}, {"name": "light3", "bone": "bone70", "color": "ffffff87", "attachment": "light2"}, {"name": "shadow", "bone": "bone", "attachment": "shadow"}, {"name": "g2_tay2", "bone": "main", "attachment": "g2_tay2"}, {"name": "g2_body", "bone": "main", "attachment": "g2_body"}, {"name": "g2_tay", "bone": "bone36", "attachment": "g2_tay"}, {"name": "g2_tocsau", "bone": "main", "attachment": "g2_tocsau"}, {"name": "g3_tocsau", "bone": "main", "attachment": "g3_tocsau"}, {"name": "boy_chansau", "bone": "bone10", "attachment": "boy_chansau"}, {"name": "boy_taysau", "bone": "bone40", "attachment": "boy_taysau"}, {"name": "g3_taysau", "bone": "main", "attachment": "g3_taysau"}, {"name": "g3_duisau", "bone": "main23", "attachment": "g3_duisau"}, {"name": "g3_body", "bone": "main", "attachment": "g3_body"}, {"name": "g3_duitruoc", "bone": "main20", "attachment": "g3_duitruoc"}, {"name": "nau_chantruoc3", "bone": "bone72", "dark": "000000", "attachment": "nau_chantruoc1"}, {"name": "g3_chantruoc", "bone": "main", "attachment": "g3_chantruoc"}, {"name": "g3_chansau", "bone": "main", "attachment": "g3_chansau"}, {"name": "g3_face", "bone": "main", "attachment": "g3_face"}, {"name": "g3_toc1", "bone": "main", "attachment": "g3_toc1"}, {"name": "g3_toctruoc2", "bone": "main", "attachment": "g3_toctruoc2"}, {"name": "g3_toctruoc1", "bone": "main", "attachment": "g3_toctruoc1"}, {"name": "boy_duisau", "bone": "bone9", "attachment": "boy_duisau"}, {"name": "boy_body", "bone": "main", "attachment": "boy_body"}, {"name": "g2_head", "bone": "root", "attachment": "g2_head"}, {"name": "boy_ciu", "bone": "bone26", "attachment": "boy_ciu"}, {"name": "boy_duit<PERSON><PERSON>", "bone": "bone6", "attachment": "boy_duit<PERSON><PERSON>"}, {"name": "boy_chantruoc", "bone": "main", "attachment": "boy_chantruoc"}, {"name": "boy_tocsau2", "bone": "bone17", "attachment": "boy_to<PERSON>au"}, {"name": "boy_to<PERSON>au", "bone": "bone17"}, {"name": "boy_head", "bone": "main", "attachment": "boy_head"}, {"name": "boy_toctruoc2", "bone": "main", "attachment": "boy_toctruoc2"}, {"name": "boy_octruoc1", "bone": "main", "attachment": "boy_octruoc1"}, {"name": "nau_tocsau2", "bone": "main", "attachment": "nau_tocsau2"}, {"name": "nau_chansau", "bone": "bone68", "attachment": "nau_chansau"}, {"name": "nau_chantruoc1", "bone": "bone45", "attachment": "nau_chantruoc1"}, {"name": "nau_body", "bone": "main", "attachment": "nau_body"}, {"name": "nau_nguc", "bone": "main", "attachment": "nau_nguc"}, {"name": "nau_taytruoc", "bone": "main", "attachment": "nau_taytruoc"}, {"name": "nau_head", "bone": "main", "attachment": "nau_head"}, {"name": "nau_toctruoc2", "bone": "main", "attachment": "nau_toctruoc2"}, {"name": "nau_toctruoc1", "bone": "main", "attachment": "nau_toctruoc1"}, {"name": "boy_ta<PERSON><PERSON>oc", "bone": "main", "attachment": "boy_ta<PERSON><PERSON>oc"}], "ik": [{"name": "boy1", "order": 0, "bones": ["bone6"], "target": "boy1"}, {"name": "boy2", "order": 1, "bones": ["bone9"], "target": "boy2"}, {"name": "boy3", "order": 2, "bones": ["bone10"], "target": "boy3"}, {"name": "boy4", "order": 3, "bones": ["bone7"], "target": "boy4"}, {"name": "boy5", "order": 6, "bones": ["bone40"], "target": "boy5"}, {"name": "nau1", "order": 4, "bones": ["bone44"], "target": "nau1"}, {"name": "nau2", "order": 5, "bones": ["bone68"], "target": "nau2"}, {"name": "nau3", "order": 7, "bones": ["bone45"], "target": "nau3"}, {"name": "nau4", "order": 8, "bones": ["bone48"], "target": "nau4"}], "skins": {"default": {"boy_body": {"boy_body": {"type": "mesh", "uvs": [0, 0.09562, 0.52564, 0, 1, 0.31513, 1, 0.62926, 0.76889, 1, 0, 1, 0.16638, 0.62359, 0, 0.48355, 0.19984, 0.26722, 0.17358, 0.41607, 0.30599, 0.60288, 0.20298, 0.87991, 0.49483, 0.44443, 0.53427, 0.63129], "triangles": [8, 0, 1, 8, 7, 0, 12, 8, 1, 12, 1, 2, 9, 8, 12, 9, 7, 8, 10, 9, 12, 6, 7, 9, 6, 9, 10, 12, 2, 3, 13, 12, 3, 10, 12, 13, 11, 6, 10, 11, 10, 13, 5, 6, 11, 4, 13, 3, 4, 11, 13, 5, 11, 4], "vertices": [1, 4, 153.47, 53.19, 1, 3, 2, -122.77, 448.9, 0.00023, 3, 435.33, -88.89, 0.04502, 4, 127.27, -144.51, 0.95475, 4, 5, -232.81, 230.2, 0.00054, 2, 106.52, 292.23, 0.08914, 3, 193.49, -225.91, 0.82891, 4, -147.36, -185.13, 0.08141, 3, 5, -17.48, 255.41, 0.26076, 2, 227.17, 112.11, 0.30043, 3, -20.27, -254.71, 0.43881, 2, 5, 306.89, 51.11, 0.98333, 2, 193.33, -269.74, 0.01667, 1, 5, 199.95, -177.38, 1, 4, 5, -56.21, -85.01, 0.19669, 2, -93.43, -8.71, 0.07674, 3, 11.5, 86.14, 0.72574, 4, -204.93, 171.59, 0.00083, 2, 3, 116.2, 134.52, 0.92817, 4, -89.83, 178.96, 0.07183, 2, 3, 260.34, 47.11, 0.30852, 4, 13.09, 45.45, 0.69148, 2, 3, 156.87, 67.77, 0.90109, 4, -75.96, 102.03, 0.09891, 3, 5, -64.44, -33.73, 0.05168, 2, -51.54, 22.01, 0.02186, 3, 20.65, 35.02, 0.92645, 1, 5, 124.96, -94.82, 1, 2, 2, -26.25, 150.15, 0.02817, 3, 124.58, -44.08, 0.97183, 3, 5, -34.29, 44.6, 0.15114, 2, 31.91, 30.91, 0.35043, 3, -8.09, -43.84, 0.49843], "hull": 8, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 0, 14, 16, 18, 18, 20, 0, 2], "width": 357, "height": 706}}, "boy_chansau": {"boy_chansau": {"x": 249.86, "y": -31.02, "rotation": -6.65, "width": 587, "height": 296}}, "boy_chantruoc": {"boy_chantruoc": {"type": "mesh", "uvs": [0.2573, 1, 0.75723, 0.49436, 0.8168, 1, 1, 1, 1, 0, 0.76149, 0, 0, 0.30288, 0, 1], "triangles": [4, 3, 1, 3, 2, 1, 1, 6, 5, 0, 7, 6, 1, 0, 6, 4, 1, 5], "vertices": [1, 7, -18.48, -79.31, 1, 2, 7, 331.2, -59.46, 0.73481, 8, 65.85, -109.56, 0.26519, 2, 7, 304.81, -216.94, 0.03246, 8, 225.26, -118.91, 0.96754, 1, 8, 258.71, -8.83, 1, 1, 8, -35.02, 80.44, 1, 1, 7, 393.1, 79.14, 1, 1, 7, -83.32, 180.89, 1, 1, 7, -167.15, -16.02, 1], "hull": 8, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 0, 14, 2, 10], "width": 628, "height": 307}}, "boy_ciu": {"boy_ciu": {"x": 110.32, "y": 10.97, "rotation": -179.29, "width": 262, "height": 103}}, "boy_duisau": {"boy_duisau": {"x": 145.27, "y": 47.18, "rotation": 75.52, "width": 225, "height": 516}}, "boy_duitruoc": {"boy_duitruoc": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 6, 588.26, 187.4, 1, 1, 6, 619.1, -116.04, 1, 1, 6, -74.33, -186.51, 1, 1, 6, -105.17, 116.93, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 305, "height": 697}}, "boy_head": {"boy_head": {"type": "mesh", "uvs": [0.1062, 0.86277, 0.48347, 0.73493, 0.53902, 0.92876, 1, 1, 1, 0.75968, 0.91861, 0.44214, 0.85843, 0.20708, 0.5668, 0, 0.21267, 0, 0, 0.12254, 0, 0.48443, 0, 0.53553, 0, 0.60517, 0, 0.81947, 0.25672, 0.39552, 0.18711, 0.40711, 0.08304, 0.47538, 0.11216, 0.47702, 0.20173, 0.44443, 0.12458, 0.49002, 0.15755, 0.48993, 0.11104, 0.59341, 0.05284, 0.53028, 0.13945, 0.68051, 0.06321, 0.67771, 0.06534, 0.70522, 0.05422, 0.6583, 0.03874, 0.63132, 0.13485, 0.44139, 0.2249, 0.43215], "triangles": [1, 5, 4, 2, 1, 4, 2, 4, 3, 5, 14, 6, 14, 15, 8, 29, 15, 14, 28, 9, 15, 18, 15, 29, 28, 15, 18, 28, 10, 9, 17, 16, 28, 17, 28, 18, 16, 10, 28, 20, 17, 18, 19, 17, 20, 22, 10, 16, 11, 10, 22, 16, 19, 22, 19, 16, 17, 21, 22, 19, 12, 11, 22, 12, 22, 21, 27, 12, 21, 26, 27, 21, 23, 24, 26, 20, 21, 19, 23, 21, 20, 23, 26, 21, 25, 24, 23, 14, 5, 1, 29, 14, 1, 12, 25, 13, 26, 12, 27, 26, 25, 12, 25, 26, 24, 0, 25, 23, 13, 25, 0, 29, 23, 20, 29, 20, 18, 23, 29, 1, 0, 23, 1, 15, 9, 8, 14, 8, 7, 6, 14, 7], "vertices": [1, 14, -28.78, 25.25, 1, 3, 14, 41.09, -74.64, 0.80946, 24, 42.01, -119.09, 0.00054, 25, 8.19, -83.4, 0.19, 2, 14, -18.52, -106.96, 0.07391, 25, 70.23, -110.8, 0.92609, 1, 25, 175.21, -16.24, 1, 2, 24, 175.39, -37.77, 0.00499, 25, 111.15, 34.09, 0.99501, 3, 16, 184.64, -49.39, 0.03192, 24, 94.24, 37.1, 0.88741, 25, 11.31, 81.25, 0.08066, 2, 16, 149.32, 24.32, 0.71217, 24, 34.17, 92.53, 0.28783, 1, 16, 47.91, 73.32, 1, 2, 16, -56.4, 49.7, 0.22935, 15, 58.92, 25.56, 0.77065, 2, 15, 2.94, 77.68, 0.86937, 14, 206.71, 117.69, 0.13063, 2, 15, -116.02, 47.69, 0.14601, 14, 87.75, 87.7, 0.85399, 2, 15, -132.81, 43.46, 0.08595, 14, 70.95, 83.47, 0.91405, 2, 15, -155.71, 37.69, 0.03557, 14, 48.06, 77.7, 0.96443, 1, 14, -22.39, 59.94, 1, 2, 15, -67.84, -20.12, 0.06239, 14, 135.92, 19.89, 0.93761, 2, 15, -76.21, 3.72, 0.14892, 14, 127.55, 43.73, 0.85108, 2, 15, -106.91, 24.12, 0.13799, 14, 96.85, 64.13, 0.86201, 2, 15, -105.3, 15.46, 0.11999, 14, 98.46, 55.47, 0.88001, 2, 15, -88.17, -6.74, 0.08098, 14, 115.59, 33.27, 0.91902, 2, 15, -105.01, 14.42, 0.09696, 14, 98.76, 54.43, 0.90304, 2, 15, -99.25, 6.36, 0.07524, 14, 104.51, 46.37, 0.92476, 2, 15, -143.64, 6.14, 0.02777, 14, 60.12, 46.16, 0.97223, 2, 15, -127.19, 28.42, 0.08479, 14, 76.58, 68.43, 0.91521, 2, 15, -169.87, -7.75, 0.0035, 14, 33.89, 32.26, 0.9965, 2, 15, -175.69, 11.9, 0.00808, 14, 28.08, 51.91, 0.99192, 2, 15, -184.44, 8.97, 0.00395, 14, 19.33, 48.98, 0.99605, 2, 15, -168.59, 19, 0.01283, 14, 35.17, 59.01, 0.98717, 2, 15, -161.52, 26.03, 0.02204, 14, 42.24, 66.05, 0.97796, 2, 15, -90.6, 16.17, 0.14343, 14, 113.17, 56.18, 0.85657, 4, 15, -80.77, -13.6, 0.06486, 14, 122.99, 26.41, 0.9351, 24, -81.17, -77.33, 0, 25, -121.84, -80.4, 4e-05], "hull": 14, "edges": [0, 2, 2, 4, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 26, 0, 28, 30, 18, 20, 32, 20, 34, 36, 38, 40, 24, 26, 24, 42, 42, 44, 20, 22, 22, 24, 44, 22, 46, 48, 48, 50, 46, 52, 52, 54, 30, 56, 56, 32, 4, 6], "width": 302, "height": 339}}, "boy_octruoc1": {"boy_octruoc1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.75, 0, 0.5, 0, 0.25, 0, 0, 1, 0, 1, 0.25, 1, 0.5, 1, 0.75], "triangles": [1, 2, 9, 1, 9, 0, 3, 7, 8, 2, 3, 8, 2, 8, 9, 4, 5, 6, 4, 6, 7, 3, 4, 7], "vertices": [2, 19, 105.3, 27.48, 0.00028, 20, 34.83, 35, 0.99972, 1, 20, 48.95, -17.13, 1, 2, 19, 68.62, -30.44, 0.08735, 20, 8.17, -28.17, 0.91265, 3, 18, 57.46, -43.51, 0.10704, 19, 26.58, -34.64, 0.78647, 20, -32.61, -39.22, 0.10648, 3, 18, 19.85, -24.26, 0.8691, 19, -15.46, -38.83, 0.13088, 20, -73.39, -50.27, 2e-05, 1, 18, -17.75, -5, 1, 1, 18, 6.86, 43.07, 1, 2, 18, 44.46, 23.81, 0.96655, 19, -20.82, 14.9, 0.03345, 1, 19, 21.22, 19.1, 1, 2, 19, 63.26, 23.29, 0.54356, 20, -5.95, 23.95, 0.45644], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 0], "width": 54, "height": 169}}, "boy_taysau": {"boy_taysau": {"x": 154.93, "y": 9.68, "rotation": 90.41, "width": 146, "height": 457}}, "boy_taytruoc": {"boy_taytruoc": {"type": "mesh", "uvs": [0, 0.00062, 0.58724, 0.3904, 0.43575, 0.70882, 0.10752, 0.72423, 0, 0.85936, 0, 1, 0.32033, 0.99974, 0.99999, 0.70187, 1, 0.34443, 0.64796, 1e-05], "triangles": [6, 3, 2, 4, 3, 6, 6, 2, 7, 5, 4, 6, 7, 1, 8, 2, 1, 7, 1, 0, 9, 1, 9, 8], "vertices": [1, 11, -141.54, -109.28, 1, 2, 11, 268.05, -99.68, 0.86215, 12, 49.08, -102.18, 0.13785, 2, 12, 325.66, -66.08, 0.62223, 13, -0.59, -73.21, 0.37777, 2, 12, 376.19, -195.08, 0.02857, 13, 99.76, -168.72, 0.97143, 1, 13, 212.96, -122.32, 1, 1, 13, 286.59, -42.89, 1, 1, 13, 198.64, 51.17, 1, 2, 12, 250.45, 125.7, 0.4189, 13, -149.86, 68.77, 0.5811, 2, 11, 326.06, 40.02, 0.16105, 12, -29.05, 27.42, 0.83895, 1, 11, 24.75, 88.83, 1], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 2, 16, 16, 18, 14, 16, 18, 0], "width": 402, "height": 847}}, "boy_tocsau2": {"boy_tocsau": {"x": 6.15, "y": -21.11, "rotation": -23.14, "width": 128, "height": 130}}, "boy_toctruoc2": {"boy_toctruoc2": {"type": "mesh", "uvs": [0, 0.23972, 0.09136, 0.4362, 0.18376, 0.63492, 0.37764, 0.91157, 1, 1, 1, 0.66282, 0.79492, 0.39547, 0.63328, 0.25885, 0.47037, 0.12115, 0.03796, 0], "triangles": [0, 9, 8, 1, 0, 8, 1, 8, 7, 2, 1, 7, 2, 7, 6, 3, 2, 6, 3, 6, 5, 3, 5, 4], "vertices": [3, 23, -93.63, -23.81, 0, 22, -37.47, -34.79, 0.11354, 21, 18.58, -27.95, 0.88646, 3, 23, -54.68, -29.38, 0.06297, 22, 1.85, -36.38, 0.82605, 21, 57, -36.46, 0.11097, 2, 23, -15.28, -35.02, 0.61716, 22, 41.61, -37.98, 0.38284, 1, 23, 42.24, -36.41, 1, 1, 23, 83.6, 18.53, 1, 2, 23, 23.17, 43.59, 0.45104, 22, 71.89, 44.12, 0.54896, 2, 22, 15.91, 38, 0.83837, 21, 83.97, 34.27, 0.16163, 2, 22, -14.45, 29.05, 0.12896, 21, 52.51, 30.82, 0.87104, 1, 21, 20.81, 27.34, 1, 1, 21, -21.08, -3.3, 1], "hull": 10, "edges": [0, 18, 0, 16, 0, 2, 2, 4, 12, 14, 14, 16, 2, 14, 4, 12, 6, 10, 6, 8, 8, 10, 10, 12, 4, 6, 16, 18], "width": 107, "height": 194}}, "g2_body": {"g2_body": {"type": "mesh", "uvs": [0.29186, 0, 0.28561, 0.10121, 0.08569, 0.21655, 0.08569, 0.31851, 0.18915, 0.44205, 0.26818, 0.53556, 0, 0.65616, 0, 0.98163, 1, 1, 0.79478, 0.68854, 0.87167, 0.51131, 0.91661, 0.40772, 1, 0.32247, 1, 0.23054, 0.88849, 0.21048, 0.77916, 0.11186, 0.60736, 0.11353, 0.60423, 0, 0.53289, 0.20973, 0.32534, 0.18513, 0.2801, 0.35113, 0.401, 0.43486, 0.52747, 0.43857, 0.6197, 0.41082, 0.6848, 0.3592, 0.66179, 0.30438, 0.59845, 0.25868, 0.76526, 0.39578, 0.8739, 0.38673, 0.95406, 0.34148, 0.52892, 0.32053, 0.94871, 0.26637, 0.58757, 0.56946, 0.62145, 0.4825, 0.51001, 0.75649, 0.22771, 0.17725, 0.27808, 0.24568, 0.26669, 0.31157], "triangles": [37, 2, 35, 36, 35, 19, 30, 20, 36, 37, 36, 20, 37, 35, 36, 30, 36, 19, 1, 35, 2, 5, 20, 21, 5, 4, 20, 16, 18, 1, 18, 19, 1, 17, 1, 0, 1, 17, 16, 4, 37, 20, 35, 1, 19, 11, 29, 12, 28, 27, 31, 29, 28, 31, 29, 31, 12, 31, 13, 12, 31, 14, 13, 21, 20, 30, 27, 23, 24, 32, 21, 22, 10, 33, 27, 32, 22, 33, 10, 28, 11, 10, 27, 28, 22, 23, 33, 27, 33, 23, 22, 30, 23, 22, 21, 30, 23, 30, 25, 23, 25, 24, 25, 30, 26, 11, 28, 29, 24, 31, 27, 24, 25, 31, 30, 18, 26, 30, 19, 18, 25, 26, 31, 31, 26, 14, 15, 14, 26, 26, 16, 15, 26, 18, 16, 37, 3, 2, 4, 3, 37, 7, 34, 8, 8, 34, 9, 9, 34, 32, 32, 33, 9, 34, 6, 5, 34, 7, 6, 9, 33, 10, 34, 5, 32, 21, 32, 5], "vertices": [1, 27, 130.45, 87.58, 1, 2, 29, 184.51, 55.66, 0.06038, 27, 55.05, 74.85, 0.93962, 2, 37, -223.97, -268.41, 0.78, 27, -46.35, 136.69, 0.22, 2, 37, -149.54, -248.75, 0.78, 27, -121.79, 121.38, 0.22, 4, 37, -57.76, -217.74, 0.23859, 26, 119.87, 185.35, 0.00125, 29, -64.74, 155.83, 0.00081, 27, -215.22, 95.75, 0.75935, 4, 37, 4.09, -173.41, 0.48328, 26, 45.12, 171.1, 0.00253, 29, -139.48, 141.58, 0.00164, 27, -279.54, 55.08, 0.51255, 3, 37, 104.43, -211.43, 0.99144, 26, -29.98, 247.75, 0.00519, 29, -209.13, 236.58, 0.00337, 1, 37, 343.42, -154.57, 1, 1, 37, 253.57, 239.8, 1, 3, 37, 49.99, 103.17, 0.86895, 26, -112.7, -60.63, 0.1296, 28, -295.31, 86.59, 0.00145, 3, 37, -93.14, 95.94, 0.32053, 26, 20.23, -114.18, 0.67893, 28, -162.39, 33.03, 0.00053, 1, 26, 91.36, -143.87, 1, 2, 26, 148.87, -188.26, 0.02909, 28, -33.75, -41.05, 0.97091, 3, 26, 217.22, -200.36, 0.00343, 28, 34.6, -53.14, 0.99593, 27, 16.76, -227.41, 0.00065, 3, 26, 239.98, -158.64, 0.70932, 28, 57.36, -11.42, 0.19, 27, 22.65, -180.25, 0.10068, 3, 26, 321, -128.12, 0.53053, 28, 138.38, 19.1, 0.22, 27, 86.83, -122.16, 0.24947, 2, 26, 331.85, -59.55, 0.26339, 27, 71.79, -54.39, 0.73661, 1, 27, 155.55, -36.1, 1, 3, 26, 265.57, -17.27, 0.75764, 29, 86.42, -28.44, 0.08311, 27, -5.37, -39.35, 0.15925, 3, 37, -271.63, -180.86, 0.78, 26, 298.47, 62.06, 0.0016, 27, -3.84, 46.52, 0.2184, 4, 37, -145.79, -166.52, 0.10638, 26, 178.25, 101.9, 0.17713, 29, -0.9, 90.73, 0.3, 27, -130.3, 39.51, 0.41649, 5, 37, -97.15, -103.15, 0.11562, 26, 107.49, 64.82, 0.42574, 29, -71.66, 53.65, 0.27897, 28, -75.13, 212.03, 0.05115, 27, -182.54, -20.93, 0.12852, 3, 26, 95.83, 14.99, 0.73264, 29, -83.32, 3.82, 0.2, 27, -175.13, -71.56, 0.06736, 5, 37, -137.26, -22.36, 0.02564, 26, 109.96, -25.34, 0.6258, 29, -69.19, -36.52, 0.2, 28, -72.65, 121.87, 0.11698, 27, -147.18, -103.91, 0.03158, 4, 26, 143.75, -58.04, 0.5668, 29, -35.4, -69.21, 0.2, 28, -38.86, 89.18, 0.12945, 27, -103.76, -121.94, 0.10375, 4, 26, 186.13, -56.1, 0.49857, 29, 6.98, -67.27, 0.2, 28, 3.52, 91.12, 0.11584, 27, -65.04, -104.59, 0.18558, 4, 37, -246.13, -60, 0.00015, 26, 224.57, -36.91, 0.61157, 29, 45.42, -48.08, 0.21623, 27, -36.32, -72.65, 0.17205, 3, 26, 110.89, -85.23, 0.66976, 28, -71.72, 61.98, 0.2845, 27, -124.36, -159.28, 0.04573, 3, 26, 109.97, -129.64, 0.49117, 28, -72.64, 17.57, 0.49472, 27, -108.94, -200.94, 0.0141, 3, 26, 137.98, -167.49, 0.18999, 28, -44.64, -20.27, 0.80463, 27, -69.01, -225.88, 0.00538, 5, 37, -193.3, -78.64, 0.00071, 26, 184.46, 2.19, 0.39553, 29, -0.14, -27.33, 0.53, 28, 1.84, 149.4, 0.01137, 27, -87.97, -50.98, 0.0624, 3, 26, 194.19, -175.24, 0.28952, 28, 11.58, -28.03, 0.68, 27, -13.86, -212.49, 0.03048, 5, 37, -22.67, -11.13, 0.48717, 26, 1.25, 12.59, 0.3077, 29, -183.35, -16.93, 0.0687, 28, -181.37, 159.8, 0.00063, 27, -262.24, -108.48, 0.1358, 5, 37, -84.22, -11.95, 0.2353, 26, 57.45, -12.52, 0.49277, 29, -127.15, -42.04, 0.13743, 28, -125.17, 134.69, 0.06546, 27, -200.74, -111.23, 0.06904, 5, 37, 127.35, -1.59, 0.7461, 26, -138.91, 66.93, 0.16002, 29, -323.51, 37.42, 0.03137, 28, -321.52, 214.15, 0.00051, 27, -412.56, -109.3, 0.06201, 1, 116, 82.42, -27.57, 1, 1, 116, 73.56, 26.32, 1, 1, 116, 88.79, 73.9, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 14, 16, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 32, 36, 36, 38, 38, 2, 6, 8, 8, 10, 10, 12, 20, 22, 18, 20, 12, 14, 62, 50, 62, 48, 62, 52, 16, 18], "width": 404, "height": 755}}, "g2_head": {"g2_head": {"type": "mesh", "uvs": [1, 0.87397, 1, 0, 0.35529, 0, 0, 0.26233, 0, 0.81804, 0.24407, 1, 1, 1, 0.65695, 0.15949, 0.47901, 0.61055, 0.58745, 0.82165, 0.10645, 0.3345, 0.24268, 0.6286, 0.36919, 0.71159, 0.79195, 0.41415, 0.75135, 0.46813, 0.65828, 0.4964, 0.61075, 0.43086, 0.76521, 0.35247, 0.91869, 0.61591, 0.85334, 0.68274], "triangles": [10, 3, 2, 2, 7, 10, 7, 8, 10, 4, 3, 10, 11, 10, 8, 12, 11, 8, 4, 10, 11, 12, 8, 9, 5, 11, 12, 5, 12, 9, 4, 11, 5, 7, 2, 1, 17, 7, 1, 13, 17, 1, 16, 7, 17, 14, 16, 17, 13, 14, 17, 15, 16, 14, 7, 16, 8, 8, 16, 15, 18, 13, 1, 0, 18, 1, 14, 13, 18, 19, 14, 18, 9, 8, 15, 15, 14, 19, 9, 15, 19, 19, 18, 0, 9, 19, 0, 9, 0, 6, 5, 9, 6], "vertices": [1, 30, -46.12, -22.3, 1, 1, 30, 105.03, -161.36, 1, 2, 30, 238.16, -16.65, 0.00346, 31, -8.51, -42.11, 0.99654, 2, 32, 29.16, -103.8, 0.00052, 31, 115.36, -27.98, 0.99948, 3, 30, 170.06, 193.26, 7e-05, 32, 142.28, -38.54, 0.93295, 31, 166.66, 92.11, 0.06698, 2, 30, 88.18, 167.42, 0.11939, 32, 142.11, 47.31, 0.88061, 1, 30, -67.92, -2.25, 1, 3, 30, 148.29, -58.98, 0.60452, 32, -91.91, 57.68, 0.00691, 31, -78.39, 28.51, 0.38857, 2, 30, 107.02, 52.73, 0.49712, 32, 27.03, 63.64, 0.50288, 2, 30, 48.12, 61.97, 0.74188, 32, 53.47, 117.08, 0.25812, 2, 32, 27.62, -67.2, 0.00196, 31, 92.17, 0.37, 0.99804, 2, 30, 152.7, 108.64, 0.00572, 32, 66.72, 3.33, 0.99428, 2, 30, 112.23, 93.45, 0.22386, 32, 64.34, 46.49, 0.77614, 1, 30, 79.59, -49.27, 1, 1, 30, 78.64, -31.57, 1, 2, 30, 92.97, -6.18, 0.99412, 31, -47.06, 98.26, 0.00588, 3, 30, 107.89, -5.97, 0.94057, 32, -29.24, 80.37, 0.0205, 31, -41.89, 84.27, 0.03893, 1, 30, 89.62, -53.14, 1, 2, 30, 14.93, -45.08, 0.99225, 31, -109.76, 158.86, 0.00775, 2, 30, 17.61, -19.84, 0.99874, 31, -85.08, 164.75, 0.00126], "hull": 7, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 14, 16, 16, 18, 2, 0, 0, 12, 18, 0, 26, 28, 28, 30, 32, 34, 36, 38], "width": 305, "height": 235}}, "g2_tay": {"g2_tay": {"type": "mesh", "uvs": [0.32833, 0.0186, 0.49107, 0.03653, 0.62682, 0.07292, 0.73039, 0.12595, 0.78433, 0.18369, 0.83505, 0.2585, 0.61087, 1, 0, 1, 0, 0.64011, 0, 0.22518, 0.06488, 0.08785, 0.40206, 0.11962], "triangles": [11, 8, 9, 11, 9, 10, 6, 7, 8, 6, 8, 11, 11, 0, 1, 10, 0, 11, 11, 1, 5, 1, 2, 5, 4, 2, 3, 4, 5, 2, 6, 11, 5], "vertices": [1, 116, 84.86, -36.41, 1, 1, 116, 75.61, -16.96, 1, 1, 116, 68.91, 8.51, 1, 1, 116, 71.24, 46.64, 1, 1, 116, 77.18, 64.11, 1, 1, 116, 89.95, 86.04, 1, 1, 36, 374.28, 35.64, 1, 1, 36, 374.96, -39.5, 1, 1, 36, 222.73, -40.87, 1, 1, 36, 47.23, -42.46, 1, 2, 116, 134.75, -27.85, 0.54593, 36, -10.93, -35.01, 0.45407, 2, 116, 107.65, 6.72, 0.72766, 36, 2.13, 6.93, 0.27234], "hull": 11, "edges": [10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10], "width": 123, "height": 423}}, "g2_tay2": {"g2_tay2": {"type": "mesh", "uvs": [0.47981, 0, 0.31848, 0.38892, 0, 0.09192, 0, 0.876, 0.25248, 1, 0.83914, 1, 1, 0], "triangles": [3, 2, 1, 4, 3, 1, 6, 1, 0, 5, 1, 6, 4, 1, 5], "vertices": [1, 39, 200.23, 54.48, 1, 2, 39, 101.78, 39.1, 0.58183, 38, 0.83, 52.89, 0.41817, 2, 39, 155.11, 106.32, 0, 38, -84.96, 51.6, 1, 2, 39, -27.27, 34.47, 0.31681, 38, 86.13, -44.07, 0.68319, 2, 39, -43.62, -8.6, 0.95418, 38, 129.82, -29.45, 0.04582, 1, 39, -14.59, -82.29, 1, 1, 39, 225.97, -10.86, 1], "hull": 7, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 0, 12, 2, 8], "width": 135, "height": 250}}, "g2_tocsau": {"g2_tocsau": {"type": "mesh", "uvs": [0.30153, 0, 0.3256, 0.16445, 0.22931, 0.44611, 0.18116, 0.69373, 0.57597, 0.83611, 0.87449, 1, 1, 1, 1, 0.73087, 0.77338, 0.60706, 0.79264, 0.4523, 1, 0], "triangles": [1, 0, 10, 9, 1, 10, 2, 1, 9, 8, 2, 9, 3, 2, 8, 4, 3, 8, 4, 8, 7, 5, 4, 7, 5, 7, 6], "vertices": [1, 33, -4.64, -24.87, 1, 2, 34, -30.73, -11.64, 0.00098, 33, 15.85, -21.23, 0.99902, 2, 34, 1.6, -28.24, 0.74924, 33, 51.86, -26.09, 0.25076, 3, 35, -18.58, -36.63, 0.07878, 34, 30.77, -39.97, 0.92062, 33, 83.28, -27.42, 0.0006, 2, 35, 13.71, -19.25, 0.93008, 34, 56.27, -13.63, 0.06992, 1, 35, 44.1, -9.9, 1, 1, 35, 49.55, -1.32, 1, 2, 35, 20.92, 16.86, 0.98211, 34, 52.22, 22.96, 0.01789, 2, 35, -2.09, 9.72, 0.2213, 34, 32.45, 9.2, 0.7787, 2, 34, 13.99, 15.68, 0.85703, 33, 48.9, 19.45, 0.14297, 2, 34, -36.83, 46.46, 0.00013, 33, -9.28, 31.51, 0.99987], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 20, 4, 18, 6, 16, 8, 14, 18, 20], "width": 81, "height": 126}}, "g3_body": {"g3_body": {"type": "mesh", "uvs": [0.86472, 0.25455, 0.9243, 0.35944, 1, 0.51008, 0.89871, 1, 0.67104, 1, 0.5263, 1, 0, 1, 0, 0, 0.33113, 0, 0.67561, 0.36784, 0.62606, 0.58394, 0.78871, 0.69183, 0.92502, 0.54377, 0.9198, 0.68016, 0.78684, 0.81195, 0.60612, 0.83206, 0.54073, 0.51709, 0.44043, 0.5713, 0.28249, 0.55566, 0.17954, 0.3905, 0.37471, 0.32048, 0.23134, 0.28464, 0.37798, 0.23592, 0.53302, 0.30401, 0.44078, 0.88581], "triangles": [8, 23, 22, 21, 7, 8, 21, 8, 22, 19, 7, 21, 0, 23, 8, 9, 23, 0, 9, 0, 1, 16, 23, 9, 20, 23, 16, 9, 1, 12, 10, 16, 9, 11, 15, 10, 10, 17, 16, 14, 15, 11, 15, 17, 10, 24, 17, 15, 6, 7, 19, 6, 19, 18, 4, 5, 24, 18, 17, 24, 6, 18, 24, 6, 24, 5, 4, 15, 14, 4, 24, 15, 13, 14, 11, 3, 14, 13, 4, 14, 3, 20, 21, 22, 20, 22, 23, 20, 19, 21, 17, 18, 20, 18, 19, 20, 16, 17, 20, 12, 1, 2, 10, 9, 12, 12, 11, 10, 13, 11, 12, 13, 12, 2, 3, 13, 2], "vertices": [2, 70, 147.6, -111.55, 0.9444, 71, 13.88, -111.49, 0.0556, 3, 73, 0.8, -214.44, 0.00013, 70, 99.4, -139.21, 0.99404, 71, -26.24, -149.93, 0.00583, 3, 72, 21.49, -21.16, 0.84, 73, -68.77, -250.36, 2e-05, 70, 29.83, -175.14, 0.15998, 1, 69, -45.55, -116.27, 1, 1, 69, -37.22, -28.56, 1, 1, 69, -106.92, -34.36, 1, 1, 69, -12.67, 229.97, 1, 2, 72, 125.95, 425.28, 0.00264, 71, 19.33, 244.9, 0.99736, 3, 72, 168.22, 304.3, 0.00158, 70, 250.55, 105.46, 0.07027, 71, 61.6, 123.92, 0.92815, 4, 72, 44.81, 119.97, 0.13585, 73, -12.18, -118.99, 2e-05, 70, 86.42, -43.77, 0.86374, 71, -61.81, -60.41, 0.00039, 4, 72, -59.85, 103.72, 0.09921, 73, -117.68, -109.58, 0, 69, 164.07, -30.19, 0.77414, 70, -19.07, -34.36, 0.12665, 3, 72, -88.18, 27.14, 0.38869, 69, 106.34, -87.94, 0.60365, 70, -65, -101.87, 0.00766, 2, 72, -3.41, 0.88, 0.99125, 69, 172.4, -147.2, 0.00875, 3, 72, -66.14, -18.9, 0.62527, 69, 107.15, -138.97, 0.37436, 70, -54.68, -151.86, 0.00037, 1, 69, 48.77, -81.75, 1, 2, 72, -175.3, 71.55, 0.00913, 69, 45.74, -11.2, 0.99087, 2, 73, -88.67, -73.7, 0.00178, 70, 9.94, 1.52, 0.99822, 4, 72, -77.79, 173.54, 0, 73, -118.29, -37.49, 0.22, 69, 176.92, 40.75, 0.71741, 70, -19.69, 37.74, 0.06259, 3, 73, -116.47, 24.07, 0.37912, 69, 190.21, 100.88, 0.6011, 70, -17.87, 99.29, 0.01979, 4, 72, -28.82, 297.6, 7e-05, 73, -40.92, 71.15, 0.56, 69, 273.22, 133.02, 0.43013, 71, -135.44, 117.23, 0.0098, 3, 73, -0.29, -0.92, 0.9913, 69, 299.68, 54.64, 0.00028, 70, 98.31, 74.3, 0.00842, 5, 72, 25.96, 295.51, 0.00033, 73, 11.75, 55.93, 0.51, 69, 322.12, 108.24, 0.3003, 70, 110.35, 131.15, 0.01389, 71, -80.66, 115.13, 0.17548, 5, 72, 66.84, 249.68, 0.02221, 73, 40.41, 1.61, 0.53, 69, 340.14, 49.52, 0.0006, 70, 139.01, 76.83, 0.177, 71, -39.77, 69.3, 0.27018, 5, 72, 55.65, 182.21, 0.09826, 73, 13.31, -61.18, 0.22, 69, 301.79, -7.1, 0.00012, 70, 111.92, 14.04, 0.62734, 71, -50.97, 1.84, 0.05428, 3, 73, -269.21, -51.72, 0.09743, 69, 26, 54.95, 0.89748, 70, -170.61, 23.5, 0.00508], "hull": 9, "edges": [12, 14, 14, 16, 16, 0, 0, 2, 2, 4, 4, 6, 10, 12, 6, 8, 8, 10, 48, 8], "width": 387, "height": 482}}, "g3_chansau": {"g3_chansau": {"type": "mesh", "uvs": [1, 0.68305, 1, 1, 0, 1, 0, 0.6256, 0, 0, 1, 0], "triangles": [3, 4, 5, 3, 5, 0, 2, 3, 0, 2, 0, 1], "vertices": [1, 92, 23.61, 59.54, 1, 1, 92, 243.2, 25.68, 1, 2, 92, 220.64, -120.59, 0.97312, 91, 649.65, -87.9, 0.02688, 2, 92, -38.75, -80.59, 0.00227, 91, 387.25, -82.58, 0.99773, 1, 91, -51.21, -73.69, 1, 1, 91, -48.2, 74.28, 1], "hull": 6, "edges": [2, 4, 8, 10, 4, 6, 6, 8, 2, 0, 0, 10, 6, 0], "width": 148, "height": 701}}, "g3_chantruoc": {"g3_chantruoc": {"type": "mesh", "uvs": [1, 0.70233, 1, 1, 0, 1, 0, 0.59802, 0, 0, 1, 0], "triangles": [3, 4, 5, 3, 5, 0, 2, 3, 0, 2, 0, 1], "vertices": [2, 89, -2.68, 67.07, 0.8735, 88, 500.06, 65.29, 0.1265, 1, 89, 224.24, 110.27, 1, 1, 89, 252.29, -37.08, 1, 2, 89, -54.14, -95.43, 0.1411, 88, 412.3, -80.83, 0.8589, 1, 88, -51.27, -59.51, 1, 1, 88, -44.38, 90.34, 1], "hull": 6, "edges": [2, 4, 8, 10, 4, 6, 6, 8, 2, 0, 0, 10, 6, 0], "width": 150, "height": 776}}, "g3_duisau": {"g3_duisau": {"x": 223.78, "y": 2.87, "rotation": -80.48, "width": 225, "height": 518}}, "g3_duitruoc": {"g3_duitruoc": {"x": 243.51, "y": 19.35, "rotation": -93.82, "width": 287, "height": 625}}, "g3_face": {"g3_face": {"type": "mesh", "uvs": [1, 0.5795, 1, 1, 0, 1, 0, 0, 1, 0, 0.63361, 0.15202, 0.43245, 0.19994, 0.4703, 0.42257, 0.52773, 0.7041, 0.86123, 0.67918, 0.91946, 0.31304, 0.56294, 0.38563, 0.6678, 0.3869, 0.77791, 0.36918, 0.8618, 0.34639, 0.93869, 0.36158, 0.44584, 0.35778, 0.69576, 0.30589, 0.85715, 0.48175, 0.76987, 0.48742, 0.69954, 0.57386, 0.76551, 0.60194, 0.93872, 0.58121, 0.93564, 0.5261, 0.68595, 0.46943, 0.75995, 0.40042, 0.82807, 0.40017], "triangles": [2, 7, 8, 1, 8, 9, 2, 8, 1, 5, 3, 4, 6, 3, 5, 10, 17, 5, 4, 10, 5, 14, 17, 10, 16, 6, 5, 16, 5, 17, 15, 10, 4, 14, 10, 15, 13, 17, 14, 11, 16, 17, 12, 11, 17, 25, 12, 17, 26, 13, 14, 13, 25, 17, 25, 13, 26, 7, 16, 11, 24, 12, 25, 15, 18, 26, 15, 26, 14, 19, 25, 26, 19, 26, 18, 24, 25, 19, 0, 15, 4, 23, 18, 15, 0, 23, 15, 20, 24, 19, 22, 23, 0, 21, 20, 19, 21, 19, 18, 22, 21, 18, 22, 18, 23, 9, 21, 22, 20, 8, 7, 16, 2, 3, 16, 3, 6, 2, 16, 7, 9, 22, 0, 1, 9, 0, 8, 20, 21, 21, 9, 8, 11, 12, 24, 24, 7, 11, 24, 20, 7], "vertices": [2, 74, -9.75, -43.98, 0.99812, 71, 66.28, -79.07, 0.00188, 2, 74, -110.12, 33.13, 0.00337, 71, -46.48, -21.59, 0.99663, 2, 74, 22.69, 206, 0.01986, 71, 52.53, 172.63, 0.98014, 2, 74, 261.38, 22.62, 0.99893, 71, 320.7, 35.92, 0.00107, 1, 74, 128.56, -150.25, 1, 1, 74, 140.94, -59.04, 1, 2, 74, 156.22, -15.47, 0.99412, 71, 224.26, -20.73, 0.00588, 2, 74, 92.87, 25.19, 0.89277, 71, 154.55, 7.68, 0.10723, 2, 74, 23.23, 60.51, 0.1479, 71, 79.63, 29.68, 0.8521, 2, 74, -15.12, -1.71, 0.82471, 71, 53.29, -38.49, 0.17529, 1, 74, 64.54, -78.92, 1, 2, 74, 90.66, 0.84, 0.9929, 71, 156.82, -16.67, 0.0071, 1, 74, 77.62, -18.53, 1, 1, 74, 68.37, -42.22, 1, 1, 74, 63.57, -62.01, 1, 1, 74, 50.4, -73.35, 1, 2, 74, 112.93, 15.88, 0.94497, 71, 175.98, 2.18, 0.05503, 1, 74, 94.51, -39.78, 1, 2, 74, 31.7, -36.16, 0.97097, 71, 105.61, -63.82, 0.02903, 2, 74, 41.28, -19.23, 0.94754, 71, 111.94, -45.42, 0.05246, 2, 74, 30.04, 8.71, 0.78411, 71, 95.79, -19.99, 0.21589, 2, 74, 15.09, 1.82, 0.81709, 71, 82.35, -29.5, 0.18291, 2, 74, -2.2, -32.85, 0.95351, 71, 71.68, -66.75, 0.04649, 2, 74, 11.17, -42.2, 0.98661, 71, 86.54, -73.5, 0.01339, 2, 74, 56.09, -7.25, 0.9078, 71, 124.32, -30.93, 0.0922, 2, 74, 63.2, -33.27, 0.97726, 71, 136.06, -55.22, 0.02274, 2, 74, 54.81, -45.82, 0.99034, 71, 130.09, -69.09, 0.00966], "hull": 5, "edges": [2, 4, 4, 6, 6, 8, 2, 0, 0, 8, 14, 22, 22, 24, 26, 28, 28, 30, 32, 34, 34, 20, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 36, 38, 48, 48, 50, 50, 52, 52, 36], "width": 218, "height": 301}}, "g3_taysau": {"g3_taysau": {"type": "mesh", "uvs": [1, 0.32863, 1, 1, 0, 1, 0, 0.2986, 0, 0, 1, 0], "triangles": [3, 4, 5, 3, 5, 0, 2, 3, 0, 2, 0, 1], "vertices": [1, 107, 199.55, -18.51, 1, 1, 107, -49.24, -46.99, 1, 1, 107, -59.82, 45.4, 1, 2, 107, 200.1, 75.16, 0.05219, 108, 28.11, 64.89, 0.94781, 1, 108, 135.48, 35.27, 1, 2, 107, 321.34, -4.56, 1e-05, 108, 110.75, -54.38, 0.99999], "hull": 6, "edges": [2, 4, 8, 10, 4, 6, 6, 8, 2, 0, 0, 10, 6, 0], "width": 93, "height": 373}}, "g3_toc1": {"g3_toc1": {"type": "mesh", "uvs": [1, 0.10124, 1, 0.28913, 1, 0.41584, 1, 0.6212, 1, 0.78943, 1, 1, 0, 1, 0, 0.80035, 0, 0.61246, 0, 0.43769, 0, 0.28476, 0, 0.11216, 0, 0, 0.5981, 0], "triangles": [11, 12, 13, 11, 13, 0, 1, 10, 11, 1, 11, 0, 10, 1, 2, 9, 10, 2, 3, 8, 9, 3, 9, 2, 8, 3, 4, 7, 8, 4, 5, 6, 7, 5, 7, 4], "vertices": [2, 84, -78.87, 88.24, 0.09158, 83, -27.45, 77.77, 0.90842, 2, 84, -23.17, 68.78, 0.5314, 83, 30.37, 66.07, 0.4686, 2, 84, 14.39, 55.66, 0.95204, 83, 69.37, 58.17, 0.04796, 2, 85, -22.49, 26.83, 0.09309, 84, 75.27, 34.39, 0.90691, 2, 86, -37.99, 28.58, 0.03172, 85, 29.53, 35.98, 0.96828, 2, 86, 23.23, 53.57, 0.93895, 85, 94.65, 47.44, 0.06105, 3, 86, 60.64, -38.08, 0.99933, 85, 111.81, -50.06, 0.00063, 84, 154.91, -98.29, 4e-05, 4, 86, 2.6, -61.78, 0.49714, 85, 50.07, -60.93, 0.34724, 84, 95.72, -77.62, 0.12289, 83, 168.06, -62.82, 0.03273, 4, 86, -52.02, -84.07, 0.04535, 85, -8.04, -71.15, 0.16438, 84, 40.03, -58.16, 0.44623, 83, 110.24, -51.11, 0.34404, 4, 86, -102.83, -104.82, 4e-05, 85, -62.09, -80.66, 0.00199, 84, -11.78, -40.06, 0.00491, 83, 56.45, -40.22, 0.99306, 1, 83, 9.38, -30.69, 1, 1, 83, -43.73, -19.94, 1, 1, 83, -78.25, -12.95, 1, 2, 84, -108.88, 98.72, 0.04499, 83, -58.61, 84.08, 0.95501], "hull": 14, "edges": [10, 12, 24, 26, 22, 24, 0, 26, 22, 0, 20, 22, 0, 2, 20, 2, 18, 20, 2, 4, 18, 4, 16, 18, 4, 6, 16, 6, 12, 14, 14, 16, 6, 8, 8, 10, 14, 8], "width": 99, "height": 314}}, "g3_tocsau": {"g3_tocsau": {"type": "mesh", "uvs": [1, 0.33008, 1, 0.4334, 1, 0.54752, 1, 0.65873, 1, 0.76847, 1, 0.89013, 1, 1, 0, 1, 0, 0.88974, 0, 0.77735, 0, 0.66837, 0, 0.54867, 0, 0.44139, 0, 0.3265, 0, 0, 1, 0], "triangles": [13, 14, 15, 13, 15, 0, 13, 0, 1, 12, 13, 1, 12, 1, 2, 11, 12, 2, 11, 2, 3, 10, 11, 3, 10, 3, 4, 9, 10, 4, 5, 9, 4, 5, 8, 9, 7, 8, 5, 7, 5, 6], "vertices": [4, 104, -207.51, 170.23, 0.00491, 103, -133.62, 159.92, 0.02273, 102, -25.6, 154.27, 0.29594, 101, 53.25, 152.05, 0.67642, 4, 104, -126.36, 160.36, 0.06724, 103, -52.06, 154.33, 0.1588, 102, 56.12, 152.05, 0.53698, 101, 134.92, 155.66, 0.23698, 5, 105, -129.09, 146.08, 0.01003, 104, -36.72, 149.45, 0.34759, 103, 38.03, 148.15, 0.34682, 102, 146.38, 149.59, 0.27492, 101, 225.13, 159.66, 0.02065, 5, 106, -139.95, 112.87, 0.00801, 105, -41.48, 137.77, 0.17199, 104, 50.63, 138.83, 0.64064, 103, 125.82, 142.13, 0.13995, 102, 234.35, 147.2, 0.03941, 5, 106, -53.54, 121.53, 0.21261, 105, 44.96, 129.58, 0.48064, 104, 136.83, 128.34, 0.29929, 103, 212.45, 136.18, 0.00716, 102, 321.15, 144.84, 0.00031, 3, 106, 42.24, 131.12, 0.79759, 105, 140.8, 120.49, 0.18333, 104, 232.39, 116.71, 0.01908, 2, 106, 128.74, 139.78, 0.97914, 105, 227.34, 112.29, 0.02086, 2, 106, 151.06, -83.11, 0.98981, 105, 206.2, -110.71, 0.01019, 2, 106, 64.25, -91.8, 0.79986, 105, 119.35, -102.48, 0.20014, 4, 106, -24.24, -100.66, 0.18328, 105, 30.81, -94.09, 0.64114, 104, 116.74, -94.87, 0.16278, 103, 204.13, -87.77, 0.0128, 4, 106, -110.04, -109.25, 0.00062, 105, -55.03, -85.95, 0.12663, 104, 31.15, -84.46, 0.5387, 103, 118.1, -81.87, 0.33405, 3, 104, -62.88, -73.02, 0.00283, 103, 23.61, -75.39, 0.74462, 102, 141.2, -74.35, 0.25255, 3, 103, -61.08, -69.58, 0.06814, 102, 56.35, -72.04, 0.89228, 101, 151.15, -67.84, 0.03958, 2, 102, -34.52, -69.57, 0.1656, 101, 60.33, -71.86, 0.8344, 1, 101, -197.77, -83.29, 1, 1, 101, -207.67, 140.5, 1], "hull": 16, "edges": [12, 14, 28, 30, 26, 0, 24, 26, 0, 2, 24, 2, 22, 24, 2, 4, 22, 4, 20, 22, 4, 6, 20, 6, 18, 20, 6, 8, 18, 8, 14, 16, 16, 18, 8, 10, 10, 12, 16, 10, 30, 0, 26, 28], "width": 224, "height": 421}}, "g3_toctruoc1": {"g3_toctruoc1": {"type": "mesh", "uvs": [1, 0.31042, 1, 0.47907, 1, 0.58389, 1, 0.72737, 1, 0.85941, 1, 1, 0, 1, 0, 0.86524, 0, 0.73467, 0, 0.60114, 0, 0.47569, 0, 0.30949, 0, 0, 1, 0], "triangles": [11, 12, 13, 11, 13, 0, 10, 11, 0, 10, 0, 1, 10, 1, 2, 9, 10, 2, 9, 2, 3, 8, 9, 3, 8, 3, 4, 7, 8, 4, 5, 6, 7, 5, 7, 4], "vertices": [2, 76, -0.17, 32.64, 0.50516, 75, 80.03, 31.25, 0.49484, 3, 77, -36.15, 21.54, 0.00187, 76, 44.48, 30.79, 0.99764, 75, 122.18, 16.4, 0.00048, 2, 77, -9.19, 28.2, 0.29023, 76, 72.24, 29.65, 0.70977, 3, 78, -52.71, 11.89, 0.00259, 77, 27.72, 37.33, 0.96877, 76, 110.23, 28.08, 0.02863, 2, 78, -27.94, 36.6, 0.24626, 77, 61.69, 45.72, 0.75374, 2, 78, -1.56, 62.91, 0.61074, 77, 97.86, 54.66, 0.38926, 3, 78, 74.01, -12.84, 0.98346, 77, 123.53, -49.21, 0.01651, 76, 178, -81.81, 3e-05, 3, 78, 48.72, -38.06, 0.68731, 77, 88.87, -57.78, 0.28793, 76, 142.32, -80.34, 0.02475, 3, 78, 24.23, -62.5, 0.1798, 77, 55.28, -66.08, 0.66095, 76, 107.75, -78.91, 0.15925, 4, 78, -0.83, -87.49, 0.00932, 77, 20.93, -74.57, 0.5356, 76, 72.4, -77.45, 0.45391, 75, 117.13, -95.27, 0.00117, 3, 77, -11.35, -82.55, 0.24666, 76, 39.18, -76.08, 0.70145, 75, 85.78, -84.22, 0.05189, 3, 77, -54.11, -93.12, 0.04141, 76, -4.83, -74.26, 0.54564, 75, 44.24, -69.59, 0.41295, 2, 76, -86.77, -70.88, 0.01597, 75, -33.11, -42.33, 0.98403, 1, 75, 2.45, 58.59, 1], "hull": 14, "edges": [10, 12, 24, 26, 22, 0, 20, 22, 0, 2, 20, 2, 18, 20, 2, 4, 18, 4, 16, 18, 4, 6, 16, 6, 12, 14, 14, 16, 6, 8, 8, 10, 14, 8, 26, 0, 22, 24], "width": 107, "height": 265}}, "g3_toctruoc2": {"g3_toctruoc2": {"type": "mesh", "uvs": [1, 0.12836, 1, 0.2837, 1, 0.42709, 1, 0.61111, 1, 0.79007, 1, 1, 0, 1, 0, 0.79246, 0, 0.62306, 0, 0.42709, 0, 0.28848, 0, 0.1403, 0, 0, 1, 0], "triangles": [5, 6, 7, 8, 3, 4, 7, 8, 4, 5, 7, 4, 10, 1, 2, 9, 10, 2, 9, 2, 3, 8, 9, 3, 12, 13, 0, 11, 12, 0, 11, 0, 1, 10, 11, 1], "vertices": [2, 79, 16.91, 59.1, 0.88151, 80, -45.8, 64.17, 0.11849, 2, 79, 59.84, 56.24, 0.40448, 80, -3.39, 56.89, 0.59552, 2, 79, 99.48, 53.59, 0.02992, 80, 35.76, 50.17, 0.97008, 2, 80, 86, 41.54, 0.94026, 81, -5.39, 42.9, 0.05974, 3, 80, 134.85, 33.15, 0.22721, 81, 43.74, 49.49, 0.72634, 82, -26.45, 46.22, 0.04645, 3, 80, 192.16, 23.31, 0.00761, 81, 101.37, 57.23, 0.40721, 82, 29.51, 62.03, 0.58518, 2, 81, 118.52, -70.63, 0.26065, 82, 64.59, -62.11, 0.73935, 3, 80, 113.67, -94.1, 0.00837, 81, 61.54, -78.27, 0.67951, 82, 9.27, -77.74, 0.31212, 4, 79, 145.06, -78.73, 0.00371, 80, 67.43, -86.16, 0.17539, 81, 15.04, -84.51, 0.78812, 82, -35.88, -90.5, 0.03277, 3, 79, 90.9, -75.12, 0.19943, 80, 13.92, -76.97, 0.47177, 81, -38.76, -91.73, 0.32879, 3, 79, 52.58, -72.57, 0.63111, 80, -23.92, -70.47, 0.27073, 81, -76.82, -96.83, 0.09816, 3, 79, 11.63, -69.84, 0.94828, 80, -64.37, -63.53, 0.03922, 81, -117.5, -102.29, 0.0125, 1, 79, -27.15, -67.25, 1, 2, 79, -18.57, 61.46, 0.97828, 80, -80.84, 70.19, 0.02172], "hull": 14, "edges": [10, 12, 24, 26, 22, 24, 0, 26, 22, 0, 20, 22, 0, 2, 20, 2, 18, 20, 2, 4, 18, 4, 16, 18, 4, 6, 16, 6, 12, 14, 14, 16, 6, 8, 8, 10, 14, 8], "width": 129, "height": 277}}, "light2": {"light2": {"x": 22.28, "y": 16.4, "scaleX": 3.572, "scaleY": 3.572, "width": 486, "height": 508}}, "light3": {"light2": {"x": 22.28, "y": 16.4, "scaleX": 3.572, "scaleY": 3.572, "width": 486, "height": 508}}, "nau_body": {"nau_body": {"type": "mesh", "uvs": [0.88271, 0, 1, 0.14768, 1, 0.36301, 0.80534, 0.78297, 0.70473, 1, 0.50698, 1, 0.54178, 0.7732, 0.57619, 0.54898, 0.26967, 0.61162, 0, 0.3728, 0, 0.09286, 0.41799, 0.17117, 0.4649, 0.11702, 0.56631, 0, 0.67786, 0.09082, 0.70643, 0.19963, 0.6257, 0.47543], "triangles": [5, 6, 4, 4, 6, 3, 6, 7, 3, 7, 16, 3, 3, 16, 2, 15, 1, 2, 15, 0, 1, 9, 10, 11, 7, 8, 11, 8, 9, 11, 7, 11, 16, 16, 11, 15, 16, 15, 2, 15, 11, 12, 15, 12, 14, 12, 13, 14, 15, 14, 0, 14, 13, 0], "vertices": [3, 43, -138.91, 43.4, 0.3227, 40, -170.19, -28.58, 0, 44, -118.29, -21.19, 0.67729, 2, 43, -152.95, 143.77, 0.02034, 44, -71.88, 67.67, 0.97966, 1, 44, 90.58, 104.39, 1, 1, 44, 386.71, 69.25, 1, 1, 44, 554.33, 60.5, 1, 1, 44, 566.89, -104.97, 1, 3, 40, 379.97, 140.58, 0.19304, 41, -116.51, 180.5, 0.0662, 44, 439.28, -129.59, 0.74076, 3, 40, 219.46, 64.96, 0.38388, 41, -45.03, 61.7, 0.13164, 44, 259.63, -164.84, 0.48447, 2, 41, 91.61, 190.92, 0.97278, 42, -116.65, 197.9, 0.02722, 2, 41, 337.76, 109.25, 0.10104, 42, 124.38, 100.28, 0.89896, 2, 41, 363.39, -76.12, 0, 42, 138.13, -81.9, 1, 4, 43, 166.32, -64.25, 0.15051, 40, 135.18, -135.19, 0.09889, 41, 86.06, -120.05, 0.71584, 42, -140.65, -112.41, 0.03476, 4, 43, 108.68, -96.46, 0.38763, 40, 76.69, -167.65, 0.20838, 41, 84.65, -185.07, 0.40057, 42, -145.85, -178.24, 0.00341, 3, 43, 35.52, -92.86, 0.86221, 40, 4.42, -165.68, 0.05801, 41, 41.83, -246.18, 0.07978, 2, 43, 22.95, -12.12, 0.99992, 41, -33.86, -215.07, 8e-05, 3, 43, 106.74, 34.1, 0.02519, 40, 73.48, -41.57, 0.97417, 41, -33.43, -115.82, 0.00064, 3, 40, 159.27, 44.04, 0.52873, 41, -58.56, 0.33, 0.01711, 44, 199.21, -148.17, 0.45416], "hull": 14, "edges": [2, 4, 8, 10, 14, 16, 16, 18, 18, 20, 0, 26, 0, 26, 22, 24, 24, 26, 24, 30, 30, 32, 32, 14, 0, 2, 20, 22, 10, 12, 12, 14, 4, 6, 6, 8], "width": 392, "height": 660}}, "nau_chansau": {"nau_chansau": {"x": 77.54, "y": 6.2, "rotation": 90.7, "width": 232, "height": 384}}, "nau_chantruoc1": {"nau_chantruoc1": {"type": "mesh", "uvs": [0, 0.92552, 0.14622, 0.0649, 0.83868, 0.01537, 1, 0, 1, 0.81247, 0.81459, 0.91423, 0.78694, 0.40545, 0.09245, 1], "triangles": [7, 0, 6, 0, 1, 6, 6, 2, 3, 6, 1, 2, 5, 6, 4, 4, 6, 3], "vertices": [1, 45, -110.1, -32.95, 1, 1, 45, 34.05, 167.76, 1, 2, 46, -50.43, -10.07, 0.03327, 45, 436.01, 51.37, 0.96673, 2, 46, -38.79, 86.77, 0.49905, 45, 529.99, 25.27, 0.50095, 1, 46, 184.86, 49.85, 1, 1, 46, 194.64, -65.27, 1, 2, 46, 51.86, -58.63, 0.39267, 45, 372.59, -42.43, 0.60733, 1, 45, -77.64, -70.28, 1], "hull": 8, "edges": [0, 2, 6, 8, 8, 10, 10, 12, 12, 14, 2, 4, 4, 6, 12, 4, 14, 0], "width": 604, "height": 279}}, "nau_chantruoc3": {"nau_chantruoc1": {"type": "mesh", "uvs": [0, 0.92552, 0.14622, 0.0649, 0.83868, 0.01537, 1, 0, 1, 0.81247, 0.81459, 0.91423, 0.78694, 0.40545, 0.09245, 1], "triangles": [7, 0, 6, 0, 1, 6, 6, 2, 3, 6, 1, 2, 5, 6, 4, 4, 6, 3], "vertices": [1, 117, -110.1, -32.95, 1, 1, 117, 34.05, 167.76, 1, 2, 118, -50.43, -10.07, 0.03327, 117, 436.01, 51.37, 0.96673, 2, 118, -38.79, 86.77, 0.49905, 117, 529.99, 25.27, 0.50095, 1, 118, 184.86, 49.85, 1, 1, 118, 194.64, -65.27, 1, 2, 118, 51.86, -58.63, 0.39267, 117, 372.59, -42.43, 0.60733, 1, 117, -77.64, -70.28, 1], "hull": 8, "edges": [0, 2, 6, 8, 8, 10, 10, 12, 12, 14, 2, 4, 4, 6, 12, 4, 14, 0], "width": 604, "height": 279}}, "nau_head": {"nau_head": {"type": "mesh", "uvs": [0, 0, 0, 0.85481, 0.87283, 1, 0.99999, 0.24987, 0.6187, 0], "triangles": [1, 0, 4, 2, 4, 3, 2, 1, 4], "vertices": [1, 53, 107.07, 96.48, 1, 2, 51, 173.89, 82.08, 0.43914, 52, -52.53, 111, 0.56086, 1, 51, -13.61, 5.78, 1, 3, 51, 101.57, -206.02, 0.2579, 52, 100.41, -143.64, 0.6238, 53, 48.63, -135.71, 0.1183, 2, 52, 193.32, -71.97, 0.03068, 53, 120.04, -42.6, 0.96932], "hull": 5, "edges": [0, 8, 4, 6, 6, 8, 0, 2, 2, 4], "width": 237, "height": 335}}, "nau_nguc": {"nau_nguc": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0, 0.38849, 0.89941, 0.54026, 0.30244], "triangles": [5, 3, 0, 5, 1, 2, 5, 2, 3, 1, 4, 0, 4, 5, 0, 4, 1, 5], "vertices": [2, 67, -101.36, -11.37, 0.84549, 66, -117.1, 103.81, 0.15451, 2, 67, 51.49, 42.32, 0.99305, 66, 35.74, 157.49, 0.00695, 2, 67, 115.78, -140.72, 0.00865, 66, 100.03, -25.55, 0.99135, 2, 67, -37.07, -194.41, 0.00014, 66, -52.82, -79.23, 0.99986, 1, 67, -1.42, 3.05, 1, 2, 67, 13.76, -114.37, 0.00782, 66, -1.99, 0.81, 0.99218], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 162, "height": 194}}, "nau_taytruoc": {"nau_taytruoc": {"type": "mesh", "uvs": [0.91865, 0, 0.7427, 0, 0.50556, 0.61115, 0.22927, 0.30748, 0.24892, 0, 0.0865, 0, 0, 0.16184, 0.05083, 0.45203, 0.08559, 0.54587, 0.2538, 1, 0.53479, 0.99999, 0.92616, 0.41591, 0.91245, 0.38144, 0.9324, 0.2768, 0.93609, 0.16748, 0.93226, 0.08891, 0.19444, 0.13998, 0.09459, 0.255, 0.8433, 0.19325], "triangles": [6, 5, 16, 16, 5, 4, 7, 17, 3, 7, 6, 17, 17, 16, 3, 3, 16, 4, 17, 6, 16, 9, 2, 10, 9, 8, 2, 8, 3, 2, 8, 7, 3, 10, 2, 11, 18, 2, 1, 2, 12, 11, 2, 18, 12, 12, 18, 13, 13, 18, 14, 18, 15, 14, 15, 1, 0, 15, 18, 1], "vertices": [1, 47, -70.31, 2.42, 1, 1, 47, -19.59, -60.32, 1, 2, 47, 268.01, -45.81, 0.61782, 48, 50.75, -26.15, 0.38218, 2, 48, 248.83, -43.88, 0.72475, 49, 26.13, -35.98, 0.27525, 3, 48, 311.42, -135.14, 0.00022, 49, 135.16, -54.92, 0.25364, 50, -22.22, -71.8, 0.74614, 1, 50, 44.85, -35.54, 1, 2, 49, 82.21, 63.21, 0.23233, 50, 54.06, 32.81, 0.76767, 3, 48, 273.42, 42.2, 0.41023, 49, -18.3, 41.74, 0.57895, 50, -14.46, 109.4, 0.01082, 3, 48, 234.02, 55.1, 0.51123, 49, -55.05, 22.55, 0.47981, 50, -49.37, 131.78, 0.00896, 1, 48, 52.55, 117.13, 1, 2, 47, 342.72, 43.12, 0.28378, 48, -50.69, 32.01, 0.71622, 2, 47, 49.89, 87.79, 0.88632, 48, -46.04, -263.61, 0.11368, 1, 47, 47.52, 73.2, 1, 1, 47, 4.26, 65.48, 1, 1, 47, -40.03, 43.71, 1, 1, 47, -55.55, 30.44, 1, 3, 48, 296.33, -85.61, 0.0425, 49, 89.17, -31.14, 0.65181, 50, -24.39, -20.07, 0.30569, 2, 49, 49.07, 19.59, 0.83461, 50, -0.26, 39.92, 0.16539, 2, 47, 10.84, 15.09, 0.96845, 48, 32.1, -290.12, 0.03155], "hull": 16, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 4, 20, 14, 6, 8, 32, 32, 34, 28, 30, 24, 26, 26, 28, 20, 22, 22, 24, 2, 0, 30, 0, 14, 16, 16, 18], "width": 511, "height": 375}}, "nau_tocsau2": {"nau_tocsau2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 65, 175.1, 9.35, 1, 3, 65, -115.21, -112.89, 0.00338, 64, -55.37, -83.37, 0.01071, 63, 18.12, -91.65, 0.98591, 1, 63, -24.72, 35.32, 1, 2, 65, 123.1, 132.85, 0.99994, 64, 235.59, 96.97, 6e-05], "hull": 4, "edges": [2, 4, 0, 6, 0, 2, 4, 6], "width": 315, "height": 134}}, "nau_toctruoc1": {"nau_toctruoc1": {"type": "mesh", "uvs": [0, 0, 0, 0.38837, 0.45768, 0.47419, 0.36559, 0.63477, 0.22037, 0.75382, 0, 0.99949, 1, 1, 0.99999, 0], "triangles": [4, 3, 6, 5, 4, 6, 3, 2, 6, 7, 2, 0, 2, 7, 6, 2, 1, 0], "vertices": [2, 58, -33.85, -50.53, 0.99982, 57, -46.59, -90.69, 0.00018, 2, 58, 79.47, -18.7, 0.93391, 57, 70.87, -83.03, 0.06609, 5, 58, 75.19, 92.72, 0.00127, 57, 89.77, 26.86, 0.38503, 54, 63.03, -53.83, 0.12432, 55, 24.69, -49.16, 0.47618, 56, -38.51, -60.63, 0.01319, 3, 57, 140.93, 10.65, 0.02057, 55, 78.32, -47.04, 0.36555, 56, 12.71, -44.61, 0.61388, 3, 57, 184.51, -16.53, 4e-05, 55, 128.54, -57.81, 0.0075, 56, 64.01, -41.92, 0.99246, 1, 56, 158.71, -26.42, 1, 2, 55, 158.1, 154.82, 0.52921, 56, 37.15, 171.07, 0.47079, 2, 54, -34.92, 113.18, 0.99956, 55, -156.05, 20.23, 0.00044], "hull": 8, "edges": [0, 2, 4, 6, 6, 8, 8, 10, 2, 4, 14, 0, 12, 14, 10, 12], "width": 179, "height": 229}}, "nau_toctruoc2": {"nau_toctruoc2": {"type": "mesh", "uvs": [1, 0.31229, 1, 0.47994, 1, 0.60036, 1, 0.7113, 1, 0.85905, 1, 1, 0, 1, 0, 0.84938, 0, 0.71179, 0, 0.58064, 0, 0.46511, 0, 0.30257, 0, 0, 1, 0], "triangles": [11, 12, 13, 11, 13, 0, 10, 0, 1, 9, 1, 2, 10, 11, 0, 9, 10, 1, 8, 9, 2, 3, 8, 2, 7, 8, 3, 7, 3, 4, 6, 7, 4, 6, 4, 5], "vertices": [3, 62, -96.77, 131.15, 2e-05, 60, -6.66, 58.35, 0.08701, 59, 92.94, 52.29, 0.91298, 4, 62, -41.94, 104.97, 0.03854, 61, -8.16, 74.13, 0.01044, 60, 52.95, 70.09, 0.69506, 59, 153.04, 43.4, 0.25596, 4, 62, -2.57, 86.17, 0.24036, 61, 35.45, 75.44, 0.14365, 60, 95.76, 78.51, 0.55377, 59, 196.2, 37.02, 0.06222, 4, 62, 33.71, 68.85, 0.64924, 61, 75.63, 76.65, 0.12129, 60, 135.2, 86.28, 0.2196, 59, 235.97, 31.14, 0.00986, 3, 62, 82.02, 45.79, 0.97745, 61, 129.15, 78.25, 0.00261, 60, 187.73, 96.61, 0.01994, 1, 62, 128.11, 23.78, 1, 1, 62, 68.66, -100.75, 1, 2, 62, 19.41, -77.23, 0.97737, 61, 129.78, -59.79, 0.02263, 2, 62, -25.59, -55.75, 0.56511, 61, 79.95, -61.28, 0.43489, 2, 62, -68.47, -35.28, 0.06715, 61, 32.45, -62.71, 0.93285, 3, 62, -106.25, -17.24, 0.00018, 61, -9.4, -63.96, 0.87819, 60, 74.32, -66.35, 0.12162, 3, 61, -68.27, -65.73, 0.3032, 60, 16.53, -77.72, 0.56663, 59, 69.26, -83.7, 0.13016, 3, 61, -177.86, -69.01, 0.00209, 60, -91.04, -98.9, 0.09059, 59, -39.19, -67.66, 0.90733, 1, 59, -19, 68.85, 1], "hull": 14, "edges": [10, 12, 24, 26, 22, 0, 20, 22, 0, 2, 20, 2, 18, 20, 2, 4, 18, 4, 16, 18, 4, 6, 16, 6, 12, 14, 14, 16, 6, 8, 8, 10, 14, 8, 22, 24, 26, 0], "width": 113, "height": 254}}, "shadow": {"shadow": {"x": 98.39, "y": 145.95, "scaleX": 4.97, "scaleY": 4.97, "width": 506, "height": 119}}}}, "animations": {"animation": {"slots": {"g2_head": {"attachment": [{"time": 0, "name": "g2_head"}, {"time": 0.8333, "name": "g2_head"}]}, "g3_duisau": {"attachment": [{"time": 0.4667, "name": "g3_duisau"}]}, "g3_duitruoc": {"attachment": [{"time": 0, "name": "g3_duitruoc"}, {"time": 0.4, "name": "g3_duitruoc"}]}, "light2": {"color": [{"time": 0, "color": "ffffffa6", "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333, "color": "ffffff78"}, {"time": 0.7333, "color": "ffffffaf", "curve": [0.279, 0, 0.622, 0.39]}, {"time": 0.8333, "color": "ffffffa6"}]}, "light3": {"color": [{"time": 0, "color": "ffffffa5", "curve": [0.379, 0.6, 0.724, 1]}, {"time": 0.1, "color": "ffffffaf", "curve": [0.248, 0, 0.629, 0.52]}, {"time": 0.3333, "color": "ffffff91", "curve": [0.378, 0.52, 0.747, 1]}, {"time": 0.5333, "color": "ffffff78", "curve": [0.242, 0, 0.667, 0.67]}, {"time": 0.8333, "color": "ffffffa5"}]}}, "bones": {"main23": {"rotate": [{"time": 0, "angle": 3}, {"time": 0.3333, "angle": 1.18, "curve": "stepped"}, {"time": 0.4667, "angle": 1.18}, {"time": 0.8333, "angle": 3}]}, "main20": {"rotate": [{"time": 0, "angle": 0.51}, {"time": 0.3333, "angle": 0.1}, {"time": 0.4, "angle": 0}, {"time": 0.8333, "angle": 0.51}]}, "bone36": {"rotate": [{"time": 0, "angle": -10.03, "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333, "angle": 0}, {"time": 0.5333, "angle": -4.6}, {"time": 0.7333, "angle": -11.99, "curve": [0.279, 0, 0.622, 0.39]}, {"time": 0.8333, "angle": -10.03}]}, "bone2": {"translate": [{"time": 0, "x": -19.19, "y": 0, "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.7333, "x": -22.95, "y": 0, "curve": [0.279, 0, 0.622, 0.39]}, {"time": 0.8333, "x": -19.19, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": 1.52, "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333, "angle": 0.39}, {"time": 0.7333, "angle": 1.74, "curve": [0.279, 0, 0.622, 0.39]}, {"time": 0.8333, "angle": 1.52}]}, "bone4": {"translate": [{"time": 0, "x": 2.96, "y": -8.28, "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.7333, "x": 3.54, "y": -9.9, "curve": [0.279, 0, 0.622, 0.39]}, {"time": 0.8333, "x": 2.96, "y": -8.28}]}, "bone5": {"rotate": [{"time": 0, "angle": -8.73}, {"time": 0.0667, "angle": -9.07}, {"time": 0.3333, "angle": 0}, {"time": 0.4667, "angle": 2.15}, {"time": 0.6667, "angle": -4.79}, {"time": 0.8333, "angle": -8.73}], "translate": [{"time": 0, "x": -15.13, "y": -6.3}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.6667, "x": -19.67, "y": -8.2}, {"time": 0.8333, "x": -15.13, "y": -6.3}]}, "bone14": {"rotate": [{"time": 0, "angle": 3.44}, {"time": 0.3333, "angle": 0}, {"time": 0.7333, "angle": 4.47}, {"time": 0.8333, "angle": 3.44}]}, "bone15": {"rotate": [{"time": 0, "angle": 1.17}, {"time": 0.3333, "angle": 0}, {"time": 0.7333, "angle": 1.52}, {"time": 0.8333, "angle": 1.17}]}, "bone16": {"rotate": [{"time": 0, "angle": 12.04}, {"time": 0.3333, "angle": 2.41}, {"time": 0.4, "angle": 0}, {"time": 0.8333, "angle": 12.04}]}, "bone17": {"rotate": [{"time": 0, "angle": 5.02}, {"time": 0.2333, "angle": 12.04}, {"time": 0.3333, "angle": 9.63}, {"time": 0.6667, "angle": 0}, {"time": 0.8333, "angle": 5.02}]}, "bone18": {"rotate": [{"time": 0, "angle": -8.09}, {"time": 0.3333, "angle": -1.62}, {"time": 0.4, "angle": 0}, {"time": 0.8333, "angle": -8.09}]}, "bone19": {"rotate": [{"time": 0, "angle": -8.88}, {"time": 0.0667, "angle": -10.65}, {"time": 0.3333, "angle": -4.26}, {"time": 0.5, "angle": 0}, {"time": 0.8333, "angle": -8.88}]}, "bone20": {"rotate": [{"time": 0, "angle": -9.11}, {"time": 0.1667, "angle": -14.8}, {"time": 0.3333, "angle": -8.88}, {"time": 0.5667, "angle": 0}, {"time": 0.8333, "angle": -9.11}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "bone21": {"rotate": [{"time": 0, "angle": -14.7}, {"time": 0.0333, "angle": -15.93}, {"time": 0.3333, "angle": -4.25}, {"time": 0.4333, "angle": 0}, {"time": 0.8333, "angle": -14.7}]}, "bone22": {"rotate": [{"time": 0, "angle": -12.04}, {"time": 0.1667, "angle": -19.57}, {"time": 0.3333, "angle": -11.74}, {"time": 0.5667, "angle": 0}, {"time": 0.8333, "angle": -12.04}]}, "bone23": {"rotate": [{"time": 0, "angle": -8.07}, {"time": 0.2667, "angle": -24.2}, {"time": 0.3333, "angle": -20.98}, {"time": 0.7, "angle": 0}, {"time": 0.8333, "angle": -8.07}]}, "bone24": {"rotate": [{"time": 0, "angle": 10.03}, {"time": 0.0667, "angle": 12.04}, {"time": 0.3333, "angle": 4.82}, {"time": 0.5, "angle": 0}, {"time": 0.8333, "angle": 10.03}]}, "bone25": {"rotate": [{"time": 0, "angle": 6.48}, {"time": 0.2, "angle": 12.04}, {"time": 0.3333, "angle": 8.03}, {"time": 0.6, "angle": 0}, {"time": 0.8333, "angle": 6.48}]}, "g3": {"rotate": [{"time": 0, "angle": 2.81, "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 3.36, "curve": [0.279, 0, 0.622, 0.39]}, {"time": 0.8333, "angle": 2.81}], "translate": [{"time": 0, "x": 9.79, "y": 0, "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.7333, "x": 11.7, "y": 0, "curve": [0.279, 0, 0.622, 0.39]}, {"time": 0.8333, "x": 9.79, "y": 0}]}, "bone27": {"rotate": [{"time": 0, "angle": 3.96}, {"time": 0.0667, "angle": 3.9}, {"time": 0.3333, "angle": 0}, {"time": 0.4667, "angle": -1.69}, {"time": 0.7333, "angle": 4.05}, {"time": 0.8333, "angle": 3.96}], "translate": [{"time": 0, "x": 1.61, "y": 0.27}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.7333, "x": 2.09, "y": 0.36}, {"time": 0.8333, "x": 1.61, "y": 0.27}]}, "bone28": {"translate": [{"time": 0, "x": -29.82, "y": 2.02}, {"time": 0.0667, "x": -33.38, "y": 1.3}, {"time": 0.3333, "x": 1.95, "y": 5.51, "curve": [0.324, 0.3, 0.661, 0.65]}, {"time": 0.4667, "x": 5.25, "y": 4.83}, {"time": 0.8333, "x": -29.82, "y": 2.02}]}, "bone29": {"translate": [{"time": 0, "x": -42.07, "y": -7.13}, {"time": 0.0667, "x": -49.76, "y": -9.04, "curve": [0.26, 0, 0.618, 0.44]}, {"time": 0.3333, "x": -12.42, "y": 6.51, "curve": [0.329, 0.32, 0.664, 0.66]}, {"time": 0.4667, "x": 0.22, "y": 3.34}, {"time": 0.8333, "x": -42.07, "y": -7.13}]}, "bone30": {"translate": [{"time": 0, "x": -4.12, "y": -0.79, "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.7333, "x": -4.93, "y": -0.94, "curve": [0.279, 0, 0.622, 0.39]}, {"time": 0.8333, "x": -4.12, "y": -0.79}]}, "bone31": {"rotate": [{"time": 0, "angle": 2.81, "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333, "angle": 0}, {"time": 0.7333, "angle": 3.37, "curve": [0.279, 0, 0.622, 0.39]}, {"time": 0.8333, "angle": 2.81}]}, "bone32": {"rotate": [{"time": 0, "angle": 3.11}, {"time": 0.0667, "angle": 3.67}, {"time": 0.3333, "angle": 1.22}, {"time": 0.4667, "angle": 0}, {"time": 0.8333, "angle": 3.11}]}, "bone33": {"rotate": [{"time": 0, "angle": 11.76}, {"time": 0.3333, "angle": 2.35}, {"time": 0.4, "angle": 0}, {"time": 0.8333, "angle": 11.76}]}, "bone34": {"rotate": [{"time": 0, "angle": 13.16}, {"time": 0.0667, "angle": 15.8}, {"time": 0.3333, "angle": 6.32}, {"time": 0.5, "angle": 0}, {"time": 0.8333, "angle": 13.16}]}, "bone35": {"rotate": [{"time": 0, "angle": 9.51}, {"time": 0.1667, "angle": 15.46}, {"time": 0.3333, "angle": 9.27}, {"time": 0.5667, "angle": 0}, {"time": 0.8333, "angle": 9.51}]}, "bone37": {"rotate": [{"time": 0, "angle": -3.28, "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333, "angle": 0}, {"time": 0.7333, "angle": -3.92, "curve": [0.279, 0, 0.622, 0.39]}, {"time": 0.8333, "angle": -3.28}]}, "bone38": {"rotate": [{"time": 0, "angle": 4.23, "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333, "angle": 0}, {"time": 0.7333, "angle": 5.06, "curve": [0.279, 0, 0.622, 0.39]}, {"time": 0.8333, "angle": 4.23}], "translate": [{"time": 0, "x": 0.6, "y": 7.88, "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.7333, "x": 0.71, "y": 9.42, "curve": [0.279, 0, 0.622, 0.39]}, {"time": 0.8333, "x": 0.6, "y": 7.88}]}, "bone39": {"rotate": [{"time": 0, "angle": 10.2, "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333, "angle": 0}, {"time": 0.7333, "angle": 12.19, "curve": [0.279, 0, 0.622, 0.39]}, {"time": 0.8333, "angle": 10.2}]}, "g_nau": {"translate": [{"time": 0, "x": -31.7, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5667, "x": 26.6, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -31.7, "y": 0}]}, "bone41": {"translate": [{"time": 0, "x": -1.24, "y": 1.04, "curve": [0.377, 0.61, 0.72, 1]}, {"time": 0.0667, "x": -1.47, "y": 1.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": [0.243, 0, 0.674, 0.69]}, {"time": 0.8333, "x": -1.24, "y": 1.04}]}, "bone42": {"rotate": [{"time": 0, "angle": -1.36}, {"time": 0.0667, "angle": -1.75}, {"time": 0.3333, "angle": 0, "curve": "stepped"}, {"time": 0.6, "angle": 0}, {"time": 0.8333, "angle": -1.36}]}, "bone43": {"translate": [{"time": 0, "x": 6.98, "y": -5.85}, {"time": 0.0667, "x": 11.64, "y": -9.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 14.59, "y": -12.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7333, "x": 0, "y": 0}, {"time": 0.8333, "x": 6.98, "y": -5.85}]}, "bone46": {"rotate": [{"time": 0, "angle": 5.23}, {"time": 0.1, "angle": 6.71}, {"time": 0.3333, "angle": 0}, {"time": 0.7333, "angle": 3.74}, {"time": 0.8333, "angle": 5.23}]}, "bone47": {"rotate": [{"time": 0, "angle": -0.07, "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333, "angle": 0}, {"time": 0.7333, "angle": -0.08, "curve": [0.279, 0, 0.622, 0.39]}, {"time": 0.8333, "angle": -0.07}]}, "bone49": {"rotate": [{"time": 0, "angle": 8.48, "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333, "angle": 0}, {"time": 0.7333, "angle": 10.14, "curve": [0.279, 0, 0.622, 0.39]}, {"time": 0.8333, "angle": 8.48}], "translate": [{"time": 0, "x": 1.8, "y": 2.24, "curve": [0.318, 0.28, 0.655, 0.63]}, {"time": 0.0667, "x": 3.88, "y": 4.81, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7333, "x": 0, "y": 0, "curve": [0.277, 0, 0.621, 0.4]}, {"time": 0.8333, "x": 1.8, "y": 2.24}]}, "bone50": {"rotate": [{"time": 0, "angle": -19.69, "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333, "angle": 0}, {"time": 0.7333, "angle": -23.53, "curve": [0.279, 0, 0.622, 0.39]}, {"time": 0.8333, "angle": -19.69}]}, "bone52": {"rotate": [{"time": 0, "angle": -3.71}, {"time": 0.1333, "angle": -4.86}, {"time": 0.3333, "angle": 0}, {"time": 0.7333, "angle": -2.85}, {"time": 0.8333, "angle": -3.71}], "translate": [{"time": 0, "x": -1.99, "y": -1.48, "curve": [0.367, 0.46, 0.754, 1]}, {"time": 0.1333, "x": -4.91, "y": -3.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7333, "x": 0, "y": 0, "curve": [0.255, 0, 0.62, 0.47]}, {"time": 0.8333, "x": -1.99, "y": -1.48}]}, "bone53": {"rotate": [{"time": 0, "angle": -2.2}, {"time": 0.3333, "angle": 0}, {"time": 0.7333, "angle": -2.85}, {"time": 0.8333, "angle": -2.2}]}, "bone54": {"rotate": [{"time": 0, "angle": -4.04}, {"time": 0.1667, "angle": -6.57}, {"time": 0.3333, "angle": -3.94}, {"time": 0.5667, "angle": 0}, {"time": 0.8333, "angle": -4.04}]}, "bone55": {"rotate": [{"time": 0, "angle": -3.94}, {"time": 0.2333, "angle": -9.46}, {"time": 0.3333, "angle": -7.56}, {"time": 0.6667, "angle": 0}, {"time": 0.8333, "angle": -3.94}]}, "bone56": {"rotate": [{"time": 0, "angle": -2.55}, {"time": 0.3333, "angle": -11.03}, {"time": 0.7333, "angle": 0}, {"time": 0.8333, "angle": -2.55}]}, "bone57": {"rotate": [{"time": 0, "angle": -7.44}, {"time": 0.0667, "angle": -8.92}, {"time": 0.3333, "angle": -3.57}, {"time": 0.5, "angle": 0}, {"time": 0.8333, "angle": -7.44}]}, "bone58": {"rotate": [{"time": 0, "angle": -8.69}, {"time": 0.3333, "angle": -1.74}, {"time": 0.4, "angle": 0}, {"time": 0.8333, "angle": -8.69}]}, "bone59": {"rotate": [{"time": 0, "angle": -8.22}, {"time": 0.0333, "angle": -8.9}, {"time": 0.3333, "angle": -2.37}, {"time": 0.4333, "angle": 0}, {"time": 0.8333, "angle": -8.22}]}, "bone60": {"rotate": [{"time": 0, "angle": -9.41}, {"time": 0.0667, "angle": -11.29}, {"time": 0.3333, "angle": -4.51}, {"time": 0.5, "angle": 0}, {"time": 0.8333, "angle": -9.41}]}, "bone61": {"rotate": [{"time": 0, "angle": -8.83}, {"time": 0.1667, "angle": -14.34}, {"time": 0.3333, "angle": -8.61}, {"time": 0.5667, "angle": 0}, {"time": 0.8333, "angle": -8.83}]}, "bone62": {"rotate": [{"time": 0, "angle": -8}, {"time": 0.2333, "angle": -19.2}, {"time": 0.3333, "angle": -15.36}, {"time": 0.6667, "angle": 0}, {"time": 0.8333, "angle": -8}]}, "bone63": {"rotate": [{"time": 0, "angle": -0.77}, {"time": 0.0667, "angle": -0.92}, {"time": 0.3333, "angle": -0.31}, {"time": 0.4667, "angle": 0}, {"time": 0.8333, "angle": -0.77}]}, "bone64": {"rotate": [{"time": 0, "angle": -0.02}, {"time": 0.2, "angle": -0.03}, {"time": 0.3333, "angle": -0.02}, {"time": 0.6, "angle": 0}, {"time": 0.8333, "angle": -0.02}]}, "bone65": {"rotate": [{"time": 0, "angle": -0.76}, {"time": 0.3333, "angle": -3.29}, {"time": 0.7333, "angle": 0}, {"time": 0.8333, "angle": -0.76}]}, "bone67": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3333, "angle": 0, "curve": "stepped"}, {"time": 0.8333, "angle": 0}], "translate": [{"time": 0, "x": 0.64, "y": 6.27, "curve": [0.32, 0.29, 0.757, 1]}, {"time": 0.1333, "x": 19.45, "y": 6.83, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5667, "x": 16.8, "y": 5.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": -2.17, "y": 6.19, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 0.8333, "x": 0.64, "y": 6.27}]}, "main2": {"rotate": [{"time": 0, "angle": -0.94, "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333, "angle": 0}, {"time": 0.7333, "angle": -1.13, "curve": [0.279, 0, 0.622, 0.39]}, {"time": 0.8333, "angle": -0.94}], "translate": [{"time": 0, "x": -5.09, "y": 0, "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.7333, "x": -6.09, "y": 0, "curve": [0.279, 0, 0.622, 0.39]}, {"time": 0.8333, "x": -5.09, "y": 0}]}, "main3": {"rotate": [{"time": 0, "angle": 1.43, "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333, "angle": 0}, {"time": 0.7333, "angle": 1.71, "curve": [0.279, 0, 0.622, 0.39]}, {"time": 0.8333, "angle": 1.43}], "translate": [{"time": 0, "x": 1.52, "y": 3.24, "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.7333, "x": 1.82, "y": 3.87, "curve": [0.279, 0, 0.622, 0.39]}, {"time": 0.8333, "x": 1.52, "y": 3.24}]}, "main4": {"rotate": [{"time": 0, "angle": 4.35}, {"time": 0.3333, "angle": 0.87}, {"time": 0.4, "angle": 0}, {"time": 0.8333, "angle": 4.35}]}, "main5": {"translate": [{"time": 0, "x": -28.37, "y": 6.52}, {"time": 0.1, "x": -15.53, "y": 9.73}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.4, "x": 1.47, "y": -3.66}, {"time": 0.5667, "x": -14.57, "y": -3.77}, {"time": 0.7333, "x": -31.92, "y": 4.06}, {"time": 0.8, "x": -32.65, "y": 5.45}, {"time": 0.8333, "x": -28.37, "y": 6.52}]}, "main6": {"translate": [{"time": 0, "x": -30.47, "y": 8.22}, {"time": 0.1, "x": -18.72, "y": -0.72}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.4, "x": 2.65, "y": 5.84}, {"time": 0.5333, "x": -11.84, "y": 16.61}, {"time": 0.7333, "x": -32.17, "y": 17.29}, {"time": 0.8, "x": -34.38, "y": 11.2}, {"time": 0.8333, "x": -30.47, "y": 8.22}]}, "main8": {"rotate": [{"time": 0, "angle": 3.24}, {"time": 0.0333, "angle": 3.51}, {"time": 0.3333, "angle": 0.94}, {"time": 0.4333, "angle": 0}, {"time": 0.8333, "angle": 3.24}]}, "main9": {"rotate": [{"time": 0, "angle": 2.34}, {"time": 0.1333, "angle": 3.51}, {"time": 0.3333, "angle": 1.87}, {"time": 0.5667, "angle": 0}, {"time": 0.8333, "angle": 2.34}]}, "main10": {"rotate": [{"time": 0, "angle": 1.46}, {"time": 0.2333, "angle": 3.51}, {"time": 0.3333, "angle": 2.81}, {"time": 0.6667, "angle": 0}, {"time": 0.8333, "angle": 1.46}]}, "main11": {"rotate": [{"time": 0, "angle": 0.55}, {"time": 0.3333, "angle": 3.28}, {"time": 0.3667, "angle": 3.51}, {"time": 0.7667, "angle": 0}, {"time": 0.8333, "angle": 0.55}]}, "main12": {"rotate": [{"time": 0, "angle": 6.06}, {"time": 0.3333, "angle": 1.21}, {"time": 0.4, "angle": 0}, {"time": 0.8333, "angle": 6.06}]}, "main13": {"rotate": [{"time": 0, "angle": 5.05}, {"time": 0.0667, "angle": 6.06}, {"time": 0.3333, "angle": 2.42}, {"time": 0.5, "angle": 0}, {"time": 0.8333, "angle": 5.05}]}, "main14": {"rotate": [{"time": 0, "angle": 3.73}, {"time": 0.1667, "angle": 6.06}, {"time": 0.3333, "angle": 3.63}, {"time": 0.5667, "angle": 0}, {"time": 0.8333, "angle": 3.73}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "main15": {"rotate": [{"time": 0, "angle": 2.52}, {"time": 0.2333, "angle": 6.06}, {"time": 0.3333, "angle": 4.85}, {"time": 0.6667, "angle": 0}, {"time": 0.8333, "angle": 2.52}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.2333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "main16": {"rotate": [{"time": 0, "angle": -8.99}, {"time": 0.3333, "angle": -1.8}, {"time": 0.4, "angle": 0}, {"time": 0.8333, "angle": -8.99}]}, "main17": {"rotate": [{"time": 0, "angle": -7.49}, {"time": 0.0667, "angle": -8.99}, {"time": 0.3333, "angle": -3.6}, {"time": 0.5, "angle": 0}, {"time": 0.8333, "angle": -7.49}]}, "main18": {"rotate": [{"time": 0, "angle": -5.53}, {"time": 0.1667, "angle": -8.99}, {"time": 0.3333, "angle": -5.39}, {"time": 0.5667, "angle": 0}, {"time": 0.8333, "angle": -5.53}]}, "main19": {"rotate": [{"time": 0, "angle": -3.75}, {"time": 0.2333, "angle": -8.99}, {"time": 0.3333, "angle": -7.19}, {"time": 0.6667, "angle": 0}, {"time": 0.8333, "angle": -3.75}]}, "main21": {"rotate": [{"time": 0, "angle": -3.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -5.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -3.42}]}, "main22": {"rotate": [{"time": 0, "angle": -3.74}, {"time": 0.3333, "angle": -8.44}, {"time": 0.8333, "angle": -3.74}]}, "main24": {"rotate": [{"time": 0, "angle": -1.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -2.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -1.71}]}, "main25": {"rotate": [{"time": 0, "angle": 4.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 1.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 4.69}]}, "bone26": {"rotate": [{"time": 0, "angle": 15.19, "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 18.16, "curve": [0.279, 0, 0.622, 0.39]}, {"time": 0.8333, "angle": 15.19}], "translate": [{"time": 0, "x": -4.6, "y": 4.68, "curve": [0.286, 0.17, 0.643, 0.58]}, {"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.7333, "x": -5.14, "y": 5.23, "curve": [0.301, 0, 0.637, 0.36]}, {"time": 0.8333, "x": -4.6, "y": 4.68}], "scale": [{"time": 0, "x": 0.812, "y": 1, "curve": [0.286, 0.17, 0.643, 0.58]}, {"time": 0.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.7333, "x": 0.791, "y": 1, "curve": [0.301, 0, 0.637, 0.36]}, {"time": 0.8333, "x": 0.812, "y": 1}]}, "nau1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3333, "angle": 0, "curve": "stepped"}, {"time": 0.7333, "angle": 0, "curve": "stepped"}, {"time": 0.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.7333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "nau2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3333, "angle": 0, "curve": "stepped"}, {"time": 0.7333, "angle": 0, "curve": "stepped"}, {"time": 0.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.7333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "main26": {"rotate": [{"time": 0, "angle": -7.67}, {"time": 0.3333, "angle": -1.53}, {"time": 0.4, "angle": 0}, {"time": 0.8333, "angle": -7.67}]}, "main27": {"rotate": [{"time": 0, "angle": -6.39}, {"time": 0.0667, "angle": -7.67}, {"time": 0.3333, "angle": -3.07}, {"time": 0.5, "angle": 0}, {"time": 0.8333, "angle": -6.39}]}, "main28": {"rotate": [{"time": 0, "angle": -4.72}, {"time": 0.1667, "angle": -7.67}, {"time": 0.3333, "angle": -4.6}, {"time": 0.5667, "angle": 0}, {"time": 0.8333, "angle": -4.72}]}, "main29": {"rotate": [{"time": 0, "angle": -3.2}, {"time": 0.2333, "angle": -7.67}, {"time": 0.3333, "angle": -6.14}, {"time": 0.6667, "angle": 0}, {"time": 0.8333, "angle": -3.2}]}, "main30": {"rotate": [{"time": 0, "angle": -1.77}, {"time": 0.3333, "angle": -7.67}, {"time": 0.7333, "angle": 0}, {"time": 0.8333, "angle": -1.77}]}, "main31": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3333, "angle": -6.14}, {"time": 0.4, "angle": -7.67}, {"time": 0.8333, "angle": 0}]}, "main32": {"rotate": [{"time": 0, "angle": 3.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 4.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 3.73}], "translate": [{"time": 0, "x": -18.54, "y": 9.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": -5.35, "y": 2.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -18.54, "y": 9.86}]}, "main33": {"rotate": [{"time": 0, "angle": -4.46}, {"time": 0.0667, "angle": -2.57}, {"time": 0.3333, "angle": -9.37}, {"time": 0.5, "angle": -10.76}, {"time": 0.8333, "angle": -4.46}]}, "nau4": {"translate": [{"time": 0, "x": -3.52, "y": -8.07}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.7333, "x": -4.58, "y": -10.49}, {"time": 0.8333, "x": -3.52, "y": -8.07}]}, "bone": {"scale": [{"time": 0, "x": 0.947, "y": 1}, {"time": 0.3333, "x": 1, "y": 1}, {"time": 0.7333, "x": 0.93, "y": 1}, {"time": 0.8333, "x": 0.947, "y": 1}]}, "bone69": {"scale": [{"time": 0, "x": 1.27, "y": 1.27, "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333, "x": 1, "y": 1}, {"time": 0.7333, "x": 1.323, "y": 1.323, "curve": [0.279, 0, 0.622, 0.39]}, {"time": 0.8333, "x": 1.27, "y": 1.27}]}, "bone70": {"scale": [{"time": 0, "x": 1.242, "y": 1.242}, {"time": 0.1, "x": 1.323, "y": 1.323, "curve": [0.248, 0, 0.629, 0.52]}, {"time": 0.3333, "x": 1.145, "y": 1.145, "curve": [0.378, 0.52, 0.747, 1]}, {"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8333, "x": 1.242, "y": 1.242}]}, "bone73": {"rotate": [{"time": 0, "angle": 5.23}, {"time": 0.1, "angle": 6.71}, {"time": 0.3333, "angle": 0}, {"time": 0.7333, "angle": 3.74}, {"time": 0.8333, "angle": 5.23}]}}, "deform": {"default": {"boy_body": {"boy_body": [{"time": 0, "offset": 40, "vertices": [0.60797, 14.07242, 5.64183, 12.90573, 8.97322, 3.58249, 9.66119, 0.10556, 0.2162, -6.31943, -5.90765, -2.25392, 0.76196, 6.27695, 19.97225, -4.63736, 3.65692, -6.09502, -7.09595, -0.41342, -2.92051, -23.453, -23.17505, -4.63501, 6.64047, 22.68201], "curve": [0.243, 0, 0.68, 0.71]}, {"time": 0.3333, "offset": 40, "vertices": [0.0791, 1.83078, 0.73399, 1.679, -0.04242, 1.39667, 0.4641, 1.31791, 0.02813, -0.82214, -0.76857, -0.29323, 0.09913, 0.81661, 0.3266, -0.02325, 0, 0, 0, 0, -6.38609, -12.45773, -14.00006, 2e-05, 6.23485, 12.53488], "curve": [0.375, 0.62, 0.716, 1]}, {"time": 0.8333, "offset": 40, "vertices": [0.60797, 14.07242, 5.64183, 12.90573, 8.97322, 3.58249, 9.66119, 0.10556, 0.2162, -6.31943, -5.90765, -2.25392, 0.76196, 6.27695, 19.97225, -4.63736, 3.65692, -6.09502, -7.09595, -0.41342, -2.92051, -23.453, -23.17505, -4.63501, 6.64047, 22.68201]}]}, "boy_duitruoc": {"boy_duitruoc": [{"time": 0, "offset": 6, "vertices": [14.08875, 23.23398]}, {"time": 0.3333, "offset": 6, "vertices": [4.83646, 1.37943]}, {"time": 0.5, "offset": 6, "vertices": [5.02975, 6.72226]}, {"time": 0.6667, "offset": 6, "vertices": [12.17694, 20.18457]}, {"time": 0.8333, "offset": 6, "vertices": [14.08875, 23.23398]}]}, "boy_head": {"boy_head": [{"time": 0, "vertices": [1.93609, -5.07442, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.17719, -2.19866, 2.17749, -2.1986, 0, 0, 3.23668, 2.69185, 3.23658, 2.69185, 2.73882, -0.23017, 2.76307, -0.23091, 2.68858, 3.7859, 2.6312, 3.81261, 2.02265, -0.52817, 2.07342, -0.43377, -1.55864, -2.80593, -1.47807, -2.84571, 1.7318, 2.1799, 1.68074, 2.24327, -1.19974, 1.07, -1.22315, 0.98621, -1.08849, 1.09912, -1.08849, 1.09924, 0, 0, 0, 0, -2.49293, 8.63972, -2.53428, 8.63767, -0.3001, 1.6044, -0.34197, 1.6023, 1.51651, 3.47245, 1.47442, 3.47037, -2.99992, 1.09864, -3.04198, 1.09676, -2.99992, 1.09864, -3.04198, 1.09676, 0, 0, 0, 0, -0.87128, -2.90527, -0.81171, -2.92264, 3.0145, -0.33698, 2.93594, 0.75983], "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333, "offset": 52, "vertices": [-0.44354, -2.28583, -0.44144, -2.2858, -0.46851, 1.66986, -0.46762, 1.67001, 3.35266, 2.01846, 3.35287, 2.01862, 0, 0, 0, 0, -2.73804, -0.36111, -2.73633, -0.36099, 0, 0, 0, 0, 0.9516, -2.14178, 0.953, -2.14175, 0, 0, 0, 0, 0, 0, 0, 0, -0.14484, -2.9536, -0.14487, -2.95352, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.26636, 1.62679, -1.26627, 1.62704]}, {"time": 0.5, "vertices": [0.64536, -1.69147, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.72573, -0.73289, 0.72583, -0.73287, 0, 0, 0.7832, -0.6266, 0.78457, -0.62658, 0.6006, 1.03652, 0.60927, 1.03637, 3.1313, 2.60761, 3.11232, 2.61661, 0.67422, -0.17606, 0.69114, -0.14459, -2.3449, -1.17605, -2.31691, -1.18923, 0.57727, 0.72663, 0.56025, 0.74776, 0.23449, -1.07119, 0.22762, -1.0991, -0.36283, 0.36637, -0.36283, 0.36641, 0, 0, 0, 0, -0.89777, 0.22761, -0.89754, 0.22767, -0.07027, -0.14843, -0.07019, -0.14843, 0.53527, 0.47426, 0.53528, 0.47425, -1.03472, -3.17326, -1.003, -3.1737, -1.03472, -3.17326, -1.003, -3.1737, -0.84424, 1.08452, -0.84418, 1.0847, -0.29043, -0.96842, -0.27057, -0.97421, 1.00483, -0.11233, 0.97865, 0.25328]}, {"time": 0.8333, "vertices": [1.93609, -5.07442, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.17719, -2.19866, 2.17749, -2.1986, 0, 0, 3.23668, 2.69185, 3.23658, 2.69185, 2.73882, -0.23017, 2.76307, -0.23091, 2.68858, 3.7859, 2.6312, 3.81261, 2.02265, -0.52817, 2.07342, -0.43377, -1.55864, -2.80593, -1.47807, -2.84571, 1.7318, 2.1799, 1.68074, 2.24327, -1.19974, 1.07, -1.22315, 0.98621, -1.08849, 1.09912, -1.08849, 1.09924, 0, 0, 0, 0, -2.49293, 8.63972, -2.53428, 8.63767, -0.3001, 1.6044, -0.34197, 1.6023, 1.51651, 3.47245, 1.47442, 3.47037, -2.99992, 1.09864, -3.04198, 1.09676, -2.99992, 1.09864, -3.04198, 1.09676, 0, 0, 0, 0, -0.87128, -2.90527, -0.81171, -2.92264, 3.0145, -0.33698, 2.93594, 0.75983]}]}, "g2_body": {"g2_body": [{"time": 0, "offset": 64, "vertices": [-11.8227, 0.89554, -11.8228, 0.89556, -11.5464, -2.693, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.31118, 5.87552, -4.31122, 5.87551, -5.87528, 4.3113, 7.66356, -1.36959, -6.85483, 3.69005, -7.64591, 1.46333, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.39933, 1.49977, -9.39849, 1.50465, -9.39851, 1.50464, -9.39855, 1.50468, -9.41659, -1.38459, -11.45246, -4.68149, -11.45263, -4.68138, -9.51911, -7.90188], "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333}, {"time": 0.8333, "offset": 64, "vertices": [-11.8227, 0.89554, -11.8228, 0.89556, -11.5464, -2.693, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.31118, 5.87552, -4.31122, 5.87551, -5.87528, 4.3113, 7.66356, -1.36959, -6.85483, 3.69005, -7.64591, 1.46333, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.39933, 1.49977, -9.39849, 1.50465, -9.39851, 1.50464, -9.39855, 1.50468, -9.41659, -1.38459, -11.45246, -4.68149, -11.45263, -4.68138, -9.51911, -7.90188]}]}, "g2_head": {"g2_head": [{"time": 0, "offset": 66, "vertices": [0.52243, 1.13217, 1.2345, -0.17572, -1.6544, -2.39465, -2.76852, 0.89807], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "vertices": [5.0058, -3.0473, 9.01405, 2.95334, 5.0058, -3.0473, -1.53819, -5.655, 4.14188, -3.54247, 5.43781, -0.35785, -9.28081, -10.73526, -6.77014, 12.47246, -12.89095, 5.93555, 20.36594, 0.1637, -7.0347, -19.11255, 5.0058, -3.0473, 9.76501, -0.72861, -3.59793, -8.80267, 2.23381, -9.36774, 11.51935, -5.31198, -8.02734, -8.81787, 0.2323, -4.73209, -5.02368, 1.43762, -4.61795, -3.60812, -1.53819, -5.655, 5.0058, -3.0473, -4.61795, -3.60812, 5.0058, -3.0473, -4.61795, -3.60812, 7.34061, -4.77957, 8.9617, -7.0423, 5.0058, -3.0473, -1.53819, -5.655, 5.0058, -3.0473, -4.61795, -3.60812, -1.53819, -5.655, 8.1806, -6.5343, 1.54761, -5.21356, -4.78995, -3.06696, -0.84158, -13.6221, -13.3392, -3.32742], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "offset": 66, "vertices": [0.52243, 1.13217, 1.2345, -0.17572, -1.6544, -2.39465, -2.76852, 0.89807]}]}, "g3_face": {"g3_face": [{"time": 0, "offset": 36, "vertices": [-1.52014, 1.28469, 4.75646, -0.28602, 4.72931, 0.58792, 0, 0, 0, 0, 1.51126, 0.08985, 0, 0, -0.58328, 0.16186, -0.60265, 0.05274, 2.10153, -1.44525, 1.819, -1.06256, 1.98283, -0.71249, 1.819, -1.06256, 1.98283, -0.71249, 0, 0, 0, 0, 3.74042, -1.38725, 3.93174, -0.68073, 7.34243, -0.4412, 7.30118, 0.90746, 3.60201, 0.94606, 3.36944, 1.58819, 0, 0, 0, 0, 1.819, -1.06258, 1.98281, -0.71244, 1.819, -1.06258, 1.98281, -0.71244], "curve": [0.329, 0.32, 0.758, 1]}, {"time": 0.3333}, {"time": 0.8333, "offset": 36, "vertices": [-1.52014, 1.28469, 4.75646, -0.28602, 4.72931, 0.58792, 0, 0, 0, 0, 1.51126, 0.08985, 0, 0, -0.58328, 0.16186, -0.60265, 0.05274, 2.10153, -1.44525, 1.819, -1.06256, 1.98283, -0.71249, 1.819, -1.06256, 1.98283, -0.71249, 0, 0, 0, 0, 3.74042, -1.38725, 3.93174, -0.68073, 7.34243, -0.4412, 7.30118, 0.90746, 3.60201, 0.94606, 3.36944, 1.58819, 0, 0, 0, 0, 1.819, -1.06258, 1.98281, -0.71244, 1.819, -1.06258, 1.98281, -0.71244]}]}}}}}}