{"skeleton": {"hash": "LEmwfTUklFiOdL/mJpPGG0uZG64", "spine": "3.7.94", "width": 1109.87, "height": 661.88, "images": "./images/", "audio": "D:/Chec D/Anime Draw/KTEK/Project KTEK/MaDao/xx4"}, "bones": [{"name": "root", "rotation": 0.02}, {"name": "Character All", "parent": "root"}, {"name": "Nam All", "parent": "Character All", "x": -19.87, "y": -38.54, "color": "0000ffff"}, {"name": "Nam Chest", "parent": "Nam All", "y": 85.5, "color": "0000ffff"}, {"name": "<PERSON>", "parent": "Nam All", "length": 50, "rotation": 90, "y": 200, "color": "0000ffff"}, {"name": "Nam Hair 1", "parent": "<PERSON>", "length": 11.25, "rotation": 172.23, "x": 49.61, "y": 44.48, "color": "0000ffff"}, {"name": "Nam Hair 2", "parent": "Nam Hair 1", "length": 11.25, "x": 11.25, "color": "0000ffff"}, {"name": "Nam Hair 3", "parent": "Nam Hair 2", "length": 11.25, "x": 11.25, "color": "0000ffff"}, {"name": "Nam Hair 4", "parent": "Nam Hair 3", "length": 11.25, "x": 11.25, "color": "0000ffff"}, {"name": "<PERSON> Left", "parent": "Nam All", "length": 75, "rotation": -45, "x": 60.28, "y": 150.71, "color": "0000ffff"}, {"name": "<PERSON>", "parent": "<PERSON> Left", "length": 40, "rotation": -120, "x": 128.3, "y": 22.43, "color": "0000ffff"}, {"name": "<PERSON>", "parent": "<PERSON>", "rotation": 164.96, "x": 52.84, "y": -11.76, "color": "0000ffff"}, {"name": "<PERSON> Finger A Left", "parent": "<PERSON>", "length": 25, "rotation": -155, "x": -30.93, "y": -1.38, "color": "0000ffff"}, {"name": "Nam Finger B 1 Left", "parent": "<PERSON>", "length": 20, "rotation": -124.92, "color": "0000ffff"}, {"name": "Nam Finger B 2 Left", "parent": "Nam Finger B 1 Left", "length": 20, "x": 20, "color": "0000ffff"}, {"name": "Nam Finger B 3 Left", "parent": "Nam Finger B 2 Left", "length": 20, "x": 20, "color": "0000ffff"}, {"name": "Dam Nguyet Anh All", "parent": "Character All", "x": -200.8, "y": -193.67, "color": "63ff00ff"}, {"name": "<PERSON>", "parent": "Dam Nguyet Anh All", "length": 111.91, "rotation": 59.92, "color": "63ff00ff"}, {"name": "<PERSON>", "parent": "Dam Nguyet Anh All", "length": 145.96, "rotation": -90, "color": "63ff00ff"}, {"name": "<PERSON> Breast Verticle", "parent": "<PERSON>", "x": 34.9, "y": -117.5, "color": "63ff00ff"}, {"name": "<PERSON> Anh Breast Horizontal", "parent": "<PERSON> Breast Verticle", "color": "63ff00ff"}, {"name": "<PERSON>", "parent": "<PERSON>", "length": 60, "rotation": 9.59, "x": 150, "y": -15, "color": "63ff00ff"}, {"name": "<PERSON>", "parent": "<PERSON>", "x": 109.58, "y": -14.27, "color": "63ff00ff"}, {"name": "<PERSON> Hair Rotation", "parent": "<PERSON>", "rotation": -144.9, "x": 58.83, "y": -39.53, "color": "63ff00ff"}, {"name": "Dam Nguyet Anh Hair 1", "parent": "<PERSON> Hair Rotation", "length": 34.84, "color": "63ff00ff"}, {"name": "<PERSON> Ng<PERSON> Anh Hair 2", "parent": "Dam Nguyet Anh Hair 1", "length": 34.84, "x": 34.84, "color": "63ff00ff"}, {"name": "Dam Nguyet Anh Hair 3", "parent": "<PERSON> Ng<PERSON> Anh Hair 2", "length": 34.84, "x": 34.84, "color": "63ff00ff"}, {"name": "<PERSON> Ng<PERSON> Anh Hair 4", "parent": "Dam Nguyet Anh Hair 3", "length": 34.84, "x": 34.84, "color": "63ff00ff"}, {"name": "Dam Nguyet Anh Arm", "parent": "<PERSON>", "length": 100, "rotation": -117.5, "x": 124.01, "y": -65.88, "color": "63ff00ff"}, {"name": "<PERSON>", "parent": "Dam Nguyet Anh Arm", "length": 65, "rotation": 145.5, "x": 106.2, "y": -0.08, "color": "63ff00ff"}, {"name": "<PERSON> Anh Hand", "parent": "<PERSON>", "length": 25.88, "rotation": -5.05, "x": 65, "color": "63ff00ff"}, {"name": "Dam Ng<PERSON> Anh Finger A", "parent": "<PERSON> Anh Hand", "length": 16.7, "rotation": 47.45, "x": 30.56, "y": 21.05, "color": "63ff00ff"}, {"name": "Dam Nguyet Anh Finger B 1", "parent": "<PERSON> Anh Hand", "length": 21.86, "rotation": 5, "x": 36.73, "y": 0.35, "color": "63ff00ff"}, {"name": "Dam Nguyet Anh Finger B 2", "parent": "Dam Nguyet Anh Finger B 1", "length": 16.4, "rotation": 35, "x": 21.86, "color": "63ff00ff"}, {"name": "<PERSON>", "parent": "Nam All", "length": 50, "rotation": 162.62, "y": -75, "color": "0000ffff"}, {"name": "Nam Penis IK", "parent": "<PERSON>", "rotation": 13.33, "x": 16.8, "y": -29.54, "color": "ff3f00ff"}, {"name": "<PERSON><PERSON><PERSON>", "parent": "Character All", "x": 135, "y": -80, "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON>", "parent": "<PERSON><PERSON><PERSON>", "length": 75, "rotation": 130, "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON> Thigh Left", "parent": "<PERSON><PERSON><PERSON>", "length": 150, "rotation": -110, "x": 29.47, "y": -21.37, "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON>", "parent": "<PERSON><PERSON><PERSON> Thigh Left", "length": 50, "rotation": 110, "x": 184.89, "y": -0.11, "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON> Breast Left Verticle", "parent": "<PERSON><PERSON><PERSON>", "x": 91.38, "y": 53.36, "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON> Breast Left Horizontal", "parent": "<PERSON><PERSON><PERSON> Breast Left Verticle", "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON> Breast Right Vertical", "parent": "<PERSON><PERSON><PERSON>", "x": 86.25, "y": 75, "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON> Breast Right Horizontal", "parent": "<PERSON><PERSON><PERSON> Breast Right Vertical", "rotation": -0.04, "color": "ffff00ff"}, {"name": "<PERSON>", "parent": "Nam All", "length": 35, "rotation": -90, "x": -10.13, "y": -83.96, "color": "0000ffff"}, {"name": "<PERSON><PERSON><PERSON>", "parent": "<PERSON><PERSON><PERSON>", "length": 75, "rotation": 80, "x": 105.6, "y": -10.28, "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON>", "parent": "<PERSON><PERSON><PERSON>", "length": 87.15, "rotation": -80, "x": 86.2, "y": -0.69, "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON>", "parent": "<PERSON><PERSON><PERSON>", "length": 20, "x": 87.15, "color": "ffff00ff"}, {"name": "Nguu Le <PERSON>a Finger B 1", "parent": "<PERSON><PERSON><PERSON>", "length": 15.02, "rotation": -2.14, "x": 24.31, "y": 5.69, "color": "ffff00ff"}, {"name": "<PERSON><PERSON>u <PERSON>a Finger B 2", "parent": "Nguu Le <PERSON>a Finger B 1", "length": 16.37, "x": 15.14, "color": "ffff00ff"}, {"name": "<PERSON><PERSON>u <PERSON>a Finger B 3", "parent": "<PERSON><PERSON>u <PERSON>a Finger B 2", "length": 18.26, "rotation": -27.63, "x": 16.25, "color": "ffff00ff"}, {"name": "Nguu Le <PERSON>a Finger A 1", "parent": "<PERSON><PERSON><PERSON>", "length": 11.77, "rotation": -26.12, "x": 23.39, "y": -12.96, "color": "ffff00ff"}, {"name": "<PERSON><PERSON>u Le <PERSON>a Finger A 2", "parent": "Nguu Le <PERSON>a Finger A 1", "length": 13.76, "x": 11.87, "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON>", "parent": "<PERSON><PERSON><PERSON>", "length": 45, "rotation": -40, "x": 159.69, "y": -16.89, "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON> Hair A 1", "parent": "<PERSON><PERSON><PERSON>", "length": 22, "rotation": -171.48, "x": 25.63, "y": -12.59, "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON> A 2", "parent": "<PERSON><PERSON><PERSON> Hair A 1", "length": 22, "x": 22, "color": "ffff00ff"}, {"name": "<PERSON><PERSON>u <PERSON> Hair B 1", "parent": "<PERSON><PERSON><PERSON>", "length": 24, "rotation": -176.48, "x": 31.39, "y": 50.88, "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON> hair B 2", "parent": "<PERSON><PERSON>u <PERSON> Hair B 1", "length": 24, "x": 23.09, "y": 13.35, "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON> Hair Back Body Rotation", "parent": "<PERSON><PERSON><PERSON>", "rotation": -170, "x": 20.11, "y": 19.92, "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON> Hair Back Head Rotation", "parent": "<PERSON><PERSON><PERSON> Hair Back Body Rotation", "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON> Hair Back 1", "parent": "<PERSON><PERSON><PERSON> Hair Back Head Rotation", "length": 43, "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON> Back 2", "parent": "<PERSON><PERSON><PERSON> Hair Back 1", "length": 43, "x": 43, "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON> Hair Back 3", "parent": "<PERSON><PERSON><PERSON> Back 2", "length": 43, "x": 43, "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON> Back 4", "parent": "<PERSON><PERSON><PERSON> Hair Back 3", "length": 43, "x": 43, "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON> Back 5", "parent": "<PERSON><PERSON><PERSON> Back 4", "length": 43, "x": 43, "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON> 6", "parent": "<PERSON><PERSON><PERSON> Back 5", "length": 43, "x": 43, "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON> Thigh Right", "parent": "<PERSON><PERSON><PERSON>", "length": 125, "rotation": -105, "x": 77.69, "y": -23.35, "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON>", "parent": "<PERSON><PERSON><PERSON> Thigh Right", "length": 139.84, "rotation": 104.99, "x": 157.06, "y": -0.01, "color": "ffff00ff"}, {"name": "<PERSON><PERSON><PERSON> Thigh Left IK", "parent": "Character All", "x": 104.48, "y": -276.37, "color": "ff3f00ff"}, {"name": "<PERSON><PERSON><PERSON> Thigh Right IK", "parent": "Character All", "x": 173.96, "y": -255.69, "color": "ff3f00ff"}, {"name": "<PERSON><PERSON><PERSON> Leg Right IK", "parent": "Character All", "x": 313.19, "y": -253.7, "color": "ff3f00ff"}, {"name": "<PERSON><PERSON><PERSON> Left IK", "parent": "Character All", "x": 160.11, "y": -275.04, "color": "ff3f00ff"}, {"name": "<PERSON> Anh Hip IK", "parent": "Character All", "x": -196.54, "y": -352.24, "color": "ff3f00ff"}, {"name": "Ton <PERSON>", "parent": "Character All", "x": -150, "y": -12, "color": "ff00c8ff"}, {"name": "<PERSON><PERSON>", "parent": "Ton <PERSON>", "length": 25, "rotation": -88.78, "color": "ff00c8ff"}, {"name": "<PERSON>n <PERSON>", "parent": "Ton <PERSON>", "length": 125, "rotation": 89.93, "color": "ff00c8ff"}, {"name": "Ton Tam Nuong Arm Right", "parent": "<PERSON>n <PERSON>", "length": 84.08, "rotation": 150, "x": 113.7, "y": 46.94, "color": "ff00c8ff"}, {"name": "<PERSON><PERSON>", "parent": "Ton Tam Nuong Arm Right", "length": 78, "rotation": 141.53, "x": 97.14, "y": 2.05, "color": "ff00c8ff"}, {"name": "<PERSON><PERSON>", "parent": "<PERSON><PERSON>", "length": 16.76, "x": 78, "color": "ff00c8ff"}, {"name": "Ton Tam Nuong Finger A 1", "parent": "<PERSON><PERSON>", "length": 20, "x": 21.35, "y": 9.14, "color": "ff00c8ff"}, {"name": "Ton Tam Nuong Finger B 1", "parent": "<PERSON><PERSON>", "length": 16.09, "x": 22.06, "y": -9.56, "color": "ff00c8ff"}, {"name": "Ton Tam Nuong Finger A 2", "parent": "Ton Tam Nuong Finger A 1", "length": 20, "x": 20, "color": "ff00c8ff"}, {"name": "Ton Tam Nuong Finger B 2", "parent": "Ton Tam Nuong Finger B 1", "length": 16.09, "x": 16.09, "color": "ff00c8ff"}, {"name": "Ton Tam Nuong Finger B 3", "parent": "Ton Tam Nuong Finger B 2", "length": 16.09, "x": 16.09, "color": "ff00c8ff"}, {"name": "Ton <PERSON>uong Breast Right", "parent": "<PERSON>n <PERSON>", "x": 67.01, "y": -12.49, "color": "ff00c8ff"}, {"name": "<PERSON><PERSON> Breast Left Verticle", "parent": "<PERSON>n <PERSON>", "x": 86.52, "y": -93.02, "color": "ff00c8ff"}, {"name": "<PERSON><PERSON> Breast Left Horizontal", "parent": "<PERSON><PERSON> Breast Left Verticle", "color": "ff00c8ff"}, {"name": "<PERSON><PERSON><PERSON> Forearm IK", "parent": "<PERSON><PERSON> Breast Left Horizontal", "rotation": -90, "x": -7.58, "y": 27.19, "color": "ff3f00ff"}, {"name": "<PERSON><PERSON>", "parent": "<PERSON>n <PERSON>", "length": 19.13, "x": 162, "y": -3.99, "color": "ff00c8ff"}, {"name": "Ton Tam <PERSON>uong Arm Left", "parent": "<PERSON>n <PERSON>", "length": 85.91, "rotation": -135, "x": 136.35, "y": -45.49, "color": "ff00c8ff"}, {"name": "<PERSON><PERSON> Hair Front A Head Rotation", "parent": "<PERSON><PERSON>", "rotation": 170, "x": 3.63, "y": 25.77, "color": "ff00c8ff"}, {"name": "<PERSON><PERSON> Hair Front A Body Rotation", "parent": "<PERSON><PERSON> Hair Front A Head Rotation", "color": "ff00c8ff"}, {"name": "Ton Tam Nuong Hair Front A 1", "parent": "<PERSON><PERSON> Hair Front A Body Rotation", "length": 36, "color": "ff00c8ff"}, {"name": "Ton Tam Nuong Hair Front A 2", "parent": "Ton Tam Nuong Hair Front A 1", "length": 36, "x": 36, "color": "ff00c8ff"}, {"name": "Ton Tam Nuong Hair Front A 3", "parent": "Ton Tam Nuong Hair Front A 2", "length": 36, "x": 36, "color": "ff00c8ff"}, {"name": "Ton Tam Nuong Hair Front A 4", "parent": "Ton Tam Nuong Hair Front A 3", "length": 36, "x": 36, "color": "ff00c8ff"}, {"name": "Ton Tam Nuong Hair Front A 5", "parent": "Ton Tam Nuong Hair Front A 4", "length": 36, "x": 36, "color": "ff00c8ff"}, {"name": "<PERSON><PERSON> Hair Front B Head Rotation", "parent": "<PERSON><PERSON>", "rotation": 180, "x": -2.66, "y": -26.88, "color": "ff00c8ff"}, {"name": "<PERSON><PERSON> Hair Front B Body Rotation", "parent": "<PERSON><PERSON> Hair Front B Head Rotation", "color": "ff00c8ff"}, {"name": "Ton Tam Nuong Hair Front B 1", "parent": "<PERSON><PERSON> Hair Front B Body Rotation", "length": 17.67, "color": "ff00c8ff"}, {"name": "Ton Tam Nuong Hair Front B 2", "parent": "Ton Tam Nuong Hair Front B 1", "length": 17.67, "x": 17.67, "color": "ff00c8ff"}, {"name": "Ton Tam Nuong Hair Front B 3", "parent": "Ton Tam Nuong Hair Front B 2", "length": 17.67, "x": 17.67, "color": "ff00c8ff"}, {"name": "<PERSON><PERSON> Behind Head Rotation", "parent": "<PERSON><PERSON>", "rotation": 165, "x": 28.83, "y": 28.67, "color": "ff00c8ff"}, {"name": "<PERSON><PERSON> Behind Body Rotation", "parent": "<PERSON><PERSON> Behind Head Rotation", "color": "ff00c8ff"}, {"name": "<PERSON>n <PERSON>ng Hair Behind 1", "parent": "<PERSON><PERSON> Behind Body Rotation", "length": 50, "color": "ff00c8ff"}, {"name": "<PERSON><PERSON> Behind 2", "parent": "<PERSON>n <PERSON>ng Hair Behind 1", "length": 68, "x": 51.92, "color": "ff00c8ff"}, {"name": "<PERSON><PERSON> Hair Behind 3", "parent": "<PERSON><PERSON> Behind 2", "length": 68, "x": 68, "color": "ff00c8ff"}, {"name": "<PERSON><PERSON> Behind 4", "parent": "<PERSON><PERSON> Hair Behind 3", "length": 68, "x": 68, "color": "ff00c8ff"}, {"name": "<PERSON>", "parent": "<PERSON>n <PERSON>", "length": 50, "rotation": 134.82, "x": 35.85, "y": 24.36, "color": "0000ffff"}, {"name": "Nam Finger A 1 Right", "parent": "<PERSON>", "length": 17, "rotation": 26.68, "x": 71.95, "y": 11.47, "color": "0000ffff"}, {"name": "Nam Finger B 1 Right", "parent": "<PERSON>", "length": 16.51, "rotation": 23.08, "x": 79.82, "y": -5.84, "color": "0000ffff"}, {"name": "Nam Finger B 2 Right", "parent": "Nam Finger B 1 Right", "length": 16.51, "x": 16.51, "color": "0000ffff"}, {"name": "Nam Finger B 3 Right", "parent": "Nam Finger B 2 Right", "length": 23.27, "rotation": 48.33, "x": 16.51, "color": "0000ffff"}, {"name": "Nam Finger A 2 Right", "parent": "Nam Finger A 1 Right", "length": 17, "rotation": 0.1, "x": 17, "color": "0000ffff"}, {"name": "<PERSON><PERSON>", "parent": "Character All", "x": 34.66, "y": 118.95, "color": "ff0000ff"}, {"name": "<PERSON><PERSON>", "parent": "<PERSON><PERSON>", "x": -7.01, "color": "ff0000ff"}, {"name": "<PERSON><PERSON>", "parent": "<PERSON><PERSON>", "length": 24.03, "rotation": 82.15, "y": 12, "color": "ff0000ff"}, {"name": "Diem Nuu Arm Right", "parent": "<PERSON><PERSON>", "length": 40.76, "rotation": -179.33, "x": -66.95, "y": 14.56, "color": "ff0000ff"}, {"name": "<PERSON><PERSON>uu <PERSON> Right", "parent": "Diem Nuu Arm Right", "length": 70, "rotation": 125, "x": 40.96, "y": 4.14, "color": "ff0000ff"}, {"name": "<PERSON><PERSON>u Hand Right", "parent": "<PERSON><PERSON>uu <PERSON> Right", "length": 22, "x": 69.57, "y": -0.26, "color": "ff0000ff"}, {"name": "Diem Nuu Finger Right 1", "parent": "<PERSON><PERSON>u Hand Right", "length": 15, "x": 21.96, "color": "ff0000ff"}, {"name": "Diem Nuu Finger Right 2", "parent": "Diem Nuu Finger Right 1", "length": 15, "x": 15, "color": "ff0000ff"}, {"name": "Diem Nuu Finger Right 3", "parent": "Diem Nuu Finger Right 2", "length": 15, "x": 15, "color": "ff0000ff"}, {"name": "<PERSON>m Nuu Arm Left", "parent": "<PERSON><PERSON>", "length": 87.65, "rotation": -31, "x": 33.72, "y": 8.54, "color": "ff0000ff"}, {"name": "<PERSON><PERSON>", "parent": "<PERSON>m Nuu Arm Left", "length": 107.96, "rotation": -127, "x": 87.65, "y": 0.22, "color": "ff0000ff"}, {"name": "<PERSON> Left IK", "parent": "<PERSON><PERSON><PERSON>", "rotation": -130, "x": 98.85, "y": -48.18, "color": "ff3f00ff"}, {"name": "BG All", "parent": "root"}, {"name": "Sparkle All", "parent": "root"}, {"name": "Sparkle 4", "parent": "Sparkle All", "x": 429.6, "y": -0.44, "scaleX": 0.5, "scaleY": 0.5, "color": "ff0000ff"}, {"name": "Sparkle 5", "parent": "<PERSON><PERSON>", "rotation": -89.94, "x": -16, "y": -20.45, "scaleX": 0.3, "scaleY": 0.3, "color": "00ff00ff"}, {"name": "Sparkle 9", "parent": "Sparkle All", "x": 464.72, "y": -13.02, "scaleX": 0.35, "scaleY": 0.35, "color": "00ff00ff"}, {"name": "Sparkle 12", "parent": "Sparkle All", "x": -477.95, "y": -89.7, "scaleX": 0.5, "scaleY": 0.5, "color": "ff0000ff"}, {"name": "Sparkle 13", "parent": "Sparkle All", "x": -250.79, "y": -158.44, "scaleX": 0.35, "scaleY": 0.35, "color": "00ff00ff"}, {"name": "Sparkle 14", "parent": "Sparkle All", "x": -387.9, "y": -43.52, "scaleX": 0.65, "scaleY": 0.65, "color": "0000ffff"}, {"name": "Sparkle 19", "parent": "<PERSON><PERSON>", "rotation": -89.94, "x": -12.64, "y": 26.92, "scaleX": 0.3, "scaleY": 0.3, "color": "ff00ffff"}, {"name": "Sparkle 20", "parent": "Sparkle All", "x": 475.28, "y": -33.16, "scaleX": 0.5, "scaleY": 0.5, "color": "ff0000ff"}, {"name": "Sparkle 22", "parent": "Sparkle All", "x": -448.15, "y": -33.88, "scaleX": 0.35, "scaleY": 0.35, "color": "00ff00ff"}, {"name": "Sparkle 25", "parent": "Sparkle All", "x": 433.89, "y": -33.99, "scaleX": 0.35, "scaleY": 0.35, "color": "00ff00ff"}, {"name": "Sparkle 30", "parent": "Sparkle All", "x": -428.56, "y": -10.05, "scaleX": 0.65, "scaleY": 0.65, "color": "0000ffff"}, {"name": "Sparkle 39", "parent": "<PERSON><PERSON><PERSON>", "rotation": -90, "x": 38.73, "y": 30.79, "scaleX": 0.4, "scaleY": 0.4, "color": "ff00ffff"}, {"name": "Sparkle 40", "parent": "<PERSON><PERSON>", "rotation": -89.94, "x": 31.56, "y": 29.09, "scaleX": 0.5, "scaleY": 0.5, "color": "ff0000ff"}, {"name": "<PERSON> Forearm IK", "parent": "<PERSON>", "rotation": -141.23, "x": -2.6, "y": 4.25, "color": "ff3f00ff"}, {"name": "Sparkle 31", "parent": "Sparkle All", "x": 430, "y": -385.42, "scaleX": 0.65, "scaleY": 0.65, "color": "0000ffff"}, {"name": "Sparkle 29", "parent": "Sparkle All", "x": -515.76, "y": -283.51, "scaleX": 0.65, "scaleY": 0.65, "color": "ff0000ff"}, {"name": "<PERSON>", "parent": "<PERSON>", "length": 33.67, "rotation": -176.8, "x": -4.54, "y": -7.27, "color": "0000ffff"}, {"name": "<PERSON>", "parent": "<PERSON>", "length": 32.11, "rotation": -172.66, "x": -7, "y": 40.59, "color": "0000ffff"}, {"name": "asdasdadads", "parent": "root", "x": 37.83, "y": 43.77, "color": "ff3f00ff"}, {"name": "Fire 1", "parent": "BG All", "x": -535.46, "y": -145.54}, {"name": "Fire 2", "parent": "BG All", "x": -599.61, "y": -74.66, "scaleX": 0.9, "scaleY": 0.9}], "slots": [{"name": "Ton Tam <PERSON>uong Arm Left", "bone": "Ton Tam <PERSON>uong Arm Left", "attachment": "Ton Tam <PERSON>uong Arm Left"}, {"name": "<PERSON>m Nuu Arm Left Below", "bone": "<PERSON>m Nuu Arm Left", "attachment": "<PERSON>m Nuu Arm Left"}, {"name": "Diem Nuu Arm Right Below", "bone": "Diem Nuu Arm Right", "attachment": "Diem Nuu Arm Right"}, {"name": "<PERSON><PERSON>", "bone": "<PERSON><PERSON>", "attachment": "<PERSON><PERSON>"}, {"name": "<PERSON>", "bone": "Nam All", "attachment": "<PERSON>"}, {"name": "<PERSON> Right", "bone": "Nam All", "attachment": "<PERSON> Right"}, {"name": "<PERSON> Left", "bone": "<PERSON> Left", "attachment": "<PERSON> Left"}, {"name": "Nam Body", "bone": "Nam All", "attachment": "Nam Body"}, {"name": "Diem Nuu Arm Right Above", "bone": "Diem Nuu Arm Right", "attachment": "Diem Nuu Arm Right"}, {"name": "<PERSON>", "bone": "<PERSON>", "attachment": "<PERSON>"}, {"name": "<PERSON>", "bone": "<PERSON>", "attachment": "<PERSON>"}, {"name": "Nam Penis Fu<PERSON>", "bone": "Nam All", "attachment": "Nam Penis Fu<PERSON>"}, {"name": "<PERSON>", "bone": "<PERSON>", "attachment": "<PERSON>"}, {"name": "<PERSON>", "bone": "<PERSON>", "attachment": "<PERSON>"}, {"name": "<PERSON><PERSON> Behind", "bone": "<PERSON><PERSON>", "attachment": "<PERSON><PERSON> Behind"}, {"name": "<PERSON> Right Below", "bone": "<PERSON>", "attachment": "<PERSON>"}, {"name": "Ton tam <PERSON>", "bone": "Ton <PERSON>", "attachment": "Ton tam <PERSON>"}, {"name": "<PERSON>n <PERSON>", "bone": "Ton <PERSON>", "attachment": "<PERSON>n <PERSON>"}, {"name": "<PERSON>m Right Above", "bone": "<PERSON>", "attachment": "<PERSON>"}, {"name": "<PERSON><PERSON>", "bone": "<PERSON><PERSON>", "attachment": "<PERSON><PERSON>"}, {"name": "<PERSON>n <PERSON>ng Hair Front B", "bone": "Ton Tam Nuong Hair Front B 3", "attachment": "<PERSON>n <PERSON>ng Hair Front B"}, {"name": "<PERSON>n <PERSON>uong Hair Front A", "bone": "Ton Tam Nuong Hair Front A 5", "attachment": "<PERSON>n <PERSON>uong Hair Front A"}, {"name": "<PERSON><PERSON>", "bone": "<PERSON><PERSON>", "attachment": "<PERSON><PERSON>"}, {"name": "Girl Mask", "bone": "BG All", "attachment": "Girl Mask"}, {"name": "<PERSON><PERSON><PERSON> Thigh Right", "bone": "<PERSON><PERSON><PERSON> Thigh Right", "attachment": "<PERSON><PERSON><PERSON> Thigh Right"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON> 6", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "bone": "<PERSON>", "attachment": "<PERSON>"}, {"name": "Diem Nuu Arm Left Above", "bone": "<PERSON>m Nuu Arm Left", "attachment": "<PERSON>m Nuu Arm Left"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON> Jewelry", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON> Jewelry"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "bone": "<PERSON>", "attachment": "<PERSON>"}, {"name": "<PERSON>", "bone": "<PERSON>", "attachment": "<PERSON>"}, {"name": "<PERSON>", "bone": "<PERSON>", "attachment": "<PERSON>"}, {"name": "<PERSON>", "bone": "<PERSON>", "attachment": "<PERSON>"}, {"name": "Sparkle 4", "bone": "Sparkle 4", "attachment": "Sparkle"}, {"name": "Sparkle 30", "bone": "Sparkle 30", "attachment": "Sparkle"}, {"name": "Sparkle 31", "bone": "Sparkle 31", "attachment": "Sparkle"}, {"name": "Sparkle 22", "bone": "Sparkle 22", "attachment": "Sparkle"}, {"name": "Sparkle 5", "bone": "Sparkle 5", "attachment": "Sparkle"}, {"name": "Sparkle 25", "bone": "Sparkle 25", "attachment": "Sparkle"}, {"name": "Sparkle 20", "bone": "Sparkle 20", "attachment": "Sparkle"}, {"name": "Sparkle 29", "bone": "Sparkle 29", "attachment": "Sparkle"}, {"name": "Sparkle 40", "bone": "Sparkle 40", "attachment": "Sparkle"}, {"name": "Sparkle 14", "bone": "Sparkle 14", "attachment": "Sparkle"}, {"name": "Sparkle 9", "bone": "Sparkle 9", "attachment": "Sparkle"}, {"name": "Sparkle 19", "bone": "Sparkle 19", "attachment": "Sparkle"}, {"name": "Sparkle 39", "bone": "Sparkle 39", "attachment": "Sparkle"}, {"name": "Sparkle 12", "bone": "Sparkle 12", "attachment": "Sparkle"}, {"name": "Sparkle 13", "bone": "Sparkle 13", "attachment": "Sparkle"}, {"name": "Fire", "bone": "Fire 1", "attachment": "Fire"}, {"name": "Fire2", "bone": "Fire 2", "attachment": "Fire"}], "ik": [{"name": "<PERSON> Forearm IK", "order": 4, "bones": ["<PERSON>", "<PERSON> Anh Hand"], "target": "<PERSON> Forearm IK", "mix": 0.5}, {"name": "<PERSON> Anh Hip IK", "order": 9, "bones": ["<PERSON>"], "target": "<PERSON> Anh Hip IK"}, {"name": "<PERSON> Left IK", "order": 5, "bones": ["<PERSON>"], "target": "<PERSON> Left IK"}, {"name": "Nam Penis IK", "order": 0, "bones": ["<PERSON>"], "target": "Nam Penis IK"}, {"name": "<PERSON><PERSON><PERSON> Forearm IK", "order": 6, "bones": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "target": "<PERSON><PERSON><PERSON> Forearm IK"}, {"name": "<PERSON><PERSON><PERSON> Left IK", "order": 8, "bones": ["<PERSON><PERSON><PERSON>"], "target": "<PERSON><PERSON><PERSON> Left IK"}, {"name": "<PERSON><PERSON><PERSON> Leg Right IK", "order": 2, "bones": ["<PERSON><PERSON><PERSON>"], "target": "<PERSON><PERSON><PERSON> Leg Right IK"}, {"name": "<PERSON><PERSON><PERSON> Thigh Left IK", "order": 7, "bones": ["<PERSON><PERSON><PERSON> Thigh Left"], "target": "<PERSON><PERSON><PERSON> Thigh Left IK"}, {"name": "<PERSON><PERSON><PERSON> Thigh Right IK", "order": 1, "bones": ["<PERSON><PERSON><PERSON> Thigh Right"], "target": "<PERSON><PERSON><PERSON> Thigh Right IK"}, {"name": "asdasdadads", "order": 3, "bones": ["<PERSON><PERSON>"], "target": "asdasdadads"}], "transform": [{"name": "<PERSON> Hair Rotation", "order": 10, "bones": ["<PERSON> Hair Rotation"], "target": "<PERSON>", "rotation": 155.21, "local": true, "x": 58.83, "y": -39.53, "rotateMix": -1, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "<PERSON>", "order": 17, "bones": ["<PERSON>"], "target": "<PERSON>n <PERSON>", "rotation": 44.82, "local": true, "x": 35.85, "y": 24.36, "rotateMix": 0.15, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "<PERSON><PERSON><PERSON> Hair Back Head Rotation", "order": 12, "bones": ["<PERSON><PERSON><PERSON> Hair Back Head Rotation"], "target": "<PERSON><PERSON><PERSON>", "rotation": -140, "local": true, "x": -139.58, "y": 36.81, "rotateMix": -1, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "<PERSON><PERSON><PERSON> Hair back Body Rotation", "order": 11, "bones": ["<PERSON><PERSON><PERSON> Hair Back Body Rotation"], "target": "<PERSON><PERSON><PERSON>", "rotation": -124.5, "local": true, "x": 20.11, "y": 19.92, "rotateMix": -1, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "<PERSON><PERSON> Behind Body Rotation", "order": 14, "bones": ["<PERSON><PERSON> Behind Body Rotation"], "target": "<PERSON>n <PERSON>", "rotation": -90, "local": true, "rotateMix": -1, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "<PERSON><PERSON> Behind Head Rotation", "order": 13, "bones": ["<PERSON><PERSON> Behind Head Rotation"], "target": "<PERSON><PERSON>", "rotation": 165, "local": true, "x": 190.37, "y": -125.49, "rotateMix": -1, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "<PERSON><PERSON> Hair Front A Body Rotation", "order": 19, "bones": ["<PERSON><PERSON> Hair Front A Body Rotation"], "target": "<PERSON>n <PERSON>", "rotation": -90, "local": true, "rotateMix": -1, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "<PERSON><PERSON> Hair Front A Head Rotation", "order": 18, "bones": ["<PERSON><PERSON> Hair Front A Head Rotation"], "target": "<PERSON><PERSON>", "rotation": 170, "local": true, "x": -158.37, "y": 29.76, "rotateMix": -1, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "<PERSON><PERSON> Hair Front B Body Rotation", "order": 16, "bones": ["<PERSON><PERSON> Hair Front B Body Rotation"], "target": "<PERSON>n <PERSON>", "rotation": -90, "local": true, "rotateMix": -1, "translateMix": 0, "scaleMix": 0, "shearMix": 0}, {"name": "<PERSON><PERSON> Hair Front B Head Rotation", "order": 15, "bones": ["<PERSON><PERSON> Hair Front B Head Rotation"], "target": "<PERSON><PERSON>", "rotation": -179.92, "local": true, "x": -164.66, "y": -22.89, "rotateMix": -1, "translateMix": 0, "scaleMix": 0, "shearMix": 0}], "skins": {"default": {"Dam Nguyet Anh Body": {"Dam Nguyet Anh Body": {"type": "mesh", "uvs": [0.23828, 0, 0, 0.17977, 0, 0.36783, 0.2014, 0.47927, 0.20031, 0.51951, 0.15583, 0.56362, 0.05169, 0.67358, 0.07122, 0.83223, 0.25347, 0.93206, 0.5908, 0.99999, 1, 0.69933, 0.80925, 0.54918, 0.69209, 0.50042, 0.62157, 0.43077, 0.64318, 0.35527, 0.8192, 0.23425, 0.64775, 0.15977, 1, 0.2054, 1, 0.09305, 0.48525, 0, 0.41375, 0.00544, 0.36675, 0.02898, 0.29625, 0.03611, 0.59968, 0.15333, 0.6109, 0.19727, 0.63835, 0.24882, 0.64547, 0.30154, 0.63918, 0.15888], "triangles": [16, 27, 23, 23, 19, 16, 18, 16, 19, 16, 18, 17, 15, 24, 23, 15, 27, 16, 27, 15, 23, 20, 19, 23, 12, 5, 4, 12, 4, 13, 6, 5, 12, 7, 6, 12, 8, 7, 12, 9, 8, 12, 9, 12, 11, 9, 11, 10, 25, 24, 15, 26, 25, 15, 25, 26, 24, 14, 26, 15, 1, 3, 2, 22, 1, 0, 3, 24, 14, 24, 26, 14, 22, 3, 1, 21, 20, 23, 3, 22, 21, 21, 24, 3, 24, 21, 23, 14, 13, 3, 4, 3, 13], "vertices": [1, 17, 157.55, -20.32, 1, 1, 17, 116.84, 45.85, 1, 1, 17, 59.8, 61.13, 1, 2, 17, 12.57, 23.92, 0.85, 18, -44.94, -25.65, 0.15, 2, 17, 0.63, 23.51, 0.45, 18, -20.63, -28.9, 0.55, 2, 17, -9.35, 39.73, 0.1, 18, -14.51, -42.17, 0.9, 1, 18, -6.28, -82.74, 1, 1, 18, 32.04, -114.87, 1, 1, 18, 83.07, -108.17, 1, 1, 18, 151.58, -69.82, 1, 1, 18, 149.64, 61.75, 1, 1, 18, 85.97, 65.07, 1, 2, 17, -23.77, -67.85, 0.15, 18, 57.15, 59.2, 0.85, 2, 17, 1.26, -53.92, 0.55, 18, 35.48, 79.59, 0.45, 1, 17, 23.22, -71.99, 1, 1, 20, -0.68, -9.47, 1, 2, 17, 85.35, -95.92, 0.176, 28, 44.5, -20.42, 0.824, 1, 28, 127.39, -17, 1, 1, 28, 119.76, 17.44, 1, 2, 17, 143.23, -73.75, 0.184, 28, -1.89, 20.68, 0.816, 2, 17, 145.73, -57.84, 0.824, 28, -17.16, 15.55, 0.176, 1, 17, 141.31, -45.76, 1, 1, 17, 143.24, -29.93, 1, 2, 17, 90.09, -86.06, 0.29, 28, 33.56, -20.77, 0.71, 3, 17, 75.97, -84.77, 0.54656, 18, -50.22, 208.5, 0.00164, 28, 39, -33.7, 0.45181, 2, 17, 58.2, -85.78, 0.736, 28, 48.04, -49.19, 0.264, 2, 17, 40.78, -81.11, 0.912, 28, 51.94, -66.8, 0.088, 3, 17, 88.19, -91.02, 0.47276, 20, 30.7, 13.98, 0.11324, 28, 41.61, -18.72, 0.414], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 6, 28, 8, 26, 10, 24, 14, 24, 12, 24, 24, 16, 50, 30, 28, 30, 30, 52, 30, 48, 46, 30, 32, 30, 46, 48, 48, 50, 50, 52, 52, 28, 46, 32, 46, 54, 54, 30], "width": 224, "height": 314}}, "Dam Nguyet Anh Forearm": {"Dam Nguyet Anh Forearm": {"type": "mesh", "uvs": [0.12694, 0.9973, 0.49274, 0.51231, 0.48708, 0.48398, 0.46641, 0.45821, 0.18983, 0.30231, 0, 0.22896, 0.06914, 0.17916, 0.38151, 0.24641, 0.44439, 0.17255, 0.39662, 0.10356, 0.27214, 0.00814, 0.65628, 0, 0.87268, 0.06898, 0.99999, 0.13688, 0.99999, 0.21946, 0.92958, 0.42739, 0.94042, 0.4593, 0.94644, 0.49012, 1, 1], "triangles": [9, 11, 12, 9, 10, 11, 7, 8, 14, 8, 13, 14, 8, 12, 13, 8, 9, 12, 4, 7, 3, 5, 6, 4, 4, 6, 7, 1, 15, 16, 1, 2, 15, 2, 3, 15, 3, 7, 15, 15, 7, 14, 0, 1, 18, 18, 1, 17, 16, 17, 1], "vertices": [1, 29, -10.31, 30.11, 1, 3, 29, 60.66, 10.95, 0.81888, 30, -4.35, 10.95, 0.17382, 31, -31.05, 18.89, 0.0073, 3, 29, 64.83, 11.15, 0.49027, 30, -0.18, 11.16, 0.48189, 31, -28.08, 15.95, 0.02784, 3, 29, 68.63, 12.1, 0.20178, 30, 3.62, 12.1, 0.72638, 31, -24.81, 13.79, 0.07184, 2, 30, 26.77, 25.25, 0.03146, 31, 0.53, 5.63, 0.96854, 1, 31, 14.65, 3.73, 1, 1, 31, 16.97, -4, 1, 3, 30, 34.83, 15.72, 0.05896, 31, -1.05, -6.75, 0.68126, 32, -0.56, 15.47, 0.25978, 3, 31, 3.85, -16.92, 0.09332, 32, 9.92, 11.27, 0.83695, 33, -3.32, 16.09, 0.06973, 3, 31, 12.33, -22.96, 0.00463, 32, 20.25, 12.54, 0.37282, 33, 5.87, 11.2, 0.62255, 1, 33, 20.46, 6.6, 1, 2, 32, 34.06, -1.7, 3e-05, 33, 9.01, -8.39, 0.99997, 2, 32, 22.86, -11.19, 0.67382, 33, -5.6, -9.74, 0.32618, 3, 30, 50.4, -14.87, 0.00527, 32, 12.29, -16.35, 0.9853, 33, -17.22, -7.9, 0.00943, 2, 30, 38.26, -14.65, 0.17705, 32, 0.21, -15.08, 0.82295, 2, 29, 72.77, -10.67, 0.08543, 30, 7.76, -10.67, 0.91457, 2, 29, 68.07, -11.12, 0.32457, 30, 3.06, -11.12, 0.67543, 2, 29, 63.53, -11.34, 0.6565, 30, -1.48, -11.33, 0.3435, 1, 29, -11.46, -12.65, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 20, 22, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 22, 24, 18, 20, 10, 12, 12, 14, 14, 16, 16, 18, 18, 24, 16, 26, 14, 28, 14, 8], "width": 49, "height": 147}}, "Dam Nguyet Anh Head": {"Dam Nguyet Anh Head": {"type": "mesh", "uvs": [0.00459, 0, 0.00459, 0.34728, 0.00459, 0.40553, 0.09093, 0.57392, 0.1298, 0.64974, 0.40401, 0.71297, 0.55047, 0.78759, 0.67986, 0.89375, 0.7478, 1, 0.99541, 1, 0.9954, 0.87023, 0.9428, 0.74081, 0.8498, 0.63836, 0.7538, 0.54419, 0.72351, 0.49115, 0.6758, 0.4076, 0.86725, 0.33345, 0.89121, 0.33534, 0.89766, 0.30194, 0.93461, 0.2876, 0.90605, 0.25972, 0.91017, 0.24876, 0.95518, 0.24019, 1, 0.21616, 0.99541, 0, 0.81778, 0.24081, 0.78467, 0.13849, 0.91491, 0.17944, 0.92219, 0.14316, 0.7729, 0.32855, 0.76965, 0.32571, 0.72873, 0.22622, 0.79613, 0.12539, 0.93191, 0.13654, 0.93478, 0.11782, 0.79614, 0.11318, 0.77633, 0.10332, 0.95145, 0.10615, 0.77013, 0.134, 0.90894, 0.23198, 0.8918, 0.22459, 0.93747, 0.14083], "triangles": [8, 7, 10, 8, 10, 9, 7, 11, 10, 6, 5, 12, 6, 12, 11, 7, 6, 11, 16, 29, 30, 2, 1, 30, 2, 30, 29, 15, 2, 29, 15, 29, 16, 3, 2, 15, 3, 15, 14, 4, 3, 14, 4, 14, 13, 5, 4, 13, 5, 13, 12, 36, 0, 24, 37, 36, 24, 35, 36, 37, 34, 35, 37, 32, 36, 35, 33, 32, 35, 36, 31, 0, 38, 36, 32, 34, 33, 35, 38, 28, 26, 41, 34, 37, 33, 34, 41, 32, 41, 38, 33, 41, 32, 41, 28, 38, 27, 26, 28, 37, 24, 23, 41, 37, 23, 40, 26, 27, 27, 28, 41, 27, 41, 23, 40, 27, 23, 38, 31, 36, 31, 38, 26, 31, 26, 40, 39, 40, 23, 31, 40, 39, 22, 39, 23, 25, 31, 39, 25, 39, 22, 21, 25, 22, 20, 25, 21, 18, 25, 20, 18, 20, 19, 30, 31, 25, 16, 30, 17, 30, 25, 17, 18, 17, 25, 1, 0, 31, 1, 31, 30], "vertices": [2, 21, 73.15, 75.52, 1, 24, -191.97, 12.3, 0, 3, 21, -33.57, 47.75, 0.15, 22, 6.85, 47.01, 0.85, 24, -88.73, -25.84, 0, 2, 21, -51.47, 43.09, 0.05, 22, -11.05, 42.36, 0.95, 1, 22, -60.11, 19.3, 1, 2, 22, -82.03, 9.14, 0.856, 24, 5.89, -46.12, 0.144, 3, 22, -93.57, -24.91, 0.40024, 24, 34.87, -24.85, 0.3737, 25, 0.04, -24.85, 0.22605, 3, 24, 62.49, -17.89, 0.00204, 25, 27.65, -17.89, 0.81996, 26, -7.19, -17.89, 0.178, 2, 26, 29.16, -16.08, 0.79014, 27, -5.68, -16.08, 0.20986, 1, 27, 28.41, -20.58, 1, 1, 27, 37.62, 4.79, 1, 2, 26, 33.91, 18.78, 0.66727, 27, -0.93, 18.78, 0.33273, 3, 24, 63.19, 27.35, 0.04537, 25, 28.35, 27.35, 0.68585, 26, -6.49, 27.35, 0.26878, 2, 24, 29.3, 28.87, 0.57119, 25, -5.54, 28.87, 0.42881, 3, 22, -32.17, -47.88, 0.77829, 24, -2.24, 29.19, 0.21214, 25, -37.08, 29.19, 0.00956, 4, 21, -62.96, -29.62, 0.02721, 22, -16.72, -40.43, 0.63534, 24, -19.13, 31.97, 0.336, 25, -53.96, 31.97, 0.00146, 3, 21, -33.11, -27.74, 0.0408, 22, 7.31, -28.47, 0.7752, 24, -45.72, 35.93, 0.184, 2, 21, -4.83, -41.21, 0.35, 22, 34.8, -42.56, 0.65, 2, 21, -5.44, -44.46, 1, 24, -58.61, 66.26, 0, 2, 21, 4.93, -42.48, 1, 24, -68.5, 70.17, 0, 2, 21, 10.32, -45.26, 1, 24, -71.18, 75.82, 0, 2, 21, 18.08, -40.05, 1, 24, -80.58, 75.37, 0, 2, 21, 21.55, -39.63, 1, 24, -83.18, 76.54, 0, 2, 21, 25.39, -43.71, 1, 24, -84.76, 82.74, 0, 2, 21, 33.96, -46.56, 1, 24, -90.05, 89.46, 0, 2, 21, 100, -29.09, 1, 24, -153.96, 113.37, 0, 2, 21, 21.48, -29.25, 1, 24, -89.56, 68.47, 0, 2, 21, 51.87, -17.73, 1, 24, -121.16, 76.1, 0, 2, 21, 42.85, -34.7, 1, 24, -104.23, 84.81, 0, 2, 21, 54.21, -32.6, 1, 24, -114.84, 89.45, 0, 2, 21, -6.18, -31.4, 0.35, 22, 33.92, -32.31, 0.65, 3, 21, -5.62, -30.99, 0.76, 22, 34.48, -31.91, 0.24, 24, -65.8, 54.57, 0, 3, 21, 23.96, -18.57, 0.99107, 22, 63.43, -18.96, 0.00892, 24, -97.06, 60.47, 0, 2, 21, 56.22, -17.9, 1, 24, -131.75, 72.67, 0, 2, 21, 56.48, -33.11, 1, 24, -125.45, 86.51, 0, 2, 21, 62.29, -31.94, 1, 24, -131.19, 87.96, 0, 2, 21, 59.95, -16.94, 1, 24, -135.54, 73.41, 0, 2, 21, 62.44, -14.07, 1, 24, -139.01, 71.89, 0, 2, 21, 66.31, -32.79, 1, 24, -134.46, 90.45, 0, 2, 21, 52.91, -15.8, 1, 24, -129.67, 69.35, 0, 2, 21, 26.65, -38.18, 1, 24, -96.34, 78.25, 0, 3, 21, 28.6, -35.75, 0.99653, 22, 74.58, -31.21, 0.00347, 24, -99.14, 76.9, 0, 2, 21, 55.32, -34.03, 1, 24, -124, 86.85, 0], "hull": 25, "edges": [8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 42, 44, 46, 48, 0, 48, 14, 20, 12, 22, 10, 24, 8, 26, 4, 30, 44, 50, 34, 36, 36, 38, 50, 36, 50, 34, 56, 52, 58, 4, 30, 32, 32, 34, 58, 32, 34, 60, 0, 2, 2, 4, 60, 2, 62, 2, 62, 60, 40, 42, 38, 40, 64, 66, 66, 68, 68, 70, 70, 64, 72, 74, 72, 76, 44, 46, 46, 80, 80, 62, 78, 62, 4, 6, 6, 8, 26, 28, 28, 30, 6, 28, 82, 76], "width": 109, "height": 316}}, "Diem Nuu Arm Left Above": {"Diem Nuu Arm Left": {"type": "mesh", "uvs": [0.58597, 0.27185, 0.56047, 0.29828, 0.35871, 0.50091, 0.46726, 0.65669, 1, 0.2462, 1, 0.12924, 0.86022, 0.06328], "triangles": [3, 1, 4, 3, 2, 1, 1, 0, 4, 4, 0, 5, 0, 6, 5], "vertices": [2, 123, 58.51, -14.59, 0.75, 124, 29.28, -14.31, 0.25, 2, 123, 55.96, -18.15, 0.5, 124, 33.66, -14.18, 0.5, 1, 124, 67.63, -13.55, 1, 1, 124, 73.64, 8.61, 1, 2, 123, 104.31, -6.89, 0.65, 124, -4.62, 17.42, 0.35, 2, 123, 102.87, 7.76, 0.7, 124, -15.41, 7.39, 0.3, 1, 123, 86.41, 14.52, 1], "hull": 7, "edges": [0, 2, 8, 10, 10, 12, 4, 6, 6, 8, 2, 4, 0, 10, 2, 8, 12, 0], "width": 112, "height": 125}}, "Diem Nuu Arm Left Below": {"Diem Nuu Arm Left": {"type": "mesh", "uvs": [0, 0.22925, 0.55047, 0.26871, 0.58597, 0.27185, 0.56047, 0.29828, 0.37251, 0.49675, 0.45913, 0.6601, 1, 0.2462, 1, 0.12924, 0.86022, 0.06328, 0, 0], "triangles": [5, 3, 6, 5, 4, 3, 3, 2, 6, 6, 2, 7, 2, 1, 8, 8, 1, 9, 2, 8, 7, 1, 0, 9], "vertices": [1, 123, -7.25, -16.17, 1, 1, 123, 54.55, -14.62, 1, 2, 123, 58.51, -14.59, 0.75, 124, 29.28, -14.31, 0.25, 2, 123, 55.96, -18.15, 0.5, 124, 33.66, -14.18, 0.5, 1, 124, 66.19, -12.77, 1, 1, 124, 74.57, 8.23, 1, 2, 123, 104.31, -6.89, 0.65, 124, -4.62, 17.42, 0.35, 2, 123, 102.87, 7.76, 0.7, 124, -15.41, 7.39, 0.3, 1, 123, 86.41, 14.52, 1, 1, 123, -10.24, 12.33, 1], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 12, 14, 14, 16, 16, 18, 0, 18, 8, 10, 10, 12, 6, 8, 4, 14, 6, 12, 2, 16], "width": 112, "height": 125}}, "Diem Nuu Arm Right Above": {"Diem Nuu Arm Right": {"type": "mesh", "uvs": [0, 0.67515, 0.01176, 0.93315, 0.11623, 1, 0.51882, 0.74508, 0.57152, 0.72294, 0.61989, 0.73508, 0.71894, 0.80244, 0.82287, 0.80708, 0.90763, 0.74443, 0.99999, 0.5056, 0.90683, 0.12491, 0.81311, 1e-05, 0.69346, 0.16418, 0.5961, 0.29308, 0.55194, 0.31708, 0.49673, 0.33708, 0.22919, 0.41239, 0.20542, 0.41908], "triangles": [8, 10, 9, 10, 7, 11, 10, 8, 7, 7, 12, 11, 12, 7, 6, 5, 4, 14, 12, 6, 13, 13, 5, 14, 5, 13, 6, 15, 14, 4, 3, 16, 15, 4, 3, 15, 1, 17, 2, 2, 17, 16, 16, 3, 2, 17, 1, 0], "vertices": [2, 117, 43.63, -9.11, 0.99828, 118, -12.39, 5.41, 0.00172, 2, 117, 57.5, -0.96, 0.52757, 118, -13.67, -10.63, 0.47243, 2, 117, 54.99, 14.65, 0.09108, 118, 0.55, -17.52, 0.90892, 2, 118, 61.24, -12.88, 0.69221, 119, -8.33, -12.62, 0.30779, 3, 118, 69.06, -12.96, 0.18374, 119, -0.51, -12.7, 0.8123, 120, -22.47, -12.7, 0.00396, 4, 118, 75.86, -15, 0.00496, 119, 6.29, -14.74, 0.93801, 120, -15.67, -14.74, 0.05619, 121, -30.67, -14.74, 0.00085, 3, 119, 19.73, -21.53, 0.49856, 120, -2.24, -21.53, 0.42293, 121, -17.24, -21.53, 0.07851, 4, 119, 34.59, -24.62, 0.10976, 120, 12.62, -24.62, 0.47147, 121, -2.38, -24.62, 0.39313, 122, -17.38, -24.62, 0.02563, 4, 119, 47.46, -23.09, 0.00963, 120, 25.5, -23.09, 0.21758, 121, 10.5, -23.09, 0.58438, 122, -4.5, -23.09, 0.1884, 3, 120, 41.5, -11.04, 0.00157, 121, 26.5, -11.04, 0.09581, 122, 11.5, -11.04, 0.90262, 3, 120, 32.5, 14.67, 0.0758, 121, 17.5, 14.67, 0.6499, 122, 2.5, 14.67, 0.2743, 4, 119, 42.45, 24.82, 0.00524, 120, 20.49, 24.82, 0.32876, 121, 5.49, 24.82, 0.63369, 122, -9.51, 24.82, 0.0323, 4, 118, 92.97, 17.79, 0.01412, 119, 23.4, 18.05, 0.32725, 120, 1.44, 18.05, 0.52565, 121, -13.56, 18.05, 0.13298, 4, 118, 77.52, 12.57, 0.36824, 119, 7.95, 12.83, 0.59745, 120, -14.01, 12.83, 0.03409, 121, -29.01, 12.83, 0.00022, 3, 118, 70.91, 12.3, 0.79306, 119, 1.34, 12.56, 0.20587, 120, -20.62, 12.56, 0.00107, 2, 118, 62.76, 12.58, 0.99833, 119, -6.81, 12.84, 0.00167, 2, 117, 15, 14.67, 0.31424, 118, 23.51, 15.22, 0.68576, 2, 117, 16.81, 11.68, 0.5873, 118, 20.02, 15.46, 0.4127], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 14, 22, 16, 20, 12, 24, 30, 32, 32, 34, 4, 32, 34, 2, 6, 30, 8, 28, 10, 26, 34, 0], "width": 146, "height": 62}}, "Diem Nuu Arm Right Below": {"Diem Nuu Arm Right": {"type": "mesh", "uvs": [0.11198, 0, 0, 0.67515, 0.01176, 0.93315, 0.11623, 1, 0.51882, 0.74508, 0.57152, 0.72294, 0.61989, 0.73508, 0.71894, 0.80244, 0.82287, 0.80708, 0.90763, 0.74443, 0.99999, 0.5056, 0.90683, 0.12491, 0.81311, 1e-05, 0.69346, 0.16418, 0.5961, 0.29308, 0.55194, 0.31708, 0.49673, 0.33708, 0.22919, 0.41239, 0.20542, 0.41908, 0.21488, 0.36572, 0.25468, 0.14108], "triangles": [9, 11, 10, 11, 8, 12, 11, 9, 8, 8, 13, 12, 13, 8, 7, 6, 5, 15, 13, 7, 14, 14, 6, 15, 6, 14, 7, 16, 15, 5, 4, 17, 16, 5, 4, 16, 2, 18, 3, 3, 18, 17, 17, 4, 3, 19, 0, 20, 19, 1, 0, 18, 1, 19, 18, 2, 1], "vertices": [1, 117, -1.25, -11.46, 1, 2, 117, 43.63, -9.11, 0.99828, 118, -12.39, 5.41, 0.00172, 2, 117, 57.5, -0.96, 0.52757, 118, -13.67, -10.63, 0.47243, 2, 117, 54.99, 14.65, 0.09108, 118, 0.55, -17.52, 0.90892, 2, 118, 61.24, -12.88, 0.69221, 119, -8.33, -12.62, 0.30779, 3, 118, 69.06, -12.96, 0.18374, 119, -0.51, -12.7, 0.8123, 120, -22.47, -12.7, 0.00396, 4, 118, 75.86, -15, 0.00496, 119, 6.29, -14.74, 0.93801, 120, -15.67, -14.74, 0.05619, 121, -30.67, -14.74, 0.00085, 3, 119, 19.73, -21.53, 0.49856, 120, -2.24, -21.53, 0.42293, 121, -17.24, -21.53, 0.07851, 4, 119, 34.59, -24.62, 0.10976, 120, 12.62, -24.62, 0.47147, 121, -2.38, -24.62, 0.39313, 122, -17.38, -24.62, 0.02563, 4, 119, 47.46, -23.09, 0.00963, 120, 25.5, -23.09, 0.21758, 121, 10.5, -23.09, 0.58438, 122, -4.5, -23.09, 0.1884, 3, 120, 41.5, -11.04, 0.00157, 121, 26.5, -11.04, 0.09581, 122, 11.5, -11.04, 0.90262, 3, 120, 32.5, 14.67, 0.0758, 121, 17.5, 14.67, 0.6499, 122, 2.5, 14.67, 0.2743, 4, 119, 42.45, 24.82, 0.00524, 120, 20.49, 24.82, 0.32876, 121, 5.49, 24.82, 0.63369, 122, -9.51, 24.82, 0.0323, 4, 118, 92.97, 17.79, 0.01412, 119, 23.4, 18.05, 0.32725, 120, 1.44, 18.05, 0.52565, 121, -13.56, 18.05, 0.13298, 4, 118, 77.52, 12.57, 0.36824, 119, 7.95, 12.83, 0.59745, 120, -14.01, 12.83, 0.03409, 121, -29.01, 12.83, 0.00022, 3, 118, 70.91, 12.3, 0.79306, 119, 1.34, 12.56, 0.20587, 120, -20.62, 12.56, 0.00107, 2, 118, 62.76, 12.58, 0.99833, 119, -6.81, 12.84, 0.00167, 2, 117, 15, 14.67, 0.31424, 118, 23.51, 15.22, 0.68576, 2, 117, 16.81, 11.68, 0.5873, 118, 20.02, 15.46, 0.4127, 2, 117, 13.23, 11.57, 0.83339, 118, 21.99, 18.45, 0.16661, 1, 117, -1.86, 11.13, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 40, 16, 24, 18, 22, 14, 26, 32, 34, 34, 36, 6, 34, 36, 4, 36, 38, 38, 40, 2, 38, 8, 32, 10, 30, 12, 28], "width": 146, "height": 62}}, "Diem Nuu Head": {"Diem Nuu Head": {"type": "mesh", "uvs": [1, 0, 0.84148, 0.56641, 1, 0.67461, 1, 1, 0.66479, 1, 0.74246, 0.75429, 0.68849, 0.65907, 0.50683, 1, 0, 1, 0, 0, 0.67796, 0.52483, 0.28697, 0.41996, 0.24547, 0.39124, 0.1731, 0.39253, 0.16281, 0.41567, 0.1922, 0.43731, 0.25134, 0.38396, 0.16906, 0.38503, 0.39396, 0.41566, 0.43886, 0.3862, 0.50774, 0.3862, 0.55723, 0.40644, 0.5057, 0.43501, 0.43478, 0.37727, 0.50825, 0.37698, 0.26451, 0.55556, 0.30747, 0.5965, 0.39379, 0.5993, 0.44268, 0.54831, 0.36004, 0.52429, 0.30714, 0.52428, 0.30457, 0.51871, 0.36188, 0.51658, 0.46802, 0.54617, 0.39856, 0.60873, 0.30342, 0.60635, 0.24137, 0.55341, 0.29794, 0.5545, 0.31411, 0.5485, 0.33504, 0.5545, 0.35158, 0.54744, 0.37618, 0.55494, 0.41622, 0.55495, 0.40373, 0.56587, 0.36259, 0.56372, 0.34164, 0.5695, 0.32438, 0.56522, 0.30969, 0.56564, 0.17577, 0.36918, 0.25881, 0.37189, 0.25556, 0.35461, 0.18236, 0.3499, 0.17661, 0.33426, 0.27085, 0.33728, 0.27044, 0.37577, 0.16927, 0.37306, 0.3643, 0.3724, 0.51588, 0.36698, 0.51025, 0.34484, 0.36787, 0.34905, 0.35545, 0.37504, 0.35521, 0.33204, 0.51355, 0.33088, 0.52381, 0.37091], "triangles": [62, 9, 0, 61, 9, 62, 53, 52, 9, 61, 53, 9, 62, 59, 61, 58, 59, 62, 50, 51, 52, 53, 50, 52, 62, 63, 58, 57, 58, 63, 52, 55, 9, 51, 55, 52, 62, 21, 63, 54, 49, 50, 51, 50, 49, 48, 51, 49, 59, 60, 61, 56, 59, 58, 56, 58, 57, 51, 48, 55, 56, 60, 59, 53, 61, 60, 53, 54, 50, 63, 60, 56, 63, 56, 57, 60, 54, 53, 55, 48, 49, 54, 55, 49, 63, 23, 60, 24, 23, 63, 16, 55, 54, 55, 14, 9, 17, 55, 16, 24, 19, 23, 20, 19, 24, 12, 17, 16, 13, 17, 12, 0, 21, 62, 24, 63, 21, 18, 60, 23, 18, 23, 19, 17, 14, 55, 14, 17, 13, 11, 54, 60, 16, 54, 11, 12, 16, 11, 22, 19, 20, 20, 24, 21, 18, 19, 22, 22, 20, 21, 15, 13, 12, 14, 13, 15, 15, 12, 11, 18, 11, 60, 32, 11, 18, 31, 11, 32, 15, 11, 31, 30, 31, 32, 29, 30, 32, 10, 21, 0, 33, 18, 22, 32, 18, 33, 28, 29, 32, 40, 30, 29, 33, 28, 32, 39, 38, 30, 36, 15, 31, 31, 25, 36, 31, 30, 25, 38, 37, 30, 40, 39, 30, 42, 41, 29, 40, 29, 41, 28, 42, 29, 30, 37, 25, 44, 40, 41, 46, 38, 39, 47, 37, 38, 47, 38, 46, 43, 41, 42, 44, 41, 43, 1, 10, 0, 45, 39, 40, 45, 40, 44, 46, 39, 45, 26, 37, 47, 25, 37, 26, 27, 44, 43, 27, 43, 42, 27, 42, 28, 45, 44, 27, 26, 47, 46, 46, 45, 26, 27, 26, 45, 35, 25, 26, 36, 25, 35, 34, 27, 28, 34, 28, 33, 35, 26, 27, 34, 35, 27, 6, 10, 1, 5, 6, 1, 5, 1, 2, 8, 9, 14, 36, 8, 14, 36, 14, 15, 8, 36, 35, 6, 33, 10, 10, 22, 21, 10, 33, 22, 6, 34, 33, 7, 34, 6, 35, 34, 7, 8, 35, 7, 5, 2, 3, 4, 5, 3], "vertices": [101.03, -48.42, 5.87, -32.89, -12.3, -48.42, -66.97, -48.42, -66.97, -15.57, -25.69, -23.18, -9.69, -17.89, -66.97, -0.09, -66.97, 49.58, 101.03, 49.58, 12.86, -16.86, 30.48, 21.45, 35.3, 25.52, 35.09, 32.61, 31.2, 33.62, 27.56, 30.74, 36.53, 24.95, 36.35, 33.01, 31.2, 10.97, 36.15, 6.57, 36.15, -0.18, 32.75, -5.03, 27.95, 0.02, 37.65, 6.97, 37.7, -0.23, 7.7, 23.66, 0.82, 19.45, 0.35, 10.99, 8.91, 6.2, 12.95, 14.29, 12.95, 19.48, 13.89, 19.73, 14.24, 14.11, 9.27, 3.71, -1.24, 10.52, -0.84, 19.84, 8.06, 25.92, 7.88, 20.38, 8.88, 18.8, 7.87, 16.74, 9.06, 15.12, 7.8, 12.71, 7.8, 8.79, 5.96, 10.01, 6.32, 14.04, 5.35, 16.1, 6.07, 17.79, 6, 19.23, 39.01, 32.35, 38.55, 24.21, 41.46, 24.53, 42.25, 31.71, 44.87, 32.27, 44.37, 23.04, 37.9, 23.08, 38.36, 32.99, 38.47, 13.88, 39.38, -0.98, 43.1, -0.43, 42.39, 13.53, 38.02, 14.74, 45.25, 14.77, 45.44, -0.75, 38.72, -1.75], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18, 30, 22, 22, 32, 32, 34, 34, 28, 28, 30, 26, 24, 36, 46, 46, 48, 48, 42, 40, 38, 36, 44, 44, 42, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 62, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 60, 88, 86, 86, 84, 84, 82, 82, 80, 80, 78, 78, 76, 76, 74, 74, 94, 94, 92, 92, 90, 90, 88, 96, 98, 100, 98, 100, 102, 102, 96, 104, 106, 106, 108, 108, 110, 110, 104, 112, 114, 114, 116, 116, 118, 118, 112, 120, 122, 122, 124, 124, 126, 126, 120], "width": 98, "height": 168}}, "Fire": {"Fire": {"y": 10, "width": 45, "height": 53}}, "Fire2": {"Fire": {"x": -0.81, "y": 10, "width": 45, "height": 53}}, "Girl Mask": {"Girl Mask": {"type": "clipping", "end": "<PERSON>", "vertexCount": 3, "vertices": [422.87, -277.8, -452.11, -278.34, 5.09, 352.95], "color": "ce3a3aff"}}, "Nam Arm Left": {"Nam Arm Left": {"x": 60.88, "y": 4.73, "rotation": 10, "width": 151, "height": 67}}, "Nam Balls": {"Nam Balls": {"x": 20.72, "y": -0.39, "rotation": 90, "width": 36, "height": 39}}, "Nam Body": {"Nam Body": {"type": "mesh", "uvs": [1, 0.1708, 1, 0.27788, 1, 0.38909, 1, 0.49773, 1, 0.5975, 1, 1, 0, 1, 0, 0.6392, 0, 0.54392, 0, 0.44469, 0, 0.34289, 0, 0.24396, 0, 0, 1, 0, 0.50902, 0.46979, 0.5011, 0.36279, 0.48918, 0.26312, 0.51294, 0.57129], "triangles": [10, 16, 1, 15, 10, 1, 8, 9, 14, 14, 2, 3, 14, 9, 2, 9, 15, 2, 9, 10, 15, 15, 1, 2, 5, 7, 4, 5, 6, 7, 7, 17, 4, 7, 8, 17, 17, 3, 4, 17, 8, 3, 8, 14, 3, 10, 11, 16, 16, 0, 1, 16, 11, 0, 11, 12, 0, 12, 13, 0], "vertices": [1, 2, 77.88, 170.39, 1, 1, 3, 77.88, 48.54, 1, 2, 2, 77.88, 96.39, 0.25, 3, 77.88, 10.84, 0.75, 2, 2, 77.88, 59.56, 0.75, 3, 77.88, -25.99, 0.25, 1, 2, 77.88, 25.74, 1, 1, 2, 77.88, -110.71, 1, 1, 2, -77.12, -110.71, 1, 1, 2, -77.12, 11.6, 1, 2, 2, -77.12, 43.9, 0.75, 3, -77.12, -41.65, 0.25, 2, 2, -77.12, 77.54, 0.25, 3, -77.12, -8.01, 0.75, 1, 3, -77.12, 26.5, 1, 1, 2, -77.12, 145.59, 1, 1, 2, -77.12, 228.29, 1, 1, 2, 77.88, 228.29, 1, 2, 2, 1.78, 69.03, 0.36863, 3, 1.78, -16.51, 0.63137, 2, 2, 0.55, 105.3, 0.26404, 3, 0.55, 19.76, 0.73596, 2, 2, -1.3, 139.09, 0.74244, 3, -1.3, 53.55, 0.25756, 2, 2, 2.38, 34.62, 0.87795, 3, 2.38, -50.92, 0.12205], "hull": 14, "edges": [10, 12, 24, 26, 22, 24, 0, 26, 22, 0, 12, 14, 8, 10, 14, 8, 18, 4, 18, 20, 20, 22, 0, 2, 2, 4, 20, 2, 14, 16, 16, 18, 4, 6, 6, 8, 16, 6], "width": 155, "height": 339}}, "Nam Forearm Left": {"Nam Forearm Left": {"x": 30.15, "y": -7.88, "rotation": 165, "width": 73, "height": 53}}, "Nam Forearm Right Above": {"Nam Forearm Right": {"type": "mesh", "uvs": [0.40194, 0.35734, 0.50326, 0.45826, 0.78495, 0.59947, 0.93461, 0.71317, 0.99999, 0.8397, 0.86946, 0.86841, 0.60898, 0.76832, 0.6315, 0.80132, 0.74795, 0.84878, 0.99187, 0.93205, 0.99999, 0.99999, 0.24312, 0.97641, 0.02488, 0.82757, 0.01021, 0.71167, 0.00737, 0.51363, 0.09383, 0.16764, 0.44767, 0.65821], "triangles": [11, 8, 10, 8, 9, 10, 11, 7, 8, 11, 12, 7, 7, 12, 16, 12, 13, 16, 13, 14, 16, 4, 5, 3, 3, 5, 6, 7, 16, 6, 6, 2, 3, 6, 16, 2, 16, 1, 2, 16, 14, 1, 14, 0, 1, 14, 15, 0], "vertices": [1, 108, 34.96, 2.31, 1, 1, 108, 48.67, 6.91, 1, 2, 108, 67.84, 19.65, 0.04846, 109, -0.02, 9.15, 0.95154, 2, 109, 16.82, 8.31, 0.5649, 113, -0.18, 8.31, 0.4351, 1, 113, 16.54, 3.3, 1, 1, 113, 17.43, -3.7, 1, 3, 109, 17.03, -8.15, 0.53176, 113, 0.03, -8.15, 0.46509, 112, -1.73, 19.85, 0.00314, 3, 110, 21.57, 11.1, 0.41324, 111, 5.06, 11.1, 0.57631, 112, 0.68, 15.93, 0.01045, 4, 109, 29.61, -7.41, 0.00758, 113, 12.62, -7.41, 0.00312, 111, 13.04, 13.41, 0.48447, 112, 7.71, 11.5, 0.50483, 1, 112, 21.75, 4.31, 1, 1, 112, 25.07, -4.32, 1, 3, 110, 36.7, -14.27, 0.00053, 111, 20.19, -14.27, 0.89052, 112, -8.21, -12.24, 0.10895, 2, 110, 14.23, -15.44, 0.5527, 111, -2.28, -15.44, 0.4473, 3, 108, 83.2, -15.17, 0.00648, 110, -0.54, -9.91, 0.9839, 111, -17.05, -9.91, 0.00962, 1, 108, 56.27, -15.38, 1, 1, 108, 9.2, -11.64, 1, 3, 108, 75.87, 4.49, 0.0237, 109, 0.39, -7.99, 0.58109, 110, 0.42, 11.05, 0.39521], "hull": 16, "edges": [20, 22, 22, 24, 18, 20, 16, 18, 6, 8, 14, 16, 8, 10, 10, 12, 12, 14, 14, 24, 16, 22, 12, 6, 24, 26, 32, 26, 26, 28, 28, 30, 2, 28, 2, 4, 4, 6, 32, 4, 2, 0, 0, 30], "width": 45, "height": 136}}, "Nam Forearm Right Below": {"Nam Forearm Right": {"type": "mesh", "uvs": [0.76806, 0.14863, 0.61168, 0.46783, 0.78495, 0.59947, 0.93461, 0.71317, 0.99999, 0.8397, 0.86946, 0.86841, 0.60898, 0.76832, 0.58673, 0.77049, 0.69518, 0.85906, 0.99187, 0.93205, 0.99999, 0.99999, 0.24312, 0.97641, 0.02488, 0.82757, 0.01021, 0.71167, 0.00737, 0.51363, 0, 0, 0.44767, 0.65821], "triangles": [8, 9, 10, 11, 8, 10, 11, 12, 7, 11, 7, 8, 13, 14, 16, 12, 13, 16, 12, 16, 7, 3, 5, 6, 4, 5, 3, 16, 1, 2, 6, 16, 2, 6, 2, 3, 7, 16, 6, 1, 15, 0, 14, 15, 1, 16, 14, 1], "vertices": [1, 108, 6.53, 18.7, 1, 1, 108, 49.96, 11.79, 1, 2, 108, 67.84, 19.65, 0.04846, 109, -0.02, 9.15, 0.95154, 2, 109, 16.82, 8.31, 0.5649, 113, -0.18, 8.31, 0.4351, 1, 113, 16.54, 3.3, 1, 1, 113, 17.43, -3.7, 1, 3, 109, 17.03, -8.15, 0.53176, 113, 0.03, -8.15, 0.46509, 112, -1.73, 19.85, 0.00314, 3, 110, 16.92, 10.87, 0.41324, 111, 0.41, 10.87, 0.57631, 112, -2.58, 19.25, 0.01045, 4, 109, 29.8, -10.16, 0.00758, 113, 12.8, -10.16, 0.00312, 111, 13.41, 10.68, 0.48447, 112, 5.92, 9.42, 0.50483, 1, 112, 21.75, 4.31, 1, 1, 112, 25.07, -4.32, 1, 3, 110, 36.7, -14.27, 0.00053, 111, 20.19, -14.27, 0.89052, 112, -8.21, -12.24, 0.10895, 2, 110, 14.23, -15.44, 0.5527, 111, -2.28, -15.44, 0.4473, 3, 108, 83.2, -15.17, 0.00648, 110, -0.54, -9.91, 0.9839, 111, -17.05, -9.91, 0.00962, 1, 108, 56.27, -15.38, 1, 1, 108, -13.58, -15.93, 1, 3, 108, 75.87, 4.49, 0.0237, 109, 0.39, -7.99, 0.58109, 110, 0.42, 11.05, 0.39521], "hull": 16, "edges": [0, 2, 20, 22, 22, 24, 30, 0, 18, 20, 16, 18, 6, 8, 14, 16, 8, 10, 10, 12, 12, 14, 14, 24, 16, 22, 12, 6, 24, 26, 32, 26, 26, 28, 28, 30, 2, 28, 2, 4, 4, 6, 32, 4], "width": 45, "height": 136}}, "Nam Hair": {"Nam Hair": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.75, 0, 0.5, 0, 0.25, 0, 0, 1, 0, 1, 0.25, 1, 0.5, 1, 0.75], "triangles": [1, 9, 2, 0, 9, 1, 9, 8, 2, 3, 7, 4, 8, 7, 3, 2, 8, 3, 4, 6, 5, 7, 6, 4], "vertices": [2, 7, 25.18, 11.14, 0.02609, 8, 13.93, 11.14, 0.97391, 2, 7, 28.16, -10.66, 0.00284, 8, 16.91, -10.66, 0.99716, 3, 6, 26.03, -12.48, 0.02213, 7, 14.78, -12.48, 0.37264, 8, 3.53, -12.48, 0.60523, 4, 5, 23.9, -14.31, 0.00378, 6, 12.65, -14.31, 0.50132, 7, 1.4, -14.31, 0.47812, 8, -9.85, -14.31, 0.01679, 3, 5, 10.53, -16.14, 0.38086, 6, -0.72, -16.14, 0.59345, 7, -11.97, -16.14, 0.02569, 2, 5, -2.85, -17.96, 0.80225, 6, -14.1, -17.96, 0.19775, 1, 5, -5.82, 3.84, 1, 2, 5, 7.55, 5.66, 0.71049, 6, -3.7, 5.66, 0.28951, 3, 6, 9.68, 7.49, 0.73238, 7, -1.57, 7.49, 0.267, 8, -12.82, 7.49, 0.00062, 3, 6, 23.05, 9.31, 0.0142, 7, 11.8, 9.31, 0.50188, 8, 0.55, 9.31, 0.48393], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 0], "width": 22, "height": 54}}, "Nam Hand Left": {"Nam Hand Left": {"type": "mesh", "uvs": [0.31127, 0.09869, 0, 0.30869, 1e-05, 0.45021, 0.29964, 0.38136, 0.30188, 0.40636, 0.19086, 0.54869, 0.0724, 0.71469, 0.3404, 1, 0.78318, 1, 0.89509, 0.79672, 0.96889, 0.598, 0.9915, 0.37944, 1, 0.22069, 0.75405, 0, 0.40653, 0.23895, 0.42231, 0.24986], "triangles": [14, 0, 13, 3, 1, 0, 3, 0, 14, 15, 3, 14, 2, 1, 3, 7, 5, 9, 6, 5, 7, 8, 7, 9, 4, 15, 10, 9, 4, 10, 5, 4, 9, 15, 4, 3, 11, 13, 12, 15, 13, 11, 10, 15, 11, 15, 14, 13], "vertices": [2, 13, 15.21, -31.19, 0.00498, 12, 0.16, -7.59, 0.99502, 1, 12, 25.62, -4.29, 1, 1, 12, 29.75, 4.25, 1, 4, 13, 31.32, -21.17, 0.00874, 14, 11.32, -21.17, 0.22704, 15, -8.68, -21.17, 0.04278, 12, 9.1, 9.15, 0.72144, 3, 13, 32.6, -20.06, 0.01607, 14, 12.6, -20.06, 0.75585, 15, -7.4, -20.06, 0.22808, 2, 14, 24.78, -20.8, 0.30959, 15, 4.78, -20.8, 0.69041, 2, 14, 38.57, -21.09, 0.0621, 15, 18.57, -21.09, 0.9379, 2, 14, 43.62, 5.04, 0.01199, 15, 23.62, 5.04, 0.98801, 3, 13, 46.1, 30.07, 0.0604, 14, 26.1, 30.07, 0.5947, 15, 6.1, 30.07, 0.34491, 3, 13, 30.51, 28.58, 0.22425, 14, 10.51, 28.58, 0.66605, 15, -9.49, 28.58, 0.1097, 3, 13, 16.69, 25.11, 0.56702, 14, -3.31, 25.11, 0.42689, 15, -23.31, 25.11, 0.0061, 2, 13, 3.8, 17.99, 0.91886, 14, -16.2, 17.99, 0.08114, 1, 13, -5.25, 12.37, 1, 1, 13, -7.59, -9.94, 1, 4, 13, 19.29, -20.47, 0.10846, 14, -0.71, -20.47, 0.07929, 15, -20.71, -20.47, 0.00044, 12, -1.67, 3.73, 0.81181, 3, 13, 19.27, -19.17, 0.61579, 14, -0.73, -19.17, 0.38214, 15, -20.73, -19.17, 0.00207], "hull": 14, "edges": [0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 0, 4, 6, 6, 8, 6, 28, 10, 18, 8, 20, 20, 22, 22, 24, 0, 28, 22, 30, 30, 28], "width": 69, "height": 67}}, "Nam Head": {"Nam Head": {"type": "mesh", "uvs": [1, 1, 0.60723, 1, 0.55812, 1, 0.48378, 1, 0.44396, 1, 0.2349, 1, 0.19556, 1, 0.07322, 1, 0, 1, 0, 0, 1, 0, 0.11514, 0.72288, 0.15275, 0.70082, 0.20452, 0.6942, 0.24257, 0.7078, 0.30319, 0.70597, 0.25231, 0.74567, 0.20939, 0.76001, 0.16116, 0.76111, 0.13638, 0.74677, 0.10231, 0.71369, 0.33479, 0.68273, 0.2116, 0.78942, 0.16204, 0.78942, 0.25099, 0.67177, 0.14112, 0.68805, 0.29995, 0.74298, 0.06576, 0.55765, 0.08843, 0.53825, 0.12719, 0.53429, 0.14294, 0.54606, 0.14063, 0.56723, 0.12062, 0.57591, 0.08416, 0.57575, 0.081, 0.53222, 0.12914, 0.53269, 0.05345, 0.52275, 0.13894, 0.53263, 0.14355, 0.50778, 0.05754, 0.48996, 0.05318, 0.46015, 0.04625, 0.52974, 0.23995, 0.52674, 0.27642, 0.50645, 0.33004, 0.50544, 0.36123, 0.51618, 0.3632, 0.53228, 0.32381, 0.55653, 0.2601, 0.55467, 0.18621, 0.51228, 0.22646, 0.527, 0.30568, 0.48621, 0.35922, 0.47851, 0.34995, 0.44579, 0.31854, 0.43585, 0.15697, 0.49343, 0.33854, 0.39073, 0.38623, 0.47189, 0.21362, 0.54354, 0.36519, 0.50572, 0.33104, 0.49688, 0.27528, 0.50422, 0.03826, 0.75004, 0.13341, 0.81102, 0.13499, 0.802, 0.03059, 0.73727, 0.46796, 0.76067, 0.55027, 0.73091, 0.46132, 0.75442, 0.5609, 0.71658], "triangles": [63, 62, 64, 7, 8, 62, 63, 7, 62, 5, 6, 63, 7, 63, 6, 66, 68, 67, 2, 3, 66, 4, 66, 3, 1, 2, 67, 2, 66, 67, 8, 65, 62, 1, 67, 69, 64, 65, 20, 11, 20, 25, 65, 33, 20, 19, 11, 12, 18, 19, 12, 23, 18, 17, 22, 5, 23, 68, 66, 4, 12, 25, 13, 65, 41, 27, 65, 27, 33, 22, 17, 16, 5, 22, 26, 26, 4, 5, 68, 4, 26, 56, 9, 10, 56, 40, 9, 57, 53, 56, 54, 56, 53, 56, 55, 40, 10, 57, 56, 52, 53, 57, 56, 54, 55, 51, 54, 53, 51, 53, 52, 40, 38, 39, 49, 55, 54, 60, 51, 52, 49, 54, 51, 61, 51, 60, 60, 43, 61, 59, 52, 57, 60, 52, 59, 44, 60, 59, 44, 43, 60, 55, 38, 40, 49, 51, 50, 45, 44, 59, 39, 41, 40, 36, 35, 34, 40, 41, 9, 50, 51, 61, 42, 61, 43, 42, 50, 61, 39, 36, 41, 37, 36, 38, 41, 36, 34, 46, 45, 59, 46, 59, 57, 39, 38, 36, 37, 35, 36, 29, 34, 35, 28, 34, 29, 58, 49, 50, 55, 49, 58, 58, 50, 42, 55, 30, 37, 35, 37, 30, 29, 35, 30, 55, 37, 38, 48, 42, 43, 47, 43, 44, 46, 47, 44, 46, 44, 45, 48, 43, 47, 27, 41, 34, 27, 34, 28, 31, 29, 30, 30, 55, 58, 33, 27, 28, 32, 28, 29, 32, 29, 31, 33, 28, 32, 48, 58, 42, 24, 58, 48, 24, 48, 47, 21, 47, 46, 24, 47, 21, 58, 31, 30, 24, 31, 58, 15, 24, 21, 14, 13, 24, 14, 24, 15, 69, 57, 10, 46, 57, 69, 68, 21, 46, 46, 69, 68, 15, 16, 14, 26, 15, 21, 26, 16, 15, 41, 65, 9, 26, 21, 68, 31, 24, 25, 32, 31, 25, 13, 25, 24, 25, 33, 32, 17, 13, 14, 17, 14, 16, 22, 16, 26, 25, 20, 33, 11, 25, 12, 8, 9, 65, 18, 12, 13, 13, 17, 18, 23, 17, 22, 67, 68, 69, 0, 69, 10, 19, 18, 23, 20, 11, 19, 23, 20, 19, 64, 20, 23, 5, 64, 23, 0, 1, 69, 64, 62, 65, 64, 5, 63], "vertices": [1, 4, -38.42, -62.83, 1, 1, 4, -38.42, -18.44, 1, 3, 4, -38.42, -12.89, 6e-05, 144, 34.15, 3.72, 0.99927, 145, 38, 49.03, 0.00067, 3, 4, -38.42, -4.49, 0.00286, 144, 33.68, -4.67, 0.9709, 145, 36.93, 40.7, 0.02624, 1, 4, -38.42, 0.01, 1, 1, 4, -38.42, 23.63, 1, 3, 4, -38.42, 28.08, 0.01568, 144, 31.86, -37.19, 0.11662, 145, 32.76, 8.39, 0.8677, 1, 145, 31, -5.32, 1, 1, 4, -38.42, 50.17, 1, 1, 4, 97.58, 50.17, 1, 1, 4, 97.58, -62.83, 1, 1, 4, -0.74, 37.16, 1, 1, 4, 2.26, 32.91, 1, 1, 4, 3.16, 27.06, 1, 1, 4, 1.31, 22.76, 1, 1, 4, 1.56, 15.91, 1, 1, 4, -3.84, 21.66, 1, 1, 4, -5.79, 26.51, 1, 1, 4, -5.94, 31.96, 1, 1, 4, -3.99, 34.76, 1, 1, 4, 0.51, 38.61, 1, 1, 4, 4.72, 12.34, 1, 1, 4, -9.78, 26.26, 1, 1, 4, -9.78, 31.86, 1, 1, 4, 6.21, 21.81, 1, 1, 4, 4, 34.23, 1, 1, 4, -3.47, 16.28, 1, 1, 4, 21.74, 42.74, 1, 1, 4, 24.37, 40.18, 1, 1, 4, 24.91, 35.8, 1, 1, 4, 23.31, 34.02, 1, 1, 4, 20.43, 34.28, 1, 1, 4, 19.25, 36.54, 1, 1, 4, 19.27, 40.66, 1, 1, 4, 25.19, 41.02, 1, 1, 4, 25.13, 35.58, 1, 1, 4, 26.48, 44.13, 1, 1, 4, 25.14, 34.47, 1, 1, 4, 28.52, 33.95, 1, 1, 4, 30.94, 43.67, 1, 1, 4, 35, 44.17, 1, 1, 4, 25.53, 44.95, 1, 1, 4, 25.94, 23.06, 1, 1, 4, 28.7, 18.94, 1, 1, 4, 28.84, 12.88, 1, 1, 4, 27.38, 9.36, 1, 1, 4, 25.19, 9.13, 1, 1, 4, 21.89, 13.58, 1, 1, 4, 22.14, 20.78, 1, 1, 4, 27.91, 29.13, 1, 1, 4, 25.9, 24.58, 1, 1, 4, 31.45, 15.63, 1, 1, 4, 32.5, 9.58, 1, 1, 4, 36.95, 10.63, 1, 1, 4, 38.3, 14.18, 1, 1, 4, 30.47, 32.44, 1, 1, 4, 44.44, 11.92, 1, 1, 4, 33.4, 6.53, 1, 1, 4, 23.65, 26.04, 1, 1, 4, 28.8, 8.91, 1, 1, 4, 30, 12.77, 1, 1, 4, 29, 19.07, 1, 2, 4, -4.43, 45.85, 0.01485, 145, -3.22, -4.89, 0.98515, 3, 4, -12.72, 35.1, 0.0414, 144, 5.8, -42.76, 0.01018, 145, 6.38, 4.71, 0.94841, 1, 4, -11.5, 34.92, 1, 1, 4, -2.69, 46.72, 1, 3, 4, -5.87, -2.7, 0.32695, 144, 1.08, -4.64, 0.66046, 145, 4.42, 43.08, 0.0126, 2, 4, -1.83, -12.01, 0.0871, 144, -2.44, 4.88, 0.9129, 1, 4, -5.02, -1.95, 1, 1, 4, 0.12, -13.21, 1], "hull": 11, "edges": [16, 18, 18, 20, 0, 20, 44, 46, 46, 40, 48, 42, 40, 50, 50, 48, 42, 52, 52, 44, 54, 68, 68, 70, 70, 60, 60, 58, 58, 56, 56, 54, 72, 74, 74, 76, 76, 78, 78, 72, 80, 82, 82, 68, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 98, 110, 112, 112, 114, 110, 116, 116, 84, 84, 86, 86, 88, 88, 90, 90, 92, 114, 92, 118, 92, 118, 120, 120, 122, 122, 84, 80, 110, 110, 60, 14, 16, 124, 14, 12, 14, 126, 12, 10, 12, 128, 10, 128, 130, 130, 16, 132, 6, 132, 134, 4, 6, 134, 4, 6, 8, 8, 10, 136, 8, 136, 138, 0, 2, 2, 4, 138, 2], "width": 113, "height": 136}}, "Nam Leg Left": {"Nam Leg Left": {"x": 110.98, "y": -107.79, "rotation": -30, "width": 177, "height": 103}}, "Nam Leg Right": {"Nam Leg Right": {"x": -118.18, "y": -106.75, "rotation": 30, "width": 183, "height": 104}}, "Nam Penis": {"Nam Penis": {"x": 32.41, "y": -1.91, "rotation": -177.62, "width": 79, "height": 30}}, "Nam Penis Fur": {"Nam Penis Fur": {"x": -1.75, "y": -73.51, "width": 56, "height": 70}}, "Nguu Le Hoa Arm": {"Nguu Le Hoa Arm": {"type": "mesh", "uvs": [1, 0.23032, 0.68602, 0.93828, 0.62889, 1, 0.5636, 0.94829, 0.28519, 0.53234, 0.26877, 0.50808, 0.24792, 0.49113, 0.16622, 0.44852, 0.09605, 0.41307, 0.03276, 0.27533, 0, 0.10247, 0.05966, 0.00954, 0.10415, 0.1378, 0.15854, 0.18149, 0.1805, 0.15862, 0.15667, 0.03666, 0.19302, 0, 0.22246, 0.10973, 0.2585, 0.19071, 0.31441, 0.30612, 0.33088, 0.33395, 0.34766, 0.35468, 0.5986, 0.60573, 0.62289, 0.62263, 0.64179, 0.58672, 0.86344, 0, 1, 0, 0.21586, 0.24051], "triangles": [10, 11, 12, 9, 10, 12, 13, 8, 9, 9, 12, 13, 7, 8, 27, 7, 27, 19, 15, 16, 17, 17, 14, 15, 13, 27, 8, 13, 14, 27, 14, 17, 18, 27, 14, 18, 27, 18, 19, 6, 7, 19, 20, 6, 19, 5, 6, 20, 1, 2, 23, 21, 5, 20, 4, 5, 21, 22, 4, 21, 3, 4, 22, 3, 22, 23, 2, 3, 23, 23, 24, 1, 25, 26, 0, 0, 24, 25, 1, 24, 0], "vertices": [1, 45, -18.33, 17.95, 1, 2, 45, 85.93, 15.73, 0.36776, 46, -16.24, 2.46, 0.63224, 2, 45, 99.38, 10.42, 0.47897, 46, -8.78, 14.83, 0.52103, 2, 45, 104.55, -4.3, 0.36, 46, 6.6, 17.5, 0.64, 3, 46, 83.17, 10.61, 0.7664, 47, -3.98, 10.61, 0.22899, 48, -28.45, 3.87, 0.00461, 3, 46, 87.67, 10.23, 0.41352, 47, 0.53, 10.23, 0.56197, 48, -23.93, 3.66, 0.02451, 3, 46, 92.61, 11.04, 0.10329, 47, 5.46, 11.04, 0.8039, 48, -19.03, 4.65, 0.09281, 3, 47, 23.47, 16.45, 0.07072, 48, -1.24, 10.72, 0.91872, 49, -16.38, 10.72, 0.01056, 2, 48, 13.98, 16.04, 0.57013, 49, -1.16, 16.04, 0.42987, 3, 48, 34, 11.18, 0.00465, 49, 18.86, 11.18, 0.58522, 50, -2.87, 11.11, 0.41013, 1, 50, 17.14, 8.29, 1, 1, 50, 19.48, -8.27, 1, 5, 51, 23.08, 19.68, 0.01715, 52, 11.22, 19.68, 0.00033, 48, 28.87, -10.08, 0.00141, 49, 13.73, -10.08, 0.52151, 50, 2.43, -10.1, 0.4596, 6, 47, 39.93, -7.62, 0.00835, 51, 12.5, 12.08, 0.31825, 52, 0.63, 12.08, 0.05573, 48, 16.11, -12.72, 0.25144, 49, 0.97, -12.72, 0.35654, 50, -7.64, -18.36, 0.0097, 6, 47, 37.02, -12.28, 0.00707, 51, 11.94, 6.62, 0.49865, 52, 0.07, 6.62, 0.32197, 48, 13.37, -17.48, 0.10512, 49, -1.77, -17.48, 0.0671, 50, -7.86, -23.85, 9e-05, 1, 52, 14.12, 3.86, 1, 1, 52, 13.07, -5.11, 1, 2, 51, 11.33, -4.13, 0.63395, 52, -0.54, -4.13, 0.36605, 3, 46, 107.45, -18.24, 0.00015, 47, 20.31, -18.24, 0.04702, 51, -0.44, -6.09, 0.95283, 3, 46, 90.28, -13.88, 0.27352, 47, 3.14, -13.88, 0.55924, 51, -17.78, -9.73, 0.16724, 3, 46, 85.57, -13.17, 0.56427, 47, -1.57, -13.17, 0.36475, 51, -22.32, -11.17, 0.07099, 3, 46, 81.2, -13.16, 0.80949, 47, -5.94, -13.16, 0.16365, 51, -26.25, -13.09, 0.02686, 2, 45, 71.2, -22.7, 0.30758, 46, 19.22, -18.44, 0.69242, 2, 45, 69.1, -17.45, 0.60858, 46, 13.69, -19.64, 0.39142, 2, 45, 63.4, -16.85, 0.87287, 46, 12.16, -25.16, 0.12713, 1, 45, -17.29, -21.51, 1, 1, 45, -37.27, 1.48, 1, 4, 47, 25.66, -8.69, 0.15483, 51, 0.16, 4.84, 0.75389, 48, 1.89, -14.32, 0.08766, 49, -13.25, -14.32, 0.00363], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52, 46, 4, 44, 6, 48, 2, 12, 38, 10, 40, 8, 42, 14, 54, 54, 36, 16, 18, 18, 20, 20, 22, 14, 16, 22, 24, 30, 32, 24, 26, 26, 28, 28, 30, 16, 26, 28, 34, 18, 24], "width": 223, "height": 109}}, "Nguu Le Hoa Body": {"Nguu Le Hoa Body": {"type": "mesh", "uvs": [0.20351, 0, 0.24118, 0.07086, 0.11597, 0.17743, 0.1507, 0.17706, 0.10971, 0.20172, 0.12298, 0.25044, 0.19849, 0.27533, 0.17213, 0.28627, 0.25282, 0.321, 0.31707, 0.34814, 0.37255, 0.37673, 0.45101, 0.41715, 0.4582, 0.42086, 0.11587, 0.62262, 0.06842, 0.68315, 0, 0.77043, 0.01728, 0.82519, 0.10441, 0.84511, 0.29609, 0.86115, 0.24293, 1, 0.53792, 1, 0.63002, 0.77108, 0.38731, 0.76444, 0.39879, 0.7583, 0.45487, 0.72827, 0.79339, 0.54703, 0.96142, 0.49779, 1, 0.42975, 0.96913, 0.35393, 0.69663, 0.29658, 0.62364, 0.27026, 0.58926, 0.2251, 0.58802, 0, 0.14419, 0.20424, 0.21637, 0.14302, 0.38595, 0.16021, 0.40301, 0.20221, 0.37793, 0.24096, 0.29511, 0.27004, 0.30473, 0.27827, 0.38747, 0.24413, 0.15805, 0.17699, 0.12442, 0.20263, 0.13075, 0.23352, 0.19137, 0.266, 0.12897, 0.20296, 0.13377, 0.22693], "triangles": [33, 45, 41, 46, 45, 33, 46, 33, 44, 16, 15, 22, 17, 16, 22, 18, 17, 22, 20, 22, 21, 18, 22, 20, 19, 18, 20, 15, 14, 22, 22, 13, 23, 12, 11, 29, 12, 29, 28, 12, 28, 27, 26, 12, 27, 25, 12, 26, 24, 13, 12, 24, 12, 25, 23, 13, 24, 14, 13, 22, 1, 0, 32, 35, 1, 32, 34, 1, 35, 2, 1, 34, 34, 41, 3, 34, 3, 2, 4, 3, 41, 31, 36, 35, 42, 4, 41, 45, 42, 41, 33, 41, 34, 33, 34, 35, 33, 35, 36, 31, 35, 32, 46, 42, 45, 43, 42, 46, 37, 33, 36, 40, 37, 36, 43, 5, 4, 43, 4, 42, 38, 44, 33, 43, 46, 44, 5, 43, 44, 37, 38, 33, 6, 44, 38, 5, 44, 6, 39, 38, 37, 39, 37, 40, 39, 7, 6, 39, 6, 38, 8, 7, 39, 8, 39, 40, 40, 36, 31, 8, 40, 31, 9, 8, 31, 9, 31, 30, 10, 9, 30, 10, 30, 29, 11, 10, 29], "vertices": [1, 37, 165.33, 7.07, 1, 1, 37, 135.27, 13.53, 1, 1, 37, 104.36, 53.91, 1, 1, 37, 101.75, 47.93, 1, 1, 37, 95.55, 59.32, 1, 1, 37, 75.82, 65.77, 1, 2, 37, 48.27, 64.95, 0.75, 41, 4.92, -18.8, 0.25, 1, 37, 58.18, 63.8, 1, 1, 37, 38.45, 56.26, 1, 2, 37, 22.94, 50.17, 0.98936, 38, 21.97, -79.95, 0.01064, 1, 37, 7.58, 45.82, 1, 2, 37, -14.16, 39.68, 0.4, 38, 31.44, -42.58, 0.6, 2, 37, -16.15, 39.12, 0.15, 38, 31.95, -40.57, 0.85, 1, 38, 138.77, -44.34, 1, 2, 38, 164.9, -35.59, 0.61598, 39, -29.84, 32.22, 0.38402, 2, 38, 202.56, -23.05, 0.08371, 39, -30.93, -7.46, 0.91629, 2, 38, 219.67, -6.8, 0.00064, 39, -21.52, -29.1, 0.99936, 1, 39, -3.52, -33, 1, 1, 39, 33.05, -30.23, 1, 1, 39, 38.59, -89.55, 1, 1, 39, 92.16, -75.19, 1, 1, 39, 83.83, 22.82, 1, 2, 38, 158.71, 34.56, 0.25, 39, 38.19, 14.05, 0.75, 2, 38, 155.34, 34.83, 0.25, 39, 39.6, 17.12, 0.75, 2, 38, 138.85, 34.39, 0.5, 39, 44.83, 32.76, 0.5, 1, 38, 39.52, 41.66, 1, 1, 38, 4.34, 55.59, 1, 2, 37, -62.6, -51.61, 0.00242, 38, -23.39, 45.02, 0.99758, 2, 37, -31.09, -59.9, 0.1, 38, -46.33, 21.87, 0.9, 2, 37, 12.55, -23.72, 0.9, 38, -36.82, -34.01, 0.1, 2, 37, 28.44, -15.99, 0.99788, 38, -38.07, -51.64, 0.00212, 1, 37, 48.49, -18.21, 1, 1, 37, 134.88, -58.24, 1, 1, 41, 0.01, 0, 1, 1, 37, 109.58, 30.65, 1, 1, 37, 89.52, 4.83, 1, 1, 37, 72.06, 9.43, 1, 2, 37, 39.71, 13.36, 0.75, 41, 26.27, -10.9, 0.25, 2, 37, 36.96, 41.47, 0.75, 41, 16.24, -18, 0.25, 1, 37, 50.71, 39.78, 1, 1, 37, 57.22, 19.58, 1, 2, 37, 106.68, 37.41, 0.5, 41, 4.32, 2.56, 0.5, 2, 37, 95.5, 57.67, 0.75, 41, -2.52, 0.63, 0.25, 2, 37, 78.98, 66.34, 0.75, 41, -2.23, -7.49, 0.25, 2, 37, 53.93, 63.43, 0.75, 41, 4.28, -16.33, 0.25, 2, 37, 95.86, 57.36, 0.5, 41, -0.65, 1.2, 0.5, 2, 37, 79.81, 67.38, 0.5, 41, -3.7, -1.84, 0.5], "hull": 33, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 0, 64, 18, 20, 14, 78, 78, 80, 80, 72, 70, 72, 72, 74, 74, 76, 76, 12, 68, 6, 66, 70, 68, 66, 68, 82, 82, 8, 66, 82, 76, 66, 66, 74, 72, 66, 24, 56, 24, 54, 52, 24, 24, 50, 8, 84, 82, 84, 10, 86, 84, 86, 12, 88, 88, 66, 86, 88, 88, 76, 66, 90, 90, 84, 90, 82, 66, 92, 92, 86, 92, 88, 90, 92, 16, 62, 18, 60, 20, 58, 20, 22, 22, 24, 24, 26, 30, 44, 44, 32, 44, 34, 26, 28, 28, 30, 44, 28, 44, 46, 26, 46, 46, 48, 48, 50, 26, 48], "width": 188, "height": 423}}, "Nguu Le Hoa Breast": {"Nguu Le Hoa Breast": {"type": "mesh", "uvs": [0.02004, 0.60045, 0.0225, 0.4725, 0.37952, 0.07783, 0.45845, 1e-05, 0.67586, 1e-05, 0.73624, 0.10123, 0.9785, 0.44379, 0.96052, 0.70177, 0.78562, 0.89861, 0.54369, 1, 0.22167, 0.92247, 0.15097, 0.50933], "triangles": [0, 1, 11, 9, 10, 11, 0, 11, 10, 8, 9, 11, 11, 1, 2, 7, 11, 6, 8, 11, 7, 11, 5, 6, 4, 5, 3, 3, 5, 2, 2, 5, 11], "vertices": [2, 37, 84.14, 82.62, 0.25, 43, -2.11, 7.62, 0.75, 2, 37, 90.68, 79.4, 0.25, 43, 4.43, 4.4, 0.75, 1, 37, 103.56, 53.82, 1, 1, 37, 105.85, 48.22, 1, 1, 37, 101.07, 37.98, 1, 1, 37, 94.52, 37.57, 1, 2, 37, 71.5, 34.4, 0.75, 43, -14.75, -40.6, 0.25, 2, 37, 58.56, 41.47, 0.75, 43, -27.69, -33.53, 0.25, 2, 37, 52.24, 54.45, 0.75, 43, -34.01, -20.55, 0.25, 2, 37, 52.32, 68.29, 0.5, 43, -33.93, -6.71, 0.5, 2, 37, 63.4, 81.6, 0.25, 43, -22.85, 6.6, 0.75, 1, 43, 0.05, -0.02, 1], "hull": 11, "edges": [6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 22, 14, 22, 18, 16, 22, 22, 12, 8, 10, 10, 12, 4, 6, 4, 22, 22, 10, 2, 4, 22, 20, 2, 0, 0, 20], "width": 52, "height": 57}}, "Nguu Le Hoa Hair": {"Nguu Le Hoa Hair": {"type": "mesh", "uvs": [0.22596, 0.35155, 0.18323, 0.50806, 0.21063, 0.72823, 0.34921, 1, 0.27771, 1, 0.09868, 1, 0, 0.8205, 1e-05, 0.49473, 1e-05, 0.25939, 0, 1e-05, 1, 1e-05, 0.95179, 0.24693, 0.93219, 0.48574, 1, 0.67078, 0.99999, 0.913, 0.68134, 1, 0.57368, 0.79654, 0.51971, 0.56598, 0.44799, 0.33776, 0.17792, 0.74375], "triangles": [5, 6, 19, 6, 7, 19, 14, 16, 13, 15, 16, 14, 5, 19, 4, 10, 18, 9, 9, 0, 8, 10, 11, 18, 18, 0, 9, 12, 18, 11, 7, 8, 0, 1, 7, 0, 17, 18, 12, 19, 7, 1, 19, 1, 2, 16, 17, 12, 16, 12, 13, 3, 4, 2, 19, 2, 4], "vertices": [1, 53, 44.62, 36.43, 1, 1, 53, 30.17, 41.9, 1, 1, 53, 9.1, 41.05, 1, 1, 53, -17.8, 29.77, 1, 2, 53, -17.19, 36.75, 0.75, 57, 25.36, 11.12, 0.25, 1, 57, 22.76, -6.24, 1, 2, 56, 28.46, -13.27, 0.5, 57, 4.46, -13.27, 0.5, 1, 56, -2.15, -8.69, 1, 1, 53, 55.27, 57.73, 1, 1, 53, 79.82, 55.58, 1, 1, 53, 71.28, -42.04, 1, 1, 53, 48.32, -35.29, 1, 1, 53, 25.89, -31.4, 1, 2, 54, 21.17, 20.99, 0.5, 55, -0.83, 20.99, 0.5, 1, 55, 21.55, 15.61, 1, 1, 55, 22.28, -16.68, 1, 2, 54, 23.02, -22.42, 0.5, 55, 1.02, -22.42, 0.5, 2, 53, 21.82, 9.53, 0.5, 54, 0.49, -22.44, 0.5, 1, 53, 44.03, 14.64, 1, 3, 53, 7.91, 44.37, 0.85, 56, 23.83, 5.05, 0.075, 57, -0.17, 5.05, 0.075], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 18, 20, 26, 28, 30, 32, 34, 36, 0, 36, 28, 30, 2, 38, 6, 8, 8, 10, 38, 8, 24, 26, 32, 34, 34, 24, 32, 26, 2, 14, 38, 12, 14, 16, 16, 18, 16, 0, 20, 22, 36, 22, 22, 24], "width": 98, "height": 95}}, "Nguu Le Hoa Hair Back": {"Nguu Le Hoa Hair Back": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.83333, 0, 0.66667, 0, 0.5, 0, 0.33333, 0, 0.16667, 0, 0, 1, 0, 1, 0.16667, 1, 0.33333, 1, 0.5, 1, 0.66667, 1, 0.83333], "triangles": [0, 13, 1, 1, 13, 2, 13, 12, 2, 2, 12, 3, 12, 11, 3, 3, 11, 4, 11, 10, 4, 4, 10, 5, 10, 9, 5, 5, 9, 6, 9, 8, 6, 6, 8, 7], "vertices": [1, 65, 42.59, 31.92, 1, 1, 65, 42.59, -32.08, 1, 3, 63, 85.43, -32.08, 0.01793, 64, 42.43, -32.08, 0.40022, 65, -0.57, -32.08, 0.58185, 4, 62, 85.26, -32.08, 0.00016, 63, 42.26, -32.08, 0.6747, 64, -0.74, -32.08, 0.3237, 65, -43.74, -32.08, 0.00143, 3, 61, 85.09, -32.08, 0.00209, 62, 42.09, -32.08, 0.45641, 63, -0.91, -32.08, 0.5415, 4, 60, 84.93, -32.08, 0.00757, 61, 41.93, -32.08, 0.61149, 62, -1.07, -32.08, 0.37941, 63, -44.07, -32.08, 0.00153, 2, 60, 41.76, -32.08, 0.61568, 61, -1.24, -32.08, 0.38432, 1, 60, -1.41, -32.08, 1, 1, 60, -1.41, 31.92, 1, 3, 60, 41.76, 31.92, 0.66126, 61, -1.24, 31.92, 0.33766, 62, -44.24, 31.92, 0.00108, 3, 60, 84.93, 31.92, 0.01127, 61, 41.93, 31.92, 0.59156, 62, -1.07, 31.92, 0.39717, 4, 61, 85.09, 31.92, 0.01863, 62, 42.09, 31.92, 0.52781, 63, -0.91, 31.92, 0.44607, 64, -43.91, 31.92, 0.00749, 4, 62, 85.26, 31.92, 0.00065, 63, 42.26, 31.92, 0.40989, 64, -0.74, 31.92, 0.58441, 65, -43.74, 31.92, 0.00505, 2, 64, 42.43, 31.92, 0.4239, 65, -0.57, 31.92, 0.5761], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 0], "width": 64, "height": 259}}, "Nguu Le Hoa Head": {"Nguu Le Hoa Head": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.22189, 0, 0, 1, 0, 0.05171, 0.40356, 0.15956, 0.36884, 0.28028, 0.45227, 0.10105, 0.50789, 0.16266, 0.35749, 0.45154, 0.45873, 0.62414, 0.33584, 0.78339, 0.34888, 0.84913, 0.39091, 0.72969, 0.47134, 0.49228, 0.38294, 0.48775, 0.37732, 0.62146, 0.32399, 0.79098, 0.33478, 0.2499, 0.74759, 0.3162, 0.74672, 0.36546, 0.74238, 0.43879, 0.74064, 0.52551, 0.75397, 0.6357, 0.74745, 0.55046, 0.81931, 0.47728, 0.86715, 0.36357, 0.86686, 0.29394, 0.79758, 0.20436, 0.72336, 0.68549, 0.72481, 0.36291, 0.89042, 0.47865, 0.89042, 0.52566, 0.72433, 0.31455, 0.7237, 0.4282, 0.35716, 0.61916, 0.31591, 0.80795, 0.31526, 0.80191, 0.26766, 0.60956, 0.26303, 0.39921, 0.31054, 0.41065, 0.37696, 0.3505, 0.24082, 0.75307, 0.19009, 0.83542, 0.27526, 0.03016, 0.34473, 0.24362, 0.35562, 0.25036, 0.30043, 0.03767, 0.29093, 0.30003, 0.23415], "triangles": [43, 3, 4, 49, 2, 3, 3, 42, 49, 43, 42, 3, 39, 42, 43, 44, 38, 43, 39, 43, 38, 4, 44, 43, 47, 48, 2, 49, 47, 2, 40, 42, 39, 37, 38, 44, 36, 39, 38, 36, 38, 37, 35, 40, 39, 18, 17, 36, 37, 18, 36, 12, 11, 17, 45, 2, 48, 18, 12, 17, 46, 48, 47, 45, 48, 46, 36, 35, 39, 9, 45, 46, 9, 5, 45, 41, 40, 35, 17, 41, 35, 17, 35, 36, 42, 40, 41, 16, 41, 17, 15, 16, 17, 11, 15, 17, 13, 44, 4, 37, 44, 13, 18, 37, 13, 12, 18, 13, 6, 5, 9, 49, 7, 46, 47, 49, 46, 7, 49, 41, 49, 42, 41, 7, 6, 9, 46, 7, 9, 10, 41, 16, 10, 16, 15, 7, 41, 10, 14, 11, 12, 14, 12, 13, 8, 5, 6, 8, 6, 7, 29, 8, 7, 34, 29, 7, 10, 34, 7, 15, 14, 10, 14, 15, 11, 33, 10, 14, 34, 10, 33, 30, 33, 14, 22, 34, 33, 21, 34, 22, 20, 34, 21, 24, 33, 30, 19, 29, 34, 19, 34, 20, 23, 22, 33, 23, 33, 24, 28, 19, 20, 25, 23, 24, 21, 28, 20, 27, 28, 21, 26, 22, 23, 26, 23, 25, 32, 26, 25, 30, 32, 25, 30, 25, 24, 31, 28, 27, 27, 21, 22, 26, 27, 22, 29, 19, 28, 31, 29, 28, 31, 27, 26, 31, 26, 32, 1, 2, 45, 1, 45, 5, 1, 5, 8, 1, 8, 29, 1, 29, 31, 13, 4, 0, 30, 14, 13, 0, 30, 13, 32, 30, 0, 1, 31, 32, 0, 1, 32], "vertices": [-21.65, -3.89, -16.95, 49.9, 36.54, 45.22, 51.79, 43.89, 47.08, -9.9, 23.81, 43.54, 25.69, 37.52, 19.38, 31.53, 16.4, 41.51, 26.45, 37.29, 18.13, 22.36, 25.77, 12.34, 24.12, 3.85, 20.92, 0.56, 15.96, 7.47, 23.15, 19.71, 23.56, 19.92, 26.6, 12.41, 25.06, 3.35, -0.77, 34.94, -1.03, 31.37, -0.96, 28.7, -1.18, 24.74, -2.51, 20.15, -2.58, 14.19, -7.12, 19.21, -10.06, 23.43, -9.51, 29.55, -4.42, 32.87, 1.11, 37.25, -1.26, 11.37, -11.12, 29.72, -11.67, 23.5, -0.47, 19.97, 0.56, 31.32, 25.22, 23, 27.16, 12.48, 26.32, 2.32, 29.62, 2.36, 30.84, 12.68, 28.57, 24.28, 23.95, 24.07, 33.59, 26.48, 35.18, 4.52, 28.94, 0.6, 27.95, 44.34, 26.2, 32.92, 29.96, 32.23, 31.61, 43.61, 34.28, 29.16], "hull": 5, "edges": [0, 2, 6, 8, 0, 8, 16, 14, 10, 12, 32, 34, 34, 36, 24, 22, 22, 30, 10, 16, 12, 14, 58, 62, 62, 64, 64, 60, 66, 60, 58, 68, 68, 66, 36, 26, 32, 20, 10, 18, 18, 14, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 70, 34, 82, 82, 84, 84, 86, 86, 88, 88, 26, 90, 92, 92, 94, 94, 96, 96, 90, 2, 4, 4, 6, 4, 98, 98, 14], "width": 54, "height": 69}}, "Nguu Le Hoa Jewelry": {"Nguu Le Hoa Jewelry": {"x": 32.47, "y": 27.8, "rotation": -90, "width": 32, "height": 28}}, "Nguu Le Hoa Leg Right": {"Nguu Le Hoa Leg Right": {"x": 106.59, "y": -13.1, "width": 237, "height": 66}}, "Nguu Le Hoa Thigh Right": {"Nguu Le Hoa Thigh Right": {"type": "mesh", "uvs": [0, 0, 0.46944, 0, 0.86261, 0.09447, 1, 0.21204, 0.66355, 1, 0, 1, 0.59625, 0.15927], "triangles": [3, 4, 6, 5, 6, 4, 6, 5, 0, 3, 6, 2, 6, 1, 2, 6, 0, 1], "vertices": [1, 36, 42.94, 19.67, 1, 1, 36, 75.3, 13.96, 1, 2, 36, 98.68, -11.94, 0.5, 66, -16.45, 17.32, 0.5, 1, 66, 9.29, 29.23, 1, 1, 66, 189.53, 21.36, 1, 1, 66, 193.58, -24.91, 1, 1, 66, -0.18, 0.03, 1], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 70, "height": 227}}, "Sparkle 12": {"Sparkle": {"x": 0.5, "y": 0.5, "width": 55, "height": 55}}, "Sparkle 13": {"Sparkle": {"x": 0.5, "y": 0.5, "width": 55, "height": 55}}, "Sparkle 14": {"Sparkle": {"x": 0.5, "y": 0.5, "width": 55, "height": 55}}, "Sparkle 19": {"Sparkle": {"x": 0.5, "y": 0.5, "width": 55, "height": 55}}, "Sparkle 20": {"Sparkle": {"x": 0.5, "y": 0.5, "width": 55, "height": 55}}, "Sparkle 22": {"Sparkle": {"x": 0.5, "y": 0.5, "width": 55, "height": 55}}, "Sparkle 25": {"Sparkle": {"x": 0.5, "y": 0.5, "width": 55, "height": 55}}, "Sparkle 29": {"Sparkle": {"x": 0.5, "y": 0.5, "width": 55, "height": 55}}, "Sparkle 30": {"Sparkle": {"x": 0.5, "y": 0.5, "width": 55, "height": 55}}, "Sparkle 31": {"Sparkle": {"x": 0.5, "y": 0.5, "width": 55, "height": 55}}, "Sparkle 39": {"Sparkle": {"x": 0.5, "y": 0.5, "width": 55, "height": 55}}, "Sparkle 4": {"Sparkle": {"x": 0.5, "y": 0.5, "width": 55, "height": 55}}, "Sparkle 40": {"Sparkle": {"x": 0.5, "y": 0.5, "width": 55, "height": 55}}, "Sparkle 5": {"Sparkle": {"x": 0.5, "y": 0.5, "width": 55, "height": 55}}, "Sparkle 9": {"Sparkle": {"x": 0.5, "y": 0.5, "width": 55, "height": 55}}, "Ton Tam Nuong Arm Left": {"Ton Tam Nuong Arm Left": {"x": 17.7, "y": -6.84, "rotation": 90, "width": 37, "height": 63}}, "Ton Tam Nuong Body": {"Ton Tam Nuong Body": {"type": "mesh", "uvs": [0.36245, 0.23637, 0.35511, 0.22248, 0.21638, 0.32668, 0.02165, 0.32636, 0.27734, 0.09974, 0.32952, 0.08841, 0.44343, 0.08582, 0.46315, 0, 0.80785, 0, 0.82041, 0.13202, 0.85897, 0.15072, 0.9308, 0.17357, 1, 0.20919, 0.96148, 0.27094, 0.84841, 0.28842, 0.84463, 0.30108, 0.83067, 0.34785, 0.80188, 0.39588, 0.77399, 0.44548, 0.7584, 0.49568, 0.75591, 0.54653, 1, 0.8295, 1, 1, 0, 1, 0, 0.78869, 0.13281, 0.47797, 0.24983, 0.4239, 0.3449, 0.39656, 0.40955, 0.37543, 0.42062, 0.34636, 0.38731, 0.20018, 0.61473, 0.25668, 0.44679, 0.19273, 0.43774, 0.2339, 0.45281, 0.27003, 0.49372, 0.30178, 0.55487, 0.31958, 0.641, 0.32024, 0.70473, 0.30162, 0.74564, 0.27228, 0.74622, 0.23771, 0.72469, 0.19961, 0.68201, 0.16214, 0.64709, 0.33512, 0.72203, 0.30978, 0.75312, 0.2747, 0.54434, 0.3308, 0.47735, 0.30868, 0.78279, 0.27742, 0.77612, 0.28161, 0.73453, 0.15192, 0.4364, 0.27485, 0.42688, 0.2353, 0.55134, 0.15289], "triangles": [34, 33, 31, 35, 34, 31, 36, 35, 31, 12, 50, 11, 13, 14, 12, 1, 5, 30, 4, 5, 1, 2, 3, 4, 1, 2, 4, 7, 42, 53, 50, 8, 9, 53, 6, 7, 42, 7, 8, 50, 42, 8, 32, 6, 53, 41, 42, 50, 30, 5, 6, 30, 6, 32, 50, 10, 11, 50, 9, 10, 41, 50, 12, 32, 52, 30, 33, 52, 32, 0, 1, 30, 0, 30, 52, 40, 41, 12, 31, 53, 42, 31, 42, 41, 31, 41, 40, 32, 53, 31, 33, 32, 31, 12, 48, 40, 39, 31, 40, 45, 39, 40, 45, 40, 49, 51, 52, 33, 51, 33, 34, 48, 12, 14, 48, 49, 40, 15, 48, 14, 49, 48, 15, 38, 31, 39, 47, 34, 35, 51, 34, 47, 44, 38, 39, 44, 39, 45, 37, 31, 38, 36, 31, 37, 46, 35, 36, 47, 35, 46, 43, 37, 38, 43, 38, 44, 36, 37, 43, 46, 36, 43, 51, 29, 0, 51, 0, 52, 29, 51, 47, 16, 49, 15, 44, 45, 49, 16, 44, 49, 43, 44, 16, 29, 46, 43, 29, 47, 46, 16, 29, 43, 17, 29, 16, 28, 29, 17, 18, 28, 17, 27, 28, 18, 19, 27, 18, 26, 27, 19, 20, 26, 19, 25, 26, 20, 20, 23, 24, 20, 21, 22, 20, 24, 25, 22, 23, 20], "vertices": [1, 75, 75.37, 40.22, 1, 2, 75, 81.07, 41.75, 0.5, 76, 25.67, 20.81, 0.5, 1, 76, 85.96, 17.96, 1, 1, 76, 106.19, -17.36, 1, 2, 75, 131.52, 58.01, 0.25, 76, -9.9, -18.49, 0.75, 2, 75, 136.17, 47.1, 0.5, 76, -19.38, -11.38, 0.5, 1, 75, 137.24, 23.29, 1, 1, 75, 172.51, 19.17, 1, 1, 75, 172.51, -52.87, 1, 1, 75, 118.25, -55.5, 1, 2, 75, 110.56, -63.56, 0.6, 86, 24.05, 29.47, 0.4, 2, 75, 101.18, -78.57, 0.376, 86, 14.66, 14.45, 0.624, 1, 86, 0.01, -0.01, 1, 2, 75, 61.16, -84.98, 0.608, 86, -25.36, 8.04, 0.392, 3, 74, -53.97, 61.35, 0.00107, 75, 53.97, -61.35, 0.83093, 86, -32.55, 31.67, 0.168, 2, 74, -48.76, 60.57, 0.00573, 75, 48.76, -60.57, 0.99427, 2, 74, -29.48, 57.67, 0.05274, 75, 29.48, -57.67, 0.94726, 2, 74, -9.56, 51.67, 0.22649, 75, 9.56, -51.67, 0.77351, 2, 74, 11.16, 45.66, 0.5951, 75, -11.16, -45.66, 0.4049, 2, 74, 32.02, 41.93, 0.90494, 75, -32.03, -41.94, 0.09506, 2, 74, 52.99, 40.91, 0.99527, 75, -53, -40.92, 0.00473, 1, 74, 170.35, 89.43, 1, 1, 74, 240.41, 87.95, 1, 1, 74, 235.97, -121.01, 1, 1, 74, 149.15, -119.16, 1, 2, 74, 22.11, -88.69, 0.96914, 75, -22.12, 88.71, 0.03086, 2, 74, 0.56, -63.77, 0.8551, 75, -0.56, 63.79, 0.1449, 2, 74, -10.1, -43.76, 0.6134, 75, 10.1, 43.76, 0.3866, 3, 74, -18.38, -30.27, 0.25881, 75, 18.38, 30.27, 0.74067, 76, 74.22, 62.09, 0.00052, 2, 74, -30.18, -28.03, 0.04703, 75, 30.18, 28.03, 0.95297, 2, 75, 90.24, 35.02, 0.39102, 76, 14.36, 22.05, 0.60898, 1, 84, 0.01, -0.02, 1, 1, 75, 93.3, 22.59, 1, 2, 75, 76.38, 24.48, 0.832, 84, 9.37, 36.97, 0.168, 2, 75, 61.53, 21.33, 0.664, 84, -5.48, 33.82, 0.336, 2, 75, 48.48, 12.78, 0.752, 84, -18.53, 25.27, 0.248, 2, 75, 41.16, 0, 0.736, 84, -25.85, 12.49, 0.264, 3, 74, -40.89, 18, 0.00014, 75, 40.89, -18, 0.78586, 84, -26.12, -5.51, 0.214, 3, 74, -48.54, 31.32, 0.00144, 75, 48.54, -31.32, 0.79456, 84, -18.47, -18.83, 0.204, 2, 75, 60.6, -39.87, 0.764, 84, -6.41, -27.38, 0.236, 2, 75, 74.81, -39.99, 0.784, 84, 7.8, -27.5, 0.216, 2, 75, 90.47, -35.49, 0.896, 84, 23.46, -23, 0.104, 2, 75, 105.87, -26.57, 0.904, 84, 38.86, -14.08, 0.096, 3, 74, -34.78, 19.28, 0.00446, 75, 34.78, -19.28, 0.98754, 84, -32.23, -6.78, 0.008, 2, 74, -45.19, 34.94, 0.00587, 75, 45.19, -34.94, 0.99413, 2, 74, -59.61, 41.43, 0.00015, 75, 59.61, -41.43, 0.99985, 1, 75, 36.55, 2.2, 1, 1, 75, 45.64, 16.2, 1, 3, 74, -58.49, 47.63, 0.00039, 75, 58.49, -47.63, 0.87161, 86, -28.03, 45.39, 0.128, 2, 74, -56.77, 46.24, 0.00088, 75, 56.77, -46.24, 0.99912, 1, 75, 110.07, -37.55, 1, 2, 74, -60.09, -23.43, 0.00582, 75, 59.55, 24.76, 0.99418, 2, 74, -76.38, -25.06, 0.00015, 75, 75.8, 26.75, 0.99985, 2, 75, 109.67, 0.74, 0.957, 84, 42.66, 13.23, 0.043], "hull": 30, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 32, 56, 34, 54, 36, 50, 40, 52, 38, 60, 10, 2, 8, 2, 0, 0, 58, 60, 0, 62, 78, 62, 80, 62, 82, 62, 84, 62, 64, 62, 66, 62, 68, 62, 70, 62, 72, 62, 74, 62, 76, 86, 88, 88, 90, 90, 80, 86, 92, 92, 94, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 96, 28, 96, 80, 80, 98, 28, 30, 30, 32, 98, 30, 100, 82, 82, 80, 82, 84, 100, 24, 82, 24, 80, 24, 96, 24, 28, 24, 104, 64, 102, 104, 102, 94], "width": 209, "height": 411}}, "Ton Tam Nuong Forearm": {"Ton Tam Nuong Forearm": {"type": "mesh", "uvs": [0.331, 0.4731, 0.23217, 1, 1, 1, 1, 0.76735, 0.80288, 0.47159, 0.78776, 0.42368, 0.81029, 0.37879, 0.92795, 0.28981, 0.93922, 0.19083, 0.93828, 0.08841, 0.57108, 0, 0.26818, 0.0098, 0.13336, 0.10474, 0.11848, 0.20113, 0.21422, 0.29043, 0.30117, 0.37474, 0.33709, 0.42368, 0.56667, 0.27629, 0.59014, 0.08537, 0.56994, 0.16588], "triangles": [9, 18, 10, 18, 9, 8, 10, 12, 11, 19, 18, 8, 19, 12, 18, 10, 18, 12, 15, 14, 17, 17, 14, 19, 19, 14, 13, 13, 12, 19, 8, 7, 19, 7, 17, 19, 5, 16, 6, 6, 16, 15, 15, 17, 6, 6, 17, 7, 2, 1, 3, 4, 3, 1, 1, 0, 4, 0, 5, 4, 0, 16, 5], "vertices": [1, 77, 70.49, 12.38, 1, 1, 77, -17.03, 22.84, 1, 1, 77, -19.44, -16.25, 1, 1, 77, 19.34, -18.64, 1, 1, 77, 69.26, -11.65, 1, 2, 77, 77.29, -11.38, 0.52798, 78, -0.71, -11.38, 0.47202, 1, 78, 6.7, -12.99, 1, 2, 78, 21.16, -19.89, 0.01945, 80, -0.89, -10.33, 0.98055, 2, 80, 15.57, -11.93, 0.72881, 82, -0.52, -11.93, 0.27119, 2, 83, 0.46, -12.93, 0.608, 81, 13.36, -31.63, 0.392, 2, 83, 16.35, 4.85, 0.384, 81, 29.25, -13.85, 0.616, 3, 83, 15.67, 20.37, 0.304, 79, 48.57, 1.67, 0.22803, 81, 28.57, 1.67, 0.46797, 2, 79, 33.16, 9.51, 0.49134, 81, 13.16, 9.51, 0.50866, 2, 79, 17.14, 11.26, 0.65328, 81, -2.86, 11.26, 0.34672, 2, 78, 23.3, 16.45, 0.0561, 79, 1.96, 7.31, 0.9439, 1, 78, 8.98, 12.89, 1, 2, 77, 78.71, 11.56, 0.45787, 78, 0.71, 11.56, 0.54213, 3, 78, 24.55, -1.64, 0.30124, 80, 2.49, 7.92, 0.15386, 79, 3.21, -10.78, 0.5449, 6, 78, 56.3, -4.8, 0.11133, 80, 34.24, 4.76, 0.05686, 82, 18.15, 4.76, 3e-05, 83, 2.06, 4.76, 0.01193, 79, 34.96, -13.94, 0.20137, 81, 14.96, -13.94, 0.61848, 6, 78, 42.95, -2.94, 0.18056, 80, 20.89, 6.61, 0.09219, 82, 4.8, 6.61, 2e-05, 83, -11.3, 6.61, 0.00757, 79, 21.6, -12.08, 0.32747, 81, 1.6, -12.08, 0.3922], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 28, 30, 30, 32, 0, 32, 0, 32, 14, 34, 34, 28, 14, 16, 16, 18, 12, 30, 10, 32, 8, 0, 20, 22, 18, 36, 16, 38, 22, 24, 24, 26, 36, 24, 38, 26, 26, 28, 18, 20, 24, 20], "width": 51, "height": 167}}, "Ton Tam Nuong Hair Behind": {"Ton Tam Nuong Hair Behind": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.75, 0, 0.5, 0, 0.25, 0, 0, 1, 0, 1, 0.25, 1, 0.5, 1, 0.75], "triangles": [1, 9, 2, 0, 9, 1, 2, 8, 3, 9, 8, 2, 3, 7, 4, 8, 7, 3, 4, 6, 5, 7, 6, 4], "vertices": [1, 107, 67.9, 48.1, 1, 1, 107, 67.9, -30.9, 1, 2, 106, 67.9, -30.9, 0.45083, 107, -0.1, -30.9, 0.54917, 2, 105, 67.9, -30.9, 0.57897, 106, -0.1, -30.9, 0.42103, 2, 104, 62.59, -30.9, 0.32991, 105, -5.41, -30.9, 0.67009, 1, 104, -16.19, -30.9, 1, 1, 104, -16.19, 48.1, 1, 2, 104, 60.01, 48.1, 0.4901, 105, -7.99, 48.1, 0.5099, 3, 105, 67.9, 48.1, 0.59026, 106, -0.1, 48.1, 0.40274, 107, -68.1, 48.1, 0.007, 3, 105, 135.9, 48.1, 0.00104, 106, 67.9, 48.1, 0.40725, 107, -0.1, 48.1, 0.59171], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 0], "width": 79, "height": 272}}, "Ton Tam Nuong Hair Front A": {"Ton Tam Nuong Hair Front A": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.8, 0, 0.6, 0, 0.4, 0, 0.2, 0, 0.02003, 0.68325, 1e-05, 1, 0.2, 1, 0.4, 1, 0.6, 1, 0.8], "triangles": [5, 6, 7, 1, 2, 11, 1, 11, 0, 2, 3, 10, 2, 10, 11, 10, 3, 4, 10, 4, 9, 5, 8, 9, 4, 5, 9, 5, 7, 8], "vertices": [1, 96, 35.79, 16.53, 1, 1, 96, 35.79, -8.47, 1, 2, 95, 35.59, -8.47, 0.65388, 96, -0.41, -8.47, 0.34612, 2, 94, 35.39, -8.47, 0.58602, 95, -0.61, -8.47, 0.41398, 2, 93, 35.19, -8.47, 0.65028, 94, -0.81, -8.47, 0.34972, 2, 92, 34.99, -8.47, 0.65826, 93, -1.01, -8.47, 0.34174, 1, 88, 3.82, 34.34, 1, 1, 88, 3.33, 17.08, 1, 2, 92, 34.99, 16.53, 0.59318, 93, -1.01, 16.53, 0.40682, 2, 93, 35.19, 16.53, 0.57041, 94, -0.81, 16.53, 0.42959, 2, 94, 35.39, 16.53, 0.54805, 95, -0.61, 16.53, 0.45195, 2, 95, 35.59, 16.53, 0.52436, 96, -0.41, 16.53, 0.47564], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 0, 10, 16], "width": 25, "height": 181}}, "Ton Tam Nuong Hair Front B": {"Ton Tam Nuong Hair Front B": {"type": "mesh", "uvs": [0, 0.34176, 1, 0, 1, 0.39273, 1, 0.71765, 1, 1, 0, 1, 1e-05, 0.78688, 0, 0.56208], "triangles": [0, 1, 2, 5, 6, 4, 6, 3, 4, 7, 0, 2, 6, 7, 3, 7, 2, 3], "vertices": [1, 88, -12.33, -10.99, 1, 1, 88, 8.96, -44.99, 1, 3, 99, 13.91, 18.11, 0.86089, 100, -3.76, 18.11, 0.12579, 101, -21.42, 18.11, 0.01332, 3, 99, 35.03, 18.11, 0.1048, 100, 17.36, 18.11, 0.3385, 101, -0.3, 18.11, 0.55669, 3, 99, 53.38, 18.11, 6e-05, 100, 35.72, 18.11, 0.00137, 101, 18.05, 18.11, 0.99856, 2, 100, 35.72, -15.89, 0.12687, 101, 18.05, -15.89, 0.87313, 2, 100, 21.86, -15.89, 0.51688, 101, 4.2, -15.89, 0.48312, 3, 99, 24.92, -15.89, 0.05537, 100, 7.25, -15.89, 0.93253, 101, -10.41, -15.89, 0.0121], "hull": 8, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 0, 14], "width": 34, "height": 65}}, "Ton Tam Nuong Head": {"Ton Tam Nuong Head": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0, 0.31753, 0.52649, 0.36295, 0.56004, 0.47651, 0.56116, 0.53031, 0.52761, 0.48966, 0.48959, 0.36654, 0.49015, 0.36713, 0.47226, 0.49325, 0.47226, 0.5052, 0.77831, 0.60463, 0.71272, 0.67359, 0.71219, 0.71152, 0.75681, 0.67302, 0.83261, 0.60635, 0.83208, 0.44342, 0.76623, 0.6052, 0.69606, 0.67302, 0.69606, 0.74183, 0.7407, 0.67244, 0.85358, 0.60634, 0.85358, 0.30718, 0.43757, 0.55891, 0.4444, 0.55945, 0.40677, 0.30773, 0.39779, 0.29103, 0.35367, 0.27617, 0.47092, 0.58939, 0.47611, 0.59161, 0.36477], "triangles": [31, 28, 2, 3, 31, 2, 26, 27, 28, 31, 26, 28, 27, 29, 28, 24, 27, 26, 25, 24, 26, 29, 2, 28, 24, 29, 27, 11, 10, 24, 29, 24, 10, 25, 11, 24, 30, 26, 31, 25, 26, 30, 11, 25, 30, 8, 10, 11, 10, 4, 29, 9, 10, 8, 9, 4, 10, 7, 11, 30, 8, 11, 7, 5, 4, 9, 6, 9, 8, 6, 8, 7, 5, 9, 6, 31, 21, 30, 20, 19, 30, 7, 30, 19, 6, 7, 19, 30, 21, 20, 21, 14, 20, 19, 18, 6, 14, 13, 19, 14, 19, 20, 21, 31, 3, 15, 14, 21, 18, 5, 6, 19, 12, 18, 13, 12, 19, 17, 13, 14, 16, 17, 14, 12, 13, 17, 15, 16, 14, 22, 17, 16, 21, 22, 16, 21, 16, 15, 23, 12, 17, 23, 17, 22, 18, 12, 23, 1, 2, 29, 1, 29, 4, 18, 1, 4, 18, 4, 5, 21, 3, 0, 22, 21, 0, 23, 22, 0, 1, 18, 23, 1, 23, 0], "vertices": [-13.14, -46.97, -13.14, 40.03, 79.86, 40.03, 79.86, -46.97, 30.9, 12.4, 27.78, 8.45, 27.67, -1.43, 30.79, -6.11, 34.33, -2.57, 34.28, 8.14, 35.94, 8.09, 35.94, -2.88, 7.48, -3.92, 13.58, -12.57, 13.63, -18.57, 9.48, -21.87, 2.43, -18.52, 2.48, -12.72, 8.6, 1.45, 15.13, -12.62, 15.13, -18.52, 10.98, -24.51, 0.48, -18.47, 0.48, -12.72, 39.17, 13.3, 38.53, -8.6, 42.03, -8.64, 42.87, 13.26, 46.97, 14.71, 36.07, 16, 35.58, -11.25, 45.94, -11.44], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6, 20, 22, 22, 14, 20, 8, 18, 16, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 36, 30, 28, 28, 26, 26, 24, 24, 34, 34, 32, 32, 30, 48, 50, 50, 52, 52, 54, 54, 48, 58, 20, 22, 60, 60, 62, 62, 56, 56, 58], "width": 87, "height": 93}}, "Ton tam Nuong Leg": {"Ton tam Nuong Leg": {"x": -163.49, "y": -171.64, "rotation": -15, "width": 138, "height": 88}}}}, "animations": {"animation": {"bones": {"Dam Nguyet Anh Head": {"rotate": [{"time": 0, "angle": -10.11, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 0.1667, "angle": -14.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 0.97, "curve": [0.243, 0, 0.649, 0.6]}, {"time": 1, "angle": -10.11}]}, "Nam Balls": {"rotate": [{"time": 0, "angle": 8.83, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -10.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 8.83}]}, "Nam Chest": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1, "y": 1}]}, "Nam Head": {"rotate": [{"time": 0, "angle": 2.08, "curve": [0.372, 0.48, 0.752, 1]}, {"time": 0.2667, "angle": 5.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -1.96, "curve": [0.252, 0, 0.622, 0.48]}, {"time": 1, "angle": 2.08}]}, "Nam Hair 1": {"rotate": [{"time": 0, "angle": 3.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -6.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 3.18}]}, "Nam Hair 2": {"rotate": [{"time": 0, "angle": 1.97, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 0.1333, "angle": 3.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -6.09, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 1, "angle": 1.97}]}, "Nam Hair 3": {"rotate": [{"time": 0, "angle": -0.23, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 0.2, "angle": 3.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -6.09, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 1, "angle": -0.23}]}, "Nam Hair 4": {"rotate": [{"time": 0, "angle": -2.68, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 0.3, "angle": 3.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": -6.09, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 1, "angle": -2.68}]}, "Nam Forearm Right": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 7.06, "y": -6.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": 0}]}, "Nam Finger A 1 Right": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -9.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0}]}, "Nam Finger A 2 Right": {"rotate": [{"time": 0, "angle": -0.39, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 0.0333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": -9.23, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 1, "angle": -0.39}]}, "Nam Finger B 1 Right": {"rotate": [{"time": 0, "angle": -6.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -6.81}]}, "Nam Finger B 2 Right": {"rotate": [{"time": 0, "angle": -6.52, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 0.0333, "angle": -6.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 0, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 1, "angle": -6.52}]}, "Nam Finger B 3 Right": {"rotate": [{"time": 0, "angle": -5.16, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 0.1667, "angle": -6.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 0, "curve": [0.243, 0, 0.655, 0.63]}, {"time": 1, "angle": -5.16}]}, "Ton Tam Nuong Body": {"rotate": [{"time": 0, "angle": 4.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -1.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 4.58}]}, "Ton Tam Nuong Hair Front B 1": {"rotate": [{"time": 0, "angle": 0.86, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.2333, "angle": -1.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 3.38, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1, "angle": 0.86}]}, "Ton Tam Nuong Hair Front B 3": {"rotate": [{"time": 0, "angle": 0.86, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.2333, "angle": -1.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 3.38, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1, "angle": 0.86}]}, "Ton Tam Nuong Hair Front B 2": {"rotate": [{"time": 0, "angle": 0.86, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.2333, "angle": -1.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 3.38, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1, "angle": 0.86}]}, "Ton Tam Nuong Forearm": {"rotate": [{"time": 0, "angle": -3.69, "curve": [0.366, 0.63, 0.703, 1]}, {"time": 0.0667, "angle": -4.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 9.79, "curve": [0.244, 0, 0.7, 0.79]}, {"time": 1, "angle": -3.69}]}, "Ton Tam Nuong Hand": {"rotate": [{"time": 0, "angle": 9.62, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -18.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 9.62}]}, "Ton Tam Nuong Arm Right": {"rotate": [{"time": 0, "angle": -4.76, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.2333, "angle": -8.51, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -1.01, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1, "angle": -4.76}]}, "Nam All": {"translate": [{"time": 0, "x": 0, "y": -2.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0, "y": 0.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": -2.24}]}, "Diem Nuu Head": {"rotate": [{"time": 0, "angle": -5.75, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 0.1667, "angle": -2.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -14, "curve": [0.243, 0, 0.649, 0.6]}, {"time": 1, "angle": -5.75}]}, "Diem Nuu All": {"translate": [{"time": 0, "x": 0, "y": 0.5, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 0.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 1.76, "curve": [0.243, 0, 0.649, 0.6]}, {"time": 1, "x": 0, "y": 0.5}]}, "Nam Finger A Left": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 14.21}, {"time": 1, "angle": 0}]}, "Nguu Le Hoa All": {"translate": [{"time": 0, "x": 6.43, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": -8.53, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 6.43, "y": 0}]}, "Ton Tam Nuong Breast Left Horizontal": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0.01, "y": 6.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": 0}]}, "Ton Tam Nuong Breast Left Verticle": {"translate": [{"time": 0, "x": -4.68, "y": 0.02, "curve": [0.366, 0.63, 0.703, 1]}, {"time": 0.0667, "x": -6.03, "y": 0.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "x": 13.39, "y": 0.17, "curve": [0.244, 0, 0.7, 0.79]}, {"time": 1, "x": -4.68, "y": 0.02}]}, "Nguu Le Hoa Arm": {"rotate": [{"time": 0, "angle": -3.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 5.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -3.91}]}, "Nguu Le Hoa Body": {"rotate": [{"time": 0, "angle": -0.35, "curve": [0.366, 0.63, 0.703, 1]}, {"time": 0.0667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": -5.09, "curve": [0.244, 0, 0.7, 0.79]}, {"time": 1, "angle": -0.35}]}, "Nguu Le Hoa Breast Left Horizontal": {"translate": [{"time": 0, "x": 3.18, "y": 4.24, "curve": [0.303, 0.23, 0.652, 0.62]}, {"time": 0.1667, "x": 0.43, "y": 0.74, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.4, "x": -3.15, "y": -3.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 4.01, "y": 5.3, "curve": [0.286, 0, 0.626, 0.38]}, {"time": 1, "x": 3.18, "y": 4.24}]}, "Nguu Le Hoa Breast Left Verticle": {"translate": [{"time": 0, "x": 1.57, "y": -1.17, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.6667, "x": 5.54, "y": -4.12, "curve": [0.243, 0, 0.649, 0.6]}, {"time": 1, "x": 1.57, "y": -1.17}]}, "Nguu Le Hoa Breast Right Horizontal": {"translate": [{"time": 0, "x": 2.82, "y": 3.75, "curve": [0.303, 0.23, 0.652, 0.62]}, {"time": 0.1667, "x": 0.76, "y": 1.12, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.4, "x": -1.91, "y": -2.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 3.44, "y": 4.54, "curve": [0.286, 0, 0.626, 0.38]}, {"time": 1, "x": 2.82, "y": 3.75}]}, "Nguu Le Hoa Breast Right Vertical": {"translate": [{"time": 0, "x": 1.52, "y": -1.16, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 0.1667, "x": 0.23, "y": -0.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 4.78, "y": -3.62, "curve": [0.243, 0, 0.649, 0.6]}, {"time": 1, "x": 1.52, "y": -1.16}]}, "Ton Tam Nuong Head": {"rotate": [{"time": 0, "angle": -3.98, "curve": [0.366, 0.63, 0.703, 1]}, {"time": 0.0667, "angle": -4.83, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 8.65, "curve": [0.244, 0, 0.7, 0.79]}, {"time": 1, "angle": -3.98}]}, "Nguu Le Hoa Hair A 1": {"rotate": [{"time": 0, "angle": 2.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -8.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 2.21}]}, "Nguu Le Hoa Hair A 2": {"rotate": [{"time": 0, "angle": 2.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -8.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 2.21}]}, "Nguu Le Hoa Hair B 1": {"rotate": [{"time": 0, "angle": -2.11, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 0.1667, "angle": 0}, {"time": 0.6667, "angle": -7.43, "curve": [0.243, 0, 0.649, 0.6]}, {"time": 1, "angle": -2.11}]}, "Nguu Le Hoa hair B 2": {"rotate": [{"time": 0, "angle": -2.11, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -7.43, "curve": [0.243, 0, 0.649, 0.6]}, {"time": 1, "angle": -2.11}]}, "Diem Nuu Arm Right": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -5.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0}]}, "Diem Nuu Forearm Right": {"rotate": [{"time": 0, "angle": 0.19, "curve": [0.378, 0.52, 0.748, 1]}, {"time": 0.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 0.41, "curve": [0.248, 0, 0.628, 0.52]}, {"time": 1, "angle": 0.19}]}, "Nam Arm Left": {"rotate": [{"time": 0, "angle": 1.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 4.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 1.65}]}, "Diem Nu Body": {"translate": [{"time": 0, "x": -14.56, "y": 3, "curve": [0.36, 0.43, 0.755, 1]}, {"time": 0.2667, "x": -5.62, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": -19.33, "y": 4.59, "curve": "stepped"}, {"time": 0.8333, "x": -19.33, "y": 4.59, "curve": [0.259, 0, 0.618, 0.45]}, {"time": 1, "x": -14.56, "y": 3}]}, "Nguu Le Hoa Head": {"rotate": [{"time": 0, "angle": -5.53, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 0.3, "angle": 0}, {"time": 0.8, "angle": -8.74, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 1, "angle": -5.53}]}, "Dam Nguyet Anh Body": {"rotate": [{"time": 0, "angle": -0.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -12.17, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -0.54}]}, "Ton Tam Nuong Hair Front A 1": {"rotate": [{"time": 0, "angle": 4.91, "curve": [0.3, 0.21, 0.756, 1]}, {"time": 0.4333, "angle": -0.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "angle": 5.33, "curve": [0.297, 0, 0.634, 0.37]}, {"time": 1, "angle": 4.91}]}, "Ton Tam Nuong Hair Front A 5": {"rotate": [{"time": 0, "angle": 3.14, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 0.2, "angle": 5.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -0.63, "curve": [0.252, 0, 0.622, 0.48]}, {"time": 0.9333, "angle": 2.09, "curve": [0.334, 0.34, 0.67, 0.68]}, {"time": 1, "angle": 3.14}]}, "Ton Tam Nuong Hair Front A 4": {"rotate": [{"time": 0, "angle": 4.13, "curve": [0.38, 0.59, 0.727, 1]}, {"time": 0.1333, "angle": 5.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -0.63, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 0.9333, "angle": 3.14, "curve": [0.342, 0.36, 0.678, 0.7]}, {"time": 1, "angle": 4.13}]}, "Ton Tam Nuong Hair Front A 3": {"rotate": [{"time": 0, "angle": 4.94, "curve": [0.366, 0.63, 0.703, 1]}, {"time": 0.0667, "angle": 5.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": -0.63, "curve": [0.242, 0, 0.663, 0.65]}, {"time": 0.9333, "angle": 4.12, "curve": [0.352, 0.41, 0.688, 0.76]}, {"time": 1, "angle": 4.94}]}, "Ton Tam Nuong Hair Front A 2": {"rotate": [{"time": 0, "angle": 5.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -0.47, "curve": [0.244, 0, 0.7, 0.79]}, {"time": 0.9333, "angle": 4.92, "curve": [0.366, 0.63, 0.703, 1]}, {"time": 1, "angle": 5.33}]}, "Nguu Le Hoa Hair Back 1": {"rotate": [{"time": 0, "angle": -1.43, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 0.1667, "angle": -0.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -4.38, "curve": [0.243, 0, 0.649, 0.6]}, {"time": 1, "angle": -1.43}]}, "Nguu Le Hoa Hair Back 6": {"rotate": [{"time": 0, "angle": -3.21, "curve": [0.325, 0.31, 0.675, 0.69]}, {"time": 0.1667, "angle": -1.43, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 0.3333, "angle": -0.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -4.38, "curve": [0.265, 0, 0.618, 0.43]}, {"time": 1, "angle": -3.21}]}, "Nguu Le Hoa Hair Back 5": {"rotate": [{"time": 0, "angle": -2.86, "curve": [0.332, 0.33, 0.676, 0.7]}, {"time": 0.1333, "angle": -1.43, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 0.3, "angle": -0.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": -4.38, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 1, "angle": -2.86}]}, "Nguu Le Hoa Hair Back 4": {"rotate": [{"time": 0, "angle": -2.5, "curve": [0.336, 0.34, 0.676, 0.69]}, {"time": 0.1, "angle": -1.43, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 0.2667, "angle": -0.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -4.38, "curve": [0.252, 0, 0.622, 0.48]}, {"time": 1, "angle": -2.5}]}, "Nguu Le Hoa Hair Back 3": {"rotate": [{"time": 0, "angle": -2.13, "curve": [0.338, 0.35, 0.674, 0.69]}, {"time": 0.0667, "angle": -1.43, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 0.2333, "angle": -0.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -4.38, "curve": [0.248, 0, 0.628, 0.52]}, {"time": 1, "angle": -2.13}]}, "Nguu Le Hoa Hair Back 2": {"rotate": [{"time": 0, "angle": -1.78, "curve": [0.337, 0.35, 0.671, 0.68]}, {"time": 0.0333, "angle": -1.43, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 0.2, "angle": -0.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -4.38, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 1, "angle": -1.78}]}, "Ton Tam Nuong Finger B 1": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 8.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0}]}, "Ton Tam Nuong Finger B 2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 8.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0}]}, "Ton Tam Nuong Finger B 3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -0.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0}]}, "Ton Tam Nuong Finger A 1": {"rotate": [{"time": 0, "angle": 0.74, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 6.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0.74}]}, "Ton Tam Nuong Finger A 2": {"rotate": [{"time": 0, "angle": 0.74, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 14.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0.74}]}, "Ton Tam Nuong Breast Right": {"translate": [{"time": 0, "x": 15.95, "y": -1.37, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 0.1667, "x": 12.22, "y": -2.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 29.91, "y": -0.29, "curve": [0.243, 0, 0.649, 0.6]}, {"time": 1, "x": 15.95, "y": -1.37}]}, "Sparkle 4": {"scale": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": 0}]}, "Sparkle 5": {"scale": [{"time": 0, "x": 0.632, "y": 0.632, "curve": [0.33, 0.32, 0.666, 0.66]}, {"time": 0.0667, "x": 0.456, "y": 0.456, "curve": [0.378, 0.52, 0.748, 1]}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.8, "x": 1, "y": 1, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 1, "x": 0.632, "y": 0.632}]}, "Sparkle 9": {"scale": [{"time": 0, "x": 0.798, "y": 0.798, "curve": [0.316, 0.28, 0.66, 0.65]}, {"time": 0.1333, "x": 0.456, "y": 0.456, "curve": [0.378, 0.52, 0.748, 1]}, {"time": 0.3667, "x": 0, "y": 0}, {"time": 0.8667, "x": 1, "y": 1, "curve": [0.273, 0, 0.62, 0.41]}, {"time": 1, "x": 0.798, "y": 0.798}]}, "Sparkle 12": {"scale": [{"time": 0, "x": 0.07, "y": 0.07, "curve": [0.366, 0.63, 0.703, 1]}, {"time": 0.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "x": 1, "y": 1, "curve": [0.244, 0, 0.7, 0.79]}, {"time": 1, "x": 0.07, "y": 0.07}]}, "Sparkle 13": {"scale": [{"time": 0, "x": 0.934, "y": 0.934, "curve": [0.289, 0.18, 0.648, 0.6]}, {"time": 0.2, "x": 0.456, "y": 0.456, "curve": [0.378, 0.52, 0.748, 1]}, {"time": 0.4333, "x": 0, "y": 0}, {"time": 0.9333, "x": 1, "y": 1, "curve": [0.297, 0, 0.634, 0.37]}, {"time": 1, "x": 0.934, "y": 0.934}]}, "Sparkle 14": {"scale": [{"time": 0, "x": 0.368, "y": 0.368, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 0.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 1, "y": 1, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 1, "x": 0.368, "y": 0.368}]}, "Sparkle 19": {"scale": [{"time": 0, "x": 0.02, "y": 0.02, "curve": [0.275, 0.11, 0.634, 0.54]}, {"time": 0.2, "x": 0.456, "y": 0.456, "curve": [0.372, 0.48, 0.752, 1]}, {"time": 0.4667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 0, "y": 0, "curve": [0.313, 0, 0.648, 0.35]}, {"time": 1, "x": 0.02, "y": 0.02}]}, "Sparkle 20": {"scale": [{"time": 0, "x": 0.368, "y": 0.368, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 0.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 1, "y": 1, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 1, "x": 0.368, "y": 0.368}]}, "Sparkle 22": {"scale": [{"time": 0, "x": 0.867, "y": 0.867}, {"time": 0.0667, "x": 1, "y": 1, "curve": [0.248, 0, 0.628, 0.52]}, {"time": 0.3333, "x": 0.456, "y": 0.456, "curve": [0.378, 0.52, 0.748, 1]}, {"time": 0.5667, "x": 0, "y": 0}, {"time": 1, "x": 0.867, "y": 0.867}]}, "Sparkle 25": {"scale": [{"time": 0, "x": 0.733, "y": 0.733}, {"time": 0.1333, "x": 1, "y": 1, "curve": [0.248, 0, 0.628, 0.52]}, {"time": 0.4, "x": 0.456, "y": 0.456, "curve": [0.378, 0.52, 0.748, 1]}, {"time": 0.6333, "x": 0, "y": 0}, {"time": 1, "x": 0.733, "y": 0.733}]}, "Sparkle 30": {"scale": [{"time": 0, "x": 0.977, "y": 0.977, "curve": [0.277, 0.12, 0.754, 1]}, {"time": 0.4667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1, "y": 1, "curve": [0.313, 0, 0.648, 0.35]}, {"time": 1, "x": 0.977, "y": 0.977}]}, "Sparkle 39": {"scale": [{"time": 0, "x": 0.456, "y": 0.456, "curve": [0.378, 0.52, 0.748, 1]}, {"time": 0.2333, "x": 0, "y": 0, "curve": [0.252, 0, 0.622, 0.48]}, {"time": 0.4667, "x": 0.456, "y": 0.456, "curve": [0.372, 0.48, 0.752, 1]}, {"time": 0.7333, "x": 1, "y": 1, "curve": [0.248, 0, 0.628, 0.52]}, {"time": 1, "x": 0.456, "y": 0.456}]}, "Sparkle 40": {"scale": [{"time": 0, "x": 0.87, "y": 0.87, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 0.1, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 0, "y": 0, "curve": [0.243, 0, 0.68, 0.71]}, {"time": 1, "x": 0.87, "y": 0.87}]}, "Nam Finger B 1 Left": {"rotate": [{"time": 0, "angle": 5.38, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -1.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 5.38}]}, "Nam Finger B 2 Left": {"rotate": [{"time": 0, "angle": 5.38, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -1.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 5.38}]}, "Nam Finger B 3 Left": {"rotate": [{"time": 0, "angle": 5.38, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -20.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 5.38}]}, "Dam Nguyet Anh Hair Rotation": {"translate": [{"time": 0.5333, "x": -0.17, "y": -0.08}]}, "Dam Nguyet Anh Hair 1": {"rotate": [{"time": 0, "angle": -2.4, "curve": [0.347, 0.38, 0.683, 0.73]}, {"time": 0.0667, "angle": -3.83, "curve": [0.358, 0.47, 0.695, 0.82]}, {"time": 0.1333, "angle": -4.84, "curve": [0.352, 0.65, 0.687, 1]}, {"time": 0.1667, "angle": -5.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 4.46, "curve": [0.243, 0, 0.649, 0.6]}, {"time": 1, "angle": -2.4}]}, "Dam Nguyet Anh Hair 2": {"rotate": [{"time": 0, "angle": -0.75, "curve": [0.338, 0.35, 0.674, 0.69]}, {"time": 0.0667, "angle": -2.39, "curve": [0.347, 0.38, 0.683, 0.73]}, {"time": 0.1333, "angle": -3.81, "curve": [0.375, 0.62, 0.716, 1]}, {"time": 0.2333, "angle": -5.05, "curve": [0.244, 0, 0.7, 0.79]}, {"time": 0.6667, "angle": 3.8, "curve": [0.366, 0.63, 0.703, 1]}, {"time": 0.7333, "angle": 4.46, "curve": [0.248, 0, 0.628, 0.52]}, {"time": 1, "angle": -0.75}]}, "Dam Nguyet Anh Hair 3": {"rotate": [{"time": 0, "angle": 0.95, "curve": [0.33, 0.32, 0.666, 0.66]}, {"time": 0.0667, "angle": -0.73, "curve": [0.338, 0.35, 0.674, 0.69]}, {"time": 0.1333, "angle": -2.35, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 0.3, "angle": -5.05, "curve": [0.242, 0, 0.663, 0.65]}, {"time": 0.6667, "angle": 2.53, "curve": [0.38, 0.59, 0.727, 1]}, {"time": 0.8, "angle": 4.46, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 1, "angle": 0.95}]}, "Dam Nguyet Anh Hair 4": {"rotate": [{"time": 0, "angle": 2.55, "curve": [0.322, 0.3, 0.658, 0.64]}, {"time": 0.0667, "angle": 0.96, "curve": [0.33, 0.32, 0.666, 0.66]}, {"time": 0.1333, "angle": -0.71, "curve": [0.378, 0.52, 0.748, 1]}, {"time": 0.3667, "angle": -5.05, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 0.6667, "angle": 0.96, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 0.8667, "angle": 4.46, "curve": [0.273, 0, 0.62, 0.41]}, {"time": 1, "angle": 2.55}]}, "Diem Nuu Hand Right": {"rotate": [{"time": 0, "angle": 3.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -2.83, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 3.31}]}, "Diem Nuu Finger Right 1": {"rotate": [{"time": 0, "angle": 2.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 3.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 2.39}]}, "Diem Nuu Finger Right 2": {"rotate": [{"time": 0, "angle": 16.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 3.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 16.9}]}, "Diem Nuu Finger Right 3": {"rotate": [{"time": 0, "angle": 2.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 3.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 2.39}]}, "Dam Nguyet Anh All": {"translate": [{"time": 0, "x": -1.56, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 6.47, "y": -7.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -1.56, "y": 0}]}, "Dam Nguyet Anh Arm": {"rotate": [{"time": 0, "angle": -11.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 24.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -11.72}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0.85, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1, "y": 1}]}, "Dam Nguyet Anh Forearm": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1.12, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1, "y": 1}]}, "Dam Nguyet Anh Hand": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0.95, "y": 1.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0, "y": -10, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": 0}]}, "Dam Nguyet Anh Finger B 1": {"rotate": [{"time": 0, "angle": 13.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -2.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 13.88}]}, "Dam Nguyet Anh Breast Verticle": {"translate": [{"time": 0, "x": -0.18, "y": 0, "curve": [0.366, 0.63, 0.703, 1]}, {"time": 0.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "x": -2.52, "y": 0, "curve": [0.244, 0, 0.7, 0.79]}, {"time": 1, "x": -0.18, "y": 0}]}, "Dam Nguyet Anh Breast Horizontal": {"translate": [{"time": 0, "x": 0, "y": -4.22, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 0.1667, "x": 0, "y": 3.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": -22.89, "curve": [0.243, 0, 0.649, 0.6]}, {"time": 1, "x": 0, "y": -4.22}]}, "Nguu Le Hoa Finger B 1": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -3.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0}]}, "Nguu Le Hoa Finger B 3": {"rotate": [{"time": 0, "angle": 9.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -6.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 9.05}]}, "Nguu Le Hoa Finger B 2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -5.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0}]}, "Nguu Le Hoa Finger A 1": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 2.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0}]}, "Nguu Le Hoa Finger A 2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 8.96, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0}]}, "Nguu Le Hoa Forearm IK": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0, "y": 4.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": 0}]}, "Dam Nguyet Anh Finger B 2": {"rotate": [{"time": 0, "angle": -4.35, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.3333, "angle": 3.75, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.6667, "angle": -2.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -4.35}]}, "Dam Nguyet Anh Finger A": {"rotate": [{"time": 0, "angle": 9.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 2.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 9.77}]}, "Sparkle 31": {"scale": [{"time": 0, "x": 0.977, "y": 0.977, "curve": [0.277, 0.12, 0.754, 1]}, {"time": 0.4667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1, "y": 1, "curve": [0.313, 0, 0.648, 0.35]}, {"time": 1, "x": 0.977, "y": 0.977}]}, "Sparkle 29": {"scale": [{"time": 0, "x": 0.368, "y": 0.368, "curve": [0.381, 0.55, 0.742, 1]}, {"time": 0.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 1, "y": 1, "curve": [0.245, 0, 0.637, 0.56]}, {"time": 1, "x": 0.368, "y": 0.368}]}, "Ton Tam Nuong Arm Left": {"rotate": [{"time": 0, "angle": -4.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -4.81}]}, "Nam Hair A": {"rotate": [{"time": 0, "angle": -4.91, "curve": [0.372, 0.48, 0.752, 1]}, {"time": 0.2667, "angle": -8.62, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -1.72, "curve": [0.252, 0, 0.622, 0.48]}, {"time": 1, "angle": -4.91}]}, "Nam Hair B": {"rotate": [{"time": 0, "angle": -6.55, "curve": [0.372, 0.48, 0.752, 1]}, {"time": 0.2667, "angle": -10.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -1.72, "curve": [0.252, 0, 0.622, 0.48]}, {"time": 1, "angle": -6.55}]}, "Ton Tam Nuong Hair Behind 1": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -2.74, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0}]}, "Ton Tam Nuong Hair Behind 4": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -2.74, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0}]}, "Ton Tam Nuong Hair Behind 3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -2.74, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0}]}, "Ton Tam Nuong Hair Behind 2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -2.74, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0}]}, "Diem Nuu Arm Left": {"rotate": [{"time": 0, "angle": 3.91, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.2667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 7.83, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1, "angle": 3.91}]}, "Nam Penis IK": {"translate": [{"time": 0, "x": -1.47, "y": -0.89, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.3333, "x": -0.01, "y": -0.85, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.6667, "x": 7.06, "y": 3.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -1.47, "y": -0.89}]}, "Fire 1": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 1, "y": 0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 0.895, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 5, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 7.5, "y": 5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 5, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 7.5, "y": 5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 5, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 7.5, "y": 5, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 5, "y": 0}]}, "Fire 2": {"scale": [{"time": 0, "x": 0.895, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1, "y": 0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0.895, "y": 1}], "shear": [{"time": 0, "x": 7.5, "y": 5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 5, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 7.5, "y": 5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 5, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 7.5, "y": 5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 5, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 7.5, "y": 5}]}}, "deform": {"default": {"Dam Nguyet Anh Head": {"Dam Nguyet Anh Head": [{"time": 0, "offset": 82, "vertices": [1.44947, -0.87251, -1.01141, 1.35614, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.49157, -0.14019, -1.065, 1.05382, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.03889, -0.87646, 4.78908, -1.58919, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.51924, -0.62238, -0.78073, 1.44431], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "offset": 78, "vertices": [0.10443, 1.25913, -0.71301, -1.04337, -0.5174, 0.42712, 0.29192, -0.60403, -2.01514, 0.38525, 1.67049, -1.19095, -1.66922, 2.15284, 0.49323, -2.74643, 1.22664, 0.39497, -1.27951, 0.15302, 1.22664, 0.39497, -1.27951, 0.15302, 0, 0, 0, 0, 0, 0, 0, 0, 0.84075, 4.83866, -3.28581, -3.9996, 0, 0, 0, 0, 0, 0, 0, 0, 1.21336, 0.3151, -1.14656, 0.5114, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.12418, -0.12403, 1.90754, -0.94189, 2.75099, 1.73772, -3.15519, -0.5712, 2.11481, 2.40699, -3.03409, -1.44899, -2.29389, 0.63856, 1.67844, -1.68874, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.91687, 0.91745, -1.21268, -0.4604, 0, 0, 0, 0, 0, 0, 1.10056, 0.78791, -1.35219, 0.0587], "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "offset": 82, "vertices": [1.44947, -0.87251, -1.01141, 1.35614, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.49157, -0.14019, -1.065, 1.05382, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.03889, -0.87646, 4.78908, -1.58919, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.51924, -0.62238, -0.78073, 1.44431]}]}, "Diem Nuu Head": {"Diem Nuu Head": [{"time": 0, "offset": 24, "vertices": [-0.80911, -0.03006, -0.80911, -0.03006, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.96246, 0.06301, -0.78272, 0.1522, 0, 0, 0, 0, 0, 0, 0, 0, 0.02477, -0.09316, -0.17738, 0.28093, 0.13864, 0.50876, -0.07809, 0.64636, 0.09198, 0.29351, 0.10251, 0.06672, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.23704, 0.03666, -0.37612, -0.02665, -0.31203, 0.18373, -0.41807, 0.264, -0.2482, 0.27762, -0.11305, 0.66746, 0.13803, 1.06841, 0.27304, 0.55197, 0.27946, 0.35341, 0.27364, 0.17415, 0.25053, 0.05944, 0.11424, -0.00512, 0.5984, 0.01861, 0.56147, 0.18838, 0.13221, 0.19041, 0, 0, 0, 0, 0, 0, 0, 0, 0.77852, 0.07148, 0.01088, 0.09436, 0.02179, -0.09938, 0.74217, -0.12854], "curve": [0.382, 0.57, 0.735, 1]}, {"time": 0.1667, "offset": 40, "vertices": [0.12888, 0.00553], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "offset": 24, "vertices": [-2.85105, -0.10593, -2.85105, -0.10593, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.39143, 0.22202, -3.08332, 0.52235, 0, 0, 0, 0, 0, 0, 0, 0, 0.08729, -0.32827, -0.62504, 0.9899, 0.48852, 1.79271, -0.27515, 2.27757, 0.32412, 1.03423, 0.36123, 0.23511, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.83524, 0.12919, -1.32533, -0.09392, -1.09951, 0.6474, -1.47317, 0.93027, -0.87458, 0.97825, -0.39835, 2.35193, 0.48638, 3.76476, 0.9621, 1.94498, 0.98475, 1.2453, 0.96424, 0.61366, 0.88279, 0.20944, 0.40254, -0.01803, 2.10858, 0.06558, 1.97844, 0.66379, 0.46587, 0.67096, 0, 0, 0, 0, 0, 0, 0, 0, 2.74327, 0.25186, 0.03835, 0.33249, 0.07677, -0.35017, 2.6152, -0.45295], "curve": [0.243, 0, 0.649, 0.6]}, {"time": 1, "offset": 24, "vertices": [-0.80911, -0.03006, -0.80911, -0.03006, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.96246, 0.06301, -0.78272, 0.1522, 0, 0, 0, 0, 0, 0, 0, 0, 0.02477, -0.09316, -0.17738, 0.28093, 0.13864, 0.50876, -0.07809, 0.64636, 0.09198, 0.29351, 0.10251, 0.06672, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.23704, 0.03666, -0.37612, -0.02665, -0.31203, 0.18373, -0.41807, 0.264, -0.2482, 0.27762, -0.11305, 0.66746, 0.13803, 1.06841, 0.27304, 0.55197, 0.27946, 0.35341, 0.27364, 0.17415, 0.25053, 0.05944, 0.11424, -0.00512, 0.5984, 0.01861, 0.56147, 0.18838, 0.13221, 0.19041, 0, 0, 0, 0, 0, 0, 0, 0, 0.77852, 0.07148, 0.01088, 0.09436, 0.02179, -0.09938, 0.74217, -0.12854]}]}, "Nam Head": {"Nam Head": [{"time": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "offset": 38, "vertices": [0.43568, 0, 1.42712, -0.03073, 2.38574, -0.04609, 1.60023, -0.02458, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.46136, 0, -1.53882, 1e-05, -1.52315, 2e-05, -0.54633, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.13991, 0.15225, 0.59546, 0.49663, 0.46674, -0.04307, 2.00652, -0.5629, 0, 0, 0, 0, -0.87852, 1e-05, -1.43886, 1e-05, -1.43886, 1e-05, -0.85785, 0, 0, 0, 0, 0, 0, 0, 0.35587, -0.69378, 1.03311, -0.93496, 2.26665, -0.04105, 3.12839, 0.17335, 2.93071, 0.80453, 2.40445, 0.96234], "curve": [0.25, 0, 0.75, 1]}, {"time": 1}]}, "Nguu Le Hoa Head": {"Nguu Le Hoa Head": [{"time": 0, "offset": 10, "vertices": [-1.23138, -0.02557, -2.26631, -0.03817, 0, 0, 0, 0, 0, 0, 0, 0, -1.83837, 0.1165, -1.7061, -0.01939, 0, 0, 0, 0, -1.21924, 0.07826, 0, 0, 0, 0, 0, 0, 0.15728, -0.27117, 0.54892, 0.01809, 0.73803, 0.63114, 0.59729, 0.53648, 0.28424, 1.12871, 0.32427, 2.78163, -0.07068, 0.93147, -0.53372, 0.24434, -0.50843, 0.61985, -0.09872, -0.40886, 0, 0, 0, 0, 0, 0, 0, 0, 0.42351, -0.02179, 0.42351, -0.02178, 1.85029, -0.12873, 0.75565, -0.74337, -0.75165, -0.43388, -0.80528, -0.92909, 0.7191, -1.29841, 1.98174, -0.65237, 0, 0, 0, 0, 0, 0, 0, 0, -1.04099, -0.62036, 1.19827, -0.551, 1.23418, 0.03169, -0.99829, -0.05205], "curve": [0.363, 0.44, 0.755, 1]}, {"time": 0.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "offset": 10, "vertices": [-1.94794, -0.04045, -3.58512, -0.06038, 0, 0, 0, 0, 0, 0, 0, 0, -2.90815, 0.18429, -2.69891, -0.03067, 0, 0, 0, 0, -1.92873, 0.12379, 0, 0, 0, 0, 0, 0, 0.2488, -0.42897, 0.86835, 0.02862, 1.16751, 0.99842, 0.94486, 0.84867, 0.44964, 1.78552, 0.51297, 4.40032, -0.11181, 1.47351, -0.84431, 0.38653, -0.8043, 0.98056, -0.15617, -0.64678, 0, 0, 0, 0, 0, 0, 0, 0, 0.66995, -0.03447, 0.66996, -0.03446, 2.92701, -0.20364, 1.19537, -1.17595, -1.18905, -0.68637, -1.2739, -1.46975, 1.13757, -2.05398, 3.13495, -1.032, 0, 0, 0, 0, 0, 0, 0, 0, -1.64676, -0.98136, 1.89558, -0.87164, 1.95238, 0.05014, -1.57922, -0.08234], "curve": [0.258, 0, 0.619, 0.45]}, {"time": 1, "offset": 10, "vertices": [-1.23138, -0.02557, -2.26631, -0.03817, 0, 0, 0, 0, 0, 0, 0, 0, -1.83837, 0.1165, -1.7061, -0.01939, 0, 0, 0, 0, -1.21924, 0.07826, 0, 0, 0, 0, 0, 0, 0.15728, -0.27117, 0.54892, 0.01809, 0.73803, 0.63114, 0.59729, 0.53648, 0.28424, 1.12871, 0.32427, 2.78163, -0.07068, 0.93147, -0.53372, 0.24434, -0.50843, 0.61985, -0.09872, -0.40886, 0, 0, 0, 0, 0, 0, 0, 0, 0.42351, -0.02179, 0.42351, -0.02178, 1.85029, -0.12873, 0.75565, -0.74337, -0.75165, -0.43388, -0.80528, -0.92909, 0.7191, -1.29841, 1.98174, -0.65237, 0, 0, 0, 0, 0, 0, 0, 0, -1.04099, -0.62036, 1.19827, -0.551, 1.23418, 0.03169, -0.99829, -0.05205]}]}, "Ton Tam Nuong Head": {"Ton Tam Nuong Head": [{"time": 0, "offset": 18, "vertices": [0.10381, 0.02631], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "offset": 16, "vertices": [-1.58243, 0.07048, -1.47862, 0.09679, 0, 0, 0, 0, 0.67491, -2.86586, 0.6835, -0.03015, 0.51514, -0.54724, -0.11858, 0.59993, -0.76976, -0.49031, -0.76976, -0.49031, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.1788, 0.80934, 3.68744, 1.21286, 3.61101, 1.98883, -1.26015, 1.63008], "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "offset": 18, "vertices": [0.10381, 0.02631]}]}}}}}}