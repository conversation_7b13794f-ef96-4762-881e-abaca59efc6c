{"skeleton": {"hash": "jycKRJhKd7TNGPu6CvJtV+cUVZI", "spine": "3.7.93", "width": 462, "height": 447, "images": "", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone2", "parent": "root", "x": -53.69, "y": -11.54}, {"name": "bone3", "parent": "root", "x": -145.96, "y": -11.54}, {"name": "bone4", "parent": "root", "x": 68.37, "y": -12.71}, {"name": "bone5", "parent": "root", "x": 147.79, "y": -12.71}, {"name": "bone6", "parent": "root", "x": -0.55, "y": -0.44}, {"name": "bone7", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone8", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone9", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone10", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone11", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone12", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone13", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone14", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone15", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone16", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone17", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone18", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone19", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone20", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone21", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone22", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone23", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone24", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone25", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone26", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone27", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone28", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone29", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone30", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone31", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone32", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone33", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone34", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone35", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone36", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone37", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone38", "parent": "root", "x": -6.97, "y": -9.2}, {"name": "bone39", "parent": "root", "x": -6.97, "y": -9.2}], "slots": [{"name": "efWin@2x", "bone": "root", "attachment": "efWin@2x"}, {"name": "coin", "bone": "bone", "attachment": "coin"}, {"name": "coin2", "bone": "bone7", "attachment": "coin"}, {"name": "coin34", "bone": "bone39", "attachment": "coin"}, {"name": "coin33", "bone": "bone38", "attachment": "coin"}, {"name": "coin32", "bone": "bone37", "attachment": "coin"}, {"name": "coin31", "bone": "bone36", "attachment": "coin"}, {"name": "coin30", "bone": "bone35", "attachment": "coin"}, {"name": "coin29", "bone": "bone34", "attachment": "coin"}, {"name": "coin28", "bone": "bone33", "attachment": "coin"}, {"name": "coin27", "bone": "bone32", "attachment": "coin"}, {"name": "coin26", "bone": "bone31", "attachment": "coin"}, {"name": "coin24", "bone": "bone29", "attachment": "coin"}, {"name": "coin23", "bone": "bone28", "attachment": "coin"}, {"name": "coin22", "bone": "bone27", "attachment": "coin"}, {"name": "coin21", "bone": "bone26", "attachment": "coin"}, {"name": "coin20", "bone": "bone25", "attachment": "coin"}, {"name": "coin19", "bone": "bone24", "attachment": "coin"}, {"name": "coin18", "bone": "bone23", "attachment": "coin"}, {"name": "coin17", "bone": "bone22", "attachment": "coin"}, {"name": "coin25", "bone": "bone30", "attachment": "coin"}, {"name": "coin16", "bone": "bone21", "attachment": "coin"}, {"name": "coin15", "bone": "bone20", "attachment": "coin"}, {"name": "coin14", "bone": "bone19", "attachment": "coin"}, {"name": "coin13", "bone": "bone18", "attachment": "coin"}, {"name": "coin12", "bone": "bone17", "attachment": "coin"}, {"name": "coin11", "bone": "bone16", "attachment": "coin"}, {"name": "coin10", "bone": "bone15", "attachment": "coin"}, {"name": "efNoHu1", "bone": "root", "attachment": "efNoHu1"}, {"name": "coin9", "bone": "bone14", "attachment": "coin"}, {"name": "coin8", "bone": "bone13", "attachment": "coin"}, {"name": "coin7", "bone": "bone12", "attachment": "coin"}, {"name": "coin5", "bone": "bone10", "attachment": "coin"}, {"name": "coin4", "bone": "bone9", "attachment": "coin"}, {"name": "coin3", "bone": "bone8", "attachment": "coin"}, {"name": "efNoHu2", "bone": "root", "attachment": "efNoHu2"}, {"name": "efNoHu3", "bone": "root", "attachment": "efNoHu3"}, {"name": "efNoHu4", "bone": "root", "attachment": "efNoHu4"}, {"name": "coin6", "bone": "bone11", "attachment": "coin"}], "skins": {"default": {"coin": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin10": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin11": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin12": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin13": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin14": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin15": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin16": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin17": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin18": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin19": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin2": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin20": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin21": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin22": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin23": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin24": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin25": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin26": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin27": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin28": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin29": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin3": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin30": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin31": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin32": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin33": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin34": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin4": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin5": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin6": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin7": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin8": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "coin9": {"coin": {"type": "mesh", "uvs": [0, 0.59591, 0.10781, 0.87729, 0.33927, 1, 0.7781, 1, 1, 0.8916, 1, 0.46237, 0.92759, 0.2096, 0.62379, 0, 0.2139, 0, 0, 0.15237], "triangles": [0, 9, 8, 7, 1, 0, 5, 7, 6, 8, 7, 0, 5, 2, 7, 5, 3, 2, 2, 1, 7, 4, 3, 5], "vertices": [-45, -8.23, -35.3, -33.83, -14.47, -45, 25.03, -45, 45, -35.14, 45, 3.92, 38.48, 26.93, 11.14, 46, -25.75, 46, -45, 32.13], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 90, "height": 91}}, "efNoHu1": {"efNoHu1": {"type": "mesh", "uvs": [0, 0, 0, 1, 1, 1, 1, 0], "triangles": [1, 0, 3, 1, 3, 2], "vertices": [1, 3, -73.03, 56.86, 1, 1, 3, -73.03, -58.14, 1, 1, 3, 58.97, -58.14, 1, 1, 3, 58.97, 56.86, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 132, "height": 115}}, "efNoHu2": {"efNoHu2": {"type": "mesh", "uvs": [0, 0, 1, 0, 1, 1, 0, 1], "triangles": [3, 0, 1, 3, 1, 2], "vertices": [1, 2, -63.24, 88.46, 1, 1, 2, 53.76, 88.46, 1, 1, 2, 53.76, -58.54, 1, 1, 2, -63.24, -58.54, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 147}}, "efNoHu3": {"efNoHu3": {"type": "mesh", "uvs": [0, 1, 1, 1, 1, 0, 0, 0], "triangles": [0, 3, 2, 0, 2, 1], "vertices": [1, 5, -48.77, -57.17, 1, 1, 5, 68.23, -57.17, 1, 1, 5, 68.23, 74.83, 1, 1, 5, -48.77, 74.83, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 132}}, "efNoHu4": {"efNoHu4": {"type": "mesh", "uvs": [0, 0, 1, 0, 1, 1, 0, 1], "triangles": [3, 0, 1, 3, 1, 2], "vertices": [1, 4, -58.6, 57.51, 1, 1, 4, 60.4, 57.51, 1, 1, 4, 60.4, -57.49, 1, 1, 4, -58.6, -57.49, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 119, "height": 115}}, "efWin@2x": {"efWin@2x": {"type": "mesh", "uvs": [0, 0, 1, 0, 1, 1, 0, 1], "triangles": [3, 0, 1, 3, 1, 2], "vertices": [1, 6, -236.6, 225.34, 1, 1, 6, 225.4, 225.34, 1, 1, 6, 225.4, -221.66, 1, 1, 6, -236.6, -221.66, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 462, "height": 447}}}}, "animations": {"animation": {"slots": {"coin": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2.1667, "color": "ffffff00"}]}, "coin2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00"}]}, "coin3": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00"}]}, "coin4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00"}]}, "coin5": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00"}]}, "coin6": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1.5, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1667, "color": "ffffffff"}, {"time": 3, "color": "ffffff00"}]}, "coin7": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1.4, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff00"}]}, "coin8": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00"}]}, "coin9": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00"}]}, "coin10": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00"}]}, "coin11": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1.6, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2667, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}]}, "coin12": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00"}]}, "coin13": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00"}]}, "coin14": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00"}]}, "coin15": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 3.1667, "color": "ffffff00"}]}, "coin16": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00"}]}, "coin17": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.9333, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}]}, "coin18": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1.6, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2667, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}]}, "coin19": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00"}]}, "coin20": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}]}, "coin21": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00"}]}, "coin22": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00"}]}, "coin23": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2.1667, "color": "ffffff00"}]}, "coin24": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00"}]}, "coin25": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00"}]}, "coin26": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}]}, "coin27": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 3, "color": "ffffff00"}]}, "coin28": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1.5, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1667, "color": "ffffffff"}, {"time": 2.8333, "color": "ffffff00"}]}, "coin29": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4333, "color": "ffffffff"}, {"time": 2.8333, "color": "ffffff00"}]}, "coin30": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00"}]}, "coin31": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00"}]}, "coin32": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00"}]}, "coin33": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0333, "color": "ffffffff"}, {"time": 3, "color": "ffffff00"}]}, "coin34": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00"}]}, "efNoHu1": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff"}]}, "efNoHu2": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1333, "color": "fffffff6"}]}, "efNoHu3": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff"}]}, "efNoHu4": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff"}]}, "efWin@2x": {"attachment": [{"time": 0, "name": null}, {"time": 0.8, "name": "efWin@2x"}]}}, "bones": {"bone3": {"translate": [{"time": 1.3333, "x": 0, "y": 0}, {"time": 1.5667, "x": 0, "y": 27}, {"time": 1.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0}, {"time": 2.5667, "x": 0, "y": 27}, {"time": 2.7333, "x": 0, "y": 0}]}, "bone2": {"translate": [{"time": 1.5, "x": 0, "y": 0}, {"time": 1.6, "x": 0, "y": 8.18}, {"time": 1.6667, "x": 0, "y": 25.96}, {"time": 1.8, "x": 0, "y": 15.92}, {"time": 1.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.5, "x": 0, "y": 0}, {"time": 2.6, "x": 0, "y": 8.18}, {"time": 2.6667, "x": 0, "y": 25.96}, {"time": 2.8, "x": 0, "y": 15.92}, {"time": 2.9333, "x": 0, "y": 0}]}, "bone4": {"translate": [{"time": 1.6333, "x": 0, "y": 0}, {"time": 1.7333, "x": 0, "y": 8.18}, {"time": 1.8333, "x": 0, "y": 27.51}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6333, "x": 0, "y": 0}, {"time": 2.7333, "x": 0, "y": 8.18}, {"time": 2.8333, "x": 0, "y": 27.51}, {"time": 3, "x": 0, "y": 0}]}, "bone5": {"translate": [{"time": 1.7667, "x": 0, "y": 0}, {"time": 1.8667, "x": 0, "y": 12.26}, {"time": 2, "x": 0, "y": 28.62}, {"time": 2.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.7667, "x": 0, "y": 0}, {"time": 2.8667, "x": 0, "y": 12.26}, {"time": 3, "x": 0, "y": 28.62}, {"time": 3.1667, "x": 0, "y": 0}]}, "root": {"scale": [{"time": 0, "x": 0.234, "y": 0.234}, {"time": 0.1333, "x": 0.276, "y": 0.276}, {"time": 0.1667, "x": 0.296, "y": 0.296}, {"time": 0.2333, "x": 0.276, "y": 0.276}, {"time": 0.2667, "x": 0.296, "y": 0.296}, {"time": 0.3333, "x": 0.276, "y": 0.276}, {"time": 0.3667, "x": 0.296, "y": 0.296}, {"time": 0.4, "x": 0.276, "y": 0.276}, {"time": 0.4667, "x": 0.296, "y": 0.296}, {"time": 0.5, "x": 0.276, "y": 0.276}, {"time": 0.6667, "x": 1.519, "y": 1.519}, {"time": 0.8, "x": 0.835, "y": 0.835}, {"time": 0.9667, "x": 1.152, "y": 1.152}, {"time": 1.1, "x": 0.962, "y": 0.962}, {"time": 1.3333, "x": 1, "y": 1}]}, "bone": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 2.1667, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 2.1667, "x": 168.74, "y": 128.7}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.154, "y": 0.154, "curve": "stepped"}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 2.1667, "x": 0.331, "y": 0.331}]}, "bone7": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 2.3333, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 2.3333, "x": -15.07, "y": -125.18}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 2.3333, "x": 0.792, "y": 0.792}]}, "bone8": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 1.8333, "x": 168.74, "y": -110}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 1.8333, "x": 0.331, "y": 0.331}]}, "bone9": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 1.8333, "x": -105.11, "y": 125.22}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 1.8333, "x": 0.331, "y": 0.331}]}, "bone10": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 1.8333, "x": -155.89, "y": 35.39}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 1.8333, "x": 0.331, "y": 0.331}]}, "bone11": {"rotate": [{"time": 1.5, "angle": 0}, {"time": 1.6667, "angle": -90.14}, {"time": 1.8667, "angle": -178.43}, {"time": 2.1, "angle": 90.29}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.5, "x": 3.11, "y": 0}, {"time": 3, "x": 45.17, "y": -27.73}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.099, "y": 0.099}, {"time": 1.5, "x": 0.154, "y": 0.154}, {"time": 3, "x": 0.906, "y": 0.906}]}, "bone12": {"rotate": [{"time": 1.4, "angle": 0}, {"time": 1.5667, "angle": -90.14}, {"time": 1.7667, "angle": -178.43}, {"time": 2, "angle": 90.29}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 1.4, "x": 0, "y": 0}, {"time": 3.3333, "x": -127.24, "y": -73.55}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.154, "y": 0.154, "curve": "stepped"}, {"time": 1.4, "x": 0.154, "y": 0.154}, {"time": 3.3333, "x": 0.331, "y": 0.331}]}, "bone13": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 1.8333, "x": -124.64, "y": -153.4}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 1.8333, "x": 0.331, "y": 0.331}]}, "bone14": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 1.8333, "x": 45.92, "y": 165.15}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 1.8333, "x": 0.331, "y": 0.331}]}, "bone15": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 1.8333, "x": -153.72, "y": 92.24}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 1.8333, "x": 0.331, "y": 0.331}]}, "bone16": {"rotate": [{"time": 1.6, "angle": 0}, {"time": 1.7667, "angle": -90.14}, {"time": 1.9667, "angle": -178.43}, {"time": 2.2, "angle": 90.29}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 1.6, "x": 0, "y": 0}, {"time": 2.6667, "x": -184.53, "y": -52.72}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.154, "y": 0.154, "curve": "stepped"}, {"time": 1.6, "x": 0.154, "y": 0.154}, {"time": 2.6667, "x": 0.331, "y": 0.331}]}, "bone17": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 2.0667, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 2.0667, "x": 145.31, "y": -104.36}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 2.0667, "x": 0.651, "y": 0.651}]}, "bone18": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 2.0667, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 2.0667, "x": 250.34, "y": 102.22}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 2.0667, "x": 0.331, "y": 0.331}]}, "bone19": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 2.0667, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 2.0667, "x": 265.53, "y": -44.9}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 2.0667, "x": 0.331, "y": 0.331}]}, "bone20": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 3.1667, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 3.1667, "x": -325.15, "y": 61.86}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 3.1667, "x": 0.331, "y": 0.331}]}, "bone21": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 2.0667, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 2.0667, "x": -262.22, "y": -67.91}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 2.0667, "x": 0.331, "y": 0.331}]}, "bone22": {"rotate": [{"time": 1.2667, "angle": 0}, {"time": 1.4333, "angle": -90.14}, {"time": 1.6333, "angle": -178.43}, {"time": 1.8667, "angle": 90.29}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 1.2667, "x": 0, "y": 0}, {"time": 2.6667, "x": -107.83, "y": 210.51}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.154, "y": 0.154, "curve": "stepped"}, {"time": 1.2667, "x": 0.154, "y": 0.154}, {"time": 2.6667, "x": 0.331, "y": 0.331}]}, "bone23": {"rotate": [{"time": 1.6, "angle": 0}, {"time": 1.7667, "angle": -90.14}, {"time": 1.9667, "angle": -178.43}, {"time": 2.2, "angle": 90.29}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 1.6, "x": 0, "y": 0}, {"time": 2.6667, "x": 91.56, "y": 207.99}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.154, "y": 0.154, "curve": "stepped"}, {"time": 1.6, "x": 0.154, "y": 0.154}, {"time": 2.6667, "x": 0.733, "y": 0.733}]}, "bone24": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 2.3333, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 2.3333, "x": -164.62, "y": 81.79}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 2.3333, "x": 0.722, "y": 0.722}]}, "bone25": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 2.6667, "x": 208.93, "y": 23.74}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 2.6667, "x": 0.331, "y": 0.331}]}, "bone26": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 2.0667, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 2.0667, "x": 164.13, "y": 172.65}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 2.0667, "x": 0.331, "y": 0.331}]}, "bone27": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 2.0667, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 2.0667, "x": 53.07, "y": -156.73}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 2.0667, "x": 0.331, "y": 0.331}]}, "bone28": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 2.1667, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 2.1667, "x": -55.46, "y": -137.8}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 2.0667, "x": 0.219, "y": 0.219}, {"time": 2.1667, "x": 0.331, "y": 0.331}]}, "bone29": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 2.3333, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 2.3333, "x": -90.16, "y": 171.39}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 2.3333, "x": 0.331, "y": 0.331}]}, "bone30": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 2.0667, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 2.0667, "x": 313.68, "y": 55.92}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 2.0667, "x": 0.331, "y": 0.331}]}, "bone31": {"rotate": [{"time": 1.1667, "angle": 0}, {"time": 1.3333, "angle": -90.14}, {"time": 1.5333, "angle": -178.43}, {"time": 1.7667, "angle": 90.29}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 1.1667, "x": 0, "y": 0}, {"time": 2.6667, "x": 274.55, "y": -108.77}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.154, "y": 0.154, "curve": "stepped"}, {"time": 1.1667, "x": 0.154, "y": 0.154}, {"time": 2.6667, "x": 0.331, "y": 0.331}]}, "bone32": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 3, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 3, "x": -348.24, "y": -65.23}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 3, "x": 0.331, "y": 0.331}]}, "bone33": {"rotate": [{"time": 1.5, "angle": 0}, {"time": 1.6667, "angle": -90.14}, {"time": 1.8667, "angle": -178.43}, {"time": 2.1, "angle": 90.29}, {"time": 2.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.8333, "x": -0.79, "y": -1.04}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2.8333, "x": -238.45, "y": -140.95}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.154, "y": 0.154, "curve": "stepped"}, {"time": 1.5, "x": 0.154, "y": 0.154}, {"time": 2.8333, "x": 0.331, "y": 0.331}]}, "bone34": {"rotate": [{"time": 1.7667, "angle": 0}, {"time": 1.9333, "angle": -90.14}, {"time": 2.1333, "angle": -178.43}, {"time": 2.3667, "angle": 90.29}, {"time": 2.8333, "angle": 0}], "translate": [{"time": 1.7667, "x": 0, "y": 0}, {"time": 2.8333, "x": 258.78, "y": 56.55}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.154, "y": 0.154, "curve": "stepped"}, {"time": 1.7667, "x": 0.154, "y": 0.154}, {"time": 2.8333, "x": 0.331, "y": 0.331}]}, "bone35": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 2.0667, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 2.0667, "x": -10.66, "y": 196}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 2.0667, "x": 0.331, "y": 0.331}]}, "bone36": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 2.0667, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 2.0667, "x": -275.05, "y": 189.06}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 2.0667, "x": 0.331, "y": 0.331}]}, "bone37": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 2.0667, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 2.0667, "x": -5, "y": 116.45}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 2.0667, "x": 0.696, "y": 0.696}]}, "bone38": {"rotate": [{"time": 1.3667, "angle": 0}, {"time": 1.5333, "angle": -90.14}, {"time": 1.7333, "angle": -178.43}, {"time": 1.9667, "angle": 90.29}, {"time": 3, "angle": 0}], "translate": [{"time": 1.3667, "x": 0, "y": 0}, {"time": 3, "x": 275.19, "y": 177.7}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.154, "y": 0.154, "curve": "stepped"}, {"time": 1.3667, "x": 0.154, "y": 0.154}, {"time": 3, "x": 0.174, "y": 0.174}]}, "bone39": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.1667, "angle": -90.14}, {"time": 1.3667, "angle": -178.43}, {"time": 1.6, "angle": 90.29}, {"time": 2.0667, "angle": 0}], "translate": [{"time": 1, "x": 0, "y": 0}, {"time": 2.0667, "x": 195.05, "y": -101.83}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.154, "y": 0.154}, {"time": 2.0667, "x": 0.331, "y": 0.331}]}, "bone6": {"rotate": [{"time": 0.8, "angle": 0}, {"time": 1, "angle": -88.19}, {"time": 1.1667, "angle": 178.68}, {"time": 1.6, "angle": 0}, {"time": 2, "angle": -87.68}, {"time": 2.4667, "angle": 177.57}, {"time": 3.3333, "angle": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.8, "x": 0.318, "y": 0.318}, {"time": 1.6, "x": 0.843, "y": 0.843}, {"time": 2, "x": 1.105, "y": 1.105}, {"time": 2.4667, "x": 0.843, "y": 0.843}]}}}}}