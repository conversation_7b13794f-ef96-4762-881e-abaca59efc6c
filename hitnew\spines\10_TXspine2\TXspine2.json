{"skeleton": {"hash": "hRecTHxSZE3+mYPMTeHrSyzmxeE", "spine": "3.7.93", "width": 0, "height": 0, "images": "/Users/<USER>/Downloads/dev/images", "audio": "/Users/<USER>/Duc/Sgame_Web"}, "bones": [{"name": "root"}, {"name": "tai", "parent": "root", "length": 100, "rotation": 41.69, "x": -7.23, "y": -56.59}, {"name": "as", "parent": "root", "x": -7.23, "y": 13.38}, {"name": "star", "parent": "root", "x": -6.25, "y": 19.69}, {"name": "as copy", "parent": "root", "length": 165.42, "rotation": 59.61, "x": 61.37, "y": 135.49}, {"name": "as copy2", "parent": "root", "length": 165.42, "rotation": 59.61, "x": 61.37, "y": 135.49, "scaleX": 0.597, "scaleY": 0.881}, {"name": "as copy3", "parent": "root", "length": 165.42, "rotation": 59.61, "x": 61.37, "y": 135.49, "scaleX": 0.816, "scaleY": 0.839}, {"name": "as copy5", "parent": "root", "length": 165.42, "rotation": 59.61, "x": 61.37, "y": 135.49}, {"name": "as copy4", "parent": "root", "length": 165.42, "rotation": 59.61, "x": 61.37, "y": 135.49, "scaleX": 0.816, "scaleY": 0.839}, {"name": "as copy6", "parent": "root", "length": 165.42, "rotation": 59.61, "x": 61.37, "y": 135.49}, {"name": "as copy7", "parent": "root", "length": 165.42, "rotation": 59.61, "x": 61.37, "y": 135.49}, {"name": "tai2", "parent": "tai", "x": 31.46, "y": 63.09}, {"name": "star2", "parent": "root", "x": -6.25, "y": 19.69, "scaleX": 0.68, "scaleY": 0.68}, {"name": "star3", "parent": "root", "x": -6.25, "y": 19.69}, {"name": "star4", "parent": "root", "x": -6.25, "y": 19.69, "scaleX": 0.68, "scaleY": 0.68}, {"name": "star5", "parent": "root", "x": -6.25, "y": 19.69}, {"name": "star6", "parent": "root", "x": -6.25, "y": 19.69, "scaleX": 0.68, "scaleY": 0.68}, {"name": "star7", "parent": "root", "x": -6.25, "y": 19.69}, {"name": "star8", "parent": "root", "x": -6.25, "y": 19.69, "scaleX": 0.68, "scaleY": 0.68}, {"name": "star9", "parent": "root", "x": -6.25, "y": 19.69}, {"name": "star10", "parent": "root", "x": -6.25, "y": 19.69, "scaleX": 0.68, "scaleY": 0.68}, {"name": "star11", "parent": "root", "x": -6.25, "y": 19.69, "scaleX": 0.68, "scaleY": 0.68}, {"name": "star12", "parent": "root", "x": -6.25, "y": 19.69}, {"name": "star13", "parent": "root", "x": -6.25, "y": 19.69}, {"name": "star14", "parent": "root", "x": -6.25, "y": 19.69, "scaleX": 0.68, "scaleY": 0.68}], "slots": [{"name": "as", "bone": "as", "attachment": "as"}, {"name": "particle", "bone": "root"}, {"name": "as copy", "bone": "as copy", "attachment": "as copy"}, {"name": "as copy7", "bone": "as copy7", "attachment": "as copy"}, {"name": "as copy6", "bone": "as copy6", "attachment": "as copy"}, {"name": "as copy2", "bone": "as copy2", "attachment": "as copy"}, {"name": "as copy3", "bone": "as copy3", "attachment": "as copy"}, {"name": "as copy4", "bone": "as copy4", "attachment": "as copy"}, {"name": "as copy5", "bone": "as copy5", "attachment": "as copy"}, {"name": "star", "bone": "star", "attachment": "particle"}, {"name": "star2", "bone": "star2", "attachment": "particle"}, {"name": "star3", "bone": "star3", "attachment": "particle"}, {"name": "star4", "bone": "star4", "attachment": "particle"}, {"name": "star5", "bone": "star5", "attachment": "particle"}, {"name": "star6", "bone": "star6", "attachment": "particle"}, {"name": "star7", "bone": "star7", "attachment": "particle"}, {"name": "star8", "bone": "star8", "attachment": "particle"}, {"name": "star9", "bone": "star9", "attachment": "particle"}, {"name": "star10", "bone": "star10", "attachment": "particle"}, {"name": "star11", "bone": "star11", "attachment": "particle"}, {"name": "star12", "bone": "star12", "attachment": "particle"}, {"name": "star13", "bone": "star13", "attachment": "particle"}, {"name": "star14", "bone": "star14", "attachment": "particle"}, {"name": "tai", "bone": "tai"}, {"name": "tai2", "bone": "tai2"}, {"name": "<PERSON><PERSON>", "bone": "root"}], "skins": {"default": {"Xiu": {"xiu2": {"path": "xiu", "x": 630.51, "y": 893.81, "width": 390, "height": 242}}, "as": {"as": {"x": -4.08, "y": 3.18, "width": 573, "height": 577}}, "as copy": {"as copy": {"x": 111.98, "y": -8.97, "rotation": -59.61, "width": 155, "height": 214}}, "as copy2": {"as copy": {"x": 111.98, "y": -8.97, "rotation": -59.61, "width": 155, "height": 214}}, "as copy3": {"as copy": {"x": 111.98, "y": -8.97, "rotation": -59.61, "width": 155, "height": 214}}, "as copy4": {"as copy": {"x": 111.98, "y": -8.97, "rotation": -59.61, "width": 155, "height": 214}}, "as copy5": {"as copy": {"x": 111.98, "y": -8.97, "rotation": -59.61, "width": 155, "height": 214}}, "as copy6": {"as copy": {"x": 111.98, "y": -8.97, "rotation": -59.61, "width": 155, "height": 214}}, "as copy7": {"as copy": {"x": 111.98, "y": -8.97, "rotation": -59.61, "width": 155, "height": 214}}, "star": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "star10": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "star11": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "star12": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "star13": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "star14": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "star2": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "star3": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "star4": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "star5": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "star6": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "star7": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "star8": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "star9": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "tai": {"tai": {"x": 41.42, "y": 93.22, "rotation": -41.69, "width": 331, "height": 223}, "xiu": {"x": 64.2, "y": 78.95, "rotation": -41.69, "width": 390, "height": 242}}, "tai2": {"tai": {"x": 9.96, "y": 30.12, "rotation": -41.69, "width": 331, "height": 223}, "xiu": {"x": 32.74, "y": 15.86, "rotation": -41.69, "width": 390, "height": 242}}}}, "animations": {"tai": {"slots": {"as copy": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "color": "ffffff00"}]}, "as copy2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "color": "ffffff00"}]}, "as copy3": {"color": [{"time": 0, "color": "ffffff8d", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "color": "ffffff8d"}]}, "as copy4": {"color": [{"time": 0, "color": "ffffff8d", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "color": "ffffff8d"}]}, "as copy5": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "color": "ffffff00"}]}, "as copy6": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "color": "ffffff00"}]}, "as copy7": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "color": "ffffff00"}]}, "star": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}]}, "star2": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "star3": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "star4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}]}, "star5": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}]}, "star6": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "star7": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}]}, "star8": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}]}, "star9": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "star10": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "star11": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}]}, "star12": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}]}, "star13": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "star14": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}]}, "tai": {"attachment": [{"time": 0, "name": "tai"}]}, "tai2": {"color": [{"time": 0, "color": "ffffff56"}, {"time": 0.4, "color": "ffffff2a"}, {"time": 0.8333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "tai"}]}}, "bones": {"as copy": {"translate": [{"time": 0, "x": -18.99, "y": -32.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "x": -67.21, "y": -114.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": -18.99, "y": -32.37}], "scale": [{"time": 0, "x": 1.831, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "x": 0.621, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 1.831, "y": 1}]}, "as copy2": {"rotate": [{"time": 0, "angle": 47.89}], "translate": [{"time": 0, "x": -107.94, "y": -34.49, "curve": "stepped"}, {"time": 0.2667, "x": -107.94, "y": -34.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": -67.21, "y": -114.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -107.94, "y": -34.49}], "scale": [{"time": 0, "x": 1.132, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "x": 1.831, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 0.621, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1.132, "y": 1}]}, "as copy3": {"rotate": [{"time": 0, "angle": -43.88}], "translate": [{"time": 0, "x": -22.91, "y": -106.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "x": -67.21, "y": -114.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 21.38, "y": -97.97, "curve": "stepped"}, {"time": 0.6667, "x": 21.38, "y": -97.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -22.91, "y": -106.28}], "scale": [{"time": 0, "x": 1.226, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "x": 0.621, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 1.831, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1.831, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1.226, "y": 1}]}, "as copy4": {"rotate": [{"time": 0, "angle": -130.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -43.88}], "translate": [{"time": 0, "x": -38.63, "y": -211.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "x": -67.21, "y": -114.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 21.38, "y": -97.97, "curve": "stepped"}, {"time": 0.6667, "x": 21.38, "y": -97.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -22.91, "y": -106.28}], "scale": [{"time": 0, "x": 1.6, "y": 1.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "x": 0.621, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 1.831, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1.831, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1.226, "y": 1}]}, "as copy5": {"translate": [{"time": 0, "x": -18.99, "y": -32.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "x": -67.21, "y": -114.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": -18.99, "y": -32.37}], "scale": [{"time": 0, "x": 1.831, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "x": 0.621, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 1.831, "y": 1}]}, "as copy6": {"rotate": [{"time": 0, "angle": 107.06}], "translate": [{"time": 0, "x": -114.3, "y": -110.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "x": -126.07, "y": -109.54, "curve": "stepped"}, {"time": 0.3333, "x": -126.07, "y": -109.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": -67.21, "y": -114.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -114.3, "y": -110.55}], "scale": [{"time": 0, "x": 1.347, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "x": 1.528, "y": 1, "curve": "stepped"}, {"time": 0.3333, "x": 1.528, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 0.621, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1.347, "y": 1}]}, "as copy7": {"rotate": [{"time": 0, "angle": 161.64}], "translate": [{"time": 0, "x": -73.04, "y": -123.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "x": -96.36, "y": -157.36, "curve": "stepped"}, {"time": 0.5, "x": -96.36, "y": -157.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "x": -67.21, "y": -114.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -73.04, "y": -123.14}], "scale": [{"time": 0, "x": 0.863, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "x": 1.831, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1.831, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "x": 0.621, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 0.863, "y": 1}]}, "star": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 120}, {"time": 0.5333, "angle": -120}, {"time": 0.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.4, "x": -416.48, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "star2": {"rotate": [{"time": 0, "angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333, "angle": 0}, {"time": 0.8333, "angle": 120}], "translate": [{"time": 0, "x": -202.68, "y": 139.77}, {"time": 0.1333, "x": -304.03, "y": 209.66, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.8333, "x": -202.68, "y": 139.77}], "scale": [{"time": 0, "x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star3": {"rotate": [{"time": 0, "angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333, "angle": 0}, {"time": 0.8333, "angle": 120}], "translate": [{"time": 0, "x": -233.18, "y": -134.69}, {"time": 0.1333, "x": -349.77, "y": -202.04, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.8333, "x": -233.18, "y": -134.69}], "scale": [{"time": 0, "x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star4": {"rotate": [{"time": 0, "angle": -84}, {"time": 0.2, "angle": 0}, {"time": 0.4667, "angle": 120}, {"time": 0.7333, "angle": -120}, {"time": 0.8333, "angle": -84}], "translate": [{"time": 0, "x": -284.97, "y": -264.93, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.6, "x": -284.97, "y": -264.93}], "scale": [{"time": 0, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.2, "x": 1, "y": 1}, {"time": 0.6, "x": 0.525, "y": 0.525}]}, "star5": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 120}, {"time": 0.5333, "angle": -120}, {"time": 0.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.4, "x": -67.68, "y": -289.71, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "star6": {"rotate": [{"time": 0, "angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333, "angle": 0}, {"time": 0.8333, "angle": 120}], "translate": [{"time": 0, "x": -149.32, "y": 207.12}, {"time": 0.1333, "x": -223.97, "y": 310.68, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.8333, "x": -149.32, "y": 207.12}], "scale": [{"time": 0, "x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star7": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 120}, {"time": 0.5333, "angle": -120}, {"time": 0.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.4, "x": -90.55, "y": 318.3, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "star8": {"rotate": [{"time": 0, "angle": -84}, {"time": 0.2, "angle": 0}, {"time": 0.4667, "angle": 120}, {"time": 0.7333, "angle": -120}, {"time": 0.8333, "angle": -84}], "translate": [{"time": 0, "x": 170.57, "y": -270.65, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.6, "x": 170.57, "y": -270.65}], "scale": [{"time": 0, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.2, "x": 1, "y": 1}, {"time": 0.6, "x": 0.525, "y": 0.525}]}, "star9": {"rotate": [{"time": 0, "angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333, "angle": 0}, {"time": 0.8333, "angle": 120}], "translate": [{"time": 0, "x": 210.28, "y": -181.71}, {"time": 0.1333, "x": 315.42, "y": -272.56, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.8333, "x": 210.28, "y": -181.71}], "scale": [{"time": 0, "x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star10": {"rotate": [{"time": 0, "angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333, "angle": 0}, {"time": 0.8333, "angle": 120}], "translate": [{"time": 0, "x": 228.07, "y": 171.54}, {"time": 0.1333, "x": 342.11, "y": 257.31, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.8333, "x": 228.07, "y": 171.54}], "scale": [{"time": 0, "x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star11": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 120}, {"time": 0.5333, "angle": -120}, {"time": 0.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.4, "x": 385.95, "y": 173.45, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "star12": {"rotate": [{"time": 0, "angle": -84}, {"time": 0.2, "angle": 0}, {"time": 0.4667, "angle": 120}, {"time": 0.7333, "angle": -120}, {"time": 0.8333, "angle": -84}], "translate": [{"time": 0, "x": 178.19, "y": 352.61, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.6, "x": 178.19, "y": 352.61}], "scale": [{"time": 0, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.2, "x": 1, "y": 1}, {"time": 0.6, "x": 0.525, "y": 0.525}]}, "star13": {"rotate": [{"time": 0, "angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333, "angle": 0}, {"time": 0.8333, "angle": 120}], "translate": [{"time": 0, "x": 262.38, "y": -11.44}, {"time": 0.1333, "x": 393.57, "y": -17.15, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.8333, "x": 262.38, "y": -11.44}], "scale": [{"time": 0, "x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star14": {"rotate": [{"time": 0, "angle": -84}, {"time": 0.2, "angle": 0}, {"time": 0.4667, "angle": 120}, {"time": 0.7333, "angle": -120}, {"time": 0.8333, "angle": -84}], "translate": [{"time": 0, "x": 401.19, "y": -144.86, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.6, "x": 401.19, "y": -144.86}], "scale": [{"time": 0, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.2, "x": 1, "y": 1}, {"time": 0.6, "x": 0.525, "y": 0.525}]}, "tai": {"scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 1.087, "y": 1.087, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1, "y": 1}]}, "tai2": {"scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 1.16, "y": 1.16}, {"time": 0.8333, "x": 1.329, "y": 1.329}]}, "as": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 120}, {"time": 0.5333, "angle": -120}, {"time": 0.8333, "angle": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 0.853, "y": 0.853}, {"time": 0.8333, "x": 1, "y": 1}]}}, "drawOrder": [{"time": 0.7333, "offsets": [{"slot": "tai", "offset": 1}]}]}, "xiu": {"slots": {"as copy": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "color": "ffffff00"}]}, "as copy2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "color": "ffffff00"}]}, "as copy3": {"color": [{"time": 0, "color": "ffffff8d", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "color": "ffffff8d"}]}, "as copy4": {"color": [{"time": 0, "color": "ffffff8d", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "color": "ffffff8d"}]}, "as copy5": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "color": "ffffff00"}]}, "as copy6": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "color": "ffffff00"}]}, "as copy7": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "color": "ffffff00"}]}, "star": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}]}, "star2": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "star3": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "star4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}]}, "star5": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}]}, "star6": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "star7": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}]}, "star8": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}]}, "star9": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "star10": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "star11": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}]}, "star12": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}]}, "star13": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "star14": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}]}, "tai": {"attachment": [{"time": 0, "name": "xiu"}]}, "tai2": {"color": [{"time": 0, "color": "ffffff56"}, {"time": 0.4, "color": "ffffff2a"}, {"time": 0.8333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "xiu"}]}}, "bones": {"as copy": {"translate": [{"time": 0, "x": -18.99, "y": -32.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "x": -67.21, "y": -114.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": -18.99, "y": -32.37}], "scale": [{"time": 0, "x": 1.831, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "x": 0.621, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 1.831, "y": 1}]}, "as copy2": {"rotate": [{"time": 0, "angle": 47.89}], "translate": [{"time": 0, "x": -107.94, "y": -34.49, "curve": "stepped"}, {"time": 0.2667, "x": -107.94, "y": -34.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": -67.21, "y": -114.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -107.94, "y": -34.49}], "scale": [{"time": 0, "x": 1.132, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "x": 1.831, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 0.621, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1.132, "y": 1}]}, "as copy3": {"rotate": [{"time": 0, "angle": -43.88}], "translate": [{"time": 0, "x": -22.91, "y": -106.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "x": -67.21, "y": -114.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 21.38, "y": -97.97, "curve": "stepped"}, {"time": 0.6667, "x": 21.38, "y": -97.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -22.91, "y": -106.28}], "scale": [{"time": 0, "x": 1.226, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "x": 0.621, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 1.831, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1.831, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1.226, "y": 1}]}, "as copy4": {"rotate": [{"time": 0, "angle": -130.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -43.88}], "translate": [{"time": 0, "x": -38.63, "y": -211.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "x": -67.21, "y": -114.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 21.38, "y": -97.97, "curve": "stepped"}, {"time": 0.6667, "x": 21.38, "y": -97.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -22.91, "y": -106.28}], "scale": [{"time": 0, "x": 1.6, "y": 1.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "x": 0.621, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 1.831, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1.831, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1.226, "y": 1}]}, "as copy5": {"translate": [{"time": 0, "x": -18.99, "y": -32.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "x": -67.21, "y": -114.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": -18.99, "y": -32.37}], "scale": [{"time": 0, "x": 1.831, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "x": 0.621, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 1.831, "y": 1}]}, "as copy6": {"rotate": [{"time": 0, "angle": 107.06}], "translate": [{"time": 0, "x": -114.3, "y": -110.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "x": -126.07, "y": -109.54, "curve": "stepped"}, {"time": 0.3333, "x": -126.07, "y": -109.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": -67.21, "y": -114.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -114.3, "y": -110.55}], "scale": [{"time": 0, "x": 1.347, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "x": 1.528, "y": 1, "curve": "stepped"}, {"time": 0.3333, "x": 1.528, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 0.621, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1.347, "y": 1}]}, "as copy7": {"rotate": [{"time": 0, "angle": 161.64}], "translate": [{"time": 0, "x": -73.04, "y": -123.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "x": -96.36, "y": -157.36, "curve": "stepped"}, {"time": 0.5, "x": -96.36, "y": -157.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "x": -67.21, "y": -114.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": -73.04, "y": -123.14}], "scale": [{"time": 0, "x": 0.863, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "x": 1.831, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1.831, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "x": 0.621, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 0.863, "y": 1}]}, "star": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 120}, {"time": 0.5333, "angle": -120}, {"time": 0.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.4, "x": -416.48, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "star2": {"rotate": [{"time": 0, "angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333, "angle": 0}, {"time": 0.8333, "angle": 120}], "translate": [{"time": 0, "x": -202.68, "y": 139.77}, {"time": 0.1333, "x": -304.03, "y": 209.66, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.8333, "x": -202.68, "y": 139.77}], "scale": [{"time": 0, "x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star3": {"rotate": [{"time": 0, "angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333, "angle": 0}, {"time": 0.8333, "angle": 120}], "translate": [{"time": 0, "x": -233.18, "y": -134.69}, {"time": 0.1333, "x": -349.77, "y": -202.04, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.8333, "x": -233.18, "y": -134.69}], "scale": [{"time": 0, "x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star4": {"rotate": [{"time": 0, "angle": -84}, {"time": 0.2, "angle": 0}, {"time": 0.4667, "angle": 120}, {"time": 0.7333, "angle": -120}, {"time": 0.8333, "angle": -84}], "translate": [{"time": 0, "x": -284.97, "y": -264.93, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.6, "x": -284.97, "y": -264.93}], "scale": [{"time": 0, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.2, "x": 1, "y": 1}, {"time": 0.6, "x": 0.525, "y": 0.525}]}, "star5": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 120}, {"time": 0.5333, "angle": -120}, {"time": 0.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.4, "x": -67.68, "y": -289.71, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "star6": {"rotate": [{"time": 0, "angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333, "angle": 0}, {"time": 0.8333, "angle": 120}], "translate": [{"time": 0, "x": -149.32, "y": 207.12}, {"time": 0.1333, "x": -223.97, "y": 310.68, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.8333, "x": -149.32, "y": 207.12}], "scale": [{"time": 0, "x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star7": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 120}, {"time": 0.5333, "angle": -120}, {"time": 0.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.4, "x": -90.55, "y": 318.3, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "star8": {"rotate": [{"time": 0, "angle": -84}, {"time": 0.2, "angle": 0}, {"time": 0.4667, "angle": 120}, {"time": 0.7333, "angle": -120}, {"time": 0.8333, "angle": -84}], "translate": [{"time": 0, "x": 170.57, "y": -270.65, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.6, "x": 170.57, "y": -270.65}], "scale": [{"time": 0, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.2, "x": 1, "y": 1}, {"time": 0.6, "x": 0.525, "y": 0.525}]}, "star9": {"rotate": [{"time": 0, "angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333, "angle": 0}, {"time": 0.8333, "angle": 120}], "translate": [{"time": 0, "x": 210.28, "y": -181.71}, {"time": 0.1333, "x": 315.42, "y": -272.56, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.8333, "x": 210.28, "y": -181.71}], "scale": [{"time": 0, "x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star10": {"rotate": [{"time": 0, "angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333, "angle": 0}, {"time": 0.8333, "angle": 120}], "translate": [{"time": 0, "x": 228.07, "y": 171.54}, {"time": 0.1333, "x": 342.11, "y": 257.31, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.8333, "x": 228.07, "y": 171.54}], "scale": [{"time": 0, "x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star11": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 120}, {"time": 0.5333, "angle": -120}, {"time": 0.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.4, "x": 385.95, "y": 173.45, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "star12": {"rotate": [{"time": 0, "angle": -84}, {"time": 0.2, "angle": 0}, {"time": 0.4667, "angle": 120}, {"time": 0.7333, "angle": -120}, {"time": 0.8333, "angle": -84}], "translate": [{"time": 0, "x": 178.19, "y": 352.61, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.6, "x": 178.19, "y": 352.61}], "scale": [{"time": 0, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.2, "x": 1, "y": 1}, {"time": 0.6, "x": 0.525, "y": 0.525}]}, "star13": {"rotate": [{"time": 0, "angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333, "angle": 0}, {"time": 0.8333, "angle": 120}], "translate": [{"time": 0, "x": 262.38, "y": -11.44}, {"time": 0.1333, "x": 393.57, "y": -17.15, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.8333, "x": 262.38, "y": -11.44}], "scale": [{"time": 0, "x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star14": {"rotate": [{"time": 0, "angle": -84}, {"time": 0.2, "angle": 0}, {"time": 0.4667, "angle": 120}, {"time": 0.7333, "angle": -120}, {"time": 0.8333, "angle": -84}], "translate": [{"time": 0, "x": 401.19, "y": -144.86, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.6, "x": 401.19, "y": -144.86}], "scale": [{"time": 0, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.2, "x": 1, "y": 1}, {"time": 0.6, "x": 0.525, "y": 0.525}]}, "tai": {"scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 1.087, "y": 1.087, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1, "y": 1}]}, "tai2": {"scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 1.16, "y": 1.16}, {"time": 0.8333, "x": 1.329, "y": 1.329}]}, "as": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 120}, {"time": 0.5333, "angle": -120}, {"time": 0.8333, "angle": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 0.853, "y": 0.853}, {"time": 0.8333, "x": 1, "y": 1}]}}, "drawOrder": [{"time": 0.7333, "offsets": [{"slot": "tai", "offset": 1}]}]}}}