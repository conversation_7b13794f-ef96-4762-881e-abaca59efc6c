{"skeleton": {"hash": "l+2kTXTE8GL7ICrgBgkGjJSCTwg", "spine": "3.6.52", "width": 222, "height": 222}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root"}, {"name": "bone17", "parent": "root", "length": 110.05, "rotation": -2.07, "x": 0.2, "y": 0.27}, {"name": "bone18", "parent": "bone17", "length": 95.75, "rotation": -8.74, "x": -6.29, "y": 53.5}, {"name": "bone19", "parent": "bone17", "length": 32.95, "rotation": -14.76, "x": 48.8, "y": 3.29}], "slots": [{"name": "Icon Cong/5-<PERSON>e Dien/images/1", "bone": "bone17", "attachment": "Icon Cong/5-<PERSON>e Dien/images/1"}, {"name": "Icon Cong/5-<PERSON>e Dien/images/2", "bone": "bone19", "attachment": "Icon Cong/5-<PERSON>e Dien/images/2"}, {"name": "Icon Cong/5-<PERSON>e Dien/images/3", "bone": "bone17", "attachment": "Icon Cong/5-<PERSON>e Dien/images/3"}, {"name": "Icon Cong/5-<PERSON>e Dien/images/4", "bone": "bone17", "attachment": "Icon Cong/5-<PERSON>e Dien/images/4"}, {"name": "Icon Cong/5-<PERSON>e Dien/images/5", "bone": "bone17", "attachment": "Icon Cong/5-<PERSON>e Dien/images/5"}, {"name": "Icon Cong/5-<PERSON><PERSON> Dien/images/6", "bone": "bone18", "attachment": "Icon Cong/5-<PERSON><PERSON> Dien/images/6"}, {"name": "Icon Cong/5-<PERSON>e Dien/images/7", "bone": "bone17", "attachment": "Icon Cong/5-<PERSON>e Dien/images/7"}], "skins": {"default": {"Icon Cong/5-Xe Dien/images/1": {"Icon Cong/5-Xe Dien/images/1": {"x": -2.95, "y": -3.31, "rotation": 2.07, "width": 222, "height": 222}}, "Icon Cong/5-Xe Dien/images/2": {"Icon Cong/5-Xe Dien/images/2": {"x": 0.17, "y": -0.79, "rotation": 16.83, "width": 62, "height": 33}}, "Icon Cong/5-Xe Dien/images/3": {"Icon Cong/5-Xe Dien/images/3": {"x": 34.69, "y": -18.51, "rotation": 2.07, "width": 59, "height": 42}}, "Icon Cong/5-Xe Dien/images/4": {"Icon Cong/5-Xe Dien/images/4": {"x": 35.51, "y": -19.36, "rotation": 2.07, "width": 59, "height": 42}}, "Icon Cong/5-Xe Dien/images/5": {"Icon Cong/5-Xe Dien/images/5": {"x": 32.49, "y": 6.95, "rotation": 2.07, "width": 127, "height": 67}}, "Icon Cong/5-Xe Dien/images/6": {"Icon Cong/5-Xe Dien/images/6": {"x": 10.88, "y": -3.21, "rotation": 10.81, "width": 175, "height": 68}}, "Icon Cong/5-Xe Dien/images/7": {"Icon Cong/5-Xe Dien/images/7": {"x": -1.8, "y": 20.94, "rotation": 2.07, "width": 202, "height": 150}}}}, "animations": {"Idle": {"slots": {"Icon Cong/5-Xe Dien/images/3": {"color": [{"time": 25.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 29.0333, "color": "ffffffff"}], "attachment": [{"time": 25.0333, "name": "Icon Cong/5-<PERSON>e Dien/images/3"}, {"time": 29.0333, "name": "Icon Cong/5-<PERSON>e Dien/images/3"}]}, "Icon Cong/5-Xe Dien/images/4": {"color": [{"time": 25.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 26.6333, "color": "ffffff00"}, {"time": 26.7333, "color": "ffffffff"}, {"time": 26.8333, "color": "ffffff00"}, {"time": 26.9333, "color": "ffffffff"}, {"time": 27.0333, "color": "ffffff00"}, {"time": 27.1333, "color": "ffffffff"}, {"time": 27.2333, "color": "ffffff00"}, {"time": 27.3333, "color": "ffffffff"}, {"time": 27.4333, "color": "ffffff00"}, {"time": 27.5333, "color": "ffffffff"}, {"time": 27.6333, "color": "ffffff00"}, {"time": 27.7333, "color": "ffffffff"}, {"time": 27.8333, "color": "ffffff00"}, {"time": 27.9333, "color": "ffffffff"}, {"time": 28.0333, "color": "ffffff00"}, {"time": 28.1333, "color": "ffffffff"}, {"time": 28.2333, "color": "ffffff00"}, {"time": 28.3333, "color": "ffffffff"}, {"time": 28.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 29.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 30.6333, "color": "ffffff00"}, {"time": 30.7333, "color": "ffffffff"}, {"time": 30.8333, "color": "ffffff00"}, {"time": 30.9333, "color": "ffffffff"}, {"time": 31.0333, "color": "ffffff00"}, {"time": 31.1333, "color": "ffffffff"}, {"time": 31.2333, "color": "ffffff00"}, {"time": 31.3333, "color": "ffffffff"}, {"time": 31.4333, "color": "ffffff00"}, {"time": 31.5333, "color": "ffffffff"}, {"time": 31.6333, "color": "ffffff00"}, {"time": 31.7333, "color": "ffffffff"}, {"time": 31.8333, "color": "ffffff00"}, {"time": 31.9333, "color": "ffffffff"}, {"time": 32.0333, "color": "ffffff00"}, {"time": 32.1333, "color": "ffffffff"}, {"time": 32.2333, "color": "ffffff00"}, {"time": 32.3333, "color": "ffffffff"}, {"time": 32.4333, "color": "ffffff00"}], "attachment": [{"time": 25.0333, "name": "Icon Cong/5-<PERSON>e Dien/images/4"}, {"time": 29.0333, "name": "Icon Cong/5-<PERSON>e Dien/images/4"}]}, "Icon Cong/5-Xe Dien/images/5": {"attachment": [{"time": 0, "name": null}, {"time": 2.6, "name": null}, {"time": 2.6333, "name": "Icon Cong/5-<PERSON>e Dien/images/5"}, {"time": 2.8333, "name": null}, {"time": 8.1, "name": null}, {"time": 8.1333, "name": "Icon Cong/5-<PERSON>e Dien/images/5"}, {"time": 8.3333, "name": null}, {"time": 16, "name": null}, {"time": 16.0333, "name": "Icon Cong/5-<PERSON>e Dien/images/5"}, {"time": 16.2333, "name": null}, {"time": 25, "name": null}, {"time": 25.0333, "name": null}, {"time": 26.4333, "name": null}, {"time": 26.4667, "name": "Icon Cong/5-<PERSON>e Dien/images/5"}, {"time": 26.6333, "name": null}, {"time": 29.0333, "name": null}, {"time": 30.4333, "name": null}, {"time": 30.4667, "name": "Icon Cong/5-<PERSON>e Dien/images/5"}, {"time": 30.6333, "name": null}, {"time": 37.7333, "name": null}, {"time": 37.7667, "name": "Icon Cong/5-<PERSON>e Dien/images/5"}, {"time": 37.9667, "name": null}, {"time": 43.1667, "name": null}, {"time": 43.2, "name": "Icon Cong/5-<PERSON>e Dien/images/5"}, {"time": 43.4, "name": null}]}, "Icon Cong/5-Xe Dien/images/6": {"color": [{"time": 15.8, "color": "ffffffff", "curve": "stepped"}, {"time": 22.2667, "color": "ffffffff"}, {"time": 23.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 25, "color": "ffffff00"}, {"time": 25.0333, "color": "ffffffff"}]}, "Icon Cong/5-Xe Dien/images/7": {"attachment": [{"time": 0, "name": "Icon Cong/5-<PERSON>e Dien/images/7"}]}}, "bones": {"bone17": {"rotate": [{"time": 25.0333, "angle": 0, "curve": [0.299, 0, 0.636, 0.36]}, {"time": 25.2, "angle": -1.68, "curve": [0.312, 0.24, 0.648, 0.59]}, {"time": 25.3667, "angle": 0, "curve": "stepped"}, {"time": 26.3667, "angle": 0, "curve": "stepped"}, {"time": 26.6667, "angle": 0}, {"time": 26.7667, "angle": 0.47}, {"time": 26.9667, "angle": 0, "curve": "stepped"}, {"time": 29.0333, "angle": 0, "curve": [0.299, 0, 0.636, 0.36]}, {"time": 29.2, "angle": -1.68, "curve": [0.312, 0.24, 0.648, 0.59]}, {"time": 29.3667, "angle": 0, "curve": "stepped"}, {"time": 30.3667, "angle": 0, "curve": "stepped"}, {"time": 30.6667, "angle": 0}, {"time": 30.7667, "angle": 0.47}, {"time": 30.9667, "angle": 0, "curve": "stepped"}, {"time": 33.0333, "angle": 0}], "translate": [{"time": 25.0333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 25.2, "x": 0, "y": -2.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 25.3667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 25.5333, "x": 0, "y": -2.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 25.7, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 25.8667, "x": 0, "y": -2.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 26.0333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 26.2, "x": 0, "y": -2.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 26.3667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 26.6667, "x": 0, "y": 0}, {"time": 26.7667, "x": -0.83, "y": 0}, {"time": 26.9667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 29.0333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 29.2, "x": 0, "y": -2.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 29.3667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 29.5333, "x": 0, "y": -2.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 29.7, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 29.8667, "x": 0, "y": -2.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 30.0333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 30.2, "x": 0, "y": -2.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 30.3667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 30.6667, "x": 0, "y": 0}, {"time": 30.7667, "x": -0.83, "y": 0}, {"time": 30.9667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 33.0333, "x": 0, "y": 0}], "scale": [{"time": 25.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 26.3667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 26.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 29.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 30.3667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 30.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 33.0333, "x": 1, "y": 1}], "shear": [{"time": 25.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 25.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 26.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 26.3667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 26.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 29.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 29.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 30.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 30.3667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 30.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 33.0333, "x": 0, "y": 0}]}, "bone18": {"rotate": [{"time": 25.3667, "angle": 0}], "translate": [{"time": 25.0333, "x": -2.4, "y": 66.29}, {"time": 25.1, "x": 0.48, "y": 1.4}, {"time": 25.3667, "x": 0, "y": 0}], "scale": [{"time": 25.0333, "x": 1, "y": 0}, {"time": 25.0667, "x": 1, "y": 1.296}, {"time": 25.1667, "x": 1, "y": 0.49}, {"time": 25.2667, "x": 1, "y": 1.088}, {"time": 25.3667, "x": 1, "y": 1}], "shear": [{"time": 25.3667, "x": 0, "y": 0}]}, "bone19": {"rotate": [{"time": 26.6333, "angle": 0, "curve": "stepped"}, {"time": 29.0333, "angle": 0, "curve": "stepped"}, {"time": 30.6333, "angle": 0, "curve": "stepped"}, {"time": 33.0333, "angle": 0}], "translate": [{"time": 26.6333, "x": 0, "y": 0}, {"time": 26.7, "x": -30.46, "y": 13.5, "curve": "stepped"}, {"time": 28.7, "x": -30.46, "y": 13.5}, {"time": 29.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 30.6333, "x": 0, "y": 0}, {"time": 30.7, "x": -30.46, "y": 13.5, "curve": "stepped"}, {"time": 32.7, "x": -30.46, "y": 13.5}, {"time": 33.0333, "x": 0, "y": 0}], "scale": [{"time": 26.6333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 29.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 30.6333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 33.0333, "x": 1, "y": 1}], "shear": [{"time": 26.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 29.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 30.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 33.0333, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 43.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 43.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 43.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 43.4, "x": 0, "y": 0}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 46.3667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 46.3667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 46.3667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 46.3667, "x": 0, "y": 0}]}}}}}