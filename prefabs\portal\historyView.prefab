[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "historyView", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 6}, {"__id__": 9}, {"__id__": 12}, {"__id__": 15}, {"__id__": 18}, {"__id__": 21}, {"__id__": 24}, {"__id__": 29}, {"__id__": 34}, {"__id__": 61}, {"__id__": 63}, {"__id__": 197}, {"__id__": 258}, {"__id__": 339}, {"__id__": 451}, {"__id__": 510}], "_active": true, "_components": [{"__id__": 512}, {"__id__": 513}], "_prefab": {"__id__": 514}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1400, "height": 700}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "black", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 4}], "_prefab": {"__id__": 5}, "_opacity": 200, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 3000, "height": 3000}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a95690f4-0cfe-4b25-840f-5c84d9abeba3"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "17yzVLtrxCyb5Wh/b1PUN0", "sync": false}, {"__type__": "cc.Node", "_name": "bg_content2", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 7}], "_prefab": {"__id__": 8}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1400, "height": 640}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -15, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "265c1222-478d-44a4-949f-14afce427aff"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "a3wNW1gaRMWqrj3oUP0WBh", "sync": false}, {"__type__": "cc.Node", "_name": "bg_content", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 10}], "_prefab": {"__id__": 11}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1370, "height": 500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -59, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "45171019-059c-4edc-bca7-d38f44ac9d63"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "84U6MptIRBYYr8hXOezkKY", "sync": false}, {"__type__": "cc.Node", "_name": "789600", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": false, "_components": [{"__id__": 13}], "_prefab": {"__id__": 14}, "_opacity": 110, "_color": {"__type__": "cc.Color", "r": 177, "g": 167, "b": 167, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 811, "height": 328}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -53, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "e8323dd2-**************-3fcd58c00d53"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "23rOV8YdRI9YtMF+LRrAJ6", "sync": false}, {"__type__": "cc.Node", "_name": "bg_more_bank", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 16}], "_prefab": {"__id__": 17}, "_opacity": 229.5, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1388, "height": 511}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3, -44, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "825e810b-432c-4685-b3cf-16e905997c00"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "7fMvvm+hxD/pbGqke0CbkE", "sync": false}, {"__type__": "cc.Node", "_name": "bo79", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 19}], "_prefab": {"__id__": 20}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 460, "height": 413}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-20.136, -49.208, 0, 0, 0, 0, 1, 0.881, 0.881, 0.881]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "76df0624-de44-43b7-90c8-b215260c0584"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "8e2U8lsW5OvJCstHIi9cQA", "sync": false}, {"__type__": "cc.Node", "_name": "boderNhoCopy", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 22}], "_prefab": {"__id__": 23}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1358.5, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 187, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b24ecb99-1fbf-4bf6-a0c1-f28309731e49"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "8duN4t+2tPdohJxADRxTjn", "sync": false}, {"__type__": "cc.Node", "_name": "changeAvatar", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 25}, {"__id__": 26}], "_prefab": {"__id__": 28}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 43, "height": 43}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [616.017, 185.372, 0, 0, 0, 0, 1, 1.299, 1.299, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "eafaa913-5766-4525-baa7-6aefa9f8a4ff"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.1, "clickEvents": [{"__id__": 27}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 24}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "86d3f2OXIpB4bp+Btj8fZ1A", "handler": "refreshHistory", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "17i7sd1fJC9KspWLDYtsiw", "sync": false}, {"__type__": "cc.Node", "_name": "btnBack", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 30}, {"__id__": 31}], "_prefab": {"__id__": 33}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 131, "height": 67}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [637.723, 270.338, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "bc03dc7c-56b5-4721-9ba1-b1a78b0e904f"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.1, "clickEvents": [{"__id__": 32}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 29}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "86d3f2OXIpB4bp+Btj8fZ1A", "handler": "closeClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "d4mhfcD1NDKJwZ0NHY/dYd", "sync": false}, {"__type__": "cc.Node", "_name": "layout-func", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 35}, {"__id__": 40}, {"__id__": 45}, {"__id__": 50}, {"__id__": 55}], "_active": false, "_components": [], "_prefab": {"__id__": 60}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 519, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 212, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "btn-tab_game_off", "_objFlags": 0, "_parent": {"__id__": 34}, "_children": [], "_active": true, "_components": [{"__id__": 36}, {"__id__": 37}], "_prefab": {"__id__": 39}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 136, "height": 36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-314, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 35}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "64ff9166-fddd-408e-8d6f-41fdf2e1e790"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 35}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 38}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "86d3f2OXIpB4bp+Btj8fZ1A", "handler": "changeTabClicked", "customEventData": "GAME"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "41MQxP0ChP2LoIiL4zs5Be", "sync": false}, {"__type__": "cc.Node", "_name": "btn-tab_nap_off", "_objFlags": 0, "_parent": {"__id__": 34}, "_children": [], "_active": true, "_components": [{"__id__": 41}, {"__id__": 42}], "_prefab": {"__id__": 44}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 58, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-168, -3.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "3a154313-77d9-4ff1-9cbe-8a4dad0e4392"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 43}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "86d3f2OXIpB4bp+Btj8fZ1A", "handler": "changeTabClicked", "customEventData": "TOPUP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "45PdfBX/RGGqVr+m0ONs5V", "sync": false}, {"__type__": "cc.Node", "_name": "btn-tab_dth_off", "_objFlags": 0, "_parent": {"__id__": 34}, "_children": [], "_active": true, "_components": [{"__id__": 46}, {"__id__": 47}], "_prefab": {"__id__": 49}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 149, "height": 43}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-3.5, 3.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 45}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "2fcac292-f76c-4a66-85af-40355de6d807"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 45}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 48}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "86d3f2OXIpB4bp+Btj8fZ1A", "handler": "changeTabClicked", "customEventData": "REDEEM_REWARD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "2bdD9xJKRFw5/5d1WdkAch", "sync": false}, {"__type__": "cc.Node", "_name": "btn-tab_transfer_off", "_objFlags": 0, "_parent": {"__id__": 34}, "_children": [], "_active": true, "_components": [{"__id__": 51}, {"__id__": 52}], "_prefab": {"__id__": 54}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 101, "height": 43}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [175.5, 3.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "3b1bb216-5b14-4c78-acbf-f69317262b41"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 53}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "86d3f2OXIpB4bp+Btj8fZ1A", "handler": "changeTabClicked", "customEventData": "TRANSFER"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "f6xBEWz1lIJ4Elz/s04m8p", "sync": false}, {"__type__": "cc.Node", "_name": "btn-tab_receive_off", "_objFlags": 0, "_parent": {"__id__": 34}, "_children": [], "_active": true, "_components": [{"__id__": 56}, {"__id__": 57}], "_prefab": {"__id__": 59}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 75, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [341.5, -1, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "08bc9f93-8696-41da-bfcc-0e8fd53f93ed"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 58}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "86d3f2OXIpB4bp+Btj8fZ1A", "handler": "changeTabClicked", "customEventData": "RECEIVE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "adfbTKxXpGKobBQrMxbLAt", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "5etauCxA5LdYLlmNlj7A8D", "sync": false}, {"__type__": "cc.Node", "_name": "layout-func-active", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 62}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 917, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 212, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "75I15XAwFJobnNSD+/daGj", "sync": false}, {"__type__": "cc.Node", "_name": "gameTransactionView", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 64}, {"__id__": 94}, {"__id__": 97}, {"__id__": 140}, {"__id__": 150}, {"__id__": 161}, {"__id__": 175}, {"__id__": 182}, {"__id__": 187}, {"__id__": 192}], "_active": false, "_components": [{"__id__": 195}], "_prefab": {"__id__": 196}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1360, "height": 440}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -70, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "layout", "_objFlags": 0, "_parent": {"__id__": 63}, "_children": [{"__id__": 65}, {"__id__": 68}, {"__id__": 71}, {"__id__": 74}, {"__id__": 77}, {"__id__": 80}, {"__id__": 83}, {"__id__": 86}, {"__id__": 89}], "_active": true, "_components": [{"__id__": 92}], "_prefab": {"__id__": 93}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1400, "height": 55}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 255, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "GD", "_objFlags": 0, "_parent": {"__id__": 64}, "_children": [], "_active": true, "_components": [{"__id__": 66}], "_prefab": {"__id__": 67}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-560.875, 5.0009999999999994, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 65}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON>", "_N$string": "<PERSON><PERSON><PERSON>", "_fontSize": 24, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d8da7e3d-fc85-40bc-812a-b1ad31bef934"}, "_isSystemFontUsed": false, "_spacingX": -2, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "5dihCSVApHM4sn6cQ+8w4F", "sync": false}, {"__type__": "cc.Node", "_name": "lineNgang", "_objFlags": 0, "_parent": {"__id__": 64}, "_children": [], "_active": true, "_components": [{"__id__": 69}], "_prefab": {"__id__": 70}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 600, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-448.311, -232.393, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 68}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "fd1c87bb-ab50-43fa-8de0-de0fb444e2e7"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "66VvcYSn9NgaYeCE4JxTR8", "sync": false}, {"__type__": "cc.Node", "_name": "CongGD", "_objFlags": 0, "_parent": {"__id__": 64}, "_children": [], "_active": true, "_components": [{"__id__": 72}], "_prefab": {"__id__": 73}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 75, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-326.25, 5.0009999999999994, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 71}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Game", "_N$string": "Game", "_fontSize": 24, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d8da7e3d-fc85-40bc-812a-b1ad31bef934"}, "_isSystemFontUsed": false, "_spacingX": -2, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "d5sCetg0NFlaqLfLpXdJq5", "sync": false}, {"__type__": "cc.Node", "_name": "lineNgang", "_objFlags": 0, "_parent": {"__id__": 64}, "_children": [], "_active": true, "_components": [{"__id__": 75}], "_prefab": {"__id__": 76}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 600, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-196.87, -232.197, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "fd1c87bb-ab50-43fa-8de0-de0fb444e2e7"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "c2wYddwOJOWa7HKz6UkSow", "sync": false}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 64}, "_children": [], "_active": true, "_components": [{"__id__": 78}], "_prefab": {"__id__": 79}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 140.25, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-95.5, 5.001, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 77}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON><PERSON>", "_N$string": "<PERSON><PERSON><PERSON><PERSON>", "_fontSize": 24, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d8da7e3d-fc85-40bc-812a-b1ad31bef934"}, "_isSystemFontUsed": false, "_spacingX": -2, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "3emZA6LF1HxJY5qUmdjDr8", "sync": false}, {"__type__": "cc.Node", "_name": "lineNgang", "_objFlags": 0, "_parent": {"__id__": 64}, "_children": [], "_active": true, "_components": [{"__id__": 81}], "_prefab": {"__id__": 82}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 600, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [19.880000000000003, -232.197, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 80}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "fd1c87bb-ab50-43fa-8de0-de0fb444e2e7"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "7fp6Q2Fp5HXKeVbPDT0F1O", "sync": false}, {"__type__": "cc.Node", "_name": "so<PERSON>", "_objFlags": 0, "_parent": {"__id__": 64}, "_children": [], "_active": true, "_components": [{"__id__": 84}], "_prefab": {"__id__": 85}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [128.75, 5.0009999999999994, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 83}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Số Dư", "_N$string": "Số Dư", "_fontSize": 24, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d8da7e3d-fc85-40bc-812a-b1ad31bef934"}, "_isSystemFontUsed": false, "_spacingX": -2, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "5c5UZ73OpALIxomMf1y8Tz", "sync": false}, {"__type__": "cc.Node", "_name": "lineNgang", "_objFlags": 0, "_parent": {"__id__": 64}, "_children": [], "_active": true, "_components": [{"__id__": 87}], "_prefab": {"__id__": 88}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 600, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [250.13, -232.197, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 86}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "fd1c87bb-ab50-43fa-8de0-de0fb444e2e7"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "75dWCorjdJjriTGSYiazys", "sync": false}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 64}, "_children": [], "_active": true, "_components": [{"__id__": 90}], "_prefab": {"__id__": 91}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 167.25, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [449.75, 5.0009999999999994, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 89}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "<PERSON>", "_N$string": "<PERSON>", "_fontSize": 24, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d8da7e3d-fc85-40bc-812a-b1ad31bef934"}, "_isSystemFontUsed": false, "_spacingX": -2, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "43wsD9+P5PY7i011rpBs7L", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 64}, "_enabled": false, "_layoutSize": {"__type__": "cc.Size", "width": 947.75, "height": 55}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 50, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "97fzZSzzRDUY3qzAydPFgY", "sync": false}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "_parent": {"__id__": 63}, "_children": [], "_active": true, "_components": [{"__id__": 95}], "_prefab": {"__id__": 96}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 282, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 370, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 94}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "LỊCH SỬ CƯỢC", "_N$string": "LỊCH SỬ CƯỢC", "_fontSize": 24, "_lineHeight": 24, "_enableWrapText": true, "_N$file": {"__uuid__": "bc6e0141-f9ea-4894-a746-c3526ba32428"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "f5p+BqB59Dz7toC1XEnH3m", "sync": false}, {"__type__": "cc.Node", "_name": "temp", "_objFlags": 0, "_parent": {"__id__": 63}, "_children": [{"__id__": 98}, {"__id__": 123}], "_active": true, "_components": [], "_prefab": {"__id__": 139}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 97}, "_children": [{"__id__": 99}, {"__id__": 105}, {"__id__": 108}, {"__id__": 111}, {"__id__": 114}, {"__id__": 117}], "_active": true, "_components": [{"__id__": 120}, {"__id__": 121}], "_prefab": {"__id__": 122}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1360, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "lbTime", "_objFlags": 0, "_parent": {"__id__": 98}, "_children": [{"__id__": 100}], "_active": true, "_components": [{"__id__": 103}], "_prefab": {"__id__": 104}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 47.459999999999994}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-568, -12, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "lbTime", "_objFlags": 0, "_parent": {"__id__": 99}, "_children": [], "_active": true, "_components": [{"__id__": 101}], "_prefab": {"__id__": 102}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 156, "b": 6, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 34.806, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 100}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "#2562562", "_N$string": "#2562562", "_fontSize": 21, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "********-d011-4b44-9a5f-19ff3d4c844a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "6b5mJwFP5IBY8qH9zDGm8v", "sync": false}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 99}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "2019-12-12 12:12:12", "_N$string": "2019-12-12 12:12:12", "_fontSize": 21, "_lineHeight": 21, "_enableWrapText": false, "_N$file": {"__uuid__": "********-d011-4b44-9a5f-19ff3d4c844a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 3, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "301/GYpy9J7YNUTmXwJdrS", "sync": false}, {"__type__": "cc.Node", "_name": "lbGame", "_objFlags": 0, "_parent": {"__id__": 98}, "_children": [], "_active": true, "_components": [{"__id__": 106}], "_prefab": {"__id__": 107}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 156, "b": 6, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-331, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 105}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON> v<PERSON>", "_N$string": "<PERSON><PERSON><PERSON> v<PERSON>", "_fontSize": 24, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "********-d011-4b44-9a5f-19ff3d4c844a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "93MY/DMsNC7rbp6AkjfI0J", "sync": false}, {"__type__": "cc.Node", "_name": "lbValue", "_objFlags": 0, "_parent": {"__id__": 98}, "_children": [], "_active": true, "_components": [{"__id__": 109}], "_prefab": {"__id__": 110}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 254, "b": 114, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 110.5, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-98, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 108}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "1.000.000", "_N$string": "1.000.000", "_fontSize": 24, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "********-d011-4b44-9a5f-19ff3d4c844a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "8875YXKLlJPbuTbAoE7LsO", "sync": false}, {"__type__": "cc.Node", "_name": "lbBalance", "_objFlags": 0, "_parent": {"__id__": 98}, "_children": [], "_active": true, "_components": [{"__id__": 112}], "_prefab": {"__id__": 113}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 156, "b": 6, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 138.04, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [128, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 111}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "100.000.000", "_N$string": "100.000.000", "_fontSize": 24, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "********-d011-4b44-9a5f-19ff3d4c844a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "a5NUs56ehBOrbe8jrlzvwk", "sync": false}, {"__type__": "cc.Node", "_name": "lbDesc", "_objFlags": 0, "_parent": {"__id__": 98}, "_children": [], "_active": true, "_components": [{"__id__": 115}], "_prefab": {"__id__": 116}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 420, "height": 66}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [458, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "<PERSON><PERSON>", "_N$string": "<PERSON><PERSON>", "_fontSize": 24, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "********-d011-4b44-9a5f-19ff3d4c844a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "5eoUcq9itJwI/DVAeDYfAb", "sync": false}, {"__type__": "cc.Node", "_name": "lineNgang", "_objFlags": 0, "_parent": {"__id__": 98}, "_children": [], "_active": true, "_components": [{"__id__": 118}], "_prefab": {"__id__": 119}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1400, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -39.69, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 117}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "fd1c87bb-ab50-43fa-8de0-de0fb444e2e7"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "dbbtdGeBVKE79AnQlGDHC9", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 98}, "_enabled": false, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f735d3ac-adee-49f5-8abc-3353221f9488"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "7f5c9xt4eNIZaPd4mql6jym", "_name": "", "_objFlags": 0, "node": {"__id__": 98}, "_enabled": true, "lbPhien": {"__id__": 101}, "lbTime": {"__id__": 103}, "lbGame": {"__id__": 106}, "lbValue": {"__id__": 109}, "lbBalance": {"__id__": 112}, "lbDesc": {"__id__": 115}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "9490pXugRNvZniXaAu5G7j", "sync": false}, {"__type__": "cc.Node", "_name": "toggleValue", "_objFlags": 0, "_parent": {"__id__": 97}, "_children": [{"__id__": 124}, {"__id__": 127}, {"__id__": 133}], "_active": false, "_components": [{"__id__": 136}, {"__id__": 137}], "_prefab": {"__id__": 138}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 211, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-524.79, 277.406, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "lbBackground", "_objFlags": 0, "_parent": {"__id__": 123}, "_children": [], "_active": true, "_components": [{"__id__": 125}], "_prefab": {"__id__": 126}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 118.25, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 124}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "2019-03-31", "_N$string": "2019-03-31", "_fontSize": 22, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "49LDcDV+xG+69g24Ls6iC5", "sync": false}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 0, "_parent": {"__id__": 123}, "_children": [{"__id__": 128}], "_active": true, "_components": [{"__id__": 131}], "_prefab": {"__id__": 132}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "lbActive ", "_objFlags": 0, "_parent": {"__id__": 127}, "_children": [], "_active": true, "_components": [{"__id__": 129}], "_prefab": {"__id__": 130}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 118.25, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 128}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "2019-03-31", "_N$string": "2019-03-31", "_fontSize": 22, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "78TWS7rLtBgJ501qjSfzSg", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 127}, "_enabled": false, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "1a32fc76-f0bd-4f66-980f-56929c0ca0b3"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "f0GOzS2IlN6JYqu0pkDNXS", "sync": false}, {"__type__": "cc.Node", "_name": "line", "_objFlags": 0, "_parent": {"__id__": 123}, "_children": [], "_active": true, "_components": [{"__id__": 134}], "_prefab": {"__id__": 135}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1, "height": 170}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -25, 0, 0, 0, 0.7071067811865475, 0.7071067811865477, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 89.99999999999999}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 133}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "e32df1c9-9634-40bc-a127-0ce2d079d13e"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "bc82g6BH5Cqb+OsNqIipRX", "sync": false}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 123}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 0.9, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 123}, "_N$isChecked": true, "toggleGroup": null, "checkMark": {"__id__": 131}, "checkEvents": [], "_id": ""}, {"__type__": "009fbMoBi9ETp6La36vgCd0", "_name": "", "_objFlags": 0, "node": {"__id__": 123}, "_enabled": true, "lbActive": {"__id__": 129}, "lbDeActive": {"__id__": 125}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "dbh/d5y25GHrxG2u/kN24f", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "c8DL8a1iVEH5oxqTi73sxc", "sync": false}, {"__type__": "cc.Node", "_name": "scrollview", "_objFlags": 0, "_parent": {"__id__": 63}, "_children": [{"__id__": 141}], "_active": true, "_components": [{"__id__": 147}, {"__id__": 148}], "_prefab": {"__id__": 149}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1360, "height": 450}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 1, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 140}, "_children": [{"__id__": 142}], "_active": true, "_components": [{"__id__": 145}], "_prefab": {"__id__": 146}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1360, "height": 450}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 141}, "_children": [], "_active": true, "_components": [{"__id__": 143}], "_prefab": {"__id__": 144}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1360, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 231.168, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 142}, "_enabled": false, "_layoutSize": {"__type__": "cc.Size", "width": 1190, "height": 75}, "_resize": 1, "_N$layoutType": 2, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 10, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "35u7sZAYhGm55Eh0kARcxl", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 141}, "_enabled": false, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "62Nh83pA5Kk7/ZVAFnpjYv", "sync": false}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 140}, "_enabled": false, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": true, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 142}, "content": {"__id__": 142}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": null, "_id": ""}, {"__type__": "1213ceBxYpKEJ/NnfjMnJzw", "_name": "", "_objFlags": 0, "node": {"__id__": 140}, "_enabled": true, "itemTemplate": {"__id__": 98}, "scrollView": {"__id__": 147}, "spawnCount": 20, "spacing": 10, "bufferZone": 800, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "dbBtWtr8NLxqRhzWXrhR6C", "sync": false}, {"__type__": "cc.Node", "_name": "btnChooseDate", "_objFlags": 0, "_parent": {"__id__": 63}, "_children": [{"__id__": 151}, {"__id__": 154}], "_active": true, "_components": [{"__id__": 157}, {"__id__": 158}], "_prefab": {"__id__": 160}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 41}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-532.824, 324, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "arrow", "_objFlags": 0, "_parent": {"__id__": 150}, "_children": [], "_active": true, "_components": [{"__id__": 152}], "_prefab": {"__id__": 153}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 27, "height": 18}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [66, -2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 151}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "10MQLsd/ZFi6tpnWABeuOb", "sync": false}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "_parent": {"__id__": 150}, "_children": [], "_active": true, "_components": [{"__id__": 155}], "_prefab": {"__id__": 156}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 107.5, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-57.106, 3.09, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 154}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "2019-03-31", "_N$string": "2019-03-31", "_fontSize": 20, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "d8EgyvW9RHnrUY4ppJEZuV", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 150}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6ed8269f-d650-4ce1-8d41-5e89bfbf5b4f"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 150}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 159}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 63}, "component": "", "_componentId": "6c363L3D1REzIySqNKRAKFN", "handler": "openMenuDateClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "2173QtNYhPq6rRIv40vDQx", "sync": false}, {"__type__": "cc.Node", "_name": "layout-chooseValue", "_objFlags": 0, "_parent": {"__id__": 63}, "_children": [{"__id__": 162}, {"__id__": 167}], "_active": false, "_components": [{"__id__": 173}], "_prefab": {"__id__": 174}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 178}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-526, 303, 0, 0, 0, 0, 1, 1, 0, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "btnCancel", "_objFlags": 0, "_parent": {"__id__": 161}, "_children": [], "_active": true, "_components": [{"__id__": 163}, {"__id__": 164}], "_prefab": {"__id__": 166}, "_opacity": 1, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 3000, "height": 3000}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [417, -169, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 162}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 162}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 165}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 162}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 63}, "component": "", "_componentId": "6c363L3D1REzIySqNKRAKFN", "handler": "hideMenuDateClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "93ULfFkphCY4Fy94lkzoNR", "sync": false}, {"__type__": "cc.Node", "_name": "toggleC<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 161}, "_children": [], "_active": true, "_components": [{"__id__": 168}, {"__id__": 169}, {"__id__": 170}, {"__id__": 171}], "_prefab": {"__id__": 172}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.ToggleContainer", "_name": "", "_objFlags": 0, "node": {"__id__": 167}, "_enabled": true, "allowSwitchOff": false, "checkEvents": [], "_id": ""}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 167}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 192, "height": 75}, "_resize": 1, "_N$layoutType": 2, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 10, "_N$paddingBottom": 15, "_N$spacingX": 0, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 167}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8510e1b5-843b-4e2b-a9a6-67fb048a7827"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "2c7bckOOQxGQrWlb0YyK0E5", "_name": "", "_objFlags": 0, "node": {"__id__": 167}, "_enabled": true, "chooseValue": {"__id__": 123}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "8bFB6juTNDZJ605W6jgBCK", "sync": false}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 161}, "_enabled": true, "_defaultClip": {"__uuid__": "076a4bd9-d8f6-4045-9d32-016637297327"}, "_clips": [{"__uuid__": "17f44f6f-8baa-4e02-b69c-d330b48eaca1"}, {"__uuid__": "076a4bd9-d8f6-4045-9d32-016637297327"}], "playOnLoad": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "5avLEQaf1HirNvzmwkPMkC", "sync": false}, {"__type__": "cc.Node", "_name": "button", "_objFlags": 0, "_parent": {"__id__": 63}, "_children": [{"__id__": 176}], "_active": true, "_components": [{"__id__": 179}, {"__id__": 180}], "_prefab": {"__id__": 181}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2335, 304, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 175}, "_children": [], "_active": true, "_components": [{"__id__": 177}], "_prefab": {"__id__": 178}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 176}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "button", "_N$string": "button", "_fontSize": 20, "_lineHeight": 40, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "87W/Eu8nxKgaSLLsXfgPaK", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 175}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 175}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 175}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "16LTBkzuRGZYKuk5XPnFPC", "sync": false}, {"__type__": "cc.Node", "_name": "btnNext", "_objFlags": 0, "_parent": {"__id__": 63}, "_children": [], "_active": true, "_components": [{"__id__": 183}, {"__id__": 184}], "_prefab": {"__id__": 186}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 23, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [684.291, -2.65, 0, 0, 0, 0, 1, 2, 2, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 182}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "78b32c40-4f5a-4f63-bb31-216bbf2c4a25"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 182}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 185}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 182}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 63}, "component": "", "_componentId": "6c363L3D1REzIySqNKRAKFN", "handler": "pageNextClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "360Qs3mklO6JDc74nuNyED", "sync": false}, {"__type__": "cc.Node", "_name": "btnPrev", "_objFlags": 0, "_parent": {"__id__": 63}, "_children": [], "_active": true, "_components": [{"__id__": 188}, {"__id__": 189}], "_prefab": {"__id__": 191}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 23, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-687.988, -2.65, 0, 0, 0, 0, 1, 2, 2, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 187}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ff9b21df-4874-4266-be9c-ab954937a2cd"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 187}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 190}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 187}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 63}, "component": "", "_componentId": "6c363L3D1REzIySqNKRAKFN", "handler": "pagePreviousClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "95ELHjIqZOtILdBdN+molt", "sync": false}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "_parent": {"__id__": 63}, "_children": [], "_active": true, "_components": [{"__id__": 193}], "_prefab": {"__id__": 194}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 98.5, "height": 30.24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [482.125, 318.201, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 192}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Trang: 1", "_N$string": "Trang: 1", "_fontSize": 24, "_lineHeight": 24, "_enableWrapText": true, "_N$file": {"__uuid__": "3208deca-08ff-4361-b0c2-5bc37b3d2919"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "bec27ZcfNDfLPUJ24uFhYW", "sync": false}, {"__type__": "6c363L3D1REzIySqNKRAKFN", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": true, "gameTransactionListView": {"__id__": 148}, "toggleChooseValue": {"__id__": 171}, "lbDateSelected": {"__id__": 155}, "animationMenuDate": {"__id__": 173}, "nodePageNext": {"__id__": 182}, "nodePagePrevious": {"__id__": 187}, "nodeLBPage": {"__id__": 193}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "97gLeVQ6VB06t1M3aRowkn", "sync": false}, {"__type__": "cc.Node", "_name": "l<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 198}, {"__id__": 201}, {"__id__": 225}, {"__id__": 246}], "_active": false, "_components": [{"__id__": 256}], "_prefab": {"__id__": 257}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1240, "height": 510}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -71, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "_parent": {"__id__": 197}, "_children": [], "_active": true, "_components": [{"__id__": 199}], "_prefab": {"__id__": 200}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 344.25, "height": 18}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 370, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 198}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "LỊCH SỬ NẠP THẺ", "_N$string": "LỊCH SỬ NẠP THẺ", "_fontSize": 24, "_lineHeight": 24, "_enableWrapText": true, "_N$file": {"__uuid__": "bc6e0141-f9ea-4894-a746-c3526ba32428"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "55ab1bZp9LYJmRea2rD1VK", "sync": false}, {"__type__": "cc.Node", "_name": "layout", "_objFlags": 0, "_parent": {"__id__": 197}, "_children": [{"__id__": 202}, {"__id__": 205}, {"__id__": 208}, {"__id__": 214}, {"__id__": 220}], "_active": true, "_components": [{"__id__": 223}], "_prefab": {"__id__": 224}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 947.75, "height": 55}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 258, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "GD", "_objFlags": 0, "_parent": {"__id__": 201}, "_children": [], "_active": true, "_components": [{"__id__": 203}], "_prefab": {"__id__": 204}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 122.75, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-522.875, 5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 202}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Thời Gian", "_N$string": "Thời Gian", "_fontSize": 24, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d8da7e3d-fc85-40bc-812a-b1ad31bef934"}, "_isSystemFontUsed": false, "_spacingX": -2, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "efquePOAtP1IvdGGH/aXpd", "sync": false}, {"__type__": "cc.Node", "_name": "lineNgang", "_objFlags": 0, "_parent": {"__id__": 201}, "_children": [], "_active": true, "_components": [{"__id__": 206}], "_prefab": {"__id__": 207}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 600, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-357.311, -232.393, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 205}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "fd1c87bb-ab50-43fa-8de0-de0fb444e2e7"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "e5pZ3P2UNOnJZSrDDmtsgP", "sync": false}, {"__type__": "cc.Node", "_name": "CongGD", "_objFlags": 0, "_parent": {"__id__": 201}, "_children": [{"__id__": 209}], "_active": true, "_components": [{"__id__": 212}], "_prefab": {"__id__": 213}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 181.5, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-130.25, 5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "lineNgang", "_objFlags": 0, "_parent": {"__id__": 208}, "_children": [], "_active": true, "_components": [{"__id__": 210}], "_prefab": {"__id__": 211}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 600, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [231.38, -237.198, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 209}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "fd1c87bb-ab50-43fa-8de0-de0fb444e2e7"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "39kBjOO8NN7IMh4WpuBJlt", "sync": false}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 208}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Thông Tin Nạp", "_N$string": "Thông Tin Nạp", "_fontSize": 24, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d8da7e3d-fc85-40bc-812a-b1ad31bef934"}, "_isSystemFontUsed": false, "_spacingX": -2, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "64cqPk9GJJ9JqI8KdNshbl", "sync": false}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 201}, "_children": [{"__id__": 215}], "_active": true, "_components": [{"__id__": 218}], "_prefab": {"__id__": 219}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 84.75, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [211.5, 5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "lineNgang", "_objFlags": 0, "_parent": {"__id__": 214}, "_children": [], "_active": true, "_components": [{"__id__": 216}], "_prefab": {"__id__": 217}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 600, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [143.38, -237.198, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 215}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "fd1c87bb-ab50-43fa-8de0-de0fb444e2e7"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "12VENGZNVCRKfG15IQ6+tJ", "sync": false}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 214}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Giá <PERSON>", "_N$string": "Giá <PERSON>", "_fontSize": 24, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d8da7e3d-fc85-40bc-812a-b1ad31bef934"}, "_isSystemFontUsed": false, "_spacingX": -2, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "64p2ulyeFPqoWMnbdfrYqc", "sync": false}, {"__type__": "cc.Node", "_name": "so<PERSON>", "_objFlags": 0, "_parent": {"__id__": 201}, "_children": [], "_active": true, "_components": [{"__id__": 221}], "_prefab": {"__id__": 222}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 135.75, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [500.728, 5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 220}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Trạng <PERSON>", "_N$string": "Trạng <PERSON>", "_fontSize": 24, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d8da7e3d-fc85-40bc-812a-b1ad31bef934"}, "_isSystemFontUsed": false, "_spacingX": -2, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "14GOB+PqRHhq2V+Ojeo/Mg", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 201}, "_enabled": false, "_layoutSize": {"__type__": "cc.Size", "width": 947.75, "height": 55}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 50, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "e1mpT12XdIR5bRHO7nDRdo", "sync": false}, {"__type__": "cc.Node", "_name": "temp", "_objFlags": 0, "_parent": {"__id__": 197}, "_children": [{"__id__": 226}], "_active": true, "_components": [], "_prefab": {"__id__": 245}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 225}, "_children": [{"__id__": 227}, {"__id__": 230}, {"__id__": 233}, {"__id__": 236}, {"__id__": 239}], "_active": true, "_components": [{"__id__": 242}, {"__id__": 243}], "_prefab": {"__id__": 244}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1360, "height": 66}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "lbTime", "_objFlags": 0, "_parent": {"__id__": 226}, "_children": [], "_active": true, "_components": [{"__id__": 228}], "_prefab": {"__id__": 229}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 260, "height": 55}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-524.344, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 227}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "2019-12-12 12:12:12", "_N$string": "2019-12-12 12:12:12", "_fontSize": 19, "_lineHeight": 19, "_enableWrapText": false, "_N$file": {"__uuid__": "********-d011-4b44-9a5f-19ff3d4c844a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "4dwoMGvJZLVIpdrVqh4Lnl", "sync": false}, {"__type__": "cc.Node", "_name": "lbInfo", "_objFlags": 0, "_parent": {"__id__": 226}, "_children": [], "_active": true, "_components": [{"__id__": 231}], "_prefab": {"__id__": 232}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 460, "height": 55}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-136.893, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 230}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Thông Tin Nạp\nsjafsjfkdksaljasasd asd asd ", "_N$string": "Thông Tin Nạp\nsjafsjfkdksaljasasd asd asd ", "_fontSize": 19, "_lineHeight": 24, "_enableWrapText": false, "_N$file": {"__uuid__": "********-d011-4b44-9a5f-19ff3d4c844a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "4bcX3bpjhCgr82rPCQ922b", "sync": false}, {"__type__": "cc.Node", "_name": "lbValue", "_objFlags": 0, "_parent": {"__id__": 226}, "_children": [], "_active": true, "_components": [{"__id__": 234}], "_prefab": {"__id__": 235}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 254, "b": 114, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 23.94}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [217.954, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 233}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "1.000.000", "_N$string": "1.000.000", "_fontSize": 19, "_lineHeight": 19, "_enableWrapText": false, "_N$file": {"__uuid__": "********-d011-4b44-9a5f-19ff3d4c844a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "2bKQE9udtHFpEQI6REJWU0", "sync": false}, {"__type__": "cc.Node", "_name": "lbState", "_objFlags": 0, "_parent": {"__id__": 226}, "_children": [], "_active": true, "_components": [{"__id__": 237}], "_prefab": {"__id__": 238}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 55}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [512.344, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 236}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Trạng <PERSON>", "_N$string": "Trạng <PERSON>", "_fontSize": 19, "_lineHeight": 19, "_enableWrapText": false, "_N$file": {"__uuid__": "********-d011-4b44-9a5f-19ff3d4c844a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "c3NipYcZdHlaW3CAJZiSvu", "sync": false}, {"__type__": "cc.Node", "_name": "lbkn", "_objFlags": 0, "_parent": {"__id__": 226}, "_children": [], "_active": true, "_components": [{"__id__": 240}], "_prefab": {"__id__": 241}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 55}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [512.344, 18, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 239}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "", "_N$string": "", "_fontSize": 19, "_lineHeight": 19, "_enableWrapText": false, "_N$file": {"__uuid__": "********-d011-4b44-9a5f-19ff3d4c844a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "33ybqdfNFGJrSt4Sz1vRrF", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 226}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f735d3ac-adee-49f5-8abc-3353221f9488"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "8a646Q6CUpFioRPVYaUYE0W", "_name": "", "_objFlags": 0, "node": {"__id__": 226}, "_enabled": true, "lbTime": {"__id__": 228}, "lbInfo": {"__id__": 231}, "lbValue": {"__id__": 234}, "lbState": {"__id__": 237}, "btKhieuNai": {"__id__": 239}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "04u133BkNMzLkHxS/bqCic", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "5bEcCtHGNJq7XnbBxcUDDf", "sync": false}, {"__type__": "cc.Node", "_name": "scrollview", "_objFlags": 0, "_parent": {"__id__": 197}, "_children": [{"__id__": 247}], "_active": true, "_components": [{"__id__": 253}, {"__id__": 254}], "_prefab": {"__id__": 255}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1360, "height": 450}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 3, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 246}, "_children": [{"__id__": 248}], "_active": true, "_components": [{"__id__": 251}], "_prefab": {"__id__": 252}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1240, "height": 450}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 247}, "_children": [], "_active": true, "_components": [{"__id__": 249}], "_prefab": {"__id__": 250}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1190, "height": 75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 200, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 248}, "_enabled": false, "_layoutSize": {"__type__": "cc.Size", "width": 1190, "height": 75}, "_resize": 1, "_N$layoutType": 2, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 10, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "81a4swvBFI+L5WYQCbAtwJ", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 247}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "38WwahDIdG8ZMp4ok2JDh/", "sync": false}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 246}, "_enabled": true, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": true, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 248}, "content": {"__id__": 248}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": null, "_id": ""}, {"__type__": "2c5dafDzVRA46rjYCw++08u", "_name": "", "_objFlags": 0, "node": {"__id__": 246}, "_enabled": true, "itemTemplate": {"__id__": 226}, "scrollView": {"__id__": 253}, "spawnCount": 20, "spacing": 10, "bufferZone": 800, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "01hKDyhVtKHKVqWTC9ixAo", "sync": false}, {"__type__": "54bf5SnyFZHlLYqDHfk90JS", "_name": "", "_objFlags": 0, "node": {"__id__": 197}, "_enabled": true, "topupTransactionListView": {"__id__": 254}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "0b4A3a2EpHB7WpRJhxZ2fl", "sync": false}, {"__type__": "cc.Node", "_name": "redeemTransactionView", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 259}, {"__id__": 264}, {"__id__": 284}, {"__id__": 327}], "_active": false, "_components": [{"__id__": 337}], "_prefab": {"__id__": 338}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1000, "height": 510}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -71, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "btn-tab_dth_on", "_objFlags": 0, "_parent": {"__id__": 258}, "_children": [], "_active": true, "_components": [{"__id__": 260}, {"__id__": 261}], "_prefab": {"__id__": 263}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 149, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-3.5, 286.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 259}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6b4b2959-3e65-4054-8910-60ff0c558244"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 259}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 262}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "86d3f2OXIpB4bp+Btj8fZ1A", "handler": "changeTabClicked", "customEventData": "REDEEM_REWARD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "1325YzDXVC1ahU0CJIKBJm", "sync": false}, {"__type__": "cc.Node", "_name": "group-title", "_objFlags": 0, "_parent": {"__id__": 258}, "_children": [{"__id__": 265}, {"__id__": 268}, {"__id__": 271}, {"__id__": 274}, {"__id__": 277}, {"__id__": 280}], "_active": true, "_components": [], "_prefab": {"__id__": 283}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1195, "height": 76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 233, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "lbTime", "_objFlags": 0, "_parent": {"__id__": 264}, "_children": [], "_active": true, "_components": [{"__id__": 266}], "_prefab": {"__id__": 267}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 251, "b": 229, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 96.8, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-378, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 265}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Thời Gian", "_N$string": "Thời Gian", "_fontSize": 22, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "77/VCwTXtDl60hSZy077rc", "sync": false}, {"__type__": "cc.Node", "_name": "lbCardType", "_objFlags": 0, "_parent": {"__id__": 264}, "_children": [], "_active": true, "_components": [{"__id__": 269}], "_prefab": {"__id__": 270}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 251, "b": 229, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 87.45, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-193, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 268}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Loại Thẻ", "_N$string": "Loại Thẻ", "_fontSize": 22, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "2e+NxHkVtDOI1n1a9E7hHC", "sync": false}, {"__type__": "cc.Node", "_name": "lbCardValue", "_objFlags": 0, "_parent": {"__id__": 264}, "_children": [], "_active": true, "_components": [{"__id__": 272}], "_prefab": {"__id__": 273}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 251, "b": 229, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 95.7, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-54, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 271}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Mệnh Giá", "_N$string": "Mệnh Giá", "_fontSize": 22, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "2bQ+lOSoNFB5wNMV6NU02F", "sync": false}, {"__type__": "cc.Node", "_name": "lbValue", "_objFlags": 0, "_parent": {"__id__": 264}, "_children": [], "_active": true, "_components": [{"__id__": 275}], "_prefab": {"__id__": 276}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 251, "b": 229, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 55, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [74, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 274}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Q <PERSON>", "_N$string": "Q <PERSON>", "_fontSize": 22, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "9fmSyYVl5OyIpiEIqjrlX1", "sync": false}, {"__type__": "cc.Node", "_name": "lbState", "_objFlags": 0, "_parent": {"__id__": 264}, "_children": [], "_active": true, "_components": [{"__id__": 278}], "_prefab": {"__id__": 279}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 251, "b": 229, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 106.25, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [208, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 277}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Trạng <PERSON>", "_N$string": "Trạng <PERSON>", "_fontSize": 22, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "c2vXhqZMtCFr4yyPhrnCY4", "sync": false}, {"__type__": "cc.Node", "_name": "lbResult", "_objFlags": 0, "_parent": {"__id__": 264}, "_children": [], "_active": true, "_components": [{"__id__": 281}], "_prefab": {"__id__": 282}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 251, "b": 229, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 89.1, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [387, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 280}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "<PERSON>ử lý thẻ", "_N$string": "<PERSON>ử lý thẻ", "_fontSize": 22, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "50hBfWEBpOSa4khfHORRXf", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "ber+JWU45KHLLfcy5hy4FZ", "sync": false}, {"__type__": "cc.Node", "_name": "temp", "_objFlags": 0, "_parent": {"__id__": 258}, "_children": [{"__id__": 285}], "_active": true, "_components": [], "_prefab": {"__id__": 326}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 284}, "_children": [{"__id__": 286}, {"__id__": 289}, {"__id__": 292}, {"__id__": 295}, {"__id__": 298}, {"__id__": 301}, {"__id__": 312}], "_active": true, "_components": [{"__id__": 323}, {"__id__": 324}], "_prefab": {"__id__": 325}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 992, "height": 66}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "lbTime", "_objFlags": 0, "_parent": {"__id__": 285}, "_children": [], "_active": true, "_components": [{"__id__": 287}], "_prefab": {"__id__": 288}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-378, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 286}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "2019-12-12 12:12", "_N$string": "2019-12-12 12:12", "_fontSize": 24, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "cf6D2EAw5ESqbyPqWwM78M", "sync": false}, {"__type__": "cc.Node", "_name": "lbCardType", "_objFlags": 0, "_parent": {"__id__": 285}, "_children": [], "_active": true, "_components": [{"__id__": 290}], "_prefab": {"__id__": 291}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-193, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 289}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Loại Thẻ", "_N$string": "Loại Thẻ", "_fontSize": 24, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "39Ds5vWaVKJJGQmBjR6wgw", "sync": false}, {"__type__": "cc.Node", "_name": "lbCardValue", "_objFlags": 0, "_parent": {"__id__": 285}, "_children": [], "_active": true, "_components": [{"__id__": 293}], "_prefab": {"__id__": 294}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 104.4, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-54, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 292}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Mệnh Giá", "_N$string": "Mệnh Giá", "_fontSize": 24, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "21ohTuUcBIjJdHjB0qCJR7", "sync": false}, {"__type__": "cc.Node", "_name": "lbValue", "_objFlags": 0, "_parent": {"__id__": 285}, "_children": [], "_active": true, "_components": [{"__id__": 296}], "_prefab": {"__id__": 297}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 111, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [74, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 295}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "1.000.000", "_N$string": "1.000.000", "_fontSize": 24, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "e7WWRHxVNJEo+2anIG8xdM", "sync": false}, {"__type__": "cc.Node", "_name": "lbState", "_objFlags": 0, "_parent": {"__id__": 285}, "_children": [], "_active": true, "_components": [{"__id__": 299}], "_prefab": {"__id__": 300}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [208, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 298}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON><PERSON> công", "_N$string": "<PERSON><PERSON><PERSON><PERSON> công", "_fontSize": 24, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "72mDGxbsJNHq2Cdsb6fgFE", "sync": false}, {"__type__": "cc.Node", "_name": "btn-openInbox", "_objFlags": 0, "_parent": {"__id__": 285}, "_children": [{"__id__": 302}, {"__id__": 305}], "_active": false, "_components": [{"__id__": 308}, {"__id__": 309}], "_prefab": {"__id__": 311}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 201, "height": 59}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [387, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 301}, "_children": [], "_active": true, "_components": [{"__id__": 303}], "_prefab": {"__id__": 304}, "_opacity": 100, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 58.8, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1, -2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 302}, "_enabled": true, "_materials": [], "_useOriginalSize": true, "_string": "Xem", "_N$string": "Xem", "_fontSize": 28, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "aejuYyxJ5F572yuN5LDhOJ", "sync": false}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 301}, "_children": [], "_active": true, "_components": [{"__id__": 306}], "_prefab": {"__id__": 307}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 58.8, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 305}, "_enabled": true, "_materials": [], "_useOriginalSize": true, "_string": "Xem", "_N$string": "Xem", "_fontSize": 28, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "cdLwLlnvdJrb2FHyWX9Td6", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 301}, "_enabled": true, "_materials": [], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d77a8767-43da-4d10-acfd-bab76f02c408"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 301}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.1, "clickEvents": [{"__id__": 310}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 301}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 285}, "component": "", "_componentId": "8b49fmH7PlIHJnAqOCaTz2F", "handler": "openInboxClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "57ro+bLxVL2boVwRRhqdN/", "sync": false}, {"__type__": "cc.Node", "_name": "btn-cancel", "_objFlags": 0, "_parent": {"__id__": 285}, "_children": [{"__id__": 313}, {"__id__": 316}], "_active": false, "_components": [{"__id__": 319}, {"__id__": 320}], "_prefab": {"__id__": 322}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 203, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [387, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 312}, "_children": [], "_active": true, "_components": [{"__id__": 314}], "_prefab": {"__id__": 315}, "_opacity": 100, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 53.9, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1, -2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 313}, "_enabled": true, "_materials": [], "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON>", "_N$string": "<PERSON><PERSON><PERSON>", "_fontSize": 28, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "bePzfKdzpF6aJgMhwGHAt4", "sync": false}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 312}, "_children": [], "_active": true, "_components": [{"__id__": 317}], "_prefab": {"__id__": 318}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 53.9, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 316}, "_enabled": true, "_materials": [], "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON>", "_N$string": "<PERSON><PERSON><PERSON>", "_fontSize": 28, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "3ccpIIGVlJOr84zV4YAZVL", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 312}, "_enabled": true, "_materials": [], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "beab5d53-865c-4f5d-8af0-d50319da0510"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 312}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.1, "clickEvents": [{"__id__": 321}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 312}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 285}, "component": "", "_componentId": "8b49fmH7PlIHJnAqOCaTz2F", "handler": "cancelClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "5ftsdJ8zRH8Z5sKLfQX7+N", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 285}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f735d3ac-adee-49f5-8abc-3353221f9488"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "8b49fmH7PlIHJnAqOCaTz2F", "_name": "", "_objFlags": 0, "node": {"__id__": 285}, "_enabled": true, "lbTime": {"__id__": 287}, "lbCardType": {"__id__": 290}, "lbCardValue": {"__id__": 293}, "lbValue": {"__id__": 296}, "lbState": {"__id__": 299}, "nodeButtonOpen": {"__id__": 301}, "nodeButtonCancel": {"__id__": 312}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "16pu435rhAbboxFiISdJsT", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "87KXEyh7dEQaEs9hFAbgQ8", "sync": false}, {"__type__": "cc.Node", "_name": "scrollview", "_objFlags": 0, "_parent": {"__id__": 258}, "_children": [{"__id__": 328}], "_active": true, "_components": [{"__id__": 334}, {"__id__": 335}], "_prefab": {"__id__": 336}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1190, "height": 410}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -4, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 327}, "_children": [{"__id__": 329}], "_active": true, "_components": [{"__id__": 332}], "_prefab": {"__id__": 333}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1190, "height": 410}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 328}, "_children": [], "_active": true, "_components": [{"__id__": 330}], "_prefab": {"__id__": 331}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1190, "height": 75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 200, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 329}, "_enabled": false, "_layoutSize": {"__type__": "cc.Size", "width": 1190, "height": 75}, "_resize": 1, "_N$layoutType": 2, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 10, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "d70fJLuLdABYDhUCEaZM1G", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 328}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "24S7hhZiFH+I+QT2Pgrfn8", "sync": false}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 327}, "_enabled": true, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": true, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 329}, "content": {"__id__": 329}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": null, "_id": ""}, {"__type__": "c8bf6PJ3QxNCIEcjr4sl09E", "_name": "", "_objFlags": 0, "node": {"__id__": 327}, "_enabled": true, "itemTemplate": {"__id__": 285}, "scrollView": {"__id__": 334}, "spawnCount": 20, "spacing": 10, "bufferZone": 800, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "58dvUzAD9KQaouHepOl5Ur", "sync": false}, {"__type__": "e69e6VVEe5Nop5m3WwFRIXD", "_name": "", "_objFlags": 0, "node": {"__id__": 258}, "_enabled": true, "redeemTransactionListView": {"__id__": 335}, "lbTitleRedeemCol": {"__id__": 275}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "c2FY4SaIpN+7h6ZgWUoci7", "sync": false}, {"__type__": "cc.Node", "_name": "LichSUGiaoDich", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 340}, {"__id__": 343}, {"__id__": 379}, {"__id__": 396}, {"__id__": 426}, {"__id__": 436}, {"__id__": 441}, {"__id__": 446}], "_active": false, "_components": [{"__id__": 449}], "_prefab": {"__id__": 450}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1400, "height": 500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -71, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "_parent": {"__id__": 339}, "_children": [], "_active": true, "_components": [{"__id__": 341}], "_prefab": {"__id__": 342}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 378.75, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 376, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 340}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "LỊCH SỬ GIAO DỊCH", "_N$string": "LỊCH SỬ GIAO DỊCH", "_fontSize": 24, "_lineHeight": 24, "_enableWrapText": true, "_N$file": {"__uuid__": "bc6e0141-f9ea-4894-a746-c3526ba32428"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "30zAwBQ7RP6aD72zNeyR4A", "sync": false}, {"__type__": "cc.Node", "_name": "layout", "_objFlags": 0, "_parent": {"__id__": 339}, "_children": [{"__id__": 344}, {"__id__": 347}, {"__id__": 350}, {"__id__": 353}, {"__id__": 356}, {"__id__": 359}, {"__id__": 362}, {"__id__": 368}, {"__id__": 374}], "_active": true, "_components": [{"__id__": 377}], "_prefab": {"__id__": 378}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 947.75, "height": 55}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 258, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "GD", "_objFlags": 0, "_parent": {"__id__": 343}, "_children": [], "_active": true, "_components": [{"__id__": 345}], "_prefab": {"__id__": 346}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 122, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-560.875, 5.0009999999999994, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 344}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON>", "_N$string": "<PERSON><PERSON><PERSON>", "_fontSize": 24, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d8da7e3d-fc85-40bc-812a-b1ad31bef934"}, "_isSystemFontUsed": false, "_spacingX": -2, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "37ON9vp8pJB7TWYx5dytSK", "sync": false}, {"__type__": "cc.Node", "_name": "lineNgang", "_objFlags": 0, "_parent": {"__id__": 343}, "_children": [], "_active": true, "_components": [{"__id__": 348}], "_prefab": {"__id__": 349}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 600, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-448.311, -232.393, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 347}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "fd1c87bb-ab50-43fa-8de0-de0fb444e2e7"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "12t2a/f9RLXrtFjGkyczKq", "sync": false}, {"__type__": "cc.Node", "_name": "CongGD", "_objFlags": 0, "_parent": {"__id__": 343}, "_children": [], "_active": true, "_components": [{"__id__": 351}], "_prefab": {"__id__": 352}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 113.25, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-351.25, 5.0009999999999994, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 350}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Cổng GD", "_N$string": "Cổng GD", "_fontSize": 24, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d8da7e3d-fc85-40bc-812a-b1ad31bef934"}, "_isSystemFontUsed": false, "_spacingX": -2, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "bbbnzzkLpLPJMzz1EcC1zj", "sync": false}, {"__type__": "cc.Node", "_name": "lineNgang", "_objFlags": 0, "_parent": {"__id__": 343}, "_children": [], "_active": true, "_components": [{"__id__": 354}], "_prefab": {"__id__": 355}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 600, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-239.87, -232.197, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 353}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "fd1c87bb-ab50-43fa-8de0-de0fb444e2e7"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "078cl9paJO2oFNhMuNLGPW", "sync": false}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 343}, "_children": [], "_active": true, "_components": [{"__id__": 357}], "_prefab": {"__id__": 358}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 130.25, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-151.5, 5.001, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 356}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON>", "_N$string": "<PERSON><PERSON><PERSON>", "_fontSize": 24, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d8da7e3d-fc85-40bc-812a-b1ad31bef934"}, "_isSystemFontUsed": false, "_spacingX": -2, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "858GEPHg5Am7GMzSBwgcks", "sync": false}, {"__type__": "cc.Node", "_name": "lineNgang", "_objFlags": 0, "_parent": {"__id__": 343}, "_children": [], "_active": true, "_components": [{"__id__": 360}], "_prefab": {"__id__": 361}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 600, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-49.12, -232.197, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 359}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "fd1c87bb-ab50-43fa-8de0-de0fb444e2e7"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "a528NuKtpObIYescD9w8DX", "sync": false}, {"__type__": "cc.Node", "_name": "so<PERSON>", "_objFlags": 0, "_parent": {"__id__": 343}, "_children": [{"__id__": 363}], "_active": true, "_components": [{"__id__": 366}], "_prefab": {"__id__": 367}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 98.25, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [23.75, 5.0009999999999994, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "lineNgang", "_objFlags": 0, "_parent": {"__id__": 362}, "_children": [], "_active": true, "_components": [{"__id__": 364}], "_prefab": {"__id__": 365}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 600, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [90.38, -237.198, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 363}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "fd1c87bb-ab50-43fa-8de0-de0fb444e2e7"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "39Hb994MBPN6//UwiImuhY", "sync": false}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 362}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Số Tiền", "_N$string": "Số Tiền", "_fontSize": 24, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d8da7e3d-fc85-40bc-812a-b1ad31bef934"}, "_isSystemFontUsed": false, "_spacingX": -2, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "2bJ9TTJz1C9YVFkq1mBnC2", "sync": false}, {"__type__": "cc.Node", "_name": "trang<PERSON>i", "_objFlags": 0, "_parent": {"__id__": 343}, "_children": [{"__id__": 369}], "_active": true, "_components": [{"__id__": 372}], "_prefab": {"__id__": 373}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 135.75, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [196.75, 5.0009999999999994, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "lineNgang", "_objFlags": 0, "_parent": {"__id__": 368}, "_children": [], "_active": true, "_components": [{"__id__": 370}], "_prefab": {"__id__": 371}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 600, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [98.38, -237.198, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 369}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "fd1c87bb-ab50-43fa-8de0-de0fb444e2e7"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "27u+t6MX1NCpY6f6shLjuq", "sync": false}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 368}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Trạng <PERSON>", "_N$string": "Trạng <PERSON>", "_fontSize": 24, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d8da7e3d-fc85-40bc-812a-b1ad31bef934"}, "_isSystemFontUsed": false, "_spacingX": -2, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "faIlUc+fBFmrj5MRVJIRPs", "sync": false}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 343}, "_children": [], "_active": true, "_components": [{"__id__": 375}], "_prefab": {"__id__": 376}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 98.25, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [481.75, 5.0009999999999994, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 374}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "<PERSON><PERSON>", "_N$string": "<PERSON><PERSON>", "_fontSize": 24, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d8da7e3d-fc85-40bc-812a-b1ad31bef934"}, "_isSystemFontUsed": false, "_spacingX": -2, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "4coh5xrQxMI5A/zeMei26R", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 343}, "_enabled": false, "_layoutSize": {"__type__": "cc.Size", "width": 947.75, "height": 55}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 50, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "01J+I1JgZOFZ7IbX51YLNt", "sync": false}, {"__type__": "cc.Node", "_name": "group-title", "_objFlags": 0, "_parent": {"__id__": 339}, "_children": [{"__id__": 380}, {"__id__": 383}, {"__id__": 386}, {"__id__": 389}, {"__id__": 392}], "_active": true, "_components": [], "_prefab": {"__id__": 395}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1195, "height": 76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.088, 3000, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "lbTime", "_objFlags": 0, "_parent": {"__id__": 379}, "_children": [], "_active": true, "_components": [{"__id__": 381}], "_prefab": {"__id__": 382}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 251, "b": 229, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 96.8, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-366, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 380}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Thời Gian", "_N$string": "Thời Gian", "_fontSize": 22, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "f1usnxzR5KVJ24+uO8JSbN", "sync": false}, {"__type__": "cc.Node", "_name": "lbReceiver", "_objFlags": 0, "_parent": {"__id__": 379}, "_children": [], "_active": true, "_components": [{"__id__": 384}], "_prefab": {"__id__": 385}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 251, "b": 229, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120.45, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-143.6, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 383}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON><PERSON>", "_N$string": "<PERSON><PERSON><PERSON><PERSON>", "_fontSize": 22, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "bewqq33bdAuI42jTE75klo", "sync": false}, {"__type__": "cc.Node", "_name": "lbValue", "_objFlags": 0, "_parent": {"__id__": 379}, "_children": [], "_active": true, "_components": [{"__id__": 387}], "_prefab": {"__id__": 388}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 251, "b": 229, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 65.55, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [19, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 386}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Giá <PERSON>", "_N$string": "Giá <PERSON>", "_fontSize": 22, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "5d+Toe5LZB8L73Qhf64UW3", "sync": false}, {"__type__": "cc.Node", "_name": "lbBalance", "_objFlags": 0, "_parent": {"__id__": 379}, "_children": [], "_active": true, "_components": [{"__id__": 390}], "_prefab": {"__id__": 391}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 251, "b": 229, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [156, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 389}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Số Dư", "_N$string": "Số Dư", "_fontSize": 22, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "95v7uXSipMtLQ9jyxNluTs", "sync": false}, {"__type__": "cc.Node", "_name": "lbDesc", "_objFlags": 0, "_parent": {"__id__": 379}, "_children": [], "_active": true, "_components": [{"__id__": 393}], "_prefab": {"__id__": 394}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 251, "b": 229, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 58.3, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [362, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 392}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "<PERSON><PERSON>", "_N$string": "<PERSON><PERSON>", "_fontSize": 22, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "b1kMkNIjJMj64FcyNWb7d5", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "767kFH4htDqoraPm+1b0UL", "sync": false}, {"__type__": "cc.Node", "_name": "temp", "_objFlags": 0, "_parent": {"__id__": 339}, "_children": [{"__id__": 397}], "_active": true, "_components": [], "_prefab": {"__id__": 425}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 47.263, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 396}, "_children": [{"__id__": 398}, {"__id__": 404}, {"__id__": 407}, {"__id__": 410}, {"__id__": 413}, {"__id__": 416}, {"__id__": 419}], "_active": true, "_components": [{"__id__": 422}, {"__id__": 423}], "_prefab": {"__id__": 424}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1360, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 397}, "_children": [{"__id__": 399}], "_active": true, "_components": [{"__id__": 402}], "_prefab": {"__id__": 403}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 105, "height": 42.94}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-568.544, -9, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "lbReceiver", "_objFlags": 0, "_parent": {"__id__": 398}, "_children": [], "_active": true, "_components": [{"__id__": 400}], "_prefab": {"__id__": 401}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 253, "g": 162, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.612, 30, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 399}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "#666666", "_N$string": "#666666", "_fontSize": 19, "_lineHeight": 24, "_enableWrapText": false, "_N$file": {"__uuid__": "********-d011-4b44-9a5f-19ff3d4c844a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "5d93XXf9JLz4vfTjJOW/hT", "sync": false}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 398}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "2019-12-12 12:12:12", "_N$string": "2019-12-12 12:12:12", "_fontSize": 19, "_lineHeight": 19, "_enableWrapText": false, "_N$file": {"__uuid__": "********-d011-4b44-9a5f-19ff3d4c844a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 3, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "be4zTAPx5GObssED6W9FfH", "sync": false}, {"__type__": "cc.Node", "_name": "lbCongGD", "_objFlags": 0, "_parent": {"__id__": 397}, "_children": [], "_active": true, "_components": [{"__id__": 405}], "_prefab": {"__id__": 406}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 253, "g": 162, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-352.975, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 404}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "VIETCOMBANK", "_N$string": "VIETCOMBANK", "_fontSize": 19, "_lineHeight": 24, "_enableWrapText": true, "_N$file": {"__uuid__": "********-d011-4b44-9a5f-19ff3d4c844a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "ea6sDjBMxLW40t3SqCrE0J", "sync": false}, {"__type__": "cc.Node", "_name": "Hinh<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 397}, "_children": [], "_active": true, "_components": [{"__id__": 408}], "_prefab": {"__id__": 409}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 54.***************}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-153.322, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 407}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Nạp Internet Banking", "_N$string": "Nạp Internet Banking", "_fontSize": 19, "_lineHeight": 24, "_enableWrapText": false, "_N$file": {"__uuid__": "********-d011-4b44-9a5f-19ff3d4c844a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 3, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "9eYfWX8X5FlJa9wMVLPz8s", "sync": false}, {"__type__": "cc.Node", "_name": "lbBalance", "_objFlags": 0, "_parent": {"__id__": 397}, "_children": [], "_active": true, "_components": [{"__id__": 411}], "_prefab": {"__id__": 412}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 253, "g": 162, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [23.27, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 410}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "100.000.000", "_N$string": "100.000.000", "_fontSize": 19, "_lineHeight": 24, "_enableWrapText": false, "_N$file": {"__uuid__": "********-d011-4b44-9a5f-19ff3d4c844a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "4dLLOz1Y1N/rQ9HrUVpbxJ", "sync": false}, {"__type__": "cc.Node", "_name": "TrangThai", "_objFlags": 0, "_parent": {"__id__": 397}, "_children": [], "_active": true, "_components": [{"__id__": 414}], "_prefab": {"__id__": 415}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 224, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [196.63, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 413}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON><PERSON> công", "_N$string": "<PERSON><PERSON><PERSON><PERSON> công", "_fontSize": 19, "_lineHeight": 24, "_enableWrapText": true, "_N$file": {"__uuid__": "********-d011-4b44-9a5f-19ff3d4c844a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "07OtxXQttNdbuJsjiUg7xt", "sync": false}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 397}, "_children": [], "_active": true, "_components": [{"__id__": 417}], "_prefab": {"__id__": 418}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 385, "height": 30.240000000000002}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [482.587, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 416}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "", "_N$string": "", "_fontSize": 20, "_lineHeight": 24, "_enableWrapText": false, "_N$file": {"__uuid__": "82e92547-7bac-49e5-a2c4-438f3025dff7"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 3, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "63CwMt0NdC1aJLOuUkUAyg", "sync": false}, {"__type__": "cc.Node", "_name": "lineNgang", "_objFlags": 0, "_parent": {"__id__": 397}, "_children": [], "_active": true, "_components": [{"__id__": 420}], "_prefab": {"__id__": 421}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1200, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -46, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 419}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "fd1c87bb-ab50-43fa-8de0-de0fb444e2e7"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "3d/MoPm7JD1pcyGXUEkIIX", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 397}, "_enabled": false, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f735d3ac-adee-49f5-8abc-3353221f9488"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "fa83760o9pBc5Mw20O/PIXZ", "_name": "", "_objFlags": 0, "node": {"__id__": 397}, "_enabled": true, "lbTime": {"__id__": 402}, "lbType": {"__id__": 408}, "lbRequestID": {"__id__": 400}, "lbTranID": {"__id__": 417}, "lbValue": {"__id__": 411}, "lbStatus": {"__id__": 414}, "tennganhang": {"__id__": 405}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "8c2IExK0tEAL7s+A+dUDRD", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "8bvi4cVitP2ZlyzTMk7Qz0", "sync": false}, {"__type__": "cc.Node", "_name": "scrollview", "_objFlags": 0, "_parent": {"__id__": 339}, "_children": [{"__id__": 427}], "_active": true, "_components": [{"__id__": 433}, {"__id__": 434}], "_prefab": {"__id__": 435}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1400, "height": 450}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 3, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 426}, "_children": [{"__id__": 428}], "_active": true, "_components": [{"__id__": 431}], "_prefab": {"__id__": 432}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1400, "height": 450}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 427}, "_children": [], "_active": true, "_components": [{"__id__": 429}], "_prefab": {"__id__": 430}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1360, "height": 75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 225.78, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 428}, "_enabled": false, "_layoutSize": {"__type__": "cc.Size", "width": 1190, "height": 75}, "_resize": 1, "_N$layoutType": 2, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 10, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "9flRVpQhdCeKwBAwIcxm5o", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 427}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "9a4ATtwcVOja6JTyD3qF2N", "sync": false}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 426}, "_enabled": false, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": true, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 428}, "content": {"__id__": 428}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": null, "_id": ""}, {"__type__": "484c3H3czdJX5fltitJIt1p", "_name": "", "_objFlags": 0, "node": {"__id__": 426}, "_enabled": true, "itemTemplate": {"__id__": 397}, "scrollView": {"__id__": 433}, "spawnCount": 20, "spacing": 10, "bufferZone": 800, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "e34ktN8udHPo9Tzdop4meF", "sync": false}, {"__type__": "cc.Node", "_name": "btnNext", "_objFlags": 0, "_parent": {"__id__": 339}, "_children": [], "_active": true, "_components": [{"__id__": 437}, {"__id__": 438}], "_prefab": {"__id__": 440}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 23, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [684.291, -2.65, 0, 0, 0, 0, 1, 2, 2, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 436}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "78b32c40-4f5a-4f63-bb31-216bbf2c4a25"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 436}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 439}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 436}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 339}, "component": "", "_componentId": "319ba6uAWhM0aBNEFwmzggm", "handler": "pageNextClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "dbecfVHTdCur1rD2UYER0F", "sync": false}, {"__type__": "cc.Node", "_name": "btnPrev", "_objFlags": 0, "_parent": {"__id__": 339}, "_children": [], "_active": true, "_components": [{"__id__": 442}, {"__id__": 443}], "_prefab": {"__id__": 445}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 23, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-687.988, -2.65, 0, 0, 0, 0, 1, 2, 2, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 441}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ff9b21df-4874-4266-be9c-ab954937a2cd"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 441}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 444}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 441}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 339}, "component": "", "_componentId": "319ba6uAWhM0aBNEFwmzggm", "handler": "pagePreviousClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "0bVeS/CbdAi7ZoyKNnXv/H", "sync": false}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "_parent": {"__id__": 339}, "_children": [], "_active": true, "_components": [{"__id__": 447}], "_prefab": {"__id__": 448}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 98.5, "height": 30.24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [482.125, 318.201, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 446}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Trang: 1", "_N$string": "Trang: 1", "_fontSize": 24, "_lineHeight": 24, "_enableWrapText": true, "_N$file": {"__uuid__": "3208deca-08ff-4361-b0c2-5bc37b3d2919"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "1cNvX89gBClI/8lRe1+svQ", "sync": false}, {"__type__": "319ba6uAWhM0aBNEFwmzggm", "_name": "", "_objFlags": 0, "node": {"__id__": 339}, "_enabled": true, "bankTransactionListView": {"__id__": 434}, "nodePageNext": {"__id__": 436}, "nodePagePrevious": {"__id__": 441}, "nodeLBPage": {"__id__": 447}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "abcQa9t71Cs6XL36m+gJc7", "sync": false}, {"__type__": "cc.Node", "_name": "receiveTransactionView", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 452}, {"__id__": 457}, {"__id__": 474}, {"__id__": 495}, {"__id__": 505}], "_active": false, "_components": [{"__id__": 508}], "_prefab": {"__id__": 509}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1000, "height": 510}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -50, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "btn-tab_receive_on", "_objFlags": 0, "_parent": {"__id__": 451}, "_children": [], "_active": false, "_components": [{"__id__": 453}, {"__id__": 454}], "_prefab": {"__id__": 456}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 75, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-18.167, 333.381, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 452}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "25c27fd5-1e8e-4874-b785-95ba7d2b4cff"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 452}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 455}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "86d3f2OXIpB4bp+Btj8fZ1A", "handler": "changeTabClicked", "customEventData": "RECEIVE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "e0W7xVq7JNqoLTuhcpv4U5", "sync": false}, {"__type__": "cc.Node", "_name": "group-title", "_objFlags": 0, "_parent": {"__id__": 451}, "_children": [{"__id__": 458}, {"__id__": 461}, {"__id__": 464}, {"__id__": 467}, {"__id__": 470}], "_active": true, "_components": [], "_prefab": {"__id__": 473}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1195, "height": 76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 239, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "lbTime", "_objFlags": 0, "_parent": {"__id__": 457}, "_children": [], "_active": true, "_components": [{"__id__": 459}], "_prefab": {"__id__": 460}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 251, "b": 229, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 96.8, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-378, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 458}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Thời Gian", "_N$string": "Thời Gian", "_fontSize": 22, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "bdQ1t6gpZPZrDZDnD5pl1S", "sync": false}, {"__type__": "cc.Node", "_name": "lbTransferPerson", "_objFlags": 0, "_parent": {"__id__": 457}, "_children": [], "_active": true, "_components": [{"__id__": 462}], "_prefab": {"__id__": 463}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 251, "b": 229, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120.45, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-152, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 461}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON><PERSON>", "_N$string": "<PERSON><PERSON><PERSON><PERSON>", "_fontSize": 22, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "d1xXIeK2pLuLgZVWgPmxLk", "sync": false}, {"__type__": "cc.Node", "_name": "lbDesc", "_objFlags": 0, "_parent": {"__id__": 457}, "_children": [], "_active": true, "_components": [{"__id__": 465}], "_prefab": {"__id__": 466}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 251, "b": 229, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 63.25, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [374.7, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 464}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "<PERSON><PERSON>", "_N$string": "<PERSON><PERSON>", "_fontSize": 22, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "e2f4jg4kdNxYeAuufB6zGa", "sync": false}, {"__type__": "cc.Node", "_name": "lbValue", "_objFlags": 0, "_parent": {"__id__": 457}, "_children": [], "_active": true, "_components": [{"__id__": 468}], "_prefab": {"__id__": 469}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 251, "b": 229, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 65.55, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [34, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 467}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Giá <PERSON>", "_N$string": "Giá <PERSON>", "_fontSize": 22, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "65KoJ6kxRGcrTSJaMVBGra", "sync": false}, {"__type__": "cc.Node", "_name": "lbBalance", "_objFlags": 0, "_parent": {"__id__": 457}, "_children": [], "_active": true, "_components": [{"__id__": 471}], "_prefab": {"__id__": 472}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 251, "b": 229, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [184, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 470}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Số Dư", "_N$string": "Số Dư", "_fontSize": 22, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "9ejg6uw9lJ85hYib4wK+Mv", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "4a2bcGnoRJH6SN5tWImiR0", "sync": false}, {"__type__": "cc.Node", "_name": "temp", "_objFlags": 0, "_parent": {"__id__": 451}, "_children": [{"__id__": 475}], "_active": true, "_components": [], "_prefab": {"__id__": 494}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 474}, "_children": [{"__id__": 476}, {"__id__": 479}, {"__id__": 482}, {"__id__": 485}, {"__id__": 488}], "_active": true, "_components": [{"__id__": 491}, {"__id__": 492}], "_prefab": {"__id__": 493}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 992, "height": 66}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "lbTime", "_objFlags": 0, "_parent": {"__id__": 475}, "_children": [], "_active": true, "_components": [{"__id__": 477}], "_prefab": {"__id__": 478}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 201, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-378, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 476}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "2019-12-12 12:12", "_N$string": "2019-12-12 12:12", "_fontSize": 24, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "39Sd9aU/VMu4BYkNvJJW5S", "sync": false}, {"__type__": "cc.Node", "_name": "lbTransferPerson", "_objFlags": 0, "_parent": {"__id__": 475}, "_children": [], "_active": true, "_components": [{"__id__": 480}], "_prefab": {"__id__": 481}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 217.8, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-152, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 479}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "accountname12345", "_N$string": "accountname12345", "_fontSize": 24, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "34Vy8rg4FDvb2AFXj7fGDw", "sync": false}, {"__type__": "cc.Node", "_name": "lbValue", "_objFlags": 0, "_parent": {"__id__": 475}, "_children": [], "_active": true, "_components": [{"__id__": 483}], "_prefab": {"__id__": 484}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 254, "b": 114, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 124.8, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [34, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 482}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "10.000.000", "_N$string": "10.000.000", "_fontSize": 24, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "4dwwEb7WRLCbkKbEzqvVAe", "sync": false}, {"__type__": "cc.Node", "_name": "lbBalance", "_objFlags": 0, "_parent": {"__id__": 475}, "_children": [], "_active": true, "_components": [{"__id__": 486}], "_prefab": {"__id__": 487}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 122, "g": 255, "b": 115, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 138.6, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [184, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 485}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "100.000.000", "_N$string": "100.000.000", "_fontSize": 24, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "688MiHuUtNoY7cvNet15KK", "sync": false}, {"__type__": "cc.Node", "_name": "lbDesc", "_objFlags": 0, "_parent": {"__id__": 475}, "_children": [], "_active": true, "_components": [{"__id__": 489}], "_prefab": {"__id__": 490}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 220, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [374.7, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 488}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "<PERSON><PERSON>", "_N$string": "<PERSON><PERSON>", "_fontSize": 24, "_lineHeight": 50, "_enableWrapText": true, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "48jXE9dyFOepjv2o51dbS/", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 475}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f735d3ac-adee-49f5-8abc-3353221f9488"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "c13eai/+ohB37r7hkXjXkke", "_name": "", "_objFlags": 0, "node": {"__id__": 475}, "_enabled": true, "lbTime": {"__id__": 477}, "lbTransferPerson": {"__id__": 480}, "lbValue": {"__id__": 483}, "lbBalance": {"__id__": 486}, "lbDesc": {"__id__": 489}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "8cK1HyeI1I24A5OU4WDHgn", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "14houCfn1IIaDCa1/x97DW", "sync": false}, {"__type__": "cc.Node", "_name": "scrollview", "_objFlags": 0, "_parent": {"__id__": 451}, "_children": [{"__id__": 496}], "_active": true, "_components": [{"__id__": 502}, {"__id__": 503}], "_prefab": {"__id__": 504}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1190, "height": 410}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -1, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 495}, "_children": [{"__id__": 497}], "_active": true, "_components": [{"__id__": 500}], "_prefab": {"__id__": 501}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1190, "height": 410}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 496}, "_children": [], "_active": true, "_components": [{"__id__": 498}], "_prefab": {"__id__": 499}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1190, "height": 75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 200, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 497}, "_enabled": false, "_layoutSize": {"__type__": "cc.Size", "width": 1190, "height": 75}, "_resize": 1, "_N$layoutType": 2, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 10, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "fcjq6L0ZZJE7vzM6VraeBp", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 496}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "33RUYuJJ1Ne6Ewj52b5eOz", "sync": false}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 495}, "_enabled": true, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": true, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 497}, "content": {"__id__": 497}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": null, "_id": ""}, {"__type__": "6dff2RU+9NKJpRmM6hrbqGd", "_name": "", "_objFlags": 0, "node": {"__id__": 495}, "_enabled": true, "itemTemplate": {"__id__": 475}, "scrollView": {"__id__": 502}, "spawnCount": 20, "spacing": 10, "bufferZone": 800, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "d7i0H+/apKc68Nx24ZmtEG", "sync": false}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "_parent": {"__id__": 451}, "_children": [], "_active": true, "_components": [{"__id__": 506}], "_prefab": {"__id__": 507}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 435, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 353, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 505}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "LỊCH SỬ CHUYỂN TIỀN", "_N$string": "LỊCH SỬ CHUYỂN TIỀN", "_fontSize": 24, "_lineHeight": 24, "_enableWrapText": true, "_N$file": {"__uuid__": "bc6e0141-f9ea-4894-a746-c3526ba32428"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "26T7ZQU9BNurrYvzi+ARP4", "sync": false}, {"__type__": "88840NKOYNJd5nH9mKQUeFn", "_name": "", "_objFlags": 0, "node": {"__id__": 451}, "_enabled": true, "receiveTransactionListView": {"__id__": 503}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "65E0jo6O9K8Io0e+Z2/r9J", "sync": false}, {"__type__": "cc.Node", "_name": "New Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 511}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "51ZDi8zfBNnIXyl0EZFpl4", "sync": false}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_defaultClip": {"__uuid__": "fd368a1d-24a5-4b8f-8745-d532e9a6fda0"}, "_clips": [{"__uuid__": "fd368a1d-24a5-4b8f-8745-d532e9a6fda0"}, {"__uuid__": "2c581fef-58f4-478d-adee-4138a71c7df4"}], "playOnLoad": false, "_id": ""}, {"__type__": "86d3f2OXIpB4bp+Btj8fZ1A", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "nodeGame": {"__id__": 63}, "nodeTopup": {"__id__": 197}, "nodeRedeem": {"__id__": 258}, "nodeTransfer": {"__id__": 510}, "nodeReceive": {"__id__": 451}, "nodeBank": {"__id__": 339}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "08c00b4d-3091-438c-bf7b-a10a6559ff6f"}, "fileId": "", "sync": false}]