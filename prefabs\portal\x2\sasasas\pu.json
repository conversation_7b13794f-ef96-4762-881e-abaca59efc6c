{"skins": {"default": {"tien1": {"tien1": {"x": 6.35, "width": 91, "y": 1.34, "height": 85}}, "tien5": {"tien5": {"x": 0.32, "width": 79, "y": 3.17, "height": 79}}, "pu": {"pu": {"x": 6.86, "width": 1320, "y": -205.54, "height": 191}}, "tien4": {"tien4": {"x": -0.97, "width": 112, "y": -4.63, "height": 112}}, "bai2": {"bai": {"triangles": [3, 10, 9, 2, 12, 11, 13, 12, 2, 14, 13, 2, 3, 11, 10, 8, 3, 9, 2, 11, 3, 4, 3, 8, 1, 0, 14, 2, 1, 14, 7, 5, 4, 7, 4, 8, 6, 5, 7], "uvs": [0, 0.72381, 0.16845, 0.72811, 0.40185, 0.40318, 0.59514, 0.42685, 0.67902, 0.60546, 0.71002, 0.73457, 0.8249, 0.82925, 1, 0.77545, 1, 0.37951, 1, 0.21812, 0.79572, 0, 0.48026, 0, 0.11192, 0, 0.01528, 0.12774, 0, 0.34938], "vertices": [-456.6, -173.77, -308.7, -175.37, -103.78, 66.38, 65.93, 48.77, 139.58, -84.12, 166.79, -180.18, 267.66, -250.62, 421.4, -210.6, 421.4, 83.99, 421.4, 204.06, 242.04, 366.34, -34.93, 366.34, -358.33, 366.34, -443.19, 271.31, -456.6, 106.4], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28], "width": 878, "type": "mesh", "hull": 15, "height": 744}}, "tien3": {"tien3": {"x": -1.52, "width": 106, "y": -4.39, "height": 102}}, "tien2": {"tien2": {"x": 0.49, "width": 57, "y": -2.02, "height": 58}}, "bai": {"bai": {"x": -17.6, "width": 878, "y": -7.26, "height": 744}}, "vina": {"vina": {"x": -12.4, "width": 148, "y": 15.05, "height": 132}}, "mm2": {"mm2": {"x": -7.23, "width": 166, "y": 12.03, "height": 137}}, "line3": {"line2": {"x": -10.89, "width": 1489, "y": 4.6, "height": 882}}, "muiten": {"muiten": {"x": -3.56, "width": 237, "y": -1.34, "height": 249}}, "tech": {"tech": {"rotation": -4, "x": 1.98, "width": 80, "y": -7.79, "height": 89}}, "set3": {"set3": {"x": 226.97, "width": 552, "y": -78.32, "height": 622}}, "set2": {"set2": {"x": 52.89, "width": 430, "y": -287.86, "height": 588}}, "set5": {"set3": {"rotation": 180, "x": -364.95, "width": 552, "y": 30.4, "height": 622}}, "set4": {"set4": {"x": 447.48, "width": 531, "y": -24.37, "height": 184}}, "xx1": {"xx1": {"x": -6.08, "width": 344, "y": 1.11, "height": 331}}, "txt": {"txt": {"width": 1022, "height": 97}}, "xx3": {"xx3": {"x": 0.89, "width": 238, "y": 3.52, "height": 231}}, "xx2": {"xx2": {"x": 0.69, "width": 287, "y": 9.9, "height": 306}}, "txt2": {"txt": {"width": 1022, "height": 97}}, "txt3": {"txt3": {"x": -0.81, "width": 854, "y": -5.2, "height": 71}}, "hop2": {"hop2": {"rotation": -109.06, "x": 83.68, "width": 192, "y": 3.28, "height": 197}}, "hop3": {"hop3": {"rotation": -70.58, "x": 74.83, "width": 222, "y": -3.28, "height": 207}}, "set1": {"set1": {"x": 16.1, "width": 560, "y": -9.57, "height": 234}}, "t1": {"t4": {"width": 1022, "height": 97}, "t5": {"width": 1022, "height": 97}, "t6": {"width": 1022, "height": 97}, "t7": {"width": 1022, "height": 97}, "t8": {"width": 1022, "height": 97}, "t9": {"width": 1022, "height": 97}, "t10": {"width": 1022, "height": 97}, "t12": {"width": 1022, "height": 97}, "t11": {"width": 1022, "height": 97}, "t14": {"width": 1022, "height": 97}, "t13": {"width": 1022, "height": 97}, "t16": {"width": 1022, "y": 0.5, "height": 97}, "t15": {"width": 1022, "y": 0.5, "height": 97}, "t18": {"width": 1022, "y": 0.5, "height": 97}, "t17": {"width": 1022, "y": 0.5, "height": 97}, "t1": {"width": 1022, "height": 97}, "t2": {"width": 1022, "height": 97}, "t3": {"width": 1022, "height": 97}}, "theViettel": {"theViettel": {"x": 0.33, "width": 146, "y": -15.78, "height": 176}}, "coin": {"coin": {"x": 52.08, "width": 806, "y": -114.67, "height": 249}}}}, "skeleton": {"images": "./images/", "width": 1544.89, "spine": "3.7.94", "audio": "C:/Users/<USER>/Desktop/NGOC/PTS/0. 200k/popup", "hash": "6keh3DzFkjD/vCyRzWhxt1eGf9A", "height": 949.93}, "slots": [{"attachment": "set1", "blend": "additive", "name": "set1", "bone": "set1"}, {"attachment": "set2", "blend": "additive", "name": "set2", "bone": "set2"}, {"attachment": "set3", "name": "set3", "bone": "root"}, {"attachment": "set3", "name": "set5", "bone": "root"}, {"attachment": "set4", "name": "set4", "bone": "root"}, {"attachment": "bai", "name": "bai", "bone": "bai"}, {"attachment": "bai", "name": "bai2", "bone": "bai2"}, {"attachment": "line2", "name": "line3", "bone": "line3"}, {"attachment": "mm2", "name": "mm2", "bone": "mm2"}, {"attachment": "hop2", "name": "hop2", "bone": "hop2"}, {"attachment": "hop3", "name": "hop3", "bone": "hop3"}, {"attachment": "tech", "name": "tech", "bone": "tech"}, {"attachment": "vina", "name": "vina", "bone": "vina"}, {"attachment": "coin", "name": "coin", "bone": "bone2"}, {"attachment": "tien5", "name": "tien5", "bone": "tien5"}, {"attachment": "theViettel", "name": "theViettel", "bone": "theViettel"}, {"attachment": "tien4", "name": "tien4", "bone": "tien4"}, {"attachment": "tien3", "name": "tien3", "bone": "tien3"}, {"attachment": "tien2", "name": "tien2", "bone": "tien2"}, {"attachment": "tien1", "name": "tien1", "bone": "tien1"}, {"attachment": "pu", "name": "pu", "bone": "root"}, {"attachment": "muiten", "name": "muiten", "bone": "muiten"}, {"attachment": "xx2", "name": "xx2", "bone": "xx2"}, {"attachment": "xx3", "name": "xx3", "bone": "xx3"}, {"attachment": "xx1", "name": "xx1", "bone": "xx1"}, {"attachment": "txt", "name": "txt", "bone": "txt"}, {"attachment": "txt", "blend": "additive", "name": "txt2", "bone": "txt2"}, {"attachment": "t18", "blend": "additive", "name": "t1", "bone": "t1"}, {"attachment": "txt3", "name": "txt3", "bone": "txt3"}], "bones": [{"name": "root"}, {"parent": "root", "name": "bone2", "y": 11.87}, {"scaleX": 1.114, "parent": "bone2", "scaleY": 1.114, "rotation": 68.63, "name": "hop3", "length": 117.2, "x": 526.47, "y": -130.42}, {"scaleX": 1.056, "parent": "bone2", "scaleY": 1.133, "rotation": 0.39, "name": "mm2", "x": 404.57, "y": 181.86}, {"scaleX": 1.15, "parent": "bone2", "scaleY": 1.15, "rotation": 109.06, "name": "hop2", "length": 158.16, "x": -415.48, "y": -147.9}, {"parent": "bone2", "name": "tien2", "x": 521.91, "y": 130.88}, {"parent": "bone2", "name": "tien4", "x": 622.55, "y": 120.13}, {"parent": "bone2", "name": "tien5", "x": 431.26, "y": 308.1}, {"scaleX": 0.851, "parent": "bone2", "scaleY": 0.851, "name": "tien3", "x": -555.85, "y": -4.73}, {"parent": "bone2", "name": "tien1", "x": -217.86, "y": 219.5}, {"scaleX": 1.168, "parent": "bone2", "scaleY": 1.168, "rotation": -99.91, "name": "tech", "x": 313.16, "y": -80.74}, {"parent": "bone2", "name": "theViettel", "x": 407.19, "y": 45.78}, {"parent": "bone2", "name": "line3", "x": 10.89, "y": -4.6}, {"parent": "root", "name": "txt3", "x": 8.95, "y": -222.23}, {"parent": "root", "name": "txt", "x": 47.08, "y": -164.75}, {"parent": "txt", "name": "t1"}, {"parent": "root", "name": "bai", "x": 319.63, "y": -11.77}, {"parent": "root", "name": "xx1", "x": 45.11, "y": 134.17}, {"parent": "root", "name": "xx3", "x": 271.61, "y": 2.32}, {"parent": "root", "name": "xx2", "x": -206.74, "y": 18.98}, {"parent": "root", "name": "vina", "x": -336.61, "y": 107.23}, {"parent": "root", "name": "muiten", "x": -656.57, "y": -164.24}, {"parent": "root", "rotation": -105.02, "name": "bai2", "x": -487.78, "y": 34.76}, {"parent": "txt", "name": "txt2"}, {"parent": "root", "name": "set1", "x": -479.04, "y": 207.21}, {"parent": "root", "name": "set2", "x": 85.6, "y": 482.93}], "animations": {"animation": {"slots": {"set3": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 1.0333}, {"color": "ffffffff", "time": 1.2}, {"color": "ffffff00", "time": 1.3667}, {"color": "ffffffff", "time": 1.5333}, {"color": "ffffff00", "time": 1.7}, {"color": "ffffff00", "curve": "stepped", "time": 1.8667}, {"color": "ffffff00", "time": 2.2667}, {"color": "ffffffff", "time": 2.4333}, {"color": "ffffff00", "time": 2.6}, {"color": "ffffffff", "time": 2.7667}, {"color": "ffffff00", "time": 2.9333}, {"color": "ffffff00", "time": 3.1}]}, "txt2": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 2.5333}, {"color": "ffffffff", "time": 2.6667}, {"color": "ffffff00", "time": 3}]}, "set2": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "curve": "stepped", "time": 0.2}, {"color": "ffffffff", "time": 0.7667}, {"color": "ffffff00", "curve": "stepped", "time": 0.9667}, {"color": "ffffff00", "time": 1}, {"color": "ffffffff", "curve": "stepped", "time": 1.2}, {"color": "ffffffff", "time": 1.7667}, {"color": "ffffff00", "curve": "stepped", "time": 1.9667}, {"color": "ffffff00", "time": 2}, {"color": "ffffffff", "curve": "stepped", "time": 2.2}, {"color": "ffffffff", "time": 2.7667}, {"color": "ffffff00", "curve": "stepped", "time": 2.9667}, {"color": "ffffff00", "time": 3}, {"color": "ffffffff", "curve": "stepped", "time": 3.2}, {"color": "ffffffff", "time": 3.7667}, {"color": "ffffff00", "time": 3.9667}]}, "set5": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3}, {"color": "ffffffff", "time": 0.4667}, {"color": "ffffff00", "time": 0.6333}, {"color": "ffffffff", "time": 0.8}, {"color": "ffffff00", "time": 0.9667}, {"color": "ffffff00", "curve": "stepped", "time": 1.1333}, {"color": "ffffff00", "time": 2.7667}, {"color": "ffffffff", "time": 2.9333}, {"color": "ffffff00", "time": 3.1}, {"color": "ffffffff", "time": 3.2667}, {"color": "ffffff00", "time": 3.4333}, {"color": "ffffff00", "time": 3.6}]}, "set4": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffff00", "time": 0.1667}, {"color": "ffffff00", "curve": "stepped", "time": 0.3333}, {"color": "ffffff00", "time": 1.4667}, {"color": "ffffffff", "time": 1.6333}, {"color": "ffffff00", "time": 1.8}, {"color": "ffffffff", "time": 1.9667}, {"color": "ffffff00", "time": 2.1333}, {"color": "ffffff00", "curve": "stepped", "time": 2.3}, {"color": "ffffff00", "time": 3.5}, {"color": "ffffffff", "time": 3.6667}, {"color": "ffffff00", "time": 3.8333}, {"color": "ffffffff", "time": 4}]}, "set1": {"color": [{"color": "ffffffff", "time": 0}, {"color": "ffffffa6", "time": 0.1667}, {"color": "ffffffff", "time": 0.3333}, {"color": "ffffffa6", "time": 0.5}, {"color": "ffffffff", "time": 0.6667}, {"color": "ffffffa6", "time": 0.8333}, {"color": "ffffffff", "time": 1}, {"color": "ffffffa6", "time": 1.1667}, {"color": "ffffffff", "time": 1.3333}, {"color": "ffffffa6", "time": 1.5}, {"color": "ffffffff", "time": 1.6667}, {"color": "ffffffa6", "time": 1.8333}, {"color": "ffffffff", "time": 2}, {"color": "ffffffa6", "time": 2.1667}, {"color": "ffffffff", "time": 2.3333}, {"color": "ffffffa6", "time": 2.5}, {"color": "ffffffff", "time": 2.6667}, {"color": "ffffffa6", "time": 2.8333}, {"color": "ffffffff", "time": 3}, {"color": "ffffffa6", "time": 3.1667}, {"color": "ffffffff", "time": 3.3333}, {"color": "ffffffa6", "time": 3.5}, {"color": "ffffffff", "time": 3.6667}, {"color": "ffffffa6", "time": 3.8333}, {"color": "ffffffff", "time": 4}]}, "line3": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "curve": "stepped", "time": 0.1667}, {"color": "ffffffff", "time": 0.4667}, {"color": "ffffff00", "curve": "stepped", "time": 0.7667}, {"color": "ffffff00", "time": 0.8}, {"color": "ffffffff", "curve": "stepped", "time": 0.9667}, {"color": "ffffffff", "time": 1.2667}, {"color": "ffffff00", "curve": "stepped", "time": 1.5667}, {"color": "ffffff00", "time": 1.6}, {"color": "ffffffff", "curve": "stepped", "time": 1.7667}, {"color": "ffffffff", "time": 2.0667}, {"color": "ffffff00", "curve": "stepped", "time": 2.3667}, {"color": "ffffff00", "time": 2.4}, {"color": "ffffffff", "curve": "stepped", "time": 2.5667}, {"color": "ffffffff", "time": 2.8667}, {"color": "ffffff00", "curve": "stepped", "time": 3.1667}, {"color": "ffffff00", "time": 3.2}, {"color": "ffffffff", "curve": "stepped", "time": 3.3667}, {"color": "ffffffff", "time": 3.6667}, {"color": "ffffff00", "time": 3.9667}]}, "t1": {"attachment": [{"name": "t1", "time": 0}, {"name": "t2", "time": 0.0667}, {"name": "t3", "time": 0.1333}, {"name": "t4", "time": 0.2}, {"name": "t5", "time": 0.2667}, {"name": "t6", "time": 0.3333}, {"name": "t7", "time": 0.4}, {"name": "t8", "time": 0.4667}, {"name": "t9", "time": 0.5333}, {"name": "t10", "time": 0.6}, {"name": "t11", "time": 0.6667}, {"name": "t12", "time": 0.7333}, {"name": "t13", "time": 0.8}, {"name": "t14", "time": 0.8667}, {"name": "t15", "time": 0.9333}, {"name": "t16", "time": 1}, {"name": "t17", "time": 1.0667}, {"name": "t18", "time": 1.1333}, {"name": null, "time": 1.2}, {"name": "t1", "time": 1.3333}, {"name": "t2", "time": 1.4}, {"name": "t3", "time": 1.4667}, {"name": "t4", "time": 1.5333}, {"name": "t5", "time": 1.6}, {"name": "t6", "time": 1.6667}, {"name": "t7", "time": 1.7333}, {"name": "t8", "time": 1.8}, {"name": "t9", "time": 1.8667}, {"name": "t10", "time": 1.9333}, {"name": "t11", "time": 2}, {"name": "t12", "time": 2.0667}, {"name": "t13", "time": 2.1333}, {"name": "t14", "time": 2.2}, {"name": "t15", "time": 2.2667}, {"name": "t16", "time": 2.3333}, {"name": "t17", "time": 2.4}, {"name": "t18", "time": 2.4667}, {"name": null, "time": 2.5333}]}, "muiten": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "curve": "stepped", "time": 0.1667}, {"color": "ffffffff", "time": 0.4667}, {"color": "ffffff00", "curve": "stepped", "time": 0.6333}, {"color": "ffffff00", "time": 0.6667}, {"color": "ffffffff", "curve": "stepped", "time": 0.8333}, {"color": "ffffffff", "time": 1.1333}, {"color": "ffffff00", "curve": "stepped", "time": 1.3}, {"color": "ffffff00", "time": 1.3333}, {"color": "ffffffff", "curve": "stepped", "time": 1.5}, {"color": "ffffffff", "time": 1.8}, {"color": "ffffff00", "curve": "stepped", "time": 1.9667}, {"color": "ffffff00", "time": 2}, {"color": "ffffffff", "curve": "stepped", "time": 2.1667}, {"color": "ffffffff", "time": 2.4667}, {"color": "ffffff00", "curve": "stepped", "time": 2.6333}, {"color": "ffffff00", "time": 2.6667}, {"color": "ffffffff", "curve": "stepped", "time": 2.8333}, {"color": "ffffffff", "time": 3.1333}, {"color": "ffffff00", "curve": "stepped", "time": 3.3}, {"color": "ffffff00", "time": 3.3333}, {"color": "ffffffff", "curve": "stepped", "time": 3.5}, {"color": "ffffffff", "time": 3.8}, {"color": "ffffff00", "time": 3.9667}]}}, "bones": {"tien1": {"rotate": [{"angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 15.96, "time": 1}, {"angle": 0, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 15.96, "time": 3}, {"angle": 0, "time": 4}], "translate": [{"curve": [0.371, 0.62, 0.71, 1], "x": 0, "y": 6.82, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 9.35, "time": 0.3333}, {"curve": [0.243, 0, 0.689, 0.75], "x": 0, "y": -16.83, "time": 1.3333}, {"curve": [0.371, 0.62, 0.71, 1], "x": 0, "y": 6.82, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 9.35, "time": 2.3333}, {"curve": [0.243, 0, 0.689, 0.75], "x": 0, "y": -16.83, "time": 3.3333}, {"x": 0, "y": 6.82, "time": 4}]}, "tech": {"rotate": [{"curve": [0.345, 0.37, 0.757, 1], "angle": -4.16, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -17.16, "time": 0.7}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.7}, {"curve": [0.345, 0.37, 0.757, 1], "angle": -4.16, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -17.16, "time": 2.7}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 3.7}, {"angle": -4.16, "time": 4}]}, "tien5": {"scale": [{"curve": [0.38, 0.59, 0.727, 1], "x": 1.02, "y": 1.02, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.2667}, {"curve": [0.242, 0, 0.663, 0.65], "x": 1.101, "y": 1.101, "time": 1.2667}, {"curve": [0.38, 0.59, 0.727, 1], "x": 1.02, "y": 1.02, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 2.2667}, {"curve": [0.242, 0, 0.663, 0.65], "x": 1.101, "y": 1.101, "time": 3.2667}, {"x": 1.02, "y": 1.02, "time": 4}], "translate": [{"curve": [0.366, 0.63, 0.703, 1], "x": 0, "y": 0.91, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.1333}, {"curve": [0.244, 0, 0.7, 0.79], "x": 0, "y": 13.09, "time": 1.1333}, {"curve": [0.366, 0.63, 0.703, 1], "x": 0, "y": 0.91, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.1333}, {"curve": [0.244, 0, 0.7, 0.79], "x": 0, "y": 13.09, "time": 3.1333}, {"x": 0, "y": 0.91, "time": 4}]}, "tien4": {"scale": [{"curve": [0.351, 0.4, 0.757, 1], "x": 1.045, "y": 1.045, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 1.16, "y": 1.16, "time": 0.6667}, {"curve": [0.265, 0, 0.618, 0.43], "x": 1, "y": 1, "time": 1.6667}, {"curve": [0.351, 0.4, 0.757, 1], "x": 1.045, "y": 1.045, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 1.16, "y": 1.16, "time": 2.6667}, {"curve": [0.265, 0, 0.618, 0.43], "x": 1, "y": 1, "time": 3.6667}, {"x": 1.045, "y": 1.045, "time": 4}]}, "set2": {"translate": [{"x": 0, "y": 0, "time": 0}, {"x": 0, "y": -35.36, "time": 0.9667}, {"x": 0, "y": 0, "time": 1}, {"x": 0, "y": -35.36, "time": 1.9667}, {"x": 0, "y": 0, "time": 2}, {"x": 0, "y": -35.36, "time": 2.9667}, {"x": 0, "y": 0, "time": 3}, {"x": 0, "y": -35.36, "time": 3.9667}]}, "tien3": {"rotate": [{"angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 30.76, "time": 1}, {"angle": 0, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 30.76, "time": 3}, {"angle": 0, "time": 4}]}, "bai2": {"rotate": [{"curve": [0.369, 0.63, 0.706, 1], "angle": 4.37, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.18, "time": 0.1333}, {"curve": [0.244, 0, 0.694, 0.77], "angle": -4.58, "time": 1.1333}, {"curve": [0.369, 0.63, 0.706, 1], "angle": 4.37, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.18, "time": 2.1333}, {"curve": [0.244, 0, 0.694, 0.77], "angle": -4.58, "time": 3.1333}, {"angle": 4.37, "time": 4}], "scale": [{"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 1.168, "y": 1.168, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 1.168, "y": 1.168, "time": 3}, {"x": 1, "y": 1, "time": 4}]}, "tien2": {"rotate": [{"curve": [0.371, 0.62, 0.71, 1], "angle": -3.33, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.1667}, {"curve": [0.243, 0, 0.689, 0.75], "angle": -34.48, "time": 1.1667}, {"curve": [0.371, 0.62, 0.71, 1], "angle": -3.33, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.1667}, {"curve": [0.243, 0, 0.689, 0.75], "angle": -34.48, "time": 3.1667}, {"angle": -3.33, "time": 4}], "scale": [{"x": 1, "y": 1, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0.931, "y": 0.931, "time": 1}, {"x": 1, "y": 1, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 0.931, "y": 0.931, "time": 3}, {"x": 1, "y": 1, "time": 4}]}, "bai": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.244, 0, 0.694, 0.77], "angle": -4.58, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2}, {"curve": [0.244, 0, 0.694, 0.77], "angle": -4.58, "time": 3}, {"angle": 0, "time": 4}], "scale": [{"curve": [0.382, 0.57, 0.735, 1], "x": 1.028, "y": 1.028, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1.1, "y": 1.1, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 2.3333}, {"curve": [0.243, 0, 0.649, 0.6], "x": 1.1, "y": 1.1, "time": 3.3333}, {"x": 1.028, "y": 1.028, "time": 4}]}, "xx1": {"rotate": [{"curve": [0.375, 0.62, 0.716, 1], "angle": -2.3, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": -17.7, "time": 0.8}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1.4667}, {"curve": [0.25, 0, 0.75, 1], "angle": -39.43, "time": 2.1333}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.8}, {"curve": [0.243, 0, 0.68, 0.71], "angle": -17.7, "time": 3.4667}, {"angle": -2.3, "time": 4}], "scale": [{"curve": [0.354, 0.41, 0.756, 1], "x": 1.041, "y": 1.041, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.4333}, {"curve": [0.25, 0, 0.75, 1], "x": 1.059, "y": 1.059, "time": 1.1}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 1.7667}, {"curve": [0.25, 0, 0.75, 1], "x": 1.059, "y": 1.059, "time": 2.4333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 3.1}, {"curve": [0.263, 0, 0.618, 0.43], "x": 1.059, "y": 1.059, "time": 3.7667}, {"x": 1.041, "y": 1.041, "time": 4}], "translate": [{"curve": [0.381, 0.55, 0.742, 1], "x": 0, "y": 16.6, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.2667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 45.14, "time": 0.9333}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -57.58, "time": 1.6}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 45.14, "time": 2.2667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.9333}, {"curve": [0.245, 0, 0.637, 0.56], "x": 0, "y": 45.14, "time": 3.6}, {"x": 0, "y": 16.6, "time": 4}]}, "vina": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.78, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 5.78, "time": 3}, {"angle": 0, "time": 4}]}, "xx3": {"rotate": [{"curve": [0.38, 0.59, 0.727, 1], "angle": 4.8, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": 23.69, "time": 1.2667}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.2667}, {"curve": [0.242, 0, 0.663, 0.65], "angle": 23.69, "time": 3.2667}, {"angle": 4.8, "time": 4}], "scale": [{"curve": [0.366, 0.63, 0.703, 1], "x": 1.012, "y": 1.012, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.1333}, {"curve": [0.25, 0, 0.75, 1], "x": 1.166, "y": 1.166, "time": 1.1333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 2.1333}, {"curve": [0.244, 0, 0.7, 0.79], "x": 1.166, "y": 1.166, "time": 3.1333}, {"x": 1.012, "y": 1.012, "time": 4}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 45.14, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 45.14, "time": 3}, {"x": 0, "y": 0, "time": 4}]}, "mm2": {"rotate": [{"curve": [0.366, 0.63, 0.703, 1], "angle": 1.3, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.1333}, {"curve": [0.244, 0, 0.7, 0.79], "angle": 18.65, "time": 1.1333}, {"curve": [0.366, 0.63, 0.703, 1], "angle": 1.3, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2.1333}, {"curve": [0.244, 0, 0.7, 0.79], "angle": 18.65, "time": 3.1333}, {"angle": 1.3, "time": 4}], "scale": [{"curve": [0.371, 0.62, 0.71, 1], "x": 1.008, "y": 1.008, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.1667}, {"curve": [0.243, 0, 0.689, 0.75], "x": 1.088, "y": 1.088, "time": 1.1667}, {"curve": [0.371, 0.62, 0.71, 1], "x": 1.008, "y": 1.008, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 2.1667}, {"curve": [0.243, 0, 0.689, 0.75], "x": 1.088, "y": 1.088, "time": 3.1667}, {"x": 1.008, "y": 1.008, "time": 4}], "translate": [{"curve": [0.378, 0.61, 0.722, 1], "x": 0, "y": -1.55, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.2333}, {"curve": [0.242, 0, 0.671, 0.68], "x": 0, "y": -9.29, "time": 1.2333}, {"curve": [0.378, 0.61, 0.722, 1], "x": 0, "y": -1.55, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.2333}, {"curve": [0.242, 0, 0.671, 0.68], "x": 0, "y": -9.29, "time": 3.2333}, {"x": 0, "y": -1.55, "time": 4}]}, "xx2": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -17.7, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -17.7, "time": 3}, {"angle": 0, "time": 4}], "scale": [{"curve": [0.311, 0.25, 0.757, 1], "x": 0.988, "y": 0.988, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0.875, "y": 0.875, "time": 0.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 1.8333}, {"curve": [0.25, 0, 0.75, 1], "x": 0.875, "y": 0.875, "time": 2.8333}, {"curve": [0.29, 0, 0.629, 0.38], "x": 1, "y": 1, "time": 3.8333}, {"x": 0.988, "y": 0.988, "time": 4}], "translate": [{"curve": [0.351, 0.4, 0.757, 1], "x": 0, "y": 12.81, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 45.14, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 45.14, "time": 2.6667}, {"curve": [0.265, 0, 0.618, 0.43], "x": 0, "y": 0, "time": 3.6667}, {"x": 0, "y": 12.81, "time": 4}]}, "txt2": {"scale": [{"x": 1, "y": 1, "time": 2.5333}, {"x": 1.06, "y": 1.06, "time": 3}]}, "txt3": {"scale": [{"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 1.05, "y": 1.05, "time": 0.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 1.05, "y": 1.05, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 1.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1.05, "y": 1.05, "time": 1.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 1.05, "y": 1.05, "time": 2.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 2.6667}, {"curve": [0.25, 0, 0.75, 1], "x": 1.05, "y": 1.05, "time": 3}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 3.3333}, {"curve": [0.25, 0, 0.75, 1], "x": 1.05, "y": 1.05, "time": 3.6667}, {"x": 1, "y": 1, "time": 4}]}, "hop2": {"rotate": [{"angle": 8.44, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.13, "time": 1}, {"angle": 8.44, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "angle": -4.13, "time": 3}, {"angle": 8.44, "time": 4}]}, "hop3": {"rotate": [{"curve": [0.335, 0.67, 0.669, 1], "angle": 1.45, "time": 0}, {"curve": [0.248, 0, 0.736, 0.94], "angle": 1.68, "time": 0.1667}, {"curve": [0.342, 0.57, 0.675, 0.9], "angle": -5.7, "time": 1.1667}, {"curve": [0.335, 0.67, 0.669, 1], "angle": 1.45, "time": 2}, {"curve": [0.248, 0, 0.736, 0.94], "angle": 1.68, "time": 2.1667}, {"curve": [0.342, 0.57, 0.675, 0.9], "angle": -5.7, "time": 3.1667}, {"angle": 1.45, "time": 4}], "translate": [{"curve": [0.382, 0.57, 0.735, 1], "x": 0, "y": 18.15, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25.35, "time": 0.3333}, {"curve": [0.243, 0, 0.649, 0.6], "x": 0, "y": 0, "time": 1.3333}, {"curve": [0.382, 0.57, 0.735, 1], "x": 0, "y": 18.15, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 25.35, "time": 2.3333}, {"curve": [0.243, 0, 0.649, 0.6], "x": 0, "y": 0, "time": 3.3333}, {"x": 0, "y": 18.15, "time": 4}]}, "line3": {"scale": [{"x": 1, "y": 1, "time": 0}, {"x": 1.2, "y": 1.2, "time": 0.7667}, {"x": 1, "y": 1, "time": 0.8}, {"x": 1.2, "y": 1.2, "time": 1.5667}, {"x": 1, "y": 1, "time": 1.6}, {"x": 1.2, "y": 1.2, "time": 2.3667}, {"x": 1, "y": 1, "time": 2.4}, {"x": 1.2, "y": 1.2, "time": 3.1667}, {"x": 1, "y": 1, "time": 3.2}, {"x": 1.2, "y": 1.2, "time": 3.9667}]}, "theViettel": {"shear": [{"curve": [0.371, 0.62, 0.71, 1], "x": 0, "y": -1.14, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -11.77, "time": 1.1667}, {"curve": [0.371, 0.62, 0.71, 1], "x": 0, "y": -1.14, "time": 2}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 2.1667}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": -11.77, "time": 3.1667}, {"x": 0, "y": -1.14, "time": 4}]}, "muiten": {"translate": [{"x": 0, "y": 0, "time": 0}, {"x": 55.94, "y": 0, "time": 0.6333}, {"x": 0, "y": 0, "time": 0.6667}, {"x": 55.94, "y": 0, "time": 1.3}, {"x": 0, "y": 0, "time": 1.3333}, {"x": 55.94, "y": 0, "time": 1.9667}, {"x": 0, "y": 0, "time": 2}, {"x": 55.94, "y": 0, "time": 2.6333}, {"x": 0, "y": 0, "time": 2.6667}, {"x": 55.94, "y": 0, "time": 3.3}, {"x": 0, "y": 0, "time": 3.3333}, {"x": 55.94, "y": 0, "time": 3.9667}]}}}}}