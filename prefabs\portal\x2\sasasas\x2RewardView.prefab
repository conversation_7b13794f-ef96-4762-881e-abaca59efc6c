[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false}, {"__type__": "cc.Node", "_name": "x2RewardView", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 7}, {"__id__": 10}, {"__id__": 13}, {"__id__": 30}, {"__id__": 77}, {"__id__": 88}, {"__id__": 100}, {"__id__": 107}], "_active": true, "_level": 2, "_components": [{"__id__": 114}, {"__id__": 76}], "_prefab": {"__id__": 115}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "black copy", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 3}, {"__id__": 4}], "_prefab": {"__id__": 6}, "_opacity": 200, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 3000, "height": 3000}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 10, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a95690f4-0cfe-4b25-840f-5c84d9abeba3"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 5}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "2cad558tSlLgajwXGxo9v8H", "handler": "closeFinished", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "c17VnPOztFSpiu/LuLL4LU", "sync": false}, {"__type__": "cc.Node", "_name": "bgx2", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 8}], "_prefab": {"__id__": 9}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 790, "height": 558}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "c49a236c-0173-4e8e-9cd5-f21ad7ee5368"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "00g7xr4zpFpI+x0s1Wfs5V", "sync": false}, {"__type__": "cc.Node", "_name": "title_x2", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 11}], "_prefab": {"__id__": 12}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 361, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 275, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "036eb99e-16e3-40bf-8aa6-80d479fdc338"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "f7a0RWPkhOka/BpGyUG5p0", "sync": false}, {"__type__": "cc.Node", "_name": "layout-tab", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 14}, {"__id__": 21}], "_active": true, "_level": 3, "_components": [{"__id__": 28}], "_prefab": {"__id__": 29}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 167, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "btnProgress", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [{"__id__": 15}], "_active": true, "_level": 4, "_components": [{"__id__": 18}], "_prefab": {"__id__": 20}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-70, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "sprite", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [], "_active": true, "_level": 5, "_components": [{"__id__": 16}], "_prefab": {"__id__": 17}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 136, "height": 52}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ef306b0a-e451-409d-9816-d05cc72891c0"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "7ep3NdEe9Bk77+kVWhiC5z", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "duration": 0.1, "zoomScale": 1.1, "clickEvents": [{"__id__": 19}], "_N$interactable": false, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": {"__uuid__": "33625d9c-48c7-428b-82e5-0bbc782bbe35"}, "_N$pressedSprite": {"__uuid__": "33625d9c-48c7-428b-82e5-0bbc782bbe35"}, "pressedSprite": {"__uuid__": "33625d9c-48c7-428b-82e5-0bbc782bbe35"}, "_N$hoverSprite": {"__uuid__": "33625d9c-48c7-428b-82e5-0bbc782bbe35"}, "hoverSprite": {"__uuid__": "33625d9c-48c7-428b-82e5-0bbc782bbe35"}, "_N$disabledSprite": {"__uuid__": "ef306b0a-e451-409d-9816-d05cc72891c0"}, "_N$target": {"__id__": 15}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "dd9b5M4Ld9MEqOTBY/Exn/n", "handler": "progressTabClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "92s4KJA7tKf6JMjIr90GuU", "sync": false}, {"__type__": "cc.Node", "_name": "btnRules", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [{"__id__": 22}], "_active": true, "_level": 4, "_components": [{"__id__": 25}], "_prefab": {"__id__": 27}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [70, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "sprite", "_objFlags": 0, "_parent": {"__id__": 21}, "_children": [], "_active": true, "_level": 5, "_components": [{"__id__": 23}], "_prefab": {"__id__": 24}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 136, "height": 52}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "7bb3ad3e-677c-43ec-a075-0c71c2f8eaed"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "27ptBLsLNKEoXDKHtVN9XG", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "duration": 0.1, "zoomScale": 1.1, "clickEvents": [{"__id__": 26}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": {"__uuid__": "7bb3ad3e-677c-43ec-a075-0c71c2f8eaed"}, "_N$pressedSprite": {"__uuid__": "7bb3ad3e-677c-43ec-a075-0c71c2f8eaed"}, "pressedSprite": {"__uuid__": "7bb3ad3e-677c-43ec-a075-0c71c2f8eaed"}, "_N$hoverSprite": {"__uuid__": "7bb3ad3e-677c-43ec-a075-0c71c2f8eaed"}, "hoverSprite": {"__uuid__": "7bb3ad3e-677c-43ec-a075-0c71c2f8eaed"}, "_N$disabledSprite": {"__uuid__": "72402153-9d30-4c0b-884f-ca205af191d5"}, "_N$target": {"__id__": 22}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "dd9b5M4Ld9MEqOTBY/Exn/n", "handler": "ruleTabClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "3dYG/YrVJEl70RtFqYyZnk", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": false, "_layoutSize": {"__type__": "cc.Size", "width": 280, "height": 70}, "_resize": 1, "_N$layoutType": 1, "_N$padding": 0, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "c33EgkEIVLpqHA5PjewUPA", "sync": false}, {"__type__": "cc.Node", "_name": "item", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 31}, {"__id__": 34}, {"__id__": 37}, {"__id__": 40}, {"__id__": 47}, {"__id__": 50}, {"__id__": 57}, {"__id__": 60}, {"__id__": 63}, {"__id__": 66}, {"__id__": 69}, {"__id__": 72}], "_active": true, "_level": 3, "_components": [{"__id__": 75}], "_prefab": {"__id__": 99}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 700, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "icon", "_objFlags": 0, "_parent": {"__id__": 30}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 32}], "_prefab": {"__id__": 33}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 111, "height": 65}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-260, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 31}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "e5e0beea-ba00-430a-826c-b8569d3e1816"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "75Qqa7klZMmLvy+Lnu7k5t", "sync": false}, {"__type__": "cc.Node", "_name": "textbox", "_objFlags": 0, "_parent": {"__id__": 30}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 35}], "_prefab": {"__id__": 36}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 157, "height": 29}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-101, 8, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "0d7648bf-4365-4b37-9e62-f3a8b4255685"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "24eKaHKp5NxKZT9MrTwgLh", "sync": false}, {"__type__": "cc.Node", "_name": "textbox copy", "_objFlags": 0, "_parent": {"__id__": 30}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 38}], "_prefab": {"__id__": 39}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 157, "height": 29}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [104, 8, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 37}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "0d7648bf-4365-4b37-9e62-f3a8b4255685"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "1bPhPowdNDmKAynKVMVZhx", "sync": false}, {"__type__": "cc.Node", "_name": "progressBar", "_objFlags": 0, "_parent": {"__id__": 30}, "_children": [{"__id__": 41}], "_active": true, "_level": 6, "_components": [{"__id__": 44}, {"__id__": 45}], "_prefab": {"__id__": 46}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 391, "height": 16}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-13.7, -30, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 0, "_parent": {"__id__": 40}, "_children": [], "_active": true, "_level": 0, "_components": [{"__id__": 42}], "_prefab": {"__id__": 43}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 391, "height": 17}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-195.4, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d022f9e7-723c-4998-ad4d-798300d9b687"}, "_type": 3, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0.6, "_isTrimmedMode": false, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "dcsM55DypPxKZizSVsjDmN", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "91827f21-c00a-472c-a640-06ebe19c502c"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "_N$totalLength": 1, "_N$barSprite": {"__id__": 42}, "_N$mode": 2, "_N$progress": 0.6, "_N$reverse": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "80pLQsewZI/4LyCjFCmtPn", "sync": false}, {"__type__": "cc.Node", "_name": "homvang_on", "_objFlags": 0, "_parent": {"__id__": 30}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 48}], "_prefab": {"__id__": 49}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [192.9, -28.9, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 47}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "c122078c-035f-4b60-9530-8ab190064eb2"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "f0YB0ar9hCqJDXCtUXo9Tr", "sync": false}, {"__type__": "cc.Node", "_name": "btnClaim", "_objFlags": 0, "_parent": {"__id__": 30}, "_children": [{"__id__": 51}], "_active": true, "_level": 6, "_components": [{"__id__": 54}], "_prefab": {"__id__": 56}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 110, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [273, -19, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "sprite", "_objFlags": 0, "_parent": {"__id__": 50}, "_children": [], "_active": true, "_level": 5, "_components": [{"__id__": 52}], "_prefab": {"__id__": 53}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 114, "height": 52}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f2c61df1-1044-4039-b867-c657dfd15d53"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "d7FKvp2AFLZod+qC+Q2stl", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "duration": 0.1, "zoomScale": 1.1, "clickEvents": [{"__id__": 55}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 50}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 30}, "component": "", "_componentId": "c7b8bkhMBFD6qRPm4/Sb66W", "handler": "claimClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "bb94/gpvZDKpq595BljRlr", "sync": false}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "_parent": {"__id__": 30}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 58}], "_prefab": {"__id__": 59}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 60.75, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-101, 32, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "_useOriginalSize": false, "_string": "Đ<PERSON>", "_N$string": "Đ<PERSON>", "_fontSize": 18, "_lineHeight": 60, "_enableWrapText": true, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "5c1BOrTB9GPrbfI+oKf45T", "sync": false}, {"__type__": "cc.Node", "_name": "lbTopup", "_objFlags": 0, "_parent": {"__id__": 30}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 61}], "_prefab": {"__id__": 62}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-101, 5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 60}, "_enabled": true, "_useOriginalSize": false, "_string": "", "_N$string": "", "_fontSize": 20, "_lineHeight": 60, "_enableWrapText": true, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "4aLyFveAVPhY8XnbdAVXda", "sync": false}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "_parent": {"__id__": 30}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 64}], "_prefab": {"__id__": 65}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 63, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [104, 32, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": true, "_useOriginalSize": false, "_string": "Thưởng", "_N$string": "Thưởng", "_fontSize": 18, "_lineHeight": 60, "_enableWrapText": true, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "1bC7Lzab9JfJ5vcQHT1XEF", "sync": false}, {"__type__": "cc.Node", "_name": "lbReward", "_objFlags": 0, "_parent": {"__id__": 30}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 67}], "_prefab": {"__id__": 68}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [104, 5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 66}, "_enabled": true, "_useOriginalSize": false, "_string": "", "_N$string": "", "_fontSize": 20, "_lineHeight": 60, "_enableWrapText": true, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "70Tt4U3bFDX5pdN2kO4G2R", "sync": false}, {"__type__": "cc.Node", "_name": "lbProgress", "_objFlags": 0, "_parent": {"__id__": 30}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 70}], "_prefab": {"__id__": 71}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2, -15, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "_useOriginalSize": false, "_string": "", "_N$string": "", "_fontSize": 18, "_lineHeight": 60, "_enableWrapText": true, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "2eXYKKQQRNTI8T1URfn+i5", "sync": false}, {"__type__": "cc.Node", "_name": "lbTime", "_objFlags": 0, "_parent": {"__id__": 30}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 73}], "_prefab": {"__id__": 74}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 239, "g": 200, "b": 34, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [271, 15, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 72}, "_enabled": true, "_useOriginalSize": false, "_string": "", "_N$string": "", "_fontSize": 16, "_lineHeight": 60, "_enableWrapText": true, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "9e93T88wFLI5SnD/tJ+SI3", "sync": false}, {"__type__": "c7b8bkhMBFD6qRPm4/Sb66W", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "x2RewardView": {"__id__": 76}, "spriteIcon": {"__id__": 32}, "spriteTreasure": {"__id__": 48}, "spriteClaim": {"__id__": 52}, "progressBar": {"__id__": 45}, "btnClaim": {"__id__": 54}, "lbTopUp": {"__id__": 61}, "lbReward": {"__id__": 67}, "lbProgress": {"__id__": 70}, "lbRemaining": {"__id__": 73}, "sfCards": [{"__uuid__": "e5e0beea-ba00-430a-826c-b8569d3e1816"}, {"__uuid__": "5087f786-9288-4838-b3e8-b64e8573d0a3"}], "sfMoMos": [{"__uuid__": "8747e892-a3af-4230-90c3-e7098af77a35"}, {"__uuid__": "c306daff-81c0-431e-9830-186a43c2700c"}], "sfBanks": [{"__uuid__": "2a165c97-671f-49a6-b563-e309582f77a1"}, {"__uuid__": "********-ea1a-4cfe-8572-35d2afe576ab"}], "sfAgencys": [{"__uuid__": "214cf7cc-7a07-4c69-bad7-704f439647ee"}, {"__uuid__": "9c726ff1-0a10-4c9c-8103-0b5283fb6569"}], "sfTreasureOn": {"__uuid__": "c122078c-035f-4b60-9530-8ab190064eb2"}, "sfTreasureOff": {"__uuid__": "4ccbc6eb-eed0-4050-9054-a6f424428a8f"}, "sfReceiveOn": {"__uuid__": "f2c61df1-1044-4039-b867-c657dfd15d53"}, "sfReceiveOff": {"__uuid__": "f7b451bc-7db0-40b7-b886-5840b4c94061"}, "sfReceived": {"__uuid__": "f7b451bc-7db0-40b7-b886-5840b4c94061"}, "_id": ""}, {"__type__": "dd9b5M4Ld9MEqOTBY/Exn/n", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "nodeProgress": {"__id__": 77}, "nodeRules": {"__id__": 88}, "btnProgress": {"__id__": 18}, "btnRules": {"__id__": 25}, "nodeParent": {"__id__": 80}, "nodeItemTemp": {"__id__": 30}, "_id": ""}, {"__type__": "cc.Node", "_name": "nodeProgress", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 78}], "_active": true, "_level": 3, "_components": [], "_prefab": {"__id__": 87}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "scrollview copy", "_objFlags": 0, "_parent": {"__id__": 77}, "_children": [{"__id__": 79}], "_active": true, "_level": 4, "_components": [{"__id__": 85}], "_prefab": {"__id__": 86}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 700, "height": 350}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 78}, "_children": [{"__id__": 80}], "_active": true, "_level": 0, "_components": [{"__id__": 83}], "_prefab": {"__id__": 84}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 700, "height": 350}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "layout", "_objFlags": 0, "_parent": {"__id__": 79}, "_children": [], "_active": true, "_level": 1, "_components": [{"__id__": 81}], "_prefab": {"__id__": 82}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 700, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 175, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 80}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 700, "height": 100}, "_resize": 1, "_N$layoutType": 2, "_N$padding": 0, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "522ZTtzO5I2L8e/P3dyjvS", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 79}, "_enabled": true, "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "f8unsJ6BVJsbO275O9XrVv", "sync": false}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": true, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 80}, "content": {"__id__": 80}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "68rq3jrWpO6L1unJHSTZap", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "d5cncnNhFAzJRfGQjqrgDu", "sync": false}, {"__type__": "cc.Node", "_name": "nodeRules", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 89}], "_active": false, "_level": 3, "_components": [], "_prefab": {"__id__": 98}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "scrollview", "_objFlags": 0, "_parent": {"__id__": 88}, "_children": [{"__id__": 90}], "_active": true, "_level": 4, "_components": [{"__id__": 96}], "_prefab": {"__id__": 97}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 700, "height": 350}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 89}, "_children": [{"__id__": 91}], "_active": true, "_level": 0, "_components": [{"__id__": 94}], "_prefab": {"__id__": 95}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 700, "height": 350}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "theletext", "_objFlags": 0, "_parent": {"__id__": 90}, "_children": [], "_active": true, "_level": 1, "_components": [{"__id__": 92}], "_prefab": {"__id__": 93}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 663, "height": 501}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [5, 166, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 91}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "17522ab0-6a0f-4e55-90e5-a0d165ed2f73"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "85qqozhHhKM5CHvzwYMAu+", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 90}, "_enabled": true, "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "6c6z3R5ndP77T0A4SjA/6H", "sync": false}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 89}, "_enabled": true, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": true, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 91}, "content": {"__id__": 91}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "a5wCb67eZOYLSdUDBoyyM2", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "a4VMSIjg5OyL5Uf2Lem0ar", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "3bSxVNNdBCLaPiEIs1Ya+r", "sync": false}, {"__type__": "cc.Node", "_name": "btnClose", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 101}], "_active": true, "_level": 3, "_components": [{"__id__": 104}], "_prefab": {"__id__": 106}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [361, 240, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "btn_back", "_objFlags": 0, "_parent": {"__id__": 100}, "_children": [], "_active": true, "_level": 5, "_components": [{"__id__": 102}], "_prefab": {"__id__": 103}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 101}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6ac8c281-cdbe-41e7-a5d1-8325fa9c3778"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "96yHJT+tpBAb7EW3K06AvA", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 100}, "_enabled": true, "duration": 0.1, "zoomScale": 1.1, "clickEvents": [{"__id__": 105}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 100}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "dd9b5M4Ld9MEqOTBY/Exn/n", "handler": "closeFinished", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "9741fc9CxJuLkD4R0OR0ze", "sync": false}, {"__type__": "cc.Node", "_name": "btnTopup", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 108}], "_active": true, "_level": 3, "_components": [{"__id__": 111}], "_prefab": {"__id__": 113}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -288, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "sprite", "_objFlags": 0, "_parent": {"__id__": 107}, "_children": [], "_active": true, "_level": 5, "_components": [{"__id__": 109}], "_prefab": {"__id__": 110}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 237, "height": 91}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 108}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b350558f-3d99-4dc3-848a-b54fb2fdd200"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "67zYUjdvZEsajllWPR8Vgd", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 107}, "_enabled": true, "duration": 0.1, "zoomScale": 1.1, "clickEvents": [{"__id__": 112}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 107}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "dd9b5M4Ld9MEqOTBY/Exn/n", "handler": "topupClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "82imJZyC1K7pAmEocEf5Ax", "sync": false}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_defaultClip": {"__uuid__": "fd368a1d-24a5-4b8f-8745-d532e9a6fda0"}, "_clips": [{"__uuid__": "fd368a1d-24a5-4b8f-8745-d532e9a6fda0"}, {"__uuid__": "2c581fef-58f4-478d-adee-4138a71c7df4"}], "playOnLoad": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3adeaa9b-a772-42df-87aa-11fb3f91452e"}, "fileId": "c51TvZ8H9GVaUbcj4vtT+P", "sync": false}]