{"skeleton": {"hash": "1AjxCHMIMzyGCq5NHxgWIwmkAl0", "spine": "3.6.53", "width": 151.57, "height": 182.34}, "bones": [{"name": "root"}, {"name": "body", "parent": "root", "length": 62.37, "rotation": 90.42, "x": 2.29, "y": 6.68}, {"name": "body2", "parent": "body", "x": 29.76, "y": 26.11}, {"name": "body3", "parent": "body", "x": 27.99, "y": -25.62}, {"name": "btay_p", "parent": "body", "length": 60.3, "rotation": -152.92, "x": 60.65, "y": -37.99}, {"name": "btay_t", "parent": "body", "length": 59.6, "rotation": 156.43, "x": 66.11, "y": 42.41}, {"name": "dau", "parent": "body", "length": 39.18, "rotation": 0.84, "x": 89.59, "y": -0.03}, {"name": "dau2", "parent": "dau", "length": 19.53, "rotation": -128.5, "x": -2.22, "y": -22.24}, {"name": "dau3", "parent": "dau2", "length": 25.09, "rotation": -5.76, "x": 11.38, "y": -19.09}, {"name": "ktay_p", "parent": "btay_p", "length": 37.26, "rotation": -95.2, "x": 59.89, "y": -0.78}, {"name": "ktay_t", "parent": "btay_t", "length": 37.56, "rotation": 86.58, "x": 60.59, "y": -0.06}, {"name": "tayT1", "parent": "ktay_t", "length": 31.69, "rotation": 3.58, "x": 39.53, "y": -0.99}, {"name": "tayp", "parent": "ktay_p", "length": 24.91, "rotation": 5.17, "x": 43.66, "y": -0.71}, {"name": "toc", "parent": "dau", "length": 20.11, "rotation": -149.54, "x": 46.66, "y": -6.21}], "slots": [{"name": "body", "bone": "body", "attachment": "body"}, {"name": "btay_p", "bone": "btay_p", "attachment": "btay_p"}, {"name": "btay_t", "bone": "btay_t", "attachment": "btay_t"}, {"name": "ktay_p", "bone": "ktay_p", "attachment": "ktay_p"}, {"name": "tayp", "bone": "tayp", "attachment": "tayp"}, {"name": "tayT1", "bone": "tayT1", "attachment": "tayT1"}, {"name": "ktay_t", "bone": "ktay_t", "attachment": "ktay_t"}, {"name": "dau", "bone": "dau", "attachment": "dau"}, {"name": "toc", "bone": "toc", "attachment": "toc"}], "skins": {"default": {"body": {"body": {"type": "mesh", "uvs": [0.63726, 0.06755, 0.74761, 0.12056, 0.89807, 0.15001, 1, 0.3002, 1, 0.44745, 0.87801, 0.50635, 0.86464, 0.63593, 0.83673, 0.68048, 0.81114, 0.72133, 0.80111, 0.90686, 0.55978, 0.94531, 0.28587, 0.93897, 0.09124, 0.92627, 0.09124, 0.78342, 0.06241, 0.71042, 0, 0.63106, 0, 0.50409, 0, 0.15001, 0.18251, 0.10583, 0.25273, 0.06166, 0.28617, 0, 0.58376, 0, 0.18337, 0.58978, 0.72338, 0.58713, 0.23438, 0.74302, 0.44138, 0.65847, 0.62138, 0.7483, 0.45938, 0.48144, 0.51938, 0.39689, 0.66938, 0.30706, 0.73238, 0.30441, 0.84038, 0.43388, 0.40838, 0.39689, 0.21638, 0.31762, 0.15338, 0.30441, 0.07538, 0.40746], "triangles": [1, 29, 0, 30, 1, 2, 30, 2, 3, 34, 17, 18, 30, 29, 1, 33, 18, 19, 34, 18, 33, 19, 21, 0, 21, 19, 20, 28, 0, 29, 0, 32, 19, 32, 33, 19, 0, 28, 32, 35, 17, 34, 31, 30, 3, 31, 3, 4, 27, 32, 28, 16, 17, 35, 5, 31, 4, 31, 23, 29, 31, 29, 30, 23, 31, 5, 28, 29, 23, 27, 28, 23, 33, 35, 34, 22, 33, 32, 22, 32, 27, 22, 35, 33, 16, 35, 22, 15, 16, 22, 6, 23, 5, 25, 22, 27, 25, 27, 23, 7, 23, 6, 14, 15, 22, 7, 26, 23, 24, 22, 25, 14, 22, 24, 26, 25, 23, 8, 26, 7, 13, 14, 24, 9, 26, 8, 12, 13, 24, 11, 24, 25, 12, 24, 11, 10, 25, 26, 10, 26, 9, 11, 25, 10], "vertices": [2, 1, 85.32, -18.52, 0.87624, 3, 57.33, 7.1, 0.12376, 2, 1, 79.46, -29.07, 0.67496, 3, 51.47, -3.45, 0.32504, 2, 1, 76.15, -43.49, 0.46826, 3, 48.15, -17.87, 0.53174, 2, 1, 59.7, -53.15, 0.27667, 3, 31.71, -27.53, 0.72333, 2, 1, 43.65, -53.03, 0.16489, 3, 15.66, -27.41, 0.83511, 2, 1, 37.32, -41.27, 0.08741, 3, 9.33, -15.65, 0.91259, 2, 1, 23.21, -39.88, 0.00155, 3, -4.79, -14.27, 0.99845, 2, 1, 18.37, -37.17, 0.03555, 3, -9.62, -11.55, 0.96445, 2, 1, 13.94, -34.68, 0.13938, 3, -14.06, -9.06, 0.86062, 2, 1, -6.28, -33.57, 0.48671, 3, -34.27, -7.95, 0.51329, 2, 1, -10.3, -10.37, 0.87809, 3, -38.29, 15.25, 0.12191, 2, 1, -9.41, 15.92, 0.79859, 2, -39.17, -10.19, 0.20141, 2, 1, -7.89, 34.59, 0.57458, 2, -37.65, 8.48, 0.42542, 2, 1, 7.68, 34.48, 0.37069, 2, -22.08, 8.36, 0.62931, 2, 1, 15.66, 37.19, 0.1379, 2, -14.1, 11.07, 0.8621, 2, 1, 24.35, 43.11, 0.00591, 2, -5.41, 17, 0.99409, 2, 1, 38.19, 43.01, 0.03702, 2, 8.43, 16.9, 0.96298, 2, 1, 76.79, 42.73, 0.56392, 2, 47.02, 16.61, 0.43608, 2, 1, 81.47, 25.17, 0.76459, 2, 51.71, -0.94, 0.23541, 2, 1, 86.24, 18.39, 0.88848, 2, 56.47, -7.72, 0.11152, 3, 1, 92.93, 15.13, 0.93498, 2, 63.17, -10.98, 0.06501, 3, 64.94, 40.75, 1e-05, 3, 1, 92.72, -13.43, 0.94172, 2, 62.96, -39.55, 0.00018, 3, 64.73, 12.18, 0.0581, 2, 1, 28.72, 25.48, 0.02244, 2, -1.04, -0.64, 0.97756, 1, 3, 0.63, -0.74, 1, 2, 1, 11.98, 20.71, 0.50802, 2, -17.78, -5.41, 0.49198, 2, 1, 21.05, 0.77, 0.9941, 2, -8.71, -25.35, 0.0059, 2, 1, 11.13, -16.44, 0.58174, 3, -16.86, 9.18, 0.41826, 2, 1, 40.33, -1.11, 0.98989, 3, 12.34, 24.51, 0.01011, 2, 1, 49.51, -6.93, 0.90675, 3, 21.51, 18.68, 0.09325, 2, 1, 59.19, -21.41, 0.64727, 3, 31.2, 4.21, 0.35273, 2, 1, 59.43, -27.46, 0.53094, 3, 31.44, -1.84, 0.46906, 2, 1, 45.25, -37.72, 0.18661, 3, 17.25, -12.1, 0.81339, 2, 1, 49.59, 3.72, 0.96186, 2, 19.82, -22.39, 0.03814, 2, 1, 58.36, 22.09, 0.61013, 2, 28.6, -4.02, 0.38987, 2, 1, 59.85, 28.13, 0.51922, 2, 30.08, 2.01, 0.48078, 2, 1, 48.67, 35.7, 0.22288, 2, 18.91, 9.58, 0.77712], "hull": 22}}, "btay_p": {"btay_p": {"x": 29.53, "y": 0.58, "rotation": 62.5, "width": 46, "height": 77}}, "btay_t": {"btay_t": {"x": 29.16, "y": 2.05, "rotation": 113.14, "width": 46, "height": 76}}, "dau": {"dau": {"type": "mesh", "uvs": [0.7527, 0.10201, 0.88029, 0.39408, 0.9401, 0.50766, 0.87565, 0.55887, 1, 0.62665, 1, 0.85922, 1, 1, 0.66897, 1, 0.48157, 0.84029, 0.35398, 0.66181, 0.07682, 0.64698, 0, 0.64288, 0, 0.50225, 0, 0.221, 0.08285, 0.09119, 0.1626, 0.05603, 0.22241, 0, 0.55334, 0, 0.56929, 0.52388, 0.661, 0.68615, 0.9401, 0.76728, 0.58125, 0.44546, 0.79656, 0.51577, 0.21044, 0.17773, 0.37392, 0.17502, 0.50948, 0.29131, 0.54935, 0.4076, 0.51347, 0.49955, 0.35398, 0.56715, 0.20247, 0.48602, 0.14665, 0.41301, 0.13469, 0.26427], "triangles": [24, 16, 17, 23, 15, 16, 24, 23, 16, 14, 15, 23, 31, 13, 14, 23, 31, 14, 25, 24, 17, 25, 17, 0, 26, 25, 0, 1, 26, 0, 30, 31, 23, 21, 26, 1, 24, 29, 30, 27, 25, 26, 27, 26, 21, 30, 12, 13, 30, 13, 31, 22, 21, 1, 22, 1, 2, 18, 27, 21, 18, 21, 22, 3, 22, 2, 23, 24, 30, 25, 29, 24, 28, 25, 27, 25, 28, 29, 12, 10, 11, 29, 12, 30, 29, 10, 12, 9, 29, 28, 10, 29, 9, 19, 18, 22, 20, 3, 4, 19, 22, 3, 20, 19, 3, 9, 27, 18, 27, 9, 28, 19, 9, 18, 19, 8, 9, 20, 4, 5, 7, 19, 20, 7, 20, 5, 8, 19, 7, 7, 5, 6], "vertices": [2, 6, 48.59, -31.76, 0.82168, 7, -24.18, 45.69, 0.17832, 2, 6, 14.79, -40.97, 0.16767, 7, 4.07, 24.97, 0.83233, 2, 6, 1.63, -45.35, 0.01435, 7, 15.69, 17.39, 0.98565, 2, 6, -4.15, -40.19, 0.00088, 7, 15.25, 9.66, 0.99912, 2, 7, 27.69, 9.32, 0.9766, 8, 13.38, 29.9, 0.0234, 2, 7, 43.87, -11.97, 0.11036, 8, 31.61, 10.34, 0.88964, 1, 8, 42.65, -1.5, 1, 1, 8, 23.77, -19.11, 1, 2, 6, -35.83, -8.75, 0.01667, 8, 0.55, -15.64, 0.98333, 3, 6, -15.09, 0.75, 0.65358, 7, -9.98, -24.38, 0.04877, 8, -20.72, -7.41, 0.29765, 2, 6, -12.9, 22.32, 0.99829, 8, -37.7, -20.91, 0.00171, 1, 6, -12.3, 28.3, 1, 1, 6, 3.87, 27.95, 1, 1, 6, 36.2, 27.23, 1, 1, 6, 50.99, 20.44, 1, 1, 6, 54.89, 14.14, 1, 1, 6, 61.23, 9.33, 1, 2, 6, 60.66, -16.48, 0.95479, 7, -43.65, 45.62, 0.04521, 3, 6, 0.4, -16.39, 0.24456, 7, -6.21, -1.59, 0.73292, 8, -19.26, 15.64, 0.02252, 3, 6, -18.41, -23.13, 0.00849, 7, 10.78, -12.12, 0.37312, 8, -1.3, 6.87, 0.61839, 2, 7, 33.75, -6.38, 0.44726, 8, 20.99, 14.89, 0.55274, 2, 6, 9.4, -17.52, 0.40426, 7, -10.92, 6.15, 0.59574, 2, 6, 0.94, -34.13, 0.01365, 7, 7.34, 9.87, 0.98635, 1, 6, 40.82, 10.71, 1, 2, 6, 40.85, -2.04, 0.99447, 7, -42.62, 21.13, 0.00553, 2, 6, 27.24, -12.32, 0.83772, 7, -26.11, 16.88, 0.16228, 2, 6, 13.8, -15.13, 0.58184, 7, -15.54, 8.11, 0.41816, 3, 6, 3.3, -12.1, 0.51767, 7, -11.37, -2, 0.46854, 8, -24.35, 14.72, 0.01379, 3, 6, -4.2, 0.51, 0.91909, 7, -16.57, -15.72, 0.0274, 8, -28.15, 0.55, 0.05352, 1, 6, 5.38, 12.12, 1, 1, 6, 13.88, 16.29, 1, 1, 6, 31, 16.84, 1], "hull": 18}}, "ktay_p": {"ktay_p": {"x": 23.22, "y": 0.21, "rotation": 157.69, "width": 66, "height": 34}}, "ktay_t": {"ktay_t": {"x": 20.22, "y": -0.98, "rotation": 26.57, "width": 52, "height": 39}}, "tayT1": {"tayT1": {"x": 21.94, "y": -0.59, "rotation": 22.99, "width": 43, "height": 28}, "tayT2": {"x": 20.56, "y": -2.2, "rotation": 22.99, "width": 44, "height": 34}}, "tayp": {"tayp": {"x": 17.14, "y": -0.15, "rotation": 152.53, "width": 42, "height": 31}}, "toc": {"toc": {"x": 20.06, "y": -2.5, "rotation": 58.28, "width": 50, "height": 50}}}}, "animations": {"animation": {"bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}]}, "body": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.8333, "x": 0, "y": 1.15}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}]}, "body2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.8333, "x": 1.3, "y": 1.67}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}]}, "body3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.8333, "x": 0.55, "y": -3.46}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}]}, "dau": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 1.22}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}]}, "toc": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.8333, "angle": 7.11}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}]}, "dau2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.8333, "angle": 6.7}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.8333, "x": 3.26, "y": -2.92}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}]}, "dau3": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.8333, "angle": 11.18}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.8333, "x": -2.31, "y": -3.63}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}]}, "btay_t": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.8333, "x": 1.07, "y": 0}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}]}, "ktay_t": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}]}, "tayT1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}]}, "btay_p": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}]}, "ktay_p": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}]}, "tayp": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}]}}}, "deal": {"slots": {"tayT1": {"attachment": [{"time": 0.2, "name": "tayT2"}, {"time": 0.3333, "name": "tayT1"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1}]}, "body": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1}]}, "body2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1}]}, "body3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1}]}, "dau": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1}]}, "toc": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1}]}, "dau2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1}]}, "dau3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1}]}, "btay_t": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1}]}, "ktay_t": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2, "angle": -53.68}, {"time": 0.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.2, "x": -1.78, "y": 1.03}, {"time": 0.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1}]}, "tayT1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1}]}, "btay_p": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1}]}, "ktay_p": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2, "angle": 1.2}, {"time": 0.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1}]}, "tayp": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2, "angle": 4.51}, {"time": 0.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1}]}}}}}