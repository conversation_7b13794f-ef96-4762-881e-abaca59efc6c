{"skeleton": {"hash": "u25yg0zjAlp4/43vrnM0BN7pn1g", "spine": "3.6.50", "width": 347.47, "height": 121.98, "images": "../../../"}, "bones": [{"name": "root"}, {"name": "bone2", "parent": "root", "length": 34.06, "rotation": 1.34, "x": -129.8, "y": -45.62}, {"name": "bone3", "parent": "root", "length": 39.41, "rotation": 1.16, "x": -70.61, "y": -31.51}, {"name": "bone4", "parent": "root", "length": 33.43, "x": -5.46, "y": -23.38}, {"name": "bone5", "parent": "root", "length": 30.25, "rotation": 0.75, "x": 60.1, "y": -24.8}, {"name": "bone6", "parent": "root", "length": 29.86, "rotation": 1.53, "x": 118.91, "y": -22.65}], "slots": [{"name": "minigame/Candy Crush/image/text-Candy/5", "bone": "bone6", "attachment": "minigame/Candy Crush/image/text-Candy/5"}, {"name": "minigame/Candy Crush/image/text-Candy/4", "bone": "bone5", "attachment": "minigame/Candy Crush/image/text-Candy/4"}, {"name": "minigame/Candy Crush/image/text-Candy/3", "bone": "bone4", "attachment": "minigame/Candy Crush/image/text-Candy/3"}, {"name": "minigame/Candy Crush/image/text-Candy/2", "bone": "bone3", "attachment": "minigame/Candy Crush/image/text-Candy/2"}, {"name": "minigame/Candy Crush/image/text-Candy/1", "bone": "bone2", "attachment": "minigame/Candy Crush/image/text-Candy/1"}], "skins": {"default": {"minigame/Candy Crush/image/text-Candy/1": {"minigame/Candy Crush/image/text-Candy/1": {"x": -1.56, "y": 38.7, "rotation": -1.34, "width": 95, "height": 100}}, "minigame/Candy Crush/image/text-Candy/2": {"minigame/Candy Crush/image/text-Candy/2": {"x": 2.48, "y": 34.3, "rotation": -1.16, "width": 86, "height": 84}}, "minigame/Candy Crush/image/text-Candy/3": {"minigame/Candy Crush/image/text-Candy/3": {"x": -0.51, "y": 33.21, "width": 90, "height": 84}}, "minigame/Candy Crush/image/text-Candy/4": {"minigame/Candy Crush/image/text-Candy/4": {"x": 2.95, "y": 40.77, "rotation": -0.75, "width": 89, "height": 98}}, "minigame/Candy Crush/image/text-Candy/5": {"minigame/Candy Crush/image/text-Candy/5": {"x": 7.19, "y": 33.38, "rotation": -1.53, "width": 85, "height": 91}}}}, "animations": {"Idle-600": {"slots": {"minigame/Candy Crush/image/text-Candy/5": {"color": [{"time": 5.6333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 5.7667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 8.8, "color": "ffffffff"}], "attachment": [{"time": 8.7333, "name": "minigame/Candy Crush/image/text-Candy/5"}]}}, "bones": {"bone3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4667, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 4.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5, "angle": 1.96, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.1667, "angle": 0.84, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3667, "angle": -1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.5667, "angle": 0.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.7667, "angle": -0.83, "curve": [0.25, 0, 0.75, 1]}, {"time": 6, "angle": 0, "curve": "stepped"}, {"time": 10, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "x": -0.01, "y": 14.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5, "x": -0.9, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.1667, "x": 0, "y": -0.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3667, "x": 0.9, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "x": 1, "y": 0.834, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "x": 1, "y": 1.069, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "x": 1, "y": 1.055, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9667, "x": 1, "y": 0.853, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1, "x": 1, "y": 1.083, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2667, "x": 1, "y": 0.979, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.4667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.8333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 4.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0333, "angle": 3.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "angle": 0.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.6, "angle": 1.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8333, "angle": -0.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.1333, "angle": 1.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.3667, "angle": 0, "curve": "stepped"}, {"time": 10, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": -0.01, "y": 14.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0333, "x": -1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.6, "x": -1.1, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.3667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 1, "y": 0.834, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "x": 1, "y": 1.069, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 1, "y": 1.055, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1, "y": 0.853, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 1, "y": 1.083, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 1, "y": 0.979, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.8333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6.3667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.3667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.9, "angle": 0, "curve": "stepped"}, {"time": 4.7667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.9333, "angle": 4.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0333, "angle": -0.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.2, "angle": -0.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "angle": 2.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.4667, "angle": 1.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.6667, "angle": 0.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8667, "angle": 1.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.1333, "angle": 0.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.3667, "angle": 0, "curve": "stepped"}, {"time": 10, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2, "x": -0.01, "y": 14.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.7667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.9333, "x": -2.11, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0333, "x": 0, "y": 6.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.2, "x": -0.7, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "x": -4.23, "y": 1.17, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.4667, "x": -0.98, "y": -0.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.6667, "x": -2.52, "y": -0.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.3667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.9, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.0667, "x": 1, "y": 0.834, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.1667, "x": 1, "y": 1.069, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3, "x": 1, "y": 1.055, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.4, "x": 1, "y": 0.853, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5333, "x": 1, "y": 1.083, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.7, "x": 1, "y": 0.979, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.9, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.7667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6.3667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.3667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0}]}, "bone5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 4.7667, "angle": 0, "curve": "stepped"}, {"time": 8.8333, "angle": 0}, {"time": 8.9, "angle": -3.08}, {"time": 8.9333, "angle": -7.93}, {"time": 9.0667, "angle": -1.83}, {"time": 9.2667, "angle": -4.53}, {"time": 9.5, "angle": -3.14}, {"time": 10, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.9333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5, "x": -0.04, "y": 65.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.1333, "x": -0.14, "y": 217.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.0667, "x": 0, "y": 0}, {"time": 10, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.7667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.9333, "x": 1.536, "y": 0.563, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.9667, "x": 1, "y": 1.834, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.1, "x": 1, "y": 1.032, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.2, "x": 0.924, "y": 1.727, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.2667, "x": 1.795, "y": 0.564, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.4333, "x": 1, "y": 1.161, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.6667, "x": 1.211, "y": 0.908, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.9333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8.8333, "x": 1, "y": 1}, {"time": 8.9, "x": 1.366, "y": 0.488}, {"time": 9.0667, "x": 0.999, "y": 1.085}, {"time": 9.2667, "x": 0.999, "y": 0.98}, {"time": 9.5, "x": 0.999, "y": 1.023}, {"time": 9.7333, "x": 0.999, "y": 1}, {"time": 10, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0}]}, "bone6": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 4.7667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.9, "angle": -8.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.1, "angle": -0.96, "curve": "stepped"}, {"time": 5.2, "angle": -0.96, "curve": "stepped"}, {"time": 5.2333, "angle": -0.96, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.2667, "angle": -46.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "angle": -113.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.7667, "angle": 92.99, "curve": "stepped"}, {"time": 8.7333, "angle": 92.99, "curve": "stepped"}, {"time": 8.8, "angle": 92.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.9, "angle": 83.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.0667, "angle": 38.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.2667, "angle": -7.17, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.4667, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.6667, "angle": -4.74, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.8667, "angle": 0, "curve": "stepped"}, {"time": 10, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.7667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.9, "x": 4.71, "y": -8.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.1, "x": 3.53, "y": -2.35, "curve": "stepped"}, {"time": 5.2, "x": 3.53, "y": -2.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.2333, "x": 13.66, "y": -19.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.2667, "x": 19.28, "y": -56.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "x": 67.13, "y": -83.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.7667, "x": 289.52, "y": -673.43, "curve": "stepped"}, {"time": 8.7333, "x": 289.52, "y": -673.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.7667, "x": -63.1, "y": 466.76, "curve": "stepped"}, {"time": 8.8, "x": -63.1, "y": 466.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.8333, "x": -3.56, "y": 129.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.8667, "x": -2.17, "y": 96.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.9, "x": 15.83, "y": 45.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.0333, "x": 15.23, "y": 89.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.0667, "x": 20, "y": 74.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.1667, "x": 8.38, "y": 0.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.7667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.2333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8.7333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8.8, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.8333, "x": 2.448, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.9, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.2667, "x": 1, "y": 0.782, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.4667, "x": 1, "y": 1.085, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.8667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10, "x": 0, "y": 0}]}, "root": {"shear": [{"time": 8.7333, "x": 0, "y": 0}]}}}, "Intro-80": {"bones": {"bone3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0, "curve": "stepped"}, {"time": 0.5, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": -0.13, "y": 201.58, "curve": "stepped"}, {"time": 0.4, "x": -0.13, "y": 201.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0, "y": 0.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 1, "y": 1.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 1, "y": 0.686, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 1, "y": 1.257, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "x": 1, "y": 0.877, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1, "y": 1.036, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3333, "angle": 0, "curve": "stepped"}, {"time": 0.4333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": -0.13, "y": 201.58, "curve": "stepped"}, {"time": 0.3333, "x": -0.13, "y": 201.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 0, "y": 0.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 1, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 1, "y": 1.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 1, "y": 0.686, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "x": 1, "y": 1.257, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 1, "y": 0.877, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 1, "y": 1.036, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4667, "angle": 0, "curve": "stepped"}, {"time": 0.5667, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": -0.13, "y": 201.58, "curve": "stepped"}, {"time": 0.4667, "x": -0.13, "y": 201.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "x": 0, "y": 0.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 1, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 1, "y": 1.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 1, "y": 0.686, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 1, "y": 1.257, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1, "y": 0.877, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 1, "y": 1.036, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "bone5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5667, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": -0.13, "y": 201.58, "curve": "stepped"}, {"time": 0.5667, "x": -0.13, "y": 201.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 0.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 0, "curve": "stepped"}, {"time": 0.5667, "x": 1, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 1, "y": 1.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 1, "y": 0.686, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 1, "y": 1.257, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "x": 1, "y": 0.877, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "x": 1, "y": 1.036, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "bone6": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6, "angle": 0, "curve": "stepped"}, {"time": 0.7, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": -0.13, "y": 201.58, "curve": "stepped"}, {"time": 0.6, "x": -0.13, "y": 201.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 0, "y": 0.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 1, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 1, "y": 1.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 1, "y": 0.686, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1, "y": 1.257, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1, "y": 0.877, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 1, "y": 1.036, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}}}}}