<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
 <dict>
  <key>frames</key>
  <dict>
   <key>efDiamond0.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{79,95}</string>
    <key>spriteSourceSize</key>
    <string>{79,95}</string>
    <key>textureRect</key>
    <string>{{82,0},{79,95}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>efDiamond2.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{78,90}</string>
    <key>spriteSourceSize</key>
    <string>{78,90}</string>
    <key>textureRect</key>
    <string>{{163,0},{78,90}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>efDiamond3.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{80,88}</string>
    <key>spriteSourceSize</key>
    <string>{80,88}</string>
    <key>textureRect</key>
    <string>{{80,97},{80,88}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>efDiamond4.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{80,95}</string>
    <key>spriteSourceSize</key>
    <string>{80,95}</string>
    <key>textureRect</key>
    <string>{{0,0},{80,95}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>efDiamond5.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{78,95}</string>
    <key>spriteSourceSize</key>
    <string>{78,95}</string>
    <key>textureRect</key>
    <string>{{0,97},{78,95}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>efDiamondWild.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{79,95}</string>
    <key>spriteSourceSize</key>
    <string>{79,95}</string>
    <key>textureRect</key>
    <string>{{82,0},{79,95}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>icDiamond0.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{80,80}</string>
    <key>spriteSourceSize</key>
    <string>{80,80}</string>
    <key>textureRect</key>
    <string>{{82,194},{80,80}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>icDiamond1.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{75,75}</string>
    <key>spriteSourceSize</key>
    <string>{75,75}</string>
    <key>textureRect</key>
    <string>{{245,92},{75,75}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>icDiamond2.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{78,75}</string>
    <key>spriteSourceSize</key>
    <string>{78,75}</string>
    <key>textureRect</key>
    <string>{{243,0},{78,75}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>icDiamond3.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{79,72}</string>
    <key>spriteSourceSize</key>
    <string>{79,72}</string>
    <key>textureRect</key>
    <string>{{164,194},{79,72}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>icDiamond4.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{80,80}</string>
    <key>spriteSourceSize</key>
    <string>{80,80}</string>
    <key>textureRect</key>
    <string>{{0,194},{80,80}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>icDiamond5.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{80,80}</string>
    <key>spriteSourceSize</key>
    <string>{80,80}</string>
    <key>textureRect</key>
    <string>{{163,92},{80,80}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>icDiamondWild.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{80,80}</string>
    <key>spriteSourceSize</key>
    <string>{80,80}</string>
    <key>textureRect</key>
    <string>{{82,194},{80,80}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
  </dict>
  <key>metadata</key>
  <dict>
   <key>format</key>
   <integer>3</integer>
   <key>size</key>
   <string>{323,276}</string>
   <key>textureFileName</key>
   <string>icdiamond.png</string>
  </dict>
 </dict>
</plist>
