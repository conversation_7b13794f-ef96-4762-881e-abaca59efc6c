{"skeleton": {"hash": "JuX6P59BM8DPgkE1h2wP/jcvJks", "spine": "3.6.53", "width": 59.4, "height": 44.4}, "bones": [{"name": "root", "scaleX": 0.6, "scaleY": 0.6}, {"name": "bone", "parent": "root"}, {"name": "stars", "parent": "root", "rotation": 29.03, "x": -34.99, "y": -20.04, "scaleX": 1.4, "scaleY": 1.4}, {"name": "stars2", "parent": "root", "rotation": 27.69, "x": -50.07, "y": 65.59, "scaleX": 2, "scaleY": 2}, {"name": "stars3", "parent": "root", "rotation": 24.07, "x": 32.92, "y": 23.22, "scaleX": 2, "scaleY": 2}], "slots": [{"name": "nho", "bone": "bone", "attachment": "kim cuong"}, {"name": "nho2", "bone": "bone", "color": "ffffff00", "attachment": "kim cuong", "blend": "additive"}, {"name": "bone", "bone": "bone"}, {"name": "hlfx3", "bone": "stars"}, {"name": "hlfx5", "bone": "stars3"}, {"name": "hlfx4", "bone": "stars2"}], "skins": {"default": {"bone": {"a": {"type": "clipping", "end": "bone", "vertexCount": 143, "vertices": [36.45, 45.06, 33.43, 48.81, 29.8, 53.41, 33.43, 56.68, 36.33, 61.03, 34.88, 66.84, 30.53, 73.13, 28.59, 76.28, 23.41, 79.62, 15.23, 80.74, 7.76, 80.74, 3.1, 81.73, -1.13, 77.22, -6.62, 78.21, -7.21, 74.99, -12.67, 75.55, -17.15, 74.15, -22.75, 70.51, -16.87, 67.85, -13.09, 64.21, -18.69, 65.05, -23.73, 64.91, -28.21, 63.65, -27.09, 60.71, -33.39, 60.57, -36.33, 59.45, -34.65, 56.51, -38.71, 56.09, -42.21, 53.15, -46.69, 48.81, -24.72, 33.78, -31.76, 33.62, -37.36, 33.3, -41.84, 33.14, -47.44, 29.3, -51.76, 23.86, -54, 16.66, -53.52, 8.66, -48.56, 1.78, -43.75, -1.9, -44.49, -6.8, -49.2, -6.04, -55.28, -6.12, -59.99, -7.94, -64.16, -12.59, -66.35, -18.29, -66.63, -23.61, -64.92, -28.84, -62.4, -32.72, -60.18, -34.57, -64.69, -34.79, -68.83, -35.9, -73.2, -38.2, -76.53, -42.41, -78.98, -47.74, -78.89, -52.48, -78.42, -56.67, -76.57, -60.23, -73.59, -63.65, -70.83, -65.57, -67.35, -66.47, -63.87, -66.71, -59.97, -65.93, -56.25, -64.16, -53.48, -62.29, -51.88, -61.12, -48.6, -62.49, -44.07, -62.93, -39.54, -61.66, -36.24, -60.24, -34.7, -59.4, -32.16, -63.09, -28.16, -65.63, -23.78, -67.01, -19.64, -67.27, -15.75, -65.9, -11.99, -63.57, -9.35, -61.2, -7.53, -58.89, -5.72, -56.42, -2.69, -59.61, 1.83, -61.98, 6.36, -63.6, 10.47, -63.92, 14.46, -63.49, 18.71, -61.31, 22.22, -58.34, 24.6, -55.96, 26.38, -53.48, 31.57, -53.64, 36.43, -52.99, 41.23, -51.05, 44.55, -48.87, 46.55, -45.2, 47.58, -40.83, 48.03, -36.18, 46.92, -31.96, 48.25, -27.3, 48.47, -23.37, 50.84, -20.86, 52.06, -19.55, 53.96, -16.7, 62.42, -21.15, 65.27, -24.44, 68.92, -26.26, 72.57, -14.51, 72.43, -12.76, 72.06, -11.08, 75.24, -8.66, 74.3, -4.55, 71.92, -2.18, 72.28, 0.49, 77.04, -3.54, 77.68, 4.52, 76.96, 6.61, 77.03, 12.65, 77.89, 15.13, 74.87, 23.56, 71.73, 28.41, 69.24, 30.9, 64.66, 31.95, 62.82, 33.39, 58.37, 33.13, 54.7, 30.37, 52.47, 26.44, 46.72, 28.04, 43.92, 28.64, 44.42, 32.34, 42.92, 35.34, 46.07, 38.04, 50.26, 42.71, 53.5, 47.68, 56.39, 53.22, 56.04, 56.39, 53.67, 60.88, 50.85, 63.52, 48.39, 64.4, 46.63, 63.78, 45.04, 61.93, 44.16, 57.62, 43.99, 54.19, 42.67, 50.76, 40.03, 47.68]}}, "hlfx3": {"hlfx3": {"width": 30, "height": 30}}, "hlfx4": {"hlfx3": {"width": 30, "height": 30}}, "hlfx5": {"hlfx3": {"width": 30, "height": 30}}, "nho": {"kim cuong": {"y": 5.15, "width": 99, "height": 74}}, "nho2": {"kim cuong": {"y": 5.15, "width": 99, "height": 74}}}}, "animations": {"animation": {"slots": {"hlfx3": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1, "color": "ffffff00"}, {"time": 0.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "hlfx3"}]}, "hlfx4": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "hlfx3"}]}, "hlfx5": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00"}, {"time": 0.3, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "hlfx3"}]}, "nho2": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff"}, {"time": 0.2667, "color": "ffffff31"}, {"time": 0.8333, "color": "ffffff49"}, {"time": 1, "color": "ffffff00"}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": 2}, {"time": 0.3333, "angle": -2}, {"time": 0.5, "angle": 2}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.1, "x": 1.2, "y": 1.2, "curve": "stepped"}, {"time": 0.8333, "x": 1.2, "y": 1.2}, {"time": 1, "x": 1, "y": 1}]}, "stars2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3333, "angle": 154.31}, {"time": 0.6333, "angle": -133.09}], "translate": [{"time": 0, "x": 13.75, "y": -17.94}, {"time": 0.1333, "x": 7.83, "y": -8.18, "curve": "stepped"}, {"time": 0.8333, "x": 7.83, "y": -8.18}, {"time": 1, "x": 13.75, "y": -17.94}], "scale": [{"time": 0, "x": 0.5, "y": 0.5}, {"time": 0.1667, "x": 1.6, "y": 1.6}, {"time": 0.6333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "stars3": {"rotate": [{"time": 0, "angle": -24.07, "curve": "stepped"}, {"time": 0.1333, "angle": -24.07}, {"time": 0.6333, "angle": 112.2}, {"time": 1, "angle": -24.07}], "translate": [{"time": 0, "x": 19.14, "y": -4.35}, {"time": 0.1333, "x": 26.9, "y": 3.36}, {"time": 0.3, "x": 26.9, "y": -0.58}, {"time": 0.4, "x": 26.9, "y": 0.2}, {"time": 0.6333, "x": 26.9, "y": 0.33}, {"time": 0.7333, "x": 26.9, "y": -2.1}, {"time": 0.8333, "x": 26.9, "y": -0.58}, {"time": 0.8667, "x": 25.34, "y": 1.03}, {"time": 1, "x": 19.14, "y": -4.35}], "scale": [{"time": 0, "x": 0.3, "y": 0.3, "curve": "stepped"}, {"time": 0.1333, "x": 0.3, "y": 0.3}, {"time": 0.3, "x": 0.7, "y": 0.7}, {"time": 0.7333, "x": 1, "y": 1}, {"time": 1, "x": 0.3, "y": 0.3}]}, "stars": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1, "angle": 0}, {"time": 0.4333, "angle": -145.33}, {"time": 0.7333, "angle": 45.12}], "translate": [{"time": 0, "x": 33.82, "y": -12.26}, {"time": 0.1, "x": 34.99, "y": -17.6}, {"time": 0.4, "x": 36.07, "y": -15.23}, {"time": 0.4333, "x": 33.91, "y": -22.33}, {"time": 0.7333, "x": 34.6, "y": -20.75}, {"time": 0.8333, "x": 34.2, "y": -18.39}, {"time": 1, "x": 33.03, "y": -20.93}], "scale": [{"time": 0, "x": 0.5, "y": 0.5, "curve": "stepped"}, {"time": 0.1, "x": 0.5, "y": 0.5}, {"time": 0.2667, "x": 1.6, "y": 1.6}, {"time": 0.7333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}}}}}