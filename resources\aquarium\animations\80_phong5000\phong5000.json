{"skins": {"default": {"3_nv2": {"3_nv": {"triangles": [55, 57, 56, 58, 55, 54, 57, 55, 58, 45, 58, 54, 46, 58, 45, 59, 58, 46, 46, 60, 59, 47, 60, 46, 47, 61, 60, 57, 64, 56, 55, 67, 54, 62, 58, 59, 60, 62, 59, 56, 65, 55, 58, 63, 57, 52, 49, 51, 41, 42, 43, 68, 49, 52, 48, 49, 68, 41, 52, 51, 53, 52, 41, 68, 52, 53, 44, 41, 43, 53, 41, 44, 67, 68, 53, 45, 54, 53, 67, 53, 54, 44, 45, 53, 77, 35, 36, 76, 38, 39, 37, 38, 77, 77, 38, 76, 37, 77, 36, 40, 76, 39, 50, 76, 40, 51, 50, 40, 41, 51, 40, 49, 50, 51, 34, 73, 33, 35, 73, 34, 77, 73, 35, 21, 14, 20, 14, 21, 25, 15, 20, 14, 17, 15, 16, 17, 20, 15, 18, 20, 17, 19, 20, 18, 11, 25, 10, 22, 23, 24, 12, 25, 11, 13, 25, 12, 25, 21, 22, 14, 25, 13, 24, 25, 22, 75, 29, 30, 75, 26, 27, 27, 28, 29, 29, 75, 27, 74, 26, 75, 25, 26, 74, 25, 74, 10, 78, 74, 75, 73, 30, 31, 73, 32, 33, 73, 31, 32, 75, 30, 73, 75, 73, 79, 77, 79, 73, 78, 79, 77, 78, 77, 76, 75, 79, 78, 50, 49, 78, 50, 78, 76, 72, 78, 49, 63, 64, 57, 62, 63, 58, 66, 70, 67, 66, 67, 55, 65, 66, 55, 83, 70, 66, 83, 66, 65, 65, 56, 64, 84, 83, 65, 84, 65, 64, 84, 64, 63, 84, 63, 62, 61, 85, 84, 61, 84, 62, 80, 70, 83, 1, 2, 84, 81, 83, 84, 85, 1, 84, 47, 0, 61, 85, 61, 0, 1, 85, 0, 61, 62, 60, 67, 70, 48, 67, 48, 68, 74, 72, 10, 78, 72, 74, 48, 72, 49, 9, 10, 72, 71, 9, 72, 70, 71, 72, 48, 70, 72, 69, 9, 71, 7, 8, 9, 69, 7, 9, 69, 71, 70, 70, 80, 69, 80, 7, 69, 6, 7, 80, 81, 6, 80, 5, 6, 81, 4, 5, 81, 82, 4, 81, 3, 4, 82, 83, 81, 80, 2, 81, 84, 82, 81, 2, 3, 82, 2], "uvs": [0.78264, 1, 0.52766, 1, 0.3988, 1, 0.17711, 1, 0.16602, 0.8797, 0.18265, 0.78263, 0.21452, 0.68969, 0.245, 0.61636, 0.27964, 0.53375, 0.29778, 0.5337, 0.28825, 0.47465, 0.26859, 0.5044, 0.25013, 0.56211, 0.1995, 0.60162, 0.1453, 0.64246, 0.16317, 0.68152, 0.17746, 0.7237, 0.1459, 0.76853, 0.08931, 0.81115, 0, 0.81603, 0, 0.72813, 0, 0.63269, 0, 0.5479, 0, 0.45406, 0.04833, 0.4197, 0.13457, 0.42755, 0.12991, 0.37072, 0.10593, 0.28146, 0.13173, 0.17911, 0.22666, 0.16194, 0.3262, 0.14752, 0.31104, 0.06093, 0.35543, 0, 0.56833, 0, 0.59551, 0.0616, 0.5946, 0.13183, 0.71147, 0.13683, 0.80252, 0.17004, 0.84805, 0.2278, 0.85289, 0.32888, 0.83642, 0.36714, 0.86064, 0.42778, 1, 0.46461, 1, 0.52132, 0.95859, 0.59336, 0.91335, 0.647, 0.90168, 0.77577, 0.87603, 0.83835, 0.6462, 0.51983, 0.67254, 0.48175, 0.68991, 0.4374, 0.74205, 0.48489, 0.73626, 0.51669, 0.74679, 0.57007, 0.76312, 0.63562, 0.73363, 0.67644, 0.7094, 0.71687, 0.71929, 0.73339, 0.74705, 0.75082, 0.75308, 0.80969, 0.79061, 0.84609, 0.76909, 0.88914, 0.74306, 0.80986, 0.71528, 0.73626, 0.71382, 0.73302, 0.69931, 0.71255, 0.68488, 0.66788, 0.70418, 0.62377, 0.68421, 0.53745, 0.37105, 0.62733, 0.51446, 0.62733, 0.44678, 0.57689, 0.45162, 0.47721, 0.44517, 0.16977, 0.28676, 0.40651, 0.30096, 0.28194, 0.68254, 0.36773, 0.62735, 0.25021, 0.45706, 0.37713, 0.45548, 0.27489, 0.32911, 0.70292, 0.30617, 0.80896, 0.2924, 0.9321, 0.57694, 0.71489, 0.62054, 0.82948, 0.65266, 0.94407], "vertices": [3, 5, 105.56, 162.89, 0.00034, 6, 139.32, 17.62, 0.92478, 12, 152.22, 2.16, 0.07488, 2, 5, 118.31, 82.56, 0.3312, 6, 115.94, -60.29, 0.6688, 2, 5, 124.75, 41.96, 0.72343, 6, 104.13, -99.66, 0.27657, 1, 5, 135.84, -27.89, 1, 1, 5, 85.54, -39.45, 1, 1, 5, 43.68, -40.72, 1, 3, 5, 2.79, -36.92, 0.98419, 2, -4.84, 72.31, 0.01579, 9, 85.9, 56.87, 3e-05, 4, 5, -29.72, -32.24, 0.66815, 2, 26.26, 61.72, 0.32602, 8, 173.49, 10.93, 0.00195, 9, 54.42, 47.47, 0.00388, 4, 5, -66.38, -26.87, 0.20727, 2, 61.3, 49.69, 0.72163, 8, 137.86, 21.08, 0.037, 9, 18.9, 36.95, 0.0341, 4, 5, -67.31, -21.16, 0.16554, 2, 61.16, 43.91, 0.74058, 8, 137.69, 26.87, 0.0498, 9, 15.66, 41.75, 0.04408, 4, 5, -91.79, -28.12, 0.01485, 2, 86.51, 46.25, 0.43486, 8, 112.51, 23.18, 0.25353, 9, -3.63, 25.15, 0.29676, 4, 5, -78.23, -32.32, 0.00234, 2, 73.96, 52.87, 0.10542, 8, 125.39, 17.24, 0.06187, 9, 10.43, 27.03, 0.83036, 2, 2, 49.43, 59.44, 0.00481, 9, 34.23, 35.9, 0.99519, 2, 9, 57.26, 31.9, 0.95365, 10, -4.91, 44.4, 0.04635, 2, 9, 81.41, 27.28, 0.44318, 10, 11.24, 25.85, 0.55682, 2, 9, 92.12, 41.32, 0.05532, 10, 28.34, 30.29, 0.94468, 2, 9, 104.58, 55.16, 0.00055, 10, 46.68, 33.5, 0.99945, 1, 10, 65.06, 22.03, 1, 1, 10, 81.91, 2.67, 1, 1, 10, 81.87, -25.9, 1, 1, 10, 44.36, -23.1, 1, 2, 9, 103.76, -13.55, 0.22168, 10, 3.62, -20.06, 0.77832, 1, 9, 73.62, -33.76, 1, 2, 8, 106.03, -68.96, 0.02212, 9, 40.26, -56.13, 0.97788, 2, 8, 90.94, -53.92, 0.06732, 9, 19.46, -51.52, 0.93268, 2, 8, 93.6, -26.34, 0.44279, 9, 6.92, -26.8, 0.55721, 2, 8, 69.32, -28.44, 0.95969, 9, -12.45, -41.57, 0.04031, 1, 8, 31.33, -37.06, 1, 1, 8, -12.68, -29.94, 1, 3, 3, 113.62, 73.28, 0.01415, 4, 15.99, 72.51, 0.05896, 8, -20.79, 0.14, 0.92689, 3, 3, 121.55, 41.92, 0.08192, 4, 22.17, 40.76, 0.60699, 8, -27.77, 31.73, 0.31109, 2, 4, 59.23, 45.59, 0.97235, 8, -64.7, 25.95, 0.02765, 3, 4, 85.3, 31.43, 0.99873, 8, -91.13, 39.45, 0.00127, 7, -117.02, -82.16, 0, 2, 4, 85.3, -36.49, 0.99329, 7, -96.94, -17.28, 0.00671, 2, 4, 58.94, -45.16, 0.94971, 7, -69.19, -16.79, 0.05029, 3, 3, 133, -43.2, 0.03622, 4, 28.88, -44.87, 0.63394, 7, -40.56, -25.96, 0.32985, 3, 3, 132.93, -80.54, 0.00153, 4, 26.74, -82.15, 0.07477, 7, -27.49, 9.02, 0.9237, 1, 7, -5.33, 32.57, 1, 1, 7, 22.58, 39.13, 1, 1, 7, 64.36, 27.82, 1, 1, 7, 78.46, 17.96, 1, 2, 7, 105.53, 17.66, 0.7399, 11, -13.55, 14.19, 0.2601, 2, 7, 133.74, 55.47, 0.03025, 11, 1.73, 58.82, 0.96975, 2, 7, 156.92, 48.29, 0.00422, 11, 26, 59.08, 0.99578, 2, 11, 56.97, 46.21, 0.95198, 12, -25.65, 44.6, 0.04802, 2, 11, 80.09, 32.03, 0.55452, 12, -1.64, 31.99, 0.44548, 1, 12, 53.6, 32.56, 1, 1, 12, 80.93, 26.48, 1, 6, 2, 64.01, -67.36, 0.17245, 6, -70.04, 34.99, 0.108, 3, -31.89, -68.84, 0.20378, 7, 122.94, -59.33, 0.15172, 11, 26.58, -53.78, 0.36381, 12, -49.29, -57.2, 0.00024, 5, 2, 80.07, -76.21, 0.07957, 6, -83.23, 47.72, 0.04914, 3, -15.16, -76.32, 0.23118, 7, 109.86, -46.49, 0.31331, 11, 10.2, -45.56, 0.32679, 5, 2, 98.89, -82.28, 0.02356, 6, -99.82, 58.48, 0.01624, 3, 4.11, -80.81, 0.23142, 7, 93.36, -35.58, 0.57164, 11, -8.85, -40.22, 0.15713, 4, 2, 78.11, -98.34, 0.02613, 3, -15.27, -98.54, 0.08388, 7, 117.7, -25.7, 0.31814, 11, 11.3, -23.37, 0.57185, 4, 2, 64.56, -96.11, 0.04361, 3, -28.96, -97.45, 0.07661, 7, 130.15, -31.49, 0.13794, 11, 24.93, -25.07, 0.74184, 5, 2, 41.63, -98.84, 0.0411, 3, -51.58, -102.07, 0.03367, 7, 152.97, -35.04, 0.02096, 11, 47.74, -21.46, 0.89863, 12, -30.34, -23.54, 0.00565, 5, 2, 13.44, -103.27, 0.01026, 3, -79.31, -108.83, 0.00603, 7, 181.31, -38.36, 0.00018, 11, 75.74, -15.95, 0.62134, 12, -2.77, -16.17, 0.36219, 4, 2, -3.77, -93.38, 0.00519, 3, -97.27, -100.4, 0.00399, 11, 93.31, -25.16, 0.16896, 12, 15.38, -24.19, 0.82187, 3, 3, -114.98, -93.65, 0.00068, 11, 110.7, -32.7, 0.03468, 12, 33.23, -30.55, 0.96464, 3, 3, -121.86, -97.19, 4e-05, 11, 117.73, -29.47, 0.00714, 12, 40.03, -26.86, 0.99283, 1, 12, 46.78, -17.45, 1, 1, 12, 71.75, -13.57, 1, 1, 12, 86.35, -0.43, 1, 1, 6, 92.62, 27.11, 1, 1, 6, 57.74, 28.91, 1, 2, 6, 25.02, 29.48, 0.99385, 11, 118.97, -30.74, 0.00615, 3, 6, 23.56, 29.43, 0.99072, 3, -121.8, -95.44, 6e-05, 11, 117.59, -31.22, 0.00922, 2, 6, 13.84, 27.51, 0.99897, 3, -113.31, -90.33, 0.00103, 3, 2, 0.33, -77.93, 0.03643, 6, -5.8, 28.6, 0.95025, 3, -94.48, -84.68, 0.01332, 3, 2, 19.03, -84.61, 0.14142, 6, -22.11, 39.92, 0.80026, 3, -75.29, -89.78, 0.05832, 6, 2, 56.14, -79.27, 0.11644, 6, -59.33, 44.43, 0.12057, 3, -38.75, -81.37, 0.12146, 7, 133.73, -49.98, 0.10857, 11, 33.99, -41.57, 0.53097, 12, -42.71, -44.53, 0.002, 4, 5, -31.39, 8.21, 0.3105, 2, 20.46, 21.66, 0.68821, 8, 177.16, 51.25, 0.00026, 9, 35.92, 83.48, 0.00103, 5, 2, 19.19, -24.07, 0.70706, 6, -38.05, -18.49, 0.23899, 3, -80.16, -29.43, 0.01463, 7, 154.47, -113.09, 0.00257, 11, 73.05, -95.3, 0.03674, 5, 2, 41.37, -3.09, 0.98584, 6, -64.93, -32.96, 0.00678, 3, -59.81, -6.68, 0.0035, 7, 127.46, -127.33, 0.00064, 11, 51.7, -117.13, 0.00325, 5, 2, 83.97, -5.82, 0.85934, 6, -105.35, -19.23, 0.0022, 3, -17.12, -5.85, 0.12579, 7, 87.16, -113.24, 0.00725, 11, 9.02, -116.05, 0.00541, 2, 4, 12.64, 2.8, 0.99088, 8, -19.21, 69.91, 0.00912, 5, 5, -120.52, -33.16, 0.0004, 2, 115.68, 45.91, 0.28594, 3, 10.17, 48.33, 0.02759, 8, 83.36, 21.97, 0.67416, 9, -27.59, 8.51, 0.01191, 4, 2, 168.85, 39.9, 0.02245, 3, 63.66, 46.77, 0.28455, 4, -35.37, 48.81, 0.00896, 8, 29.95, 25.14, 0.68405, 5, 2, 128.76, -80.76, 0.00042, 6, -129.06, 64.8, 0.00186, 3, 33.75, -76.8, 0.24177, 7, 64.18, -29.01, 0.74367, 11, -38.64, -42.9, 0.01226, 3, 3, 82.99, -56.44, 0.27974, 4, -21.79, -55.31, 0.07124, 7, 10.93, -30.96, 0.64902, 4, 6, -145.87, -5.26, 5e-05, 3, 25.74, -5.21, 0.9855, 7, 46.76, -98.92, 0.01374, 11, -33.83, -114.78, 0.00071, 3, 3, 69.4, -2.28, 0.99066, 4, -32.35, -0.49, 0.0007, 7, 4.8, -86.46, 0.00864, 2, 5, 2.66, 0.07, 0.99996, 6, -24.04, -84.42, 4e-05, 3, 5, 48.63, -0.04, 0.9993, 2, -56.68, 44.51, 0.00026, 6, 17.32, -104.47, 0.00044, 2, 5, 101.37, 3.88, 0.97555, 6, 66.54, -123.82, 0.02445, 3, 5, -4.68, 78.95, 0.00868, 2, -18.83, -42.95, 0.09806, 6, 3.58, -10.17, 0.89326, 3, 5, 41.58, 100.38, 0.03154, 2, -68.24, -55.5, 0.00712, 6, 54.55, -10.94, 0.96134, 3, 5, 88.41, 118.19, 0.05423, 6, 104.47, -15.22, 0.94571, 12, 131.58, -41.04, 6e-05], "width": 319, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 0, 94, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 94, 0, 122, 122, 124, 124, 126, 128, 130, 130, 132, 132, 134, 134, 136, 136, 96, 14, 138, 138, 140, 140, 134, 20, 148, 148, 150, 100, 152, 152, 154], "type": "mesh", "hull": 48, "height": 428}}, "3_khung": {"3_khung": {"width": 260, "height": 95}}, "3_nv": {"3_nv": {"triangles": [55, 57, 56, 58, 55, 54, 57, 55, 58, 45, 58, 54, 46, 58, 45, 59, 58, 46, 46, 60, 59, 47, 60, 46, 47, 61, 60, 57, 64, 56, 55, 67, 54, 62, 58, 59, 60, 62, 59, 56, 65, 55, 58, 63, 57, 52, 49, 51, 41, 42, 43, 68, 49, 52, 48, 49, 68, 41, 52, 51, 53, 52, 41, 68, 52, 53, 44, 41, 43, 53, 41, 44, 67, 68, 53, 45, 54, 53, 67, 53, 54, 44, 45, 53, 77, 35, 36, 76, 38, 39, 37, 38, 77, 77, 38, 76, 37, 77, 36, 40, 76, 39, 50, 76, 40, 51, 50, 40, 41, 51, 40, 49, 50, 51, 34, 73, 33, 35, 73, 34, 77, 73, 35, 21, 14, 20, 14, 21, 25, 15, 20, 14, 17, 15, 16, 17, 20, 15, 18, 20, 17, 19, 20, 18, 11, 25, 10, 22, 23, 24, 12, 25, 11, 13, 25, 12, 25, 21, 22, 14, 25, 13, 24, 25, 22, 75, 29, 30, 75, 26, 27, 27, 28, 29, 29, 75, 27, 74, 26, 75, 25, 26, 74, 25, 74, 10, 78, 74, 75, 73, 30, 31, 73, 32, 33, 73, 31, 32, 75, 30, 73, 75, 73, 79, 77, 79, 73, 78, 79, 77, 78, 77, 76, 75, 79, 78, 50, 49, 78, 50, 78, 76, 72, 78, 49, 63, 64, 57, 62, 63, 58, 66, 70, 67, 66, 67, 55, 65, 66, 55, 83, 70, 66, 83, 66, 65, 65, 56, 64, 84, 83, 65, 84, 65, 64, 84, 64, 63, 84, 63, 62, 61, 85, 84, 61, 84, 62, 80, 70, 83, 1, 2, 84, 81, 83, 84, 85, 1, 84, 47, 0, 61, 85, 61, 0, 1, 85, 0, 61, 62, 60, 67, 70, 48, 67, 48, 68, 74, 72, 10, 78, 72, 74, 48, 72, 49, 9, 10, 72, 71, 9, 72, 70, 71, 72, 48, 70, 72, 69, 9, 71, 7, 8, 9, 69, 7, 9, 69, 71, 70, 70, 80, 69, 80, 7, 69, 6, 7, 80, 81, 6, 80, 5, 6, 81, 4, 5, 81, 82, 4, 81, 3, 4, 82, 83, 81, 80, 2, 81, 84, 82, 81, 2, 3, 82, 2], "uvs": [0.78264, 1, 0.52766, 1, 0.3988, 1, 0.17711, 1, 0.16602, 0.8797, 0.18265, 0.78263, 0.21452, 0.68969, 0.245, 0.61636, 0.27964, 0.53375, 0.29778, 0.5337, 0.28825, 0.47465, 0.26859, 0.5044, 0.25013, 0.56211, 0.1995, 0.60162, 0.1453, 0.64246, 0.16317, 0.68152, 0.17746, 0.7237, 0.1459, 0.76853, 0.08931, 0.81115, 0, 0.81603, 0, 0.72813, 0, 0.63269, 0, 0.5479, 0, 0.45406, 0.04833, 0.4197, 0.13457, 0.42755, 0.12991, 0.37072, 0.10593, 0.28146, 0.13173, 0.17911, 0.22666, 0.16194, 0.3262, 0.14752, 0.31104, 0.06093, 0.35543, 0, 0.56833, 0, 0.59551, 0.0616, 0.5946, 0.13183, 0.71147, 0.13683, 0.80252, 0.17004, 0.84805, 0.2278, 0.85289, 0.32888, 0.83642, 0.36714, 0.86064, 0.42778, 1, 0.46461, 1, 0.52132, 0.95859, 0.59336, 0.91335, 0.647, 0.90168, 0.77577, 0.87603, 0.83835, 0.6462, 0.51983, 0.67254, 0.48175, 0.68991, 0.4374, 0.74205, 0.48489, 0.73626, 0.51669, 0.74679, 0.57007, 0.76312, 0.63562, 0.73363, 0.67644, 0.7094, 0.71687, 0.71929, 0.73339, 0.74705, 0.75082, 0.75308, 0.80969, 0.79061, 0.84609, 0.76909, 0.88914, 0.74306, 0.80986, 0.71528, 0.73626, 0.71382, 0.73302, 0.69931, 0.71255, 0.68488, 0.66788, 0.70418, 0.62377, 0.68421, 0.53745, 0.37105, 0.62733, 0.51446, 0.62733, 0.44678, 0.57689, 0.45162, 0.47721, 0.44517, 0.16977, 0.28676, 0.40651, 0.30096, 0.28194, 0.68254, 0.36773, 0.62735, 0.25021, 0.45706, 0.37713, 0.45548, 0.27489, 0.32911, 0.70292, 0.30617, 0.80896, 0.2924, 0.9321, 0.57694, 0.71489, 0.62054, 0.82948, 0.65266, 0.94407], "vertices": [3, 5, 105.56, 162.89, 0.00034, 6, 139.32, 17.62, 0.92478, 12, 152.22, 2.16, 0.07488, 2, 5, 118.31, 82.56, 0.3312, 6, 115.94, -60.29, 0.6688, 2, 5, 124.75, 41.96, 0.72343, 6, 104.13, -99.66, 0.27657, 1, 5, 135.84, -27.89, 1, 1, 5, 85.54, -39.45, 1, 1, 5, 43.68, -40.72, 1, 3, 5, 2.79, -36.92, 0.98419, 2, -4.84, 72.31, 0.01579, 9, 85.9, 56.87, 3e-05, 4, 5, -29.72, -32.24, 0.66815, 2, 26.26, 61.72, 0.32602, 8, 173.49, 10.93, 0.00195, 9, 54.42, 47.47, 0.00388, 4, 5, -66.38, -26.87, 0.20727, 2, 61.3, 49.69, 0.72163, 8, 137.86, 21.08, 0.037, 9, 18.9, 36.95, 0.0341, 4, 5, -67.31, -21.16, 0.16554, 2, 61.16, 43.91, 0.74058, 8, 137.69, 26.87, 0.0498, 9, 15.66, 41.75, 0.04408, 4, 5, -91.79, -28.12, 0.01485, 2, 86.51, 46.25, 0.43486, 8, 112.51, 23.18, 0.25353, 9, -3.63, 25.15, 0.29676, 4, 5, -78.23, -32.32, 0.00234, 2, 73.96, 52.87, 0.10542, 8, 125.39, 17.24, 0.06187, 9, 10.43, 27.03, 0.83036, 2, 2, 49.43, 59.44, 0.00481, 9, 34.23, 35.9, 0.99519, 2, 9, 57.26, 31.9, 0.95365, 10, -4.91, 44.4, 0.04635, 2, 9, 81.41, 27.28, 0.44318, 10, 11.24, 25.85, 0.55682, 2, 9, 92.12, 41.32, 0.05532, 10, 28.34, 30.29, 0.94468, 2, 9, 104.58, 55.16, 0.00055, 10, 46.68, 33.5, 0.99945, 1, 10, 65.06, 22.03, 1, 1, 10, 81.91, 2.67, 1, 1, 10, 81.87, -25.9, 1, 1, 10, 44.36, -23.1, 1, 2, 9, 103.76, -13.55, 0.22168, 10, 3.62, -20.06, 0.77832, 1, 9, 73.62, -33.76, 1, 2, 8, 106.03, -68.96, 0.02212, 9, 40.26, -56.13, 0.97788, 2, 8, 90.94, -53.92, 0.06732, 9, 19.46, -51.52, 0.93268, 2, 8, 93.6, -26.34, 0.44279, 9, 6.92, -26.8, 0.55721, 2, 8, 69.32, -28.44, 0.95969, 9, -12.45, -41.57, 0.04031, 1, 8, 31.33, -37.06, 1, 1, 8, -12.68, -29.94, 1, 3, 3, 113.62, 73.28, 0.01415, 4, 15.99, 72.51, 0.05896, 8, -20.79, 0.14, 0.92689, 3, 3, 121.55, 41.92, 0.08192, 4, 22.17, 40.76, 0.60699, 8, -27.77, 31.73, 0.31109, 2, 4, 59.23, 45.59, 0.97235, 8, -64.7, 25.95, 0.02765, 3, 4, 85.3, 31.43, 0.99873, 8, -91.13, 39.45, 0.00127, 7, -117.02, -82.16, 0, 2, 4, 85.3, -36.49, 0.99329, 7, -96.94, -17.28, 0.00671, 2, 4, 58.94, -45.16, 0.94971, 7, -69.19, -16.79, 0.05029, 3, 3, 133, -43.2, 0.03622, 4, 28.88, -44.87, 0.63394, 7, -40.56, -25.96, 0.32985, 3, 3, 132.93, -80.54, 0.00153, 4, 26.74, -82.15, 0.07477, 7, -27.49, 9.02, 0.9237, 1, 7, -5.33, 32.57, 1, 1, 7, 22.58, 39.13, 1, 1, 7, 64.36, 27.82, 1, 1, 7, 78.46, 17.96, 1, 2, 7, 105.53, 17.66, 0.7399, 11, -13.55, 14.19, 0.2601, 2, 7, 133.74, 55.47, 0.03025, 11, 1.73, 58.82, 0.96975, 2, 7, 156.92, 48.29, 0.00422, 11, 26, 59.08, 0.99578, 2, 11, 56.97, 46.21, 0.95198, 12, -25.65, 44.6, 0.04802, 2, 11, 80.09, 32.03, 0.55452, 12, -1.64, 31.99, 0.44548, 1, 12, 53.6, 32.56, 1, 1, 12, 80.93, 26.48, 1, 6, 2, 64.01, -67.36, 0.17245, 6, -70.04, 34.99, 0.108, 3, -31.89, -68.84, 0.20378, 7, 122.94, -59.33, 0.15172, 11, 26.58, -53.78, 0.36381, 12, -49.29, -57.2, 0.00024, 5, 2, 80.07, -76.21, 0.07957, 6, -83.23, 47.72, 0.04914, 3, -15.16, -76.32, 0.23118, 7, 109.86, -46.49, 0.31331, 11, 10.2, -45.56, 0.32679, 5, 2, 98.89, -82.28, 0.02356, 6, -99.82, 58.48, 0.01624, 3, 4.11, -80.81, 0.23142, 7, 93.36, -35.58, 0.57164, 11, -8.85, -40.22, 0.15713, 4, 2, 78.11, -98.34, 0.02613, 3, -15.27, -98.54, 0.08388, 7, 117.7, -25.7, 0.31814, 11, 11.3, -23.37, 0.57185, 4, 2, 64.56, -96.11, 0.04361, 3, -28.96, -97.45, 0.07661, 7, 130.15, -31.49, 0.13794, 11, 24.93, -25.07, 0.74184, 5, 2, 41.63, -98.84, 0.0411, 3, -51.58, -102.07, 0.03367, 7, 152.97, -35.04, 0.02096, 11, 47.74, -21.46, 0.89863, 12, -30.34, -23.54, 0.00565, 5, 2, 13.44, -103.27, 0.01026, 3, -79.31, -108.83, 0.00603, 7, 181.31, -38.36, 0.00018, 11, 75.74, -15.95, 0.62134, 12, -2.77, -16.17, 0.36219, 4, 2, -3.77, -93.38, 0.00519, 3, -97.27, -100.4, 0.00399, 11, 93.31, -25.16, 0.16896, 12, 15.38, -24.19, 0.82187, 3, 3, -114.98, -93.65, 0.00068, 11, 110.7, -32.7, 0.03468, 12, 33.23, -30.55, 0.96464, 3, 3, -121.86, -97.19, 4e-05, 11, 117.73, -29.47, 0.00714, 12, 40.03, -26.86, 0.99283, 1, 12, 46.78, -17.45, 1, 1, 12, 71.75, -13.57, 1, 1, 12, 86.35, -0.43, 1, 1, 6, 92.62, 27.11, 1, 1, 6, 57.74, 28.91, 1, 2, 6, 25.02, 29.48, 0.99385, 11, 118.97, -30.74, 0.00615, 3, 6, 23.56, 29.43, 0.99072, 3, -121.8, -95.44, 6e-05, 11, 117.59, -31.22, 0.00922, 2, 6, 13.84, 27.51, 0.99897, 3, -113.31, -90.33, 0.00103, 3, 2, 0.33, -77.93, 0.03643, 6, -5.8, 28.6, 0.95025, 3, -94.48, -84.68, 0.01332, 3, 2, 19.03, -84.61, 0.14142, 6, -22.11, 39.92, 0.80026, 3, -75.29, -89.78, 0.05832, 6, 2, 56.14, -79.27, 0.11644, 6, -59.33, 44.43, 0.12057, 3, -38.75, -81.37, 0.12146, 7, 133.73, -49.98, 0.10857, 11, 33.99, -41.57, 0.53097, 12, -42.71, -44.53, 0.002, 4, 5, -31.39, 8.21, 0.3105, 2, 20.46, 21.66, 0.68821, 8, 177.16, 51.25, 0.00026, 9, 35.92, 83.48, 0.00103, 5, 2, 19.19, -24.07, 0.70706, 6, -38.05, -18.49, 0.23899, 3, -80.16, -29.43, 0.01463, 7, 154.47, -113.09, 0.00257, 11, 73.05, -95.3, 0.03674, 5, 2, 41.37, -3.09, 0.98584, 6, -64.93, -32.96, 0.00678, 3, -59.81, -6.68, 0.0035, 7, 127.46, -127.33, 0.00064, 11, 51.7, -117.13, 0.00325, 5, 2, 83.97, -5.82, 0.85934, 6, -105.35, -19.23, 0.0022, 3, -17.12, -5.85, 0.12579, 7, 87.16, -113.24, 0.00725, 11, 9.02, -116.05, 0.00541, 2, 4, 12.64, 2.8, 0.99088, 8, -19.21, 69.91, 0.00912, 5, 5, -120.52, -33.16, 0.0004, 2, 115.68, 45.91, 0.28594, 3, 10.17, 48.33, 0.02759, 8, 83.36, 21.97, 0.67416, 9, -27.59, 8.51, 0.01191, 4, 2, 168.85, 39.9, 0.02245, 3, 63.66, 46.77, 0.28455, 4, -35.37, 48.81, 0.00896, 8, 29.95, 25.14, 0.68405, 5, 2, 128.76, -80.76, 0.00042, 6, -129.06, 64.8, 0.00186, 3, 33.75, -76.8, 0.24177, 7, 64.18, -29.01, 0.74367, 11, -38.64, -42.9, 0.01226, 3, 3, 82.99, -56.44, 0.27974, 4, -21.79, -55.31, 0.07124, 7, 10.93, -30.96, 0.64902, 4, 6, -145.87, -5.26, 5e-05, 3, 25.74, -5.21, 0.9855, 7, 46.76, -98.92, 0.01374, 11, -33.83, -114.78, 0.00071, 3, 3, 69.4, -2.28, 0.99066, 4, -32.35, -0.49, 0.0007, 7, 4.8, -86.46, 0.00864, 2, 5, 2.66, 0.07, 0.99996, 6, -24.04, -84.42, 4e-05, 3, 5, 48.63, -0.04, 0.9993, 2, -56.68, 44.51, 0.00026, 6, 17.32, -104.47, 0.00044, 2, 5, 101.37, 3.88, 0.97555, 6, 66.54, -123.82, 0.02445, 3, 5, -4.68, 78.95, 0.00868, 2, -18.83, -42.95, 0.09806, 6, 3.58, -10.17, 0.89326, 3, 5, 41.58, 100.38, 0.03154, 2, -68.24, -55.5, 0.00712, 6, 54.55, -10.94, 0.96134, 3, 5, 88.41, 118.19, 0.05423, 6, 104.47, -15.22, 0.94571, 12, 131.58, -41.04, 6e-05], "width": 319, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 0, 94, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 94, 0, 122, 122, 124, 124, 126, 128, 130, 130, 132, 132, 134, 134, 136, 136, 96, 14, 138, 138, 140, 140, 134, 20, 148, 148, 150, 100, 152, 152, 154], "type": "mesh", "hull": 48, "height": 428}}}}, "skeleton": {"images": "../../Google Drive/GiangHoang/T7.2021/Pay_lobby/chonphong/galaxy/images/", "width": 319, "spine": "3.6.53", "hash": "C3IEWqP4xzkIFSddxeLJcCupUW8", "height": 436}, "slots": [{"attachment": "3_nv", "name": "3_nv", "bone": "3_nv7"}, {"color": "ffffff00", "attachment": "3_nv", "blend": "additive", "name": "3_nv2", "bone": "3_nv7"}, {"attachment": "3_khung", "name": "3_khung", "bone": "3"}], "bones": [{"name": "root"}, {"parent": "root", "name": "3"}, {"parent": "3", "rotation": 91.59, "name": "3_nv", "length": 101.56, "x": -8.42, "y": 100.16}, {"parent": "3_nv", "rotation": -4.77, "name": "3_nv2", "length": 101.68, "x": 101.52, "y": -1.41}, {"parent": "3_nv2", "rotation": 3.18, "name": "3_nv3", "length": 83.19, "x": 101.68}, {"parent": "3_nv", "rotation": 169.39, "name": "3_nv4", "length": 89.94, "x": -8.89, "y": 35.51}, {"parent": "3_nv", "rotation": -164.89, "name": "3_nv5", "length": 88.33, "x": -12.72, "y": -51.84}, {"parent": "3_nv2", "rotation": -159.62, "name": "3_nv6", "length": 114.09, "x": 104.01, "y": -81.65}, {"parent": "3_nv2", "rotation": -178.28, "name": "3_nv7", "length": 102.1, "x": 92.84, "y": 72.8}, {"parent": "3_nv7", "rotation": -32.39, "name": "3_nv8", "length": 88.53, "x": 102.1}, {"parent": "3_nv8", "rotation": 38.11, "name": "3_nv9", "length": 58.12, "x": 88.53}, {"parent": "3_nv6", "rotation": -17.82, "name": "3_nv10", "length": 79.58, "x": 114.09}, {"parent": "3_nv10", "rotation": -3.83, "name": "3_nv11", "length": 66.81, "x": 79.58}], "animations": {"animation": {"slots": {"3_nv2": {"color": [{"color": "ffffff00", "curve": [0.25, 0, 0.75, 1], "time": 0}, {"color": "ffffffa3", "curve": [0.25, 0, 0.75, 1], "time": 0.6667}, {"color": "ffffff00", "time": 1.3333}]}}, "bones": {"3_nv11": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 1.3333}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 1.3333}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 1.3333}]}, "3_nv3": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 0.3}, {"angle": 0, "time": 1.3333}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.3}, {"x": 1, "y": 1, "time": 1.3333}], "translate": [{"curve": [0.379, 0.52, 0.747, 1], "x": 0.85, "y": 0.05, "time": 0}, {"x": 0, "y": 0, "time": 0.3}, {"curve": [0.247, 0, 0.63, 0.53], "x": 1.96, "y": 0.11, "time": 0.9667}, {"x": 0.85, "y": 0.05, "time": 1.3333}]}, "3_nv2": {"rotate": [{"curve": [0.382, 0.57, 0.737, 1], "angle": 0.42, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0.2333}, {"curve": [0.244, 0, 0.646, 0.59], "angle": 1.38, "time": 0.9}, {"angle": 0.42, "time": 1.3333}], "scale": [{"curve": [0.382, 0.58, 0.731, 1], "x": 1.005, "y": 1.005, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 1, "y": 1, "time": 0.2}, {"curve": [0.243, 0, 0.655, 0.63], "x": 1.022, "y": 1.022, "time": 0.8667}, {"x": 1.005, "y": 1.005, "time": 1.3333}], "translate": [{"curve": [0.375, 0.62, 0.716, 1], "x": 0.56, "y": -0.02, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0.1333}, {"curve": [0.243, 0, 0.68, 0.71], "x": 4.32, "y": -0.12, "time": 0.8}, {"x": 0.56, "y": -0.02, "time": 1.3333}]}, "3_nv10": {"rotate": [{"curve": [0.375, 0.5, 0.75, 1], "angle": 2.53, "time": 0}, {"angle": 0, "time": 0.3333}, {"curve": [0.25, 0, 0.625, 0.5], "angle": 5.06, "time": 1}, {"angle": 2.53, "time": 1.3333}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.3333}, {"x": 1, "y": 1, "time": 1.3333}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 1.3333}]}, "3_nv": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 1.3333}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 1.3333}], "translate": [{"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": 0, "y": 2.6, "time": 0.6667}, {"x": 0, "y": 0, "time": 1.3333}]}, "3": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 1.3333}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 1.3333}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 1.3333}]}, "root": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 1.3333}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 1.3333}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 1.3333}]}, "3_nv7": {"rotate": [{"curve": [0.382, 0.58, 0.731, 1], "angle": -0.74, "time": 0}, {"angle": 0, "time": 0.2}, {"curve": [0.243, 0, 0.655, 0.63], "angle": 356.94, "time": 0.8667}, {"angle": -0.74, "time": 1.3333}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.2}, {"x": 1, "y": 1, "time": 1.3333}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.2}, {"x": 0, "y": 0, "time": 1.3333}]}, "3_nv6": {"rotate": [{"curve": [0.382, 0.58, 0.731, 1], "angle": 0.25, "time": 0}, {"angle": 0, "time": 0.2}, {"curve": [0.243, 0, 0.655, 0.63], "angle": 1.03, "time": 0.8667}, {"angle": 0.25, "time": 1.3333}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.2}, {"x": 1, "y": 1, "time": 1.3333}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.2}, {"x": 0, "y": 0, "time": 1.3333}]}, "3_nv5": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 1.3333}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 1.3333}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 1.3333}]}, "3_nv4": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 1.3333}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 1.3333}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 1.3333}]}, "3_nv9": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 1.3333}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 1.3333}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 1.3333}]}, "3_nv8": {"rotate": [{"curve": [0.375, 0.5, 0.75, 1], "angle": -1.99, "time": 0}, {"angle": 0, "time": 0.3333}, {"curve": [0.25, 0, 0.625, 0.5], "angle": -3.99, "time": 1}, {"angle": -1.99, "time": 1.3333}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.3333}, {"x": 1, "y": 1, "time": 1.3333}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 1.3333}]}}}}}