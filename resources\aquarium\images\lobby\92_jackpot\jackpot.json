{"skins": {"default": {"A": {"A": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 3, 18.16, -24.34, 1, 1, 3, -15.84, -24.34, 1, 1, 3, -15.84, 24.66, 1, 1, 3, 18.16, 24.66, 1], "width": 34, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 49}}, "P2": {"P2": {"x": -1.63, "width": 42, "y": 6.54, "height": 61}}, "C": {"C": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 4, 16.32, -24.34, 1, 1, 4, -14.68, -24.34, 1, 1, 4, -14.68, 24.66, 1, 1, 4, 16.32, 24.66, 1], "width": 31, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 49}}, "O2": {"O2": {"x": -0.09, "width": 47, "y": 4.23, "height": 68}}, "K2": {"K2": {"x": -2.1, "width": 49, "y": 8.8, "height": 71}}, "J2": {"J2": {"x": -1.21, "width": 45, "y": 1.91, "height": 70}}, "icon": {"icon": {"x": -2.35, "width": 110, "y": 9.49, "height": 106}}, "J": {"J": {"width": 31, "height": 49}}, "K": {"K": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 5, 20.5, -24.34, 1, 1, 5, -13.5, -24.34, 1, 1, 5, -13.5, 24.66, 1, 1, 5, 20.5, 24.66, 1], "width": 34, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 49}}, "C2": {"C2": {"x": -3.09, "width": 49, "y": 7.2, "height": 78}}, "O": {"O": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 7, 19.45, -24.34, 1, 1, 7, -14.55, -24.34, 1, 1, 7, -14.55, 24.66, 1, 1, 7, 19.45, 24.66, 1], "width": 34, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 49}}, "icon2": {"icon2": {"x": -4.76, "width": 136, "y": 9.62, "height": 131}}, "P": {"P": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 6, 20.05, -24.34, 1, 1, 6, -13.95, -24.34, 1, 1, 6, -13.95, 24.66, 1, 1, 6, 20.05, 24.66, 1], "width": 34, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 49}}, "A2": {"A2": {"x": -3.04, "width": 54, "y": 6.79, "height": 77}}, "T": {"T": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 8, 18.02, -24.34, 1, 1, 8, -13.98, -24.34, 1, 1, 8, -13.98, 24.66, 1, 1, 8, 18.02, 24.66, 1], "width": 32, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 49}}, "T2": {"T2": {"x": 0.28, "width": 43, "y": 2.93, "height": 65}}}}, "skeleton": {"images": "./images/", "width": 165.98, "spine": "3.6.53", "hash": "9unjxgFQDSrANfxu92l0ZlDyHQc", "height": 131}, "slots": [{"attachment": "icon", "name": "icon", "bone": "icon"}, {"attachment": "J", "name": "J", "bone": "J"}, {"attachment": "A", "name": "A", "bone": "root"}, {"attachment": "C", "name": "C", "bone": "root"}, {"attachment": "K", "name": "K", "bone": "root"}, {"attachment": "P", "name": "P", "bone": "root"}, {"attachment": "O", "name": "O", "bone": "root"}, {"attachment": "T", "name": "T", "bone": "root"}, {"color": "ffffff00", "attachment": "T2", "blend": "additive", "name": "T2", "bone": "J7"}, {"color": "ffffff00", "attachment": "J2", "blend": "additive", "name": "J2", "bone": "J"}, {"color": "ffffff00", "attachment": "A2", "blend": "additive", "name": "A2", "bone": "J2"}, {"color": "ffffff00", "attachment": "C2", "blend": "additive", "name": "C2", "bone": "J3"}, {"color": "ffffff00", "attachment": "K2", "blend": "additive", "name": "K2", "bone": "J4"}, {"color": "ffffff00", "attachment": "O2", "blend": "additive", "name": "O2", "bone": "J6"}, {"color": "ffffff00", "attachment": "P2", "blend": "additive", "name": "P2", "bone": "J5"}, {"color": "ffffff00", "attachment": "icon2", "blend": "additive", "name": "icon2", "bone": "icon"}], "bones": [{"name": "root"}, {"parent": "root", "name": "bone", "x": 7.73, "y": -37.81}, {"parent": "bone", "name": "J", "x": -71.6, "y": 3.11}, {"parent": "bone", "name": "J2", "x": -52.8, "y": 3.11}, {"parent": "bone", "name": "J3", "x": -32.53, "y": 3.11}, {"parent": "bone", "name": "J4", "x": -16.42, "y": 3.11}, {"parent": "bone", "name": "J5", "x": 6.2, "y": 3.11}, {"parent": "bone", "name": "J6", "x": 28.05, "y": 3.11}, {"parent": "bone", "name": "J7", "x": 48.89, "y": 3.11}, {"parent": "root", "name": "icon", "x": -1.5, "y": -11.99}], "animations": {"Jackpot": {"slots": {"A2": {"color": [{"color": "ffffff00", "time": 2}, {"color": "ffffffff", "time": 2.2}, {"color": "ffffff00", "time": 2.5333}]}, "P2": {"color": [{"color": "ffffff00", "time": 2.3}, {"color": "ffffffff", "time": 2.5}, {"color": "ffffff00", "time": 2.8333}]}, "O2": {"color": [{"color": "ffffff00", "time": 2.4}, {"color": "ffffffff", "time": 2.6}, {"color": "ffffff00", "time": 2.9333}]}, "K2": {"color": [{"color": "ffffff00", "time": 2.2}, {"color": "ffffffff", "time": 2.4}, {"color": "ffffff00", "time": 2.7333}]}, "J2": {"color": [{"color": "ffffff00", "time": 1.9}, {"color": "ffffffff", "time": 2.1}, {"color": "ffffff00", "time": 2.4333}]}, "icon": {"color": [{"color": "ffffffff", "time": 1.4}, {"color": "ffae00e0", "time": 1.7}, {"color": "ffffffff", "time": 1.9667}, {"color": "ffae00e0", "time": 2.2}, {"color": "ffffffff", "time": 2.5}]}, "T2": {"color": [{"color": "ffffff00", "time": 2.5}, {"color": "ffffffff", "time": 2.7}, {"color": "ffffff00", "time": 3.0333}]}, "C2": {"color": [{"color": "ffffff00", "time": 2.1}, {"color": "ffffffff", "time": 2.3}, {"color": "ffffff00", "time": 2.6333}]}, "icon2": {"color": [{"color": "ffffff00", "time": 2.5}, {"color": "ffffffff", "time": 2.6667}, {"color": "ffffff00", "time": 2.9667}]}}, "bones": {"J2": {"scale": [{"x": 1, "y": 1, "time": 2}, {"x": 0.943, "y": 1.035, "time": 2.2}, {"x": 1.041, "y": 0.967, "time": 2.3333}, {"x": 1.016, "y": 1.034, "time": 2.5333}, {"x": 1, "y": 1, "time": 2.9333}], "translate": [{"curve": "stepped", "x": 0, "y": 0.78, "time": 0}, {"x": 0, "y": 0.78, "time": 2}, {"x": 0, "y": 4.62, "time": 2.2}, {"x": 0, "y": 0.23, "time": 2.3333}, {"x": 0, "y": 1.33, "time": 2.5333}, {"x": 0, "y": 0.78, "time": 2.7333}]}, "icon": {"rotate": [{"angle": 0, "time": 1.4}, {"angle": -2.14, "time": 1.7}, {"angle": -0.26, "time": 1.9667}, {"angle": -3.47, "time": 2.2}, {"angle": -1.57, "time": 2.5}, {"angle": -3.5, "time": 2.6667}, {"angle": 3.61, "time": 2.8}, {"angle": -3.91, "time": 2.9667}, {"angle": -0.05, "time": 3.1}, {"angle": 0, "time": 3.3333}], "scale": [{"curve": "stepped", "x": 1.044, "y": 1.011, "time": 0}, {"x": 1.044, "y": 1.011, "time": 1.4}, {"x": 1.03, "y": 0.982, "time": 2.5}, {"x": 1.2, "y": 1.145, "time": 2.6667}, {"x": 1.042, "y": 0.994, "time": 2.9667}, {"x": 1.044, "y": 1.011, "time": 3.3333}], "translate": [{"curve": "stepped", "x": 0, "y": 0.75, "time": 0}, {"x": 0, "y": 0.75, "time": 1.4}, {"x": 0, "y": -2.66, "time": 1.7}, {"x": 0, "y": -7.7, "time": 1.9667}, {"x": 0, "y": -2.66, "time": 2.2}, {"x": 0, "y": -7.7, "time": 2.5}, {"x": 0, "y": 3.5, "time": 2.6667}, {"x": 0, "y": -0.55, "time": 2.9667}, {"x": 0, "y": 0.75, "time": 3.3333}]}, "J3": {"scale": [{"x": 1, "y": 1, "time": 2.1}, {"x": 0.943, "y": 1.035, "time": 2.3}, {"x": 1.041, "y": 0.967, "time": 2.4333}, {"x": 1.016, "y": 1.034, "time": 2.6333}, {"x": 1, "y": 1, "time": 3.0333}], "translate": [{"curve": "stepped", "x": 0, "y": 0.78, "time": 0}, {"x": 0, "y": 0.78, "time": 2.1}, {"x": 0, "y": 4.62, "time": 2.3}, {"x": 0, "y": 0.23, "time": 2.4333}, {"x": 0, "y": 1.33, "time": 2.6333}, {"x": 0, "y": 0.78, "time": 2.8333}]}, "J4": {"scale": [{"x": 1, "y": 1, "time": 2.2}, {"x": 0.943, "y": 1.035, "time": 2.4}, {"x": 1.041, "y": 0.967, "time": 2.5333}, {"x": 1.016, "y": 1.034, "time": 2.7333}, {"x": 1, "y": 1, "time": 3.1333}], "translate": [{"curve": "stepped", "x": 0, "y": 0.78, "time": 0}, {"x": 0, "y": 0.78, "time": 2.2}, {"x": 0, "y": 4.62, "time": 2.4}, {"x": 0, "y": 0.23, "time": 2.5333}, {"x": 0, "y": 1.33, "time": 2.7333}, {"x": 0, "y": 0.78, "time": 2.9333}]}, "J": {"scale": [{"x": 1, "y": 1, "time": 1.9}, {"x": 0.943, "y": 1.035, "time": 2.1}, {"x": 1.041, "y": 0.967, "time": 2.2333}, {"x": 1.016, "y": 1.034, "time": 2.4333}, {"x": 1, "y": 1, "time": 2.8333}], "translate": [{"curve": "stepped", "x": 0, "y": 0.78, "time": 0}, {"x": 0, "y": 0.78, "time": 1.9}, {"x": 0, "y": 4.62, "time": 2.1}, {"x": 0, "y": 0.23, "time": 2.2333}, {"x": 0, "y": 1.33, "time": 2.4333}, {"x": 0, "y": 0.78, "time": 2.6333}]}, "bone": {"scale": [{"x": 1.005, "y": 1.005, "time": 0}, {"x": 1, "y": 1, "time": 1.5667}, {"x": 1.352, "y": 1.352, "time": 1.7}, {"x": 0.959, "y": 0.959, "time": 1.8667}, {"x": 1.021, "y": 1.021, "time": 2.1667}, {"x": 1.005, "y": 1.005, "time": 2.4}]}, "J5": {"scale": [{"x": 1, "y": 1, "time": 2.3}, {"x": 0.943, "y": 1.035, "time": 2.5}, {"x": 1.041, "y": 0.967, "time": 2.6333}, {"x": 1.016, "y": 1.034, "time": 2.8333}, {"x": 1, "y": 1, "time": 3.2333}], "translate": [{"curve": "stepped", "x": 0, "y": 0.78, "time": 0}, {"x": 0, "y": 0.78, "time": 2.3}, {"x": 0, "y": 4.62, "time": 2.5}, {"x": 0, "y": 0.23, "time": 2.6333}, {"x": 0, "y": 1.33, "time": 2.8333}, {"x": 0, "y": 0.78, "time": 3.0333}]}, "J6": {"scale": [{"x": 1, "y": 1, "time": 2.4}, {"x": 0.943, "y": 1.035, "time": 2.6}, {"x": 1.041, "y": 0.967, "time": 2.7333}, {"x": 1.016, "y": 1.034, "time": 2.9333}, {"x": 1, "y": 1, "time": 3.3333}], "translate": [{"curve": "stepped", "x": 0, "y": 0.78, "time": 0}, {"x": 0, "y": 0.78, "time": 2.4}, {"x": 0, "y": 4.62, "time": 2.6}, {"x": 0, "y": 0.23, "time": 2.7333}, {"x": 0, "y": 1.33, "time": 2.9333}, {"x": 0, "y": 0.78, "time": 3.1333}]}, "J7": {"scale": [{"x": 1, "y": 1, "time": 2.5}, {"x": 0.943, "y": 1.035, "time": 2.7}, {"x": 1.041, "y": 0.967, "time": 2.8333}, {"x": 1.016, "y": 1.034, "time": 3.0333}, {"x": 1, "y": 1, "time": 3.3333}], "translate": [{"curve": "stepped", "x": 0, "y": 0.78, "time": 0}, {"x": 0, "y": 0.78, "time": 2.5}, {"x": 0, "y": 4.62, "time": 2.7}, {"x": 0, "y": 0.23, "time": 2.8333}, {"x": 0, "y": 1.33, "time": 3.0333}, {"x": 0, "y": 0.78, "time": 3.2333}]}}, "drawOrder": [{"offsets": [{"offset": -14, "slot": "icon2"}], "time": 0}]}}}