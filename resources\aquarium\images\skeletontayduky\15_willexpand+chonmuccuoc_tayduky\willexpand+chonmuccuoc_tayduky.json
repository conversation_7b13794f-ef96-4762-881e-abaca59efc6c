{"skeleton": {"hash": "+sB1aie7+ROy49gBqtQOzXXvyM4", "spine": "3.7.93", "width": 718.38, "height": 662.67, "images": "./wildexpand/", "audio": "/Users/<USER>/Trung/longshin/file Ve/tayduky/MaingameItems/wildexpand"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 35.85, "rotation": 92.16, "x": -20.54, "y": 8.82, "scaleX": 0.638, "scaleY": 0.638}, {"name": "bone2", "parent": "bone", "length": 106.19, "rotation": -7.19, "x": 35.85}, {"name": "bone3", "parent": "bone2", "length": 71.33, "rotation": -5.77, "x": 157.22, "y": 5.07}, {"name": "bone4", "parent": "bone2", "length": 77.36, "rotation": 123.23, "x": 109.8, "y": 43.46}, {"name": "bone5", "parent": "bone4", "length": 56.61, "rotation": -81.17, "x": 85.51, "y": 0.21}, {"name": "bone6", "parent": "bone5", "length": 28.66, "rotation": 11.08, "x": 67.73, "y": -1.62}, {"name": "bone7", "parent": "bone6", "length": 72.5, "rotation": -132.39, "x": -27.9, "y": -44.46}, {"name": "bone8", "parent": "bone2", "length": 96.77, "rotation": -127.73, "x": 117.72, "y": -64.26}, {"name": "bone9", "parent": "bone8", "length": 40.8, "rotation": -144.23, "x": 114.62, "y": -13.67}, {"name": "bone10", "parent": "bone9", "length": 49.4, "rotation": -24.73, "x": 40.8}, {"name": "bone11", "parent": "bone10", "length": 17.08, "rotation": 21.42, "x": 53.8, "y": 4.51}, {"name": "bone12", "parent": "bone3", "length": 29.65, "rotation": -49.01, "x": 139.73, "y": -56.18}, {"name": "bone13", "parent": "bone12", "length": 32.13, "rotation": -6.23, "x": 29.65}, {"name": "bone14", "parent": "bone13", "length": 34.53, "rotation": -2.59, "x": 32.13}, {"name": "bone15", "parent": "bone14", "length": 33.76, "rotation": -1.18, "x": 34.53}, {"name": "bone16", "parent": "bone15", "length": 29.91, "rotation": -2.03, "x": 33.76}, {"name": "bone17", "parent": "bone16", "length": 29.73, "rotation": -4.55, "x": 29.91}, {"name": "bone18", "parent": "bone17", "length": 25.27, "rotation": -8.31, "x": 29.73}, {"name": "bone19", "parent": "bone18", "length": 21.56, "rotation": -11.49, "x": 25.27}, {"name": "bone20", "parent": "bone19", "length": 19.47, "rotation": -14.83, "x": 21.56}, {"name": "bone21", "parent": "bone20", "length": 19.87, "rotation": -18.25, "x": 19.64, "y": -0.43}, {"name": "bone22", "parent": "bone21", "length": 21.68, "rotation": -22.49, "x": 19.87}, {"name": "bone23", "parent": "bone22", "length": 21.83, "rotation": -12.1, "x": 21.68}, {"name": "bone24", "parent": "bone23", "length": 24.25, "rotation": -18.32, "x": 22.72, "y": -0.26}, {"name": "bone25", "parent": "bone24", "length": 27.6, "rotation": -9.49, "x": 24.25}, {"name": "bone26", "parent": "bone25", "length": 29.77, "rotation": -5.61, "x": 27.6}, {"name": "bone27", "parent": "bone26", "length": 35.23, "rotation": -2.81, "x": 29.77}, {"name": "bone28", "parent": "bone27", "length": 39.19, "rotation": -5.23, "x": 35.23}, {"name": "bone29", "parent": "bone28", "length": 36.89, "rotation": -0.9, "x": 40.23, "y": -0.02}, {"name": "bone30", "parent": "bone29", "length": 37.95, "rotation": 1.56, "x": 37.51, "y": -0.21}, {"name": "bone31", "parent": "bone30", "length": 42.66, "rotation": 2.88, "x": 37.95}, {"name": "bone32", "parent": "bone31", "length": 38.34, "rotation": 1.16, "x": 42.66}, {"name": "bone33", "parent": "bone32", "length": 38.34, "rotation": 4.41, "x": 38.34}, {"name": "bone34", "parent": "bone33", "length": 34.01, "rotation": 6.77, "x": 38.21, "y": 0.45}, {"name": "bone35", "parent": "bone34", "length": 37.42, "rotation": 4.46, "x": 34.01}, {"name": "bone36", "parent": "bone3", "length": 28.3, "rotation": 13.09, "x": 146.01, "y": 11.04}, {"name": "bone37", "parent": "bone36", "length": 26.41, "rotation": 0.16, "x": 28.3}, {"name": "bone38", "parent": "bone37", "length": 27.96, "rotation": 1.41, "x": 26.79, "y": -0.02}, {"name": "bone39", "parent": "bone38", "length": 24.37, "rotation": 4.14, "x": 28.31, "y": -0.4}, {"name": "bone40", "parent": "bone39", "length": 19.3, "rotation": 4.4, "x": 24.37}, {"name": "bone41", "parent": "bone40", "length": 17.46, "rotation": 20.25, "x": 19.3}, {"name": "bone42", "parent": "bone41", "length": 14.21, "rotation": 35.54, "x": 17.46}, {"name": "bone43", "parent": "bone42", "length": 14.88, "rotation": 30.55, "x": 14.21}, {"name": "bone44", "parent": "bone43", "length": 17.38, "rotation": 31.86, "x": 14.88}, {"name": "bone45", "parent": "bone44", "length": 22.27, "rotation": 21.1, "x": 17.38}, {"name": "bone46", "parent": "bone45", "length": 35.64, "rotation": 5.26, "x": 22.27}, {"name": "bone47", "parent": "bone46", "length": 38.75, "rotation": 3.72, "x": 35.64}, {"name": "bone48", "parent": "bone47", "length": 42.93, "rotation": 2.47, "x": 39.11, "y": 0.12}, {"name": "bone49", "parent": "bone48", "length": 34.7, "rotation": -0.21, "x": 43.66, "y": 0.22}, {"name": "bone50", "parent": "bone49", "length": 37.95, "rotation": -3.9, "x": 36.14, "y": 0.44}, {"name": "bone51", "parent": "bone50", "length": 31.15, "rotation": -0.33, "x": 37.95}, {"name": "bone52", "parent": "bone51", "length": 32.37, "rotation": -4.98, "x": 33.67, "y": -0.23}, {"name": "bone53", "parent": "bone52", "length": 32.21, "rotation": 0.6, "x": 33.89, "y": 0.33}, {"name": "bone54", "parent": "bone53", "length": 31.87, "rotation": -1.81, "x": 32.21}, {"name": "bone55", "parent": "bone54", "length": 32.03, "rotation": 0.91, "x": 32.89, "y": -0.32}, {"name": "bone56", "parent": "bone55", "length": 31.87, "rotation": -0.91, "x": 32.03}, {"name": "bone57", "parent": "bone56", "length": 38.51, "rotation": 4.42, "x": 32.2, "y": 0.17}, {"name": "bone58", "parent": "bone2", "length": 69.7, "rotation": -148.62, "x": 50.92, "y": -52.3}, {"name": "bone59", "parent": "bone58", "length": 72.77, "rotation": 10.97, "x": 69.7}, {"name": "bone60", "parent": "bone59", "length": 92.89, "rotation": 13.69, "x": 72.77}, {"name": "bone61", "parent": "bone60", "length": 126.51, "rotation": 4.51, "x": 92.8, "y": -0.81}, {"name": "bone62", "parent": "bone2", "length": 38.78, "rotation": 166.06, "x": 100.42, "y": 58.47}, {"name": "bone63", "parent": "bone62", "length": 58.16, "rotation": 24.62, "x": 41.86, "y": -0.15}, {"name": "bone64", "parent": "bone63", "length": 86.58, "rotation": 24.98, "x": 58.16}, {"name": "bone65", "parent": "bone64", "length": 87.7, "rotation": 7.73, "x": 87.85, "y": -0.09}, {"name": "bone66", "parent": "bone65", "length": 84.57, "rotation": -10.93, "x": 87.7}, {"name": "bone67", "parent": "bone", "length": 138.08, "rotation": -170.08, "x": -38.08, "y": -7.06}, {"name": "bone68", "parent": "bone67", "length": 115.38, "rotation": 6.65, "x": 138.08}, {"name": "bone69", "parent": "bone", "length": 139.25, "rotation": 173.18, "x": -27.44, "y": 8.25}, {"name": "bone70", "parent": "bone69", "length": 105.16, "rotation": 12.21, "x": 141.13, "y": 0.15}, {"name": "bone71", "parent": "bone", "length": 12.58, "rotation": 174.98, "x": -15.42, "y": 43.62}, {"name": "bone72", "parent": "bone71", "length": 42.87, "rotation": -2.18, "x": 12.58}, {"name": "bone73", "parent": "bone72", "length": 58.42, "rotation": 3.81, "x": 42.93, "y": -0.63}, {"name": "bone74", "parent": "bone73", "length": 67.21, "rotation": 0.16, "x": 59.67, "y": 0.03}, {"name": "bone75", "parent": "bone74", "length": 58.43, "rotation": 2.92, "x": 67.21}, {"name": "bone76", "parent": "bone", "length": 59.25, "rotation": -152.71, "x": -14.54, "y": -31.84}, {"name": "bone77", "parent": "bone76", "length": 125.8, "rotation": 8.69, "x": 60.08, "y": 0.23}, {"name": "bone78", "parent": "bone", "length": 45.64, "rotation": 163.99, "x": -24.48, "y": 59.04}, {"name": "bone79", "parent": "bone78", "length": 51.9, "rotation": 3.07, "x": 45.05, "y": -0.15}, {"name": "bone80", "parent": "bone3", "x": 39.8, "y": 0.8}, {"name": "bone81", "parent": "bone3", "length": 6.46, "rotation": 52.94, "x": 33.63, "y": 6.35}, {"name": "bone82", "parent": "bone81", "length": 8.16, "rotation": -6.23, "x": 6.46}, {"name": "bone83", "parent": "bone82", "length": 7.14, "rotation": 27.53, "x": 8.16}, {"name": "bone84", "parent": "bone3", "length": 7.69, "rotation": -67.22, "x": 37.18, "y": -7.36}, {"name": "bone85", "parent": "bone84", "length": 8.32, "rotation": -2.51, "x": 7.69}, {"name": "bone86", "parent": "bone85", "length": 5.63, "rotation": -41.22, "x": 8.28, "y": -0.22}, {"name": "bone87", "parent": "bone2", "length": 4.03, "rotation": -129.97, "x": 104.31, "y": 24.49}, {"name": "bone88", "parent": "bone87", "length": 21.9, "rotation": -37.53, "x": 4.53, "y": -0.5}, {"name": "bone89", "parent": "bone88", "length": 26.7, "rotation": -8.24, "x": 21.9}, {"name": "bone90", "parent": "bone89", "length": 37.74, "rotation": 0.22, "x": 26.7}, {"name": "bone91", "parent": "bone2", "length": 3.83, "rotation": 116.83, "x": 107.18, "y": 36.54}, {"name": "bone92", "parent": "bone91", "length": 25.82, "rotation": 53.01, "x": 3.83}, {"name": "bone93", "parent": "bone92", "length": 32.2, "rotation": 9.48, "x": 26.6, "y": -0.16}, {"name": "bone94", "parent": "bone93", "length": 30.72, "rotation": 1.06, "x": 33.62, "y": 0.14}, {"name": "bone95", "parent": "bone3", "length": 1.68, "rotation": 169, "x": 58.88, "y": 1.07}, {"name": "bone96", "parent": "bone95", "length": 7.34, "rotation": 9.54, "x": 2.46, "y": 0.14}, {"name": "bone97", "parent": "bone96", "length": 9.37, "rotation": 14.17, "x": 7.34}, {"name": "bone98", "parent": "bone10", "length": 15.57, "rotation": -1.97, "x": 48.69, "y": -13.31}, {"name": "bone99", "parent": "bone98", "length": 14.44, "rotation": -0.56, "x": 15.57}, {"name": "bone102", "parent": "root"}, {"name": "bone100", "parent": "bone3", "length": 20.82, "rotation": -1.73, "x": 53.64, "y": 43.69}, {"name": "bone101", "parent": "bone3", "length": 12.49, "rotation": -10.17, "x": 42.07, "y": -42.82}, {"name": "bone103", "parent": "bone", "x": -487.44, "y": -26.17, "scaleX": 1.589, "scaleY": 1.589}, {"name": "bone105", "parent": "root"}, {"name": "bone106", "parent": "root"}, {"name": "bone107", "parent": "root", "x": 1.84, "y": 1.23, "scaleX": 0.956, "scaleY": 1.025}, {"name": "bone108", "parent": "root", "x": 1.84, "y": 1.23, "scaleX": 0.956, "scaleY": 1.025}, {"name": "bone109", "parent": "bone108", "length": 40.2, "rotation": 105.95, "x": 31.02, "y": -43.85}, {"name": "bone110", "parent": "bone109", "length": 126.82, "rotation": -21.36, "x": 40.2}, {"name": "bone111", "parent": "bone110", "length": 17.33, "rotation": 29.02, "x": 140.39, "y": 22.66}, {"name": "bone112", "parent": "bone110", "length": 13.51, "rotation": -128.94, "x": 141.1, "y": -27.76}, {"name": "bone113", "parent": "bone112", "length": 72.87, "rotation": -32.13, "x": 13.51}, {"name": "bone114", "parent": "bone113", "length": 57.08, "rotation": 21.4, "x": 72.87}, {"name": "bone115", "parent": "bone113", "length": 42.26, "rotation": -51.08, "x": 119.9, "y": -19.34}, {"name": "bone116", "parent": "bone115", "length": 53.33, "rotation": -24.07, "x": 42.26}, {"name": "bone117", "parent": "bone116", "length": 56.34, "rotation": -9.69, "x": 53.33}, {"name": "bone118", "parent": "bone117", "length": 65.81, "rotation": -1.99, "x": 56.34}, {"name": "bone119", "parent": "bone114", "length": 21.51, "rotation": 5.36, "x": 66.34, "y": -3.02}, {"name": "bone120", "parent": "bone119", "length": 11.7, "rotation": 25.36, "x": 28.24, "y": 15.11}, {"name": "bone121", "parent": "bone119", "length": 13.59, "rotation": 67.15, "x": 6.43, "y": 29.64}, {"name": "bone122", "parent": "bone121", "length": 21.52, "rotation": 5.05, "x": 13.59}, {"name": "bone123", "parent": "bone111", "length": 5.03, "rotation": 170.07, "x": 42.83, "y": -11.86}, {"name": "bone124", "parent": "bone123", "length": 20.44, "rotation": -12.33, "x": 5.03}, {"name": "bone125", "parent": "bone124", "length": 24.02, "rotation": -19.45, "x": 20.44}, {"name": "bone126", "parent": "bone125", "length": 22.7, "rotation": -12.95, "x": 24.02}, {"name": "bone127", "parent": "bone126", "length": 39.25, "rotation": -8.31, "x": 22.7}, {"name": "bone128", "parent": "bone127", "length": 37.52, "rotation": -31.53, "x": 39.25}, {"name": "bone129", "parent": "bone111", "length": 11.84, "rotation": 144.15, "x": 18.83, "y": 20.5}, {"name": "bone130", "parent": "bone129", "length": 13.5, "rotation": -6.55, "x": 11.84}, {"name": "bone131", "parent": "bone130", "length": 20.07, "rotation": -24.72, "x": 13.41, "y": -0.68}, {"name": "bone132", "parent": "bone131", "length": 25.64, "rotation": -14.48, "x": 20.07}, {"name": "bone133", "parent": "bone132", "length": 27.83, "rotation": -6.36, "x": 25.64}, {"name": "bone134", "parent": "bone133", "length": 27.86, "rotation": -20.98, "x": 27.83}, {"name": "bone135", "parent": "bone110", "length": 61.85, "rotation": 140.96, "x": 59.18, "y": 25.88}, {"name": "bone136", "parent": "bone135", "length": 96.63, "rotation": -30.86, "x": 77.77, "y": 28.54}, {"name": "bone137", "parent": "bone136", "length": 28.78, "rotation": 31.7, "x": 76.92, "y": 30.54}, {"name": "bone138", "parent": "bone137", "length": 49.85, "rotation": -3.76, "x": 28.78}, {"name": "bone139", "parent": "bone138", "length": 56.04, "rotation": 0.7, "x": 49.85}, {"name": "bone140", "parent": "bone136", "length": 18.1, "rotation": -75.12, "x": 104.8, "y": -23.02}, {"name": "bone141", "parent": "bone109", "length": 43.94, "rotation": -179.06, "x": -21.52, "y": -0.92}, {"name": "bone142", "parent": "bone141", "length": 41.29, "rotation": 6.69, "x": 43.94}, {"name": "bone143", "parent": "bone142", "length": 42.24, "rotation": 2.8, "x": 41.29}, {"name": "bone144", "parent": "bone143", "length": 61, "rotation": 8.96, "x": 49.52, "y": -1.1}, {"name": "bone145", "parent": "bone144", "length": 55.91, "rotation": 4.79, "x": 67.74, "y": -0.18}, {"name": "bone146", "parent": "bone109", "length": 48.33, "rotation": 142.16, "x": -20.14, "y": 54.91}, {"name": "bone147", "parent": "bone146", "length": 38.67, "rotation": -9.72, "x": 49.63, "y": 0.52}, {"name": "bone148", "parent": "bone147", "length": 53.87, "rotation": -8.26, "x": 38.67}, {"name": "bone149", "parent": "bone148", "length": 47.68, "rotation": -5.25, "x": 53.87}, {"name": "bone150", "parent": "bone149", "length": 69.54, "rotation": -14.62, "x": 49.23, "y": -0.56}, {"name": "bone151", "parent": "bone149", "length": 63.46, "rotation": -38.53, "x": 62.29, "y": -63}, {"name": "bone152", "parent": "bone151", "length": 96.11, "rotation": -6.34, "x": 63.54, "y": 0.7}, {"name": "bone153", "parent": "bone109", "length": 34.9, "rotation": 147.91, "x": 40.61, "y": 50.06}, {"name": "bone154", "parent": "bone153", "length": 48.54, "rotation": -12.33, "x": 34.9}, {"name": "bone155", "parent": "bone154", "length": 51.9, "rotation": -15.39, "x": 48.54}, {"name": "bone156", "parent": "bone155", "length": 58.98, "rotation": -5.98, "x": 51.9}, {"name": "bone157", "parent": "bone156", "length": 71.51, "rotation": -7.34, "x": 58.98}, {"name": "bone161", "parent": "bone110", "length": 7.12, "rotation": 134.38, "x": 152.19, "y": 0.07}, {"name": "bone162", "parent": "bone161", "length": 30.31, "rotation": 23.47, "x": 7.12}, {"name": "bone163", "parent": "bone162", "length": 49.82, "rotation": 0.69, "x": 30.31}, {"name": "bone164", "parent": "bone163", "length": 46.25, "rotation": -0.71, "x": 49.82}, {"name": "bone165", "parent": "bone164", "length": 31.44, "rotation": -27.69, "x": 46.25}, {"name": "bone166", "parent": "bone110", "length": 12.11, "rotation": 145.61, "x": 83.2, "y": 56.19}, {"name": "bone167", "parent": "bone166", "length": 26.25, "rotation": 5.58, "x": 12.11}, {"name": "bone168", "parent": "bone167", "length": 23.21, "rotation": 3.64, "x": 26.25}, {"name": "bone169", "parent": "bone168", "length": 20.32, "rotation": 20.12, "x": 23.21}, {"name": "bone158", "parent": "root", "length": 7.64, "rotation": 90, "x": 3.15, "y": -190.07}, {"name": "bone159", "parent": "root", "length": 7.64, "rotation": 90, "x": 3.15, "y": -190.07}, {"name": "bone160", "parent": "root", "length": 15.76, "rotation": 92.05, "x": 31.12, "y": 11.46, "scaleY": -0.999}, {"name": "bone170", "parent": "bone160", "length": 109.63, "rotation": -1.49, "x": 15.76}, {"name": "bone171", "parent": "bone170", "length": 38.31, "rotation": 16.99, "x": 118.74, "y": 3.71}, {"name": "bone172", "parent": "bone171", "length": 5.78, "rotation": 83.4, "x": 44.39, "y": 17.42}, {"name": "bone173", "parent": "bone172", "length": 17.44, "rotation": -9.07, "x": 5.78}, {"name": "bone174", "parent": "bone171", "length": 5.65, "rotation": 117.39, "x": 40.34, "y": -20.32}, {"name": "bone175", "parent": "bone170", "length": 61.67, "rotation": -121.18, "x": 82.7, "y": -66.2}, {"name": "bone176", "parent": "bone175", "length": 21.74, "rotation": -26.36, "x": 82.34, "y": -20.28}, {"name": "bone177", "parent": "bone176", "length": 24.5, "rotation": -4.24, "x": 27.21, "y": 0.05}, {"name": "bone178", "parent": "bone177", "length": 29.07, "rotation": 2.84, "x": 27.95, "y": 0.3}, {"name": "bone179", "parent": "bone175", "length": 25.91, "rotation": -110.64, "x": 71.17, "y": -13.12}, {"name": "bone180", "parent": "bone160", "length": 17.62, "rotation": -141.35, "x": 37.85, "y": 82.61}, {"name": "bone181", "parent": "bone180", "length": 18.93, "rotation": -69.66, "x": 8, "y": -24.29}, {"name": "bone182", "parent": "bone181", "length": 25.44, "rotation": -17.93, "x": 23.85, "y": 0.84}, {"name": "bone183", "parent": "bone160", "length": 32.87, "rotation": 148.83, "x": -22.17, "y": 27.87}, {"name": "bone184", "parent": "bone183", "length": 42.24, "rotation": -10.57, "x": 33.47, "y": -0.36}, {"name": "bone185", "parent": "bone160", "length": 13.7, "rotation": 178.87, "x": -22.4, "y": -0.5}, {"name": "bone186", "parent": "root", "length": 23.86, "rotation": 90, "x": -10.36, "y": 28.55, "scaleY": -1.002}, {"name": "bone187", "parent": "bone186", "length": 95.42, "rotation": -14.12, "x": 26.19, "y": -0.58}, {"name": "bone188", "parent": "bone187", "length": 44.7, "rotation": 23.02, "x": 118.78, "y": 20.71}, {"name": "bone189", "parent": "bone188", "length": 6.54, "rotation": 174.47, "x": 21.12, "y": 15.74}, {"name": "bone190", "parent": "bone187", "length": 16.9, "rotation": 172.8, "x": 131.48, "y": -3.02}, {"name": "bone191", "parent": "bone190", "length": 37.37, "rotation": -18.26, "x": 16.9}, {"name": "bone192", "parent": "bone191", "length": 40.9, "rotation": -16.13, "x": 41.99, "y": -0.17}, {"name": "bone193", "parent": "bone187", "length": 13.87, "rotation": 152.49, "x": 118.83, "y": 50.42}, {"name": "bone194", "parent": "bone193", "length": 33.69, "rotation": 17.41, "x": 19.93, "y": 1.28}, {"name": "bone195", "parent": "bone187", "length": 7.57, "rotation": 163.66, "x": 71.41, "y": 57.89}, {"name": "bone196", "parent": "bone195", "length": 51.45, "rotation": 8.38, "x": 7.57}, {"name": "bone197", "parent": "bone196", "length": 62.92, "rotation": -1.38, "x": 51.45}, {"name": "bone198", "parent": "bone197", "length": 33.59, "rotation": 9.44, "x": 70.57, "y": 0.33}, {"name": "bone199", "parent": "bone187", "length": 27.77, "rotation": -136.37, "x": 74.67, "y": -49.46}, {"name": "bone200", "parent": "bone199", "length": 8.98, "rotation": -53.47, "x": 42.33, "y": 9.05}, {"name": "bone201", "parent": "bone188", "x": 37.5, "y": -27.01}, {"name": "bone202", "parent": "bone188", "x": 45.03, "y": 32.49}, {"name": "bone203", "parent": "root", "x": 5.79, "y": -294.07}], "slots": [{"name": "bgtank", "bone": "root", "attachment": "bgtank"}, {"name": "BGfire0", "bone": "bone105", "attachment": "BGfire0"}, {"name": "duongtank15", "bone": "root", "attachment": "duongtank12"}, {"name": "Monkeyking25", "bone": "root", "attachment": "Monkeyking25"}, {"name": "Monkeyking20", "bone": "root", "attachment": "Monkeyking20"}, {"name": "Monkeyking23", "bone": "root", "attachment": "Monkeyking23"}, {"name": "WildexpandBorderlight0", "bone": "bone107", "attachment": "WildexpandBorderlight0"}, {"name": "Monkeyking27", "bone": "root", "attachment": "Monkeyking27"}, {"name": "Monkeyking24", "bone": "root", "attachment": "Monkeyking24"}, {"name": "Monkeyking19", "bone": "root", "attachment": "Monkeyking19"}, {"name": "Monkeyking18", "bone": "root", "attachment": "Monkeyking18"}, {"name": "Monkeyking26", "bone": "root", "attachment": "Monkeyking26"}, {"name": "Monkeyking6", "bone": "root", "attachment": "Monkeyking6"}, {"name": "Monkeyking17", "bone": "root", "attachment": "Monkeyking17"}, {"name": "Monkeyking2", "bone": "root", "attachment": "Monkeyking2"}, {"name": "Monkeyking22", "bone": "root", "attachment": "Monkeyking22"}, {"name": "Monkeyking3", "bone": "root", "attachment": "Monkeyking3"}, {"name": "Monkeyking1", "bone": "root", "attachment": "Monkeyking1"}, {"name": "Monkeyking16", "bone": "root", "attachment": "Monkeyking16"}, {"name": "Monkeyking15", "bone": "root", "attachment": "Monkeyking15"}, {"name": "Monkeyking14", "bone": "root", "attachment": "Monkeyking14"}, {"name": "Monkeyking13", "bone": "root", "attachment": "Monkeyking13"}, {"name": "Monkeyking11", "bone": "root", "attachment": "Monkeyking11"}, {"name": "Monkeyking10", "bone": "root", "attachment": "Monkeyking10"}, {"name": "Monkeyking12", "bone": "root", "attachment": "Monkeyking12"}, {"name": "Monkeyking21", "bone": "root", "attachment": "Monkeyking21"}, {"name": "Monkeyking7", "bone": "root", "attachment": "Monkeyking7"}, {"name": "Monkeyking8", "bone": "root", "attachment": "Monkeyking8"}, {"name": "Monkeyking5", "bone": "root", "attachment": "Monkeyking5"}, {"name": "Monkeyking4", "bone": "root", "attachment": "Monkeyking4"}, {"name": "Monkeyking9", "bone": "root", "attachment": "Monkeyking9"}, {"name": "bone102", "bone": "bone102"}, {"name": "duongtank10", "bone": "root", "attachment": "duongtank3"}, {"name": "duongtank13", "bone": "root", "attachment": "duongtank2"}, {"name": "duongtank14", "bone": "root", "attachment": "duongtank6"}, {"name": "path", "bone": "bone106"}, {"name": "duongtank12", "bone": "root", "attachment": "duongtank12"}, {"name": "duongtank11", "bone": "root", "attachment": "duongtank11"}, {"name": "duongtank9", "bone": "root", "attachment": "duongtank9"}, {"name": "duongtank8", "bone": "root", "attachment": "duongtank8"}, {"name": "duongtank7", "bone": "root", "attachment": "duongtank7"}, {"name": "duongtank5", "bone": "root", "attachment": "duongtank5"}, {"name": "WildexpandBorderlight1", "bone": "bone108", "attachment": "WildexpandBorderlight0"}, {"name": "duongtank6", "bone": "root", "attachment": "duongtank6"}, {"name": "duongtank1", "bone": "root", "attachment": "duongtank1"}, {"name": "duongtank3", "bone": "root", "attachment": "duongtank3"}, {"name": "duongtank4", "bone": "root", "attachment": "duongtank4"}, {"name": "duongtank2", "bone": "root", "attachment": "duongtank2"}, {"name": "wild text", "bone": "bone158", "attachment": "wild text"}, {"name": "batgioifull9", "bone": "root", "attachment": "batgioifull9"}, {"name": "wild text2", "bone": "bone159", "attachment": "wild text", "blend": "screen"}, {"name": "batgioifull8", "bone": "root", "attachment": "batgioifull8"}, {"name": "batgioifull2", "bone": "root", "attachment": "batgioifull2"}, {"name": "batgioifull6", "bone": "root", "attachment": "batgioifull6"}, {"name": "batgioifull7", "bone": "root", "attachment": "batgioifull7"}, {"name": "batgioifull5", "bone": "root", "attachment": "batgioifull5"}, {"name": "batgioifull3", "bone": "root", "attachment": "batgioifull3"}, {"name": "batgioifull1", "bone": "root", "attachment": "batgioifull1"}, {"name": "batgioifull4", "bone": "root", "attachment": "batgioifull4"}, {"name": "satangfull9", "bone": "root", "attachment": "satangfull9"}, {"name": "satangfull4", "bone": "root", "attachment": "satangfull4"}, {"name": "cloud", "bone": "bone103"}, {"name": "satangfull6", "bone": "root", "attachment": "satangfull6"}, {"name": "satangfull2", "bone": "root", "attachment": "satangfull2"}, {"name": "satangfull8", "bone": "root", "attachment": "satangfull8"}, {"name": "satangfull5", "bone": "root", "attachment": "satangfull5"}, {"name": "satangfull7", "bone": "root", "attachment": "satangfull7"}, {"name": "satangfull1", "bone": "root", "attachment": "satangfull1"}, {"name": "satangfull3", "bone": "root", "attachment": "satangfull3"}, {"name": "chonmuccuocboard", "bone": "root"}, {"name": "chonmuccuocboard2", "bone": "root"}, {"name": "brush", "bone": "root"}, {"name": "muccuoc0", "bone": "bone203"}], "path": [{"name": "rau1", "order": 0, "bones": ["bone12", "bone13", "bone14", "bone15", "bone16", "bone17", "bone18", "bone19", "bone20", "bone21", "bone22", "bone23", "bone24", "bone25", "bone26", "bone27", "bone28", "bone29", "bone30", "bone31", "bone32", "bone33", "bone34", "bone35"], "target": "bone102", "rotateMode": "chain"}], "skins": {"default": {"BGfire0": {"BGfire0": {"scaleX": 2.32, "scaleY": 2.32, "width": 82, "height": 200}, "BGfire1": {"scaleX": 2.32, "scaleY": 2.32, "width": 82, "height": 200}, "BGfire2": {"scaleX": 2.32, "scaleY": 2.32, "width": 82, "height": 200}, "BGfire3": {"scaleX": 2.32, "scaleY": 2.32, "width": 82, "height": 200}, "BGfire4": {"scaleX": 2.32, "scaleY": 2.32, "width": 82, "height": 200}, "BGfire5": {"scaleX": 2.32, "scaleY": 2.32, "width": 82, "height": 200}}, "Monkeyking1": {"Monkeyking1": {"type": "mesh", "uvs": [0.30995, 0.00102, 0.28224, 0.09855, 0.16584, 0.15057, 0.10487, 0.20258, 0, 0.37294, 1e-05, 0.5628, 0.04021, 0.69545, 0.00141, 0.80988, 0.05868, 1, 0.24159, 1, 0.36722, 0.89441, 0.42501, 0.8898, 0.74228, 0.8645, 0.70348, 0.72015, 0.84205, 0.59661, 0.8365, 0.52639, 1, 0.36124, 1, 0.19868, 0.879, 0.11155, 0.70533, 0.02963, 0.65729, 0, 0.71087, 0.15447, 0.6111, 0.19738, 0.55013, 0.2442, 0.47069, 0.41455, 0.42635, 0.60832, 0.41711, 0.75266], "triangles": [21, 19, 18, 22, 0, 20, 21, 22, 20, 21, 20, 19, 23, 1, 0, 22, 23, 0, 18, 17, 16, 24, 1, 23, 16, 15, 21, 16, 21, 18, 22, 21, 15, 23, 22, 15, 24, 23, 15, 24, 5, 4, 24, 2, 1, 13, 25, 24, 15, 13, 24, 24, 3, 2, 3, 24, 4, 25, 5, 24, 6, 5, 25, 14, 13, 15, 26, 6, 25, 26, 25, 13, 11, 26, 13, 11, 13, 12, 10, 6, 26, 10, 26, 11, 7, 6, 10, 9, 8, 7, 10, 9, 7], "vertices": [1, 2, 166.47, 31.99, 1, 1, 2, 143.43, 34.53, 1, 1, 2, 129.68, 52.48, 1, 1, 2, 116.73, 61.38, 1, 1, 2, 75.69, 75.03, 1, 2, 1, 76.13, 66.63, 0.04228, 2, 31.62, 71.15, 0.95772, 2, 1, 45, 61.21, 0.32065, 2, 1.41, 61.87, 0.67935, 2, 1, 18.59, 68.58, 0.69127, 2, -25.71, 65.88, 0.30873, 2, 1, -26.03, 60.86, 0.97316, 2, -69.01, 52.64, 0.02684, 2, 1, -27.16, 30.88, 0.99942, 2, -66.38, 22.75, 0.00058, 2, 1, -3.35, 9.37, 0.99902, 2, -40.07, 4.39, 0.00098, 1, 1, -2.64, -0.14, 1, 2, 1, 1.29, -52.36, 0.81667, 2, -27.74, -56.27, 0.18333, 2, 1, 35.14, -47.27, 0.39485, 2, 5.21, -46.99, 0.60515, 2, 1, 63.05, -71.06, 0.04138, 2, 35.88, -67.1, 0.95862, 2, 1, 79.43, -70.77, 0.01085, 2, 52.1, -64.76, 0.98915, 1, 2, 92.78, -88.1, 1, 1, 2, 130.51, -84.78, 1, 1, 2, 148.99, -63.23, 1, 1, 2, 165.51, -33.19, 1, 1, 2, 171.7, -24.73, 1, 1, 2, 136.61, -36.64, 1, 1, 2, 125.22, -21.22, 1, 1, 2, 113.48, -12.21, 1, 1, 2, 72.79, -2.72, 1, 1, 2, 27.18, 0.57, 1, 2, 1, 29.34, -0.05, 0.99943, 2, -6.45, -0.87, 0.00057], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 36, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 20, 22, 22, 24, 52, 22], "width": 164, "height": 233}}, "Monkeyking10": {"Monkeyking10": {"type": "mesh", "uvs": [0, 0.45147, 0.26653, 0.17848, 0.59875, 0, 0.87319, 0, 1, 0.42547, 1, 1, 0.90689, 1, 0.878, 0.76347, 0.80097, 0.63347, 0.60356, 0.64647, 0.30023, 0.78947, 0, 0.93247], "triangles": [5, 7, 4, 5, 6, 7, 7, 8, 4, 4, 8, 2, 8, 9, 2, 9, 1, 2, 4, 2, 3, 11, 0, 10, 9, 10, 1, 10, 0, 1], "vertices": [1, 84, -4.05, 2.18, 1, 1, 84, 3.56, 3.36, 1, 1, 85, 4.87, 3.46, 1, 2, 85, 12.17, 2.24, 0.72651, 86, 1.3, 4.42, 0.27349, 2, 85, 14.85, -2.52, 0.037, 86, 6.45, 2.6, 0.963, 1, 86, 9.48, -2.28, 1, 1, 86, 7.34, -3.61, 1, 1, 86, 5.43, -2.01, 1, 1, 86, 2.98, -2, 1, 3, 84, 11.49, -3.11, 0.00739, 85, 3.93, -2.94, 0.8206, 86, -1.48, -4.91, 0.17201, 1, 84, 3.18, -2.81, 1, 1, 84, -5.05, -2.53, 1], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22], "width": 27, "height": 10}}, "Monkeyking11": {"Monkeyking11": {"type": "mesh", "uvs": [0.86319, 1, 1, 0.88838, 0.82605, 0.63522, 0.58462, 0.24523, 0.37415, 0, 0.10177, 0, 0, 0.15628, 0, 0.2726, 0.17605, 0.31365, 0.43605, 0.60786, 0.64033, 0.8268], "triangles": [8, 6, 5, 8, 4, 3, 8, 5, 4, 8, 7, 6, 9, 3, 2, 9, 8, 3, 1, 0, 2, 0, 10, 2, 10, 9, 2], "vertices": [1, 81, -1.61, 1.78, 1, 1, 81, -1.97, -1.77, 1, 2, 81, 4.05, -2.29, 0.9181, 82, -2.14, -2.54, 0.0819, 2, 82, 6.83, -2.78, 0.91903, 83, -2.46, -1.85, 0.08097, 1, 83, 3.57, -4.04, 1, 1, 83, 8.69, -1.48, 1, 1, 83, 9.27, 2.13, 1, 1, 83, 8.28, 4.11, 1, 2, 82, 10.81, 4.93, 0.00453, 83, 4.63, 3.15, 0.99547, 3, 81, 9.93, 3.43, 0.00024, 82, 3.08, 3.79, 0.93287, 83, -2.75, 5.71, 0.06689, 2, 81, 3.97, 3.04, 0.89354, 82, -2.8, 2.75, 0.10646], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "width": 21, "height": 19}}, "Monkeyking12": {"Monkeyking12": {"type": "mesh", "uvs": [0, 0, 1, 0.36691, 1, 0.52977, 0.88455, 0.8012, 0.61011, 1, 0.35677, 1, 0.09816, 0.57727, 0, 0.20406], "triangles": [3, 4, 6, 4, 5, 6, 3, 6, 2, 2, 6, 1, 6, 7, 1, 1, 7, 0], "vertices": [3, 95, 2.38, -19.67, 0.89829, 96, -3.37, -19.53, 0.02926, 97, -15.17, -16.31, 0.07245, 3, 95, -1.45, 17.57, 0.30396, 96, -0.98, 17.83, 0.39527, 97, -3.7, 19.32, 0.30077, 3, 95, 2.78, 19.26, 0.27245, 96, 3.48, 18.8, 0.37648, 97, 0.86, 19.17, 0.35107, 3, 95, 11.38, 18.22, 0.14339, 96, 11.79, 16.35, 0.26338, 97, 8.31, 14.77, 0.59323, 3, 95, 20.22, 11.12, 0.01077, 96, 19.33, 7.88, 0.02908, 97, 13.55, 4.71, 0.96016, 2, 95, 23.6, 2.65, 0.00155, 97, 13.24, -4.41, 0.99845, 3, 95, 16.07, -10.39, 0.36391, 96, 11.68, -12.64, 0.14633, 97, 1.1, -13.32, 0.48975, 3, 95, 7.68, -17.55, 0.82974, 96, 2.22, -18.32, 0.05865, 97, -9.46, -16.5, 0.11161], "hull": 8, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 0, 14], "width": 36, "height": 28}}, "Monkeyking13": {"Monkeyking13": {"type": "mesh", "uvs": [0.1682, 0.94912, 0.20985, 1, 0.29317, 1, 0.51577, 0.93912, 0.66808, 0.82037, 0.74358, 0.50912, 0.685, 0.35412, 0.64855, 0.32662, 0.73317, 0.29662, 0.9037, 0.24663, 1, 0.22663, 1, 0.12413, 0.85814, 0.11538, 0.65506, 0.16788, 0.58476, 0.08038, 0.41553, 0.09538, 0.32831, 0.05288, 0.27624, 0, 0.22938, 0.00163, 0.22287, 0.12538, 0.31399, 0.23038, 0.27885, 0.26163, 0.19423, 0.24538, 0.08879, 0.35287, 0, 0.52912, 0, 0.71537, 0.05755, 0.88912, 0.05755, 0.97162, 0.10701, 0.99787, 0.36743, 0.75995, 0.38834, 0.74991, 0.4109, 0.74885, 0.43457, 0.7515, 0.45493, 0.76259, 0.46869, 0.77844, 0.45163, 0.79535, 0.43182, 0.80381, 0.4087, 0.80117, 0.39109, 0.79271, 0.37569, 0.77632, 0.26398, 0.73829, 0.24637, 0.72138, 0.22656, 0.70764, 0.2084, 0.69496, 0.18914, 0.68914, 0.17318, 0.68492, 0.17428, 0.70922, 0.18694, 0.72712, 0.20785, 0.73714, 0.23096, 0.74296, 0.25022, 0.74299, 0.25682, 0.73142, 0.27773, 0.7367, 0.26508, 0.71768, 0.24362, 0.68967, 0.22601, 0.66854, 0.19684, 0.65427, 0.16272, 0.65585, 0.13741, 0.67488, 0.13851, 0.71926, 0.15887, 0.76465, 0.21115, 0.78711, 0.26067, 0.77035, 0.27718, 0.75196, 0.33606, 0.76941, 0.34762, 0.74674, 0.36798, 0.72666, 0.4109, 0.71292, 0.45383, 0.70764, 0.48354, 0.72085, 0.5006, 0.76788, 0.48134, 0.80539, 0.43952, 0.84185, 0.37514, 0.83868, 0.34102, 0.8036, 0.20179, 0.86488, 0.2128, 0.8872, 0.23866, 0.90268, 0.26453, 0.90915, 0.28269, 0.91983, 0.308, 0.92259, 0.32781, 0.91626, 0.1449, 0.87275, 0.17075, 0.92647, 0.20198, 0.97466, 0.22728, 0.99054, 0.26286, 0.99583, 0.31363, 0.98071, 0.37357, 0.94616, 0.39635, 0.93367, 0.18155, 0.53433, 0.24339, 0.56808, 0.43149, 0.63058, 0.5109, 0.6337, 0.5428, 0.63683, 0.16987, 0.56351, 0.17831, 0.59594, 0.14714, 0.6134, 0.12051, 0.67389, 0.09843, 0.64833, 0.07505, 0.6502, 0.05492, 0.67202, 0.05362, 0.70819, 0.07115, 0.76058, 0.09778, 0.78604, 0.14519, 0.80341, 0.15623, 0.83814, 0.51982, 0.66065, 0.52525, 0.70964, 0.52188, 0.76692, 0.55406, 0.7657, 0.56983, 0.78085, 0.56542, 0.80993, 0.52567, 0.85052, 0.49539, 0.86384, 0.44618, 0.86505, 0.40328, 0.89472], "triangles": [23, 22, 21, 24, 23, 90, 3, 88, 89, 3, 115, 114, 3, 89, 115, 89, 116, 115, 3, 2, 88, 2, 87, 88, 1, 86, 2, 1, 85, 86, 2, 86, 87, 0, 84, 1, 1, 84, 85, 28, 27, 0, 87, 79, 80, 87, 86, 79, 79, 86, 78, 86, 85, 78, 85, 77, 78, 85, 84, 77, 87, 81, 88, 87, 80, 81, 0, 83, 84, 77, 83, 76, 77, 84, 83, 0, 27, 83, 83, 27, 26, 82, 83, 26, 89, 88, 116, 4, 113, 112, 4, 3, 113, 3, 114, 113, 116, 88, 81, 83, 75, 76, 83, 82, 75, 81, 80, 78, 80, 79, 78, 116, 81, 73, 77, 62, 78, 73, 81, 74, 81, 78, 74, 74, 62, 64, 64, 62, 63, 62, 74, 78, 62, 77, 75, 116, 72, 115, 116, 73, 72, 26, 25, 103, 26, 104, 82, 26, 103, 104, 103, 25, 102, 77, 76, 75, 62, 75, 61, 82, 106, 75, 106, 104, 105, 106, 82, 104, 115, 72, 114, 75, 106, 61, 109, 113, 71, 71, 70, 109, 72, 71, 114, 113, 114, 71, 112, 109, 110, 112, 113, 109, 73, 37, 72, 37, 36, 72, 72, 35, 71, 72, 36, 35, 73, 38, 37, 38, 74, 39, 38, 73, 74, 106, 105, 61, 112, 111, 4, 5, 4, 94, 112, 110, 111, 35, 34, 71, 71, 34, 70, 35, 36, 32, 39, 64, 29, 39, 74, 64, 105, 104, 60, 105, 60, 61, 60, 104, 59, 36, 37, 32, 37, 31, 32, 37, 38, 31, 35, 33, 34, 35, 32, 33, 39, 30, 38, 38, 30, 31, 62, 49, 50, 62, 61, 49, 61, 60, 48, 60, 47, 48, 61, 48, 49, 59, 98, 58, 102, 98, 59, 59, 104, 103, 94, 4, 111, 34, 33, 70, 39, 29, 30, 63, 50, 40, 63, 62, 50, 64, 65, 29, 65, 64, 52, 109, 70, 69, 70, 33, 69, 109, 108, 110, 109, 69, 108, 108, 94, 110, 111, 110, 94, 47, 59, 46, 47, 60, 59, 69, 33, 68, 100, 98, 101, 100, 99, 98, 102, 59, 103, 101, 98, 102, 30, 29, 66, 64, 63, 52, 63, 40, 52, 31, 67, 32, 33, 32, 68, 32, 67, 68, 29, 65, 66, 31, 30, 67, 67, 30, 66, 65, 52, 66, 50, 51, 40, 51, 50, 41, 50, 49, 41, 41, 49, 42, 52, 40, 53, 49, 48, 42, 42, 48, 43, 40, 51, 53, 52, 53, 66, 51, 41, 53, 48, 47, 43, 43, 46, 44, 43, 47, 46, 67, 66, 92, 53, 41, 54, 108, 69, 107, 59, 45, 46, 59, 58, 45, 41, 42, 54, 54, 92, 66, 102, 25, 101, 68, 67, 92, 69, 68, 107, 108, 107, 94, 46, 45, 44, 101, 25, 24, 54, 66, 53, 68, 93, 107, 68, 92, 93, 42, 43, 54, 43, 55, 54, 55, 43, 56, 92, 54, 91, 43, 44, 56, 44, 45, 56, 58, 57, 45, 45, 57, 56, 57, 58, 97, 97, 58, 98, 101, 24, 100, 54, 55, 91, 55, 56, 91, 107, 93, 94, 98, 99, 97, 56, 57, 96, 57, 97, 96, 56, 96, 91, 99, 100, 97, 97, 100, 24, 5, 94, 6, 6, 94, 93, 91, 21, 92, 21, 20, 92, 92, 20, 7, 6, 93, 7, 93, 92, 7, 13, 7, 15, 15, 7, 20, 97, 95, 96, 97, 24, 95, 96, 95, 91, 95, 90, 91, 91, 90, 21, 95, 24, 90, 90, 23, 21, 7, 13, 8, 13, 15, 14, 8, 12, 9, 8, 13, 12, 10, 9, 11, 20, 19, 16, 17, 16, 19, 20, 16, 15, 17, 19, 18, 9, 12, 11], "vertices": [1, 3, -8.42, 17.91, 1, 1, 3, -15.68, 9.36, 1, 1, 3, -13.04, -4.47, 1, 2, 3, 4.53, -39.41, 0.616, 102, -37.55, -3.28, 0.384, 2, 3, 29.89, -60.78, 0.344, 102, -8.82, -19.83, 0.656, 2, 3, 86.09, -63.05, 0.264, 102, 46.9, -12.14, 0.736, 2, 3, 111.03, -48.22, 0.752, 102, 68.82, 6.87, 0.248, 1, 3, 114.63, -41.26, 1, 1, 3, 122.49, -54.31, 1, 1, 3, 136.54, -80.98, 1, 1, 3, 143.05, -96.3, 1, 1, 3, 160.77, -92.92, 1, 1, 3, 157.79, -69.08, 1, 1, 3, 142.28, -37.1, 1, 1, 3, 155.18, -22.55, 1, 1, 3, 147.23, 5.05, 1, 1, 3, 151.81, 20.93, 1, 1, 3, 159.31, 31.32, 1, 1, 3, 157.54, 39.05, 1, 1, 3, 135.94, 36.04, 1, 1, 3, 120.67, 17.46, 1, 2, 3, 114.16, 22.26, 0.768, 101, 67.46, -21.29, 0.232, 2, 3, 114.29, 36.84, 0.152, 101, 67.15, -6.71, 0.848, 2, 3, 92.36, 50.8, 0.216, 101, 44.82, 6.58, 0.784, 2, 3, 59.08, 59.73, 0.152, 101, 11.28, 14.5, 0.848, 2, 3, 26.88, 53.59, 0.728, 101, -20.72, 7.39, 0.272, 2, 3, -1.33, 38.3, 0.656, 101, -48.46, -8.74, 0.344, 2, 3, -15.6, 35.58, 0.568, 101, -62.63, -11.89, 0.432, 2, 3, -18.57, 26.51, 0.736, 101, -65.33, -21.05, 0.264, 1, 3, 30.81, -8.88, 1, 1, 3, 33.21, -12.02, 1, 1, 3, 34.11, -15.73, 1, 1, 3, 34.4, -19.75, 1, 1, 3, 33.13, -23.49, 1, 1, 3, 30.82, -26.3, 1, 1, 3, 27.36, -24.02, 1, 1, 3, 25.27, -21.01, 1, 1, 3, 24.99, -17.09, 1, 1, 3, 25.9, -13.89, 1, 1, 3, 28.24, -10.79, 1, 1, 3, 31.28, 9.01, 1, 1, 3, 33.65, 12.49, 1, 1, 3, 35.39, 16.23, 1, 1, 3, 37.01, 19.66, 1, 1, 3, 37.41, 23.05, 1, 1, 3, 37.63, 25.84, 1, 1, 3, 33.46, 24.86, 1, 1, 3, 30.76, 22.16, 1, 1, 3, 29.69, 18.36, 1, 1, 3, 29.41, 14.33, 1, 1, 3, 30.02, 11.14, 1, 1, 3, 32.24, 10.42, 1, 1, 3, 31.99, 6.78, 1, 1, 3, 34.88, 9.51, 1, 1, 3, 39.04, 13.99, 1, 1, 3, 42.14, 17.61, 1, 1, 3, 43.68, 22.92, 1, 1, 3, 42.33, 28.54, 1, 1, 3, 38.24, 32.11, 1, 1, 3, 30.6, 30.46, 1, 1, 3, 23.38, 25.59, 1, 1, 3, 21.11, 16.16, 1, 1, 3, 25.6, 8.49, 1, 1, 3, 29.32, 6.36, 1, 1, 3, 28.17, -3.99, 1, 1, 3, 32.47, -5.16, 1, 1, 3, 36.58, -7.87, 1, 1, 3, 40.32, -14.55, 1, 1, 3, 42.59, -21.5, 1, 1, 3, 41.25, -26.87, 1, 1, 3, 33.66, -31.25, 1, 1, 3, 26.56, -29.29, 1, 1, 3, 18.93, -23.55, 1, 1, 3, 17.44, -12.76, 1, 1, 3, 22.39, -5.94, 1, 1, 3, 7.3, 15.13, 1, 1, 3, 3.81, 12.57, 1, 1, 3, 1.98, 7.77, 1, 1, 3, 1.7, 3.27, 1, 1, 3, 0.45, -0.09, 1, 1, 3, 0.79, -4.38, 1, 1, 3, 2.52, -7.46, 1, 1, 3, 4.19, 24.33, 1, 1, 3, -4.38, 18.25, 1, 1, 3, -11.62, 11.49, 1, 1, 3, -13.5, 6.78, 1, 1, 3, -13.28, 0.7, 1, 1, 3, -9.06, -7.23, 1, 1, 3, -1.19, -16.04, 1, 1, 3, 1.69, -19.41, 1, 1, 3, 63.93, 29.42, 1, 1, 3, 60.05, 18.04, 1, 1, 3, 55.21, -15.25, 1, 1, 3, 57.18, -28.53, 1, 1, 3, 57.65, -33.93, 1, 1, 3, 58.52, 30.39, 1, 1, 3, 53.18, 27.92, 1, 1, 3, 49.17, 32.52, 1, 1, 3, 37.87, 34.95, 1, 1, 3, 41.59, 39.46, 1, 1, 3, 40.53, 43.28, 1, 1, 3, 36.12, 45.9, 1, 1, 3, 29.82, 44.92, 1, 1, 3, 21.32, 40.28, 1, 1, 3, 17.74, 35.02, 1, 1, 3, 16.23, 26.57, 1, 1, 3, 10.54, 23.59, 1, 1, 3, 52.8, -30.9, 1, 1, 3, 44.51, -33.42, 1, 1, 3, 34.5, -34.75, 1, 1, 3, 35.73, -40.05, 1, 1, 3, 33.61, -43.17, 1, 1, 3, 28.44, -43.39, 1, 1, 3, 20.17, -38.14, 1, 1, 3, 16.9, -33.55, 1, 1, 3, 15.13, -25.42, 1, 1, 3, 8.64, -19.28, 1], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 58, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 80, 80, 102, 102, 82, 100, 102, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 104, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 128, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 180, 182, 182, 184, 184, 186, 186, 188, 180, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 164, 188, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 232, 178], "width": 169, "height": 176}}, "Monkeyking14": {"Monkeyking14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 80, -13.77, -22.97, 1, 1, 80, -15.64, -13.15, 1, 1, 80, -6.8, -11.46, 1, 1, 80, -4.93, -21.28, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 10, "height": 9}}, "Monkeyking15": {"Monkeyking15": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 80, -9.77, 10.48, 1, 1, 80, -11.45, 19.32, 1, 1, 80, -3.6, 20.82, 1, 1, 80, -1.91, 11.98, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 9, "height": 8}}, "Monkeyking16": {"Monkeyking16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, 18.55, -33.7, 1, 1, 3, 5.43, 35.06, 1, 1, 3, 40.79, 41.8, 1, 1, 3, 53.91, -26.96, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 70, "height": 36}}, "Monkeyking17": {"Monkeyking17": {"type": "mesh", "uvs": [0.53185, 0, 1, 0, 1, 0.06662, 0.77295, 0.33773, 0.55377, 0.56452, 0.56473, 0.79652, 0.71816, 1, 0, 1, 0, 0.76264, 0, 0.52542, 0.1154, 0.12918], "triangles": [9, 10, 4, 8, 9, 4, 8, 4, 5, 7, 8, 5, 7, 5, 6, 0, 1, 2, 3, 0, 2, 4, 0, 3, 10, 0, 4], "vertices": [1, 78, -31.73, -9.45, 1, 1, 78, -37.23, 12.82, 1, 1, 78, -23.9, 16.11, 1, 2, 78, 32.99, 18.68, 0.99875, 79, -11.04, 19.44, 0.00125, 2, 78, 80.92, 19.43, 0.00333, 79, 36.87, 17.63, 0.99667, 1, 79, 83.71, 27.1, 1, 1, 79, 123.48, 42.33, 1, 1, 79, 130.07, 7.76, 1, 1, 79, 82.03, -1.39, 1, 1, 79, 34.03, -10.53, 1, 2, 78, -1.01, -22.89, 0.99998, 79, -47.21, -20.25, 2e-05], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "width": 49, "height": 206}}, "Monkeyking18": {"Monkeyking18": {"type": "mesh", "uvs": [0.12591, 0.00428, 0, 0.14032, 0, 0.21569, 0.11314, 0.41975, 0.177, 0.56315, 0.18977, 0.62198, 0.28556, 0.94737, 0.48672, 1, 0.92415, 1, 1, 0.94737, 1, 0.84074, 0.86349, 0.59624, 0.83156, 0.52454, 0.65275, 0], "triangles": [7, 6, 5, 12, 7, 5, 11, 7, 12, 10, 7, 11, 10, 8, 7, 9, 8, 10, 2, 1, 0, 3, 2, 0, 13, 3, 0, 4, 13, 12, 4, 3, 13, 5, 4, 12], "vertices": [1, 67, -44.94, -20.37, 1, 1, 67, -9.93, -49.88, 1, 1, 67, 11.96, -54.56, 1, 1, 67, 75.27, -48.32, 1, 2, 67, 119.2, -46.56, 0.86571, 68, -24.14, -44.06, 0.13429, 2, 67, 136.74, -48.08, 0.59872, 68, -6.89, -47.6, 0.40128, 1, 68, 89.89, -63.12, 1, 1, 68, 115.74, -35.56, 1, 1, 68, 139.76, 35.28, 1, 1, 68, 129.12, 52.58, 1, 2, 67, 229.27, 73.81, 0.00413, 68, 99.13, 62.75, 0.99587, 2, 67, 153.37, 66.18, 0.38467, 68, 22.86, 63.96, 0.61533, 2, 67, 131.41, 65.3, 0.66438, 68, 0.94, 65.63, 0.33562, 1, 67, -27.33, 67.99, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 171, "height": 297}}, "Monkeyking19": {"Monkeyking19": {"type": "mesh", "uvs": [0.227, 0.00304, 0.09992, 0.12645, 0.00578, 0.47985, 0, 0.56587, 0.04343, 0.67245, 0, 0.95854, 0.1564, 1, 0.69299, 1, 1, 0.92301, 1, 0.77716, 0.93304, 0.6631, 0.92833, 0.61074, 0.93774, 0.54156, 1, 0.17507, 0.7024, 0], "triangles": [7, 10, 9, 4, 7, 6, 5, 4, 6, 11, 7, 4, 7, 11, 10, 8, 7, 9, 12, 14, 13, 12, 0, 14, 0, 4, 2, 3, 2, 4, 1, 0, 2, 12, 4, 0, 12, 11, 4], "vertices": [1, 69, -23.81, -45.74, 1, 1, 69, 13.3, -57.5, 1, 2, 69, 117.04, -60.01, 0.88689, 70, -36.26, -53.71, 0.11311, 2, 69, 142.13, -58.64, 0.60268, 70, -11.45, -57.68, 0.39732, 2, 69, 172.74, -51.09, 0.15008, 70, 20.06, -56.77, 0.84992, 1, 70, 102.22, -72.74, 1, 1, 70, 116.6, -56.35, 1, 1, 70, 124.78, 5.36, 1, 1, 70, 107.17, 43.62, 1, 2, 69, 194.21, 61.99, 0.0289, 70, 64.95, 49.21, 0.9711, 2, 69, 161.64, 51.54, 0.26298, 70, 30.92, 45.89, 0.73702, 2, 69, 146.45, 49.75, 0.52128, 70, 15.69, 47.36, 0.47872, 2, 69, 126.22, 49.2, 0.84582, 70, -4.2, 51.09, 0.15418, 1, 69, 18.97, 47.71, 1, 1, 69, -29.17, 9.16, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28], "width": 116, "height": 292}}, "Monkeyking2": {"Monkeyking2": {"type": "mesh", "uvs": [0.00764, 0.20783, 0, 0.01971, 0.39663, 0.00231, 0.51479, 0.09181, 0.66121, 0.28823, 0.75882, 0.49459, 0.89418, 0.70167, 0.99771, 0.86006, 1, 1, 0.78964, 1, 0.43773, 0.92222, 0.19627, 0.76061, 0.01646, 0.42497], "triangles": [11, 4, 5, 10, 11, 5, 10, 5, 6, 9, 10, 6, 9, 6, 7, 9, 7, 8, 0, 1, 2, 3, 12, 0, 12, 4, 11, 2, 3, 0, 4, 12, 3], "vertices": [1, 76, -17.56, -66.33, 1, 1, 76, -54.06, -47.57, 1, 2, 76, -16.22, 27.17, 0.97633, 77, -71.35, 38.15, 0.02367, 2, 76, 13.03, 39.28, 0.81249, 77, -40.61, 45.71, 0.18751, 2, 76, 65.51, 45.13, 0.05186, 77, 12.15, 43.56, 0.94814, 1, 77, 60.25, 31.98, 1, 1, 77, 113.39, 26.56, 1, 1, 77, 154.04, 22.41, 1, 1, 77, 178.33, 3.95, 1, 2, 76, 213.95, -7.57, 0.00178, 77, 150.92, -30.96, 0.99822, 2, 76, 162.67, -63.89, 0.16082, 77, 91.72, -78.88, 0.83918, 2, 76, 106.94, -90.93, 0.56058, 77, 32.55, -97.19, 0.43942, 2, 76, 24.57, -87.99, 0.99319, 77, -48.43, -81.84, 0.00681], "hull": 13, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 10, 12, 12, 14, 2, 0, 0, 24], "width": 211, "height": 218}}, "Monkeyking20": {"Monkeyking20": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 68, 215.72, 10.57, 1, 1, 68, 165.61, -137.21, 1, 1, 68, 39.27, -94.37, 1, 1, 68, 89.38, 53.4, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 151, "height": 118}}, "Monkeyking21": {"Monkeyking21": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 6, -20.38, -1.48, 1, 1, 6, 28.46, 51.43, 1, 1, 6, 67.4, 15.48, 1, 1, 6, 18.57, -37.43, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 72, "height": 53}}, "Monkeyking22": {"Monkeyking22": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 7, 583.44, -19.55, 1, 1, 7, -307.54, -14.57, 1, 1, 7, -307.36, 17.43, 1, 1, 7, 583.62, 12.45, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 891, "height": 32}}, "Monkeyking23": {"Monkeyking23": {"type": "mesh", "uvs": [0.10244, 0.02367, 0.23926, 0.00418, 0.25481, 0.03566, 0.25481, 0.14808, 0.31078, 0.259, 0.42584, 0.37442, 0.59376, 0.49134, 0.77412, 0.60376, 0.90784, 0.73866, 1, 0.88406, 1, 1, 0.70882, 0.95601, 0.36987, 0.92603, 0.10555, 0.87957, 0.20506, 0.79712, 0.26414, 0.6952, 0.24548, 0.60526, 0.13664, 0.50933, 0.03402, 0.39091, 0, 0.27849, 0, 0.16907, 0.04646, 0.06864], "triangles": [11, 9, 10, 11, 8, 9, 11, 7, 8, 7, 11, 6, 13, 14, 12, 14, 15, 12, 11, 12, 6, 12, 15, 6, 6, 16, 5, 6, 15, 16, 16, 17, 5, 4, 17, 18, 17, 4, 5, 4, 18, 3, 3, 18, 19, 3, 19, 20, 20, 21, 3, 3, 21, 0, 3, 0, 2, 0, 1, 2], "vertices": [1, 62, -25.13, -10.88, 1, 1, 62, -43.98, 15.63, 1, 1, 62, -31.06, 23.82, 1, 3, 62, 19.23, 41.1, 0.92601, 63, -3.38, 46.93, 0.03939, 64, -35.96, 68.53, 0.0346, 3, 62, 64.7, 70.23, 0.20888, 63, 50.09, 54.46, 0.1844, 64, 15.69, 52.78, 0.60673, 3, 62, 107.8, 112.78, 0.00485, 64, 76.03, 47.53, 0.956, 65, -5.31, 48.77, 0.03915, 3, 64, 143.12, 52.28, 0.09426, 65, 61.81, 44.47, 0.84646, 66, -33.85, 38.75, 0.05928, 2, 65, 129.03, 43.7, 0.04897, 66, 32.29, 50.75, 0.95103, 1, 66, 102.97, 48.41, 1, 1, 66, 173.69, 35.37, 1, 1, 66, 222.36, 10.11, 1, 1, 66, 173.31, -39.23, 1, 1, 66, 125.12, -101.29, 1, 2, 65, 136.69, -156.79, 0.00055, 66, 77.85, -144.65, 0.99945, 2, 65, 120.2, -114.8, 0.03359, 66, 53.69, -106.55, 0.96641, 2, 65, 90.76, -74.31, 0.27908, 66, 17.1, -72.38, 0.72092, 2, 65, 54.77, -51.24, 0.81161, 66, -22.61, -56.56, 0.18839, 3, 64, 97.33, -41.73, 0.2761, 65, 3.79, -42.53, 0.72386, 66, -74.32, -57.67, 3e-05, 1, 64, 37.21, -33.31, 1, 2, 63, 52.28, -16.96, 0.83785, 64, -12.49, -12.89, 0.16215, 1, 63, 0.77, -11.86, 1, 1, 62, -0.86, -16.03, 1], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42], "width": 228, "height": 473}}, "Monkeyking24": {"Monkeyking24": {"type": "mesh", "uvs": [0.80274, 0.04487, 0.68591, 0.83694, 0.26673, 1, 0.17739, 1, 0.08806, 0.88423, 0, 0.6872, 0, 0.4941, 0.0599, 0.36931, 0.02193, 0.11699, 0.04912, 0, 0.18198, 0, 0.24153, 0.06457, 0.2603, 0.16677, 0.28966, 0.25011, 0.29961, 0.27494, 0.31931, 0.26839, 0.45594, 0.19514, 0.59574, 0.06395, 0.08023, 0.49935, 0.13004, 0.60891, 0.20129, 0.65881, 0.28011, 0.62192, 0.32677, 0.53731, 0.3274, 0.39413], "triangles": [12, 8, 11, 10, 8, 9, 10, 11, 8, 7, 12, 13, 7, 13, 14, 7, 8, 12, 14, 15, 23, 7, 14, 23, 18, 7, 23, 6, 7, 18, 22, 18, 23, 19, 18, 22, 21, 19, 22, 20, 19, 21, 19, 5, 6, 19, 6, 18, 22, 23, 16, 21, 22, 1, 20, 4, 5, 20, 5, 19, 2, 3, 4, 2, 20, 21, 2, 21, 1, 2, 4, 20, 23, 15, 16, 0, 16, 17, 1, 16, 0, 1, 22, 16], "vertices": [1, 4, -27.93, -17.03, 1, 2, 4, 33.18, 57.56, 0.9993, 5, -64.7, -42.91, 0.0007, 2, 4, 118.41, 38.9, 0.09755, 5, -33.18, 38.44, 0.90245, 2, 4, 134.83, 31.21, 0.01774, 5, -23.06, 53.49, 0.98226, 1, 5, -1.61, 60.92, 1, 1, 5, 27.66, 62.78, 1, 1, 5, 46.57, 50.07, 1, 1, 5, 52, 31.76, 1, 1, 5, 81.01, 21.54, 1, 1, 5, 89.39, 9.26, 1, 1, 5, 74.34, -13.12, 1, 1, 5, 61.27, -18.9, 1, 2, 4, 77.9, -50.7, 0.00484, 5, 49.14, -15.34, 0.99516, 2, 4, 76.67, -39.26, 0.07194, 5, 37.65, -14.79, 0.92806, 2, 4, 76.08, -35.75, 0.15484, 5, 34.09, -14.84, 0.84516, 2, 4, 72.13, -34.76, 0.29795, 5, 32.5, -18.59, 0.70205, 2, 4, 43.35, -30.83, 0.92074, 5, 24.2, -46.42, 0.07926, 1, 4, 11.08, -32.81, 1, 1, 5, 36.97, 36.9, 1, 1, 5, 20.6, 35.72, 1, 1, 5, 7.64, 27, 1, 2, 4, 97.03, -0.35, 0.00019, 5, 2.32, 11.3, 0.99981, 2, 4, 84.22, -5.38, 0.13065, 5, 5.32, -2.13, 0.86935, 2, 4, 76.94, -20.62, 0.29866, 5, 19.27, -11.67, 0.70134], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 14, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 28], "width": 203, "height": 118}}, "Monkeyking25": {"Monkeyking25": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 4, 16.64, 40.3, 1, 1, 4, 89.1, 6.38, 1, 1, 4, 50.51, -76.04, 1, 1, 4, -21.94, -42.11, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 91}}, "Monkeyking26": {"Monkeyking26": {"type": "mesh", "uvs": [0, 0.19212, 0.07546, 0.19523, 0.21519, 0.16417, 0.3108, 0.13777, 0.3819, 0.11758, 0.46034, 0.09739, 0.53389, 0.0772, 0.5919, 0.06385, 0.63931, 0.05651, 0.69198, 0.05317, 0.74466, 0.05183, 0.8026, 0.05651, 0.84369, 0.06518, 0.88267, 0.0792, 0.90585, 0.09588, 0.92165, 0.11523, 0.9385, 0.15061, 0.94272, 0.1813, 0.93745, 0.21963, 0.91954, 0.27569, 0.88899, 0.3411, 0.83631, 0.41517, 0.7868, 0.47524, 0.73202, 0.54064, 0.67091, 0.61872, 0.63088, 0.68079, 0.58347, 0.76755, 0.54871, 0.86165, 0.53606, 0.94106, 0.55292, 1, 0.57294, 0.99779, 0.59401, 0.93573, 0.61402, 0.85564, 0.65827, 0.77088, 0.70989, 0.69614, 0.76046, 0.62673, 0.81208, 0.55466, 0.86054, 0.48925, 0.91041, 0.42124, 0.95044, 0.35049, 0.98626, 0.28242, 1, 0.22636, 1, 0.17698, 0.99575, 0.1376, 0.981, 0.1009, 0.96835, 0.0742, 0.94307, 0.04551, 0.90093, 0.02148, 0.85036, 0.00813, 0.80611, 0, 0.74501, 0, 0.67759, 0.00012, 0.60068, 0.01013, 0.54168, 0.02348, 0.45003, 0.04284, 0.35205, 0.06286, 0.25723, 0.08621, 0.16453, 0.10557, 0.04548, 0.14227, 0, 0.16897], "triangles": [29, 28, 30, 30, 28, 31, 28, 27, 31, 31, 27, 32, 27, 26, 32, 32, 26, 33, 26, 25, 33, 33, 25, 34, 25, 24, 34, 34, 24, 35, 24, 23, 35, 35, 23, 36, 23, 22, 36, 36, 22, 37, 22, 21, 37, 37, 21, 38, 21, 20, 38, 38, 20, 39, 20, 19, 39, 39, 19, 40, 19, 18, 40, 40, 18, 41, 18, 17, 41, 17, 42, 41, 17, 16, 42, 16, 43, 42, 16, 44, 43, 16, 15, 44, 15, 45, 44, 15, 14, 45, 14, 46, 45, 14, 13, 46, 13, 47, 46, 13, 12, 47, 12, 48, 47, 12, 11, 48, 11, 49, 48, 11, 10, 49, 49, 10, 50, 8, 51, 9, 10, 9, 50, 50, 9, 51, 7, 52, 8, 7, 53, 52, 8, 52, 51, 5, 54, 6, 6, 53, 7, 6, 54, 53, 5, 4, 54, 4, 55, 54, 2, 56, 3, 3, 55, 4, 3, 56, 55, 1, 57, 2, 2, 57, 56, 0, 58, 1, 1, 58, 57, 0, 59, 58], "vertices": [2, 12, -19.8, 2.36, 1, 26, 50.31, -282.2, 0, 1, 12, -1.04, -10.25, 1, 3, 12, 42.45, -18.59, 0.02994, 13, 14.74, -17.09, 0.93781, 14, -16.6, -17.86, 0.03225, 3, 13, 45.92, -17.31, 0.04458, 14, 14.56, -16.67, 0.94959, 15, -19.62, -17.08, 0.00583, 2, 14, 37.83, -15.55, 0.34815, 15, 3.62, -15.47, 0.65185, 2, 15, 28.93, -14.62, 0.73813, 16, -4.31, -14.79, 0.26187, 2, 16, 19.55, -12.58, 0.95634, 17, -9.33, -13.36, 0.04366, 2, 16, 38, -11.99, 0.09112, 17, 9.01, -11.32, 0.90888, 3, 17, 23.6, -11.28, 0.79784, 18, -4.43, -12.05, 0.20216, 26, -67.65, -118.73, 0, 4, 17, 39.28, -13.45, 0.02473, 18, 11.4, -11.93, 0.90163, 19, -11.22, -14.46, 0.07364, 26, -73.84, -104.16, 0, 2, 18, 27.14, -12.76, 0.33199, 19, 4.37, -12.13, 0.66801, 3, 19, 21.83, -12.45, 0.38528, 20, 3.45, -11.96, 0.61376, 21, -11.76, -16.02, 0.00095, 4, 19, 34.49, -15.19, 0.00924, 20, 16.39, -11.38, 0.59494, 21, 0.34, -11.41, 0.39395, 22, -13.68, -18.01, 0.00187, 3, 20, 29.64, -13.37, 0.03759, 21, 13.55, -9.15, 0.76608, 22, -2.34, -10.87, 0.19633, 4, 20, 38.93, -18.23, 0.00017, 21, 23.9, -10.85, 0.04065, 22, 7.87, -8.49, 0.94883, 23, -11.72, -11.19, 0.01035, 2, 22, 18.16, -8.64, 0.68991, 23, -1.63, -9.19, 0.31009, 3, 23, 15.8, -8.98, 0.88889, 24, -3.83, -10.46, 0.11111, 26, -51.85, -20.11, 0, 3, 23, 30.07, -11.79, 0.04275, 24, 10.6, -8.64, 0.95724, 26, -38.39, -14.59, 1e-05, 2, 24, 28.74, -9.52, 0.20631, 25, 6, -8.65, 0.79369, 2, 25, 33, -8.53, 0.10318, 26, 6.2, -7.96, 0.89682, 1, 27, 8.98, -7.07, 1, 2, 27, 47.23, -9.84, 0.00239, 28, 12.84, -8.7, 0.99761, 1, 29, 4.72, -9.85, 1, 2, 29, 39.65, -10.89, 0.31692, 30, 1.85, -10.74, 0.68308, 2, 30, 42.97, -11.95, 0.22363, 31, 4.41, -12.19, 0.77637, 2, 31, 36.06, -12.42, 0.88918, 32, -6.86, -12.29, 0.11082, 2, 32, 36.46, -11.12, 0.67543, 33, -2.73, -10.94, 0.32457, 2, 33, 42.82, -8.51, 0.19958, 34, 3.52, -9.43, 0.80042, 2, 34, 41.12, -7, 0.0426, 35, 6.54, -7.53, 0.9574, 1, 35, 33.81, -0.09, 1, 1, 35, 32.25, 5.79, 1, 2, 34, 35.78, 9.67, 0.33961, 35, 2.52, 9.51, 0.66039, 2, 33, 34.64, 9.45, 0.69384, 34, -2.48, 9.36, 0.30616, 2, 32, 30.05, 10.37, 0.90074, 33, -7.47, 10.97, 0.09926, 2, 31, 34.01, 12.2, 0.885, 32, -8.4, 12.38, 0.115, 2, 30, 35.22, 13.96, 0.64579, 31, -2.02, 14.08, 0.35421, 2, 29, 35, 13.5, 0.63627, 30, -2.13, 13.78, 0.36373, 2, 28, 41.34, 12.81, 0.506, 29, 0.91, 12.85, 0.494, 2, 27, 42.3, 11.95, 0.16462, 28, 5.94, 12.54, 0.83538, 2, 26, 37.16, 11.36, 0.14771, 27, 6.83, 11.71, 0.85229, 2, 25, 32.06, 11.65, 0.26352, 26, 3.3, 12.03, 0.73648, 2, 24, 31.2, 9.29, 0.09711, 25, 5.32, 10.31, 0.90289, 2, 23, 32.87, 5.23, 0.00692, 24, 7.9, 8.4, 0.99308, 2, 23, 14.66, 9.16, 0.99997, 25, -35.45, 0.58, 3e-05, 3, 22, 20.58, 10.19, 0.6791, 23, -3.21, 9.74, 0.3209, 25, -51.52, -7.25, 0, 2, 21, 31.88, 8.9, 0.05912, 22, 7.69, 12.82, 0.94088, 3, 20, 40.79, 7.96, 0.00298, 21, 17.45, 14.6, 0.7954, 22, -7.82, 12.56, 0.20162, 2, 20, 24.96, 14.02, 0.39208, 21, 0.52, 15.39, 0.60792, 4, 19, 33.56, 11.79, 0.10071, 20, 8.58, 14.47, 0.86867, 21, -15.17, 10.7, 0.03062, 26, -108.22, -65.27, 0, 3, 19, 20, 14.18, 0.65298, 20, -5.14, 13.31, 0.34702, 26, -107.95, -79.04, 0, 2, 18, 29.5, 11.6, 0.29259, 19, 1.83, 12.21, 0.70741, 2, 17, 40.98, 11.89, 0.06032, 18, 9.42, 13.4, 0.93968, 2, 17, 17.52, 12.71, 0.98062, 18, -13.91, 10.81, 0.01938, 3, 16, 29.67, 10.79, 0.54305, 17, -1.1, 10.73, 0.45695, 26, -73.86, -151.23, 0, 3, 15, 34.92, 10.61, 0.42461, 16, 0.78, 10.64, 0.57539, 26, -56.98, -174.68, 0, 2, 14, 38.93, 11.76, 0.2491, 15, 4.16, 11.85, 0.7509, 2, 13, 41.17, 11.43, 0.05835, 14, 8.52, 11.82, 0.94165, 3, 12, 43.26, 12.94, 0.03037, 13, 12.13, 14.34, 0.96963, 26, -3.33, -247.38, 0, 2, 12, 3.79, 15.86, 1, 26, 23.8, -276.21, 0, 2, 12, -14.3, 11.81, 1, 26, 39.87, -285.45, 0], "hull": 60, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 0, 118], "width": 299, "height": 472}}, "Monkeyking27": {"Monkeyking27": {"type": "mesh", "uvs": [0.88385, 0.40434, 0.95129, 0.40703, 0.97687, 0.34784, 0.97687, 0.26308, 0.96292, 0.17295, 0.93268, 0.08282, 0.90478, 0.03035, 0.85594, 0.00479, 0.7978, 0, 0.74896, 0.0021, 0.69547, 0.01286, 0.63966, 0.04246, 0.59082, 0.08551, 0.54199, 0.16219, 0.49315, 0.24156, 0.44431, 0.32766, 0.41175, 0.39761, 0.36524, 0.47833, 0.32106, 0.56174, 0.24664, 0.64111, 0.15361, 0.75008, 0.07222, 0.87116, 0, 0.99896, 0.03268, 1, 0.13501, 0.87923, 0.23268, 0.76084, 0.33036, 0.65187, 0.40478, 0.56847, 0.46292, 0.48237, 0.51175, 0.40299, 0.54896, 0.33169, 0.58152, 0.25905, 0.62803, 0.18909, 0.64664, 0.15815, 0.67222, 0.12721, 0.70478, 0.09627, 0.73966, 0.0734, 0.77062, 0.06108, 0.80124, 0.06391, 0.82329, 0.07879, 0.83309, 0.09934, 0.84289, 0.17161, 0.83309, 0.26017, 0.84901, 0.34448], "triangles": [23, 22, 21, 24, 21, 20, 23, 21, 24, 25, 20, 19, 24, 20, 25, 19, 18, 26, 25, 19, 26, 18, 17, 27, 26, 18, 27, 17, 16, 28, 27, 17, 28, 16, 15, 29, 28, 16, 29, 30, 14, 31, 15, 14, 30, 29, 15, 30, 31, 13, 32, 14, 13, 31, 12, 11, 34, 33, 12, 34, 13, 12, 33, 32, 13, 33, 11, 10, 35, 34, 11, 35, 36, 10, 9, 35, 10, 36, 37, 9, 8, 36, 9, 37, 38, 8, 7, 37, 8, 38, 39, 38, 7, 39, 7, 6, 40, 39, 6, 5, 40, 6, 41, 40, 5, 41, 5, 4, 4, 42, 41, 42, 4, 3, 43, 42, 3, 43, 3, 2, 0, 43, 2, 1, 0, 2], "vertices": [1, 36, -22.43, 11.59, 1, 1, 36, -24.33, -5.75, 1, 1, 36, 1.79, -13.4, 1, 3, 36, 39.56, -14.91, 0.05628, 37, 11.22, -14.94, 0.93821, 38, -15.94, -14.54, 0.00551, 2, 38, 24.41, -13.66, 0.75367, 39, -4.84, -12.94, 0.24633, 3, 39, 36.05, -10.81, 0.00849, 40, 10.82, -11.68, 0.95286, 41, -12, -8.02, 0.03865, 3, 40, 35.22, -9.67, 0.00478, 41, 11.59, -14.59, 0.89158, 42, -13.26, -8.45, 0.10364, 2, 41, 27.98, -10.13, 0.14356, 42, 2.67, -14.36, 0.85644, 2, 42, 17.39, -10.77, 0.51851, 43, -2.73, -10.89, 0.48149, 3, 42, 28.74, -5.22, 0.01745, 43, 9.86, -11.88, 0.86535, 44, -10.53, -7.45, 0.1172, 3, 43, 24.23, -9.24, 0.20821, 44, 3.07, -12.78, 0.78464, 45, -17.95, -6.78, 0.00715, 2, 44, 22.59, -12.13, 0.34276, 45, 0.5, -13.2, 0.65724, 2, 45, 23.38, -15.19, 0.49502, 46, -0.29, -15.23, 0.50498, 2, 46, 36.12, -13.44, 0.50304, 47, -0.4, -13.44, 0.49696, 2, 47, 37.18, -13.62, 0.64462, 48, -2.52, -13.65, 0.35538, 2, 48, 37.88, -14.58, 0.78129, 49, -5.72, -14.82, 0.21871, 2, 49, 26.57, -13.7, 0.98492, 50, -8.59, -14.76, 0.01508, 3, 49, 64.51, -14.61, 0.00013, 50, 29.32, -13.09, 0.91032, 51, -8.55, -13.14, 0.08955, 2, 51, 30.25, -10.26, 0.57383, 52, -2.54, -10.28, 0.42617, 2, 52, 37.7, -11.83, 0.72036, 53, 3.69, -12.2, 0.27964, 2, 54, 26.07, -11.97, 0.87002, 55, -7, -11.54, 0.12998, 1, 56, 18.75, -5.88, 1, 1, 57, 45.87, 0.18, 1, 1, 57, 42.99, 8.12, 1, 1, 56, 14.47, 10.16, 1, 1, 54, 20.92, 8.34, 1, 2, 52, 32.45, 9.66, 0.55719, 53, -1.34, 9.34, 0.44281, 1, 51, 25.21, 10.96, 1, 1, 50, 21.99, 11.09, 1, 1, 49, 21.3, 11.67, 1, 1, 48, 31.78, 11.78, 1, 2, 47, 37, 10.48, 0.61849, 48, -1.66, 10.43, 0.38151, 2, 46, 38.47, 11.69, 0.29035, 47, 3.58, 11.48, 0.70965, 2, 46, 23.89, 10.71, 0.98526, 47, -11.03, 11.45, 0.01474, 2, 45, 29.8, 12.12, 0.11182, 46, 8.61, 11.38, 0.88818, 5, 42, 23.73, 38.01, 0.0007, 43, 27.51, 27.9, 0.00381, 44, 25.46, 17.02, 0.04787, 45, 13.67, 12.97, 0.87258, 46, -7.37, 13.71, 0.07503, 5, 41, 18.41, 31.64, 0.00464, 42, 19.16, 25.2, 0.04147, 43, 17.07, 19.18, 0.12184, 44, 11.99, 15.14, 0.44217, 45, 0.42, 16.06, 0.38988, 6, 40, 29.27, 27.07, 0.00736, 41, 18.73, 21.95, 0.05997, 42, 13.79, 17.13, 0.22827, 43, 8.34, 14.97, 0.36459, 44, 2.34, 16.16, 0.27278, 45, -8.21, 20.49, 0.06703, 6, 40, 26.34, 19.63, 0.07765, 41, 13.4, 15.98, 0.28868, 42, 5.98, 15.37, 0.4025, 43, 0.72, 17.42, 0.17111, 44, -2.83, 22.26, 0.05322, 45, -10.84, 28.05, 0.00684, 7, 39, 41.76, 16.89, 0.00431, 40, 18.64, 15.5, 0.40381, 41, 4.74, 14.77, 0.44505, 42, -1.76, 19.41, 0.11575, 43, -3.89, 24.84, 0.02432, 44, -2.83, 31, 0.00661, 45, -7.69, 36.2, 0.00015, 5, 39, 32.33, 15.66, 0.13099, 40, 9.14, 15, 0.74723, 41, -4.34, 17.59, 0.10889, 42, -7.52, 26.98, 0.01224, 43, -5, 34.28, 0.00066, 4, 37, 53.46, 17.85, 0.00362, 38, 27.1, 17.2, 0.56466, 39, 0.06, 17.64, 0.43016, 40, -22.88, 19.46, 0.00156, 3, 36, 42.34, 22.1, 0.07483, 37, 14.1, 22.06, 0.85559, 38, -12.14, 22.39, 0.06957, 2, 36, 4.6, 19.5, 0.98765, 37, -23.64, 19.57, 0.01235], "hull": 44, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 0, 86], "width": 258, "height": 446}}, "Monkeyking3": {"Monkeyking3": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 1e-05, 0.99999, 0.02907, 0.86064, 0.07268, 0.73351, 0.06783, 0.58366, 0.11629, 0.43905, 0.14052, 0.2962, 0.2035, 0.1446, 0.24711, 0, 0.5, 0, 0.81588, 0.00175, 0.83526, 0.14111, 0.84979, 0.28047, 0.87402, 0.42508, 0.89825, 0.57143, 0.96608, 0.71429, 0.99999, 0.86413, 0.5, 0.85714, 0.5, 0.71429, 0.5, 0.57143, 0.5, 0.42857, 0.5, 0.28571, 0.5, 0.14286], "triangles": [1, 17, 0, 2, 3, 1, 1, 3, 18, 3, 4, 18, 1, 18, 17, 18, 16, 17, 4, 19, 18, 16, 18, 19, 16, 19, 15, 4, 5, 19, 5, 20, 19, 19, 20, 15, 5, 6, 20, 20, 14, 15, 20, 21, 14, 6, 21, 20, 6, 7, 21, 7, 22, 21, 21, 13, 14, 21, 22, 13, 7, 8, 22, 22, 12, 13, 8, 23, 22, 22, 23, 12, 8, 9, 23, 23, 11, 12, 9, 10, 23, 23, 10, 11], "vertices": [1, 75, 71.58, 55.29, 1, 1, 75, 70.02, 6.82, 1, 1, 75, 68.45, -41.65, 1, 2, 74, 100.16, -36, 0.00873, 75, 31.08, -37.63, 0.99127, 2, 74, 65.89, -32.41, 0.53134, 75, -2.97, -32.3, 0.46866, 3, 73, 85.36, -33.53, 0.00416, 74, 25.59, -33.63, 0.99542, 75, -43.27, -31.47, 0.00042, 2, 73, 46.37, -29.67, 0.74201, 74, -13.38, -29.66, 0.25799, 1, 73, 7.9, -28.15, 1, 3, 71, 23.11, -26.1, 0.1354, 72, 11.52, -25.68, 0.66801, 73, -33, -22.91, 0.19659, 2, 71, -15.95, -23.82, 0.99927, 72, -27.6, -24.89, 0.00073, 1, 71, -17.17, 0.68, 1, 2, 71, -18.23, 31.31, 0.92575, 72, -31.98, 30.11, 0.07425, 2, 71, 19.11, 35.06, 0.3108, 72, 5.2, 35.28, 0.6892, 4, 71, 56.48, 38.34, 0.00291, 72, 42.42, 39.98, 0.93643, 73, 2.19, 40.55, 0.02507, 74, -57.37, 40.68, 0.03559, 4, 72, 80.96, 45.74, 0.29171, 73, 41.03, 43.73, 0.25363, 74, -18.52, 43.76, 0.45362, 75, -83.39, 48.07, 0.00105, 4, 72, 119.97, 51.54, 0.02311, 73, 80.34, 46.93, 0.00336, 74, 20.8, 46.84, 0.87858, 75, -43.96, 49.15, 0.09495, 2, 74, 59.1, 54.14, 0.45399, 75, -5.34, 54.48, 0.54601, 2, 74, 99.34, 58.18, 0.04963, 75, 35.05, 56.47, 0.95037, 2, 74, 98.37, 9.66, 0.00188, 75, 31.61, 8.06, 0.99812, 2, 74, 59.94, 8.94, 0.69261, 75, -6.8, 9.3, 0.30739, 3, 72, 123.37, 13.06, 0.00215, 74, 21.52, 8.22, 0.98932, 75, -45.21, 10.54, 0.00853, 3, 72, 85.09, 9.68, 0.04196, 73, 42.75, 7.48, 0.8669, 74, -16.9, 7.5, 0.09114, 2, 72, 46.81, 6.3, 0.99741, 74, -55.32, 6.78, 0.00259, 2, 71, 21.21, 2.6, 0.02785, 72, 8.53, 2.93, 0.97215], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 0], "width": 97, "height": 269}}, "Monkeyking4": {"Monkeyking4": {"type": "mesh", "uvs": [0.81201, 0.39814, 0.88542, 0.41785, 1, 0.59797, 1, 0.78935, 0.87395, 0.99762, 0.72713, 1, 0.63536, 0.9554, 0.60783, 0.86815, 0.43577, 0.74713, 0.38301, 0.70492, 0.03201, 0.54731, 0, 0.46006, 0.01136, 0.36156, 0.06871, 0.2912, 0.1536, 0.25742, 0.12112, 0.21907, 0.05789, 0.18381, 0, 0.10801, 0, 0, 0.06364, 0, 0.10962, 0.06923, 0.14986, 0.10801, 0.2059, 0.15561, 0.26481, 0.18558, 0.44731, 0.35305, 0.48898, 0.34071, 0.52778, 0.35658, 0.60825, 0.41828, 0.76201, 0.42709], "triangles": [17, 18, 19, 17, 19, 20, 16, 17, 20, 16, 20, 21, 24, 14, 23, 15, 16, 21, 15, 21, 22, 14, 15, 22, 14, 22, 23, 13, 11, 12, 14, 10, 11, 14, 24, 10, 14, 11, 13, 24, 9, 10, 9, 27, 8, 24, 25, 26, 24, 27, 9, 26, 27, 24, 28, 1, 2, 7, 8, 27, 7, 27, 28, 28, 0, 1, 2, 7, 28, 3, 7, 2, 7, 5, 6, 3, 4, 7, 4, 5, 7], "vertices": [2, 9, 10.29, -24.92, 0.99979, 10, -17.28, -35.4, 0.00021, 1, 9, 1.39, -24.09, 1, 1, 9, -14.27, -8.4, 1, 1, 9, -16.53, 10.02, 1, 1, 9, -4.1, 31.9, 1, 2, 9, 13.21, 34.26, 1, 10, -39.38, 19.57, 0, 1, 9, 24.58, 31.29, 1, 2, 9, 28.86, 23.29, 0.99193, 10, -20.58, 16.16, 0.00807, 2, 9, 50.61, 14.13, 0.27057, 10, 3.01, 16.94, 0.72943, 2, 9, 57.34, 10.83, 0.04843, 10, 10.5, 16.76, 0.95157, 1, 10, 54.07, 25.72, 1, 1, 10, 61.76, 20.52, 1, 1, 10, 65.63, 11.68, 1, 2, 10, 63.41, 2.29, 0.96118, 98, 14.18, 16.09, 0.03882, 3, 10, 56.55, -5.81, 0.5188, 98, 7.6, 7.77, 0.48, 99, -8.05, 7.69, 0.0012, 1, 98, 12.88, 6.81, 1, 3, 10, 69.99, -5.89, 0.00031, 98, 21.03, 8.14, 0.10768, 99, 5.39, 8.2, 0.89201, 1, 99, 15.22, 5.99, 1, 1, 99, 21.11, -2.67, 1, 1, 99, 14.85, -6.93, 1, 2, 98, 22.08, -4.52, 0.00548, 99, 6.55, -4.46, 0.99452, 2, 98, 16.01, -4.05, 0.46397, 99, 0.48, -4.04, 0.53603, 1, 98, 7.9, -3.9, 1, 2, 10, 48.95, -18.69, 0.00195, 98, 0.45, -5.37, 0.99805, 3, 9, 53.9, -23.98, 0.05624, 10, 21.94, -16.3, 0.87898, 98, -26.63, -3.9, 0.06478, 3, 9, 49.13, -25.77, 0.13071, 10, 18.35, -19.92, 0.84455, 98, -30.09, -7.65, 0.02473, 3, 9, 44.36, -24.81, 0.21476, 10, 13.61, -21.04, 0.77401, 98, -34.79, -8.93, 0.01123, 3, 9, 34.12, -20.03, 0.63326, 10, 2.32, -20.98, 0.36654, 98, -46.07, -9.26, 0.00019, 2, 9, 15.86, -21.41, 0.9913, 10, -13.69, -29.88, 0.0087], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56], "width": 119, "height": 97}}, "Monkeyking5": {"Monkeyking5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 11, -7.75, 7.14, 1, 1, 11, 20.79, 12.33, 1, 1, 11, 24.01, -5.38, 1, 1, 11, -4.53, -10.57, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 29, "height": 18}}, "Monkeyking6": {"Monkeyking6": {"type": "mesh", "uvs": [0.0045, 0, 0.14314, 0.09569, 0.18175, 0.18575, 0.26072, 0.27908, 0.35022, 0.35768, 0.48535, 0.43955, 0.65032, 0.52797, 0.8223, 0.6475, 0.93813, 0.7523, 1, 0.89475, 0.86618, 0.80469, 0.75737, 0.76048, 0.70823, 0.76376, 0.65032, 0.80961, 0.57837, 0.82107, 0.46254, 0.86364, 0.44324, 0.78013, 0.38357, 0.69171, 0.28705, 0.60657, 0.19403, 0.52633, 0.11681, 0.42809, 0.0396, 0.29055, 0.01327, 0.17429], "triangles": [17, 18, 5, 17, 5, 6, 7, 12, 6, 10, 11, 7, 6, 16, 17, 7, 11, 12, 16, 6, 14, 8, 10, 7, 6, 12, 14, 12, 13, 14, 15, 16, 14, 10, 8, 9, 4, 19, 20, 18, 19, 4, 18, 4, 5, 3, 20, 21, 3, 21, 2, 4, 20, 3, 22, 0, 1, 22, 1, 2, 21, 22, 2], "vertices": [1, 58, -50.65, 8.51, 1, 1, 58, -3.77, 20.75, 1, 2, 58, 53.56, 29.45, 0.79471, 59, -10.25, 31.98, 0.20529, 3, 58, 103.93, 40.09, 0.02714, 59, 41.23, 32.85, 0.87328, 60, -22.87, 39.38, 0.09959, 3, 59, 90.22, 40.97, 0.14418, 60, 26.65, 35.68, 0.8541, 61, -63.09, 41.58, 0.00172, 2, 60, 91.39, 42.48, 0.41172, 61, 1.99, 43.26, 0.58828, 1, 61, 78.6, 49.43, 1, 1, 61, 165.17, 46.11, 1, 1, 61, 229.44, 35.19, 1, 1, 61, 284.96, -1.5, 1, 1, 61, 218.32, 0.04, 1, 1, 61, 171.25, -9.07, 1, 1, 61, 155.68, -21.48, 1, 1, 61, 147.64, -51.09, 1, 1, 61, 126.49, -71.64, 1, 1, 61, 98.35, -113.33, 1, 1, 61, 71.45, -87.93, 1, 2, 60, 128.12, -68.26, 0.07673, 61, 29.9, -70.02, 0.92327, 2, 60, 74.62, -64.14, 0.73081, 61, -23.11, -61.71, 0.26919, 3, 59, 110.04, -53.48, 0.06406, 60, 23.55, -60.78, 0.93428, 61, -73.76, -54.35, 0.00166, 2, 59, 57.3, -52.5, 0.80817, 60, -27.46, -47.35, 0.19183, 2, 58, 68.72, -42.16, 0.58, 59, -8.98, -41.2, 0.42, 1, 58, 20.51, -26.07, 1], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44], "width": 404, "height": 433}}, "Monkeyking7": {"Monkeyking7": {"type": "mesh", "uvs": [0.35181, 0.0004, 0.58941, 0.33281, 0.79281, 0.49969, 1, 0.69778, 1, 0.89452, 0.84861, 1, 0.65241, 1, 0.46161, 0.90944, 0.09981, 0.72763, 0, 0.36809, 0, 0.18763, 0.20421, 0], "triangles": [9, 10, 11, 1, 8, 9, 6, 7, 2, 0, 9, 11, 7, 1, 2, 2, 3, 6, 9, 0, 1, 7, 8, 1, 3, 5, 6, 4, 5, 3], "vertices": [1, 8, -39.62, 52.92, 1, 1, 8, 31.46, 28.55, 1, 1, 8, 76.41, 24.88, 1, 1, 8, 125.99, 17.04, 1, 1, 8, 152.57, -11.7, 1, 1, 8, 150.15, -42.53, 1, 1, 8, 128.54, -62.51, 1, 1, 8, 95.29, -68.71, 1, 1, 8, 30.88, -78.99, 1, 1, 8, -28.69, -36.63, 1, 1, 8, -53.07, -10.26, 1, 1, 8, -55.93, 37.95, 1], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22], "width": 150, "height": 199}}, "Monkeyking8": {"Monkeyking8": {"type": "mesh", "uvs": [0.30649, 0, 0.52386, 0, 0.70171, 0.11971, 0.74123, 0.34771, 0.84003, 0.68671, 1, 0.90871, 0.58314, 1, 0.23403, 1, 0, 0.92971, 0.22086, 0.64171, 0.31308, 0.35371, 0.2538, 0.09571], "triangles": [6, 7, 9, 8, 9, 7, 9, 3, 4, 6, 4, 5, 4, 6, 9, 10, 2, 3, 3, 9, 10, 1, 11, 0, 10, 1, 2, 10, 11, 1], "vertices": [2, 87, -7.09, 0.81, 0.94892, 88, -10.02, -6.04, 0.05108, 1, 87, -0.02, 7.88, 1, 2, 87, 14.31, 5.11, 0.44972, 88, 4.34, 10.41, 0.55028, 3, 87, 31.88, -9.89, 5e-05, 88, 27.4, 9.22, 0.1168, 89, 4.13, 9.91, 0.88315, 2, 89, 38.3, 14.91, 0.00482, 90, 11.66, 14.87, 0.99518, 1, 90, 34.01, 22.44, 1, 1, 90, 43.41, 3.35, 1, 1, 90, 43.56, -12.71, 1, 1, 90, 36.56, -23.54, 1, 2, 89, 34.14, -13.63, 0.05066, 90, 7.38, -13.66, 0.94934, 3, 88, 25.44, -10.39, 0.21095, 89, 4.99, -9.77, 0.78893, 90, -21.75, -9.69, 0.00012, 2, 87, -1.97, -7.74, 0.2371, 88, -0.75, -9.7, 0.7629], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22], "width": 46, "height": 101}}, "Monkeyking9": {"Monkeyking9": {"type": "mesh", "uvs": [0.79375, 0, 1, 0, 1, 0.18104, 0.90485, 0.44839, 0.93515, 0.68901, 0.82405, 1, 0.31905, 1, 0, 0.93854, 0.22815, 0.67119, 0.36955, 0.41572, 0.65235, 0.14836], "triangles": [4, 6, 8, 7, 8, 6, 8, 9, 4, 5, 6, 4, 9, 10, 3, 4, 9, 3, 2, 10, 0, 3, 10, 2, 0, 1, 2], "vertices": [2, 91, 3.96, -4.7, 0.91576, 92, -3.68, -2.93, 0.08424, 1, 91, -1.78, -2.4, 1, 2, 91, 5.07, 14.74, 0.00373, 92, 12.52, 7.88, 0.99627, 3, 92, 39.59, 12.27, 0.04807, 93, 14.85, 10.12, 0.94524, 94, -18.58, 10.32, 0.00669, 2, 93, 39.18, 13.46, 0.02076, 94, 5.81, 13.22, 0.97924, 1, 94, 37.7, 12.47, 1, 1, 94, 38.93, -2.63, 1, 1, 94, 33.46, -12.68, 1, 2, 93, 39.49, -7.82, 0.0339, 94, 5.72, -8.07, 0.9661, 1, 93, 13.13, -6.19, 1, 1, 92, 12.04, -3.06, 1], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "width": 30, "height": 102}}, "WildexpandBorderlight0": {"WildexpandBorderlight0": {"width": 276, "height": 518}, "WildexpandBorderlight1": {"width": 276, "height": 518}, "WildexpandBorderlight2": {"width": 276, "height": 518}, "WildexpandBorderlight3": {"width": 276, "height": 518}, "WildexpandBorderlight4": {"width": 276, "height": 518}, "WildexpandBorderlight5": {"width": 276, "height": 518}, "WildexpandBorderlight6": {"width": 276, "height": 518}, "WildexpandBorderlight7": {"width": 276, "height": 518}}, "WildexpandBorderlight1": {"WildexpandBorderlight0": {"width": 276, "height": 518}, "WildexpandBorderlight1": {"width": 276, "height": 518}, "WildexpandBorderlight2": {"width": 276, "height": 518}, "WildexpandBorderlight3": {"width": 276, "height": 518}, "WildexpandBorderlight4": {"width": 276, "height": 518}, "WildexpandBorderlight5": {"width": 276, "height": 518}, "WildexpandBorderlight6": {"width": 276, "height": 518}, "WildexpandBorderlight7": {"width": 276, "height": 518}}, "batgioifull1": {"batgioifull1": {"type": "mesh", "uvs": [0, 0.68811, 0.02314, 0.81014, 0.06466, 0.81582, 0.16059, 0.76757, 0.25366, 0.71649, 0.31139, 0.65982, 0.34543, 0.59107, 0.36703, 0.52101, 0.37292, 0.49118, 0.38012, 0.54047, 0.36245, 0.60145, 0.32972, 0.65593, 0.28652, 0.74285, 0.25445, 0.8142, 0.28586, 0.94393, 0.31925, 0.93745, 0.38666, 0.84015, 0.42986, 0.74545, 0.46586, 0.65853, 0.51692, 0.94004, 0.61575, 0.93615, 0.64717, 0.79215, 0.68055, 0.67669, 0.69757, 0.67799, 0.71852, 0.82069, 0.75517, 1, 0.87692, 1, 0.96594, 0.94004, 1, 0.83496, 1, 0.5755, 0.9175, 0.27063, 0.7303, 0.22523, 0.55554, 0.03453, 0.46848, 0.20318, 0.40892, 0.16685, 0.39059, 0.06696, 0.36506, 0.01507, 0.33234, 0, 0.24463, 0, 0.19161, 0.05009, 0.17197, 0.13053, 0.15561, 0.19928, 0.14055, 0.24339, 0.11045, 0.2875, 0.06986, 0.33291, 0.02863, 0.41853, 0, 0.54436], "triangles": [3, 2, 0, 2, 1, 0, 4, 3, 46, 5, 4, 46, 46, 3, 0, 6, 5, 46, 46, 45, 6, 6, 45, 7, 7, 45, 44, 7, 44, 43, 7, 43, 42, 8, 42, 41, 8, 41, 40, 27, 26, 24, 26, 25, 24, 14, 13, 15, 27, 24, 28, 21, 20, 19, 15, 13, 16, 18, 21, 19, 13, 12, 16, 16, 12, 17, 28, 23, 29, 28, 24, 23, 21, 18, 22, 12, 11, 17, 17, 11, 18, 18, 11, 10, 29, 23, 30, 30, 23, 31, 23, 22, 31, 31, 22, 8, 10, 9, 18, 31, 8, 33, 18, 8, 22, 8, 7, 42, 8, 40, 39, 37, 34, 39, 39, 38, 37, 18, 9, 8, 34, 8, 39, 8, 34, 33, 35, 37, 36, 34, 37, 35, 33, 32, 31], "vertices": [2, 180, 61.02, -21.25, 0.00814, 181, 42.16, -9.57, 0.99186, 1, 181, 46.87, 4.12, 1, 1, 181, 40.24, 10.42, 1, 2, 179, 37.67, -66.08, 0.00063, 181, 20.56, 19.75, 0.99937, 3, 179, 45.53, -46.34, 0.05766, 180, 33.72, 27.53, 0.11396, 181, 1.17, 28.43, 0.82839, 3, 179, 48.19, -32.42, 0.18613, 180, 21.59, 34.86, 0.27169, 181, -12.63, 31.68, 0.54219, 3, 179, 46.64, -21.84, 0.3517, 180, 11.13, 37.08, 0.31015, 181, -23.26, 30.57, 0.33815, 3, 179, 43.31, -13.35, 0.5914, 180, 2.02, 36.92, 0.2385, 181, -31.88, 27.61, 0.1701, 3, 179, 41.46, -10.32, 0.96, 180, -1.47, 36.23, 0.02541, 181, -34.99, 25.88, 0.01459, 1, 179, 46.78, -12.36, 1, 1, 179, 49.82, -19.55, 1, 1, 179, 50.29, -28.94, 1, 3, 179, 52.23, -42.35, 0.99983, 180, 32.3, 35.19, 0.00014, 181, -2.54, 35.29, 3e-05, 1, 179, 54.27, -52.75, 1, 1, 179, 69.92, -55.95, 1, 1, 179, 73.79, -49.67, 1, 2, 179, 74.15, -31.32, 0.99989, 180, 29.59, 59.58, 0.00011, 3, 179, 71.52, -17.4, 0.99636, 180, 15.62, 61.95, 0.00262, 181, -26.65, 55.61, 0.00102, 1, 179, 68.62, -5.26, 1, 1, 179, 100.3, -15.19, 1, 1, 179, 113.1, 2.39, 1, 1, 179, 104.54, 17.56, 1, 1, 179, 98.77, 31.16, 1, 1, 179, 101.15, 34.05, 1, 1, 179, 116.55, 28.15, 1, 1, 179, 137.28, 22.54, 1, 1, 179, 153.47, 43.88, 1, 1, 179, 160, 63.51, 1, 1, 179, 155.24, 76.53, 1, 1, 179, 132.3, 93.93, 1, 1, 179, 94.37, 99.93, 1, 1, 179, 65.46, 70.16, 1, 1, 179, 25.36, 52.32, 1, 1, 179, 28.7, 25.75, 1, 1, 179, 17.57, 17.75, 1, 1, 179, 6.29, 21.24, 1, 1, 179, -1.69, 20.25, 1, 1, 179, -7.37, 15.52, 1, 2, 179, -19.03, 0.15, 0.85861, 180, -32.31, -16.85, 0.14139, 2, 179, -21.65, -12.51, 0.62374, 180, -21.36, -23.71, 0.37626, 2, 179, -17.15, -21.34, 0.39216, 180, -11.51, -22.56, 0.60784, 3, 179, -13.25, -28.82, 0.16279, 180, -3.14, -21.5, 0.8345, 181, -18.8, -29.56, 0.00271, 3, 179, -11.35, -34.42, 0.05222, 180, 2.77, -21.66, 0.91909, 181, -13.13, -27.89, 0.02868, 3, 179, -11.45, -42.66, 0.0043, 180, 10.46, -24.62, 0.84619, 181, -4.9, -28.34, 0.14951, 2, 180, 19.5, -29.45, 0.61713, 181, 5.19, -30.15, 0.38287, 2, 180, 32.39, -32, 0.32958, 181, 18.24, -28.61, 0.67042, 2, 180, 47.56, -29.81, 0.10087, 181, 31.99, -21.86, 0.89913], "hull": 47, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 0, 92], "width": 220, "height": 111}}, "batgioifull2": {"batgioifull2": {"type": "mesh", "uvs": [0.57584, 0, 1, 0.12814, 0.99405, 0.28226, 0.83176, 0.62131, 0.65699, 0.84049, 0.49157, 1, 0.38234, 1, 0.16075, 0.86447, 0.04528, 0.67268, 0.02655, 0.47062, 0.09833, 0.45007, 0.35737, 0.26171, 0.50406, 0.05622], "triangles": [8, 9, 10, 4, 11, 3, 10, 11, 4, 7, 8, 10, 4, 7, 10, 6, 7, 4, 5, 6, 4, 2, 0, 1, 2, 12, 0, 2, 11, 12, 3, 11, 2], "vertices": [2, 182, -8.76, -26.33, 0.86412, 183, -36.74, -33.28, 0.13588, 1, 182, -24.93, 25.77, 1, 1, 182, -9.87, 34.54, 1, 2, 182, 33.25, 38.26, 0.97005, 183, -7.3, 37.92, 0.02995, 2, 182, 65.79, 33.37, 0.41735, 183, 25.59, 39.08, 0.58265, 2, 182, 92.03, 25.81, 0.09474, 183, 52.77, 36.47, 0.90526, 2, 182, 99.34, 14.41, 0.04305, 183, 62.05, 26.6, 0.95695, 1, 183, 69.7, -3.91, 1, 1, 183, 63.7, -29.19, 1, 1, 183, 48.65, -46.51, 1, 1, 183, 40.87, -41.62, 1, 2, 182, 30.75, -33.19, 0.09767, 183, 3.36, -32.78, 0.90233, 2, 182, 1.39, -30.4, 0.75479, 183, -26.02, -35.42, 0.24521], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 24], "width": 124, "height": 113}}, "batgioifull3": {"batgioifull3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0, 0.10629, 0.40244, 0.14637, 0.35041, 0.18645, 0.33205, 0.23294, 0.33358, 0.30669, 0.39173, 0.37883, 0.45294, 0.40448, 0.54323, 0.36921, 0.60597, 0.26821, 0.63811, 0.18645, 0.63658, 0.12393, 0.65494, 0.06621, 0.62893, 0.04217, 0.5769, 0.06782, 0.5264, 0.09187, 0.45447, 0.41507, 0.3426, 0.44872, 0.31684, 0.49031, 0.30411, 0.53602, 0.30199, 0.57888, 0.31078, 0.64174, 0.33836, 0.58618, 0.37108, 0.53729, 0.38533, 0.48523, 0.38714, 0.44015, 0.36745, 0.37219, 0.36131, 0.37375, 0.32048, 0.44375, 0.27816, 0.51997, 0.26406, 0.62419, 0.28336, 0.71752, 0.32939, 0.74008, 0.42442, 0.6833, 0.49866, 0.58686, 0.51574, 0.48108, 0.46822, 0.40719, 0.40289, 0.15844, 0.71423, 0.18679, 0.6772, 0.21812, 0.6537, 0.28228, 0.65583, 0.42402, 0.64515, 0.54189, 0.64444, 0.63366, 0.65014, 0.55383, 0.70995, 0.42775, 0.77404, 0.26736, 0.78117, 0.21215, 0.76479, 0.17709, 0.73844, 0.21461, 0.32374, 0.1827, 0.31284, 0.14893, 0.30649, 0.11539, 0.32106], "triangles": [32, 2, 3, 31, 2, 32, 33, 32, 3, 23, 22, 32, 21, 31, 32, 21, 32, 22, 53, 54, 2, 33, 23, 32, 31, 53, 2, 20, 31, 21, 31, 52, 53, 31, 19, 30, 55, 2, 54, 30, 52, 31, 34, 33, 3, 6, 53, 52, 7, 52, 30, 6, 52, 7, 24, 33, 34, 23, 33, 24, 20, 19, 31, 5, 55, 54, 6, 5, 54, 6, 54, 53, 30, 8, 7, 29, 30, 19, 28, 19, 20, 25, 23, 24, 26, 22, 23, 26, 23, 25, 21, 22, 26, 27, 20, 21, 27, 21, 26, 28, 20, 27, 29, 8, 30, 55, 18, 2, 4, 55, 5, 39, 29, 19, 39, 19, 28, 35, 34, 3, 9, 29, 39, 8, 29, 9, 4, 18, 55, 27, 39, 28, 38, 39, 27, 9, 39, 38, 35, 36, 24, 35, 24, 34, 25, 24, 36, 37, 25, 36, 26, 25, 37, 38, 27, 26, 37, 38, 26, 2, 18, 16, 10, 9, 38, 18, 17, 16, 8, 12, 13, 11, 9, 10, 15, 16, 17, 8, 4, 7, 7, 5, 6, 7, 4, 5, 8, 9, 12, 8, 18, 4, 13, 18, 8, 17, 18, 13, 14, 15, 17, 11, 12, 9, 45, 38, 37, 10, 38, 45, 44, 11, 10, 45, 44, 10, 46, 37, 36, 45, 37, 46, 42, 13, 12, 13, 14, 17, 43, 12, 11, 42, 12, 43, 43, 11, 44, 41, 13, 42, 14, 13, 41, 47, 45, 46, 40, 14, 41, 51, 40, 41, 50, 41, 42, 51, 41, 50, 50, 42, 43, 47, 48, 44, 47, 44, 45, 43, 44, 48, 49, 50, 43, 48, 49, 43, 16, 1, 2, 1, 16, 15, 40, 1, 15, 40, 15, 14, 1, 40, 51, 1, 51, 50, 35, 3, 0, 36, 35, 0, 46, 36, 0, 47, 46, 0, 48, 47, 0, 1, 49, 48, 1, 50, 49, 48, 0, 1], "vertices": [1, 170, -21.49, -16.25, 1, 1, 170, -6.21, 44.87, 1, 1, 170, 57.82, 28.86, 1, 1, 170, 42.54, -32.26, 1, 1, 170, 30.42, 28.8, 1, 1, 170, 33.14, 25.52, 1, 1, 170, 33.71, 22.78, 1, 1, 170, 32.9, 19.96, 1, 1, 170, 28.05, 16.38, 1, 1, 170, 23.03, 12.96, 1, 1, 170, 16.85, 12.83, 1, 1, 170, 13.37, 15.99, 1, 1, 170, 12.86, 22.68, 1, 1, 170, 14.21, 27.65, 1, 1, 170, 13.99, 31.77, 1, 1, 170, 16.53, 34.88, 1, 1, 170, 20.23, 35.52, 1, 1, 170, 23.07, 33.14, 1, 1, 170, 27.31, 30.52, 1, 1, 170, 29.54, 8.97, 1, 1, 170, 30.67, 6.51, 1, 1, 170, 30.85, 3.76, 1, 1, 170, 30.29, 0.93, 1, 1, 170, 29.07, -1.55, 1, 1, 170, 26.35, -4.95, 1, 1, 170, 25.1, -1.03, 1, 1, 170, 24.93, 2.19, 1, 1, 170, 25.61, 5.4, 1, 1, 170, 27.56, 7.84, 1, 1, 170, 28.99, 11.89, 1, 1, 170, 31.58, 11.15, 1, 1, 170, 33.23, 6.19, 1, 1, 170, 32.96, 1.31, 1, 1, 170, 30.14, -4.76, 1, 1, 170, 25.76, -9.72, 1, 1, 170, 19.33, -9.58, 1, 1, 170, 15.45, -4.92, 1, 1, 170, 15.83, 1.25, 1, 1, 170, 20.49, 6.95, 1, 1, 170, 25.8, 10.42, 1, 1, 170, 9.66, 30.61, 1, 1, 170, 11.6, 28.28, 1, 1, 170, 12.63, 25.99, 1, 1, 170, 11.51, 22.1, 1, 1, 170, 10.03, 13.27, 1, 1, 170, 8.27, 6.05, 1, 1, 170, 6.51, 0.54, 1, 1, 170, 3.9, 6.37, 1, 1, 170, 1.72, 15.11, 1, 1, 170, 3.71, 25.02, 1, 1, 170, 5.61, 28.13, 1, 1, 170, 7.83, 29.86, 1, 1, 170, 33.81, 20.92, 1, 1, 170, 34.99, 22.7, 1, 1, 170, 35.92, 24.66, 1, 1, 170, 35.5, 26.95, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 8, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 38, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 58, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 80, 104, 106, 106, 108, 108, 110], "width": 63, "height": 66}}, "batgioifull4": {"batgioifull4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 173, -0.71, 11.26, 1, 1, 173, 15.78, -3.3, 1, 1, 173, 5.86, -14.55, 1, 1, 173, -10.64, 0.01, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 22, "height": 15}}, "batgioifull5": {"batgioifull5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 172, -5.92, 11.09, 0.49285, 171, 1.68, 11.89, 0.50715, 1, 172, 24.07, 11.95, 1, 1, 172, 24.61, -7.04, 1, 2, 172, -5.38, -7.9, 0.08274, 171, -0.78, -6.95, 0.91726], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 19}}, "batgioifull6": {"batgioifull6": {"type": "mesh", "uvs": [0, 0.63007, 0.04797, 0.67668, 0.14059, 0.68601, 0.15463, 0.7233, 0.22255, 0.72278, 0.34262, 0.72187, 0.50453, 0.72063, 0.52605, 0.65138, 0.53915, 0.57413, 0.54775, 0.56235, 0.57729, 0.60004, 0.60835, 0.62324, 0.63126, 0.64861, 0.64932, 0.69133, 0.66137, 0.73067, 0.66916, 0.78211, 0.67554, 0.86483, 0.67483, 0.94048, 0.67838, 0.96671, 0.70955, 0.99092, 0.8045, 1, 0.89804, 0.99798, 1, 0.95763, 1, 0.90114, 0.96039, 0.84365, 0.91859, 0.79825, 0.88245, 0.75387, 0.86119, 0.71655, 0.8619, 0.67418, 0.85765, 0.63181, 0.83923, 0.58339, 0.8123, 0.52791, 0.76199, 0.41998, 0.68121, 0.33625, 0.56571, 0.20007, 0.41265, 0.08205, 0.23976, 0.08508, 0.18661, 0.16376, 0.12638, 0.23134, 0.03427, 0.51177, 0, 0.57431, 0.43063, 0.14888, 0.42561, 0.32305, 0.41167, 0.46855, 0.38771, 0.57641, 0.35907, 0.64551, 0.50893, 0.34982, 0.50638, 0.49345, 0.21576, 0.15745, 0.18429, 0.31271, 0.1826, 0.46888, 0.19745, 0.59305, 0.14429, 0.31593, 0.13879, 0.45892, 0.15104, 0.61091], "triangles": [21, 24, 23, 19, 17, 20, 25, 16, 26, 19, 18, 17, 16, 20, 17, 16, 25, 20, 24, 21, 25, 23, 22, 21, 25, 21, 20, 26, 16, 15, 26, 15, 27, 12, 11, 31, 12, 31, 30, 13, 12, 30, 13, 30, 29, 13, 29, 28, 28, 14, 13, 27, 14, 28, 15, 14, 27, 33, 46, 34, 33, 47, 46, 32, 47, 33, 10, 9, 32, 32, 9, 47, 32, 11, 10, 31, 11, 32, 34, 41, 35, 48, 37, 36, 37, 52, 38, 49, 37, 48, 49, 52, 37, 41, 36, 35, 42, 41, 34, 46, 42, 34, 52, 39, 38, 41, 48, 36, 42, 48, 41, 42, 49, 48, 43, 42, 46, 43, 49, 42, 49, 53, 52, 50, 49, 43, 50, 53, 49, 47, 43, 46, 53, 39, 52, 8, 47, 9, 44, 50, 43, 44, 43, 47, 44, 47, 8, 51, 50, 44, 54, 53, 50, 54, 50, 51, 39, 53, 54, 40, 39, 54, 0, 40, 54, 45, 51, 44, 7, 44, 8, 45, 44, 7, 1, 0, 54, 2, 1, 54, 6, 45, 7, 45, 4, 51, 5, 45, 6, 5, 4, 45, 2, 54, 51, 4, 2, 51, 3, 2, 4], "vertices": [1, 169, 10.12, 110.9, 1, 1, 169, 0.27, 94.74, 1, 1, 169, -0.3, 64.47, 1, 1, 169, -8.6, 59.46, 1, 1, 169, -7.34, 37.35, 1, 1, 169, -5.11, -1.72, 1, 1, 169, -2.1, -54.43, 1, 2, 169, 14.1, -60.61, 0.936, 174, 30.74, -61.58, 0.064, 3, 169, 31.98, -63.96, 0.90722, 174, 24.35, -44.55, 0.08847, 175, -41.19, -47.49, 0.00431, 3, 169, 34.82, -66.63, 0.2828, 174, 25.16, -40.74, 0.64162, 175, -42.16, -43.72, 0.07558, 3, 169, 26.7, -76.69, 0.09573, 174, 37.97, -42.48, 0.67891, 175, -29.9, -39.59, 0.22536, 4, 169, 21.92, -87.07, 0.03751, 174, 49.33, -41.19, 0.55629, 175, -20.29, -33.4, 0.40599, 176, -44.9, -36.87, 0.0002, 4, 169, 16.5, -94.83, 0.01243, 174, 58.78, -41.81, 0.3525, 175, -11.56, -29.76, 0.62607, 176, -36.46, -32.59, 0.009, 4, 169, 7.03, -101.22, 0.00217, 174, 69.14, -46.6, 0.12096, 175, -0.14, -29.45, 0.81379, 176, -25.1, -31.44, 0.06308, 5, 169, 0.03, -106.96, 0.00016, 174, 77.68, -49.63, 0.02575, 175, 8.85, -28.37, 0.77886, 176, -16.21, -29.7, 0.19188, 177, -45.59, -27.78, 0.00335, 4, 174, 88.92, -59.45, 0.00048, 175, 23.28, -32.19, 0.53822, 176, -1.53, -32.44, 0.4221, 177, -31.07, -31.24, 0.0392, 3, 175, 40.5, -33.7, 0.19301, 176, 15.75, -32.68, 0.55581, 177, -13.82, -32.34, 0.25118, 3, 175, 59.67, -32.81, 0.06546, 176, 34.8, -30.37, 0.45117, 177, 5.33, -30.97, 0.48337, 3, 175, 68.66, -34.02, 0.0525, 176, 43.85, -30.91, 0.42656, 177, 14.34, -31.97, 0.52094, 3, 175, 76.55, -27.39, 0.03331, 176, 51.23, -23.72, 0.36144, 177, 22.06, -25.14, 0.60525, 2, 176, 65.18, -4.92, 0.04268, 177, 36.92, -7.06, 0.95732, 1, 177, 45.96, 5.88, 1, 1, 177, 47.62, 28.88, 1, 1, 177, 37.94, 40.38, 1, 2, 176, 51.39, 44.35, 0.01869, 177, 25.59, 42.83, 0.98131, 3, 175, 66.05, 32.87, 0.00019, 176, 36.31, 35.59, 0.13743, 177, 10.1, 34.83, 0.86238, 3, 175, 49.42, 27.04, 0.03521, 176, 20.16, 28.56, 0.43523, 177, -6.39, 28.61, 0.52956, 4, 174, 129.55, -12.62, 0.0034, 175, 38.9, 27.82, 0.19368, 176, 9.6, 28.56, 0.58841, 177, -16.93, 29.13, 0.21451, 4, 174, 124.29, -4.46, 0.02119, 175, 30.57, 32.8, 0.44361, 176, 0.93, 32.91, 0.4616, 177, -25.38, 33.9, 0.0736, 4, 174, 117.7, 2.79, 0.05697, 175, 21.44, 36.37, 0.62104, 176, -8.44, 35.79, 0.29962, 177, -34.59, 37.25, 0.02237, 4, 174, 106.51, 8.6, 0.15764, 175, 8.83, 36.61, 0.71558, 176, -21.03, 35.09, 0.12481, 177, -47.2, 37.18, 0.00197, 3, 174, 92.11, 14.19, 0.4346, 175, -6.55, 35.22, 0.54585, 176, -36.27, 32.58, 0.01955, 2, 174, 64.67, 25.44, 0.97153, 175, -36.14, 33.12, 0.02847, 1, 174, 32.11, 26.53, 1, 2, 169, 117.98, -68.19, 0.68346, 174, -16.55, 31.21, 0.31654, 1, 169, 142.39, -16.97, 1, 1, 169, 138.79, 39.29, 1, 1, 169, 119.9, 55.66, 1, 1, 169, 103.43, 74.47, 1, 1, 169, 37.75, 101.14, 1, 1, 169, 22.87, 111.56, 1, 2, 169, 127.41, -23.61, 0.992, 174, -59.58, 16.2, 0.008, 2, 169, 87.5, -24.03, 0.928, 174, -38.55, -17.73, 0.072, 2, 169, 53.99, -21.21, 0.888, 174, -23.62, -47.86, 0.112, 2, 169, 28.91, -14.69, 0.928, 174, -16.22, -72.68, 0.072, 1, 169, 12.63, -6.18, 1, 3, 169, 82.77, -51.48, 0.42566, 174, -12.63, -7.56, 0.54756, 175, -90.75, -30.77, 0.02677, 3, 169, 49.88, -52.34, 0.43938, 174, 5.14, -35.25, 0.51071, 175, -62.53, -47.69, 0.04991, 2, 169, 121.84, 46.24, 0.99483, 174, -116.46, -24.73, 0.00517, 2, 169, 85.8, 54.66, 0.97401, 174, -105, -59.92, 0.02599, 2, 169, 50.06, 53.36, 0.97156, 174, -85.38, -89.83, 0.02844, 2, 169, 21.91, 47.06, 0.9888, 174, -65.42, -110.65, 0.0112, 2, 169, 84.39, 67.64, 0.98597, 174, -115.37, -67.85, 0.01403, 2, 169, 51.6, 67.74, 0.98093, 174, -98.48, -95.96, 0.01907, 2, 169, 17.04, 61.96, 0.99205, 174, -75.64, -122.52, 0.00795], "hull": 41, "edges": [0, 2, 2, 4, 4, 6, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 0, 80, 70, 82, 82, 84, 84, 86, 86, 88, 88, 90, 10, 12, 90, 10, 68, 92, 92, 94, 72, 96, 96, 98, 98, 100, 100, 102, 6, 8, 8, 10, 102, 8, 74, 104, 104, 106, 106, 108], "width": 326, "height": 229}}, "batgioifull7": {"batgioifull7": {"type": "mesh", "uvs": [0, 0.07831, 0.03083, 0.04005, 0.24249, 0.02291, 0.43967, 0.02423, 0.48852, 0.0638, 0.41073, 0.21548, 0.61696, 0.47664, 0.64789, 0.47903, 0.67296, 0.48865, 0.72177, 0.48384, 0.76796, 0.49827, 0.76532, 0.53387, 0.73101, 0.55792, 0.71471, 0.55694, 0.69076, 0.57324, 0.99938, 0.95483, 1, 1, 0.94122, 0.97264, 0.64111, 0.59185, 0.569, 0.54605, 0.57016, 0.49855, 0.29933, 0.17949, 0, 0.22274], "triangles": [21, 2, 3, 5, 21, 3, 5, 3, 4, 22, 0, 1, 21, 22, 1, 21, 1, 2, 20, 5, 6, 21, 5, 20, 11, 9, 10, 13, 8, 9, 12, 13, 9, 11, 12, 9, 14, 8, 13, 18, 6, 7, 18, 7, 8, 18, 8, 14, 19, 20, 6, 18, 19, 6, 17, 14, 15, 18, 14, 17, 17, 15, 16], "vertices": [1, 178, 71.57, -250.75, 1, 1, 178, 55.6, -257.97, 1, 1, 178, 1.89, -228.35, 1, 1, 178, -44.2, -195.18, 1, 1, 178, -46.68, -174.29, 1, 1, 178, 6.18, -138.26, 1, 1, 178, 17.14, -19.74, 1, 1, 178, 10.41, -13.83, 1, 1, 178, 6.7, -6.56, 1, 1, 178, -5.88, 0, 1, 1, 178, -13.46, 12.32, 1, 1, 178, -4.73, 23.37, 1, 1, 178, 8.82, 25.43, 1, 1, 178, 12.43, 22.41, 1, 1, 178, 21.78, 23.69, 1, 1, 178, 36.09, 198.09, 1, 1, 178, 46.23, 212.77, 1, 1, 178, 53.83, 194.18, 1, 1, 178, 37.7, 21.45, 1, 1, 178, 44.24, -5.3, 1, 1, 178, 33.14, -20.44, 1, 1, 178, 24.19, -168.38, 1, 1, 178, 104.47, -204.14, 1], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44], "width": 288, "height": 395}}, "batgioifull8": {"batgioifull8": {"type": "mesh", "uvs": [1, 0.68612, 1, 1, 0.36524, 1, 0, 1, 0, 0.60945, 0, 0, 0.21477, 0, 1, 0, 0.73891, 0.06454, 0.77627, 0.2083, 0.87019, 0.41641, 0.92574, 0.56975, 0.52885, 0.02621, 0.48441, 0.25074, 0.52481, 0.4698, 0.56419, 0.68612, 0.57429, 0.94352, 0.43796, 0.05633, 0.41978, 0.25622, 0.39756, 0.49855, 0.35515, 0.74774, 0.16125, 0.16449, 0.11883, 0.31098, 0.08348, 0.48623], "triangles": [12, 6, 7, 17, 6, 12, 8, 12, 7, 21, 5, 6, 9, 8, 7, 13, 17, 12, 17, 21, 6, 18, 17, 13, 18, 21, 17, 22, 5, 21, 22, 21, 18, 10, 9, 7, 13, 9, 14, 8, 13, 12, 9, 13, 8, 14, 9, 10, 22, 4, 5, 19, 22, 18, 23, 22, 19, 18, 13, 14, 19, 18, 14, 11, 10, 7, 0, 11, 7, 22, 23, 4, 15, 14, 10, 15, 10, 11, 19, 14, 15, 20, 23, 19, 20, 19, 15, 4, 23, 20, 0, 16, 15, 0, 15, 11, 20, 15, 16, 3, 4, 20, 2, 20, 16, 3, 20, 2, 16, 0, 1, 2, 16, 1], "vertices": [1, 184, 69.6, 110.81, 1, 1, 184, 116.32, 112.93, 1, 1, 184, 122.14, -15.16, 1, 1, 184, 125.49, -88.86, 1, 1, 184, 67.36, -91.5, 1, 1, 184, -23.36, -95.62, 1, 1, 184, -25.33, -52.28, 1, 1, 184, -32.53, 106.17, 1, 1, 184, -20.53, 53.92, 1, 1, 184, 0.53, 62.43, 1, 1, 184, 30.64, 82.79, 1, 1, 184, 52.96, 95.04, 1, 1, 184, -24.31, 11.27, 1, 1, 184, 9.52, 3.82, 1, 1, 184, 41.76, 13.46, 1, 1, 184, 73.6, 22.87, 1, 1, 184, 111.81, 26.65, 1, 1, 184, -18.99, -6.87, 1, 1, 184, 10.93, -9.18, 1, 1, 184, 47.2, -12.03, 1, 1, 184, 84.68, -18.9, 1, 1, 184, -0.35, -61.97, 1, 1, 184, 21.84, -69.54, 1, 1, 184, 48.25, -75.49, 1], "hull": 8, "edges": [16, 18, 18, 20, 20, 22, 2, 0, 0, 14, 22, 0, 24, 26, 26, 28, 28, 30, 30, 32, 34, 36, 36, 38, 38, 40, 2, 4, 4, 6, 40, 4, 10, 12, 12, 14, 12, 42, 42, 44, 44, 46, 6, 8, 8, 10, 46, 8], "width": 202, "height": 149}}, "batgioifull9": {"batgioifull9": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 184, 213.09, 106.48, 1, 1, 184, 221.67, -82.33, 1, 1, 184, -12.09, -92.95, 1, 1, 184, -20.67, 95.85, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 189, "height": 234}}, "bgtank": {"bgtank": {"type": "mesh", "uvs": [0, 0, 1, 0, 1, 1, 0, 1], "triangles": [3, 0, 1, 3, 1, 2], "vertices": [-95, 232, 95, 232, 95, -232, -95, -232], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 95, "height": 232}}, "bone102": {"bone102": {"type": "path", "lengths": [160.78, 438.29, 701.68], "vertexCount": 9, "vertices": [17.09, 201.35, 36.05, 212.67, 59.93, 226.93, 155.39, 285.34, 184.55, 259.61, 241.96, 209.05, 157.29, 98.29, 150.17, -6.98, 140.47, -92.56]}}, "brush": {"brush": {"x": 0.89, "y": -247.33, "width": 229, "height": 34}}, "chonmuccuocboard": {"chonmuccuocboard": {"x": -73.79, "y": -259.46, "width": 148, "height": 110}}, "chonmuccuocboard2": {"chonmuccuocboard": {"x": 73.3, "y": -259.46, "scaleX": -1.003, "width": 148, "height": 110}}, "cloud": {"cloud": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-38.24, -111.65, -29.99, 106.92, 87.94, 102.46, 79.69, -116.1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 139, "height": 75}}, "duongtank1": {"duongtank1": {"type": "mesh", "uvs": [0.91893, 0, 0.8439, 0.04384, 0.72847, 0.23666, 0.54956, 0.46096, 0.40527, 0.66952, 0.24367, 0.85643, 0.12824, 0.89775, 0, 0.91939, 0, 1, 0.17153, 1, 0.29273, 0.96071, 0.39373, 0.89578, 0.5611, 0.701, 0.69096, 0.49244, 0.87564, 0.27011, 1, 0.03204, 1, 0], "triangles": [8, 6, 9, 9, 5, 10, 9, 6, 5, 8, 7, 6, 10, 5, 11, 11, 5, 4, 11, 4, 12, 4, 3, 12, 12, 3, 13, 13, 2, 14, 13, 3, 2, 2, 1, 14, 15, 1, 0, 15, 14, 1, 0, 16, 15], "vertices": [1, 157, -2.57, -7.48, 1, 2, 157, 7.98, -7.53, 0.74408, 158, -2.21, -7.25, 0.25592, 2, 158, 29.34, -5.08, 0.7293, 159, -1.03, -5.06, 0.2707, 1, 159, 37.92, -7.35, 1, 1, 160, 23.31, -7.03, 1, 1, 161, 13.54, -3.91, 1, 1, 161, 27.49, -6.03, 1, 1, 161, 40.92, -11.38, 1, 1, 161, 47.82, -1.42, 1, 1, 161, 32.34, 9.31, 1, 1, 161, 18.04, 12.04, 1, 2, 160, 54.04, 7.59, 0.03523, 161, 3.37, 10.34, 0.96477, 1, 160, 19.58, 10.33, 1, 1, 159, 35.12, 8.64, 1, 2, 158, 26.32, 11.58, 0.74915, 159, -3.85, 11.62, 0.25085, 1, 157, -6.46, 1.87, 1, 1, 157, -9.49, -1.88, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32], "width": 105, "height": 154}}, "duongtank10": {"duongtank3": {"type": "mesh", "uvs": [0.6754, 0, 0.72678, 0.04621, 0.74391, 0.07997, 0.79301, 0.13784, 0.80786, 0.22584, 0.82156, 0.24995, 0.90721, 0.33675, 0.93004, 0.39824, 0.95517, 0.432, 0.986, 0.46334, 0.98942, 0.53688, 0.9723, 0.60801, 0.86952, 0.75268, 0.79301, 0.81537, 0.66969, 0.88529, 0.57948, 0.95763, 0.46529, 1, 0.43788, 1, 0.37622, 0.96606, 0.29742, 0.96245, 0.23234, 0.92749, 0.12614, 0.93834, 0.04963, 0.92146, 0, 0.99741, 0, 0.91543, 0.04278, 0.82983, 0.11814, 0.73218, 0.2369, 0.66347, 0.38078, 0.67552, 0.471, 0.61163, 0.5555, 0.53929, 0.61031, 0.44887, 0.61602, 0.42356, 0.60117, 0.33555, 0.59432, 0.20896, 0.58062, 0.08841, 0.61602, 0], "triangles": [25, 21, 22, 24, 25, 22, 20, 26, 27, 20, 27, 28, 26, 21, 25, 26, 20, 21, 19, 20, 28, 23, 24, 22, 15, 28, 14, 28, 18, 19, 15, 18, 28, 16, 17, 18, 15, 16, 18, 14, 30, 13, 29, 30, 14, 14, 28, 29, 30, 31, 12, 13, 30, 12, 10, 11, 8, 10, 8, 9, 32, 6, 7, 6, 32, 5, 32, 8, 11, 8, 32, 7, 12, 31, 11, 32, 11, 31, 35, 2, 34, 33, 34, 4, 1, 2, 35, 3, 34, 2, 4, 34, 3, 5, 32, 33, 5, 33, 4, 35, 36, 0, 0, 1, 35], "vertices": [1, 111, 0.23, 14.13, 1, 2, 111, 20.92, 16.25, 0.31677, 112, -2.37, 17.71, 0.68323, 2, 111, 31.41, 13.29, 0.03337, 112, 8.09, 20.77, 0.96663, 2, 112, 27.49, 32.11, 0.9926, 113, -30.54, 46.46, 0.0074, 2, 112, 52.55, 30.93, 0.77868, 113, -7.63, 36.21, 0.22132, 2, 112, 60.14, 33.57, 0.57227, 113, 0.39, 35.91, 0.42773, 2, 112, 90.12, 54.28, 0.04544, 113, 35.86, 44.24, 0.95456, 2, 112, 108.55, 57.29, 0.00353, 113, 54.13, 40.32, 0.99647, 1, 113, 66.43, 41.44, 1, 2, 113, 79.23, 44.42, 0.99951, 114, -63.11, 64.49, 0.00049, 3, 113, 96.74, 33.52, 0.97335, 114, -47.44, 77.91, 0.02639, 115, -113.68, 34.55, 0.00026, 3, 113, 109.97, 17.67, 0.88619, 114, -28.34, 85.75, 0.10627, 115, -99.44, 49.51, 0.00754, 4, 113, 124.56, -32.22, 0.41754, 114, 23.63, 84.65, 0.41722, 115, -51.53, 69.7, 0.16447, 116, -115.1, 51.05, 0.00077, 4, 113, 125.09, -62.15, 0.20431, 114, 52.33, 76.14, 0.39854, 115, -21.86, 73.63, 0.37428, 116, -86.51, 59.92, 0.02287, 4, 113, 118.79, -105.41, 0.03486, 114, 91.68, 57.11, 0.12815, 115, 21.83, 72.3, 0.60895, 116, -43.22, 65.97, 0.22804, 5, 113, 119.04, -140.44, 0.0022, 114, 125.17, 46.81, 0.01598, 115, 56.61, 76.55, 0.42035, 116, -9.66, 76.01, 0.54794, 117, -68.59, 73.68, 0.01353, 4, 114, 156.64, 25.37, 1e-05, 115, 94.09, 69.81, 0.16814, 116, 28.42, 75.67, 0.72816, 117, -30.52, 74.66, 0.10369, 3, 115, 101.73, 65.68, 0.13889, 116, 36.65, 72.89, 0.73051, 117, -22.2, 72.17, 0.1306, 3, 115, 114.41, 48.04, 0.05366, 116, 52.11, 57.63, 0.6411, 117, -6.22, 57.45, 0.30524, 3, 115, 135.9, 35.29, 0.00443, 116, 75.44, 48.68, 0.3506, 117, 17.41, 49.31, 0.64498, 2, 116, 91.85, 32.8, 0.091, 117, 34.35, 34.01, 0.909, 1, 117, 67.45, 27.26, 1, 1, 117, 89.32, 15.77, 1, 1, 117, 110.49, 31.64, 1, 1, 117, 103.9, 9.64, 1, 1, 117, 84.03, -9.44, 1, 1, 117, 53.3, -28.78, 1, 2, 116, 66.8, -36.8, 0.18893, 117, 11.73, -36.41, 0.81107, 3, 115, 74.47, -22.89, 0.0243, 116, 24.69, -19, 0.96463, 117, -30.97, -20.09, 0.01107, 3, 114, 69.31, -39.53, 0.01455, 115, 40.82, -25.06, 0.82169, 116, -8.12, -26.81, 0.16376, 3, 112, 119.24, -67.33, 0.00011, 114, 36.92, -30.66, 0.5727, 115, 7.63, -30.17, 0.42719, 4, 112, 98.68, -44.53, 0.26211, 113, 7.77, -50.87, 0.52185, 114, 6.26, -32.34, 0.17025, 115, -19.68, -44.21, 0.04579, 4, 112, 92.2, -41.11, 0.31703, 113, 2.99, -45.33, 0.49872, 114, -0.46, -35.23, 0.18202, 115, -24.64, -49.59, 0.00223, 3, 112, 67.13, -39.93, 0.62419, 113, -19.91, -35.08, 0.1919, 114, -17.13, -53.98, 0.18391, 4, 111, 22.78, -45.68, 0.03074, 112, 32.15, -33.75, 0.96165, 113, -50.23, -16.57, 0.00091, 114, -43.92, -77.32, 0.0067, 2, 111, -3.93, -24.57, 0.56258, 112, -1.7, -30.09, 0.43742, 1, 111, -13.22, 0.97, 1], "hull": 37, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 0, 72], "width": 303, "height": 287}}, "duongtank11": {"duongtank11": {"type": "mesh", "uvs": [1, 0, 0.70681, 0.07256, 0.65313, 0.05539, 0.53602, 0.06765, 0.36036, 0.14859, 0.36768, 0.19764, 0.47747, 0.31537, 0.40183, 0.42573, 0.27984, 0.51648, 0.08466, 0.59497, 0.00171, 0.64892, 0, 0.73722, 0.08222, 0.93588, 0.29204, 1, 0.51406, 1, 0.58482, 0.93588, 0.68973, 0.73477, 0.89467, 0.56063, 1, 0.47969], "triangles": [12, 9, 13, 12, 11, 9, 9, 11, 10, 9, 8, 13, 13, 8, 15, 14, 13, 15, 16, 8, 7, 15, 8, 16, 1, 0, 18, 17, 6, 1, 7, 6, 16, 6, 5, 3, 6, 3, 2, 5, 4, 3, 1, 6, 2, 18, 17, 1, 16, 6, 17], "vertices": [1, 0, -44.4, 25.63, 1, 1, 135, 57.64, -15.1, 1, 1, 135, 66.53, -20.64, 1, 1, 135, 88.22, -24.41, 1, 1, 135, 123.63, -19.57, 1, 1, 135, 124.56, -10.99, 1, 1, 135, 110.15, 14.19, 1, 2, 135, 128.86, 28.93, 0.456, 136, 43.35, -28.67, 0.544, 2, 136, 70.42, -34.64, 0.52, 137, 43.82, -31.83, 0.48, 2, 136, 105.26, -52.09, 0.224, 137, 79.73, -46.97, 0.776, 1, 137, 97.44, -50.8, 1, 2, 137, 108.19, -39.85, 0.696, 138, 57.85, -40.55, 0.304, 1, 138, 70.75, -5.01, 1, 2, 137, 99.73, 30.91, 0.456, 138, 50.25, 30.3, 0.544, 3, 136, 102.03, 54.74, 0.39782, 137, 69.51, 59.43, 0.49018, 138, 20.39, 59.18, 0.112, 2, 136, 84.87, 56.85, 0.344, 137, 52.25, 60.4, 0.656, 2, 136, 45.95, 47.4, 0.768, 137, 14.03, 48.43, 0.232, 1, 135, 46.14, 76.02, 1, 1, 135, 23.42, 67.69, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36], "width": 191, "height": 190}}, "duongtank12": {"duongtank12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 139, -300.16, -33.51, 1, 1, 139, -288.02, 72.44, 1, 1, 139, 279.3, 35.86, 1, 1, 139, 267.16, -70.09, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 576}}, "duongtank13": {"duongtank2": {"type": "mesh", "uvs": [0.10931, 0.43673, 0.11893, 0.493, 0.14641, 0.42969, 0.24669, 0.32417, 0.29202, 0.29604, 0.43764, 0.10611, 0.51869, 0.07562, 0.66018, 0.06624, 0.72063, 0.04749, 0.79893, 0, 0.97339, 0, 1, 0.0428, 0.9885, 0.14597, 0.87723, 0.24914, 0.79618, 0.27493, 0.73436, 0.28431, 0.66842, 0.357, 0.64095, 0.47893, 0.65606, 0.56335, 0.69453, 0.63838, 0.73162, 0.67824, 0.77558, 0.73452, 0.78794, 0.80017, 0.77695, 0.88459, 0.71238, 0.9221, 0.66568, 0.90335, 0.62859, 0.85879, 0.59149, 0.8119, 0.55853, 0.79783, 0.54067, 0.84004, 0.51044, 0.99948, 0.25905, 1, 0.0681, 0.87521, 0, 0.58914, 0, 0.2468, 0.04612, 0.21397, 0.09832, 0.27728], "triangles": [12, 10, 11, 12, 13, 9, 12, 9, 10, 8, 9, 13, 14, 8, 13, 15, 8, 14, 7, 8, 15, 15, 6, 7, 16, 6, 15, 16, 5, 6, 17, 4, 16, 3, 17, 18, 28, 18, 19, 27, 28, 19, 28, 1, 18, 20, 27, 19, 27, 20, 21, 26, 27, 21, 22, 26, 21, 22, 25, 26, 23, 25, 22, 24, 25, 23, 16, 4, 5, 34, 0, 33, 36, 34, 35, 0, 34, 36, 33, 0, 1, 17, 3, 4, 18, 2, 3, 18, 1, 2, 29, 1, 28, 32, 33, 1, 32, 1, 29, 29, 31, 32, 30, 31, 29], "vertices": [1, 118, -5.86, -22.07, 1, 1, 118, -2.59, -22.76, 1, 1, 118, -4.32, -18.52, 1, 1, 118, -4.44, -6.55, 1, 2, 118, -3.53, -1.67, 0.99837, 120, -32.72, -2.98, 0.00163, 3, 118, -5.52, 16.73, 0.45258, 119, -29.81, 15.93, 0.00055, 120, -16.53, 6, 0.54687, 3, 118, -2.91, 24.89, 0.17857, 119, -23.96, 22.18, 0.00016, 120, -8, 6.77, 0.82127, 2, 120, 6.62, 5.64, 0.99245, 121, -6.45, 6.23, 0.00755, 2, 120, 12.95, 5.99, 0.51639, 121, -0.11, 6.02, 0.48361, 1, 121, 8.37, 7.03, 1, 1, 121, 26.07, 3.42, 1, 1, 121, 28.28, 0.49, 1, 1, 121, 25.95, -4.99, 1, 1, 121, 13.49, -8.41, 1, 3, 119, 6.01, 29.58, 0.00049, 120, 19.27, -7.69, 0.09882, 121, 4.98, -8.16, 0.90069, 3, 119, 1.09, 25.45, 0.02044, 120, 12.85, -7.5, 0.6436, 121, -1.4, -7.4, 0.33596, 4, 118, 18.57, 30.6, 0.00738, 119, -2.1, 18.14, 0.19911, 120, 5.6, -10.81, 0.7892, 121, -8.91, -10.07, 0.00432, 3, 118, 23.19, 24.74, 0.02023, 119, -0.44, 10.87, 0.63543, 120, 2, -17.35, 0.34434, 2, 119, 3.6, 7.87, 0.90655, 120, 3.01, -22.27, 0.09345, 2, 119, 9.31, 6.7, 0.9928, 120, 6.49, -26.94, 0.0072, 2, 119, 13.74, 7.08, 0.99999, 120, 10.05, -29.62, 1e-05, 1, 119, 19.3, 7.11, 1, 1, 119, 22.49, 4.82, 1, 1, 119, 24.32, 0.26, 1, 1, 119, 20.1, -5.34, 1, 2, 118, 45.39, 15.2, 0, 119, 15.54, -7.26, 1, 2, 118, 41.31, 13.09, 0.00972, 119, 10.94, -7.42, 0.99028, 2, 118, 37.12, 11.04, 0.09194, 119, 6.28, -7.48, 0.90806, 2, 118, 34.75, 8.46, 0.31913, 119, 3.03, -8.8, 0.68087, 2, 118, 35.93, 5.68, 0.56847, 119, 2.9, -11.82, 0.43153, 2, 118, 42.26, -1.47, 0.83443, 119, 5.56, -20.99, 0.16557, 1, 118, 29.53, -24.18, 1, 1, 118, 13.69, -37.95, 1, 1, 118, -3.88, -36.16, 1, 1, 118, -20.77, -26.66, 1, 1, 118, -20.05, -21.59, 1, 1, 118, -14.28, -18.64, 1], "hull": 37, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 0, 72], "width": 99, "height": 58}}, "duongtank14": {"duongtank6": {"type": "mesh", "uvs": [0.61092, 0.34648, 0.59457, 0.33142, 0.56691, 0.32904, 0.57949, 0.25455, 0.57068, 0.23316, 0.54553, 0.21651, 0.57194, 0.18878, 0.59332, 0.04376, 0.47134, 0, 0.21859, 0, 0.0702, 0.07942, 0, 0.16025, 0.15697, 0.26882, 0.19595, 0.2815, 0.2103, 0.30009, 0.22322, 0.31231, 0.23465, 0.32264, 0.27242, 0.37182, 0.343, 0.43665, 0.36039, 0.44323, 0.4096, 0.43916, 0.41457, 0.44573, 0.41307, 0.45952, 0.22024, 0.59557, 0.18831, 0.64127, 0.11162, 0.87338, 0.11593, 0.89512, 0.16192, 0.93225, 0.16192, 1, 0.20647, 1, 0.79281, 0.9395, 0.7885, 0.86704, 0.77126, 0.84984, 0.92647, 0.69587, 0.97964, 0.50659, 0.89054, 0.38795, 0.75832, 0.35806, 0.31932, 0.29831, 0.32087, 0.28697, 0.32961, 0.28036, 0.34717, 0.27539, 0.36661, 0.2721, 0.38297, 0.27008, 0.40052, 0.26819, 0.38719, 0.2782, 0.37707, 0.28706, 0.36054, 0.29537, 0.34753, 0.29892, 0.33046, 0.29856, 0.30084, 0.3058, 0.29948, 0.28958, 0.31933, 0.27481, 0.36506, 0.26021, 0.4106, 0.25544, 0.43711, 0.26839, 0.43206, 0.29124, 0.41186, 0.30772, 0.37568, 0.3209, 0.33801, 0.31748, 0.27538, 0.30646, 0.26608, 0.30386, 0.25792, 0.30352, 0.24792, 0.30558, 0.24048, 0.30873, 0.23878, 0.31491, 0.24534, 0.31725, 0.254, 0.31762, 0.26194, 0.31637, 0.26872, 0.31395, 0.27495, 0.31095, 0.27812, 0.30576, 0.26535, 0.30051, 0.24906, 0.2991, 0.23818, 0.30062, 0.22958, 0.30463, 0.22865, 0.31251, 0.24089, 0.32138, 0.25613, 0.3306, 0.27292, 0.33061, 0.2784, 0.31296], "triangles": [6, 8, 7, 5, 8, 6, 8, 52, 9, 53, 8, 5, 53, 52, 8, 53, 42, 52, 54, 53, 5, 12, 10, 9, 9, 13, 12, 11, 10, 12, 43, 42, 53, 41, 52, 42, 52, 51, 9, 40, 51, 52, 41, 40, 52, 44, 42, 43, 39, 51, 40, 51, 13, 9, 38, 51, 39, 45, 41, 42, 45, 42, 44, 51, 72, 13, 50, 51, 38, 54, 43, 53, 55, 43, 54, 46, 40, 41, 46, 41, 45, 37, 50, 38, 47, 48, 39, 40, 47, 39, 38, 39, 48, 37, 38, 48, 46, 47, 40, 50, 72, 51, 73, 14, 13, 71, 72, 50, 72, 73, 13, 61, 72, 71, 71, 59, 60, 61, 71, 60, 74, 14, 73, 62, 73, 72, 62, 72, 61, 50, 70, 71, 49, 50, 37, 70, 50, 49, 70, 59, 71, 56, 43, 55, 44, 43, 56, 45, 44, 56, 63, 73, 62, 74, 73, 63, 69, 60, 59, 15, 14, 74, 75, 15, 74, 75, 74, 63, 79, 70, 49, 69, 59, 70, 79, 69, 70, 68, 60, 69, 64, 75, 63, 67, 61, 60, 67, 60, 68, 65, 63, 62, 64, 63, 65, 58, 48, 47, 37, 48, 58, 49, 37, 58, 66, 62, 61, 66, 61, 67, 65, 62, 66, 57, 46, 45, 57, 45, 56, 47, 46, 57, 58, 47, 57, 76, 64, 65, 75, 64, 76, 16, 75, 76, 15, 75, 16, 3, 2, 5, 3, 5, 4, 54, 5, 2, 55, 54, 2, 77, 66, 67, 65, 66, 77, 76, 65, 77, 79, 78, 68, 79, 68, 69, 67, 68, 78, 77, 67, 78, 17, 77, 78, 16, 76, 77, 17, 16, 77, 18, 58, 57, 78, 79, 49, 49, 58, 78, 58, 17, 78, 18, 17, 58, 20, 57, 56, 19, 18, 57, 20, 19, 57, 56, 2, 20, 2, 56, 55, 21, 20, 2, 0, 21, 2, 0, 2, 1, 0, 22, 21, 33, 35, 34, 33, 32, 36, 33, 36, 35, 27, 26, 25, 24, 27, 25, 29, 28, 27, 32, 0, 36, 32, 29, 24, 29, 27, 24, 23, 32, 24, 32, 22, 0, 32, 23, 22, 30, 29, 32, 30, 32, 31], "vertices": [2, 109, 155.08, 4.04, 0.28989, 110, 5.16, -19.16, 0.71011, 2, 109, 158.69, 7.25, 0.15781, 110, 9.91, -18.29, 0.84219, 2, 109, 158.84, 12.12, 0.06645, 110, 12.36, -14.32, 0.93355, 1, 110, 29.55, -24.26, 1, 1, 110, 35.33, -25.22, 1, 1, 110, 41.06, -23.17, 1, 1, 110, 45.99, -30.19, 1, 1, 110, 79.66, -49.06, 1, 1, 110, 98.53, -35.15, 1, 1, 110, 115.67, 3.42, 1, 1, 110, 106.5, 34.61, 1, 1, 110, 91.69, 54.02, 1, 1, 110, 54.75, 41.75, 1, 1, 110, 49.04, 37.17, 1, 1, 110, 43.56, 36.98, 1, 1, 110, 39.73, 36.32, 1, 1, 110, 36.45, 35.69, 1, 1, 110, 21.98, 35.22, 1, 2, 109, 127.45, 48.42, 0.02436, 110, 1.49, 31.43, 0.97564, 2, 109, 126.04, 45.24, 0.03543, 110, -1.28, 29.48, 0.96457, 2, 109, 127.9, 36.78, 0.15787, 110, -3.63, 21.53, 0.84213, 2, 109, 126.29, 35.76, 0.25409, 110, -5.56, 21.48, 0.74591, 2, 109, 122.71, 35.68, 0.42196, 110, -8.8, 23.19, 0.57804, 2, 109, 84.5, 65.89, 0.95491, 110, -28.67, 67.26, 0.04509, 2, 109, 72.21, 70.33, 0.97671, 110, -37.57, 77.06, 0.02329, 1, 109, 11.18, 78, 1, 1, 109, 5.66, 76.72, 1, 1, 109, -3.15, 67.82, 1, 1, 109, -20.59, 66.16, 1, 1, 109, -19.86, 58.42, 1, 1, 109, 5.38, -42.05, 1, 1, 109, 23.97, -39.53, 1, 1, 109, 28.11, -36.11, 1, 1, 109, 70.31, -59.34, 1, 1, 109, 119.92, -63.97, 1, 2, 109, 149.01, -45.58, 0.99313, 110, -23.85, -57.37, 0.00687, 2, 109, 154.52, -21.87, 0.87892, 110, -7.65, -40.41, 0.12108, 1, 110, 36.6, 20.15, 1, 1, 110, 39.24, 18.69, 1, 1, 110, 40.25, 16.65, 1, 2, 109, 169.07, 53.93, 0.00013, 110, 40.26, 13.43, 0.99987, 2, 109, 170.23, 50.77, 0.0015, 110, 39.74, 10.11, 0.9985, 2, 109, 171.01, 48.09, 0.00307, 110, 39.11, 7.39, 0.99693, 2, 109, 171.76, 45.21, 0.00492, 110, 38.38, 4.5, 0.99508, 2, 109, 168.93, 47.17, 0.0111, 110, 36.86, 7.59, 0.9889, 2, 109, 166.45, 48.65, 0.01027, 110, 35.4, 10.09, 0.98973, 2, 109, 164, 51.22, 0.00805, 110, 34.51, 13.52, 0.99195, 2, 109, 162.88, 53.31, 0.00588, 110, 34.53, 15.89, 0.99412, 2, 109, 162.72, 56.16, 0.00232, 110, 35.78, 18.47, 0.99768, 1, 110, 36.04, 23.78, 1, 1, 110, 40.06, 22.24, 1, 1, 110, 42.29, 17.62, 1, 2, 109, 173.35, 51.3, 0.00188, 110, 42.72, 9.06, 0.99812, 2, 109, 175.28, 43.83, 0.004, 110, 40.78, 1.59, 0.996, 2, 109, 172.26, 39.06, 0.0105, 110, 35.83, -1.12, 0.9895, 2, 109, 166.18, 39.33, 0.03999, 110, 30.65, 2.07, 0.96001, 2, 109, 161.54, 42.29, 0.05052, 110, 28.02, 6.91, 0.94948, 2, 109, 157.5, 48.07, 0.02872, 110, 27.3, 13.92, 0.97128, 2, 109, 157.85, 54.43, 0.01426, 110, 30.68, 19.32, 0.98574, 1, 110, 37.6, 27.73, 1, 1, 110, 38.87, 28.87, 1, 1, 110, 39.5, 30.08, 1, 1, 110, 39.68, 31.83, 1, 1, 110, 39.42, 33.31, 1, 1, 110, 38.04, 34.23, 1, 1, 110, 37.03, 33.48, 1, 1, 110, 36.35, 32.2, 1, 1, 110, 36.12, 30.85, 1, 1, 110, 36.24, 29.56, 1, 1, 110, 36.55, 28.28, 1, 1, 110, 37.59, 27.24, 1, 1, 110, 39.73, 28.62, 1, 1, 110, 41.17, 30.96, 1, 1, 110, 41.54, 32.78, 1, 1, 110, 41.15, 34.53, 1, 1, 110, 39.31, 35.52, 1, 1, 110, 36.33, 34.6, 1, 1, 110, 33.06, 33.27, 1, 1, 110, 31.92, 30.71, 1, 1, 110, 35.83, 27.98, 1], "hull": 37, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 0, 72, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 74, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 98, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 118, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 140, 158, 158, 156], "width": 167, "height": 265}}, "duongtank15": {"duongtank12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 139, -300.16, -33.51, 1, 1, 139, -288.02, 72.44, 1, 1, 139, 279.3, 35.86, 1, 1, 139, 267.16, -70.09, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 103, "height": 576}}, "duongtank2": {"duongtank2": {"type": "mesh", "uvs": [0.10931, 0.43673, 0.11893, 0.493, 0.14641, 0.42969, 0.24669, 0.32417, 0.29202, 0.29604, 0.43764, 0.10611, 0.51869, 0.07562, 0.66018, 0.06624, 0.72063, 0.04749, 0.79893, 0, 0.97339, 0, 1, 0.0428, 0.9885, 0.14597, 0.87723, 0.24914, 0.79618, 0.27493, 0.73436, 0.28431, 0.66842, 0.357, 0.64095, 0.47893, 0.65606, 0.56335, 0.69453, 0.63838, 0.73162, 0.67824, 0.77558, 0.73452, 0.78794, 0.80017, 0.77695, 0.88459, 0.71238, 0.9221, 0.66568, 0.90335, 0.62859, 0.85879, 0.59149, 0.8119, 0.55853, 0.79783, 0.54067, 0.84004, 0.51044, 0.99948, 0.25905, 1, 0.0681, 0.87521, 0, 0.58914, 0, 0.2468, 0.04612, 0.21397, 0.09832, 0.27728], "triangles": [12, 10, 11, 12, 13, 9, 12, 9, 10, 8, 9, 13, 14, 8, 13, 15, 8, 14, 7, 8, 15, 15, 6, 7, 16, 6, 15, 16, 5, 6, 17, 4, 16, 3, 17, 18, 28, 18, 19, 27, 28, 19, 28, 1, 18, 20, 27, 19, 27, 20, 21, 26, 27, 21, 22, 26, 21, 22, 25, 26, 23, 25, 22, 24, 25, 23, 16, 4, 5, 34, 0, 33, 36, 34, 35, 0, 34, 36, 33, 0, 1, 17, 3, 4, 18, 2, 3, 18, 1, 2, 29, 1, 28, 32, 33, 1, 32, 1, 29, 29, 31, 32, 30, 31, 29], "vertices": [1, 118, -5.86, -22.07, 1, 1, 118, -2.59, -22.76, 1, 1, 118, -4.32, -18.52, 1, 1, 118, -4.44, -6.55, 1, 2, 118, -3.53, -1.67, 0.99837, 120, -32.72, -2.98, 0.00163, 3, 118, -5.52, 16.73, 0.45258, 119, -29.81, 15.93, 0.00055, 120, -16.53, 6, 0.54687, 3, 118, -2.91, 24.89, 0.17857, 119, -23.96, 22.18, 0.00016, 120, -8, 6.77, 0.82127, 2, 120, 6.62, 5.64, 0.99245, 121, -6.45, 6.23, 0.00755, 2, 120, 12.95, 5.99, 0.51639, 121, -0.11, 6.02, 0.48361, 1, 121, 8.37, 7.03, 1, 1, 121, 26.07, 3.42, 1, 1, 121, 28.28, 0.49, 1, 1, 121, 25.95, -4.99, 1, 1, 121, 13.49, -8.41, 1, 3, 119, 6.01, 29.58, 0.00049, 120, 19.27, -7.69, 0.09882, 121, 4.98, -8.16, 0.90069, 3, 119, 1.09, 25.45, 0.02044, 120, 12.85, -7.5, 0.6436, 121, -1.4, -7.4, 0.33596, 4, 118, 18.57, 30.6, 0.00738, 119, -2.1, 18.14, 0.19911, 120, 5.6, -10.81, 0.7892, 121, -8.91, -10.07, 0.00432, 3, 118, 23.19, 24.74, 0.02023, 119, -0.44, 10.87, 0.63543, 120, 2, -17.35, 0.34434, 2, 119, 3.6, 7.87, 0.90655, 120, 3.01, -22.27, 0.09345, 2, 119, 9.31, 6.7, 0.9928, 120, 6.49, -26.94, 0.0072, 2, 119, 13.74, 7.08, 0.99999, 120, 10.05, -29.62, 1e-05, 1, 119, 19.3, 7.11, 1, 1, 119, 22.49, 4.82, 1, 1, 119, 24.32, 0.26, 1, 1, 119, 20.1, -5.34, 1, 2, 118, 45.39, 15.2, 0, 119, 15.54, -7.26, 1, 2, 118, 41.31, 13.09, 0.00972, 119, 10.94, -7.42, 0.99028, 2, 118, 37.12, 11.04, 0.09194, 119, 6.28, -7.48, 0.90806, 2, 118, 34.75, 8.46, 0.31913, 119, 3.03, -8.8, 0.68087, 2, 118, 35.93, 5.68, 0.56847, 119, 2.9, -11.82, 0.43153, 2, 118, 42.26, -1.47, 0.83443, 119, 5.56, -20.99, 0.16557, 1, 118, 29.53, -24.18, 1, 1, 118, 13.69, -37.95, 1, 1, 118, -3.88, -36.16, 1, 1, 118, -20.77, -26.66, 1, 1, 118, -20.05, -21.59, 1, 1, 118, -14.28, -18.64, 1], "hull": 37, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 0, 72], "width": 99, "height": 58}}, "duongtank3": {"duongtank3": {"type": "mesh", "uvs": [0.6754, 0, 0.72678, 0.04621, 0.74391, 0.07997, 0.79301, 0.13784, 0.80786, 0.22584, 0.82156, 0.24995, 0.90721, 0.33675, 0.93004, 0.39824, 0.95517, 0.432, 0.986, 0.46334, 0.98942, 0.53688, 0.9723, 0.60801, 0.86952, 0.75268, 0.79301, 0.81537, 0.66969, 0.88529, 0.57948, 0.95763, 0.46529, 1, 0.43788, 1, 0.37622, 0.96606, 0.29742, 0.96245, 0.23234, 0.92749, 0.12614, 0.93834, 0.04963, 0.92146, 0, 0.99741, 0, 0.91543, 0.04278, 0.82983, 0.11814, 0.73218, 0.2369, 0.66347, 0.38078, 0.67552, 0.471, 0.61163, 0.5555, 0.53929, 0.61031, 0.44887, 0.61602, 0.42356, 0.60117, 0.33555, 0.59432, 0.20896, 0.58062, 0.08841, 0.61602, 0], "triangles": [25, 21, 22, 24, 25, 22, 20, 26, 27, 20, 27, 28, 26, 21, 25, 26, 20, 21, 19, 20, 28, 23, 24, 22, 15, 28, 14, 28, 18, 19, 15, 18, 28, 16, 17, 18, 15, 16, 18, 14, 30, 13, 29, 30, 14, 14, 28, 29, 30, 31, 12, 13, 30, 12, 10, 11, 8, 10, 8, 9, 32, 6, 7, 6, 32, 5, 32, 8, 11, 8, 32, 7, 12, 31, 11, 32, 11, 31, 35, 2, 34, 33, 34, 4, 1, 2, 35, 3, 34, 2, 4, 34, 3, 5, 32, 33, 5, 33, 4, 35, 36, 0, 0, 1, 35], "vertices": [1, 111, 0.23, 14.13, 1, 2, 111, 20.92, 16.25, 0.31677, 112, -2.37, 17.71, 0.68323, 2, 111, 31.41, 13.29, 0.03337, 112, 8.09, 20.77, 0.96663, 2, 112, 27.49, 32.11, 0.9926, 113, -30.54, 46.46, 0.0074, 2, 112, 52.55, 30.93, 0.77868, 113, -7.63, 36.21, 0.22132, 2, 112, 60.14, 33.57, 0.57227, 113, 0.39, 35.91, 0.42773, 2, 112, 90.12, 54.28, 0.04544, 113, 35.86, 44.24, 0.95456, 2, 112, 108.55, 57.29, 0.00353, 113, 54.13, 40.32, 0.99647, 1, 113, 66.43, 41.44, 1, 2, 113, 79.23, 44.42, 0.99951, 114, -63.11, 64.49, 0.00049, 3, 113, 96.74, 33.52, 0.97335, 114, -47.44, 77.91, 0.02639, 115, -113.68, 34.55, 0.00026, 3, 113, 109.97, 17.67, 0.88619, 114, -28.34, 85.75, 0.10627, 115, -99.44, 49.51, 0.00754, 4, 113, 124.56, -32.22, 0.41754, 114, 23.63, 84.65, 0.41722, 115, -51.53, 69.7, 0.16447, 116, -115.1, 51.05, 0.00077, 4, 113, 125.09, -62.15, 0.20431, 114, 52.33, 76.14, 0.39854, 115, -21.86, 73.63, 0.37428, 116, -86.51, 59.92, 0.02287, 4, 113, 118.79, -105.41, 0.03486, 114, 91.68, 57.11, 0.12815, 115, 21.83, 72.3, 0.60895, 116, -43.22, 65.97, 0.22804, 5, 113, 119.04, -140.44, 0.0022, 114, 125.17, 46.81, 0.01598, 115, 56.61, 76.55, 0.42035, 116, -9.66, 76.01, 0.54794, 117, -68.59, 73.68, 0.01353, 4, 114, 156.64, 25.37, 1e-05, 115, 94.09, 69.81, 0.16814, 116, 28.42, 75.67, 0.72816, 117, -30.52, 74.66, 0.10369, 3, 115, 101.73, 65.68, 0.13889, 116, 36.65, 72.89, 0.73051, 117, -22.2, 72.17, 0.1306, 3, 115, 114.41, 48.04, 0.05366, 116, 52.11, 57.63, 0.6411, 117, -6.22, 57.45, 0.30524, 3, 115, 135.9, 35.29, 0.00443, 116, 75.44, 48.68, 0.3506, 117, 17.41, 49.31, 0.64498, 2, 116, 91.85, 32.8, 0.091, 117, 34.35, 34.01, 0.909, 1, 117, 67.45, 27.26, 1, 1, 117, 89.32, 15.77, 1, 1, 117, 110.49, 31.64, 1, 1, 117, 103.9, 9.64, 1, 1, 117, 84.03, -9.44, 1, 1, 117, 53.3, -28.78, 1, 2, 116, 66.8, -36.8, 0.18893, 117, 11.73, -36.41, 0.81107, 3, 115, 74.47, -22.89, 0.0243, 116, 24.69, -19, 0.96463, 117, -30.97, -20.09, 0.01107, 3, 114, 69.31, -39.53, 0.01455, 115, 40.82, -25.06, 0.82169, 116, -8.12, -26.81, 0.16376, 3, 112, 119.24, -67.33, 0.00011, 114, 36.92, -30.66, 0.5727, 115, 7.63, -30.17, 0.42719, 4, 112, 98.68, -44.53, 0.26211, 113, 7.77, -50.87, 0.52185, 114, 6.26, -32.34, 0.17025, 115, -19.68, -44.21, 0.04579, 4, 112, 92.2, -41.11, 0.31703, 113, 2.99, -45.33, 0.49872, 114, -0.46, -35.23, 0.18202, 115, -24.64, -49.59, 0.00223, 3, 112, 67.13, -39.93, 0.62419, 113, -19.91, -35.08, 0.1919, 114, -17.13, -53.98, 0.18391, 4, 111, 22.78, -45.68, 0.03074, 112, 32.15, -33.75, 0.96165, 113, -50.23, -16.57, 0.00091, 114, -43.92, -77.32, 0.0067, 2, 111, -3.93, -24.57, 0.56258, 112, -1.7, -30.09, 0.43742, 1, 111, -13.22, 0.97, 1], "hull": 37, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 0, 72], "width": 303, "height": 287}}, "duongtank4": {"duongtank4": {"type": "mesh", "uvs": [0.5239, 0, 0.3789, 0.04273, 0.5839, 0.13671, 0.7239, 0.21559, 0.7689, 0.35657, 0.7289, 0.50762, 0.6539, 0.68888, 0.3939, 0.85, 0, 1, 0.2539, 1, 0.6239, 0.87182, 0.8639, 0.76105, 1, 0.51433, 1, 0.35154, 0.9389, 0.20384, 0.7489, 0.07797], "triangles": [10, 7, 6, 9, 8, 7, 9, 7, 10, 5, 4, 12, 11, 5, 12, 6, 5, 11, 10, 6, 11, 4, 14, 13, 4, 13, 12, 3, 15, 14, 4, 3, 14, 2, 0, 15, 3, 2, 15, 1, 0, 2], "vertices": [1, 122, -1.41, 4.27, 1, 1, 122, -1.87, -4.98, 1, 1, 123, 10.93, -5.05, 1, 2, 123, 24.05, -5.29, 0.05596, 124, 5.16, -3.78, 0.94404, 2, 124, 25.32, -5.98, 0.2633, 125, 2.61, -5.54, 0.7367, 2, 125, 24.23, -7.23, 0.26401, 126, 2.56, -6.93, 0.73599, 2, 126, 28.72, -6.47, 0.96722, 127, -5.59, -11.02, 0.03278, 1, 127, 20.04, -5.57, 1, 1, 127, 48.63, -6.04, 1, 1, 127, 40.72, 3.23, 1, 1, 127, 15.25, 4.86, 1, 2, 126, 37.35, 5.09, 0.94751, 127, -4.28, 3.35, 0.05249, 2, 125, 25.05, 5.8, 0.20032, 126, 1.49, 6.07, 0.79968, 2, 124, 26.99, 5.01, 0.21244, 125, 1.77, 5.55, 0.78756, 1, 124, 5.73, 6.66, 1, 1, 123, 7.96, 6.1, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 48, "height": 143}}, "duongtank5": {"duongtank5": {"type": "mesh", "uvs": [0.96118, 0, 0.8272, 0.01155, 0.54708, 0.13944, 0.27913, 0.28559, 0.09035, 0.43327, 0, 0.58247, 0, 0.69056, 0.15124, 0.78799, 0.42528, 0.87477, 0.77849, 1, 0.84368, 1, 0.82809, 0.86144, 0.43056, 0.78934, 0.31753, 0.70457, 0.45784, 0.5818, 0.47343, 0.47365, 0.47343, 0.30216, 0.73845, 0.17062, 0.98789, 0.08975, 1, 0], "triangles": [9, 11, 10, 9, 8, 11, 7, 13, 8, 8, 12, 11, 8, 13, 12, 13, 7, 5, 6, 5, 7, 4, 14, 13, 4, 13, 5, 14, 4, 15, 15, 4, 3, 3, 16, 15, 16, 3, 2, 16, 2, 17, 17, 2, 1, 18, 1, 0, 18, 17, 1, 18, 0, 19], "vertices": [1, 152, -7.1, -0.76, 1, 1, 152, 1.3, -8.69, 1, 2, 152, 47.54, -11.26, 0.00299, 153, 13.55, -10.14, 0.99701, 1, 154, 13.97, -8.54, 1, 2, 154, 62.55, -9.17, 0.00651, 155, 8.66, -11.08, 0.99349, 2, 155, 55.41, -13.28, 0.83787, 156, -7.68, -11.42, 0.16213, 1, 156, 24.77, -18.66, 1, 1, 156, 56.11, -13.15, 1, 1, 156, 85.96, 2.86, 1, 1, 156, 128.45, 22.59, 1, 1, 156, 129.36, 27.79, 1, 1, 156, 87.55, 35.84, 1, 1, 156, 60.39, 9.01, 1, 1, 156, 33.38, 5.69, 1, 2, 155, 49.43, 23.26, 0.7541, 156, -1.52, 25.1, 0.2459, 2, 154, 63.68, 23.63, 0.03518, 155, 16.17, 20.87, 0.96482, 1, 154, 13.39, 7.74, 1, 1, 153, 13.2, 7.67, 1, 1, 152, 16.65, 13.26, 1, 1, 152, -8.61, 1.99, 1], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38], "width": 78, "height": 312}}, "duongtank6": {"duongtank6": {"type": "mesh", "uvs": [0.61092, 0.34648, 0.59457, 0.33142, 0.56691, 0.32904, 0.57949, 0.25455, 0.57068, 0.23316, 0.54553, 0.21651, 0.57194, 0.18878, 0.59332, 0.04376, 0.47134, 0, 0.21859, 0, 0.0702, 0.07942, 0, 0.16025, 0.15697, 0.26882, 0.19595, 0.2815, 0.2103, 0.30009, 0.22322, 0.31231, 0.23465, 0.32264, 0.27242, 0.37182, 0.343, 0.43665, 0.36039, 0.44323, 0.4096, 0.43916, 0.41457, 0.44573, 0.41307, 0.45952, 0.22024, 0.59557, 0.18831, 0.64127, 0.11162, 0.87338, 0.11593, 0.89512, 0.16192, 0.93225, 0.16192, 1, 0.20647, 1, 0.79281, 0.9395, 0.7885, 0.86704, 0.77126, 0.84984, 0.92647, 0.69587, 0.97964, 0.50659, 0.89054, 0.38795, 0.75832, 0.35806, 0.31932, 0.29831, 0.32087, 0.28697, 0.32961, 0.28036, 0.34717, 0.27539, 0.36661, 0.2721, 0.38297, 0.27008, 0.40052, 0.26819, 0.38719, 0.2782, 0.37707, 0.28706, 0.36054, 0.29537, 0.34753, 0.29892, 0.33046, 0.29856, 0.30084, 0.3058, 0.29948, 0.28958, 0.31933, 0.27481, 0.36506, 0.26021, 0.4106, 0.25544, 0.43711, 0.26839, 0.43206, 0.29124, 0.41186, 0.30772, 0.37568, 0.3209, 0.33801, 0.31748, 0.27538, 0.30646, 0.26608, 0.30386, 0.25792, 0.30352, 0.24792, 0.30558, 0.24048, 0.30873, 0.23878, 0.31491, 0.24534, 0.31725, 0.254, 0.31762, 0.26194, 0.31637, 0.26872, 0.31395, 0.27495, 0.31095, 0.27812, 0.30576, 0.26535, 0.30051, 0.24906, 0.2991, 0.23818, 0.30062, 0.22958, 0.30463, 0.22865, 0.31251, 0.24089, 0.32138, 0.25613, 0.3306, 0.27292, 0.33061, 0.2784, 0.31296], "triangles": [6, 8, 7, 5, 8, 6, 8, 52, 9, 53, 8, 5, 53, 52, 8, 53, 42, 52, 54, 53, 5, 12, 10, 9, 9, 13, 12, 11, 10, 12, 43, 42, 53, 41, 52, 42, 52, 51, 9, 40, 51, 52, 41, 40, 52, 44, 42, 43, 39, 51, 40, 51, 13, 9, 38, 51, 39, 45, 41, 42, 45, 42, 44, 51, 72, 13, 50, 51, 38, 54, 43, 53, 55, 43, 54, 46, 40, 41, 46, 41, 45, 37, 50, 38, 47, 48, 39, 40, 47, 39, 38, 39, 48, 37, 38, 48, 46, 47, 40, 50, 72, 51, 73, 14, 13, 71, 72, 50, 72, 73, 13, 61, 72, 71, 71, 59, 60, 61, 71, 60, 74, 14, 73, 62, 73, 72, 62, 72, 61, 50, 70, 71, 49, 50, 37, 70, 50, 49, 70, 59, 71, 56, 43, 55, 44, 43, 56, 45, 44, 56, 63, 73, 62, 74, 73, 63, 69, 60, 59, 15, 14, 74, 75, 15, 74, 75, 74, 63, 79, 70, 49, 69, 59, 70, 79, 69, 70, 68, 60, 69, 64, 75, 63, 67, 61, 60, 67, 60, 68, 65, 63, 62, 64, 63, 65, 58, 48, 47, 37, 48, 58, 49, 37, 58, 66, 62, 61, 66, 61, 67, 65, 62, 66, 57, 46, 45, 57, 45, 56, 47, 46, 57, 58, 47, 57, 76, 64, 65, 75, 64, 76, 16, 75, 76, 15, 75, 16, 3, 2, 5, 3, 5, 4, 54, 5, 2, 55, 54, 2, 77, 66, 67, 65, 66, 77, 76, 65, 77, 79, 78, 68, 79, 68, 69, 67, 68, 78, 77, 67, 78, 17, 77, 78, 16, 76, 77, 17, 16, 77, 18, 58, 57, 78, 79, 49, 49, 58, 78, 58, 17, 78, 18, 17, 58, 20, 57, 56, 19, 18, 57, 20, 19, 57, 56, 2, 20, 2, 56, 55, 21, 20, 2, 0, 21, 2, 0, 2, 1, 0, 22, 21, 33, 35, 34, 33, 32, 36, 33, 36, 35, 27, 26, 25, 24, 27, 25, 29, 28, 27, 32, 0, 36, 32, 29, 24, 29, 27, 24, 23, 32, 24, 32, 22, 0, 32, 23, 22, 30, 29, 32, 30, 32, 31], "vertices": [2, 109, 155.08, 4.04, 0.28989, 110, 5.16, -19.16, 0.71011, 2, 109, 158.69, 7.25, 0.15781, 110, 9.91, -18.29, 0.84219, 2, 109, 158.84, 12.12, 0.06645, 110, 12.36, -14.32, 0.93355, 1, 110, 29.55, -24.26, 1, 1, 110, 35.33, -25.22, 1, 1, 110, 41.06, -23.17, 1, 1, 110, 45.99, -30.19, 1, 1, 110, 79.66, -49.06, 1, 1, 110, 98.53, -35.15, 1, 1, 110, 115.67, 3.42, 1, 1, 110, 106.5, 34.61, 1, 1, 110, 91.69, 54.02, 1, 1, 110, 54.75, 41.75, 1, 1, 110, 49.04, 37.17, 1, 1, 110, 43.56, 36.98, 1, 1, 110, 39.73, 36.32, 1, 1, 110, 36.45, 35.69, 1, 1, 110, 21.98, 35.22, 1, 2, 109, 127.45, 48.42, 0.02436, 110, 1.49, 31.43, 0.97564, 2, 109, 126.04, 45.24, 0.03543, 110, -1.28, 29.48, 0.96457, 2, 109, 127.9, 36.78, 0.15787, 110, -3.63, 21.53, 0.84213, 2, 109, 126.29, 35.76, 0.25409, 110, -5.56, 21.48, 0.74591, 2, 109, 122.71, 35.68, 0.42196, 110, -8.8, 23.19, 0.57804, 2, 109, 84.5, 65.89, 0.95491, 110, -28.67, 67.26, 0.04509, 2, 109, 72.21, 70.33, 0.97671, 110, -37.57, 77.06, 0.02329, 1, 109, 11.18, 78, 1, 1, 109, 5.66, 76.72, 1, 1, 109, -3.15, 67.82, 1, 1, 109, -20.59, 66.16, 1, 1, 109, -19.86, 58.42, 1, 1, 109, 5.38, -42.05, 1, 1, 109, 23.97, -39.53, 1, 1, 109, 28.11, -36.11, 1, 1, 109, 70.31, -59.34, 1, 1, 109, 119.92, -63.97, 1, 2, 109, 149.01, -45.58, 0.99313, 110, -23.85, -57.37, 0.00687, 2, 109, 154.52, -21.87, 0.87892, 110, -7.65, -40.41, 0.12108, 1, 110, 36.6, 20.15, 1, 1, 110, 39.24, 18.69, 1, 1, 110, 40.25, 16.65, 1, 2, 109, 169.07, 53.93, 0.00013, 110, 40.26, 13.43, 0.99987, 2, 109, 170.23, 50.77, 0.0015, 110, 39.74, 10.11, 0.9985, 2, 109, 171.01, 48.09, 0.00307, 110, 39.11, 7.39, 0.99693, 2, 109, 171.76, 45.21, 0.00492, 110, 38.38, 4.5, 0.99508, 2, 109, 168.93, 47.17, 0.0111, 110, 36.86, 7.59, 0.9889, 2, 109, 166.45, 48.65, 0.01027, 110, 35.4, 10.09, 0.98973, 2, 109, 164, 51.22, 0.00805, 110, 34.51, 13.52, 0.99195, 2, 109, 162.88, 53.31, 0.00588, 110, 34.53, 15.89, 0.99412, 2, 109, 162.72, 56.16, 0.00232, 110, 35.78, 18.47, 0.99768, 1, 110, 36.04, 23.78, 1, 1, 110, 40.06, 22.24, 1, 1, 110, 42.29, 17.62, 1, 2, 109, 173.35, 51.3, 0.00188, 110, 42.72, 9.06, 0.99812, 2, 109, 175.28, 43.83, 0.004, 110, 40.78, 1.59, 0.996, 2, 109, 172.26, 39.06, 0.0105, 110, 35.83, -1.12, 0.9895, 2, 109, 166.18, 39.33, 0.03999, 110, 30.65, 2.07, 0.96001, 2, 109, 161.54, 42.29, 0.05052, 110, 28.02, 6.91, 0.94948, 2, 109, 157.5, 48.07, 0.02872, 110, 27.3, 13.92, 0.97128, 2, 109, 157.85, 54.43, 0.01426, 110, 30.68, 19.32, 0.98574, 1, 110, 37.6, 27.73, 1, 1, 110, 38.87, 28.87, 1, 1, 110, 39.5, 30.08, 1, 1, 110, 39.68, 31.83, 1, 1, 110, 39.42, 33.31, 1, 1, 110, 38.04, 34.23, 1, 1, 110, 37.03, 33.48, 1, 1, 110, 36.35, 32.2, 1, 1, 110, 36.12, 30.85, 1, 1, 110, 36.24, 29.56, 1, 1, 110, 36.55, 28.28, 1, 1, 110, 37.59, 27.24, 1, 1, 110, 39.73, 28.62, 1, 1, 110, 41.17, 30.96, 1, 1, 110, 41.54, 32.78, 1, 1, 110, 41.15, 34.53, 1, 1, 110, 39.31, 35.52, 1, 1, 110, 36.33, 34.6, 1, 1, 110, 33.06, 33.27, 1, 1, 110, 31.92, 30.71, 1, 1, 110, 35.83, 27.98, 1], "hull": 37, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 0, 72, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 74, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 98, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 118, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 140, 158, 158, 156], "width": 167, "height": 265}}, "duongtank7": {"duongtank7": {"type": "mesh", "uvs": [0.73816, 0, 0.59633, 0.02711, 0.39648, 0.25219, 0.19018, 0.54653, 0, 0.84088, 0, 0.9664, 0.19018, 1, 0.32557, 0.9361, 0.52542, 0.62012, 0.73816, 0.33443, 0.89933, 0.11368, 0.87354, 0.02711], "triangles": [4, 3, 7, 6, 5, 4, 7, 6, 4, 3, 2, 8, 7, 3, 8, 10, 9, 0, 1, 0, 9, 10, 0, 11, 2, 1, 9, 8, 2, 9], "vertices": [2, 162, 11.56, -3, 0.34029, 163, -0.84, -2.94, 0.65971, 1, 163, 4.62, -7.66, 1, 2, 163, 22.86, -7.13, 0.94292, 164, -3.84, -6.9, 0.05708, 2, 164, 18.63, -5.39, 0.99998, 165, -6.16, -3.49, 2e-05, 1, 165, 15.31, -9.03, 1, 1, 165, 23.75, -7.47, 1, 1, 165, 24.31, 2.14, 1, 2, 164, 38.15, 13.88, 0.00078, 165, 18.8, 7.89, 0.99922, 3, 163, 40.07, 12.25, 0.00241, 164, 14.57, 11.35, 0.89377, 165, -4.21, 13.63, 0.10383, 2, 163, 18.06, 9.92, 0.94519, 164, -7.55, 10.42, 0.05481, 2, 162, 12.46, 8.05, 0.17323, 163, 1.13, 7.98, 0.82677, 2, 162, 8.72, 3.29, 0.66719, 163, -3.05, 3.61, 0.33281], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22], "width": 47, "height": 70}}, "duongtank8": {"duongtank8": {"type": "mesh", "uvs": [0.92911, 0, 1, 0.01555, 0.98911, 0.16255, 0.93456, 0.37255, 0.8582, 0.54355, 0.68729, 0.75654, 0.49456, 0.90054, 0.33275, 0.98754, 0.11638, 0.94854, 0, 0.80755, 0, 0.74155, 0.12366, 0.79854, 0.32366, 0.81354, 0.47275, 0.73854, 0.64002, 0.57955, 0.77456, 0.44155, 0.84911, 0.29455, 0.90184, 0.09955], "triangles": [9, 10, 11, 8, 9, 11, 8, 11, 12, 7, 12, 6, 8, 12, 7, 6, 13, 5, 12, 13, 6, 5, 14, 4, 13, 14, 5, 15, 16, 3, 4, 15, 3, 14, 15, 4, 3, 16, 2, 1, 17, 0, 2, 17, 1, 16, 17, 2], "vertices": [1, 128, -4.5, -1.26, 1, 1, 128, -5.21, 8.16, 1, 2, 128, 6.59, 9.17, 0.90978, 129, -6.26, 8.51, 0.09022, 1, 129, 11.95, 7, 1, 3, 129, 28.12, 1.76, 0.07679, 130, 12.34, 8.36, 0.92212, 131, -9.57, 6.16, 0.00109, 2, 131, 18.61, 8.48, 0.98786, 132, -7.93, 7.65, 0.01214, 1, 132, 19.99, 6.85, 1, 1, 133, 12.12, 8.65, 1, 1, 133, 40.29, 3.04, 1, 1, 133, 54.61, -9.54, 1, 1, 133, 54.15, -14.8, 1, 1, 133, 38.29, -8.83, 1, 1, 133, 12.09, -5.32, 1, 2, 132, 16.89, -6.06, 0.99969, 133, -8.04, -9.57, 0.00031, 2, 131, 16.3, -6.82, 0.99694, 132, -8.53, -7.81, 0.00306, 2, 130, 13.94, -5.27, 0.95981, 131, -4.61, -6.64, 0.04019, 2, 129, 9.6, -5.68, 0.37558, 130, -1.37, -6.14, 0.62442, 2, 128, 4.03, -3.14, 0.98522, 129, -7.4, -4.01, 0.01478], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34], "width": 132, "height": 80}}, "duongtank9": {"duongtank9": {"type": "mesh", "uvs": [0.7197, 0, 0.75557, 0.09499, 0.78053, 0.19167, 0.80314, 0.32004, 0.83278, 0.4484, 0.86553, 0.55617, 0.9248, 0.72733, 1, 0.86837, 1, 0.90007, 0.89517, 0.96663, 0.80782, 0.9809, 0.75635, 0.90324, 0.71424, 0.91592, 0.65809, 0.92067, 0.62144, 0.90166, 0.61208, 0.93494, 0.54111, 0.94128, 0.50602, 0.91909, 0.45221, 0.86045, 0.38904, 1, 0.35473, 1, 0.32587, 0.9286, 0.24555, 0.82083, 0.16756, 0.72416, 0.10049, 0.67661, 0.03577, 0.73208, 0, 0.67028, 0, 0.63858, 0.11141, 0.55617, 0.19174, 0.51813, 0.29546, 0.54191, 0.36876, 0.51655, 0.45533, 0.43097, 0.49354, 0.33747, 0.54969, 0.06805, 0.56139, 0], "triangles": [22, 30, 18, 22, 23, 30, 25, 26, 24, 24, 28, 23, 23, 29, 30, 23, 28, 29, 28, 24, 27, 24, 26, 27, 30, 31, 18, 18, 31, 32, 18, 19, 21, 19, 20, 21, 21, 22, 18, 18, 32, 14, 15, 16, 14, 14, 16, 17, 17, 18, 14, 32, 33, 4, 2, 33, 34, 10, 11, 9, 8, 9, 7, 12, 14, 11, 7, 9, 11, 6, 7, 11, 11, 14, 5, 6, 11, 5, 13, 14, 12, 5, 14, 32, 5, 32, 4, 33, 3, 4, 33, 2, 3, 2, 34, 1, 1, 34, 0, 34, 35, 0], "vertices": [3, 108, 34.23, -51.01, 0.81472, 140, -54.91, 51, 0.18314, 141, -92.24, 62.17, 0.00214, 4, 108, -3.43, -67.37, 0.47186, 140, -17, 66.73, 0.47246, 141, -52.75, 73.38, 0.05284, 142, -90.34, 77.89, 0.00284, 4, 108, -39.43, -75.94, 0.16401, 140, 19.15, 74.71, 0.57319, 141, -15.92, 77.1, 0.21779, 142, -53.37, 79.8, 0.04501, 5, 108, -85.15, -79.97, 0.01614, 140, 64.92, 78, 0.23812, 141, 29.93, 75.02, 0.42419, 142, -7.68, 75.48, 0.29991, 143, -44.57, 84.56, 0.02165, 5, 140, 112.18, 86.16, 0.02413, 141, 77.82, 77.61, 0.15993, 142, 40.28, 75.74, 0.53367, 143, 2.84, 77.33, 0.26549, 144, -58.21, 82.66, 0.01679, 5, 140, 153.51, 98.49, 4e-05, 141, 120.31, 85.04, 0.02182, 142, 83.08, 81.08, 0.25487, 143, 45.95, 75.94, 0.48868, 144, -15.36, 77.68, 0.23459, 3, 142, 153.4, 94.28, 0.01423, 143, 117.47, 78.03, 0.04831, 144, 56.08, 73.78, 0.93746, 1, 144, 127.31, 85.22, 1, 1, 144, 135.39, 78.4, 1, 1, 144, 103.27, 5.83, 1, 4, 143, 137.32, -40.29, 0.0002, 144, 65.99, -45.77, 0.99135, 147, 6.72, 252.2, 0.00255, 148, -70.04, 246.83, 0.00591, 5, 143, 94.54, -55.8, 0.14425, 144, 22.06, -57.66, 0.75424, 146, 79.1, 203.18, 0.00173, 147, 10.81, 206.88, 0.03413, 148, -61.82, 202.07, 0.06565, 5, 143, 80.28, -83.22, 0.30977, 144, 5.57, -83.79, 0.43947, 146, 98.75, 179.33, 0.00676, 147, 33.68, 186.1, 0.08197, 148, -37.15, 183.47, 0.16203, 6, 142, 125.08, -108.05, 0.00019, 143, 57.97, -117.42, 0.33148, 144, -19.52, -116.01, 0.22856, 146, 121.49, 145.41, 0.0113, 147, 61.06, 155.8, 0.1303, 148, -7.11, 155.8, 0.29816, 7, 142, 107.55, -129.09, 0.00101, 143, 37.38, -135.48, 0.27246, 144, -41.55, -132.29, 0.13187, 146, 130.05, 119.4, 0.00947, 147, 73.27, 131.29, 0.14704, 148, 7.29, 132.51, 0.43799, 149, -74.16, 118.19, 0.00016, 7, 142, 114.48, -140.12, 0.0003, 143, 42.51, -147.45, 0.24389, 144, -37.44, -144.65, 0.10689, 146, 143.08, 119.43, 0.00635, 147, 86.16, 133.19, 0.14255, 148, 19.95, 135.59, 0.49898, 149, -62.69, 124.35, 0.00105, 6, 143, 14.39, -190.74, 0.15351, 144, -69.07, -185.45, 0.05375, 146, 171.91, 76.61, 0.00035, 147, 120.85, 94.96, 0.09679, 148, 58, 100.7, 0.65068, 149, -17.07, 100.19, 0.04492, 5, 143, -6.4, -207.26, 0.10927, 144, -91.17, -200.17, 0.03545, 147, 131.52, 70.64, 0.06196, 148, 70.85, 77.45, 0.65912, 149, 1.23, 80.95, 0.13421, 5, 143, -44.99, -227.84, 0.02504, 144, -131.34, -217.45, 0.00736, 147, 141.57, 28.08, 0.00523, 148, 84.75, 35.99, 0.24939, 149, 25.15, 44.33, 0.71298, 1, 149, 88.27, 61.44, 1, 2, 149, 109.81, 48.87, 0.9946, 151, -46.15, 124.87, 0.0054, 3, 149, 115.92, 17.72, 0.92086, 150, 49.67, 103.9, 0.01371, 151, -25.18, 101.04, 0.06543, 3, 149, 148.22, -42.77, 0.27644, 150, 103.72, 61.7, 0.05026, 151, 33.2, 65.07, 0.6733, 2, 149, 180.92, -99.2, 0.00858, 151, 89.88, 32.8, 0.99142, 1, 151, 138.62, 16.93, 1, 1, 151, 185.67, 35.45, 1, 1, 151, 211.66, 14.82, 1, 1, 151, 211.66, 4.24, 1, 1, 151, 130.69, -23.27, 1, 1, 151, 72.31, -35.97, 1, 2, 150, 57.39, -26.82, 0.63443, 151, -3.07, -28.03, 0.36557, 5, 145, 147.17, -122.08, 0.00286, 147, 92.35, -92.06, 0.0328, 148, 46.75, -88.15, 0.00525, 149, 19.69, -85.38, 0.00259, 150, 3.5, -29.35, 0.9565, 6, 145, 97.21, -74.36, 0.16222, 146, 59.54, -65.77, 0.04093, 147, 30.1, -62.09, 0.4815, 148, -17.99, -64, 0.04293, 149, -49.04, -78.35, 0.04563, 150, -62.18, -50.8, 0.2268, 5, 145, 57.89, -60.22, 0.67284, 146, 18.4, -58.48, 0.09103, 147, -11.66, -60.78, 0.20374, 149, -88.76, -91.32, 0.00187, 150, -93.23, -78.75, 0.03052, 2, 108, 46.33, 74.04, 0.47237, 145, -40.76, -55.88, 0.52763, 2, 108, 65.84, 59.62, 0.60562, 145, -65.01, -56.46, 0.39438], "hull": 36, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 0, 70], "width": 695, "height": 342}}, "muccuoc0": {"muccuoc0": {"x": -2.65, "y": 16.33, "width": 114, "height": 30}, "muccuoc1": {"x": -2.15, "y": 16.33, "width": 93, "height": 30}, "muccuoc2": {"x": -2.15, "y": 16.33, "width": 57, "height": 28}, "muccuoc3": {"x": -2.65, "y": 34.29, "width": 192, "height": 47}}, "path": {"path": {"type": "clipping", "end": "path", "vertexCount": 12, "vertices": [-95.14, -221.26, -94.71, 224.89, -92.13, 229.52, -87.77, 231.85, 86.18, 231.99, 91.74, 229.94, 94.78, 223.7, 95.05, -220.59, 90.57, -226.74, 80.82, -231.73, -81.7, -231.83, -91.5, -228.02], "color": "ce3a3aff"}}, "satangfull1": {"satangfull1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 199, 317.26, 166.22, 1, 1, 199, 346.09, 101.34, 1, 1, 199, -152.85, -120.41, 1, 1, 199, -181.68, -55.53, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 71, "height": 546}}, "satangfull2": {"satangfull2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0, 0.3752, 0.51463, 0.38312, 0.49995, 0.395, 0.4894, 0.41579, 0.48298, 0.44847, 0.47977, 0.48213, 0.4816, 0.45144, 0.50225, 0.42123, 0.51418, 0.39897, 0.51692, 0.38318, 0.51685, 0.35342, 0.52151, 0.37382, 0.49848, 0.40005, 0.48472, 0.4372, 0.46639, 0.5049, 0.46096, 0.52718, 0.48435, 0.51678, 0.54261, 0.45094, 0.58114, 0.38857, 0.56004, 0.30447, 0.52265, 0.29041, 0.50962, 0.27833, 0.50026, 0.26091, 0.49714, 0.24685, 0.49843, 0.23457, 0.50393, 0.23081, 0.51604, 0.24447, 0.52558, 0.25734, 0.52724, 0.27378, 0.5265, 0.29101, 0.52393, 0.31095, 0.52257, 0.29055, 0.50147, 0.26897, 0.48954, 0.24303, 0.48532, 0.22303, 0.49358, 0.21809, 0.5163, 0.22402, 0.54384, 0.24144, 0.56972, 0.27748, 0.57211, 0.30501, 0.5444, 0.22355, 0.42588, 0.22867, 0.36939, 0.25613, 0.31636, 0.29754, 0.28359, 0.35524, 0.2603, 0.41993, 0.25901, 0.49485, 0.27194, 0.55069, 0.3073, 0.56093, 0.35947, 0.53766, 0.43536, 0.58699, 0.48366, 0.63957, 0.46425, 0.65772, 0.50349, 0.65586, 0.54747, 0.62887, 0.59879, 0.56931, 0.62379, 0.55302, 0.69192, 0.22119, 0.47584, 0.21548, 0.52971, 0.2246, 0.58288, 0.25411, 0.64747, 0.2868, 0.75032, 0.3369, 0.81995, 0.42185, 0.81793, 0.50135, 0.7594], "triangles": [1, 2, 62, 0, 1, 67, 1, 66, 67, 67, 68, 0, 68, 60, 0, 0, 60, 58, 60, 59, 58, 58, 57, 0, 57, 56, 0, 56, 3, 0, 1, 65, 66, 1, 64, 65, 1, 63, 64, 1, 62, 63, 65, 68, 67, 22, 65, 14, 22, 4, 13, 22, 14, 4, 65, 67, 66, 43, 14, 64, 14, 43, 34, 68, 65, 21, 21, 65, 22, 68, 21, 60, 14, 65, 64, 43, 64, 42, 60, 21, 59, 59, 21, 20, 64, 63, 42, 42, 63, 41, 58, 59, 20, 58, 20, 54, 54, 20, 19, 58, 54, 57, 62, 40, 63, 63, 40, 41, 21, 22, 11, 22, 12, 11, 21, 10, 20, 21, 11, 10, 41, 31, 42, 31, 32, 42, 43, 32, 33, 43, 42, 32, 41, 30, 31, 41, 40, 30, 22, 13, 12, 57, 54, 56, 56, 54, 55, 33, 23, 43, 43, 23, 34, 29, 40, 39, 40, 29, 30, 39, 40, 62, 10, 9, 20, 20, 9, 19, 19, 9, 18, 39, 62, 61, 30, 27, 31, 31, 26, 32, 31, 27, 26, 32, 24, 33, 32, 25, 24, 32, 26, 25, 29, 28, 30, 30, 28, 27, 33, 24, 23, 34, 23, 35, 14, 34, 35, 35, 23, 24, 14, 15, 4, 14, 35, 15, 11, 12, 6, 4, 5, 13, 6, 12, 5, 12, 13, 5, 29, 39, 28, 39, 61, 38, 61, 62, 2, 28, 39, 38, 4, 15, 5, 6, 7, 11, 10, 7, 8, 10, 11, 7, 24, 25, 35, 27, 28, 37, 56, 55, 3, 10, 8, 9, 15, 35, 36, 26, 36, 25, 35, 25, 36, 5, 15, 6, 6, 15, 16, 28, 38, 37, 27, 37, 26, 26, 37, 36, 38, 61, 37, 15, 44, 16, 6, 16, 7, 36, 44, 15, 37, 44, 36, 17, 7, 16, 19, 53, 54, 19, 18, 53, 54, 53, 55, 7, 17, 8, 44, 37, 61, 16, 44, 45, 16, 45, 46, 16, 46, 47, 48, 17, 16, 48, 16, 47, 18, 9, 17, 9, 8, 17, 48, 49, 17, 61, 2, 44, 53, 18, 17, 17, 49, 50, 53, 52, 55, 3, 55, 51, 52, 53, 17, 52, 50, 51, 17, 50, 52, 44, 2, 45, 45, 2, 46, 55, 52, 51, 46, 2, 47, 51, 50, 3, 47, 2, 48, 50, 49, 3, 48, 2, 49, 49, 2, 3], "vertices": [2, 187, -25.99, -43.5, 0.176, 200, -63.75, -14.99, 0.824, 2, 187, -10.38, 56.28, 0.24, 201, -55.4, 23.79, 0.76, 2, 187, 97.31, 39.42, 0.256, 201, 52.29, 6.93, 0.744, 2, 187, 81.69, -60.36, 0.416, 200, 43.94, -31.84, 0.584, 1, 187, 36.03, 10.66, 1, 1, 187, 37.49, 9.62, 1, 1, 187, 38.44, 8.26, 1, 1, 187, 38.81, 6.08, 1, 1, 187, 38.64, 2.76, 1, 1, 187, 37.92, -0.57, 1, 1, 187, 36.18, 2.84, 1, 1, 187, 35.36, 6.06, 1, 1, 187, 35.42, 8.33, 1, 1, 187, 35.67, 9.9, 1, 1, 187, 35.63, 12.95, 1, 1, 187, 37.79, 10.53, 1, 1, 187, 38.87, 7.68, 1, 1, 187, 40.26, 3.66, 1, 1, 187, 39.79, -3.19, 1, 1, 187, 36.92, -5.02, 1, 1, 187, 30.81, -3, 1, 1, 187, 27.69, 4.22, 1, 1, 187, 30.93, 10.09, 1, 1, 187, 36.27, 17.85, 1, 1, 187, 37.9, 19.04, 1, 1, 187, 39.09, 20.08, 1, 1, 187, 39.7, 21.77, 1, 1, 187, 39.78, 23.19, 1, 1, 187, 39.38, 24.51, 1, 1, 187, 38.14, 25.09, 1, 1, 187, 36.9, 23.89, 1, 1, 187, 36.52, 22.63, 1, 1, 187, 36.34, 20.98, 1, 1, 187, 36.35, 19.22, 1, 1, 187, 36.18, 17.21, 1, 1, 187, 38.77, 18.88, 1, 1, 187, 40.39, 20.84, 1, 1, 187, 41.25, 23.35, 1, 1, 187, 40.68, 25.49, 1, 1, 187, 38.31, 26.37, 1, 1, 187, 35.25, 26.24, 1, 1, 187, 32.19, 24.94, 1, 1, 187, 31.37, 21.38, 1, 1, 187, 33.92, 18.17, 1, 1, 187, 47.96, 24.3, 1, 1, 187, 53.96, 22.83, 1, 1, 187, 59.25, 19.2, 1, 1, 187, 62.13, 14.51, 1, 1, 187, 63.73, 8.36, 1, 1, 187, 62.86, 1.89, 1, 1, 187, 60.3, -5.37, 1, 1, 187, 55.62, -10.35, 1, 1, 187, 49.84, -10.49, 1, 1, 187, 42.03, -6.89, 1, 1, 187, 36.06, -10.99, 1, 1, 187, 37.33, -16.57, 1, 1, 187, 32.82, -17.72, 1, 1, 187, 28.11, -16.79, 1, 1, 187, 23.01, -13.23, 1, 1, 187, 21.25, -6.87, 1, 1, 187, 14.16, -4.09, 1, 1, 187, 42.62, 25.37, 1, 1, 187, 36.9, 26.85, 1, 1, 187, 31.04, 26.84, 1, 1, 187, 23.62, 24.98, 1, 1, 187, 12.03, 23.45, 1, 1, 187, 3.75, 19.63, 1, 1, 187, 2.64, 11.12, 1, 1, 187, 7.7, 2.2, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 8, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 46, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 68, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 88, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 120], "width": 101, "height": 109}}, "satangfull3": {"satangfull3": {"type": "mesh", "uvs": [0.33416, 0.09479, 0.1998, 0.08157, 0.1238, 0.11296, 0, 0.10801, 0, 0.45331, 0.11159, 0.80687, 0.2378, 1, 0.33823, 1, 0.59473, 0.84982, 0.85258, 0.50618, 1, 0.14105, 0.99915, 0.06505, 0.66123, 0, 0.55266, 0, 0.4943, 0.06505, 0.34094, 0.16748, 0.41965, 0.23027, 0.44816, 0.29635, 0.44137, 0.34922, 0.39523, 0.42357, 0.3233, 0.43018, 0.2663, 0.35087, 0.20523, 0.43844, 0.1618, 0.36244, 0.14823, 0.27487, 0.19166, 0.17574, 0.25001, 0.16418], "triangles": [26, 1, 0, 15, 0, 14, 26, 0, 15, 25, 2, 1, 25, 1, 26, 16, 15, 14, 24, 2, 25, 17, 16, 14, 17, 21, 16, 21, 26, 15, 21, 15, 16, 18, 21, 17, 24, 25, 26, 21, 24, 26, 23, 24, 21, 19, 21, 18, 20, 21, 19, 22, 23, 21, 22, 21, 20, 24, 4, 3, 24, 3, 2, 4, 24, 23, 10, 9, 12, 10, 12, 11, 22, 5, 4, 22, 4, 23, 14, 13, 12, 12, 17, 14, 9, 17, 12, 9, 18, 17, 9, 19, 18, 8, 19, 9, 20, 5, 22, 5, 7, 6, 20, 19, 8, 8, 5, 20, 8, 7, 5], "vertices": [1, 188, -3.97, 4.51, 1, 1, 188, -5.02, -2.97, 1, 1, 188, -3.83, -7.3, 1, 1, 188, -4.46, -14.21, 1, 1, 188, 11.39, -15.14, 1, 1, 188, 28, -9.86, 1, 1, 188, 37.28, -3.32, 1, 1, 188, 37.61, 2.29, 1, 1, 188, 31.56, 17.04, 1, 1, 188, 16.62, 32.38, 1, 1, 188, 0.34, 41.61, 1, 1, 188, -3.15, 41.76, 1, 1, 188, -7.25, 23.05, 1, 1, 188, -7.61, 16.98, 1, 1, 188, -4.81, 13.54, 1, 1, 188, -0.61, 4.69, 1, 1, 188, 2.53, 8.92, 1, 1, 188, 5.66, 10.34, 1, 1, 188, 8.06, 9.81, 1, 1, 188, 11.33, 7.03, 1, 1, 188, 11.39, 3, 1, 1, 188, 7.56, 0.02, 1, 1, 188, 11.38, -3.63, 1, 1, 188, 7.75, -5.85, 1, 1, 188, 3.69, -6.37, 1, 1, 188, -0.72, -3.68, 1, 1, 188, -1.06, -0.38, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 30], "width": 56, "height": 46}}, "satangfull4": {"satangfull4": {"type": "mesh", "uvs": [0.27696, 0.03561, 0.42383, 0, 0.47698, 0, 0.53433, 0.07281, 0.63504, 0.13425, 0.67561, 0.16983, 0.73995, 0.1957, 0.80849, 0.29919, 0.83087, 0.40429, 0.912, 0.57893, 0.92459, 0.6517, 0.93858, 0.68727, 0.98474, 0.76974, 1, 0.93953, 0.76653, 0.96702, 0.78192, 0.85868, 0.78331, 0.81987, 0.77073, 0.78914, 0.71897, 0.74872, 0.66861, 0.67595, 0.66302, 0.64361, 0.62525, 0.70991, 0.60427, 0.81178, 0.61826, 0.86514, 0.60707, 1, 0.04756, 1, 0.03637, 0.88293, 0.03357, 0.8134, 0, 0.65817, 0, 0.46412, 0.05455, 0.262, 0.13848, 0.20217, 0.26997, 0.14072, 0.28675, 0.11323, 0.52874, 0.51749, 0.39865, 0.45119, 0.07554, 0.43178, 0.46999, 0.65655, 0.38187, 0.64038, 0.05595, 0.62744], "triangles": [20, 8, 9, 20, 9, 10, 19, 20, 10, 18, 19, 10, 17, 18, 10, 11, 17, 10, 16, 17, 11, 12, 16, 11, 15, 16, 12, 13, 15, 12, 14, 15, 13, 33, 0, 1, 36, 30, 31, 33, 3, 32, 35, 32, 3, 31, 32, 35, 36, 31, 35, 1, 2, 3, 3, 33, 1, 29, 30, 36, 35, 5, 34, 4, 35, 3, 5, 35, 4, 39, 29, 36, 38, 36, 35, 38, 35, 34, 39, 36, 38, 7, 34, 5, 34, 8, 20, 6, 7, 5, 8, 34, 7, 37, 38, 34, 37, 34, 20, 28, 29, 39, 21, 37, 20, 22, 37, 21, 27, 28, 39, 38, 26, 27, 38, 25, 26, 39, 38, 27, 23, 38, 22, 38, 37, 22, 23, 24, 38, 25, 38, 24], "vertices": [1, 186, 130.84, 47.24, 1, 2, 186, 141.55, 25.25, 0.99991, 198, -99.96, -7.92, 9e-05, 2, 186, 143.66, 16.85, 0.99842, 198, -95.69, -0.38, 0.00158, 2, 186, 135.99, 5.28, 0.98011, 198, -82.15, 2.7, 0.01989, 2, 186, 131.59, -12.76, 0.87968, 198, -66.53, 12.71, 0.12032, 2, 186, 128.34, -20.39, 0.79408, 198, -58.9, 16, 0.20593, 2, 186, 127.36, -31.45, 0.67324, 198, -50.56, 23.33, 0.32676, 2, 186, 115.93, -45.85, 0.46206, 198, -32.36, 25.86, 0.53794, 2, 186, 102.45, -53, 0.22917, 198, -17.67, 21.74, 0.77083, 1, 198, 10.28, 21.12, 1, 1, 198, 20.22, 17.85, 1, 1, 198, 25.71, 17.36, 1, 1, 198, 39.53, 18.18, 1, 1, 198, 61.59, 8.55, 1, 1, 198, 46.22, -26.47, 1, 1, 198, 34.16, -16.77, 1, 1, 198, 29.51, -13.87, 1, 2, 186, 47.44, -56.73, 0.00117, 198, 24.73, -13.53, 0.99883, 2, 186, 50.91, -47.16, 0.02588, 198, 15.61, -18.06, 0.97412, 2, 186, 58.85, -36.7, 0.18661, 198, 2.64, -20.15, 0.81339, 2, 186, 63.05, -34.7, 0.35963, 198, -1.78, -18.7, 0.64037, 2, 186, 52.49, -31.01, 0.77163, 198, 3.33, -28.66, 0.22837, 2, 186, 37.72, -31.2, 0.9516, 198, 14.14, -38.71, 0.0484, 2, 186, 30.98, -35.24, 0.98085, 198, 21.81, -40.43, 0.01915, 2, 186, 12.1, -38.11, 0.99949, 198, 37.46, -51.39, 0.00051, 1, 186, -10.15, 50.33, 1, 1, 186, 5.41, 56.13, 1, 1, 186, 14.81, 58.96, 1, 1, 186, 34.7, 69.61, 1, 1, 186, 61.23, 76.28, 1, 1, 186, 91.04, 74.61, 1, 1, 186, 102.56, 63.4, 1, 1, 186, 116.19, 44.73, 1, 1, 186, 120.61, 43.02, 1, 2, 186, 74.96, -9.13, 0.91691, 198, -28.04, -28.98, 0.08309, 1, 186, 78.85, 13.71, 1, 1, 186, 68.66, 65.45, 1, 2, 186, 53.61, -4.63, 0.98528, 198, -15.69, -46.98, 0.01472, 1, 186, 52.31, 9.85, 1, 1, 186, 41.12, 61.82, 1], "hull": 34, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66, 40, 68, 68, 70, 70, 72, 72, 58, 42, 74, 74, 76, 76, 78, 78, 56], "width": 163, "height": 141}}, "satangfull5": {"satangfull5": {"type": "mesh", "uvs": [0.70139, 0.02439, 0.74762, 0, 0.88322, 0, 1, 0.1036, 0.95102, 0.30003, 0.77844, 0.59467, 0.55963, 0.85764, 0.26994, 1, 0.07579, 1, 0, 0.90833, 0, 0.78477, 0.0388, 0.68972, 0.24837, 0.61685, 0.47334, 0.40458, 0.63051, 0.1638], "triangles": [6, 7, 12, 10, 7, 8, 12, 7, 10, 12, 10, 11, 8, 9, 10, 12, 13, 6, 6, 13, 5, 13, 14, 5, 5, 14, 4, 14, 2, 4, 2, 14, 1, 0, 1, 14, 4, 2, 3], "vertices": [2, 189, -11.88, -19.14, 0.98941, 190, -21.34, -27.19, 0.01059, 2, 189, -16.16, -15.35, 0.9982, 190, -26.59, -24.94, 0.0018, 1, 189, -21.59, -1.45, 1, 1, 189, -15.93, 14.54, 1, 2, 189, 5.61, 17.17, 0.99448, 190, -16.1, 12.76, 0.00552, 3, 189, 41.88, 10.94, 0.00186, 190, 20.29, 18.22, 0.99073, 191, -25.95, 11.63, 0.00741, 2, 190, 57.31, 17.59, 0.01389, 191, 9.79, 21.32, 0.98611, 1, 191, 44.7, 15.95, 1, 1, 191, 62.34, 3.92, 1, 1, 191, 63.71, -8.88, 1, 1, 191, 56.26, -19.8, 1, 1, 191, 47, -25.8, 1, 2, 190, 59.27, -25.21, 0.0221, 191, 23.56, -19.26, 0.9779, 3, 189, 35.13, -27.72, 0.02141, 190, 26, -20.61, 0.85835, 191, -9.68, -24.08, 0.12024, 2, 189, 4.85, -20.98, 0.77075, 190, -4.87, -23.7, 0.22925], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28], "width": 110, "height": 107}}, "satangfull6": {"satangfull6": {"type": "mesh", "uvs": [0.62229, 0, 0.45279, 0.07128, 0.27724, 0.25178, 0.10774, 0.51594, 0, 0.73167, 0, 0.88576, 0.10169, 1, 0.33778, 1, 0.53149, 0.92098, 0.66467, 0.63481, 0.79179, 0.41908, 1, 0.26059, 1, 0.1021, 0.87049, 0], "triangles": [9, 2, 10, 3, 2, 9, 4, 6, 5, 8, 3, 9, 3, 7, 4, 4, 7, 6, 3, 8, 7, 13, 12, 11, 10, 0, 13, 10, 13, 11, 1, 0, 10, 2, 1, 10], "vertices": [1, 192, -2.96, -12.54, 1, 2, 192, 7.45, -15.99, 0.95299, 193, -17.07, -12.74, 0.04701, 2, 192, 24.37, -14.11, 0.27482, 193, -0.37, -16.01, 0.72518, 1, 193, 22.08, -16.31, 1, 1, 193, 39.7, -15, 1, 1, 193, 50.52, -10.13, 1, 1, 193, 56.21, -1.33, 1, 1, 193, 50.78, 10.73, 1, 1, 193, 40.78, 18.12, 1, 2, 192, 32, 21.7, 0.00223, 193, 17.63, 15.88, 0.99777, 2, 192, 14.86, 15.99, 0.44493, 193, -0.44, 15.56, 0.55507, 2, 192, -2.01, 16.6, 0.97093, 193, -16.36, 21.18, 0.02907, 1, 192, -11.13, 8.49, 1, 1, 192, -12.19, -2.16, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 56, "height": 77}}, "satangfull7": {"satangfull7": {"type": "mesh", "uvs": [1, 0.7346, 1, 1, 0, 1, 0, 0.80534, 0, 0, 1, 0, 0.78174, 0.40276, 0.81129, 0.5108, 0.85427, 0.60083, 0.92277, 0.68701, 0.24447, 0.42719, 0.20149, 0.52495, 0.14642, 0.63299, 0.06852, 0.72302, 0.26865, 0.40661, 0.29014, 0.35902, 0.34521, 0.27156, 0.38013, 0.27285, 0.39759, 0.30243, 0.42311, 0.32172, 0.42849, 0.37575, 0.47281, 0.38989, 0.49161, 0.37446, 0.48893, 0.33459, 0.53056, 0.35131, 0.56817, 0.3423, 0.58698, 0.3796, 0.64339, 0.40404], "triangles": [17, 16, 4, 5, 17, 4, 19, 18, 17, 17, 23, 19, 17, 25, 23, 5, 25, 17, 24, 23, 25, 15, 4, 16, 22, 23, 24, 20, 19, 23, 21, 20, 23, 25, 27, 26, 22, 21, 23, 5, 6, 25, 6, 27, 25, 15, 10, 4, 14, 10, 15, 7, 6, 5, 11, 4, 10, 0, 8, 7, 12, 4, 11, 0, 9, 8, 7, 5, 0, 13, 4, 12, 18, 15, 16, 18, 16, 17, 20, 15, 18, 13, 3, 4, 20, 18, 19, 2, 3, 13, 14, 15, 20, 11, 10, 14, 14, 20, 11, 21, 11, 20, 9, 0, 1, 2, 8, 9, 8, 27, 7, 27, 6, 7, 8, 26, 27, 21, 26, 8, 24, 25, 26, 22, 24, 26, 21, 22, 26, 12, 11, 21, 21, 8, 12, 2, 13, 12, 12, 8, 2, 9, 1, 2], "vertices": [1, 185, -160.57, -129.33, 1, 1, 185, -235.68, -129.33, 1, 1, 185, -235.68, 141.67, 1, 1, 185, -180.59, 141.67, 1, 1, 185, 47.32, 141.67, 1, 1, 185, 47.32, -129.33, 1, 1, 185, -66.66, -70.18, 1, 1, 185, -97.24, -78.19, 1, 1, 185, -122.72, -89.84, 1, 1, 185, -147.1, -108.4, 1, 1, 185, -73.58, 75.42, 1, 1, 185, -101.24, 87.07, 1, 1, 185, -131.82, 101.99, 1, 1, 185, -157.3, 123.1, 1, 1, 185, -67.75, 68.87, 1, 1, 185, -54.28, 63.04, 1, 1, 185, -29.53, 48.12, 1, 1, 185, -29.9, 38.65, 1, 1, 185, -38.27, 33.92, 1, 1, 185, -43.73, 27.01, 1, 1, 185, -59.02, 25.55, 1, 1, 185, -63.02, 13.54, 1, 1, 185, -58.65, 8.44, 1, 1, 185, -47.37, 9.17, 1, 1, 185, -52.1, -2.11, 1, 1, 185, -49.55, -12.31, 1, 1, 185, -60.11, -17.4, 1, 1, 185, -67.02, -32.69, 1], "hull": 6, "edges": [2, 4, 8, 10, 12, 14, 14, 16, 16, 18, 2, 0, 0, 10, 18, 0, 20, 22, 22, 24, 24, 26, 4, 6, 6, 8, 26, 6, 20, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 12], "width": 271, "height": 283}}, "satangfull8": {"satangfull8": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 185, -279.98, -97.17, 1, 1, 185, -279.98, 88.83, 1, 1, 185, -199.98, 88.83, 1, 1, 185, -199.98, -97.17, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 186, "height": 80}}, "satangfull9": {"satangfull9": {"type": "mesh", "uvs": [0.8017, 0, 0.6821, 0.03212, 0.6301, 0.12366, 0.4221, 0.28843, 0.3389, 0.33551, 0.2453, 0.42705, 0.1153, 0.71212, 0.0789, 0.75397, 0, 0.86904, 0, 0.96843, 0.1465, 1, 0.3181, 0.96581, 0.3649, 0.84289, 0.35804, 0.77557, 0.38377, 0.73818, 0.66679, 0.51963, 0.7011, 0.41898, 0.72397, 0.37728, 0.95267, 0.22342, 1, 0.1314, 1, 0], "triangles": [13, 6, 14, 6, 12, 7, 12, 6, 13, 12, 10, 7, 10, 8, 7, 10, 9, 8, 10, 12, 11, 15, 3, 16, 15, 14, 4, 15, 4, 3, 5, 4, 14, 6, 5, 14, 18, 0, 19, 2, 0, 18, 0, 2, 1, 17, 2, 18, 3, 2, 17, 16, 3, 17, 0, 20, 19], "vertices": [2, 194, -2.95, -12.11, 0.86871, 195, -10.09, -12.48, 0.13129, 2, 194, 6.88, -18.12, 0.49459, 195, -0.05, -18.13, 0.50541, 2, 194, 22.46, -14.09, 0.03686, 195, 15.37, -13.55, 0.96314, 2, 195, 48.36, -13.5, 0.72454, 196, -5.95, -12.5, 0.27546, 2, 195, 58.87, -15.19, 0.18025, 196, 3.95, -16.43, 0.81975, 1, 196, 21.21, -18.62, 1, 2, 196, 70.26, -12.54, 0.31626, 197, -2.42, -12.65, 0.68374, 2, 196, 77.96, -13.03, 0.03852, 197, 5.1, -14.39, 0.96148, 1, 197, 25.45, -17.43, 1, 1, 197, 41.96, -14.36, 1, 1, 197, 44.93, -1.14, 1, 1, 197, 36.58, 12.14, 1, 1, 197, 15.43, 12.25, 1, 2, 196, 73.29, 10.51, 0.09947, 197, 4.35, 9.6, 0.90053, 2, 196, 66.6, 10.41, 0.52765, 197, -2.26, 10.59, 0.47235, 2, 195, 70.13, 25.04, 0.00122, 196, 23.67, 20.39, 0.99878, 2, 195, 54.22, 18.36, 0.23415, 196, 6.68, 17.32, 0.76585, 2, 195, 47.23, 16.21, 0.6135, 196, -0.6, 16.74, 0.3865, 2, 194, 23.09, 18.09, 0.00266, 195, 14.86, 18.63, 0.99734, 2, 194, 7.64, 13.68, 0.38125, 195, -0.42, 13.67, 0.61875, 1, 194, -11.5, 2.42, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40], "width": 85, "height": 169}}, "wild text": {"wild text": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-30.43, -83.45, -30.43, 88.55, 32.57, 88.55, 32.57, -83.45], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 172, "height": 63}}, "wild text2": {"wild text": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-30.43, -83.45, -30.43, 88.55, 32.57, 88.55, 32.57, -83.45], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 172, "height": 63}}}}, "animations": {"Duongtank": {"slots": {"BGfire0": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking1": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking2": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking3": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking4": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking5": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking6": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking7": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking8": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking9": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking10": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking11": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking12": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking13": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking14": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking15": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking16": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking17": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking18": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking19": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking20": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking21": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking22": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking23": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking24": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking25": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking26": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking27": {"attachment": [{"time": 0, "name": null}]}, "WildexpandBorderlight0": {"attachment": [{"time": 0, "name": null}]}, "WildexpandBorderlight1": {"attachment": [{"time": 0, "name": null}]}, "batgioifull1": {"attachment": [{"time": 0, "name": null}]}, "batgioifull2": {"attachment": [{"time": 0, "name": null}]}, "batgioifull3": {"attachment": [{"time": 0, "name": null}]}, "batgioifull4": {"attachment": [{"time": 0, "name": null}]}, "batgioifull5": {"attachment": [{"time": 0, "name": null}]}, "batgioifull6": {"attachment": [{"time": 0, "name": null}]}, "batgioifull7": {"attachment": [{"time": 0, "name": null}]}, "batgioifull8": {"attachment": [{"time": 0, "name": null}]}, "batgioifull9": {"attachment": [{"time": 0, "name": null}]}, "bgtank": {"attachment": [{"time": 0, "name": null}]}, "brush": {"attachment": [{"time": 0, "name": "brush"}]}, "chonmuccuocboard": {"attachment": [{"time": 0, "name": "chonmuccuocboard"}]}, "chonmuccuocboard2": {"attachment": [{"time": 0, "name": "chonmuccuocboard"}]}, "muccuoc0": {"attachment": [{"time": 0, "name": "muccuoc0"}]}, "satangfull1": {"attachment": [{"time": 0, "name": null}]}, "satangfull2": {"attachment": [{"time": 0, "name": null}]}, "satangfull3": {"attachment": [{"time": 0, "name": null}]}, "satangfull4": {"attachment": [{"time": 0, "name": null}]}, "satangfull5": {"attachment": [{"time": 0, "name": null}]}, "satangfull6": {"attachment": [{"time": 0, "name": null}]}, "satangfull7": {"attachment": [{"time": 0, "name": null}]}, "satangfull8": {"attachment": [{"time": 0, "name": null}]}, "satangfull9": {"attachment": [{"time": 0, "name": null}]}, "wild text": {"attachment": [{"time": 0, "name": null}]}, "wild text2": {"attachment": [{"time": 0, "name": null}]}}, "bones": {"bone110": {"rotate": [{"time": 0, "angle": 0}, {"time": 2.5, "angle": -2.36}, {"time": 5, "angle": 0}]}, "bone115": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": -27.26}, {"time": 0.5667, "angle": -31.87}, {"time": 0.8333, "angle": -28.81}, {"time": 1.1, "angle": -13.76}, {"time": 1.4333, "angle": -11.19}, {"time": 1.7, "angle": -9.89}, {"time": 1.9667, "angle": -19.6}, {"time": 2.2333, "angle": -18.7}, {"time": 2.5, "angle": 0}, {"time": 2.7667, "angle": -27.26}, {"time": 3.0667, "angle": -31.87}, {"time": 3.3333, "angle": -28.81}, {"time": 3.6, "angle": -13.76}, {"time": 3.9333, "angle": -11.19}, {"time": 4.2, "angle": -9.89}, {"time": 4.4667, "angle": -19.6}, {"time": 4.7333, "angle": -18.7}, {"time": 5, "angle": 0}]}, "bone118": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 15.45}, {"time": 0.5667, "angle": 1.95}, {"time": 0.8333, "angle": -38.17}, {"time": 1.1, "angle": -25.73}, {"time": 1.4333, "angle": -14.52}, {"time": 1.7, "angle": 12.52}, {"time": 1.9667, "angle": 24.42}, {"time": 2.2333, "angle": 13.49}, {"time": 2.5, "angle": 0}, {"time": 2.7667, "angle": 15.45}, {"time": 3.0667, "angle": 1.95}, {"time": 3.3333, "angle": -38.17}, {"time": 3.6, "angle": -25.73}, {"time": 3.9333, "angle": -14.52}, {"time": 4.2, "angle": 12.52}, {"time": 4.4667, "angle": 24.42}, {"time": 4.7333, "angle": 13.49}, {"time": 5, "angle": 0}]}, "bone117": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 23.18}, {"time": 0.5667, "angle": 8.75}, {"time": 0.8333, "angle": 12.76}, {"time": 1.1, "angle": -5.29}, {"time": 1.4333, "angle": 5.24}, {"time": 1.7, "angle": 9.47}, {"time": 1.9667, "angle": 18.29}, {"time": 2.2333, "angle": 6.48}, {"time": 2.5, "angle": 0}, {"time": 2.7667, "angle": 23.18}, {"time": 3.0667, "angle": 8.75}, {"time": 3.3333, "angle": 12.76}, {"time": 3.6, "angle": -5.29}, {"time": 3.9333, "angle": 5.24}, {"time": 4.2, "angle": 9.47}, {"time": 4.4667, "angle": 18.29}, {"time": 4.7333, "angle": 6.48}, {"time": 5, "angle": 0}]}, "bone116": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 10.01}, {"time": 0.5667, "angle": 14.34}, {"time": 0.8333, "angle": 15.53}, {"time": 1.1, "angle": 16.16}, {"time": 1.4333, "angle": 19.05}, {"time": 1.7, "angle": 19.36}, {"time": 1.9667, "angle": 21.02}, {"time": 2.2333, "angle": 15.17}, {"time": 2.5, "angle": 0}, {"time": 2.7667, "angle": 10.01}, {"time": 3.0667, "angle": 14.34}, {"time": 3.3333, "angle": 15.53}, {"time": 3.6, "angle": 16.16}, {"time": 3.9333, "angle": 19.05}, {"time": 4.2, "angle": 19.36}, {"time": 4.4667, "angle": 21.02}, {"time": 4.7333, "angle": 15.17}, {"time": 5, "angle": 0}]}, "bone111": {"rotate": [{"time": 0, "angle": -10.8}, {"time": 2.5, "angle": -3.64}, {"time": 5, "angle": -10.8}]}, "bone124": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -9.37}, {"time": 0.4667, "angle": -16.76}, {"time": 0.7333, "angle": -27.9}, {"time": 1, "angle": -24.66}, {"time": 1.2333, "angle": 2.7}, {"time": 1.5, "angle": -3.17}, {"time": 1.7667, "angle": -7.24}, {"time": 2.0333, "angle": -9.2}, {"time": 2.2667, "angle": -18.24}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": -9.37}, {"time": 2.9667, "angle": -16.76}, {"time": 3.2333, "angle": -27.9}, {"time": 3.5, "angle": -24.66}, {"time": 3.7333, "angle": 2.7}, {"time": 4, "angle": -3.17}, {"time": 4.2667, "angle": -7.24}, {"time": 4.5333, "angle": -9.2}, {"time": 4.7667, "angle": -18.24}, {"time": 5, "angle": 0}]}, "bone128": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 24.88}, {"time": 0.4667, "angle": 4.3}, {"time": 0.7333, "angle": 46.42}, {"time": 1, "angle": 36.05}, {"time": 1.2333, "angle": 47.37}, {"time": 1.5, "angle": 42.46}, {"time": 1.7667, "angle": 0.32}, {"time": 2.0333, "angle": 1.74}, {"time": 2.2667, "angle": 36}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": 24.88}, {"time": 2.9667, "angle": 4.3}, {"time": 3.2333, "angle": 46.42}, {"time": 3.5, "angle": 36.05}, {"time": 3.7333, "angle": 47.37}, {"time": 4, "angle": 42.46}, {"time": 4.2667, "angle": 0.32}, {"time": 4.5333, "angle": 1.74}, {"time": 4.7667, "angle": 36}, {"time": 5, "angle": 0}]}, "bone127": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 24.53}, {"time": 0.4667, "angle": 1.06}, {"time": 0.7333, "angle": 18.91}, {"time": 1, "angle": -21.69}, {"time": 1.2333, "angle": 13.6}, {"time": 1.5, "angle": 21.29}, {"time": 1.7667, "angle": 33.37}, {"time": 2.0333, "angle": 1.5}, {"time": 2.2667, "angle": 14.89}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": 24.53}, {"time": 2.9667, "angle": 1.06}, {"time": 3.2333, "angle": 18.91}, {"time": 3.5, "angle": -21.69}, {"time": 3.7333, "angle": 13.6}, {"time": 4, "angle": 21.29}, {"time": 4.2667, "angle": 33.37}, {"time": 4.5333, "angle": 1.5}, {"time": 4.7667, "angle": 14.89}, {"time": 5, "angle": 0}]}, "bone126": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 5.27}, {"time": 0.4667, "angle": 12.37}, {"time": 0.7333, "angle": 10.95}, {"time": 1, "angle": 12.35}, {"time": 1.2333, "angle": -7.64}, {"time": 1.5, "angle": -4.04}, {"time": 1.7667, "angle": 10.03}, {"time": 2.0333, "angle": 20.65}, {"time": 2.2667, "angle": 15.22}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": 5.27}, {"time": 2.9667, "angle": 12.37}, {"time": 3.2333, "angle": 10.95}, {"time": 3.5, "angle": 12.35}, {"time": 3.7333, "angle": -7.64}, {"time": 4, "angle": -4.04}, {"time": 4.2667, "angle": 10.03}, {"time": 4.5333, "angle": 20.65}, {"time": 4.7667, "angle": 15.22}, {"time": 5, "angle": 0}]}, "bone125": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -3.78}, {"time": 0.4667, "angle": 14.08}, {"time": 0.7333, "angle": 6.09}, {"time": 1, "angle": 32.06}, {"time": 1.2333, "angle": -6.82}, {"time": 1.5, "angle": -9.36}, {"time": 1.7667, "angle": -4.68}, {"time": 2.0333, "angle": 7.05}, {"time": 2.2667, "angle": 5.11}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": -3.78}, {"time": 2.9667, "angle": 14.08}, {"time": 3.2333, "angle": 6.09}, {"time": 3.5, "angle": 32.06}, {"time": 3.7333, "angle": -6.82}, {"time": 4, "angle": -9.36}, {"time": 4.2667, "angle": -4.68}, {"time": 4.5333, "angle": 7.05}, {"time": 4.7667, "angle": 5.11}, {"time": 5, "angle": 0}]}, "bone129": {"rotate": [{"time": 0, "angle": -0.32}, {"time": 0.2667, "angle": -0.77}, {"time": 0.5, "angle": -7.13}, {"time": 0.7333, "angle": -17.58}, {"time": 0.9667, "angle": -21.33}, {"time": 1.2333, "angle": -16.59}, {"time": 1.5, "angle": -14.72}, {"time": 1.7333, "angle": -14.31}, {"time": 1.9667, "angle": -13.35}, {"time": 2.2333, "angle": -26.9}, {"time": 2.5, "angle": -0.32}, {"time": 2.7667, "angle": -0.77}, {"time": 3, "angle": -7.13}, {"time": 3.2333, "angle": -17.58}, {"time": 3.4667, "angle": -21.33}, {"time": 3.7333, "angle": -16.59}, {"time": 4, "angle": -14.72}, {"time": 4.2333, "angle": -14.31}, {"time": 4.4667, "angle": -13.35}, {"time": 4.7333, "angle": -26.9}, {"time": 5, "angle": -0.32}]}, "bone130": {"rotate": [{"time": 0, "angle": 0.48}, {"time": 0.2667, "angle": 4.41}, {"time": 0.5, "angle": -2.31}, {"time": 0.7333, "angle": -9.86}, {"time": 0.9667, "angle": -7.76}, {"time": 1.2333, "angle": 3.85}, {"time": 1.5, "angle": 4.43}, {"time": 1.7333, "angle": 4.11}, {"time": 1.9667, "angle": 5.13}, {"time": 2.2333, "angle": 9}, {"time": 2.5, "angle": 0.48}, {"time": 2.7667, "angle": 4.41}, {"time": 3, "angle": -2.31}, {"time": 3.2333, "angle": -9.86}, {"time": 3.4667, "angle": -7.76}, {"time": 3.7333, "angle": 3.85}, {"time": 4, "angle": 4.43}, {"time": 4.2333, "angle": 4.11}, {"time": 4.4667, "angle": 5.13}, {"time": 4.7333, "angle": 9}, {"time": 5, "angle": 0.48}]}, "bone131": {"rotate": [{"time": 0, "angle": 4.54}, {"time": 0.2667, "angle": 20.1}, {"time": 0.5, "angle": 10.65}, {"time": 0.7333, "angle": 8.85}, {"time": 0.9667, "angle": 17.44}, {"time": 1.2333, "angle": 24.51}, {"time": 1.5, "angle": 25.79}, {"time": 1.7333, "angle": 23.33}, {"time": 1.9667, "angle": 24.46}, {"time": 2.2333, "angle": 35.37}, {"time": 2.5, "angle": 4.54}, {"time": 2.7667, "angle": 20.1}, {"time": 3, "angle": 10.65}, {"time": 3.2333, "angle": 8.85}, {"time": 3.4667, "angle": 17.44}, {"time": 3.7333, "angle": 24.51}, {"time": 4, "angle": 25.79}, {"time": 4.2333, "angle": 23.33}, {"time": 4.4667, "angle": 24.46}, {"time": 4.7333, "angle": 35.37}, {"time": 5, "angle": 4.54}]}, "bone132": {"rotate": [{"time": 0, "angle": 14.41}, {"time": 0.2667, "angle": 13.46}, {"time": 0.5, "angle": 5.62}, {"time": 0.7333, "angle": 16.76}, {"time": 0.9667, "angle": 14.92}, {"time": 1.2333, "angle": 13.84}, {"time": 1.5, "angle": 23.38}, {"time": 1.7333, "angle": 15.29}, {"time": 1.9667, "angle": 18.49}, {"time": 2.2333, "angle": 24.07}, {"time": 2.5, "angle": 14.41}, {"time": 2.7667, "angle": 13.46}, {"time": 3, "angle": 5.62}, {"time": 3.2333, "angle": 16.76}, {"time": 3.4667, "angle": 14.92}, {"time": 3.7333, "angle": 13.84}, {"time": 4, "angle": 23.38}, {"time": 4.2333, "angle": 15.29}, {"time": 4.4667, "angle": 18.49}, {"time": 4.7333, "angle": 24.07}, {"time": 5, "angle": 14.41}]}, "bone133": {"rotate": [{"time": 0, "angle": 24.42}, {"time": 0.2667, "angle": -7.34}, {"time": 0.5, "angle": 7.68}, {"time": 0.7333, "angle": 18.65}, {"time": 0.9667, "angle": -6.78}, {"time": 1.2333, "angle": -33.37}, {"time": 1.5, "angle": -30.09}, {"time": 1.7333, "angle": -1.46}, {"time": 1.9667, "angle": 9.42}, {"time": 2.2333, "angle": 12.21}, {"time": 2.5, "angle": 24.42}, {"time": 2.7667, "angle": -7.34}, {"time": 3, "angle": 7.68}, {"time": 3.2333, "angle": 18.65}, {"time": 3.4667, "angle": -6.78}, {"time": 3.7333, "angle": -33.37}, {"time": 4, "angle": -30.09}, {"time": 4.2333, "angle": -1.46}, {"time": 4.4667, "angle": 9.42}, {"time": 4.7333, "angle": 12.21}, {"time": 5, "angle": 24.42}]}, "bone134": {"rotate": [{"time": 0, "angle": 18.17}, {"time": 0.2667, "angle": 26.7}, {"time": 0.5, "angle": 59.97}, {"time": 0.7333, "angle": 26.7}, {"time": 0.9667, "angle": 24.56}, {"time": 1.2333, "angle": 23.35}, {"time": 1.5, "angle": 22.89}, {"time": 1.7333, "angle": 27.93}, {"time": 1.9667, "angle": 23.62}, {"time": 2.2333, "angle": 12.65}, {"time": 2.5, "angle": 18.17}, {"time": 2.7667, "angle": 26.7}, {"time": 3, "angle": 59.97}, {"time": 3.2333, "angle": 26.7}, {"time": 3.4667, "angle": 24.56}, {"time": 3.7333, "angle": 23.35}, {"time": 4, "angle": 22.89}, {"time": 4.2333, "angle": 27.93}, {"time": 4.4667, "angle": 23.62}, {"time": 4.7333, "angle": 12.65}, {"time": 5, "angle": 18.17}]}, "bone141": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": -0.16}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": -0.16}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": -0.16}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": -0.16}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": -0.16}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": -0.16}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": -0.16}, {"time": 5, "angle": 0}]}, "bone145": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 7.62}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": 7.62}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": 7.62}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": 7.62}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": 7.62}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": 7.62}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": 7.62}, {"time": 5, "angle": 0}]}, "bone144": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 0.17}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": 0.17}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": 0.17}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": 0.17}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": 0.17}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": 0.17}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": 0.17}, {"time": 5, "angle": 0}]}, "bone143": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": -1.82}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": -1.82}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": -1.82}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": -1.82}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": -1.82}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": -1.82}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": -1.82}, {"time": 5, "angle": 0}]}, "bone142": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": -3.29}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": -3.29}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": -3.29}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": -3.29}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": -3.29}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": -3.29}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": -3.29}, {"time": 5, "angle": 0}]}, "bone150": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 16.47}, {"time": 0.5, "angle": 0}, {"time": 0.7333, "angle": 16.47}, {"time": 1, "angle": 0}, {"time": 1.2333, "angle": 16.47}, {"time": 1.5, "angle": 0}, {"time": 1.7333, "angle": 16.47}, {"time": 2, "angle": 0}, {"time": 2.2333, "angle": 16.47}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": 16.47}, {"time": 3, "angle": 0}, {"time": 3.2333, "angle": 16.47}, {"time": 3.5, "angle": 0}, {"time": 3.7333, "angle": 16.47}, {"time": 4, "angle": 0}, {"time": 4.2333, "angle": 16.47}, {"time": 4.5, "angle": 0}, {"time": 4.7333, "angle": 16.47}, {"time": 5, "angle": 0}]}, "bone149": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -3.76}, {"time": 0.5, "angle": 0}, {"time": 0.7333, "angle": -3.76}, {"time": 1, "angle": 0}, {"time": 1.2333, "angle": -3.76}, {"time": 1.5, "angle": 0}, {"time": 1.7333, "angle": -3.76}, {"time": 2, "angle": 0}, {"time": 2.2333, "angle": -3.76}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": -3.76}, {"time": 3, "angle": 0}, {"time": 3.2333, "angle": -3.76}, {"time": 3.5, "angle": 0}, {"time": 3.7333, "angle": -3.76}, {"time": 4, "angle": 0}, {"time": 4.2333, "angle": -3.76}, {"time": 4.5, "angle": 0}, {"time": 4.7333, "angle": -3.76}, {"time": 5, "angle": 0}]}, "bone148": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -4}, {"time": 0.5, "angle": 0}, {"time": 0.7333, "angle": -4}, {"time": 1, "angle": 0}, {"time": 1.2333, "angle": -4}, {"time": 1.5, "angle": 0}, {"time": 1.7333, "angle": -4}, {"time": 2, "angle": 0}, {"time": 2.2333, "angle": -4}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": -4}, {"time": 3, "angle": 0}, {"time": 3.2333, "angle": -4}, {"time": 3.5, "angle": 0}, {"time": 3.7333, "angle": -4}, {"time": 4, "angle": 0}, {"time": 4.2333, "angle": -4}, {"time": 4.5, "angle": 0}, {"time": 4.7333, "angle": -4}, {"time": 5, "angle": 0}]}, "bone147": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -2.9}, {"time": 0.5, "angle": 0}, {"time": 0.7333, "angle": -2.9}, {"time": 1, "angle": 0}, {"time": 1.2333, "angle": -2.9}, {"time": 1.5, "angle": 0}, {"time": 1.7333, "angle": -2.9}, {"time": 2, "angle": 0}, {"time": 2.2333, "angle": -2.9}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": -2.9}, {"time": 3, "angle": 0}, {"time": 3.2333, "angle": -2.9}, {"time": 3.5, "angle": 0}, {"time": 3.7333, "angle": -2.9}, {"time": 4, "angle": 0}, {"time": 4.2333, "angle": -2.9}, {"time": 4.5, "angle": 0}, {"time": 4.7333, "angle": -2.9}, {"time": 5, "angle": 0}]}, "bone151": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 14.86}, {"time": 0.5, "angle": 0}, {"time": 0.7333, "angle": 14.86}, {"time": 1, "angle": 0}, {"time": 1.2333, "angle": 14.86}, {"time": 1.5, "angle": 0}, {"time": 1.7333, "angle": 14.86}, {"time": 2, "angle": 0}, {"time": 2.2333, "angle": 14.86}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": 14.86}, {"time": 3, "angle": 0}, {"time": 3.2333, "angle": 14.86}, {"time": 3.5, "angle": 0}, {"time": 3.7333, "angle": 14.86}, {"time": 4, "angle": 0}, {"time": 4.2333, "angle": 14.86}, {"time": 4.5, "angle": 0}, {"time": 4.7333, "angle": 14.86}, {"time": 5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.2333, "x": 18.63, "y": 24.72}, {"time": 0.5, "x": 0, "y": 0}, {"time": 0.7333, "x": 18.63, "y": 24.72}, {"time": 1, "x": 0, "y": 0}, {"time": 1.2333, "x": 18.63, "y": 24.72}, {"time": 1.5, "x": 0, "y": 0}, {"time": 1.7333, "x": 18.63, "y": 24.72}, {"time": 2, "x": 0, "y": 0}, {"time": 2.2333, "x": 18.63, "y": 24.72}, {"time": 2.5, "x": 0, "y": 0}, {"time": 2.7333, "x": 18.63, "y": 24.72}, {"time": 3, "x": 0, "y": 0}, {"time": 3.2333, "x": 18.63, "y": 24.72}, {"time": 3.5, "x": 0, "y": 0}, {"time": 3.7333, "x": 18.63, "y": 24.72}, {"time": 4, "x": 0, "y": 0}, {"time": 4.2333, "x": 18.63, "y": 24.72}, {"time": 4.5, "x": 0, "y": 0}, {"time": 4.7333, "x": 18.63, "y": 24.72}, {"time": 5, "x": 0, "y": 0}]}, "bone152": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 11.63}, {"time": 0.5, "angle": 0}, {"time": 0.7667, "angle": 11.63}, {"time": 1, "angle": 0}, {"time": 1.2667, "angle": 11.63}, {"time": 1.5, "angle": 0}, {"time": 1.7667, "angle": 11.63}, {"time": 2, "angle": 0}, {"time": 2.2667, "angle": 11.63}, {"time": 2.5, "angle": 0}, {"time": 2.7667, "angle": 11.63}, {"time": 3, "angle": 0}, {"time": 3.2667, "angle": 11.63}, {"time": 3.5, "angle": 0}, {"time": 3.7667, "angle": 11.63}, {"time": 4, "angle": 0}, {"time": 4.2667, "angle": 11.63}, {"time": 4.5, "angle": 0}, {"time": 4.7667, "angle": 11.63}, {"time": 5, "angle": 0}]}, "bone135": {"translate": [{"time": 0, "x": -12.53, "y": -46.57}]}, "bone136": {"rotate": [{"time": 0, "angle": -8.11}], "translate": [{"time": 0, "x": -13.67, "y": -38.45}], "scale": [{"time": 0, "x": 0.768, "y": 0.768}]}, "bone137": {"rotate": [{"time": 0, "angle": 27.37}, {"time": 0.2333, "angle": 25.91}, {"time": 0.5, "angle": 27.37}, {"time": 0.7333, "angle": 25.91}, {"time": 1, "angle": 27.37}, {"time": 1.2333, "angle": 25.91}, {"time": 1.5, "angle": 27.37}, {"time": 1.7333, "angle": 25.91}, {"time": 2, "angle": 27.37}, {"time": 2.2333, "angle": 25.91}, {"time": 2.5, "angle": 27.37}, {"time": 2.7333, "angle": 25.91}, {"time": 3, "angle": 27.37}, {"time": 3.2333, "angle": 25.91}, {"time": 3.5, "angle": 27.37}, {"time": 3.7333, "angle": 25.91}, {"time": 4, "angle": 27.37}, {"time": 4.2333, "angle": 25.91}, {"time": 4.5, "angle": 27.37}, {"time": 4.7333, "angle": 25.91}, {"time": 5, "angle": 27.37}]}, "bone138": {"rotate": [{"time": 0, "angle": -15.28}, {"time": 0.2333, "angle": -13.14}, {"time": 0.5, "angle": -15.28}, {"time": 0.7333, "angle": -13.14}, {"time": 1, "angle": -15.28}, {"time": 1.2333, "angle": -13.14}, {"time": 1.5, "angle": -15.28}, {"time": 1.7333, "angle": -13.14}, {"time": 2, "angle": -15.28}, {"time": 2.2333, "angle": -13.14}, {"time": 2.5, "angle": -15.28}, {"time": 2.7333, "angle": -13.14}, {"time": 3, "angle": -15.28}, {"time": 3.2333, "angle": -13.14}, {"time": 3.5, "angle": -15.28}, {"time": 3.7333, "angle": -13.14}, {"time": 4, "angle": -15.28}, {"time": 4.2333, "angle": -13.14}, {"time": 4.5, "angle": -15.28}, {"time": 4.7333, "angle": -13.14}, {"time": 5, "angle": -15.28}]}, "bone139": {"rotate": [{"time": 0, "angle": -7.27}, {"time": 0.2333, "angle": 14.86}, {"time": 0.5, "angle": -7.27}, {"time": 0.7333, "angle": 14.86}, {"time": 1, "angle": -7.27}, {"time": 1.2333, "angle": 14.86}, {"time": 1.5, "angle": -7.27}, {"time": 1.7333, "angle": 14.86}, {"time": 2, "angle": -7.27}, {"time": 2.2333, "angle": 14.86}, {"time": 2.5, "angle": -7.27}, {"time": 2.7333, "angle": 14.86}, {"time": 3, "angle": -7.27}, {"time": 3.2333, "angle": 14.86}, {"time": 3.5, "angle": -7.27}, {"time": 3.7333, "angle": 14.86}, {"time": 4, "angle": -7.27}, {"time": 4.2333, "angle": 14.86}, {"time": 4.5, "angle": -7.27}, {"time": 4.7333, "angle": 14.86}, {"time": 5, "angle": -7.27}]}, "bone153": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -6.28}, {"time": 0.4667, "angle": -10.84}, {"time": 0.7, "angle": -19.11}, {"time": 0.9667, "angle": -18.53}, {"time": 1.2, "angle": -19.01}, {"time": 1.4333, "angle": -25.32}, {"time": 1.6667, "angle": -31.56}, {"time": 1.9333, "angle": -31.93}, {"time": 2.2, "angle": -29.84}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": -6.28}, {"time": 2.9667, "angle": -10.84}, {"time": 3.2, "angle": -19.11}, {"time": 3.4667, "angle": -18.53}, {"time": 3.7, "angle": -19.01}, {"time": 3.9333, "angle": -25.32}, {"time": 4.1667, "angle": -31.56}, {"time": 4.4333, "angle": -31.93}, {"time": 4.7, "angle": -29.84}, {"time": 5, "angle": 0}]}, "bone157": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 18.5}, {"time": 0.4667, "angle": 6.67}, {"time": 0.7, "angle": -20.22}, {"time": 0.9667, "angle": -25.69}, {"time": 1.2, "angle": 4.67}, {"time": 1.4333, "angle": 15.35}, {"time": 1.6667, "angle": 24.92}, {"time": 1.9333, "angle": -1.78}, {"time": 2.2, "angle": -11.33}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": 18.5}, {"time": 2.9667, "angle": 6.67}, {"time": 3.2, "angle": -20.22}, {"time": 3.4667, "angle": -25.69}, {"time": 3.7, "angle": 4.67}, {"time": 3.9333, "angle": 15.35}, {"time": 4.1667, "angle": 24.92}, {"time": 4.4333, "angle": -1.78}, {"time": 4.7, "angle": -11.33}, {"time": 5, "angle": 0}]}, "bone156": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 8.48}, {"time": 0.4667, "angle": 20.86}, {"time": 0.7, "angle": -0.67}, {"time": 0.9667, "angle": 14.38}, {"time": 1.2, "angle": 5.91}, {"time": 1.4333, "angle": 10.22}, {"time": 1.6667, "angle": 13.75}, {"time": 1.9333, "angle": 14.48}, {"time": 2.2, "angle": 0.01}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": 8.48}, {"time": 2.9667, "angle": 20.86}, {"time": 3.2, "angle": -0.67}, {"time": 3.4667, "angle": 14.38}, {"time": 3.7, "angle": 5.91}, {"time": 3.9333, "angle": 10.22}, {"time": 4.1667, "angle": 13.75}, {"time": 4.4333, "angle": 14.48}, {"time": 4.7, "angle": 0.01}, {"time": 5, "angle": 0}]}, "bone155": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -0.79}, {"time": 0.4667, "angle": 11.07}, {"time": 0.7, "angle": 14.52}, {"time": 0.9667, "angle": 18.15}, {"time": 1.2, "angle": 17.04}, {"time": 1.4333, "angle": 16.2}, {"time": 1.6667, "angle": 14.15}, {"time": 1.9333, "angle": 16.96}, {"time": 2.2, "angle": 13.91}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": -0.79}, {"time": 2.9667, "angle": 11.07}, {"time": 3.2, "angle": 14.52}, {"time": 3.4667, "angle": 18.15}, {"time": 3.7, "angle": 17.04}, {"time": 3.9333, "angle": 16.2}, {"time": 4.1667, "angle": 14.15}, {"time": 4.4333, "angle": 16.96}, {"time": 4.7, "angle": 13.91}, {"time": 5, "angle": 0}]}, "bone154": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -6.97}, {"time": 0.4667, "angle": -6.48}, {"time": 0.7, "angle": 8.51}, {"time": 0.9667, "angle": 9.48}, {"time": 1.2, "angle": 9.1}, {"time": 1.4333, "angle": 7.73}, {"time": 1.6667, "angle": 3.49}, {"time": 1.9333, "angle": 4.73}, {"time": 2.2, "angle": 11.37}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": -6.97}, {"time": 2.9667, "angle": -6.48}, {"time": 3.2, "angle": 8.51}, {"time": 3.4667, "angle": 9.48}, {"time": 3.7, "angle": 9.1}, {"time": 3.9333, "angle": 7.73}, {"time": 4.1667, "angle": 3.49}, {"time": 4.4333, "angle": 4.73}, {"time": 4.7, "angle": 11.37}, {"time": 5, "angle": 0}]}, "bone162": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 4.36}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": 4.36}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": 4.36}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": 4.36}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": 4.36}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": 4.36}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": 4.36}, {"time": 5, "angle": 0}]}, "bone165": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 3.76}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": 3.76}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": 3.76}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": 3.76}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": 3.76}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": 3.76}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": 3.76}, {"time": 5, "angle": 0}]}, "bone164": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": -6.5}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": -6.5}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": -6.5}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": -6.5}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": -6.5}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": -6.5}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": -6.5}, {"time": 5, "angle": 0}]}, "bone163": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 3.55}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": 3.55}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": 3.55}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": 3.55}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": 3.55}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": 3.55}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": 3.55}, {"time": 5, "angle": 0}]}, "bone166": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 1.62}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": 1.62}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": 1.62}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": 1.62}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": 1.62}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": 1.62}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": 1.62}, {"time": 5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3667, "x": -3.07, "y": -0.31}, {"time": 0.7, "x": 0, "y": 0}, {"time": 1.0667, "x": -3.07, "y": -0.31}, {"time": 1.4333, "x": 0, "y": 0}, {"time": 1.8, "x": -3.07, "y": -0.31}, {"time": 2.1333, "x": 0, "y": 0}, {"time": 2.5, "x": -3.07, "y": -0.31}, {"time": 2.8667, "x": 0, "y": 0}, {"time": 3.2, "x": -3.07, "y": -0.31}, {"time": 3.5667, "x": 0, "y": 0}, {"time": 3.9333, "x": -3.07, "y": -0.31}, {"time": 4.3, "x": 0, "y": 0}, {"time": 4.6333, "x": -3.07, "y": -0.31}, {"time": 5, "x": 0, "y": 0}]}, "bone169": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": -9.72}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": -9.72}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": -9.72}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": -9.72}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": -9.72}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": -9.72}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": -9.72}, {"time": 5, "angle": 0}]}, "bone168": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 7.84}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": 7.84}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": 7.84}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": 7.84}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": 7.84}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": 7.84}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": 7.84}, {"time": 5, "angle": 0}]}, "bone167": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 4.66}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": 4.66}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": 4.66}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": 4.66}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": 4.66}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": 4.66}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": 4.66}, {"time": 5, "angle": 0}]}, "bone119": {"rotate": [{"time": 0, "angle": 0}, {"time": 2.5, "angle": 8.02}, {"time": 5, "angle": 0}]}, "bone121": {"rotate": [{"time": 0, "angle": 0}, {"time": 2.5, "angle": -12.37}, {"time": 5, "angle": 0}]}, "bone120": {"rotate": [{"time": 0, "angle": 0}, {"time": 2.5, "angle": -26.55}, {"time": 5, "angle": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 2.5, "x": 1.281, "y": 1}, {"time": 5, "x": 1, "y": 1}]}, "bone113": {"rotate": [{"time": 0, "angle": 0}, {"time": 2.5, "angle": 8.35}, {"time": 5, "angle": 0}], "translate": [{"time": 0, "x": -3.64, "y": -1.61}]}}, "deform": {"default": {"duongtank6": {"duongtank6": [{"time": 1.3333}, {"time": 1.4, "offset": 100, "vertices": [-3.46284, -0.67547, -4.85697, -0.95688, -4.27189, -2.41112, -4.87589, -0.53442, -3.37874, -2.1026, -3.93338, -0.60247, -2.36238, -0.90533, -2.52792, 0.09793, -0.86084, 0.03476, -0.7776, 0.37091, -0.19911, -0.2986, -0.30051, -0.19598, 0.45673, -0.51258, 0.21813, -0.65079, 0.86752, -0.33534, 0.66554, -0.64965, 0.84981, 0.12609, 0.83083, -0.21847, 0.05131, -0.41515, -0.11617, -0.40179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.67245, 0.06406, -2.18774, 0.02368, -3.09427, -0.01911, -3.13937, -0.24525, -2.45679, -0.20327, -0.83402, 0.06728, -0.05833, 0.43076, 0.51112, 0.85381, 0.40126, 0.75496, 0.09489, 0.53883, 0.00824, 0.52025], "curve": "stepped"}, {"time": 1.5333, "offset": 100, "vertices": [-3.46284, -0.67547, -4.85697, -0.95688, -4.27189, -2.41112, -4.87589, -0.53442, -3.37874, -2.1026, -3.93338, -0.60247, -2.36238, -0.90533, -2.52792, 0.09793, -0.86084, 0.03476, -0.7776, 0.37091, -0.19911, -0.2986, -0.30051, -0.19598, 0.45673, -0.51258, 0.21813, -0.65079, 0.86752, -0.33534, 0.66554, -0.64965, 0.84981, 0.12609, 0.83083, -0.21847, 0.05131, -0.41515, -0.11617, -0.40179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.67245, 0.06406, -2.18774, 0.02368, -3.09427, -0.01911, -3.13937, -0.24525, -2.45679, -0.20327, -0.83402, 0.06728, -0.05833, 0.43076, 0.51112, 0.85381, 0.40126, 0.75496, 0.09489, 0.53883, 0.00824, 0.52025]}, {"time": 1.6, "curve": "stepped"}, {"time": 3.5333}, {"time": 3.6, "offset": 100, "vertices": [-3.46284, -0.67547, -4.85697, -0.95688, -4.27189, -2.41112, -4.87589, -0.53442, -3.37874, -2.1026, -3.93338, -0.60247, -2.36238, -0.90533, -2.52792, 0.09793, -0.86084, 0.03476, -0.7776, 0.37091, -0.19911, -0.2986, -0.30051, -0.19598, 0.45673, -0.51258, 0.21813, -0.65079, 0.86752, -0.33534, 0.66554, -0.64965, 0.84981, 0.12609, 0.83083, -0.21847, 0.05131, -0.41515, -0.11617, -0.40179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.67245, 0.06406, -2.18774, 0.02368, -3.09427, -0.01911, -3.13937, -0.24525, -2.45679, -0.20327, -0.83402, 0.06728, -0.05833, 0.43076, 0.51112, 0.85381, 0.40126, 0.75496, 0.09489, 0.53883, 0.00824, 0.52025], "curve": "stepped"}, {"time": 3.7333, "offset": 100, "vertices": [-3.46284, -0.67547, -4.85697, -0.95688, -4.27189, -2.41112, -4.87589, -0.53442, -3.37874, -2.1026, -3.93338, -0.60247, -2.36238, -0.90533, -2.52792, 0.09793, -0.86084, 0.03476, -0.7776, 0.37091, -0.19911, -0.2986, -0.30051, -0.19598, 0.45673, -0.51258, 0.21813, -0.65079, 0.86752, -0.33534, 0.66554, -0.64965, 0.84981, 0.12609, 0.83083, -0.21847, 0.05131, -0.41515, -0.11617, -0.40179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.67245, 0.06406, -2.18774, 0.02368, -3.09427, -0.01911, -3.13937, -0.24525, -2.45679, -0.20327, -0.83402, 0.06728, -0.05833, 0.43076, 0.51112, 0.85381, 0.40126, 0.75496, 0.09489, 0.53883, 0.00824, 0.52025]}, {"time": 3.8}]}, "duongtank14": {"duongtank6": [{"time": 1.3333}, {"time": 1.4, "offset": 100, "vertices": [-3.46284, -0.67547, -4.85697, -0.95688, -4.27189, -2.41112, -4.87589, -0.53442, -3.37874, -2.1026, -3.93338, -0.60247, -2.36238, -0.90533, -2.52792, 0.09793, -0.86084, 0.03476, -0.7776, 0.37091, -0.19911, -0.2986, -0.30051, -0.19598, 0.45673, -0.51258, 0.21813, -0.65079, 0.86752, -0.33534, 0.66554, -0.64965, 0.84981, 0.12609, 0.83083, -0.21847, 0.05131, -0.41515, -0.11617, -0.40179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.67245, 0.06406, -2.18774, 0.02368, -3.09427, -0.01911, -3.13937, -0.24525, -2.45679, -0.20327, -0.83402, 0.06728, -0.05833, 0.43076, 0.51112, 0.85381, 0.40126, 0.75496, 0.09489, 0.53883, 0.00824, 0.52025], "curve": "stepped"}, {"time": 1.5333, "offset": 100, "vertices": [-3.46284, -0.67547, -4.85697, -0.95688, -4.27189, -2.41112, -4.87589, -0.53442, -3.37874, -2.1026, -3.93338, -0.60247, -2.36238, -0.90533, -2.52792, 0.09793, -0.86084, 0.03476, -0.7776, 0.37091, -0.19911, -0.2986, -0.30051, -0.19598, 0.45673, -0.51258, 0.21813, -0.65079, 0.86752, -0.33534, 0.66554, -0.64965, 0.84981, 0.12609, 0.83083, -0.21847, 0.05131, -0.41515, -0.11617, -0.40179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.67245, 0.06406, -2.18774, 0.02368, -3.09427, -0.01911, -3.13937, -0.24525, -2.45679, -0.20327, -0.83402, 0.06728, -0.05833, 0.43076, 0.51112, 0.85381, 0.40126, 0.75496, 0.09489, 0.53883, 0.00824, 0.52025]}, {"time": 1.6, "curve": "stepped"}, {"time": 3.5333}, {"time": 3.6, "offset": 100, "vertices": [-3.46284, -0.67547, -4.85697, -0.95688, -4.27189, -2.41112, -4.87589, -0.53442, -3.37874, -2.1026, -3.93338, -0.60247, -2.36238, -0.90533, -2.52792, 0.09793, -0.86084, 0.03476, -0.7776, 0.37091, -0.19911, -0.2986, -0.30051, -0.19598, 0.45673, -0.51258, 0.21813, -0.65079, 0.86752, -0.33534, 0.66554, -0.64965, 0.84981, 0.12609, 0.83083, -0.21847, 0.05131, -0.41515, -0.11617, -0.40179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.67245, 0.06406, -2.18774, 0.02368, -3.09427, -0.01911, -3.13937, -0.24525, -2.45679, -0.20327, -0.83402, 0.06728, -0.05833, 0.43076, 0.51112, 0.85381, 0.40126, 0.75496, 0.09489, 0.53883, 0.00824, 0.52025], "curve": "stepped"}, {"time": 3.7333, "offset": 100, "vertices": [-3.46284, -0.67547, -4.85697, -0.95688, -4.27189, -2.41112, -4.87589, -0.53442, -3.37874, -2.1026, -3.93338, -0.60247, -2.36238, -0.90533, -2.52792, 0.09793, -0.86084, 0.03476, -0.7776, 0.37091, -0.19911, -0.2986, -0.30051, -0.19598, 0.45673, -0.51258, 0.21813, -0.65079, 0.86752, -0.33534, 0.66554, -0.64965, 0.84981, 0.12609, 0.83083, -0.21847, 0.05131, -0.41515, -0.11617, -0.40179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.67245, 0.06406, -2.18774, 0.02368, -3.09427, -0.01911, -3.13937, -0.24525, -2.45679, -0.20327, -0.83402, 0.06728, -0.05833, 0.43076, 0.51112, 0.85381, 0.40126, 0.75496, 0.09489, 0.53883, 0.00824, 0.52025]}, {"time": 3.8}]}}}}, "DuongtankWild": {"slots": {"BGfire0": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking1": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking2": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking3": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking4": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking5": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking6": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking7": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking8": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking9": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking10": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking11": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking12": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking13": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking14": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking15": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking16": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking17": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking18": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking19": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking20": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking21": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking22": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking23": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking24": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking25": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking26": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking27": {"attachment": [{"time": 0, "name": null}]}, "WildexpandBorderlight0": {"attachment": [{"time": 0.0667, "name": "WildexpandBorderlight1"}, {"time": 0.1333, "name": "WildexpandBorderlight2"}, {"time": 0.2, "name": "WildexpandBorderlight3"}, {"time": 0.2667, "name": "WildexpandBorderlight4"}, {"time": 0.3667, "name": "WildexpandBorderlight5"}, {"time": 0.4333, "name": "WildexpandBorderlight6"}, {"time": 0.5, "name": "WildexpandBorderlight7"}, {"time": 0.5667, "name": "WildexpandBorderlight0"}, {"time": 0.6333, "name": "WildexpandBorderlight1"}, {"time": 0.7, "name": "WildexpandBorderlight2"}, {"time": 0.7667, "name": "WildexpandBorderlight3"}, {"time": 0.8333, "name": "WildexpandBorderlight4"}, {"time": 0.9, "name": "WildexpandBorderlight5"}, {"time": 1, "name": "WildexpandBorderlight6"}, {"time": 1.0667, "name": "WildexpandBorderlight7"}, {"time": 1.1333, "name": "WildexpandBorderlight0"}, {"time": 1.2, "name": "WildexpandBorderlight1"}, {"time": 1.2667, "name": "WildexpandBorderlight2"}, {"time": 1.3333, "name": "WildexpandBorderlight3"}, {"time": 1.4, "name": "WildexpandBorderlight4"}, {"time": 1.4667, "name": "WildexpandBorderlight5"}, {"time": 1.5333, "name": "WildexpandBorderlight6"}, {"time": 1.6333, "name": "WildexpandBorderlight7"}, {"time": 1.7, "name": "WildexpandBorderlight0"}, {"time": 1.7667, "name": "WildexpandBorderlight1"}, {"time": 1.8333, "name": "WildexpandBorderlight2"}, {"time": 1.9, "name": "WildexpandBorderlight3"}, {"time": 1.9667, "name": "WildexpandBorderlight4"}, {"time": 2.0333, "name": "WildexpandBorderlight5"}, {"time": 2.1, "name": "WildexpandBorderlight6"}, {"time": 2.1667, "name": "WildexpandBorderlight7"}, {"time": 2.2667, "name": "WildexpandBorderlight0"}, {"time": 2.3333, "name": "WildexpandBorderlight1"}, {"time": 2.4, "name": "WildexpandBorderlight2"}, {"time": 2.4667, "name": "WildexpandBorderlight3"}, {"time": 2.5333, "name": "WildexpandBorderlight4"}, {"time": 2.6, "name": "WildexpandBorderlight5"}, {"time": 2.6667, "name": "WildexpandBorderlight6"}, {"time": 2.7333, "name": "WildexpandBorderlight7"}, {"time": 2.8, "name": "WildexpandBorderlight0"}, {"time": 2.9, "name": "WildexpandBorderlight1"}, {"time": 2.9667, "name": "WildexpandBorderlight2"}, {"time": 3.0333, "name": "WildexpandBorderlight3"}, {"time": 3.1, "name": "WildexpandBorderlight4"}, {"time": 3.1667, "name": "WildexpandBorderlight5"}, {"time": 3.2333, "name": "WildexpandBorderlight6"}, {"time": 3.3, "name": "WildexpandBorderlight7"}, {"time": 3.3667, "name": "WildexpandBorderlight0"}, {"time": 3.4333, "name": "WildexpandBorderlight1"}, {"time": 3.5333, "name": "WildexpandBorderlight2"}, {"time": 3.6, "name": "WildexpandBorderlight3"}, {"time": 3.6667, "name": "WildexpandBorderlight4"}, {"time": 3.7333, "name": "WildexpandBorderlight5"}, {"time": 3.8, "name": "WildexpandBorderlight6"}, {"time": 3.8667, "name": "WildexpandBorderlight7"}, {"time": 3.9333, "name": "WildexpandBorderlight0"}, {"time": 4, "name": "WildexpandBorderlight1"}, {"time": 4.0667, "name": "WildexpandBorderlight2"}, {"time": 4.1667, "name": "WildexpandBorderlight3"}, {"time": 4.2333, "name": "WildexpandBorderlight4"}, {"time": 4.3, "name": "WildexpandBorderlight5"}, {"time": 4.3667, "name": "WildexpandBorderlight6"}, {"time": 4.4333, "name": "WildexpandBorderlight7"}, {"time": 4.5, "name": "WildexpandBorderlight0"}, {"time": 4.5667, "name": "WildexpandBorderlight1"}, {"time": 4.6333, "name": "WildexpandBorderlight2"}, {"time": 4.7, "name": "WildexpandBorderlight3"}, {"time": 4.8, "name": "WildexpandBorderlight4"}, {"time": 4.8667, "name": "WildexpandBorderlight5"}, {"time": 4.9333, "name": "WildexpandBorderlight6"}, {"time": 5, "name": "WildexpandBorderlight7"}]}, "WildexpandBorderlight1": {"attachment": [{"time": 0.0667, "name": "WildexpandBorderlight1"}, {"time": 0.1333, "name": "WildexpandBorderlight2"}, {"time": 0.2, "name": "WildexpandBorderlight3"}, {"time": 0.2667, "name": "WildexpandBorderlight4"}, {"time": 0.3667, "name": "WildexpandBorderlight5"}, {"time": 0.4333, "name": "WildexpandBorderlight6"}, {"time": 0.5, "name": "WildexpandBorderlight7"}, {"time": 0.5667, "name": "WildexpandBorderlight0"}, {"time": 0.6333, "name": "WildexpandBorderlight1"}, {"time": 0.7, "name": "WildexpandBorderlight2"}, {"time": 0.7667, "name": "WildexpandBorderlight3"}, {"time": 0.8333, "name": "WildexpandBorderlight4"}, {"time": 0.9, "name": "WildexpandBorderlight5"}, {"time": 1, "name": "WildexpandBorderlight6"}, {"time": 1.0667, "name": "WildexpandBorderlight7"}, {"time": 1.1333, "name": "WildexpandBorderlight0"}, {"time": 1.2, "name": "WildexpandBorderlight1"}, {"time": 1.2667, "name": "WildexpandBorderlight2"}, {"time": 1.3333, "name": "WildexpandBorderlight3"}, {"time": 1.4, "name": "WildexpandBorderlight4"}, {"time": 1.4667, "name": "WildexpandBorderlight5"}, {"time": 1.5333, "name": "WildexpandBorderlight6"}, {"time": 1.6333, "name": "WildexpandBorderlight7"}, {"time": 1.7, "name": "WildexpandBorderlight0"}, {"time": 1.7667, "name": "WildexpandBorderlight1"}, {"time": 1.8333, "name": "WildexpandBorderlight2"}, {"time": 1.9, "name": "WildexpandBorderlight3"}, {"time": 1.9667, "name": "WildexpandBorderlight4"}, {"time": 2.0333, "name": "WildexpandBorderlight5"}, {"time": 2.1, "name": "WildexpandBorderlight6"}, {"time": 2.1667, "name": "WildexpandBorderlight7"}, {"time": 2.2667, "name": "WildexpandBorderlight0"}, {"time": 2.3333, "name": "WildexpandBorderlight1"}, {"time": 2.4, "name": "WildexpandBorderlight2"}, {"time": 2.4667, "name": "WildexpandBorderlight3"}, {"time": 2.5333, "name": "WildexpandBorderlight4"}, {"time": 2.6, "name": "WildexpandBorderlight5"}, {"time": 2.6667, "name": "WildexpandBorderlight6"}, {"time": 2.7333, "name": "WildexpandBorderlight7"}, {"time": 2.8333, "name": "WildexpandBorderlight0"}, {"time": 2.9, "name": "WildexpandBorderlight1"}, {"time": 2.9667, "name": "WildexpandBorderlight2"}, {"time": 3.0333, "name": "WildexpandBorderlight3"}, {"time": 3.1, "name": "WildexpandBorderlight4"}, {"time": 3.1667, "name": "WildexpandBorderlight5"}, {"time": 3.2333, "name": "WildexpandBorderlight6"}, {"time": 3.3, "name": "WildexpandBorderlight7"}, {"time": 3.3667, "name": "WildexpandBorderlight0"}, {"time": 3.4667, "name": "WildexpandBorderlight1"}, {"time": 3.5333, "name": "WildexpandBorderlight2"}, {"time": 3.6, "name": "WildexpandBorderlight3"}, {"time": 3.6667, "name": "WildexpandBorderlight4"}, {"time": 3.7333, "name": "WildexpandBorderlight5"}, {"time": 3.8, "name": "WildexpandBorderlight6"}, {"time": 3.8667, "name": "WildexpandBorderlight7"}, {"time": 3.9333, "name": "WildexpandBorderlight0"}, {"time": 4, "name": "WildexpandBorderlight1"}, {"time": 4.1, "name": "WildexpandBorderlight2"}, {"time": 4.1667, "name": "WildexpandBorderlight3"}, {"time": 4.2333, "name": "WildexpandBorderlight4"}, {"time": 4.3, "name": "WildexpandBorderlight5"}, {"time": 4.3667, "name": "WildexpandBorderlight6"}, {"time": 4.4333, "name": "WildexpandBorderlight7"}, {"time": 4.5, "name": "WildexpandBorderlight0"}, {"time": 4.5667, "name": "WildexpandBorderlight1"}, {"time": 4.6333, "name": "WildexpandBorderlight2"}, {"time": 4.7333, "name": "WildexpandBorderlight3"}, {"time": 4.8, "name": "WildexpandBorderlight4"}, {"time": 4.8667, "name": "WildexpandBorderlight5"}, {"time": 4.9333, "name": "WildexpandBorderlight6"}, {"time": 5, "name": "WildexpandBorderlight7"}]}, "batgioifull1": {"attachment": [{"time": 0, "name": null}]}, "batgioifull2": {"attachment": [{"time": 0, "name": null}]}, "batgioifull3": {"attachment": [{"time": 0, "name": null}]}, "batgioifull4": {"attachment": [{"time": 0, "name": null}]}, "batgioifull5": {"attachment": [{"time": 0, "name": null}]}, "batgioifull6": {"attachment": [{"time": 0, "name": null}]}, "batgioifull7": {"attachment": [{"time": 0, "name": null}]}, "batgioifull8": {"attachment": [{"time": 0, "name": null}]}, "batgioifull9": {"attachment": [{"time": 0, "name": null}]}, "path": {"attachment": [{"time": 0, "name": "path"}]}, "satangfull1": {"attachment": [{"time": 0, "name": null}]}, "satangfull2": {"attachment": [{"time": 0, "name": null}]}, "satangfull3": {"attachment": [{"time": 0, "name": null}]}, "satangfull4": {"attachment": [{"time": 0, "name": null}]}, "satangfull5": {"attachment": [{"time": 0, "name": null}]}, "satangfull6": {"attachment": [{"time": 0, "name": null}]}, "satangfull7": {"attachment": [{"time": 0, "name": null}]}, "satangfull8": {"attachment": [{"time": 0, "name": null}]}, "satangfull9": {"attachment": [{"time": 0, "name": null}]}, "wild text2": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 2.6667, "color": "ffffffff"}, {"time": 2.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}, {"time": 3.6667, "color": "ffffffff"}, {"time": 3.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 4, "color": "ffffff00"}, {"time": 4.6667, "color": "ffffffff"}, {"time": 4.9333, "color": "ffffff00"}]}}, "bones": {"bone110": {"rotate": [{"time": 0, "angle": 0}, {"time": 2.5, "angle": -2.36}, {"time": 5, "angle": 0}]}, "bone115": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": -27.26}, {"time": 0.5667, "angle": -31.87}, {"time": 0.8333, "angle": -28.81}, {"time": 1.1, "angle": -13.76}, {"time": 1.4333, "angle": -11.19}, {"time": 1.7, "angle": -9.89}, {"time": 1.9667, "angle": -19.6}, {"time": 2.2333, "angle": -18.7}, {"time": 2.5, "angle": 0}, {"time": 2.7667, "angle": -27.26}, {"time": 3.0667, "angle": -31.87}, {"time": 3.3333, "angle": -28.81}, {"time": 3.6, "angle": -13.76}, {"time": 3.9333, "angle": -11.19}, {"time": 4.2, "angle": -9.89}, {"time": 4.4667, "angle": -19.6}, {"time": 4.7333, "angle": -18.7}, {"time": 5, "angle": 0}]}, "bone118": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 15.45}, {"time": 0.5667, "angle": 1.95}, {"time": 0.8333, "angle": -38.17}, {"time": 1.1, "angle": -25.73}, {"time": 1.4333, "angle": -14.52}, {"time": 1.7, "angle": 12.52}, {"time": 1.9667, "angle": 24.42}, {"time": 2.2333, "angle": 13.49}, {"time": 2.5, "angle": 0}, {"time": 2.7667, "angle": 15.45}, {"time": 3.0667, "angle": 1.95}, {"time": 3.3333, "angle": -38.17}, {"time": 3.6, "angle": -25.73}, {"time": 3.9333, "angle": -14.52}, {"time": 4.2, "angle": 12.52}, {"time": 4.4667, "angle": 24.42}, {"time": 4.7333, "angle": 13.49}, {"time": 5, "angle": 0}]}, "bone117": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 23.18}, {"time": 0.5667, "angle": 8.75}, {"time": 0.8333, "angle": 12.76}, {"time": 1.1, "angle": -5.29}, {"time": 1.4333, "angle": 5.24}, {"time": 1.7, "angle": 9.47}, {"time": 1.9667, "angle": 18.29}, {"time": 2.2333, "angle": 6.48}, {"time": 2.5, "angle": 0}, {"time": 2.7667, "angle": 23.18}, {"time": 3.0667, "angle": 8.75}, {"time": 3.3333, "angle": 12.76}, {"time": 3.6, "angle": -5.29}, {"time": 3.9333, "angle": 5.24}, {"time": 4.2, "angle": 9.47}, {"time": 4.4667, "angle": 18.29}, {"time": 4.7333, "angle": 6.48}, {"time": 5, "angle": 0}]}, "bone116": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 10.01}, {"time": 0.5667, "angle": 14.34}, {"time": 0.8333, "angle": 15.53}, {"time": 1.1, "angle": 16.16}, {"time": 1.4333, "angle": 19.05}, {"time": 1.7, "angle": 19.36}, {"time": 1.9667, "angle": 21.02}, {"time": 2.2333, "angle": 15.17}, {"time": 2.5, "angle": 0}, {"time": 2.7667, "angle": 10.01}, {"time": 3.0667, "angle": 14.34}, {"time": 3.3333, "angle": 15.53}, {"time": 3.6, "angle": 16.16}, {"time": 3.9333, "angle": 19.05}, {"time": 4.2, "angle": 19.36}, {"time": 4.4667, "angle": 21.02}, {"time": 4.7333, "angle": 15.17}, {"time": 5, "angle": 0}]}, "bone111": {"rotate": [{"time": 0, "angle": -10.8}, {"time": 2.5, "angle": -3.64}, {"time": 5, "angle": -10.8}]}, "bone124": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -9.37}, {"time": 0.4667, "angle": -16.76}, {"time": 0.7333, "angle": -27.9}, {"time": 1, "angle": -24.66}, {"time": 1.2333, "angle": 2.7}, {"time": 1.5, "angle": -3.17}, {"time": 1.7667, "angle": -7.24}, {"time": 2.0333, "angle": -9.2}, {"time": 2.2667, "angle": -18.24}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": -9.37}, {"time": 2.9667, "angle": -16.76}, {"time": 3.2333, "angle": -27.9}, {"time": 3.5, "angle": -24.66}, {"time": 3.7333, "angle": 2.7}, {"time": 4, "angle": -3.17}, {"time": 4.2667, "angle": -7.24}, {"time": 4.5333, "angle": -9.2}, {"time": 4.7667, "angle": -18.24}, {"time": 5, "angle": 0}]}, "bone128": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 24.88}, {"time": 0.4667, "angle": 4.3}, {"time": 0.7333, "angle": 46.42}, {"time": 1, "angle": 36.05}, {"time": 1.2333, "angle": 47.37}, {"time": 1.5, "angle": 42.46}, {"time": 1.7667, "angle": 0.32}, {"time": 2.0333, "angle": 1.74}, {"time": 2.2667, "angle": 36}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": 24.88}, {"time": 2.9667, "angle": 4.3}, {"time": 3.2333, "angle": 46.42}, {"time": 3.5, "angle": 36.05}, {"time": 3.7333, "angle": 47.37}, {"time": 4, "angle": 42.46}, {"time": 4.2667, "angle": 0.32}, {"time": 4.5333, "angle": 1.74}, {"time": 4.7667, "angle": 36}, {"time": 5, "angle": 0}]}, "bone127": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 24.53}, {"time": 0.4667, "angle": 1.06}, {"time": 0.7333, "angle": 18.91}, {"time": 1, "angle": -21.69}, {"time": 1.2333, "angle": 13.6}, {"time": 1.5, "angle": 21.29}, {"time": 1.7667, "angle": 33.37}, {"time": 2.0333, "angle": 1.5}, {"time": 2.2667, "angle": 14.89}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": 24.53}, {"time": 2.9667, "angle": 1.06}, {"time": 3.2333, "angle": 18.91}, {"time": 3.5, "angle": -21.69}, {"time": 3.7333, "angle": 13.6}, {"time": 4, "angle": 21.29}, {"time": 4.2667, "angle": 33.37}, {"time": 4.5333, "angle": 1.5}, {"time": 4.7667, "angle": 14.89}, {"time": 5, "angle": 0}]}, "bone126": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 5.27}, {"time": 0.4667, "angle": 12.37}, {"time": 0.7333, "angle": 10.95}, {"time": 1, "angle": 12.35}, {"time": 1.2333, "angle": -7.64}, {"time": 1.5, "angle": -4.04}, {"time": 1.7667, "angle": 10.03}, {"time": 2.0333, "angle": 20.65}, {"time": 2.2667, "angle": 15.22}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": 5.27}, {"time": 2.9667, "angle": 12.37}, {"time": 3.2333, "angle": 10.95}, {"time": 3.5, "angle": 12.35}, {"time": 3.7333, "angle": -7.64}, {"time": 4, "angle": -4.04}, {"time": 4.2667, "angle": 10.03}, {"time": 4.5333, "angle": 20.65}, {"time": 4.7667, "angle": 15.22}, {"time": 5, "angle": 0}]}, "bone125": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -3.78}, {"time": 0.4667, "angle": 14.08}, {"time": 0.7333, "angle": 6.09}, {"time": 1, "angle": 32.06}, {"time": 1.2333, "angle": -6.82}, {"time": 1.5, "angle": -9.36}, {"time": 1.7667, "angle": -4.68}, {"time": 2.0333, "angle": 7.05}, {"time": 2.2667, "angle": 5.11}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": -3.78}, {"time": 2.9667, "angle": 14.08}, {"time": 3.2333, "angle": 6.09}, {"time": 3.5, "angle": 32.06}, {"time": 3.7333, "angle": -6.82}, {"time": 4, "angle": -9.36}, {"time": 4.2667, "angle": -4.68}, {"time": 4.5333, "angle": 7.05}, {"time": 4.7667, "angle": 5.11}, {"time": 5, "angle": 0}]}, "bone129": {"rotate": [{"time": 0, "angle": -0.32}, {"time": 0.2667, "angle": -0.77}, {"time": 0.5, "angle": -7.13}, {"time": 0.7333, "angle": -17.58}, {"time": 0.9667, "angle": -21.33}, {"time": 1.2333, "angle": -16.59}, {"time": 1.5, "angle": -14.72}, {"time": 1.7333, "angle": -14.31}, {"time": 1.9667, "angle": -13.35}, {"time": 2.2333, "angle": -26.9}, {"time": 2.5, "angle": -0.32}, {"time": 2.7667, "angle": -0.77}, {"time": 3, "angle": -7.13}, {"time": 3.2333, "angle": -17.58}, {"time": 3.4667, "angle": -21.33}, {"time": 3.7333, "angle": -16.59}, {"time": 4, "angle": -14.72}, {"time": 4.2333, "angle": -14.31}, {"time": 4.4667, "angle": -13.35}, {"time": 4.7333, "angle": -26.9}, {"time": 5, "angle": -0.32}]}, "bone130": {"rotate": [{"time": 0, "angle": 0.48}, {"time": 0.2667, "angle": 4.41}, {"time": 0.5, "angle": -2.31}, {"time": 0.7333, "angle": -9.86}, {"time": 0.9667, "angle": -7.76}, {"time": 1.2333, "angle": 3.85}, {"time": 1.5, "angle": 4.43}, {"time": 1.7333, "angle": 4.11}, {"time": 1.9667, "angle": 5.13}, {"time": 2.2333, "angle": 9}, {"time": 2.5, "angle": 0.48}, {"time": 2.7667, "angle": 4.41}, {"time": 3, "angle": -2.31}, {"time": 3.2333, "angle": -9.86}, {"time": 3.4667, "angle": -7.76}, {"time": 3.7333, "angle": 3.85}, {"time": 4, "angle": 4.43}, {"time": 4.2333, "angle": 4.11}, {"time": 4.4667, "angle": 5.13}, {"time": 4.7333, "angle": 9}, {"time": 5, "angle": 0.48}]}, "bone131": {"rotate": [{"time": 0, "angle": 4.54}, {"time": 0.2667, "angle": 20.1}, {"time": 0.5, "angle": 10.65}, {"time": 0.7333, "angle": 8.85}, {"time": 0.9667, "angle": 17.44}, {"time": 1.2333, "angle": 24.51}, {"time": 1.5, "angle": 25.79}, {"time": 1.7333, "angle": 23.33}, {"time": 1.9667, "angle": 24.46}, {"time": 2.2333, "angle": 35.37}, {"time": 2.5, "angle": 4.54}, {"time": 2.7667, "angle": 20.1}, {"time": 3, "angle": 10.65}, {"time": 3.2333, "angle": 8.85}, {"time": 3.4667, "angle": 17.44}, {"time": 3.7333, "angle": 24.51}, {"time": 4, "angle": 25.79}, {"time": 4.2333, "angle": 23.33}, {"time": 4.4667, "angle": 24.46}, {"time": 4.7333, "angle": 35.37}, {"time": 5, "angle": 4.54}]}, "bone132": {"rotate": [{"time": 0, "angle": 14.41}, {"time": 0.2667, "angle": 13.46}, {"time": 0.5, "angle": 5.62}, {"time": 0.7333, "angle": 16.76}, {"time": 0.9667, "angle": 14.92}, {"time": 1.2333, "angle": 13.84}, {"time": 1.5, "angle": 23.38}, {"time": 1.7333, "angle": 15.29}, {"time": 1.9667, "angle": 18.49}, {"time": 2.2333, "angle": 24.07}, {"time": 2.5, "angle": 14.41}, {"time": 2.7667, "angle": 13.46}, {"time": 3, "angle": 5.62}, {"time": 3.2333, "angle": 16.76}, {"time": 3.4667, "angle": 14.92}, {"time": 3.7333, "angle": 13.84}, {"time": 4, "angle": 23.38}, {"time": 4.2333, "angle": 15.29}, {"time": 4.4667, "angle": 18.49}, {"time": 4.7333, "angle": 24.07}, {"time": 5, "angle": 14.41}]}, "bone133": {"rotate": [{"time": 0, "angle": 24.42}, {"time": 0.2667, "angle": -7.34}, {"time": 0.5, "angle": 7.68}, {"time": 0.7333, "angle": 18.65}, {"time": 0.9667, "angle": -6.78}, {"time": 1.2333, "angle": -33.37}, {"time": 1.5, "angle": -30.09}, {"time": 1.7333, "angle": -1.46}, {"time": 1.9667, "angle": 9.42}, {"time": 2.2333, "angle": 12.21}, {"time": 2.5, "angle": 24.42}, {"time": 2.7667, "angle": -7.34}, {"time": 3, "angle": 7.68}, {"time": 3.2333, "angle": 18.65}, {"time": 3.4667, "angle": -6.78}, {"time": 3.7333, "angle": -33.37}, {"time": 4, "angle": -30.09}, {"time": 4.2333, "angle": -1.46}, {"time": 4.4667, "angle": 9.42}, {"time": 4.7333, "angle": 12.21}, {"time": 5, "angle": 24.42}]}, "bone134": {"rotate": [{"time": 0, "angle": 18.17}, {"time": 0.2667, "angle": 26.7}, {"time": 0.5, "angle": 59.97}, {"time": 0.7333, "angle": 26.7}, {"time": 0.9667, "angle": 24.56}, {"time": 1.2333, "angle": 23.35}, {"time": 1.5, "angle": 22.89}, {"time": 1.7333, "angle": 27.93}, {"time": 1.9667, "angle": 23.62}, {"time": 2.2333, "angle": 12.65}, {"time": 2.5, "angle": 18.17}, {"time": 2.7667, "angle": 26.7}, {"time": 3, "angle": 59.97}, {"time": 3.2333, "angle": 26.7}, {"time": 3.4667, "angle": 24.56}, {"time": 3.7333, "angle": 23.35}, {"time": 4, "angle": 22.89}, {"time": 4.2333, "angle": 27.93}, {"time": 4.4667, "angle": 23.62}, {"time": 4.7333, "angle": 12.65}, {"time": 5, "angle": 18.17}]}, "bone141": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": -0.16}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": -0.16}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": -0.16}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": -0.16}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": -0.16}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": -0.16}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": -0.16}, {"time": 5, "angle": 0}]}, "bone145": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 7.62}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": 7.62}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": 7.62}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": 7.62}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": 7.62}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": 7.62}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": 7.62}, {"time": 5, "angle": 0}]}, "bone144": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 0.17}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": 0.17}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": 0.17}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": 0.17}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": 0.17}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": 0.17}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": 0.17}, {"time": 5, "angle": 0}]}, "bone143": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": -1.82}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": -1.82}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": -1.82}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": -1.82}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": -1.82}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": -1.82}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": -1.82}, {"time": 5, "angle": 0}]}, "bone142": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": -3.29}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": -3.29}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": -3.29}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": -3.29}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": -3.29}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": -3.29}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": -3.29}, {"time": 5, "angle": 0}]}, "bone150": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 16.47}, {"time": 0.5, "angle": 0}, {"time": 0.7333, "angle": 16.47}, {"time": 1, "angle": 0}, {"time": 1.2333, "angle": 16.47}, {"time": 1.5, "angle": 0}, {"time": 1.7333, "angle": 16.47}, {"time": 2, "angle": 0}, {"time": 2.2333, "angle": 16.47}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": 16.47}, {"time": 3, "angle": 0}, {"time": 3.2333, "angle": 16.47}, {"time": 3.5, "angle": 0}, {"time": 3.7333, "angle": 16.47}, {"time": 4, "angle": 0}, {"time": 4.2333, "angle": 16.47}, {"time": 4.5, "angle": 0}, {"time": 4.7333, "angle": 16.47}, {"time": 5, "angle": 0}]}, "bone149": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -3.76}, {"time": 0.5, "angle": 0}, {"time": 0.7333, "angle": -3.76}, {"time": 1, "angle": 0}, {"time": 1.2333, "angle": -3.76}, {"time": 1.5, "angle": 0}, {"time": 1.7333, "angle": -3.76}, {"time": 2, "angle": 0}, {"time": 2.2333, "angle": -3.76}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": -3.76}, {"time": 3, "angle": 0}, {"time": 3.2333, "angle": -3.76}, {"time": 3.5, "angle": 0}, {"time": 3.7333, "angle": -3.76}, {"time": 4, "angle": 0}, {"time": 4.2333, "angle": -3.76}, {"time": 4.5, "angle": 0}, {"time": 4.7333, "angle": -3.76}, {"time": 5, "angle": 0}]}, "bone148": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -4}, {"time": 0.5, "angle": 0}, {"time": 0.7333, "angle": -4}, {"time": 1, "angle": 0}, {"time": 1.2333, "angle": -4}, {"time": 1.5, "angle": 0}, {"time": 1.7333, "angle": -4}, {"time": 2, "angle": 0}, {"time": 2.2333, "angle": -4}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": -4}, {"time": 3, "angle": 0}, {"time": 3.2333, "angle": -4}, {"time": 3.5, "angle": 0}, {"time": 3.7333, "angle": -4}, {"time": 4, "angle": 0}, {"time": 4.2333, "angle": -4}, {"time": 4.5, "angle": 0}, {"time": 4.7333, "angle": -4}, {"time": 5, "angle": 0}]}, "bone147": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -2.9}, {"time": 0.5, "angle": 0}, {"time": 0.7333, "angle": -2.9}, {"time": 1, "angle": 0}, {"time": 1.2333, "angle": -2.9}, {"time": 1.5, "angle": 0}, {"time": 1.7333, "angle": -2.9}, {"time": 2, "angle": 0}, {"time": 2.2333, "angle": -2.9}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": -2.9}, {"time": 3, "angle": 0}, {"time": 3.2333, "angle": -2.9}, {"time": 3.5, "angle": 0}, {"time": 3.7333, "angle": -2.9}, {"time": 4, "angle": 0}, {"time": 4.2333, "angle": -2.9}, {"time": 4.5, "angle": 0}, {"time": 4.7333, "angle": -2.9}, {"time": 5, "angle": 0}]}, "bone151": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 14.86}, {"time": 0.5, "angle": 0}, {"time": 0.7333, "angle": 14.86}, {"time": 1, "angle": 0}, {"time": 1.2333, "angle": 14.86}, {"time": 1.5, "angle": 0}, {"time": 1.7333, "angle": 14.86}, {"time": 2, "angle": 0}, {"time": 2.2333, "angle": 14.86}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": 14.86}, {"time": 3, "angle": 0}, {"time": 3.2333, "angle": 14.86}, {"time": 3.5, "angle": 0}, {"time": 3.7333, "angle": 14.86}, {"time": 4, "angle": 0}, {"time": 4.2333, "angle": 14.86}, {"time": 4.5, "angle": 0}, {"time": 4.7333, "angle": 14.86}, {"time": 5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.2333, "x": 18.63, "y": 24.72}, {"time": 0.5, "x": 0, "y": 0}, {"time": 0.7333, "x": 18.63, "y": 24.72}, {"time": 1, "x": 0, "y": 0}, {"time": 1.2333, "x": 18.63, "y": 24.72}, {"time": 1.5, "x": 0, "y": 0}, {"time": 1.7333, "x": 18.63, "y": 24.72}, {"time": 2, "x": 0, "y": 0}, {"time": 2.2333, "x": 18.63, "y": 24.72}, {"time": 2.5, "x": 0, "y": 0}, {"time": 2.7333, "x": 18.63, "y": 24.72}, {"time": 3, "x": 0, "y": 0}, {"time": 3.2333, "x": 18.63, "y": 24.72}, {"time": 3.5, "x": 0, "y": 0}, {"time": 3.7333, "x": 18.63, "y": 24.72}, {"time": 4, "x": 0, "y": 0}, {"time": 4.2333, "x": 18.63, "y": 24.72}, {"time": 4.5, "x": 0, "y": 0}, {"time": 4.7333, "x": 18.63, "y": 24.72}, {"time": 5, "x": 0, "y": 0}]}, "bone152": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 11.63}, {"time": 0.5, "angle": 0}, {"time": 0.7667, "angle": 11.63}, {"time": 1, "angle": 0}, {"time": 1.2667, "angle": 11.63}, {"time": 1.5, "angle": 0}, {"time": 1.7667, "angle": 11.63}, {"time": 2, "angle": 0}, {"time": 2.2667, "angle": 11.63}, {"time": 2.5, "angle": 0}, {"time": 2.7667, "angle": 11.63}, {"time": 3, "angle": 0}, {"time": 3.2667, "angle": 11.63}, {"time": 3.5, "angle": 0}, {"time": 3.7667, "angle": 11.63}, {"time": 4, "angle": 0}, {"time": 4.2667, "angle": 11.63}, {"time": 4.5, "angle": 0}, {"time": 4.7667, "angle": 11.63}, {"time": 5, "angle": 0}]}, "bone135": {"translate": [{"time": 0, "x": -12.53, "y": -46.57}]}, "bone136": {"rotate": [{"time": 0, "angle": -8.11}], "translate": [{"time": 0, "x": -13.67, "y": -38.45}], "scale": [{"time": 0, "x": 0.768, "y": 0.768}]}, "bone137": {"rotate": [{"time": 0, "angle": 27.37}, {"time": 0.2333, "angle": 25.91}, {"time": 0.5, "angle": 27.37}, {"time": 0.7333, "angle": 25.91}, {"time": 1, "angle": 27.37}, {"time": 1.2333, "angle": 25.91}, {"time": 1.5, "angle": 27.37}, {"time": 1.7333, "angle": 25.91}, {"time": 2, "angle": 27.37}, {"time": 2.2333, "angle": 25.91}, {"time": 2.5, "angle": 27.37}, {"time": 2.7333, "angle": 25.91}, {"time": 3, "angle": 27.37}, {"time": 3.2333, "angle": 25.91}, {"time": 3.5, "angle": 27.37}, {"time": 3.7333, "angle": 25.91}, {"time": 4, "angle": 27.37}, {"time": 4.2333, "angle": 25.91}, {"time": 4.5, "angle": 27.37}, {"time": 4.7333, "angle": 25.91}, {"time": 5, "angle": 27.37}]}, "bone138": {"rotate": [{"time": 0, "angle": -15.28}, {"time": 0.2333, "angle": -13.14}, {"time": 0.5, "angle": -15.28}, {"time": 0.7333, "angle": -13.14}, {"time": 1, "angle": -15.28}, {"time": 1.2333, "angle": -13.14}, {"time": 1.5, "angle": -15.28}, {"time": 1.7333, "angle": -13.14}, {"time": 2, "angle": -15.28}, {"time": 2.2333, "angle": -13.14}, {"time": 2.5, "angle": -15.28}, {"time": 2.7333, "angle": -13.14}, {"time": 3, "angle": -15.28}, {"time": 3.2333, "angle": -13.14}, {"time": 3.5, "angle": -15.28}, {"time": 3.7333, "angle": -13.14}, {"time": 4, "angle": -15.28}, {"time": 4.2333, "angle": -13.14}, {"time": 4.5, "angle": -15.28}, {"time": 4.7333, "angle": -13.14}, {"time": 5, "angle": -15.28}]}, "bone139": {"rotate": [{"time": 0, "angle": -7.27}, {"time": 0.2333, "angle": 14.86}, {"time": 0.5, "angle": -7.27}, {"time": 0.7333, "angle": 14.86}, {"time": 1, "angle": -7.27}, {"time": 1.2333, "angle": 14.86}, {"time": 1.5, "angle": -7.27}, {"time": 1.7333, "angle": 14.86}, {"time": 2, "angle": -7.27}, {"time": 2.2333, "angle": 14.86}, {"time": 2.5, "angle": -7.27}, {"time": 2.7333, "angle": 14.86}, {"time": 3, "angle": -7.27}, {"time": 3.2333, "angle": 14.86}, {"time": 3.5, "angle": -7.27}, {"time": 3.7333, "angle": 14.86}, {"time": 4, "angle": -7.27}, {"time": 4.2333, "angle": 14.86}, {"time": 4.5, "angle": -7.27}, {"time": 4.7333, "angle": 14.86}, {"time": 5, "angle": -7.27}]}, "bone153": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -6.28}, {"time": 0.4667, "angle": -10.84}, {"time": 0.7, "angle": -19.11}, {"time": 0.9667, "angle": -18.53}, {"time": 1.2, "angle": -19.01}, {"time": 1.4333, "angle": -25.32}, {"time": 1.6667, "angle": -31.56}, {"time": 1.9333, "angle": -31.93}, {"time": 2.2, "angle": -29.84}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": -6.28}, {"time": 2.9667, "angle": -10.84}, {"time": 3.2, "angle": -19.11}, {"time": 3.4667, "angle": -18.53}, {"time": 3.7, "angle": -19.01}, {"time": 3.9333, "angle": -25.32}, {"time": 4.1667, "angle": -31.56}, {"time": 4.4333, "angle": -31.93}, {"time": 4.7, "angle": -29.84}, {"time": 5, "angle": 0}]}, "bone157": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 18.5}, {"time": 0.4667, "angle": 6.67}, {"time": 0.7, "angle": -20.22}, {"time": 0.9667, "angle": -25.69}, {"time": 1.2, "angle": 4.67}, {"time": 1.4333, "angle": 15.35}, {"time": 1.6667, "angle": 24.92}, {"time": 1.9333, "angle": -1.78}, {"time": 2.2, "angle": -11.33}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": 18.5}, {"time": 2.9667, "angle": 6.67}, {"time": 3.2, "angle": -20.22}, {"time": 3.4667, "angle": -25.69}, {"time": 3.7, "angle": 4.67}, {"time": 3.9333, "angle": 15.35}, {"time": 4.1667, "angle": 24.92}, {"time": 4.4333, "angle": -1.78}, {"time": 4.7, "angle": -11.33}, {"time": 5, "angle": 0}]}, "bone156": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 8.48}, {"time": 0.4667, "angle": 20.86}, {"time": 0.7, "angle": -0.67}, {"time": 0.9667, "angle": 14.38}, {"time": 1.2, "angle": 5.91}, {"time": 1.4333, "angle": 10.22}, {"time": 1.6667, "angle": 13.75}, {"time": 1.9333, "angle": 14.48}, {"time": 2.2, "angle": 0.01}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": 8.48}, {"time": 2.9667, "angle": 20.86}, {"time": 3.2, "angle": -0.67}, {"time": 3.4667, "angle": 14.38}, {"time": 3.7, "angle": 5.91}, {"time": 3.9333, "angle": 10.22}, {"time": 4.1667, "angle": 13.75}, {"time": 4.4333, "angle": 14.48}, {"time": 4.7, "angle": 0.01}, {"time": 5, "angle": 0}]}, "bone155": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -0.79}, {"time": 0.4667, "angle": 11.07}, {"time": 0.7, "angle": 14.52}, {"time": 0.9667, "angle": 18.15}, {"time": 1.2, "angle": 17.04}, {"time": 1.4333, "angle": 16.2}, {"time": 1.6667, "angle": 14.15}, {"time": 1.9333, "angle": 16.96}, {"time": 2.2, "angle": 13.91}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": -0.79}, {"time": 2.9667, "angle": 11.07}, {"time": 3.2, "angle": 14.52}, {"time": 3.4667, "angle": 18.15}, {"time": 3.7, "angle": 17.04}, {"time": 3.9333, "angle": 16.2}, {"time": 4.1667, "angle": 14.15}, {"time": 4.4333, "angle": 16.96}, {"time": 4.7, "angle": 13.91}, {"time": 5, "angle": 0}]}, "bone154": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -6.97}, {"time": 0.4667, "angle": -6.48}, {"time": 0.7, "angle": 8.51}, {"time": 0.9667, "angle": 9.48}, {"time": 1.2, "angle": 9.1}, {"time": 1.4333, "angle": 7.73}, {"time": 1.6667, "angle": 3.49}, {"time": 1.9333, "angle": 4.73}, {"time": 2.2, "angle": 11.37}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": -6.97}, {"time": 2.9667, "angle": -6.48}, {"time": 3.2, "angle": 8.51}, {"time": 3.4667, "angle": 9.48}, {"time": 3.7, "angle": 9.1}, {"time": 3.9333, "angle": 7.73}, {"time": 4.1667, "angle": 3.49}, {"time": 4.4333, "angle": 4.73}, {"time": 4.7, "angle": 11.37}, {"time": 5, "angle": 0}]}, "bone162": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 4.36}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": 4.36}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": 4.36}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": 4.36}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": 4.36}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": 4.36}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": 4.36}, {"time": 5, "angle": 0}]}, "bone165": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 3.76}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": 3.76}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": 3.76}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": 3.76}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": 3.76}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": 3.76}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": 3.76}, {"time": 5, "angle": 0}]}, "bone164": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": -6.5}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": -6.5}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": -6.5}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": -6.5}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": -6.5}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": -6.5}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": -6.5}, {"time": 5, "angle": 0}]}, "bone163": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 3.55}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": 3.55}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": 3.55}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": 3.55}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": 3.55}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": 3.55}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": 3.55}, {"time": 5, "angle": 0}]}, "bone166": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 1.62}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": 1.62}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": 1.62}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": 1.62}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": 1.62}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": 1.62}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": 1.62}, {"time": 5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3667, "x": -3.07, "y": -0.31}, {"time": 0.7, "x": 0, "y": 0}, {"time": 1.0667, "x": -3.07, "y": -0.31}, {"time": 1.4333, "x": 0, "y": 0}, {"time": 1.8, "x": -3.07, "y": -0.31}, {"time": 2.1333, "x": 0, "y": 0}, {"time": 2.5, "x": -3.07, "y": -0.31}, {"time": 2.8667, "x": 0, "y": 0}, {"time": 3.2, "x": -3.07, "y": -0.31}, {"time": 3.5667, "x": 0, "y": 0}, {"time": 3.9333, "x": -3.07, "y": -0.31}, {"time": 4.3, "x": 0, "y": 0}, {"time": 4.6333, "x": -3.07, "y": -0.31}, {"time": 5, "x": 0, "y": 0}]}, "bone169": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": -9.72}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": -9.72}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": -9.72}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": -9.72}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": -9.72}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": -9.72}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": -9.72}, {"time": 5, "angle": 0}]}, "bone168": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 7.84}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": 7.84}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": 7.84}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": 7.84}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": 7.84}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": 7.84}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": 7.84}, {"time": 5, "angle": 0}]}, "bone167": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 4.66}, {"time": 0.7, "angle": 0}, {"time": 1.0667, "angle": 4.66}, {"time": 1.4333, "angle": 0}, {"time": 1.8, "angle": 4.66}, {"time": 2.1333, "angle": 0}, {"time": 2.5, "angle": 4.66}, {"time": 2.8667, "angle": 0}, {"time": 3.2, "angle": 4.66}, {"time": 3.5667, "angle": 0}, {"time": 3.9333, "angle": 4.66}, {"time": 4.3, "angle": 0}, {"time": 4.6333, "angle": 4.66}, {"time": 5, "angle": 0}]}, "bone119": {"rotate": [{"time": 0, "angle": 0}, {"time": 2.5, "angle": 8.02}, {"time": 5, "angle": 0}]}, "bone121": {"rotate": [{"time": 0, "angle": 0}, {"time": 2.5, "angle": -12.37}, {"time": 5, "angle": 0}]}, "bone120": {"rotate": [{"time": 0, "angle": 0}, {"time": 2.5, "angle": -26.55}, {"time": 5, "angle": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 2.5, "x": 1.281, "y": 1}, {"time": 5, "x": 1, "y": 1}]}, "bone113": {"rotate": [{"time": 0, "angle": 0}, {"time": 2.5, "angle": 8.35}, {"time": 5, "angle": 0}], "translate": [{"time": 0, "x": -3.64, "y": -1.61}]}, "bone159": {"scale": [{"time": 0.6667, "x": 1, "y": 1}, {"time": 0.9333, "x": 1.267, "y": 1.267}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}, {"time": 1.9333, "x": 1.267, "y": 1.267}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}, {"time": 2.9333, "x": 1.267, "y": 1.267}, {"time": 3, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.6667, "x": 1, "y": 1}, {"time": 3.9333, "x": 1.267, "y": 1.267}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.6667, "x": 1, "y": 1}, {"time": 4.9333, "x": 1.267, "y": 1.267}]}}, "deform": {"default": {"duongtank6": {"duongtank6": [{"time": 1.3333}, {"time": 1.4, "offset": 100, "vertices": [-3.46284, -0.67547, -4.85697, -0.95688, -4.27189, -2.41112, -4.87589, -0.53442, -3.37874, -2.1026, -3.93338, -0.60247, -2.36238, -0.90533, -2.52792, 0.09793, -0.86084, 0.03476, -0.7776, 0.37091, -0.19911, -0.2986, -0.30051, -0.19598, 0.45673, -0.51258, 0.21813, -0.65079, 0.86752, -0.33534, 0.66554, -0.64965, 0.84981, 0.12609, 0.83083, -0.21847, 0.05131, -0.41515, -0.11617, -0.40179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.67245, 0.06406, -2.18774, 0.02368, -3.09427, -0.01911, -3.13937, -0.24525, -2.45679, -0.20327, -0.83402, 0.06728, -0.05833, 0.43076, 0.51112, 0.85381, 0.40126, 0.75496, 0.09489, 0.53883, 0.00824, 0.52025], "curve": "stepped"}, {"time": 1.5333, "offset": 100, "vertices": [-3.46284, -0.67547, -4.85697, -0.95688, -4.27189, -2.41112, -4.87589, -0.53442, -3.37874, -2.1026, -3.93338, -0.60247, -2.36238, -0.90533, -2.52792, 0.09793, -0.86084, 0.03476, -0.7776, 0.37091, -0.19911, -0.2986, -0.30051, -0.19598, 0.45673, -0.51258, 0.21813, -0.65079, 0.86752, -0.33534, 0.66554, -0.64965, 0.84981, 0.12609, 0.83083, -0.21847, 0.05131, -0.41515, -0.11617, -0.40179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.67245, 0.06406, -2.18774, 0.02368, -3.09427, -0.01911, -3.13937, -0.24525, -2.45679, -0.20327, -0.83402, 0.06728, -0.05833, 0.43076, 0.51112, 0.85381, 0.40126, 0.75496, 0.09489, 0.53883, 0.00824, 0.52025]}, {"time": 1.6, "curve": "stepped"}, {"time": 3.5333}, {"time": 3.6, "offset": 100, "vertices": [-3.46284, -0.67547, -4.85697, -0.95688, -4.27189, -2.41112, -4.87589, -0.53442, -3.37874, -2.1026, -3.93338, -0.60247, -2.36238, -0.90533, -2.52792, 0.09793, -0.86084, 0.03476, -0.7776, 0.37091, -0.19911, -0.2986, -0.30051, -0.19598, 0.45673, -0.51258, 0.21813, -0.65079, 0.86752, -0.33534, 0.66554, -0.64965, 0.84981, 0.12609, 0.83083, -0.21847, 0.05131, -0.41515, -0.11617, -0.40179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.67245, 0.06406, -2.18774, 0.02368, -3.09427, -0.01911, -3.13937, -0.24525, -2.45679, -0.20327, -0.83402, 0.06728, -0.05833, 0.43076, 0.51112, 0.85381, 0.40126, 0.75496, 0.09489, 0.53883, 0.00824, 0.52025], "curve": "stepped"}, {"time": 3.7333, "offset": 100, "vertices": [-3.46284, -0.67547, -4.85697, -0.95688, -4.27189, -2.41112, -4.87589, -0.53442, -3.37874, -2.1026, -3.93338, -0.60247, -2.36238, -0.90533, -2.52792, 0.09793, -0.86084, 0.03476, -0.7776, 0.37091, -0.19911, -0.2986, -0.30051, -0.19598, 0.45673, -0.51258, 0.21813, -0.65079, 0.86752, -0.33534, 0.66554, -0.64965, 0.84981, 0.12609, 0.83083, -0.21847, 0.05131, -0.41515, -0.11617, -0.40179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.67245, 0.06406, -2.18774, 0.02368, -3.09427, -0.01911, -3.13937, -0.24525, -2.45679, -0.20327, -0.83402, 0.06728, -0.05833, 0.43076, 0.51112, 0.85381, 0.40126, 0.75496, 0.09489, 0.53883, 0.00824, 0.52025]}, {"time": 3.8}]}, "duongtank14": {"duongtank6": [{"time": 1.3333}, {"time": 1.4, "offset": 100, "vertices": [-3.46284, -0.67547, -4.85697, -0.95688, -4.27189, -2.41112, -4.87589, -0.53442, -3.37874, -2.1026, -3.93338, -0.60247, -2.36238, -0.90533, -2.52792, 0.09793, -0.86084, 0.03476, -0.7776, 0.37091, -0.19911, -0.2986, -0.30051, -0.19598, 0.45673, -0.51258, 0.21813, -0.65079, 0.86752, -0.33534, 0.66554, -0.64965, 0.84981, 0.12609, 0.83083, -0.21847, 0.05131, -0.41515, -0.11617, -0.40179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.67245, 0.06406, -2.18774, 0.02368, -3.09427, -0.01911, -3.13937, -0.24525, -2.45679, -0.20327, -0.83402, 0.06728, -0.05833, 0.43076, 0.51112, 0.85381, 0.40126, 0.75496, 0.09489, 0.53883, 0.00824, 0.52025], "curve": "stepped"}, {"time": 1.5333, "offset": 100, "vertices": [-3.46284, -0.67547, -4.85697, -0.95688, -4.27189, -2.41112, -4.87589, -0.53442, -3.37874, -2.1026, -3.93338, -0.60247, -2.36238, -0.90533, -2.52792, 0.09793, -0.86084, 0.03476, -0.7776, 0.37091, -0.19911, -0.2986, -0.30051, -0.19598, 0.45673, -0.51258, 0.21813, -0.65079, 0.86752, -0.33534, 0.66554, -0.64965, 0.84981, 0.12609, 0.83083, -0.21847, 0.05131, -0.41515, -0.11617, -0.40179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.67245, 0.06406, -2.18774, 0.02368, -3.09427, -0.01911, -3.13937, -0.24525, -2.45679, -0.20327, -0.83402, 0.06728, -0.05833, 0.43076, 0.51112, 0.85381, 0.40126, 0.75496, 0.09489, 0.53883, 0.00824, 0.52025]}, {"time": 1.6, "curve": "stepped"}, {"time": 3.5333}, {"time": 3.6, "offset": 100, "vertices": [-3.46284, -0.67547, -4.85697, -0.95688, -4.27189, -2.41112, -4.87589, -0.53442, -3.37874, -2.1026, -3.93338, -0.60247, -2.36238, -0.90533, -2.52792, 0.09793, -0.86084, 0.03476, -0.7776, 0.37091, -0.19911, -0.2986, -0.30051, -0.19598, 0.45673, -0.51258, 0.21813, -0.65079, 0.86752, -0.33534, 0.66554, -0.64965, 0.84981, 0.12609, 0.83083, -0.21847, 0.05131, -0.41515, -0.11617, -0.40179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.67245, 0.06406, -2.18774, 0.02368, -3.09427, -0.01911, -3.13937, -0.24525, -2.45679, -0.20327, -0.83402, 0.06728, -0.05833, 0.43076, 0.51112, 0.85381, 0.40126, 0.75496, 0.09489, 0.53883, 0.00824, 0.52025], "curve": "stepped"}, {"time": 3.7333, "offset": 100, "vertices": [-3.46284, -0.67547, -4.85697, -0.95688, -4.27189, -2.41112, -4.87589, -0.53442, -3.37874, -2.1026, -3.93338, -0.60247, -2.36238, -0.90533, -2.52792, 0.09793, -0.86084, 0.03476, -0.7776, 0.37091, -0.19911, -0.2986, -0.30051, -0.19598, 0.45673, -0.51258, 0.21813, -0.65079, 0.86752, -0.33534, 0.66554, -0.64965, 0.84981, 0.12609, 0.83083, -0.21847, 0.05131, -0.41515, -0.11617, -0.40179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.67245, 0.06406, -2.18774, 0.02368, -3.09427, -0.01911, -3.13937, -0.24525, -2.45679, -0.20327, -0.83402, 0.06728, -0.05833, 0.43076, 0.51112, 0.85381, 0.40126, 0.75496, 0.09489, 0.53883, 0.00824, 0.52025]}, {"time": 3.8}]}}}, "drawOrder": [{"time": 0, "offsets": [{"slot": "WildexpandBorderlight0", "offset": 27}, {"slot": "duongtank12", "offset": -5}, {"slot": "duongtank11", "offset": -5}, {"slot": "duongtank7", "offset": 2}]}, {"time": 2.2333, "offsets": [{"slot": "duongtank15", "offset": 29}, {"slot": "WildexpandBorderlight0", "offset": 27}, {"slot": "duongtank12", "offset": -6}, {"slot": "duongtank11", "offset": -5}, {"slot": "duongtank7", "offset": 2}]}]}, "MonkeyKing": {"slots": {"BGfire0": {"attachment": [{"time": 0, "name": null}]}, "WildexpandBorderlight0": {"attachment": [{"time": 0, "name": null}]}, "WildexpandBorderlight1": {"attachment": [{"time": 0, "name": null}]}, "batgioifull1": {"attachment": [{"time": 0, "name": null}]}, "batgioifull2": {"attachment": [{"time": 0, "name": null}]}, "batgioifull3": {"attachment": [{"time": 0, "name": null}]}, "batgioifull4": {"attachment": [{"time": 0, "name": null}]}, "batgioifull5": {"attachment": [{"time": 0, "name": null}]}, "batgioifull6": {"attachment": [{"time": 0, "name": null}]}, "batgioifull7": {"attachment": [{"time": 0, "name": null}]}, "batgioifull8": {"attachment": [{"time": 0, "name": null}]}, "batgioifull9": {"attachment": [{"time": 0, "name": null}]}, "bgtank": {"attachment": [{"time": 0, "name": null}]}, "bone102": {"attachment": [{"time": 0, "name": "bone102"}]}, "brush": {"attachment": [{"time": 0, "name": "brush"}]}, "chonmuccuocboard": {"attachment": [{"time": 0, "name": "chonmuccuocboard"}]}, "chonmuccuocboard2": {"attachment": [{"time": 0, "name": "chonmuccuocboard"}]}, "cloud": {"attachment": [{"time": 0, "name": "cloud"}]}, "duongtank1": {"attachment": [{"time": 0, "name": null}]}, "duongtank2": {"attachment": [{"time": 0, "name": null}]}, "duongtank3": {"attachment": [{"time": 0, "name": null}]}, "duongtank4": {"attachment": [{"time": 0, "name": null}]}, "duongtank5": {"attachment": [{"time": 0, "name": null}]}, "duongtank6": {"attachment": [{"time": 0, "name": null}]}, "duongtank7": {"attachment": [{"time": 0, "name": null}]}, "duongtank8": {"attachment": [{"time": 0, "name": null}]}, "duongtank9": {"attachment": [{"time": 0, "name": null}]}, "duongtank10": {"attachment": [{"time": 0, "name": null}]}, "duongtank11": {"attachment": [{"time": 0, "name": null}]}, "duongtank12": {"attachment": [{"time": 0, "name": null}]}, "duongtank13": {"attachment": [{"time": 0, "name": null}]}, "duongtank14": {"attachment": [{"time": 0, "name": null}]}, "duongtank15": {"attachment": [{"time": 0, "name": null}]}, "muccuoc0": {"attachment": [{"time": 0, "name": "muccuoc1"}]}, "satangfull1": {"attachment": [{"time": 0, "name": null}]}, "satangfull2": {"attachment": [{"time": 0, "name": null}]}, "satangfull3": {"attachment": [{"time": 0, "name": null}]}, "satangfull4": {"attachment": [{"time": 0, "name": null}]}, "satangfull5": {"attachment": [{"time": 0, "name": null}]}, "satangfull6": {"attachment": [{"time": 0, "name": null}]}, "satangfull7": {"attachment": [{"time": 0, "name": null}]}, "satangfull8": {"attachment": [{"time": 0, "name": null}]}, "satangfull9": {"attachment": [{"time": 0, "name": null}]}, "wild text": {"attachment": [{"time": 0, "name": null}]}, "wild text2": {"attachment": [{"time": 0, "name": null}]}}, "bones": {"bone2": {"rotate": [{"time": 0, "angle": 0}, {"time": 2.6667, "angle": -1.06}, {"time": 5, "angle": 0}]}, "bone3": {"rotate": [{"time": 0.5, "angle": 0}, {"time": 0.8333, "angle": 10.78}, {"time": 2.6667, "angle": 15.32}, {"time": 3, "angle": 0}, {"time": 3.6333, "angle": 1.97}, {"time": 5, "angle": 0}], "translate": [{"time": 3.6333, "x": 0, "y": 0}, {"time": 3.7333, "x": -1.49, "y": 0.24}, {"time": 3.8333, "x": 0, "y": 0}, {"time": 3.9333, "x": -1.49, "y": 0.24}, {"time": 4.0333, "x": 0, "y": 0}, {"time": 4.1333, "x": -1.49, "y": 0.24}, {"time": 4.2333, "x": 0, "y": 0}]}, "bone98": {"rotate": [{"time": 0, "angle": 20.52}, {"time": 0.6667, "angle": -8.75, "curve": "stepped"}, {"time": 1.1667, "angle": -8.75}, {"time": 1.3333, "angle": -24.55}, {"time": 1.6667, "angle": -21.13}, {"time": 1.8333, "angle": -7.25}, {"time": 2.1667, "angle": -7.85}, {"time": 2.3333, "angle": -21.78}, {"time": 2.6667, "angle": -8.75}, {"time": 3.3333, "angle": 20.52}, {"time": 4.1667, "angle": 15.71}, {"time": 5, "angle": 20.52}]}, "bone99": {"rotate": [{"time": 0, "angle": 59.3}, {"time": 0.6667, "angle": -0.7, "curve": "stepped"}, {"time": 2.6667, "angle": -0.7}, {"time": 3.3333, "angle": 59.3}, {"time": 4.1667, "angle": 54.49}, {"time": 5, "angle": 59.3}]}, "bone9": {"rotate": [{"time": 0, "angle": 62.9}, {"time": 0.4333, "angle": 39.96}, {"time": 0.6667, "angle": -21.23}, {"time": 1.1667, "angle": -35.08}, {"time": 1.6667, "angle": -21.23}, {"time": 2.1667, "angle": -35.08}, {"time": 2.6667, "angle": -21.23}, {"time": 3.3333, "angle": 62.9}, {"time": 4.1667, "angle": 67.53}, {"time": 5, "angle": 62.9}], "translate": [{"time": 0, "x": -6.46, "y": -6.57}, {"time": 0.4333, "x": -4.22, "y": -5.18}, {"time": 0.6667, "x": 6.46, "y": -5.65, "curve": "stepped"}, {"time": 2.6667, "x": 6.46, "y": -5.65}, {"time": 3.3333, "x": -6.46, "y": -6.57}]}, "bone10": {"rotate": [{"time": 0, "angle": -27.34}, {"time": 0.6667, "angle": -5.92, "curve": "stepped"}, {"time": 2.6667, "angle": -5.92}, {"time": 3.3333, "angle": -3.28}, {"time": 4.1667, "angle": 6.89}, {"time": 5, "angle": -27.34}]}, "bone8": {"rotate": [{"time": 0, "angle": -7.35}, {"time": 0.1667, "angle": -0.35}, {"time": 0.6667, "angle": 5.15}, {"time": 1.6667, "angle": 1.87, "curve": "stepped"}, {"time": 2.6667, "angle": 1.87}, {"time": 3.3333, "angle": -7.35}, {"time": 4.1667, "angle": -12.16}, {"time": 5, "angle": -7.35}]}, "bone11": {"rotate": [{"time": 4.1667, "angle": -4.81}]}, "bone76": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 1.63}, {"time": 0.5333, "angle": -2.85}, {"time": 0.7667, "angle": -0.65}, {"time": 1, "angle": 0}, {"time": 1.2667, "angle": 1.63}, {"time": 1.5333, "angle": -2.85}, {"time": 1.7667, "angle": -0.65}, {"time": 2.0333, "angle": 0}, {"time": 2.3, "angle": 1.63}, {"time": 2.5667, "angle": -2.85}, {"time": 2.8, "angle": -0.65}, {"time": 3.1, "angle": 0}, {"time": 3.3667, "angle": 1.63}, {"time": 3.6333, "angle": -2.85}, {"time": 3.8667, "angle": -0.65}, {"time": 4.0333, "angle": 0}, {"time": 4.3, "angle": 1.63}, {"time": 4.5667, "angle": -2.85}, {"time": 4.8, "angle": -0.65}, {"time": 5, "angle": 0}]}, "bone77": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 10.52}, {"time": 0.5333, "angle": 0.7}, {"time": 0.7667, "angle": -4.12}, {"time": 1, "angle": 0}, {"time": 1.2667, "angle": 10.52}, {"time": 1.5333, "angle": 0.7}, {"time": 1.7667, "angle": -4.12}, {"time": 2.0333, "angle": 0}, {"time": 2.3, "angle": 10.52}, {"time": 2.5667, "angle": 0.7}, {"time": 2.8, "angle": -4.12}, {"time": 3.1, "angle": 0}, {"time": 3.3667, "angle": 10.52}, {"time": 3.6333, "angle": 0.7}, {"time": 3.8667, "angle": -4.12}, {"time": 4.0333, "angle": 0}, {"time": 4.3, "angle": 10.52}, {"time": 4.5667, "angle": 0.7}, {"time": 4.8, "angle": -4.12}, {"time": 5, "angle": 0}]}, "bone72": {"rotate": [{"time": 0, "angle": 4.65}, {"time": 0.4333, "angle": -4.82}, {"time": 0.9, "angle": -3.4}, {"time": 1.2667, "angle": 6.52}, {"time": 1.6, "angle": 7.04}, {"time": 1.9667, "angle": -0.24}, {"time": 2.3667, "angle": -4.16}, {"time": 2.8, "angle": -3.11}, {"time": 3.1667, "angle": 4.91}, {"time": 3.5, "angle": 6.44}, {"time": 3.8, "angle": 1.34}, {"time": 4.1, "angle": -8.05}, {"time": 4.3333, "angle": -3.67}, {"time": 4.6333, "angle": -0.42}, {"time": 5, "angle": 4.65}]}, "bone75": {"rotate": [{"time": 0, "angle": 0.56}, {"time": 0.4333, "angle": -4.53}, {"time": 0.9, "angle": -3.48}, {"time": 1.2667, "angle": -6.43}, {"time": 1.6, "angle": -6.19}, {"time": 1.9667, "angle": -4}, {"time": 2.3667, "angle": -1.83}, {"time": 2.8, "angle": -11.07}, {"time": 3.1667, "angle": -12.26}, {"time": 3.5, "angle": -4.01}, {"time": 3.8, "angle": -3.33}, {"time": 4.1, "angle": 5.02}, {"time": 4.3333, "angle": -1.87}, {"time": 4.6333, "angle": -8.46}, {"time": 5, "angle": 0.56}]}, "bone74": {"rotate": [{"time": 0, "angle": 8.24}, {"time": 0.4333, "angle": 7.12}, {"time": 0.9, "angle": -6.64}, {"time": 1.2667, "angle": -10.3}, {"time": 1.6, "angle": 2.46}, {"time": 1.9667, "angle": 5.62}, {"time": 2.3667, "angle": 5.1}, {"time": 2.8, "angle": -3.46}, {"time": 3.1667, "angle": -10.45}, {"time": 3.5, "angle": 2.73}, {"time": 3.8, "angle": 6.3}, {"time": 4.1, "angle": 0.78}, {"time": 4.3333, "angle": 2.84}, {"time": 4.6333, "angle": -6.15}, {"time": 5, "angle": 8.24}]}, "bone73": {"rotate": [{"time": 0, "angle": -0.8}, {"time": 0.4333, "angle": 2.92}, {"time": 0.9, "angle": -5.28}, {"time": 1.2667, "angle": -8.54}, {"time": 1.6, "angle": -2.07}, {"time": 1.9667, "angle": -1.84}, {"time": 2.3667, "angle": -5.47}, {"time": 2.8, "angle": -6.08}, {"time": 3.1667, "angle": -1.99}, {"time": 3.5, "angle": -0.4}, {"time": 3.8, "angle": -0.64}, {"time": 4.1, "angle": 8.74}, {"time": 4.3333, "angle": -5.34}, {"time": 4.6333, "angle": -6.01}, {"time": 5, "angle": -0.8}]}, "bone5": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3333, "angle": 6.95}, {"time": 2.3333, "angle": 6.12}, {"time": 2.6667, "angle": 0}]}, "bone6": {"rotate": [{"time": 0.6667, "angle": 0}, {"time": 1, "angle": 4.44}, {"time": 1.1667, "angle": -3.55}, {"time": 1.6667, "angle": 4.44}, {"time": 1.8333, "angle": -3.55}, {"time": 2.6667, "angle": -0.75}, {"time": 5, "angle": 0}], "scale": [{"time": 0, "x": 0.863, "y": 0.863}]}, "bone4": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3333, "angle": -31.19, "curve": "stepped"}, {"time": 2.5, "angle": -31.19}, {"time": 2.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": 12.81, "y": -7.01, "curve": "stepped"}, {"time": 2.5, "x": 12.81, "y": -7.01}, {"time": 2.8333, "x": 0, "y": 0}, {"time": 3.9, "x": -4.51, "y": -1.36}, {"time": 5, "x": 0, "y": 0}]}, "bone102": {"translate": [{"time": 0, "x": 0, "y": -37.49}, {"time": 0.6333, "x": 0, "y": -56.42}, {"time": 1.2333, "x": 0, "y": -23.61}, {"time": 2.5, "x": 0, "y": -37.49}, {"time": 3.7333, "x": 0, "y": -22.27}, {"time": 5, "x": 0, "y": -37.49}]}, "bone42": {"rotate": [{"time": 0, "angle": -34.35}, {"time": 0.3333, "angle": -38.77}, {"time": 0.6667, "angle": -38.03}, {"time": 1.4333, "angle": -33.34}, {"time": 2.5, "angle": -16.72}, {"time": 2.9333, "angle": -20.88}, {"time": 3.2667, "angle": -29.98}, {"time": 3.6, "angle": -36.73}, {"time": 4, "angle": -28.46}, {"time": 5, "angle": -34.35}]}, "bone43": {"rotate": [{"time": 0, "angle": 0.7}, {"time": 0.3333, "angle": 4.59}, {"time": 0.6667, "angle": -6.66}, {"time": 1.4333, "angle": -14.86}, {"time": 2.5, "angle": -17.91}, {"time": 2.9333, "angle": -6.34}, {"time": 3.2667, "angle": -13.42}, {"time": 3.6, "angle": -4.54}, {"time": 4, "angle": -12.17}, {"time": 5, "angle": 0.7}]}, "bone44": {"rotate": [{"time": 0, "angle": 0.73}, {"time": 0.3333, "angle": -5.63}, {"time": 0.6667, "angle": -13.82}, {"time": 1.4333, "angle": -9.61}, {"time": 2.5, "angle": -7}, {"time": 2.9333, "angle": -11.59}, {"time": 3.2667, "angle": -21.73}, {"time": 3.6, "angle": -19.19}, {"time": 4, "angle": -22.22}, {"time": 5, "angle": 0.73}]}, "bone45": {"rotate": [{"time": 0, "angle": -0.22}, {"time": 0.3333, "angle": -1.27}, {"time": 0.6667, "angle": 10.53}, {"time": 1.4333, "angle": 4.75}, {"time": 2.5, "angle": 6.37}, {"time": 2.9333, "angle": -6.87}, {"time": 3.2667, "angle": -4.54}, {"time": 3.6, "angle": -0.15}, {"time": 4, "angle": 6.88}, {"time": 5, "angle": -0.22}]}, "bone46": {"rotate": [{"time": 0, "angle": 17.77}, {"time": 0.3333, "angle": 22.69}, {"time": 0.6667, "angle": 19.15}, {"time": 1.4333, "angle": 13.62}, {"time": 2.5, "angle": 11.82}, {"time": 2.9333, "angle": 4.6}, {"time": 3.2667, "angle": 19.35}, {"time": 3.6, "angle": 19.22}, {"time": 4, "angle": 17.88}, {"time": 5, "angle": 17.77}]}, "bone47": {"rotate": [{"time": 0, "angle": 7.47}, {"time": 0.3333, "angle": 9.96}, {"time": 0.6667, "angle": 7.43}, {"time": 1.4333, "angle": 17.96}, {"time": 2.5, "angle": 10.12}, {"time": 2.9333, "angle": 8.59}, {"time": 3.2667, "angle": 8.29}, {"time": 3.6, "angle": 9.55}, {"time": 4, "angle": 16.9}, {"time": 5, "angle": 7.47}]}, "bone48": {"rotate": [{"time": 0, "angle": -1.53}, {"time": 0.3333, "angle": 5.28}, {"time": 0.6667, "angle": 10.17}, {"time": 1.4333, "angle": 7.18}, {"time": 2.5, "angle": 2.73}, {"time": 2.9333, "angle": -0.36}, {"time": 3.2667, "angle": 7.95}, {"time": 3.6, "angle": 2.92}, {"time": 4, "angle": 7.03}, {"time": 4.3333, "angle": 12.69}, {"time": 5, "angle": -1.53}]}, "bone49": {"rotate": [{"time": 0, "angle": -1.99}, {"time": 0.3333, "angle": -2.71}, {"time": 0.6667, "angle": 9.69}, {"time": 1.4333, "angle": 9.68}, {"time": 2.5, "angle": -0.09}, {"time": 2.9333, "angle": 9.16}, {"time": 3.2667, "angle": 8.43}, {"time": 3.6, "angle": -7.66}, {"time": 4, "angle": 9.97}, {"time": 4.3333, "angle": 10.7}, {"time": 5, "angle": -1.99}]}, "bone50": {"rotate": [{"time": 0, "angle": -1.87}, {"time": 0.3333, "angle": 3.35}, {"time": 0.6667, "angle": 11.14}, {"time": 1.4333, "angle": 6.11}, {"time": 2.5, "angle": 12.96}, {"time": 2.9333, "angle": 9.11}, {"time": 3.2667, "angle": 3.23}, {"time": 3.6, "angle": 0.23}, {"time": 4, "angle": 4.1}, {"time": 4.3333, "angle": 14.42}, {"time": 5, "angle": -1.87}]}, "bone51": {"rotate": [{"time": 0, "angle": -2.23}, {"time": 0.3333, "angle": 2.62}, {"time": 0.6667, "angle": 4.12}, {"time": 1.4333, "angle": 5.31}, {"time": 1.9, "angle": 5.78}, {"time": 2.5, "angle": -0.12}, {"time": 2.9333, "angle": 2.86}, {"time": 3.2667, "angle": -1.33}, {"time": 3.6, "angle": -3.06}, {"time": 4, "angle": 3.32}, {"time": 4.3333, "angle": 5.73}, {"time": 5, "angle": -2.23}]}, "bone52": {"rotate": [{"time": 0, "angle": -2.14}, {"time": 0.3333, "angle": -6.67}, {"time": 0.6667, "angle": 3.51}, {"time": 1.4333, "angle": 4.53}, {"time": 1.9, "angle": 10.17}, {"time": 2.5, "angle": 9.2}, {"time": 2.9333, "angle": 12.93}, {"time": 3.2667, "angle": 1.22}, {"time": 3.6, "angle": -0.6}, {"time": 4, "angle": 1.79}, {"time": 4.3333, "angle": 11.54}, {"time": 5, "angle": -2.14}]}, "bone53": {"rotate": [{"time": 0, "angle": -3.09}, {"time": 0.3333, "angle": -6.97}, {"time": 0.6667, "angle": -1.81}, {"time": 1.4333, "angle": 0.05}, {"time": 1.9, "angle": 3.65}, {"time": 2.5, "angle": 1.72}, {"time": 2.9333, "angle": -0.89}, {"time": 3.2667, "angle": -0.39}, {"time": 3.6, "angle": -4.67}, {"time": 4, "angle": -18.28}, {"time": 4.3333, "angle": 2.46}, {"time": 5, "angle": -3.09}]}, "bone54": {"rotate": [{"time": 0, "angle": -2.99}, {"time": 0.3333, "angle": -7.58}, {"time": 0.6667, "angle": -2.41}, {"time": 1.4333, "angle": 6.57}, {"time": 1.9, "angle": 9.75}, {"time": 2.5, "angle": 4.03}, {"time": 2.9333, "angle": 10.37}, {"time": 3.2667, "angle": 4.09}, {"time": 3.6, "angle": -4.43}, {"time": 4, "angle": -16.56}, {"time": 4.3333, "angle": 3.2}, {"time": 4.6333, "angle": 6.19}, {"time": 5, "angle": -2.99}]}, "bone55": {"rotate": [{"time": 0, "angle": -4.23}, {"time": 0.3333, "angle": -6.14}, {"time": 0.6667, "angle": -6.94}, {"time": 1.4333, "angle": 0.72}, {"time": 2.5, "angle": -0.71}, {"time": 2.9333, "angle": 5.01}, {"time": 3.2667, "angle": -3.82}, {"time": 3.6, "angle": -9.45}, {"time": 4, "angle": -16.95}, {"time": 4.3333, "angle": 0.88}, {"time": 4.6333, "angle": 11.68}, {"time": 5, "angle": -4.23}]}, "bone56": {"rotate": [{"time": 0, "angle": 2.64}, {"time": 0.3333, "angle": -9.13}, {"time": 0.6667, "angle": -4.45}, {"time": 1.4333, "angle": 3.25}, {"time": 2.5, "angle": 6.02}, {"time": 2.9333, "angle": 10.39}, {"time": 3.2667, "angle": -6.25}, {"time": 3.6, "angle": -13.85}, {"time": 4, "angle": -13.52}, {"time": 4.6333, "angle": 2.7}, {"time": 5, "angle": 2.64}]}, "bone57": {"rotate": [{"time": 0, "angle": -20.63}, {"time": 0.3333, "angle": -12.39}, {"time": 0.6667, "angle": -14.86}, {"time": 1.4333, "angle": -5.44}, {"time": 2.5, "angle": 0.2}, {"time": 2.9333, "angle": -5.11}, {"time": 3.2667, "angle": -5.31}, {"time": 3.6, "angle": -4.09}, {"time": 4, "angle": -4.52}, {"time": 4.6333, "angle": 3.09}, {"time": 5, "angle": -20.63}]}, "bone36": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5667, "angle": 8.54}, {"time": 1.1333, "angle": 8.82}, {"time": 3, "angle": 6.29}, {"time": 5, "angle": 0}]}, "bone40": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5667, "angle": -3.47}, {"time": 1.1333, "angle": -2.92}, {"time": 3, "angle": -4.9}, {"time": 5, "angle": 0}]}, "bone39": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5667, "angle": -5.78}, {"time": 1.1333, "angle": 5.94}, {"time": 3, "angle": 2.91}, {"time": 5, "angle": 0}]}, "bone38": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5667, "angle": 2.04}, {"time": 1.1333, "angle": 5.68}, {"time": 3, "angle": 6.44}, {"time": 5, "angle": 0}]}, "bone37": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5667, "angle": 7.26}, {"time": 1.1333, "angle": 7.93}, {"time": 3, "angle": 6.33}, {"time": 5, "angle": 0}]}, "bone58": {"rotate": [{"time": 0, "angle": -0.92}, {"time": 0.3667, "angle": 11.9}, {"time": 0.7333, "angle": -3.4}, {"time": 1.1, "angle": 1.41}, {"time": 1.4333, "angle": 13.35}, {"time": 1.7667, "angle": 14.22}, {"time": 2.1333, "angle": 28.45}, {"time": 2.4667, "angle": -0.55}, {"time": 2.8333, "angle": 12.08}, {"time": 3.2, "angle": -3.52}, {"time": 3.5667, "angle": 1.41}, {"time": 3.9, "angle": 13.35}, {"time": 4.2333, "angle": 20.3}, {"time": 4.6, "angle": 28.45}, {"time": 5, "angle": -0.92}], "scale": [{"time": 0, "x": 0.931, "y": 1}, {"time": 1.1, "x": 0.96, "y": 0.893}, {"time": 2.1333, "x": 0.876, "y": 1}, {"time": 3.2, "x": 0.928, "y": 0.888}, {"time": 4.1667, "x": 0.887, "y": 0.93}, {"time": 5, "x": 0.931, "y": 1}]}, "bone61": {"rotate": [{"time": 0, "angle": 18.72}, {"time": 0.3667, "angle": 5.63}, {"time": 0.7333, "angle": 15.06}, {"time": 1.1, "angle": -13.03}, {"time": 1.4333, "angle": -16.67}, {"time": 1.7667, "angle": -1.2}, {"time": 2.1333, "angle": 4.54}, {"time": 2.4667, "angle": 20.64}, {"time": 2.8333, "angle": -16.06}, {"time": 3.2, "angle": 1.25}, {"time": 3.5667, "angle": -13.03}, {"time": 3.9, "angle": -16.67}, {"time": 4.2333, "angle": -10.69}, {"time": 4.6, "angle": 4.54}, {"time": 5, "angle": 18.72}], "scale": [{"time": 0, "x": 0.931, "y": 1}, {"time": 1.1, "x": 0.96, "y": 0.893}, {"time": 2.1333, "x": 0.876, "y": 1}, {"time": 3.2, "x": 0.928, "y": 0.888}, {"time": 4.1667, "x": 0.887, "y": 0.93}, {"time": 5, "x": 0.931, "y": 1}]}, "bone60": {"rotate": [{"time": 0, "angle": -1.87}, {"time": 0.3667, "angle": -8.17}, {"time": 0.7333, "angle": -6.74}, {"time": 1.1, "angle": -12.35}, {"time": 1.4333, "angle": -23.59}, {"time": 1.7667, "angle": -14.88}, {"time": 2.1333, "angle": -9.4}, {"time": 2.4667, "angle": 0.04}, {"time": 2.8333, "angle": -17.5}, {"time": 3.2, "angle": -7.25}, {"time": 3.5667, "angle": -12.35}, {"time": 3.9, "angle": -23.59}, {"time": 4.2333, "angle": -18.79}, {"time": 4.6, "angle": -9.4}, {"time": 5, "angle": -1.87}], "scale": [{"time": 0, "x": 0.931, "y": 1}, {"time": 1.1, "x": 0.96, "y": 0.893}, {"time": 2.1333, "x": 0.876, "y": 1}, {"time": 3.2, "x": 0.928, "y": 0.888}, {"time": 4.1667, "x": 0.887, "y": 0.93}, {"time": 5, "x": 0.931, "y": 1}]}, "bone59": {"rotate": [{"time": 0, "angle": -1.12}, {"time": 0.3667, "angle": -6.35}, {"time": 0.7333, "angle": 0.15}, {"time": 1.1, "angle": 1.32}, {"time": 1.4333, "angle": -2.17}, {"time": 1.7667, "angle": -1.51}, {"time": 2.1333, "angle": -4.89}, {"time": 2.4667, "angle": -0.56}, {"time": 2.8333, "angle": 4.25}, {"time": 3.2, "angle": 0.02}, {"time": 3.5667, "angle": 1.32}, {"time": 3.9, "angle": -2.17}, {"time": 4.2333, "angle": -2.3}, {"time": 4.6, "angle": -4.89}, {"time": 5, "angle": -1.12}], "scale": [{"time": 0, "x": 0.931, "y": 1}, {"time": 1.1, "x": 0.96, "y": 0.893}, {"time": 2.1333, "x": 0.876, "y": 1}, {"time": 3.2, "x": 0.928, "y": 0.888}, {"time": 4.1667, "x": 0.887, "y": 0.93}, {"time": 5, "x": 0.931, "y": 1}]}, "bone63": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 32.72}, {"time": 0.8, "angle": 31.59}, {"time": 1.1, "angle": 14.57}, {"time": 1.4333, "angle": 28.39}, {"time": 1.7333, "angle": 30.84}, {"time": 2.1333, "angle": 8.12}, {"time": 2.5667, "angle": 0}, {"time": 2.9333, "angle": 32.72}, {"time": 3.3333, "angle": 31.83}, {"time": 3.6667, "angle": 10.94}, {"time": 4, "angle": 28.67}, {"time": 4.3, "angle": 30.84}, {"time": 4.7, "angle": 8.12}, {"time": 5, "angle": 0}]}, "bone66": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 11.08}, {"time": 0.8, "angle": 50.75}, {"time": 1.1, "angle": 3.2}, {"time": 1.4333, "angle": 22.9}, {"time": 1.7333, "angle": -1.72}, {"time": 2.1333, "angle": 16.76}, {"time": 2.5667, "angle": 0}, {"time": 2.9333, "angle": 11.08}, {"time": 3.3333, "angle": 14.5}, {"time": 3.6667, "angle": 29.48}, {"time": 4, "angle": 29.26}, {"time": 4.3, "angle": -1.72}, {"time": 4.7, "angle": 16.76}, {"time": 5, "angle": 0}]}, "bone65": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": -11.12}, {"time": 0.8, "angle": -12.77}, {"time": 1.1, "angle": 10.51}, {"time": 1.4333, "angle": -8.89}, {"time": 1.7333, "angle": -12.81}, {"time": 2.1333, "angle": 0.45}, {"time": 2.5667, "angle": 0}, {"time": 2.9333, "angle": -11.12}, {"time": 3.3333, "angle": -4.11}, {"time": 3.6667, "angle": 9.13}, {"time": 4, "angle": -6.52}, {"time": 4.3, "angle": -12.81}, {"time": 4.7, "angle": 0.45}, {"time": 5, "angle": 0}]}, "bone64": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": -20.31}, {"time": 0.8, "angle": -17.91}, {"time": 1.1, "angle": -1.21}, {"time": 1.4333, "angle": -22.91}, {"time": 1.7333, "angle": -19.8}, {"time": 2.1333, "angle": -22.13}, {"time": 2.5667, "angle": 0}, {"time": 2.9333, "angle": -20.31}, {"time": 3.3333, "angle": -13.13}, {"time": 3.6667, "angle": -5.94}, {"time": 4, "angle": -22.06}, {"time": 4.3, "angle": -19.8}, {"time": 4.7, "angle": -22.13}, {"time": 5, "angle": 0}]}, "bone78": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -4.45}, {"time": 0.5, "angle": 0}, {"time": 0.7333, "angle": -4.45}, {"time": 1, "angle": 0}, {"time": 1.2333, "angle": -4.45}, {"time": 1.5, "angle": 0}, {"time": 1.7333, "angle": -4.45}, {"time": 2, "angle": 0}, {"time": 2.2333, "angle": -4.45}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": -4.45}, {"time": 3, "angle": 0}, {"time": 3.2333, "angle": -4.45}, {"time": 3.5, "angle": 0}, {"time": 3.7333, "angle": -4.45}, {"time": 4, "angle": 0}, {"time": 4.2333, "angle": -4.45}, {"time": 4.5, "angle": 0}, {"time": 4.7333, "angle": -4.45}, {"time": 5, "angle": 0}]}, "bone84": {"rotate": [{"time": 0, "angle": -9.75, "curve": "stepped"}, {"time": 0.7667, "angle": -9.75}, {"time": 0.9333, "angle": 4.7, "curve": "stepped"}, {"time": 2.3333, "angle": 4.7}, {"time": 3, "angle": -9.75}], "translate": [{"time": 0, "x": -3.44, "y": 0.81, "curve": "stepped"}, {"time": 0.7667, "x": -3.44, "y": 0.81}, {"time": 0.9333, "x": -1.27, "y": 0.5, "curve": "stepped"}, {"time": 2.3333, "x": -1.27, "y": 0.5}, {"time": 3, "x": -3.44, "y": 0.81}]}, "bone88": {"rotate": [{"time": 0, "angle": 20.8}, {"time": 0.3, "angle": 8.8}, {"time": 0.6333, "angle": -5.74}, {"time": 0.9333, "angle": -0.94}, {"time": 1.2, "angle": -2.21}, {"time": 1.4667, "angle": 6.04}, {"time": 1.7333, "angle": 7.22}, {"time": 2, "angle": 20.8}, {"time": 2.3, "angle": 8.8}, {"time": 2.6333, "angle": -5.74}, {"time": 2.9333, "angle": -0.94}, {"time": 3.2, "angle": -2.21}, {"time": 3.4667, "angle": 6.04}, {"time": 3.7333, "angle": 7.22}, {"time": 4.0333, "angle": -5.74}, {"time": 4.3333, "angle": -0.94}, {"time": 4.6, "angle": -2.21}, {"time": 5, "angle": 20.8}]}, "bone89": {"rotate": [{"time": 0, "angle": -8.33}, {"time": 0.3, "angle": -5.85}, {"time": 0.6333, "angle": 9.42}, {"time": 0.9333, "angle": 14.85}, {"time": 1.2, "angle": 10.04}, {"time": 1.4667, "angle": 6.63}, {"time": 1.7333, "angle": 5.83}, {"time": 2, "angle": -8.33}, {"time": 2.3, "angle": -5.85}, {"time": 2.6333, "angle": 9.42}, {"time": 2.9333, "angle": 14.85}, {"time": 3.2, "angle": 10.04}, {"time": 3.4667, "angle": 6.63}, {"time": 3.7333, "angle": 5.83}, {"time": 4.0333, "angle": 9.42}, {"time": 4.3333, "angle": 14.85}, {"time": 4.6, "angle": 10.04}, {"time": 5, "angle": -8.33}]}, "bone90": {"rotate": [{"time": 0, "angle": 2.64}, {"time": 0.3, "angle": 21.14}, {"time": 0.6333, "angle": 1.1}, {"time": 0.9333, "angle": 16.08}, {"time": 1.2, "angle": 3.78}, {"time": 1.4667, "angle": -4.4}, {"time": 1.7333, "angle": 17.2}, {"time": 2, "angle": 2.64}, {"time": 2.3, "angle": 21.14}, {"time": 2.6333, "angle": 1.1}, {"time": 2.9333, "angle": 16.08}, {"time": 3.2, "angle": 3.78}, {"time": 3.4667, "angle": -4.4}, {"time": 3.7333, "angle": 17.2}, {"time": 4.0333, "angle": 1.1}, {"time": 4.3333, "angle": 16.08}, {"time": 4.6, "angle": 3.78}, {"time": 5, "angle": 2.64}]}, "bone94": {"rotate": [{"time": 0, "angle": -0.85}, {"time": 0.2333, "angle": -16.33}, {"time": 0.5, "angle": -8.15}, {"time": 0.7667, "angle": 14.26}, {"time": 1, "angle": 21.87}, {"time": 1.3333, "angle": -0.85}, {"time": 1.6333, "angle": -16.33}, {"time": 1.9, "angle": -8.15}, {"time": 2.1667, "angle": 14.26}, {"time": 2.4, "angle": 21.87}, {"time": 2.6, "angle": -0.85}, {"time": 2.8, "angle": -16.33}, {"time": 3.0667, "angle": -8.15}, {"time": 3.3333, "angle": 14.26}, {"time": 3.5667, "angle": 21.87}, {"time": 3.9, "angle": -0.85}, {"time": 4.2, "angle": -16.33}, {"time": 4.4667, "angle": -8.15}, {"time": 4.7333, "angle": 14.26}, {"time": 5, "angle": -0.85}]}, "bone92": {"rotate": [{"time": 0, "angle": -28.11}, {"time": 0.2333, "angle": -15.82}, {"time": 0.5, "angle": -2.84}, {"time": 0.7667, "angle": -6.74}, {"time": 1, "angle": -8.21}, {"time": 1.3333, "angle": -28.11}, {"time": 1.6333, "angle": -15.82}, {"time": 1.9, "angle": -2.84}, {"time": 2.1667, "angle": -6.74}, {"time": 2.4, "angle": -8.21}, {"time": 2.6, "angle": -28.11}, {"time": 2.8, "angle": -15.82}, {"time": 3.0667, "angle": -2.84}, {"time": 3.3333, "angle": -6.74}, {"time": 3.5667, "angle": -8.21}, {"time": 3.9, "angle": -28.11}, {"time": 4.2, "angle": -15.82}, {"time": 4.4667, "angle": -2.84}, {"time": 4.7333, "angle": -6.74}, {"time": 5, "angle": -28.11}]}, "bone93": {"rotate": [{"time": 0, "angle": 24.2}, {"time": 0.2333, "angle": 20.19}, {"time": 0.5, "angle": 4.79}, {"time": 0.7667, "angle": 2.21}, {"time": 1, "angle": -0.88}, {"time": 1.3333, "angle": 24.2}, {"time": 1.6333, "angle": 20.19}, {"time": 1.9, "angle": 4.79}, {"time": 2.1667, "angle": 2.21}, {"time": 2.4, "angle": -0.88}, {"time": 2.6, "angle": 24.2}, {"time": 2.8, "angle": 20.19}, {"time": 3.0667, "angle": 4.79}, {"time": 3.3333, "angle": 2.21}, {"time": 3.5667, "angle": -0.88}, {"time": 3.9, "angle": 24.2}, {"time": 4.2, "angle": 20.19}, {"time": 4.4667, "angle": 4.79}, {"time": 4.7333, "angle": 2.21}, {"time": 5, "angle": 24.2}]}, "bone100": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -5.01}, {"time": 0.3333, "angle": 0}, {"time": 0.5, "angle": -5.01}, {"time": 0.6667, "angle": 0}, {"time": 0.8333, "angle": -5.01}, {"time": 1, "angle": 0}, {"time": 1.1667, "angle": -5.01}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": -5.01}, {"time": 1.6667, "angle": 0}, {"time": 1.8333, "angle": -5.01}, {"time": 2, "angle": 0}, {"time": 2.1667, "angle": -5.01}, {"time": 2.3333, "angle": 0}, {"time": 2.5, "angle": -5.01}, {"time": 2.6667, "angle": 0}, {"time": 2.8333, "angle": -5.01}, {"time": 3, "angle": 0}, {"time": 3.1667, "angle": -5.01}, {"time": 3.3333, "angle": 0}, {"time": 3.5, "angle": -5.01}, {"time": 3.6667, "angle": 0}, {"time": 3.8333, "angle": -5.01}, {"time": 4, "angle": 0}, {"time": 4.1667, "angle": -5.01}, {"time": 4.3333, "angle": 0}, {"time": 4.5, "angle": -5.01}, {"time": 4.6667, "angle": 0}, {"time": 4.8333, "angle": -5.01}, {"time": 5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1667, "x": 1.51, "y": -2.7}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5, "x": 1.51, "y": -2.7}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 0.8333, "x": 1.51, "y": -2.7}, {"time": 1, "x": 0, "y": 0}, {"time": 1.1667, "x": 1.51, "y": -2.7}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 1.5, "x": 1.51, "y": -2.7}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.8333, "x": 1.51, "y": -2.7}, {"time": 2, "x": 0, "y": 0}, {"time": 2.1667, "x": 1.51, "y": -2.7}, {"time": 2.3333, "x": 0, "y": 0}, {"time": 2.5, "x": 1.51, "y": -2.7}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 2.8333, "x": 1.51, "y": -2.7}, {"time": 3, "x": 0, "y": 0}, {"time": 3.1667, "x": 1.51, "y": -2.7}, {"time": 3.3333, "x": 0, "y": 0}, {"time": 3.5, "x": 1.51, "y": -2.7}, {"time": 3.6667, "x": 0, "y": 0}, {"time": 3.8333, "x": 1.51, "y": -2.7}, {"time": 4, "x": 0, "y": 0}, {"time": 4.1667, "x": 1.51, "y": -2.7}, {"time": 4.3333, "x": 0, "y": 0}, {"time": 4.5, "x": 1.51, "y": -2.7}, {"time": 4.6667, "x": 0, "y": 0}, {"time": 4.8333, "x": 1.51, "y": -2.7}, {"time": 5, "x": 0, "y": 0}]}, "bone101": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -7.16}, {"time": 0.3333, "angle": 0}, {"time": 0.5, "angle": -7.16}, {"time": 0.6667, "angle": 0}, {"time": 0.8333, "angle": -7.16}, {"time": 1, "angle": 0}, {"time": 1.1667, "angle": -7.16}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": -7.16}, {"time": 1.6667, "angle": 0}, {"time": 1.8333, "angle": -7.16}, {"time": 2, "angle": 0}, {"time": 2.1667, "angle": -7.16}, {"time": 2.3333, "angle": 0}, {"time": 2.5, "angle": -7.16}, {"time": 2.6667, "angle": 0}, {"time": 2.8333, "angle": -7.16}, {"time": 3, "angle": 0}, {"time": 3.1667, "angle": -7.16}, {"time": 3.3333, "angle": 0}, {"time": 3.5, "angle": -7.16}, {"time": 3.6667, "angle": 0}, {"time": 3.8333, "angle": -7.16}, {"time": 4, "angle": 0}, {"time": 4.1667, "angle": -7.16}, {"time": 4.3333, "angle": 0}, {"time": 4.5, "angle": -7.16}, {"time": 4.6667, "angle": 0}, {"time": 4.8333, "angle": -7.16}, {"time": 5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1667, "x": -2.28, "y": -5.42}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5, "x": -2.28, "y": -5.42}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 0.8333, "x": -2.28, "y": -5.42}, {"time": 1, "x": 0, "y": 0}, {"time": 1.1667, "x": -2.28, "y": -5.42}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 1.5, "x": -2.28, "y": -5.42}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.8333, "x": -2.28, "y": -5.42}, {"time": 2, "x": 0, "y": 0}, {"time": 2.1667, "x": -2.28, "y": -5.42}, {"time": 2.3333, "x": 0, "y": 0}, {"time": 2.5, "x": -2.28, "y": -5.42}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 2.8333, "x": -2.28, "y": -5.42}, {"time": 3, "x": 0, "y": 0}, {"time": 3.1667, "x": -2.28, "y": -5.42}, {"time": 3.3333, "x": 0, "y": 0}, {"time": 3.5, "x": -2.28, "y": -5.42}, {"time": 3.6667, "x": 0, "y": 0}, {"time": 3.8333, "x": -2.28, "y": -5.42}, {"time": 4, "x": 0, "y": 0}, {"time": 4.1667, "x": -2.28, "y": -5.42}, {"time": 4.3333, "x": 0, "y": 0}, {"time": 4.5, "x": -2.28, "y": -5.42}, {"time": 4.6667, "x": 0, "y": 0}, {"time": 4.8333, "x": -2.28, "y": -5.42}, {"time": 5, "x": 0, "y": 0}]}, "bone103": {"translate": [{"time": 0, "x": 11.1, "y": 0}, {"time": 1, "x": 14.05, "y": 0}, {"time": 2.5, "x": 0, "y": 0}, {"time": 3.5, "x": 14.05, "y": 0, "curve": "stepped"}, {"time": 3.8333, "x": 14.05, "y": 0}, {"time": 5, "x": 11.1, "y": 0}], "scale": [{"time": 0, "x": 1.004, "y": 1.004}, {"time": 1, "x": 1, "y": 1}, {"time": 1.5, "x": 1.195, "y": 1.069}, {"time": 2.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.5, "x": 1, "y": 1}, {"time": 4, "x": 1.195, "y": 1.069}, {"time": 5, "x": 1, "y": 1}]}, "bone": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6, "x": 0.8, "y": -21.32}, {"time": 1.2333, "x": -0.52, "y": 13.7}, {"time": 2.5, "x": 0, "y": 0}, {"time": 3.7333, "x": -0.52, "y": 13.7}, {"time": 5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.814, "y": 0.814}]}}, "deform": {"default": {"Monkeyking1": {"Monkeyking1": [{"time": 0}, {"time": 1.2333, "offset": 4, "vertices": [1.69043, 2.02614, 1.64249, 2.55708, 1.6185, 2.82257, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.28943, -0.24149, 0.82041, -0.19355, 2.677, -2.96982]}, {"time": 2.5}, {"time": 3.7333, "offset": 4, "vertices": [1.69043, 2.02614, 1.64249, 2.55708, 1.6185, 2.82257, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.28943, -0.24149, 0.82041, -0.19355, 2.677, -2.96982]}, {"time": 5}]}, "Monkeyking12": {"Monkeyking12": [{"time": 0}, {"time": 0.1667, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 0.3333}, {"time": 0.5, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 0.6667}, {"time": 0.8333, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 1}, {"time": 1.1667, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 1.3333}, {"time": 1.5, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 1.6667}, {"time": 1.8333, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 2}, {"time": 2.1667, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 2.3333}, {"time": 2.5, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 2.6667}, {"time": 2.8333, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 3}, {"time": 3.1667, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 3.3333}, {"time": 3.5, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 3.6667}, {"time": 3.8333, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 4}, {"time": 4.1667, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 4.3333}, {"time": 4.5, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 4.6667}, {"time": 4.8333, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 5}]}, "Monkeyking13": {"Monkeyking13": [{"time": 0, "offset": 84, "vertices": [-1.82471, 0.1947, -1.86423, -0.10015, -2.28961, -0.21322, -1.21742, 0.0232, -0.36963, -0.0705, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.0945, 0.27097, -0.76857, 0.01305, 0, 0, 0, 0, 0, 0, -0.56473, 0.11579, -2.25432, -0.398], "curve": "stepped"}, {"time": 0.7333, "offset": 84, "vertices": [-1.82471, 0.1947, -1.86423, -0.10015, -2.28961, -0.21322, -1.21742, 0.0232, -0.36963, -0.0705, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.0945, 0.27097, -0.76857, 0.01305, 0, 0, 0, 0, 0, 0, -0.56473, 0.11579, -2.25432, -0.398]}, {"time": 0.9333, "offset": 174, "vertices": [0.19453, 0.37917, -0.23415, -0.17181, 0, 0, 0, 0, 0, 0, -0.4686, -0.16582, -0.21234, -0.88715], "curve": "stepped"}, {"time": 2.6667, "offset": 174, "vertices": [0.19453, 0.37917, -0.23415, -0.17181, 0, 0, 0, 0, 0, 0, -0.4686, -0.16582, -0.21234, -0.88715]}, {"time": 3.3333, "offset": 84, "vertices": [-1.82471, 0.1947, -1.86423, -0.10015, -2.28961, -0.21322, -1.21742, 0.0232, -0.36963, -0.0705, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.0945, 0.27097, -0.76857, 0.01305, 0, 0, 0, 0, 0, 0, -0.56473, 0.11579, -2.25432, -0.398]}]}, "Monkeyking24": {"Monkeyking24": [{"time": 0, "offset": 2, "vertices": [-13.01288, 0.63295, -4.1468, -12.35062]}]}, "bone102": {"bone102": [{"time": 0}, {"time": 0.3333, "offset": 12, "vertices": [56.69998, 7.70008, 20.99997, 51.10004, -18.30865, 65.85756]}, {"time": 0.5, "offset": 12, "vertices": [74.34994, -5.4499, 39.79996, 41.15005, 0.02794, 57.06464]}, {"time": 0.8333, "vertices": [-15.25994, 8.54001, -15.25994, 8.54001, -15.25994, 8.54001, -32.18904, -9.97604, -16.78903, -2.97601, -3.68091, 17.93129, 85.78223, -2.20223, 61.25531, 37.92387, 33.93359, 48.61537]}, {"time": 1.1667, "vertices": [-16.32713, 8.68881, -16.32713, 8.68881, -16.32713, 8.68881, -32.55599, 2.12, -18.38798, 8.56002, -6.3285, 27.79475, 108.03962, 6.962, 93.17487, 40.37796, 78.49285, 46.23164]}, {"time": 1.4667, "vertices": [-17.10039, 8.69794, -17.10039, 8.69794, -17.10039, 8.69794, -31.24015, -1.45177, -18.18094, 4.48426, -7.06525, 22.21366, 55.66098, 3.30058, 109.7621, 42.22751, 167.60764, 59.85087]}, {"time": 1.7667, "vertices": [-17.51642, 9.32193, -17.51642, 9.32193, -17.51642, 9.32193, -30.69924, -2.31277, -17.64004, 3.62325, -6.52434, 21.35265, 10.86102, 6.10055, 80.36212, 38.02747, 144.0336, 60.17762]}, {"time": 2.0667, "vertices": [-18.14042, 9.32192, -18.14042, 9.32192, -18.14042, 9.32192, -30.69924, -2.31277, -17.64004, 3.62325, -6.52434, 21.35265, 29.76102, 7.5005, 57.96213, 37.32744, 90.09061, 42.70488]}, {"time": 2.3667, "vertices": [-19.18041, 10.1539, -19.18041, 10.1539, -19.18041, 10.1539, -30.69924, -2.31277, -17.64004, 3.62325, -6.52434, 21.35265, 37.46104, 5.40053, 25.76218, 42.92744, 13.18379, 48.26741]}, {"time": 2.7, "vertices": [-17.10044, 9.94592, -17.10044, 9.94592, -17.10044, 9.94592, -30.69924, -2.31277, -17.64004, 3.62325, -6.52434, 21.35265, 24.8611, -15.59942, -1.53778, 45.02748, -41.83188, 64.01028]}, {"time": 3, "vertices": [3.07551, -1.28606, 3.07551, -1.28606, 3.07551, -1.28606, -19.39024, -13.11081, -6.33103, -7.17478, 4.78466, 10.55461, 55.66116, -20.67344, 30.24823, 51.29144, -18.23733, 77.29768]}, {"time": 3.3, "vertices": [1.8275, 0.16995, 1.8275, 0.16995, 1.8275, 0.16995, -27.96722, -13.92371, -14.90801, -7.98768, -3.79232, 9.74171, 68.96107, -0.19936, 64.26218, 61.82755, 52.02203, 67.0743]}, {"time": 3.6333, "vertices": [-0.25248, 1.00194, -0.25248, 1.00194, -0.25248, 1.00194, -27.28423, -14.60675, -14.22503, -8.67072, -3.10933, 9.05867, 70.3611, 24.3007, 84.56221, 77.92758, 105.59416, 81.00383]}, {"time": 3.9667, "vertices": [0.37153, 0.79395, 0.37153, 0.79395, 0.37153, 0.79395, -30.01626, -9.14276, -16.95705, -3.20674, -5.84136, 14.52266, 78.76108, -7.19926, 113.26221, 52.7276, 166.0312, 67.02624]}, {"time": 4.3333, "vertices": [0.37151, 0.58595, 0.37151, 0.58595, 0.37151, 0.58595, -30.69924, -2.31277, -17.64004, 3.62325, -6.52434, 21.35265, 59.86104, -17.69933, 88.76215, 45.72757, 136.93573, 57.44525]}, {"time": 4.6667, "vertices": [0.57953, 0.16997, 0.57953, 0.16997, 0.57953, 0.16997, -30.69924, -2.31277, -17.64004, 3.62325, -6.52434, 21.35265, 19.96107, -8.59929, 39.76221, 53.42761, 72.91838, 59.09937]}, {"time": 5}]}}}}, "MonkeyKingWild": {"slots": {"BGfire0": {"attachment": [{"time": 0.0667, "name": "BGfire1"}, {"time": 0.1333, "name": "BGfire2"}, {"time": 0.2, "name": "BGfire3"}, {"time": 0.2667, "name": "BGfire4"}, {"time": 0.3667, "name": "BGfire5"}, {"time": 0.4333, "name": "BGfire0"}, {"time": 0.5, "name": "BGfire1"}, {"time": 0.5667, "name": "BGfire2"}, {"time": 0.6333, "name": "BGfire3"}, {"time": 0.7, "name": "BGfire4"}, {"time": 0.7667, "name": "BGfire5"}, {"time": 0.8333, "name": "BGfire0"}, {"time": 0.9, "name": "BGfire1"}, {"time": 1, "name": "BGfire2"}, {"time": 1.0667, "name": "BGfire3"}, {"time": 1.1333, "name": "BGfire4"}, {"time": 1.2, "name": "BGfire5"}, {"time": 1.2667, "name": "BGfire0"}, {"time": 1.3333, "name": "BGfire1"}, {"time": 1.4, "name": "BGfire2"}, {"time": 1.4667, "name": "BGfire3"}, {"time": 1.5333, "name": "BGfire4"}, {"time": 1.6333, "name": "BGfire5"}, {"time": 1.7, "name": "BGfire0"}, {"time": 1.7667, "name": "BGfire1"}, {"time": 1.8333, "name": "BGfire2"}, {"time": 1.9, "name": "BGfire3"}, {"time": 1.9667, "name": "BGfire4"}, {"time": 2.0333, "name": "BGfire5"}, {"time": 2.1, "name": "BGfire0"}, {"time": 2.1667, "name": "BGfire1"}, {"time": 2.2667, "name": "BGfire2"}, {"time": 2.3333, "name": "BGfire3"}, {"time": 2.4, "name": "BGfire4"}, {"time": 2.4667, "name": "BGfire5"}, {"time": 2.5333, "name": "BGfire0"}, {"time": 2.6, "name": "BGfire1"}, {"time": 2.6667, "name": "BGfire2"}, {"time": 2.7333, "name": "BGfire3"}, {"time": 2.8, "name": "BGfire4"}, {"time": 2.9, "name": "BGfire5"}, {"time": 2.9667, "name": "BGfire0"}, {"time": 3.0333, "name": "BGfire1"}, {"time": 3.1, "name": "BGfire2"}, {"time": 3.1667, "name": "BGfire3"}, {"time": 3.2333, "name": "BGfire4"}, {"time": 3.3, "name": "BGfire5"}, {"time": 3.3667, "name": "BGfire0"}, {"time": 3.4333, "name": "BGfire1"}, {"time": 3.5333, "name": "BGfire2"}, {"time": 3.6, "name": "BGfire3"}, {"time": 3.6667, "name": "BGfire4"}, {"time": 3.7333, "name": "BGfire5"}, {"time": 3.8, "name": "BGfire0"}, {"time": 3.8667, "name": "BGfire1"}, {"time": 3.9333, "name": "BGfire2"}, {"time": 4, "name": "BGfire3"}, {"time": 4.0667, "name": "BGfire4"}, {"time": 4.1667, "name": "BGfire5"}, {"time": 4.2333, "name": "BGfire0"}, {"time": 4.3, "name": "BGfire1"}, {"time": 4.3667, "name": "BGfire2"}, {"time": 4.4333, "name": "BGfire3"}, {"time": 4.5, "name": "BGfire4"}, {"time": 4.5667, "name": "BGfire5"}, {"time": 4.6333, "name": "BGfire0"}, {"time": 4.7, "name": "BGfire1"}, {"time": 4.8, "name": "BGfire2"}, {"time": 4.8667, "name": "BGfire3"}, {"time": 4.9333, "name": "BGfire4"}, {"time": 5, "name": "BGfire5"}]}, "WildexpandBorderlight0": {"attachment": [{"time": 0.0667, "name": "WildexpandBorderlight1"}, {"time": 0.1333, "name": "WildexpandBorderlight2"}, {"time": 0.2, "name": "WildexpandBorderlight3"}, {"time": 0.2667, "name": "WildexpandBorderlight4"}, {"time": 0.3667, "name": "WildexpandBorderlight5"}, {"time": 0.4333, "name": "WildexpandBorderlight6"}, {"time": 0.5, "name": "WildexpandBorderlight7"}, {"time": 0.5667, "name": "WildexpandBorderlight0"}, {"time": 0.6333, "name": "WildexpandBorderlight1"}, {"time": 0.7, "name": "WildexpandBorderlight2"}, {"time": 0.7667, "name": "WildexpandBorderlight3"}, {"time": 0.8333, "name": "WildexpandBorderlight4"}, {"time": 0.9, "name": "WildexpandBorderlight5"}, {"time": 1, "name": "WildexpandBorderlight6"}, {"time": 1.0667, "name": "WildexpandBorderlight7"}, {"time": 1.1333, "name": "WildexpandBorderlight0"}, {"time": 1.2, "name": "WildexpandBorderlight1"}, {"time": 1.2667, "name": "WildexpandBorderlight2"}, {"time": 1.3333, "name": "WildexpandBorderlight3"}, {"time": 1.4, "name": "WildexpandBorderlight4"}, {"time": 1.4667, "name": "WildexpandBorderlight5"}, {"time": 1.5333, "name": "WildexpandBorderlight6"}, {"time": 1.6333, "name": "WildexpandBorderlight7"}, {"time": 1.7, "name": "WildexpandBorderlight0"}, {"time": 1.7667, "name": "WildexpandBorderlight1"}, {"time": 1.8333, "name": "WildexpandBorderlight2"}, {"time": 1.9, "name": "WildexpandBorderlight3"}, {"time": 1.9667, "name": "WildexpandBorderlight4"}, {"time": 2.0333, "name": "WildexpandBorderlight5"}, {"time": 2.1, "name": "WildexpandBorderlight6"}, {"time": 2.1667, "name": "WildexpandBorderlight7"}, {"time": 2.2667, "name": "WildexpandBorderlight0"}, {"time": 2.3333, "name": "WildexpandBorderlight1"}, {"time": 2.4, "name": "WildexpandBorderlight2"}, {"time": 2.4667, "name": "WildexpandBorderlight3"}, {"time": 2.5333, "name": "WildexpandBorderlight4"}, {"time": 2.6, "name": "WildexpandBorderlight5"}, {"time": 2.6667, "name": "WildexpandBorderlight6"}, {"time": 2.7333, "name": "WildexpandBorderlight7"}, {"time": 2.8, "name": "WildexpandBorderlight0"}, {"time": 2.9, "name": "WildexpandBorderlight1"}, {"time": 2.9667, "name": "WildexpandBorderlight2"}, {"time": 3.0333, "name": "WildexpandBorderlight3"}, {"time": 3.1, "name": "WildexpandBorderlight4"}, {"time": 3.1667, "name": "WildexpandBorderlight5"}, {"time": 3.2333, "name": "WildexpandBorderlight6"}, {"time": 3.3, "name": "WildexpandBorderlight7"}, {"time": 3.3667, "name": "WildexpandBorderlight0"}, {"time": 3.4333, "name": "WildexpandBorderlight1"}, {"time": 3.5333, "name": "WildexpandBorderlight2"}, {"time": 3.6, "name": "WildexpandBorderlight3"}, {"time": 3.6667, "name": "WildexpandBorderlight4"}, {"time": 3.7333, "name": "WildexpandBorderlight5"}, {"time": 3.8, "name": "WildexpandBorderlight6"}, {"time": 3.8667, "name": "WildexpandBorderlight7"}, {"time": 3.9333, "name": "WildexpandBorderlight0"}, {"time": 4, "name": "WildexpandBorderlight1"}, {"time": 4.0667, "name": "WildexpandBorderlight2"}, {"time": 4.1667, "name": "WildexpandBorderlight3"}, {"time": 4.2333, "name": "WildexpandBorderlight4"}, {"time": 4.3, "name": "WildexpandBorderlight5"}, {"time": 4.3667, "name": "WildexpandBorderlight6"}, {"time": 4.4333, "name": "WildexpandBorderlight7"}, {"time": 4.5, "name": "WildexpandBorderlight0"}, {"time": 4.5667, "name": "WildexpandBorderlight1"}, {"time": 4.6333, "name": "WildexpandBorderlight2"}, {"time": 4.7, "name": "WildexpandBorderlight3"}, {"time": 4.8, "name": "WildexpandBorderlight4"}, {"time": 4.8667, "name": "WildexpandBorderlight5"}, {"time": 4.9333, "name": "WildexpandBorderlight6"}, {"time": 5, "name": "WildexpandBorderlight7"}]}, "WildexpandBorderlight1": {"attachment": [{"time": 0.0667, "name": "WildexpandBorderlight1"}, {"time": 0.1333, "name": "WildexpandBorderlight2"}, {"time": 0.2, "name": "WildexpandBorderlight3"}, {"time": 0.2667, "name": "WildexpandBorderlight4"}, {"time": 0.3667, "name": "WildexpandBorderlight5"}, {"time": 0.4333, "name": "WildexpandBorderlight6"}, {"time": 0.5, "name": "WildexpandBorderlight7"}, {"time": 0.5667, "name": "WildexpandBorderlight0"}, {"time": 0.6333, "name": "WildexpandBorderlight1"}, {"time": 0.7, "name": "WildexpandBorderlight2"}, {"time": 0.7667, "name": "WildexpandBorderlight3"}, {"time": 0.8333, "name": "WildexpandBorderlight4"}, {"time": 0.9, "name": "WildexpandBorderlight5"}, {"time": 1, "name": "WildexpandBorderlight6"}, {"time": 1.0667, "name": "WildexpandBorderlight7"}, {"time": 1.1333, "name": "WildexpandBorderlight0"}, {"time": 1.2, "name": "WildexpandBorderlight1"}, {"time": 1.2667, "name": "WildexpandBorderlight2"}, {"time": 1.3333, "name": "WildexpandBorderlight3"}, {"time": 1.4, "name": "WildexpandBorderlight4"}, {"time": 1.4667, "name": "WildexpandBorderlight5"}, {"time": 1.5333, "name": "WildexpandBorderlight6"}, {"time": 1.6333, "name": "WildexpandBorderlight7"}, {"time": 1.7, "name": "WildexpandBorderlight0"}, {"time": 1.7667, "name": "WildexpandBorderlight1"}, {"time": 1.8333, "name": "WildexpandBorderlight2"}, {"time": 1.9, "name": "WildexpandBorderlight3"}, {"time": 1.9667, "name": "WildexpandBorderlight4"}, {"time": 2.0333, "name": "WildexpandBorderlight5"}, {"time": 2.1, "name": "WildexpandBorderlight6"}, {"time": 2.1667, "name": "WildexpandBorderlight7"}, {"time": 2.2667, "name": "WildexpandBorderlight0"}, {"time": 2.3333, "name": "WildexpandBorderlight1"}, {"time": 2.4, "name": "WildexpandBorderlight2"}, {"time": 2.4667, "name": "WildexpandBorderlight3"}, {"time": 2.5333, "name": "WildexpandBorderlight4"}, {"time": 2.6, "name": "WildexpandBorderlight5"}, {"time": 2.6667, "name": "WildexpandBorderlight6"}, {"time": 2.7333, "name": "WildexpandBorderlight7"}, {"time": 2.8, "name": "WildexpandBorderlight0"}, {"time": 2.9, "name": "WildexpandBorderlight1"}, {"time": 2.9667, "name": "WildexpandBorderlight2"}, {"time": 3.0333, "name": "WildexpandBorderlight3"}, {"time": 3.1, "name": "WildexpandBorderlight4"}, {"time": 3.1667, "name": "WildexpandBorderlight5"}, {"time": 3.2333, "name": "WildexpandBorderlight6"}, {"time": 3.3, "name": "WildexpandBorderlight7"}, {"time": 3.3667, "name": "WildexpandBorderlight0"}, {"time": 3.4333, "name": "WildexpandBorderlight1"}, {"time": 3.5333, "name": "WildexpandBorderlight2"}, {"time": 3.6, "name": "WildexpandBorderlight3"}, {"time": 3.6667, "name": "WildexpandBorderlight4"}, {"time": 3.7333, "name": "WildexpandBorderlight5"}, {"time": 3.8, "name": "WildexpandBorderlight6"}, {"time": 3.8667, "name": "WildexpandBorderlight7"}, {"time": 3.9333, "name": "WildexpandBorderlight0"}, {"time": 4, "name": "WildexpandBorderlight1"}, {"time": 4.0667, "name": "WildexpandBorderlight2"}, {"time": 4.1667, "name": "WildexpandBorderlight3"}, {"time": 4.2333, "name": "WildexpandBorderlight4"}, {"time": 4.3, "name": "WildexpandBorderlight5"}, {"time": 4.3667, "name": "WildexpandBorderlight6"}, {"time": 4.4333, "name": "WildexpandBorderlight7"}, {"time": 4.5, "name": "WildexpandBorderlight0"}, {"time": 4.5667, "name": "WildexpandBorderlight1"}, {"time": 4.6333, "name": "WildexpandBorderlight2"}, {"time": 4.7, "name": "WildexpandBorderlight3"}, {"time": 4.8, "name": "WildexpandBorderlight4"}, {"time": 4.8667, "name": "WildexpandBorderlight5"}, {"time": 4.9333, "name": "WildexpandBorderlight6"}, {"time": 5, "name": "WildexpandBorderlight7"}]}, "batgioifull1": {"attachment": [{"time": 0, "name": null}]}, "batgioifull2": {"attachment": [{"time": 0, "name": null}]}, "batgioifull3": {"attachment": [{"time": 0, "name": null}]}, "batgioifull4": {"attachment": [{"time": 0, "name": null}]}, "batgioifull5": {"attachment": [{"time": 0, "name": null}]}, "batgioifull6": {"attachment": [{"time": 0, "name": null}]}, "batgioifull7": {"attachment": [{"time": 0, "name": null}]}, "batgioifull8": {"attachment": [{"time": 0, "name": null}]}, "batgioifull9": {"attachment": [{"time": 0, "name": null}]}, "bgtank": {"attachment": [{"time": 0, "name": null}]}, "bone102": {"attachment": [{"time": 0, "name": "bone102"}]}, "duongtank1": {"attachment": [{"time": 0, "name": null}]}, "duongtank2": {"attachment": [{"time": 0, "name": null}]}, "duongtank3": {"attachment": [{"time": 0, "name": null}]}, "duongtank4": {"attachment": [{"time": 0, "name": null}]}, "duongtank5": {"attachment": [{"time": 0, "name": null}]}, "duongtank6": {"attachment": [{"time": 0, "name": null}]}, "duongtank7": {"attachment": [{"time": 0, "name": null}]}, "duongtank8": {"attachment": [{"time": 0, "name": null}]}, "duongtank9": {"attachment": [{"time": 0, "name": null}]}, "duongtank10": {"attachment": [{"time": 0, "name": null}]}, "duongtank11": {"attachment": [{"time": 0, "name": null}]}, "duongtank12": {"attachment": [{"time": 0, "name": null}]}, "duongtank13": {"attachment": [{"time": 0, "name": null}]}, "duongtank14": {"attachment": [{"time": 0, "name": null}]}, "duongtank15": {"attachment": [{"time": 0, "name": null}]}, "satangfull1": {"attachment": [{"time": 0, "name": null}]}, "satangfull2": {"attachment": [{"time": 0, "name": null}]}, "satangfull3": {"attachment": [{"time": 0, "name": null}]}, "satangfull4": {"attachment": [{"time": 0, "name": null}]}, "satangfull5": {"attachment": [{"time": 0, "name": null}]}, "satangfull6": {"attachment": [{"time": 0, "name": null}]}, "satangfull7": {"attachment": [{"time": 0, "name": null}]}, "satangfull8": {"attachment": [{"time": 0, "name": null}]}, "satangfull9": {"attachment": [{"time": 0, "name": null}]}, "wild text2": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 2.6667, "color": "ffffffff"}, {"time": 2.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}, {"time": 3.6667, "color": "ffffffff"}, {"time": 3.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 4, "color": "ffffff00"}, {"time": 4.6667, "color": "ffffffff"}, {"time": 4.9333, "color": "ffffff00"}]}}, "bones": {"bone2": {"rotate": [{"time": 0, "angle": 0}, {"time": 2.6667, "angle": -1.06}, {"time": 5, "angle": 0}]}, "bone3": {"rotate": [{"time": 0.5, "angle": 0}, {"time": 0.8333, "angle": 10.78}, {"time": 2.6667, "angle": 15.32}, {"time": 3, "angle": 0}, {"time": 3.6333, "angle": 1.97}, {"time": 5, "angle": 0}], "translate": [{"time": 3.6333, "x": 0, "y": 0}, {"time": 3.7333, "x": -1.49, "y": 0.24}, {"time": 3.8333, "x": 0, "y": 0}, {"time": 3.9333, "x": -1.49, "y": 0.24}, {"time": 4.0333, "x": 0, "y": 0}, {"time": 4.1333, "x": -1.49, "y": 0.24}, {"time": 4.2333, "x": 0, "y": 0}]}, "bone98": {"rotate": [{"time": 0, "angle": 20.52}, {"time": 0.6667, "angle": -8.75, "curve": "stepped"}, {"time": 1.1667, "angle": -8.75}, {"time": 1.3333, "angle": -24.55}, {"time": 1.6667, "angle": -21.13}, {"time": 1.8333, "angle": -7.25}, {"time": 2.1667, "angle": -7.85}, {"time": 2.3333, "angle": -21.78}, {"time": 2.6667, "angle": -8.75}, {"time": 3.3333, "angle": 20.52}, {"time": 4.1667, "angle": 15.71}, {"time": 5, "angle": 20.52}]}, "bone99": {"rotate": [{"time": 0, "angle": 59.3}, {"time": 0.6667, "angle": -0.7, "curve": "stepped"}, {"time": 2.6667, "angle": -0.7}, {"time": 3.3333, "angle": 59.3}, {"time": 4.1667, "angle": 54.49}, {"time": 5, "angle": 59.3}]}, "bone9": {"rotate": [{"time": 0, "angle": 62.9}, {"time": 0.4333, "angle": 39.96}, {"time": 0.6667, "angle": -21.23}, {"time": 1.1667, "angle": -35.08}, {"time": 1.6667, "angle": -21.23}, {"time": 2.1667, "angle": -35.08}, {"time": 2.6667, "angle": -21.23}, {"time": 3.3333, "angle": 62.9}, {"time": 4.1667, "angle": 67.53}, {"time": 5, "angle": 62.9}], "translate": [{"time": 0, "x": -6.46, "y": -6.57}, {"time": 0.4333, "x": -4.22, "y": -5.18}, {"time": 0.6667, "x": 6.46, "y": -5.65, "curve": "stepped"}, {"time": 2.6667, "x": 6.46, "y": -5.65}, {"time": 3.3333, "x": -6.46, "y": -6.57}]}, "bone10": {"rotate": [{"time": 0, "angle": -27.34}, {"time": 0.6667, "angle": -5.92, "curve": "stepped"}, {"time": 2.6667, "angle": -5.92}, {"time": 3.3333, "angle": -3.28}, {"time": 4.1667, "angle": 6.89}, {"time": 5, "angle": -27.34}]}, "bone8": {"rotate": [{"time": 0, "angle": -7.35}, {"time": 0.1667, "angle": -0.35}, {"time": 0.6667, "angle": 5.15}, {"time": 1.6667, "angle": 1.87, "curve": "stepped"}, {"time": 2.6667, "angle": 1.87}, {"time": 3.3333, "angle": -7.35}, {"time": 4.1667, "angle": -12.16}, {"time": 5, "angle": -7.35}]}, "bone11": {"rotate": [{"time": 4.1667, "angle": -4.81}]}, "bone76": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 1.63}, {"time": 0.5333, "angle": -2.85}, {"time": 0.7667, "angle": -0.65}, {"time": 1, "angle": 0}, {"time": 1.2667, "angle": 1.63}, {"time": 1.5333, "angle": -2.85}, {"time": 1.7667, "angle": -0.65}, {"time": 2.0333, "angle": 0}, {"time": 2.3, "angle": 1.63}, {"time": 2.5667, "angle": -2.85}, {"time": 2.8, "angle": -0.65}, {"time": 3.1, "angle": 0}, {"time": 3.3667, "angle": 1.63}, {"time": 3.6333, "angle": -2.85}, {"time": 3.8667, "angle": -0.65}, {"time": 4.0333, "angle": 0}, {"time": 4.3, "angle": 1.63}, {"time": 4.5667, "angle": -2.85}, {"time": 4.8, "angle": -0.65}, {"time": 5, "angle": 0}]}, "bone77": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 10.52}, {"time": 0.5333, "angle": 0.7}, {"time": 0.7667, "angle": -4.12}, {"time": 1, "angle": 0}, {"time": 1.2667, "angle": 10.52}, {"time": 1.5333, "angle": 0.7}, {"time": 1.7667, "angle": -4.12}, {"time": 2.0333, "angle": 0}, {"time": 2.3, "angle": 10.52}, {"time": 2.5667, "angle": 0.7}, {"time": 2.8, "angle": -4.12}, {"time": 3.1, "angle": 0}, {"time": 3.3667, "angle": 10.52}, {"time": 3.6333, "angle": 0.7}, {"time": 3.8667, "angle": -4.12}, {"time": 4.0333, "angle": 0}, {"time": 4.3, "angle": 10.52}, {"time": 4.5667, "angle": 0.7}, {"time": 4.8, "angle": -4.12}, {"time": 5, "angle": 0}]}, "bone72": {"rotate": [{"time": 0, "angle": 4.65}, {"time": 0.4333, "angle": -4.82}, {"time": 0.9, "angle": -3.4}, {"time": 1.2667, "angle": 6.52}, {"time": 1.6, "angle": 7.04}, {"time": 1.9667, "angle": -0.24}, {"time": 2.3667, "angle": -4.16}, {"time": 2.8, "angle": -3.11}, {"time": 3.1667, "angle": 4.91}, {"time": 3.5, "angle": 6.44}, {"time": 3.8, "angle": 1.34}, {"time": 4.1, "angle": -8.05}, {"time": 4.3333, "angle": -3.67}, {"time": 4.6333, "angle": -0.42}, {"time": 5, "angle": 4.65}]}, "bone75": {"rotate": [{"time": 0, "angle": 0.56}, {"time": 0.4333, "angle": -4.53}, {"time": 0.9, "angle": -3.48}, {"time": 1.2667, "angle": -6.43}, {"time": 1.6, "angle": -6.19}, {"time": 1.9667, "angle": -4}, {"time": 2.3667, "angle": -1.83}, {"time": 2.8, "angle": -11.07}, {"time": 3.1667, "angle": -12.26}, {"time": 3.5, "angle": -4.01}, {"time": 3.8, "angle": -3.33}, {"time": 4.1, "angle": 5.02}, {"time": 4.3333, "angle": -1.87}, {"time": 4.6333, "angle": -8.46}, {"time": 5, "angle": 0.56}]}, "bone74": {"rotate": [{"time": 0, "angle": 8.24}, {"time": 0.4333, "angle": 7.12}, {"time": 0.9, "angle": -6.64}, {"time": 1.2667, "angle": -10.3}, {"time": 1.6, "angle": 2.46}, {"time": 1.9667, "angle": 5.62}, {"time": 2.3667, "angle": 5.1}, {"time": 2.8, "angle": -3.46}, {"time": 3.1667, "angle": -10.45}, {"time": 3.5, "angle": 2.73}, {"time": 3.8, "angle": 6.3}, {"time": 4.1, "angle": 0.78}, {"time": 4.3333, "angle": 2.84}, {"time": 4.6333, "angle": -6.15}, {"time": 5, "angle": 8.24}]}, "bone73": {"rotate": [{"time": 0, "angle": -0.8}, {"time": 0.4333, "angle": 2.92}, {"time": 0.9, "angle": -5.28}, {"time": 1.2667, "angle": -8.54}, {"time": 1.6, "angle": -2.07}, {"time": 1.9667, "angle": -1.84}, {"time": 2.3667, "angle": -5.47}, {"time": 2.8, "angle": -6.08}, {"time": 3.1667, "angle": -1.99}, {"time": 3.5, "angle": -0.4}, {"time": 3.8, "angle": -0.64}, {"time": 4.1, "angle": 8.74}, {"time": 4.3333, "angle": -5.34}, {"time": 4.6333, "angle": -6.01}, {"time": 5, "angle": -0.8}]}, "bone5": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3333, "angle": 6.95}, {"time": 2.3333, "angle": 6.12}, {"time": 2.6667, "angle": 0}]}, "bone6": {"rotate": [{"time": 0.6667, "angle": 0}, {"time": 1, "angle": 4.44}, {"time": 1.1667, "angle": -3.55}, {"time": 1.6667, "angle": 4.44}, {"time": 1.8333, "angle": -3.55}, {"time": 2.6667, "angle": -0.75}, {"time": 5, "angle": 0}], "scale": [{"time": 0, "x": 0.863, "y": 0.863}]}, "bone4": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3333, "angle": -31.19, "curve": "stepped"}, {"time": 2.5, "angle": -31.19}, {"time": 2.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": 12.81, "y": -7.01, "curve": "stepped"}, {"time": 2.5, "x": 12.81, "y": -7.01}, {"time": 2.8333, "x": 0, "y": 0}, {"time": 3.9, "x": -4.51, "y": -1.36}, {"time": 5, "x": 0, "y": 0}]}, "bone42": {"rotate": [{"time": 0, "angle": -34.35}, {"time": 0.3333, "angle": -38.77}, {"time": 0.6667, "angle": -38.03}, {"time": 1.4333, "angle": -33.34}, {"time": 2.5, "angle": -16.72}, {"time": 2.9333, "angle": -20.88}, {"time": 3.2667, "angle": -29.98}, {"time": 3.6, "angle": -36.73}, {"time": 4, "angle": -28.46}, {"time": 5, "angle": -34.35}]}, "bone43": {"rotate": [{"time": 0, "angle": 0.7}, {"time": 0.3333, "angle": 4.59}, {"time": 0.6667, "angle": -6.66}, {"time": 1.4333, "angle": -14.86}, {"time": 2.5, "angle": -17.91}, {"time": 2.9333, "angle": -6.34}, {"time": 3.2667, "angle": -13.42}, {"time": 3.6, "angle": -4.54}, {"time": 4, "angle": -12.17}, {"time": 5, "angle": 0.7}]}, "bone44": {"rotate": [{"time": 0, "angle": 0.73}, {"time": 0.3333, "angle": -5.63}, {"time": 0.6667, "angle": -13.82}, {"time": 1.4333, "angle": -9.61}, {"time": 2.5, "angle": -7}, {"time": 2.9333, "angle": -11.59}, {"time": 3.2667, "angle": -21.73}, {"time": 3.6, "angle": -19.19}, {"time": 4, "angle": -22.22}, {"time": 5, "angle": 0.73}]}, "bone45": {"rotate": [{"time": 0, "angle": -0.22}, {"time": 0.3333, "angle": -1.27}, {"time": 0.6667, "angle": 10.53}, {"time": 1.4333, "angle": 4.75}, {"time": 2.5, "angle": 6.37}, {"time": 2.9333, "angle": -6.87}, {"time": 3.2667, "angle": -4.54}, {"time": 3.6, "angle": -0.15}, {"time": 4, "angle": 6.88}, {"time": 5, "angle": -0.22}]}, "bone46": {"rotate": [{"time": 0, "angle": 17.77}, {"time": 0.3333, "angle": 22.69}, {"time": 0.6667, "angle": 19.15}, {"time": 1.4333, "angle": 13.62}, {"time": 2.5, "angle": 11.82}, {"time": 2.9333, "angle": 4.6}, {"time": 3.2667, "angle": 19.35}, {"time": 3.6, "angle": 19.22}, {"time": 4, "angle": 17.88}, {"time": 5, "angle": 17.77}]}, "bone47": {"rotate": [{"time": 0, "angle": 7.47}, {"time": 0.3333, "angle": 9.96}, {"time": 0.6667, "angle": 7.43}, {"time": 1.4333, "angle": 17.96}, {"time": 2.5, "angle": 10.12}, {"time": 2.9333, "angle": 8.59}, {"time": 3.2667, "angle": 8.29}, {"time": 3.6, "angle": 9.55}, {"time": 4, "angle": 16.9}, {"time": 5, "angle": 7.47}]}, "bone48": {"rotate": [{"time": 0, "angle": -1.53}, {"time": 0.3333, "angle": 5.28}, {"time": 0.6667, "angle": 10.17}, {"time": 1.4333, "angle": 7.18}, {"time": 2.5, "angle": 2.73}, {"time": 2.9333, "angle": -0.36}, {"time": 3.2667, "angle": 7.95}, {"time": 3.6, "angle": 2.92}, {"time": 4, "angle": 7.03}, {"time": 4.3333, "angle": 12.69}, {"time": 5, "angle": -1.53}]}, "bone49": {"rotate": [{"time": 0, "angle": -1.99}, {"time": 0.3333, "angle": -2.71}, {"time": 0.6667, "angle": 9.69}, {"time": 1.4333, "angle": 9.68}, {"time": 2.5, "angle": -0.09}, {"time": 2.9333, "angle": 9.16}, {"time": 3.2667, "angle": 8.43}, {"time": 3.6, "angle": -7.66}, {"time": 4, "angle": 9.97}, {"time": 4.3333, "angle": 10.7}, {"time": 5, "angle": -1.99}]}, "bone50": {"rotate": [{"time": 0, "angle": -1.87}, {"time": 0.3333, "angle": 3.35}, {"time": 0.6667, "angle": 11.14}, {"time": 1.4333, "angle": 6.11}, {"time": 2.5, "angle": 12.96}, {"time": 2.9333, "angle": 9.11}, {"time": 3.2667, "angle": 3.23}, {"time": 3.6, "angle": 0.23}, {"time": 4, "angle": 4.1}, {"time": 4.3333, "angle": 14.42}, {"time": 5, "angle": -1.87}]}, "bone51": {"rotate": [{"time": 0, "angle": -2.23}, {"time": 0.3333, "angle": 2.62}, {"time": 0.6667, "angle": 4.12}, {"time": 1.4333, "angle": 5.31}, {"time": 1.9, "angle": 5.78}, {"time": 2.5, "angle": -0.12}, {"time": 2.9333, "angle": 2.86}, {"time": 3.2667, "angle": -1.33}, {"time": 3.6, "angle": -3.06}, {"time": 4, "angle": 3.32}, {"time": 4.3333, "angle": 5.73}, {"time": 5, "angle": -2.23}]}, "bone52": {"rotate": [{"time": 0, "angle": -2.14}, {"time": 0.3333, "angle": -6.67}, {"time": 0.6667, "angle": 3.51}, {"time": 1.4333, "angle": 4.53}, {"time": 1.9, "angle": 10.17}, {"time": 2.5, "angle": 9.2}, {"time": 2.9333, "angle": 12.93}, {"time": 3.2667, "angle": 1.22}, {"time": 3.6, "angle": -0.6}, {"time": 4, "angle": 1.79}, {"time": 4.3333, "angle": 11.54}, {"time": 5, "angle": -2.14}]}, "bone53": {"rotate": [{"time": 0, "angle": -3.09}, {"time": 0.3333, "angle": -6.97}, {"time": 0.6667, "angle": -1.81}, {"time": 1.4333, "angle": 0.05}, {"time": 1.9, "angle": 3.65}, {"time": 2.5, "angle": 1.72}, {"time": 2.9333, "angle": -0.89}, {"time": 3.2667, "angle": -0.39}, {"time": 3.6, "angle": -4.67}, {"time": 4, "angle": -18.28}, {"time": 4.3333, "angle": 2.46}, {"time": 5, "angle": -3.09}]}, "bone54": {"rotate": [{"time": 0, "angle": -2.99}, {"time": 0.3333, "angle": -7.58}, {"time": 0.6667, "angle": -2.41}, {"time": 1.4333, "angle": 6.57}, {"time": 1.9, "angle": 9.75}, {"time": 2.5, "angle": 4.03}, {"time": 2.9333, "angle": 10.37}, {"time": 3.2667, "angle": 4.09}, {"time": 3.6, "angle": -4.43}, {"time": 4, "angle": -16.56}, {"time": 4.3333, "angle": 3.2}, {"time": 4.6333, "angle": 6.19}, {"time": 5, "angle": -2.99}]}, "bone55": {"rotate": [{"time": 0, "angle": -4.23}, {"time": 0.3333, "angle": -6.14}, {"time": 0.6667, "angle": -6.94}, {"time": 1.4333, "angle": 0.72}, {"time": 2.5, "angle": -0.71}, {"time": 2.9333, "angle": 5.01}, {"time": 3.2667, "angle": -3.82}, {"time": 3.6, "angle": -9.45}, {"time": 4, "angle": -16.95}, {"time": 4.3333, "angle": 0.88}, {"time": 4.6333, "angle": 11.68}, {"time": 5, "angle": -4.23}]}, "bone56": {"rotate": [{"time": 0, "angle": 2.64}, {"time": 0.3333, "angle": -9.13}, {"time": 0.6667, "angle": -4.45}, {"time": 1.4333, "angle": 3.25}, {"time": 2.5, "angle": 6.02}, {"time": 2.9333, "angle": 10.39}, {"time": 3.2667, "angle": -6.25}, {"time": 3.6, "angle": -13.85}, {"time": 4, "angle": -13.52}, {"time": 4.6333, "angle": 2.7}, {"time": 5, "angle": 2.64}]}, "bone57": {"rotate": [{"time": 0, "angle": -20.63}, {"time": 0.3333, "angle": -12.39}, {"time": 0.6667, "angle": -14.86}, {"time": 1.4333, "angle": -5.44}, {"time": 2.5, "angle": 0.2}, {"time": 2.9333, "angle": -5.11}, {"time": 3.2667, "angle": -5.31}, {"time": 3.6, "angle": -4.09}, {"time": 4, "angle": -4.52}, {"time": 4.6333, "angle": 3.09}, {"time": 5, "angle": -20.63}]}, "bone36": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5667, "angle": 8.54}, {"time": 1.1333, "angle": 8.82}, {"time": 3, "angle": 6.29}, {"time": 5, "angle": 0}]}, "bone40": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5667, "angle": -3.47}, {"time": 1.1333, "angle": -2.92}, {"time": 3, "angle": -4.9}, {"time": 5, "angle": 0}]}, "bone39": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5667, "angle": -5.78}, {"time": 1.1333, "angle": 5.94}, {"time": 3, "angle": 2.91}, {"time": 5, "angle": 0}]}, "bone38": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5667, "angle": 2.04}, {"time": 1.1333, "angle": 5.68}, {"time": 3, "angle": 6.44}, {"time": 5, "angle": 0}]}, "bone37": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5667, "angle": 7.26}, {"time": 1.1333, "angle": 7.93}, {"time": 3, "angle": 6.33}, {"time": 5, "angle": 0}]}, "bone58": {"rotate": [{"time": 0, "angle": -0.92}, {"time": 0.3667, "angle": 11.9}, {"time": 0.7333, "angle": -3.4}, {"time": 1.1, "angle": 1.41}, {"time": 1.4333, "angle": 13.35}, {"time": 1.7667, "angle": 14.22}, {"time": 2.1333, "angle": 28.45}, {"time": 2.4667, "angle": -0.55}, {"time": 2.8333, "angle": 12.08}, {"time": 3.2, "angle": -3.52}, {"time": 3.5667, "angle": 1.41}, {"time": 3.9, "angle": 13.35}, {"time": 4.2333, "angle": 20.3}, {"time": 4.6, "angle": 28.45}, {"time": 5, "angle": -0.92}], "scale": [{"time": 0, "x": 0.931, "y": 1}, {"time": 1.1, "x": 0.96, "y": 0.893}, {"time": 2.1333, "x": 0.876, "y": 1}, {"time": 3.2, "x": 0.928, "y": 0.888}, {"time": 4.1667, "x": 0.887, "y": 0.93}, {"time": 5, "x": 0.931, "y": 1}]}, "bone61": {"rotate": [{"time": 0, "angle": 18.72}, {"time": 0.3667, "angle": 5.63}, {"time": 0.7333, "angle": 15.06}, {"time": 1.1, "angle": -13.03}, {"time": 1.4333, "angle": -16.67}, {"time": 1.7667, "angle": -1.2}, {"time": 2.1333, "angle": 4.54}, {"time": 2.4667, "angle": 20.64}, {"time": 2.8333, "angle": -16.06}, {"time": 3.2, "angle": 1.25}, {"time": 3.5667, "angle": -13.03}, {"time": 3.9, "angle": -16.67}, {"time": 4.2333, "angle": -10.69}, {"time": 4.6, "angle": 4.54}, {"time": 5, "angle": 18.72}], "scale": [{"time": 0, "x": 0.931, "y": 1}, {"time": 1.1, "x": 0.96, "y": 0.893}, {"time": 2.1333, "x": 0.876, "y": 1}, {"time": 3.2, "x": 0.928, "y": 0.888}, {"time": 4.1667, "x": 0.887, "y": 0.93}, {"time": 5, "x": 0.931, "y": 1}]}, "bone60": {"rotate": [{"time": 0, "angle": -1.87}, {"time": 0.3667, "angle": -8.17}, {"time": 0.7333, "angle": -6.74}, {"time": 1.1, "angle": -12.35}, {"time": 1.4333, "angle": -23.59}, {"time": 1.7667, "angle": -14.88}, {"time": 2.1333, "angle": -9.4}, {"time": 2.4667, "angle": 0.04}, {"time": 2.8333, "angle": -17.5}, {"time": 3.2, "angle": -7.25}, {"time": 3.5667, "angle": -12.35}, {"time": 3.9, "angle": -23.59}, {"time": 4.2333, "angle": -18.79}, {"time": 4.6, "angle": -9.4}, {"time": 5, "angle": -1.87}], "scale": [{"time": 0, "x": 0.931, "y": 1}, {"time": 1.1, "x": 0.96, "y": 0.893}, {"time": 2.1333, "x": 0.876, "y": 1}, {"time": 3.2, "x": 0.928, "y": 0.888}, {"time": 4.1667, "x": 0.887, "y": 0.93}, {"time": 5, "x": 0.931, "y": 1}]}, "bone59": {"rotate": [{"time": 0, "angle": -1.12}, {"time": 0.3667, "angle": -6.35}, {"time": 0.7333, "angle": 0.15}, {"time": 1.1, "angle": 1.32}, {"time": 1.4333, "angle": -2.17}, {"time": 1.7667, "angle": -1.51}, {"time": 2.1333, "angle": -4.89}, {"time": 2.4667, "angle": -0.56}, {"time": 2.8333, "angle": 4.25}, {"time": 3.2, "angle": 0.02}, {"time": 3.5667, "angle": 1.32}, {"time": 3.9, "angle": -2.17}, {"time": 4.2333, "angle": -2.3}, {"time": 4.6, "angle": -4.89}, {"time": 5, "angle": -1.12}], "scale": [{"time": 0, "x": 0.931, "y": 1}, {"time": 1.1, "x": 0.96, "y": 0.893}, {"time": 2.1333, "x": 0.876, "y": 1}, {"time": 3.2, "x": 0.928, "y": 0.888}, {"time": 4.1667, "x": 0.887, "y": 0.93}, {"time": 5, "x": 0.931, "y": 1}]}, "bone63": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 32.72}, {"time": 0.8, "angle": 31.59}, {"time": 1.1, "angle": 14.57}, {"time": 1.4333, "angle": 28.39}, {"time": 1.7333, "angle": 30.84}, {"time": 2.1333, "angle": 8.12}, {"time": 2.5667, "angle": 0}, {"time": 2.9333, "angle": 32.72}, {"time": 3.3333, "angle": 31.83}, {"time": 3.6667, "angle": 10.94}, {"time": 4, "angle": 28.67}, {"time": 4.3, "angle": 30.84}, {"time": 4.7, "angle": 8.12}, {"time": 5, "angle": 0}]}, "bone66": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": 11.08}, {"time": 0.8, "angle": 50.75}, {"time": 1.1, "angle": 3.2}, {"time": 1.4333, "angle": 22.9}, {"time": 1.7333, "angle": -1.72}, {"time": 2.1333, "angle": 16.76}, {"time": 2.5667, "angle": 0}, {"time": 2.9333, "angle": 11.08}, {"time": 3.3333, "angle": 14.5}, {"time": 3.6667, "angle": 29.48}, {"time": 4, "angle": 29.26}, {"time": 4.3, "angle": -1.72}, {"time": 4.7, "angle": 16.76}, {"time": 5, "angle": 0}]}, "bone65": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": -11.12}, {"time": 0.8, "angle": -12.77}, {"time": 1.1, "angle": 10.51}, {"time": 1.4333, "angle": -8.89}, {"time": 1.7333, "angle": -12.81}, {"time": 2.1333, "angle": 0.45}, {"time": 2.5667, "angle": 0}, {"time": 2.9333, "angle": -11.12}, {"time": 3.3333, "angle": -4.11}, {"time": 3.6667, "angle": 9.13}, {"time": 4, "angle": -6.52}, {"time": 4.3, "angle": -12.81}, {"time": 4.7, "angle": 0.45}, {"time": 5, "angle": 0}]}, "bone64": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3667, "angle": -20.31}, {"time": 0.8, "angle": -17.91}, {"time": 1.1, "angle": -1.21}, {"time": 1.4333, "angle": -22.91}, {"time": 1.7333, "angle": -19.8}, {"time": 2.1333, "angle": -22.13}, {"time": 2.5667, "angle": 0}, {"time": 2.9333, "angle": -20.31}, {"time": 3.3333, "angle": -13.13}, {"time": 3.6667, "angle": -5.94}, {"time": 4, "angle": -22.06}, {"time": 4.3, "angle": -19.8}, {"time": 4.7, "angle": -22.13}, {"time": 5, "angle": 0}]}, "bone78": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": -4.45}, {"time": 0.5, "angle": 0}, {"time": 0.7333, "angle": -4.45}, {"time": 1, "angle": 0}, {"time": 1.2333, "angle": -4.45}, {"time": 1.5, "angle": 0}, {"time": 1.7333, "angle": -4.45}, {"time": 2, "angle": 0}, {"time": 2.2333, "angle": -4.45}, {"time": 2.5, "angle": 0}, {"time": 2.7333, "angle": -4.45}, {"time": 3, "angle": 0}, {"time": 3.2333, "angle": -4.45}, {"time": 3.5, "angle": 0}, {"time": 3.7333, "angle": -4.45}, {"time": 4, "angle": 0}, {"time": 4.2333, "angle": -4.45}, {"time": 4.5, "angle": 0}, {"time": 4.7333, "angle": -4.45}, {"time": 5, "angle": 0}]}, "bone84": {"rotate": [{"time": 0, "angle": -9.75, "curve": "stepped"}, {"time": 0.7667, "angle": -9.75}, {"time": 0.9333, "angle": 4.7, "curve": "stepped"}, {"time": 2.3333, "angle": 4.7}, {"time": 3, "angle": -9.75}], "translate": [{"time": 0, "x": -3.44, "y": 0.81, "curve": "stepped"}, {"time": 0.7667, "x": -3.44, "y": 0.81}, {"time": 0.9333, "x": -1.27, "y": 0.5, "curve": "stepped"}, {"time": 2.3333, "x": -1.27, "y": 0.5}, {"time": 3, "x": -3.44, "y": 0.81}]}, "bone88": {"rotate": [{"time": 0, "angle": 20.8}, {"time": 0.3, "angle": 8.8}, {"time": 0.6333, "angle": -5.74}, {"time": 0.9333, "angle": -0.94}, {"time": 1.2, "angle": -2.21}, {"time": 1.4667, "angle": 6.04}, {"time": 1.7333, "angle": 7.22}, {"time": 2, "angle": 20.8}, {"time": 2.3, "angle": 8.8}, {"time": 2.6333, "angle": -5.74}, {"time": 2.9333, "angle": -0.94}, {"time": 3.2, "angle": -2.21}, {"time": 3.4667, "angle": 6.04}, {"time": 3.7333, "angle": 7.22}, {"time": 4.0333, "angle": -5.74}, {"time": 4.3333, "angle": -0.94}, {"time": 4.6, "angle": -2.21}, {"time": 5, "angle": 20.8}]}, "bone89": {"rotate": [{"time": 0, "angle": -8.33}, {"time": 0.3, "angle": -5.85}, {"time": 0.6333, "angle": 9.42}, {"time": 0.9333, "angle": 14.85}, {"time": 1.2, "angle": 10.04}, {"time": 1.4667, "angle": 6.63}, {"time": 1.7333, "angle": 5.83}, {"time": 2, "angle": -8.33}, {"time": 2.3, "angle": -5.85}, {"time": 2.6333, "angle": 9.42}, {"time": 2.9333, "angle": 14.85}, {"time": 3.2, "angle": 10.04}, {"time": 3.4667, "angle": 6.63}, {"time": 3.7333, "angle": 5.83}, {"time": 4.0333, "angle": 9.42}, {"time": 4.3333, "angle": 14.85}, {"time": 4.6, "angle": 10.04}, {"time": 5, "angle": -8.33}]}, "bone90": {"rotate": [{"time": 0, "angle": 2.64}, {"time": 0.3, "angle": 21.14}, {"time": 0.6333, "angle": 1.1}, {"time": 0.9333, "angle": 16.08}, {"time": 1.2, "angle": 3.78}, {"time": 1.4667, "angle": -4.4}, {"time": 1.7333, "angle": 17.2}, {"time": 2, "angle": 2.64}, {"time": 2.3, "angle": 21.14}, {"time": 2.6333, "angle": 1.1}, {"time": 2.9333, "angle": 16.08}, {"time": 3.2, "angle": 3.78}, {"time": 3.4667, "angle": -4.4}, {"time": 3.7333, "angle": 17.2}, {"time": 4.0333, "angle": 1.1}, {"time": 4.3333, "angle": 16.08}, {"time": 4.6, "angle": 3.78}, {"time": 5, "angle": 2.64}]}, "bone94": {"rotate": [{"time": 0, "angle": -0.85}, {"time": 0.2333, "angle": -16.33}, {"time": 0.5, "angle": -8.15}, {"time": 0.7667, "angle": 14.26}, {"time": 1, "angle": 21.87}, {"time": 1.3333, "angle": -0.85}, {"time": 1.6333, "angle": -16.33}, {"time": 1.9, "angle": -8.15}, {"time": 2.1667, "angle": 14.26}, {"time": 2.4, "angle": 21.87}, {"time": 2.6, "angle": -0.85}, {"time": 2.8, "angle": -16.33}, {"time": 3.0667, "angle": -8.15}, {"time": 3.3333, "angle": 14.26}, {"time": 3.5667, "angle": 21.87}, {"time": 3.9, "angle": -0.85}, {"time": 4.2, "angle": -16.33}, {"time": 4.4667, "angle": -8.15}, {"time": 4.7333, "angle": 14.26}, {"time": 5, "angle": -0.85}]}, "bone92": {"rotate": [{"time": 0, "angle": -28.11}, {"time": 0.2333, "angle": -15.82}, {"time": 0.5, "angle": -2.84}, {"time": 0.7667, "angle": -6.74}, {"time": 1, "angle": -8.21}, {"time": 1.3333, "angle": -28.11}, {"time": 1.6333, "angle": -15.82}, {"time": 1.9, "angle": -2.84}, {"time": 2.1667, "angle": -6.74}, {"time": 2.4, "angle": -8.21}, {"time": 2.6, "angle": -28.11}, {"time": 2.8, "angle": -15.82}, {"time": 3.0667, "angle": -2.84}, {"time": 3.3333, "angle": -6.74}, {"time": 3.5667, "angle": -8.21}, {"time": 3.9, "angle": -28.11}, {"time": 4.2, "angle": -15.82}, {"time": 4.4667, "angle": -2.84}, {"time": 4.7333, "angle": -6.74}, {"time": 5, "angle": -28.11}]}, "bone93": {"rotate": [{"time": 0, "angle": 24.2}, {"time": 0.2333, "angle": 20.19}, {"time": 0.5, "angle": 4.79}, {"time": 0.7667, "angle": 2.21}, {"time": 1, "angle": -0.88}, {"time": 1.3333, "angle": 24.2}, {"time": 1.6333, "angle": 20.19}, {"time": 1.9, "angle": 4.79}, {"time": 2.1667, "angle": 2.21}, {"time": 2.4, "angle": -0.88}, {"time": 2.6, "angle": 24.2}, {"time": 2.8, "angle": 20.19}, {"time": 3.0667, "angle": 4.79}, {"time": 3.3333, "angle": 2.21}, {"time": 3.5667, "angle": -0.88}, {"time": 3.9, "angle": 24.2}, {"time": 4.2, "angle": 20.19}, {"time": 4.4667, "angle": 4.79}, {"time": 4.7333, "angle": 2.21}, {"time": 5, "angle": 24.2}]}, "bone100": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -5.01}, {"time": 0.3333, "angle": 0}, {"time": 0.5, "angle": -5.01}, {"time": 0.6667, "angle": 0}, {"time": 0.8333, "angle": -5.01}, {"time": 1, "angle": 0}, {"time": 1.1667, "angle": -5.01}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": -5.01}, {"time": 1.6667, "angle": 0}, {"time": 1.8333, "angle": -5.01}, {"time": 2, "angle": 0}, {"time": 2.1667, "angle": -5.01}, {"time": 2.3333, "angle": 0}, {"time": 2.5, "angle": -5.01}, {"time": 2.6667, "angle": 0}, {"time": 2.8333, "angle": -5.01}, {"time": 3, "angle": 0}, {"time": 3.1667, "angle": -5.01}, {"time": 3.3333, "angle": 0}, {"time": 3.5, "angle": -5.01}, {"time": 3.6667, "angle": 0}, {"time": 3.8333, "angle": -5.01}, {"time": 4, "angle": 0}, {"time": 4.1667, "angle": -5.01}, {"time": 4.3333, "angle": 0}, {"time": 4.5, "angle": -5.01}, {"time": 4.6667, "angle": 0}, {"time": 4.8333, "angle": -5.01}, {"time": 5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1667, "x": 1.51, "y": -2.7}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5, "x": 1.51, "y": -2.7}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 0.8333, "x": 1.51, "y": -2.7}, {"time": 1, "x": 0, "y": 0}, {"time": 1.1667, "x": 1.51, "y": -2.7}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 1.5, "x": 1.51, "y": -2.7}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.8333, "x": 1.51, "y": -2.7}, {"time": 2, "x": 0, "y": 0}, {"time": 2.1667, "x": 1.51, "y": -2.7}, {"time": 2.3333, "x": 0, "y": 0}, {"time": 2.5, "x": 1.51, "y": -2.7}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 2.8333, "x": 1.51, "y": -2.7}, {"time": 3, "x": 0, "y": 0}, {"time": 3.1667, "x": 1.51, "y": -2.7}, {"time": 3.3333, "x": 0, "y": 0}, {"time": 3.5, "x": 1.51, "y": -2.7}, {"time": 3.6667, "x": 0, "y": 0}, {"time": 3.8333, "x": 1.51, "y": -2.7}, {"time": 4, "x": 0, "y": 0}, {"time": 4.1667, "x": 1.51, "y": -2.7}, {"time": 4.3333, "x": 0, "y": 0}, {"time": 4.5, "x": 1.51, "y": -2.7}, {"time": 4.6667, "x": 0, "y": 0}, {"time": 4.8333, "x": 1.51, "y": -2.7}, {"time": 5, "x": 0, "y": 0}]}, "bone101": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -7.16}, {"time": 0.3333, "angle": 0}, {"time": 0.5, "angle": -7.16}, {"time": 0.6667, "angle": 0}, {"time": 0.8333, "angle": -7.16}, {"time": 1, "angle": 0}, {"time": 1.1667, "angle": -7.16}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": -7.16}, {"time": 1.6667, "angle": 0}, {"time": 1.8333, "angle": -7.16}, {"time": 2, "angle": 0}, {"time": 2.1667, "angle": -7.16}, {"time": 2.3333, "angle": 0}, {"time": 2.5, "angle": -7.16}, {"time": 2.6667, "angle": 0}, {"time": 2.8333, "angle": -7.16}, {"time": 3, "angle": 0}, {"time": 3.1667, "angle": -7.16}, {"time": 3.3333, "angle": 0}, {"time": 3.5, "angle": -7.16}, {"time": 3.6667, "angle": 0}, {"time": 3.8333, "angle": -7.16}, {"time": 4, "angle": 0}, {"time": 4.1667, "angle": -7.16}, {"time": 4.3333, "angle": 0}, {"time": 4.5, "angle": -7.16}, {"time": 4.6667, "angle": 0}, {"time": 4.8333, "angle": -7.16}, {"time": 5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1667, "x": -2.28, "y": -5.42}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5, "x": -2.28, "y": -5.42}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 0.8333, "x": -2.28, "y": -5.42}, {"time": 1, "x": 0, "y": 0}, {"time": 1.1667, "x": -2.28, "y": -5.42}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 1.5, "x": -2.28, "y": -5.42}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.8333, "x": -2.28, "y": -5.42}, {"time": 2, "x": 0, "y": 0}, {"time": 2.1667, "x": -2.28, "y": -5.42}, {"time": 2.3333, "x": 0, "y": 0}, {"time": 2.5, "x": -2.28, "y": -5.42}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 2.8333, "x": -2.28, "y": -5.42}, {"time": 3, "x": 0, "y": 0}, {"time": 3.1667, "x": -2.28, "y": -5.42}, {"time": 3.3333, "x": 0, "y": 0}, {"time": 3.5, "x": -2.28, "y": -5.42}, {"time": 3.6667, "x": 0, "y": 0}, {"time": 3.8333, "x": -2.28, "y": -5.42}, {"time": 4, "x": 0, "y": 0}, {"time": 4.1667, "x": -2.28, "y": -5.42}, {"time": 4.3333, "x": 0, "y": 0}, {"time": 4.5, "x": -2.28, "y": -5.42}, {"time": 4.6667, "x": 0, "y": 0}, {"time": 4.8333, "x": -2.28, "y": -5.42}, {"time": 5, "x": 0, "y": 0}]}, "bone103": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1, "x": 14.05, "y": 0}, {"time": 2.5, "x": 0, "y": 0}, {"time": 3.5, "x": 14.05, "y": 0, "curve": "stepped"}, {"time": 3.8333, "x": 14.05, "y": 0}, {"time": 5, "x": 0, "y": 0}], "scale": [{"time": 1, "x": 1, "y": 1}, {"time": 1.5, "x": 1.195, "y": 1.069}, {"time": 2.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.5, "x": 1, "y": 1}, {"time": 4, "x": 1.195, "y": 1.069}]}, "bone159": {"scale": [{"time": 0.6667, "x": 1, "y": 1}, {"time": 0.9333, "x": 1.378, "y": 1.378}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}, {"time": 1.9333, "x": 1.378, "y": 1.378}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}, {"time": 2.9333, "x": 1.378, "y": 1.378}, {"time": 3, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.6667, "x": 1, "y": 1}, {"time": 3.9333, "x": 1.378, "y": 1.378}, {"time": 4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.6667, "x": 1, "y": 1}, {"time": 4.9333, "x": 1.378, "y": 1.378}]}}, "deform": {"default": {"Monkeyking1": {"Monkeyking1": [{"time": 0}, {"time": 1.2333, "offset": 4, "vertices": [1.69043, 2.02614, 1.64249, 2.55708, 1.6185, 2.82257, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.28943, -0.24149, 0.82041, -0.19355, 2.677, -2.96982]}, {"time": 2.5}, {"time": 3.7333, "offset": 4, "vertices": [1.69043, 2.02614, 1.64249, 2.55708, 1.6185, 2.82257, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.28943, -0.24149, 0.82041, -0.19355, 2.677, -2.96982]}, {"time": 5}]}, "Monkeyking12": {"Monkeyking12": [{"time": 0}, {"time": 0.1667, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 0.3333}, {"time": 0.5, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 0.6667}, {"time": 0.8333, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 1}, {"time": 1.1667, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 1.3333}, {"time": 1.5, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 1.6667}, {"time": 1.8333, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 2}, {"time": 2.1667, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 2.3333}, {"time": 2.5, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 2.6667}, {"time": 2.8333, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 3}, {"time": 3.1667, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 3.3333}, {"time": 3.5, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 3.6667}, {"time": 3.8333, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 4}, {"time": 4.1667, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 4.3333}, {"time": 4.5, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 4.6667}, {"time": 4.8333, "offset": 12, "vertices": [-0.35271, 0.41595, -0.27893, 0.46867, -0.1557, 0.52271, -2.46915, 2.91154, -1.95251, 3.28054, -1.09009, 3.65891, -2.69559, 4.86494, -1.85217, 5.24449, -0.51202, 5.53849, -1.82678, 1.31094, -1.14563, 1.93505, -2.21231, 2.27125, -1.80522, 2.60659, -1.11234, 2.96937, 0.06322, 0.7687, 0.18973, 0.74764, 0.36697, 0.67844]}, {"time": 5}]}, "Monkeyking13": {"Monkeyking13": [{"time": 0, "offset": 84, "vertices": [-1.82471, 0.1947, -1.86423, -0.10015, -2.28961, -0.21322, -1.21742, 0.0232, -0.36963, -0.0705, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.0945, 0.27097, -0.76857, 0.01305, 0, 0, 0, 0, 0, 0, -0.56473, 0.11579, -2.25432, -0.398], "curve": "stepped"}, {"time": 0.7333, "offset": 84, "vertices": [-1.82471, 0.1947, -1.86423, -0.10015, -2.28961, -0.21322, -1.21742, 0.0232, -0.36963, -0.0705, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.0945, 0.27097, -0.76857, 0.01305, 0, 0, 0, 0, 0, 0, -0.56473, 0.11579, -2.25432, -0.398]}, {"time": 0.9333, "offset": 174, "vertices": [0.19453, 0.37917, -0.23415, -0.17181, 0, 0, 0, 0, 0, 0, -0.4686, -0.16582, -0.21234, -0.88715], "curve": "stepped"}, {"time": 2.6667, "offset": 174, "vertices": [0.19453, 0.37917, -0.23415, -0.17181, 0, 0, 0, 0, 0, 0, -0.4686, -0.16582, -0.21234, -0.88715]}, {"time": 3.3333, "offset": 84, "vertices": [-1.82471, 0.1947, -1.86423, -0.10015, -2.28961, -0.21322, -1.21742, 0.0232, -0.36963, -0.0705, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.0945, 0.27097, -0.76857, 0.01305, 0, 0, 0, 0, 0, 0, -0.56473, 0.11579, -2.25432, -0.398]}]}, "Monkeyking24": {"Monkeyking24": [{"time": 0, "offset": 2, "vertices": [-13.01288, 0.63295, -4.1468, -12.35062]}]}, "bone102": {"bone102": [{"time": 0}, {"time": 0.3333, "offset": 12, "vertices": [56.69998, 7.70008, 20.99997, 51.10004, -18.30865, 65.85756]}, {"time": 0.5, "offset": 12, "vertices": [74.34994, -5.4499, 39.79996, 41.15005, 0.02794, 57.06464]}, {"time": 0.8333, "vertices": [-15.25994, 8.54001, -15.25994, 8.54001, -15.25994, 8.54001, -32.18904, -9.97604, -16.78903, -2.97601, -3.68091, 17.93129, 85.78223, -2.20223, 61.25531, 37.92387, 33.93359, 48.61537]}, {"time": 1.1667, "vertices": [-16.32713, 8.68881, -16.32713, 8.68881, -16.32713, 8.68881, -32.55599, 2.12, -18.38798, 8.56002, -6.3285, 27.79475, 108.03962, 6.962, 93.17487, 40.37796, 78.49285, 46.23164]}, {"time": 1.4667, "vertices": [-17.10039, 8.69794, -17.10039, 8.69794, -17.10039, 8.69794, -31.24015, -1.45177, -18.18094, 4.48426, -7.06525, 22.21366, 55.66098, 3.30058, 109.7621, 42.22751, 167.60764, 59.85087]}, {"time": 1.7667, "vertices": [-17.51642, 9.32193, -17.51642, 9.32193, -17.51642, 9.32193, -30.69924, -2.31277, -17.64004, 3.62325, -6.52434, 21.35265, 10.86102, 6.10055, 80.36212, 38.02747, 144.0336, 60.17762]}, {"time": 2.0667, "vertices": [-18.14042, 9.32192, -18.14042, 9.32192, -18.14042, 9.32192, -30.69924, -2.31277, -17.64004, 3.62325, -6.52434, 21.35265, 29.76102, 7.5005, 57.96213, 37.32744, 90.09061, 42.70488]}, {"time": 2.3667, "vertices": [-19.18041, 10.1539, -19.18041, 10.1539, -19.18041, 10.1539, -30.69924, -2.31277, -17.64004, 3.62325, -6.52434, 21.35265, 37.46104, 5.40053, 25.76218, 42.92744, 13.18379, 48.26741]}, {"time": 2.7, "vertices": [-17.10044, 9.94592, -17.10044, 9.94592, -17.10044, 9.94592, -30.69924, -2.31277, -17.64004, 3.62325, -6.52434, 21.35265, 24.8611, -15.59942, -1.53778, 45.02748, -41.83188, 64.01028]}, {"time": 3, "vertices": [3.07551, -1.28606, 3.07551, -1.28606, 3.07551, -1.28606, -19.39024, -13.11081, -6.33103, -7.17478, 4.78466, 10.55461, 55.66116, -20.67344, 30.24823, 51.29144, -18.23733, 77.29768]}, {"time": 3.3, "vertices": [1.8275, 0.16995, 1.8275, 0.16995, 1.8275, 0.16995, -27.96722, -13.92371, -14.90801, -7.98768, -3.79232, 9.74171, 68.96107, -0.19936, 64.26218, 61.82755, 52.02203, 67.0743]}, {"time": 3.6333, "vertices": [-0.25248, 1.00194, -0.25248, 1.00194, -0.25248, 1.00194, -27.28423, -14.60675, -14.22503, -8.67072, -3.10933, 9.05867, 70.3611, 24.3007, 84.56221, 77.92758, 105.59416, 81.00383]}, {"time": 3.9667, "vertices": [0.37153, 0.79395, 0.37153, 0.79395, 0.37153, 0.79395, -30.01626, -9.14276, -16.95705, -3.20674, -5.84136, 14.52266, 78.76108, -7.19926, 113.26221, 52.7276, 166.0312, 67.02624]}, {"time": 4.3333, "vertices": [0.37151, 0.58595, 0.37151, 0.58595, 0.37151, 0.58595, -30.69924, -2.31277, -17.64004, 3.62325, -6.52434, 21.35265, 59.86104, -17.69933, 88.76215, 45.72757, 136.93573, 57.44525]}, {"time": 4.6667, "vertices": [0.57953, 0.16997, 0.57953, 0.16997, 0.57953, 0.16997, -30.69924, -2.31277, -17.64004, 3.62325, -6.52434, 21.35265, 19.96107, -8.59929, 39.76221, 53.42761, 72.91838, 59.09937]}, {"time": 5}]}}}, "drawOrder": [{"time": 0, "offsets": [{"slot": "WildexpandBorderlight1", "offset": -35}]}]}, "batgioi": {"slots": {"BGfire0": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking1": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking2": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking3": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking4": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking5": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking6": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking7": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking8": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking9": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking10": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking11": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking12": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking13": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking14": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking15": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking16": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking17": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking18": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking19": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking20": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking21": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking22": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking23": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking24": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking25": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking26": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking27": {"attachment": [{"time": 0, "name": null}]}, "WildexpandBorderlight0": {"attachment": [{"time": 0, "name": null}]}, "WildexpandBorderlight1": {"attachment": [{"time": 0, "name": null}]}, "bgtank": {"attachment": [{"time": 0, "name": null}]}, "brush": {"attachment": [{"time": 0, "name": "brush"}]}, "chonmuccuocboard": {"attachment": [{"time": 0, "name": "chonmuccuocboard"}]}, "chonmuccuocboard2": {"attachment": [{"time": 0, "name": "chonmuccuocboard"}]}, "duongtank1": {"attachment": [{"time": 0, "name": null}]}, "duongtank2": {"attachment": [{"time": 0, "name": null}]}, "duongtank3": {"attachment": [{"time": 0, "name": null}]}, "duongtank4": {"attachment": [{"time": 0, "name": null}]}, "duongtank5": {"attachment": [{"time": 0, "name": null}]}, "duongtank6": {"attachment": [{"time": 0, "name": null}]}, "duongtank7": {"attachment": [{"time": 0, "name": null}]}, "duongtank8": {"attachment": [{"time": 0, "name": null}]}, "duongtank9": {"attachment": [{"time": 0, "name": null}]}, "duongtank10": {"attachment": [{"time": 0, "name": null}]}, "duongtank11": {"attachment": [{"time": 0, "name": null}]}, "duongtank12": {"attachment": [{"time": 0, "name": null}]}, "duongtank13": {"attachment": [{"time": 0, "name": null}]}, "duongtank14": {"attachment": [{"time": 0, "name": null}]}, "duongtank15": {"attachment": [{"time": 0, "name": null}]}, "muccuoc0": {"attachment": [{"time": 0, "name": "muccuoc2"}]}, "satangfull1": {"attachment": [{"time": 0, "name": null}]}, "satangfull2": {"attachment": [{"time": 0, "name": null}]}, "satangfull3": {"attachment": [{"time": 0, "name": null}]}, "satangfull4": {"attachment": [{"time": 0, "name": null}]}, "satangfull5": {"attachment": [{"time": 0, "name": null}]}, "satangfull6": {"attachment": [{"time": 0, "name": null}]}, "satangfull7": {"attachment": [{"time": 0, "name": null}]}, "satangfull8": {"attachment": [{"time": 0, "name": null}]}, "satangfull9": {"attachment": [{"time": 0, "name": null}]}, "wild text": {"attachment": [{"time": 0, "name": null}]}, "wild text2": {"attachment": [{"time": 0, "name": null}]}}, "bones": {"bone175": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.6667, "angle": -6.7}, {"time": 2.5, "angle": 11.24}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 1.6667, "x": 0, "y": 0}, {"time": 2.5, "x": 21.8, "y": 15.01}, {"time": 3.3333, "x": 0, "y": 0}]}, "bone179": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.6667, "angle": 3.96}, {"time": 2.5, "angle": -4.3}, {"time": 3.3333, "angle": 0}]}, "bone176": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": 7.23}, {"time": 0.3333, "angle": 12.03}, {"time": 0.5, "angle": 6.96}, {"time": 0.6667, "angle": 7}, {"time": 0.8333, "angle": 0}, {"time": 1, "angle": 7.23}, {"time": 1.1667, "angle": 12.03}, {"time": 1.3333, "angle": 6.96}, {"time": 1.5, "angle": 7}, {"time": 1.6667, "angle": 0}, {"time": 1.8333, "angle": 7.23}, {"time": 2, "angle": 12.03}, {"time": 2.1667, "angle": 6.96}, {"time": 2.3333, "angle": 7}, {"time": 2.5, "angle": 0}, {"time": 2.6667, "angle": 7.23}, {"time": 2.8333, "angle": 12.03}, {"time": 3, "angle": 6.96}, {"time": 3.1667, "angle": 7}, {"time": 3.3333, "angle": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.912, "y": 1}, {"time": 1.6667, "x": 1.048, "y": 1}, {"time": 2.5, "x": 0.956, "y": 1}, {"time": 3.3333, "x": 1, "y": 1}]}, "bone178": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -6.69}, {"time": 0.3333, "angle": -1.7}, {"time": 0.5, "angle": 12.84}, {"time": 0.6667, "angle": -0.93}, {"time": 0.8333, "angle": 0}, {"time": 1, "angle": -6.69}, {"time": 1.1667, "angle": -1.7}, {"time": 1.3333, "angle": 12.84}, {"time": 1.5, "angle": -0.93}, {"time": 1.6667, "angle": 0}, {"time": 1.8333, "angle": -6.69}, {"time": 2, "angle": -1.7}, {"time": 2.1667, "angle": 12.84}, {"time": 2.3333, "angle": -0.93}, {"time": 2.5, "angle": 0}, {"time": 2.6667, "angle": -6.69}, {"time": 2.8333, "angle": -1.7}, {"time": 3, "angle": 12.84}, {"time": 3.1667, "angle": -0.93}, {"time": 3.3333, "angle": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.912, "y": 1}, {"time": 1.6667, "x": 1.048, "y": 1}, {"time": 2.5, "x": 0.956, "y": 1}, {"time": 3.3333, "x": 1, "y": 1}]}, "bone177": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": 11.22}, {"time": 0.3333, "angle": 14.98}, {"time": 0.5, "angle": 12.94}, {"time": 0.6667, "angle": 9.05}, {"time": 0.8333, "angle": 0}, {"time": 1, "angle": 11.22}, {"time": 1.1667, "angle": 14.98}, {"time": 1.3333, "angle": 12.94}, {"time": 1.5, "angle": 9.05}, {"time": 1.6667, "angle": 0}, {"time": 1.8333, "angle": 11.22}, {"time": 2, "angle": 14.98}, {"time": 2.1667, "angle": 12.94}, {"time": 2.3333, "angle": 9.05}, {"time": 2.5, "angle": 0}, {"time": 2.6667, "angle": 11.22}, {"time": 2.8333, "angle": 14.98}, {"time": 3, "angle": 12.94}, {"time": 3.1667, "angle": 9.05}, {"time": 3.3333, "angle": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.912, "y": 1}, {"time": 1.6667, "x": 1.048, "y": 1}, {"time": 2.5, "x": 0.956, "y": 1}, {"time": 3.3333, "x": 1, "y": 1}]}, "bone181": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3, "angle": -15.36}, {"time": 0.5667, "angle": 0}, {"time": 0.8667, "angle": -15.36}, {"time": 1.1, "angle": 0}, {"time": 1.4333, "angle": -15.36}, {"time": 1.6667, "angle": 0}, {"time": 1.9667, "angle": -15.36}, {"time": 2.2333, "angle": 0}, {"time": 2.5333, "angle": -15.36}, {"time": 2.7667, "angle": 0}, {"time": 3.1, "angle": -15.36}, {"time": 3.3333, "angle": 1.71}]}, "bone182": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3, "angle": -3.65}, {"time": 0.5667, "angle": 0}, {"time": 0.8667, "angle": -3.65}, {"time": 1.1, "angle": 0}, {"time": 1.4333, "angle": -3.65}, {"time": 1.6667, "angle": 0}, {"time": 1.9667, "angle": -3.65}, {"time": 2.2333, "angle": 0}, {"time": 2.5333, "angle": -3.65}, {"time": 2.7667, "angle": 0}, {"time": 3.1, "angle": -3.65}, {"time": 3.3333, "angle": 2.11}]}, "bone183": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3333, "angle": 5.8}, {"time": 0.6667, "angle": 0}, {"time": 1, "angle": 5.8}, {"time": 1.3333, "angle": 0}, {"time": 1.6667, "angle": 5.8}, {"time": 2, "angle": 0}, {"time": 2.3333, "angle": 5.8}, {"time": 2.6667, "angle": 0}, {"time": 3, "angle": 5.8}, {"time": 3.3333, "angle": 0}]}, "bone184": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3333, "angle": 2.19}, {"time": 0.6667, "angle": 0}, {"time": 1, "angle": 2.19}, {"time": 1.3333, "angle": 0}, {"time": 1.6667, "angle": 2.19}, {"time": 2, "angle": 0}, {"time": 2.3333, "angle": 2.19}, {"time": 2.6667, "angle": 0}, {"time": 3, "angle": 2.19}, {"time": 3.3333, "angle": 0}]}, "bone171": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 4.94}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.8333, "x": 2.4, "y": 0.85}, {"time": 1.6667, "x": 0.33, "y": 0.37}, {"time": 1.8667, "x": 0.66, "y": 0.39}, {"time": 1.9667, "x": -0.61, "y": 0.31}, {"time": 2.0667, "x": 1.05, "y": 0.42}, {"time": 2.1667, "x": -0.48, "y": 0.32}, {"time": 2.2667, "x": 1.7, "y": 0.69}, {"time": 2.3667, "x": 0.33, "y": 0.33}, {"time": 2.5, "x": 1.73, "y": 0.46}, {"time": 3.3333, "x": 0, "y": 0}]}, "bone173": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3333, "angle": -12.15}, {"time": 0.6667, "angle": 0}, {"time": 1, "angle": -12.15}, {"time": 1.3333, "angle": 0}, {"time": 1.6667, "angle": -12.15}, {"time": 2, "angle": -5.7}, {"time": 2.1667, "angle": -0.17}, {"time": 2.3333, "angle": -5.17}, {"time": 2.5, "angle": 4.03}, {"time": 2.6667, "angle": -5.63}, {"time": 2.8333, "angle": -11.4}, {"time": 3, "angle": -12.15}, {"time": 3.3333, "angle": -0.85}]}, "bone174": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": -1.72, "y": 0.52}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 1, "x": -1.72, "y": 0.52}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 1.6667, "x": -1.72, "y": 0.52}, {"time": 2, "x": 0, "y": 0}, {"time": 2.3333, "x": -1.72, "y": 0.52}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 3, "x": -1.72, "y": 0.52}, {"time": 3.3333, "x": 0, "y": 0}]}, "bone160": {"translate": [{"time": 0, "x": -24.4, "y": -25.01}]}}, "deform": {"default": {"batgioifull3": {"batgioifull3": [{"time": 1.1667}, {"time": 1.2333, "offset": 38, "vertices": [-0.87049, 0.16467, -3.30074, 1.0415, -4.35648, 1.28357, -4.40044, 1.27802, -3.18644, 0.46307, 0, 0, 0.78538, -0.03076, 0.79128, -0.01172, 0.80577, -0.37134, -0.27081, -0.33447], "curve": "stepped"}, {"time": 1.3333, "offset": 38, "vertices": [-0.87049, 0.16467, -3.30074, 1.0415, -4.35648, 1.28357, -4.40044, 1.27802, -3.18644, 0.46307, 0, 0, 0.78538, -0.03076, 0.79128, -0.01172, 0.80577, -0.37134, -0.27081, -0.33447]}, {"time": 1.4333}, {"time": 1.8667, "offset": 8, "vertices": [1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729]}, {"time": 1.9667, "offset": 80, "vertices": [1.01934, -0.36475, 0, 0, -0.90041, 0.25476, -0.732, 0.05719, -0.57376, 0.0874, -0.6926, 0.19586, 0.19618, 1.21765, 1.76085, -0.12634, 3.75931, -0.46851, 2.81005, -0.64868, 2.25591, -0.49146, 1.10828, -0.31519]}, {"time": 2.0667, "offset": 8, "vertices": [1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729]}, {"time": 2.1667, "offset": 80, "vertices": [1.01934, -0.36475, 0, 0, -0.90041, 0.25476, -0.732, 0.05719, -0.57376, 0.0874, -0.6926, 0.19586, 0.19618, 1.21765, 1.76085, -0.12634, 3.75931, -0.46851, 2.81005, -0.64868, 2.25591, -0.49146, 1.10828, -0.31519]}, {"time": 2.3, "offset": 8, "vertices": [1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729]}, {"time": 2.4333, "offset": 80, "vertices": [1.01934, -0.36475, 0, 0, -0.90041, 0.25476, -0.732, 0.05719, -0.57376, 0.0874, -0.6926, 0.19586, 0.19618, 1.21765, 1.76085, -0.12634, 3.75931, -0.46851, 2.81005, -0.64868, 2.25591, -0.49146, 1.10828, -0.31519]}, {"time": 2.6667, "offset": 8, "vertices": [1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729, 1.00449, -0.28729]}, {"time": 3.3333}]}, "batgioifull6": {"batgioifull6": [{"time": 0}, {"time": 0.8333, "offset": 192, "vertices": [5.26628, -2.83002, -0.65363, 5.9433, 3.09248, -2.9646, 0.68936, 4.22861, 0.76936, -0.67944, 0.12415, 1.01984, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.2661, 2.56873, -3.40042, 0.41113, 2.9609, 3.09717, -4.22964, 0.68387, -1.38682, 2.82874, -1.54382, -2.74551]}, {"time": 1.6667}, {"time": 2.5, "offset": 192, "vertices": [5.26628, -2.83002, -0.65363, 5.9433, 3.09248, -2.9646, 0.68936, 4.22861, 0.76936, -0.67944, 0.12415, 1.01984, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.2661, 2.56873, -3.40042, 0.41113, 2.9609, 3.09717, -4.22964, 0.68387, -1.38682, 2.82874, -1.54382, -2.74551]}, {"time": 3.3333}]}}}}, "satang": {"slots": {"BGfire0": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking1": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking2": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking3": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking4": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking5": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking6": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking7": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking8": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking9": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking10": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking11": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking12": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking13": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking14": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking15": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking16": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking17": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking18": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking19": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking20": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking21": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking22": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking23": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking24": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking25": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking26": {"attachment": [{"time": 0, "name": null}]}, "Monkeyking27": {"attachment": [{"time": 0, "name": null}]}, "WildexpandBorderlight0": {"attachment": [{"time": 0, "name": null}]}, "WildexpandBorderlight1": {"attachment": [{"time": 0, "name": null}]}, "batgioifull1": {"attachment": [{"time": 0, "name": null}]}, "batgioifull2": {"attachment": [{"time": 0, "name": null}]}, "batgioifull3": {"attachment": [{"time": 0, "name": null}]}, "batgioifull4": {"attachment": [{"time": 0, "name": null}]}, "batgioifull5": {"attachment": [{"time": 0, "name": null}]}, "batgioifull6": {"attachment": [{"time": 0, "name": null}]}, "batgioifull7": {"attachment": [{"time": 0, "name": null}]}, "batgioifull8": {"attachment": [{"time": 0, "name": null}]}, "batgioifull9": {"attachment": [{"time": 0, "name": null}]}, "bgtank": {"attachment": [{"time": 0, "name": null}]}, "chonmuccuocboard": {"attachment": [{"time": 0, "name": "chonmuccuocboard"}]}, "chonmuccuocboard2": {"attachment": [{"time": 0, "name": "chonmuccuocboard"}]}, "duongtank1": {"attachment": [{"time": 0, "name": null}]}, "duongtank2": {"attachment": [{"time": 0, "name": null}]}, "duongtank3": {"attachment": [{"time": 0, "name": null}]}, "duongtank4": {"attachment": [{"time": 0, "name": null}]}, "duongtank5": {"attachment": [{"time": 0, "name": null}]}, "duongtank6": {"attachment": [{"time": 0, "name": null}]}, "duongtank7": {"attachment": [{"time": 0, "name": null}]}, "duongtank8": {"attachment": [{"time": 0, "name": null}]}, "duongtank9": {"attachment": [{"time": 0, "name": null}]}, "duongtank10": {"attachment": [{"time": 0, "name": null}]}, "duongtank11": {"attachment": [{"time": 0, "name": null}]}, "duongtank12": {"attachment": [{"time": 0, "name": null}]}, "duongtank13": {"attachment": [{"time": 0, "name": null}]}, "duongtank14": {"attachment": [{"time": 0, "name": null}]}, "duongtank15": {"attachment": [{"time": 0, "name": null}]}, "muccuoc0": {"attachment": [{"time": 0, "name": "muccuoc3"}]}, "wild text": {"attachment": [{"time": 0, "name": null}]}, "wild text2": {"attachment": [{"time": 0, "name": null}]}}, "bones": {"bone199": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.1667, "angle": 8.93, "curve": "stepped"}, {"time": 2.3333, "angle": 8.93}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.1667, "x": 5.89, "y": -5.56, "curve": "stepped"}, {"time": 2.3333, "x": 5.89, "y": -5.56}, {"time": 3.3333, "x": 0, "y": 0}]}, "bone200": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -15.57}, {"time": 1.0667, "angle": -8.79}, {"time": 1.1667, "angle": -8.4}, {"time": 2.3333, "angle": -7.3}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 7.76, "y": 4.05}, {"time": 1.0667, "x": -4.51, "y": 8.31}, {"time": 1.1667, "x": 7.76, "y": 4.05}, {"time": 3.3333, "x": 0, "y": 0}]}, "bone201": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1667, "x": -0.47, "y": -2.89}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5, "x": 1.17, "y": 0.28}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 0.8333, "x": 2.84, "y": -3.7}, {"time": 1, "x": 0, "y": 0}, {"time": 1.1667, "x": 2.84, "y": -3.7}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 1.5, "x": 2.84, "y": -3.7}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.8333, "x": 2.84, "y": -3.7}, {"time": 2, "x": 0, "y": 0}, {"time": 2.1667, "x": 2.84, "y": -3.7}, {"time": 2.3333, "x": 0, "y": 0}, {"time": 2.5, "x": 2.84, "y": -3.7}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 2.8333, "x": 2.84, "y": -3.7}, {"time": 3, "x": 0, "y": 0}, {"time": 3.1667, "x": 2.84, "y": -3.7}, {"time": 3.3333, "x": 0, "y": 0}]}, "bone202": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -7.68}, {"time": 0.3333, "angle": 0}, {"time": 0.5, "angle": -7.68}, {"time": 0.6667, "angle": 0}, {"time": 0.8333, "angle": -7.68}, {"time": 1, "angle": 0}, {"time": 1.1667, "angle": -7.68}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": -7.68}, {"time": 1.6667, "angle": 0}, {"time": 1.8333, "angle": -7.68}, {"time": 2, "angle": 0}, {"time": 2.1667, "angle": -7.68}, {"time": 2.3333, "angle": 0}, {"time": 2.5, "angle": -7.68}, {"time": 2.6667, "angle": 0}, {"time": 2.8333, "angle": -7.68}, {"time": 3, "angle": 0}, {"time": 3.1667, "angle": -7.68}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1667, "x": 12.4, "y": -6.65}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5, "x": 12.4, "y": -6.65}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 0.8333, "x": 12.4, "y": -6.65}, {"time": 1, "x": 0, "y": 0}, {"time": 1.1667, "x": 12.4, "y": -6.65}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 1.5, "x": 12.4, "y": -6.65}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.8333, "x": 12.4, "y": -6.65}, {"time": 2, "x": 0, "y": 0}, {"time": 2.1667, "x": 12.4, "y": -6.65}, {"time": 2.3333, "x": 0, "y": 0}, {"time": 2.5, "x": 12.4, "y": -6.65}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 2.8333, "x": 12.4, "y": -6.65}, {"time": 3, "x": 0, "y": 0}, {"time": 3.1667, "x": 12.4, "y": -6.65}, {"time": 3.3333, "x": 0, "y": 0}]}, "bone188": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.3333, "angle": -6.65, "curve": "stepped"}, {"time": 2, "angle": -6.65}, {"time": 2.3333, "angle": 0}]}, "bone196": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.6667, "angle": 4.29}, {"time": 3.3333, "angle": 0}]}, "bone197": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.6667, "angle": -17.12}, {"time": 3.3333, "angle": 0}]}, "bone198": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.6667, "angle": -13.55}, {"time": 3.3333, "angle": 0}]}, "bone190": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5667, "angle": -2.09}, {"time": 1.1, "angle": 0}, {"time": 1.6667, "angle": -2.09}, {"time": 2.2333, "angle": 0}, {"time": 2.7667, "angle": -2.09}, {"time": 3.3333, "angle": 0}]}, "bone192": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5667, "angle": -1.61}, {"time": 1.1, "angle": 0}, {"time": 1.6667, "angle": -1.61}, {"time": 2.2333, "angle": 0}, {"time": 2.7667, "angle": -1.61}, {"time": 3.3333, "angle": 0}]}, "bone191": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5667, "angle": -1.17}, {"time": 1.1, "angle": 0}, {"time": 1.6667, "angle": -1.17}, {"time": 2.2333, "angle": 0}, {"time": 2.7667, "angle": -1.17}, {"time": 3.3333, "angle": 0}]}, "bone193": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5667, "angle": -4.51}, {"time": 1.1, "angle": 0}, {"time": 1.6667, "angle": -4.51}, {"time": 2.2333, "angle": 0}, {"time": 2.7667, "angle": -4.51}, {"time": 3.3333, "angle": 0}]}, "bone194": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5667, "angle": -5.28}, {"time": 1.1, "angle": 0}, {"time": 1.6667, "angle": -5.28}, {"time": 2.2333, "angle": 0}, {"time": 2.7667, "angle": -5.28}, {"time": 3.3333, "angle": 0}]}}, "deform": {"default": {"satangfull2": {"satangfull2": [{"time": 1.1667}, {"time": 1.2333, "offset": 18, "vertices": [-1.82774, 0.25214, -2.94557, 0.224, -3.27927, -0.01465, -2.2982, 0.19891, -0.60605, 0.16779, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.46655, 0.27399, -2.56198, 0.82043, -3.17633, 0.77271, -2.92877, 0.65546, -1.54555, 0.09698], "curve": "stepped"}, {"time": 1.3333, "offset": 18, "vertices": [-1.82774, 0.25214, -2.94557, 0.224, -3.27927, -0.01465, -2.2982, 0.19891, -0.60605, 0.16779, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.46655, 0.27399, -2.56198, 0.82043, -3.17633, 0.77271, -2.92877, 0.65546, -1.54555, 0.09698]}, {"time": 1.4333}]}, "satangfull3": {"satangfull3": [{"time": 0}, {"time": 0.3333, "offset": 4, "vertices": [-0.27168, 0.57953, -1.35464, 0.75568, -1.01878, 1.1322, -1.35138, 2.77875, 1.33669, 5.78894, 0.46751, 4.31158, -2.32239, 3.0802, -2.96045, 1.73474, -0.02415, 2.49817, -3.56143, 1.81958, 0.46809, 0.14612]}, {"time": 0.6667}, {"time": 1, "offset": 4, "vertices": [-0.27168, 0.57953, -1.35464, 0.75568, -1.01878, 1.1322, -1.35138, 2.77875, 1.33669, 5.78894, 0.46751, 4.31158, -2.32239, 3.0802, -2.96045, 1.73474, -0.02415, 2.49817, -3.56143, 1.81958, 0.46809, 0.14612]}, {"time": 1.3333}, {"time": 1.6667, "offset": 4, "vertices": [-0.27168, 0.57953, -1.35464, 0.75568, -1.01878, 1.1322, -1.35138, 2.77875, 1.33669, 5.78894, 0.46751, 4.31158, -2.32239, 3.0802, -2.96045, 1.73474, -0.02415, 2.49817, -3.56143, 1.81958, 0.46809, 0.14612]}, {"time": 2}, {"time": 2.3333, "offset": 4, "vertices": [-0.27168, 0.57953, -1.35464, 0.75568, -1.01878, 1.1322, -1.35138, 2.77875, 1.33669, 5.78894, 0.46751, 4.31158, -2.32239, 3.0802, -2.96045, 1.73474, -0.02415, 2.49817, -3.56143, 1.81958, 0.46809, 0.14612]}, {"time": 2.6667}, {"time": 3, "offset": 4, "vertices": [-0.27168, 0.57953, -1.35464, 0.75568, -1.01878, 1.1322, -1.35138, 2.77875, 1.33669, 5.78894, 0.46751, 4.31158, -2.32239, 3.0802, -2.96045, 1.73474, -0.02415, 2.49817, -3.56143, 1.81958, 0.46809, 0.14612]}, {"time": 3.3333}]}, "satangfull4": {"satangfull4": [{"time": 0}, {"time": 0.8333, "offset": 10, "vertices": [2.08847, 0.22003, -1.51053, 1.45917, 3.16692, 2.02014, -3.58264, 1.12875, 2.3765, 0.2926, -1.75116, 1.63333, 0.64838, -0.1424, -0.30743, 0.58856, 2.16216, -1.29004, -0.40076, 2.48627, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.34464, 1.37018, 3.60831, 3.51624, 2.30416, 0.58002, 2.95252, 0.43756, 2.30416, 0.58008, 1.29543, 0.93768, 2.88104, 1.30865, -2.85312, 1.36819, 5.05722, -0.37842, 2.38786, 3.17059, 0.8166, -0.58112, -0.0788, 1.00052, -0.47881, -1.57642, 0.95989, 0.8244]}, {"time": 1.6667}, {"time": 2.5, "offset": 10, "vertices": [2.08847, 0.22003, -1.51053, 1.45917, 3.16692, 2.02014, -3.58264, 1.12875, 2.3765, 0.2926, -1.75116, 1.63333, 0.64838, -0.1424, -0.30743, 0.58856, 2.16216, -1.29004, -0.40076, 2.48627, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.34464, 1.37018, 3.60831, 3.51624, 2.30416, 0.58002, 2.95252, 0.43756, 2.30416, 0.58008, 1.29543, 0.93768, 2.88104, 1.30865, -2.85312, 1.36819, 5.05722, -0.37842, 2.38786, 3.17059, 0.8166, -0.58112, -0.0788, 1.00052, -0.47881, -1.57642, 0.95989, 0.8244]}, {"time": 3.3333}]}, "satangfull7": {"satangfull7": [{"time": 0}, {"time": 0.3333, "vertices": [2.63894, -9.78601, -3e-05, -11.02155, -3.68005, -14.69519, -6.40903, -3.38751, 0, 0, 0, 0, 0, 0, 2.63898, -2.25867, 3.76996, -4.89294, 3.01594, -7.15137, -1.13103, -3.76416, -1.508, -2.6347, -3.77005, -4.89313, -9.42504, -5.64587, -0.75402, -2.2583, -1.131, -3.01111]}, {"time": 0.6667}, {"time": 0.9, "vertices": [2.63894, -9.78601, -3e-05, -11.02155, -3.68005, -14.69519, -6.40903, -3.38751, 0, 0, 0, 0, 0, 0, 2.63898, -2.25867, 3.76996, -4.89294, 3.01594, -7.15137, -1.13103, -3.76416, -1.508, -2.6347, -3.77005, -4.89313, -9.42504, -5.64587, -0.75402, -2.2583, -1.131, -3.01111]}, {"time": 1.1333}, {"time": 1.4, "vertices": [2.63894, -9.78601, -3e-05, -11.02155, -3.68005, -14.69519, -6.40903, -3.38751, 0, 0, 0, 0, 0, 0, 2.63898, -2.25867, 3.76996, -4.89294, 3.01594, -7.15137, -1.13103, -3.76416, -1.508, -2.6347, -3.77005, -4.89313, -9.42504, -5.64587, -0.75402, -2.2583, -1.131, -3.01111]}, {"time": 1.6333}, {"time": 1.9, "vertices": [2.63894, -9.78601, -3e-05, -11.02155, -3.68005, -14.69519, -6.40903, -3.38751, 0, 0, 0, 0, 0, 0, 2.63898, -2.25867, 3.76996, -4.89294, 3.01594, -7.15137, -1.13103, -3.76416, -1.508, -2.6347, -3.77005, -4.89313, -9.42504, -5.64587, -0.75402, -2.2583, -1.131, -3.01111]}, {"time": 2.1333}, {"time": 2.4, "vertices": [2.63894, -9.78601, -3e-05, -11.02155, -3.68005, -14.69519, -6.40903, -3.38751, 0, 0, 0, 0, 0, 0, 2.63898, -2.25867, 3.76996, -4.89294, 3.01594, -7.15137, -1.13103, -3.76416, -1.508, -2.6347, -3.77005, -4.89313, -9.42504, -5.64587, -0.75402, -2.2583, -1.131, -3.01111]}, {"time": 2.6667}, {"time": 3, "vertices": [2.63894, -9.78601, -3e-05, -11.02155, -3.68005, -14.69519, -6.40903, -3.38751, 0, 0, 0, 0, 0, 0, 2.63898, -2.25867, 3.76996, -4.89294, 3.01594, -7.15137, -1.13103, -3.76416, -1.508, -2.6347, -3.77005, -4.89313, -9.42504, -5.64587, -0.75402, -2.2583, -1.131, -3.01111]}, {"time": 3.3333}]}}}}}}