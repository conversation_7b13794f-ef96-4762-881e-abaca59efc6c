[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "aquariumBonusGameView", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 6}, {"__id__": 9}, {"__id__": 44}, {"__id__": 195}], "_active": true, "_components": [{"__id__": 218}], "_prefab": {"__id__": 219}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "black", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 4}], "_prefab": {"__id__": 5}, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 3000, "height": 3000}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a95690f4-0cfe-4b25-840f-5c84d9abeba3"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "e19Argo6RMbovbQbLsAnw0", "sync": false}, {"__type__": "cc.Node", "_name": "tayduky_bonusgame_BG0", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 7}], "_prefab": {"__id__": 8}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 800, "height": 369}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 2.744, 2.744, 2.744]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "937d6d08-afe7-48b8-adfb-9d4fd26deb83"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "18I9g25i9Paq9fw9kpnnxX", "sync": false}, {"__type__": "cc.Node", "_name": "bonusGameStartView", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 10}, {"__id__": 15}, {"__id__": 18}, {"__id__": 21}, {"__id__": 24}, {"__id__": 27}, {"__id__": 30}, {"__id__": 33}, {"__id__": 36}, {"__id__": 39}], "_active": false, "_components": [{"__id__": 42}], "_prefab": {"__id__": 43}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1174, "height": 452}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 55, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "black", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 11}, {"__id__": 12}], "_prefab": {"__id__": 14}, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 3000, "height": 3000}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a95690f4-0cfe-4b25-840f-5c84d9abeba3"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 13}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 9}, "component": "", "_componentId": "09052Zq0oVJl59gzr1ZolUq", "handler": "startClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "8drcUoHlBOip3lw/vSAc0g", "sync": false}, {"__type__": "cc.Node", "_name": "nen popup", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 16}], "_prefab": {"__id__": 17}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 208}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -24, 0, 0, 0, 0, 1, 3.06, 3.06, 3.06]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "3d21284e-1e75-48d6-8a7e-983356f5dbb6"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "e8HsmY83RM9pTi4SlnlRad", "sync": false}, {"__type__": "cc.Node", "_name": "tex_<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": false, "_components": [{"__id__": 19}], "_prefab": {"__id__": 20}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 208}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 237, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "3d21284e-1e75-48d6-8a7e-983356f5dbb6"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "1ec77Fv35DN5cj913IJUij", "sync": false}, {"__type__": "cc.Node", "_name": "bg_popup", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": false, "_components": [{"__id__": 22}], "_prefab": {"__id__": 23}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 840, "height": 473}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -72, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ed149b50-cce3-4876-a720-4563c9f1a1ed"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "7c5IEsyEdMN7Dw8olARDoW", "sync": false}, {"__type__": "cc.Node", "_name": "Tex1", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": false, "_components": [{"__id__": 25}], "_prefab": {"__id__": 26}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 681, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 93, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "06583c0b-ba21-497f-a4c9-da2d64b813b4"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "b4M8vZ1KxCj6D68rv/+rv6", "sync": false}, {"__type__": "cc.Node", "_name": "tex_SanNgoc", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": false, "_components": [{"__id__": 28}], "_prefab": {"__id__": 29}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 321, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -6, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9a7f8631-4cee-4e16-970d-749bdf214e79"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "e8bz33ak5NyJTM63tR2tyM", "sync": false}, {"__type__": "cc.Node", "_name": "iteam_Ngoc", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 31}], "_prefab": {"__id__": 32}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 729, "height": 729}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-108.398, 226.139, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8d8f0537-f94c-47e8-aa61-97ecf25c109b"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "77lUtBojNHirpMdqwLuxH7", "sync": false}, {"__type__": "cc.Node", "_name": "quat", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 34}], "_prefab": {"__id__": 35}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 251}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-114.49, -10.065, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8d8f0537-f94c-47e8-aa61-97ecf25c109b"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "29L8Bbmu5FCp8v3tOTL4EX", "sync": false}, {"__type__": "cc.Node", "_name": "quat", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 37}], "_prefab": {"__id__": 38}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 251}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [349.762, -7.549, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8d8f0537-f94c-47e8-aa61-97ecf25c109b"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "a8MTCRbO1AB4ZCgoBBg2mK", "sync": false}, {"__type__": "cc.Node", "_name": "quat", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 40}], "_prefab": {"__id__": 41}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 251}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [113.232, -10.065, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 39}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8d8f0537-f94c-47e8-aa61-97ecf25c109b"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "195jCb3VJEkqG1muqzZjHX", "sync": false}, {"__type__": "09052Zq0oVJl59gzr1ZolUq", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "a2FzyZJ4FI5YmozDBBjBT9", "sync": false}, {"__type__": "cc.Node", "_name": "bonusGamePickView", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 45}, {"__id__": 50}, {"__id__": 61}, {"__id__": 64}, {"__id__": 75}, {"__id__": 83}, {"__id__": 93}, {"__id__": 188}], "_active": false, "_components": [{"__id__": 193}], "_prefab": {"__id__": 194}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "bacground", "_objFlags": 0, "_parent": {"__id__": 44}, "_children": [], "_active": true, "_components": [{"__id__": 46}, {"__id__": 47}, {"__id__": 48}], "_prefab": {"__id__": 49}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 45}, "_enabled": false, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "0beaaa32-47a4-4257-9852-2aea41255a46"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 45}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": ""}, {"__type__": "b5964xPIH1BUbpO82T+GdIa", "_name": "", "_objFlags": 0, "node": {"__id__": 45}, "_enabled": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "c6uXavV/VOmra5SDrwcMSF", "sync": false}, {"__type__": "cc.Node", "_name": "multiplier", "_objFlags": 0, "_parent": {"__id__": 44}, "_children": [{"__id__": 51}, {"__id__": 55}], "_active": true, "_components": [{"__id__": 59}], "_prefab": {"__id__": 60}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 273, "height": 167}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-405, 282, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "lbMultiplier", "_objFlags": 0, "_parent": {"__id__": 50}, "_children": [], "_active": true, "_components": [{"__id__": 52}, {"__id__": 53}], "_prefab": {"__id__": 54}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-79.6, -25.579, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": false, "_string": "<PERSON><PERSON> số: ", "_N$string": "<PERSON><PERSON> số: ", "_fontSize": 40, "_lineHeight": 40, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "f92cbvNs3pBuIDcZJI7cvrJ", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "label": null, "surfix": "", "duration": 1.5, "fromValue": 0, "toValue": 0, "range": 0, "sign": 0, "canRun": false, "delta": 0, "formatCurrencyUnit": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "1ds47po4pM/ZWALBAvfisL", "sync": false}, {"__type__": "cc.Node", "_name": "lbMultiplierVal", "_objFlags": 0, "_parent": {"__id__": 50}, "_children": [], "_active": true, "_components": [{"__id__": 56}, {"__id__": 57}], "_prefab": {"__id__": 58}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 37.5, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [67.2, -26.579, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": false, "_string": "x15", "_N$string": "x15", "_fontSize": 40, "_lineHeight": 40, "_enableWrapText": false, "_N$file": {"__uuid__": "23aae01c-fa56-4dbb-b8ec-e714f70ebabd"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "_defaultClip": null, "_clips": [{"__uuid__": "c10201a4-f6eb-41bc-a6da-bf6b95efc747"}], "playOnLoad": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "f5jYWPFSZBU6tmzGgcObYR", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "2dd065ad-4a0e-45ac-90df-453d77f169b6"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "5fF1r5GuFJPbeRBTt0sZdf", "sync": false}, {"__type__": "cc.Node", "_name": "bg_SanNgoc", "_objFlags": 0, "_parent": {"__id__": 44}, "_children": [], "_active": false, "_components": [{"__id__": 62}], "_prefab": {"__id__": 63}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 273, "height": 167}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-3.814, 312.046, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 61}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "2dd065ad-4a0e-45ac-90df-453d77f169b6"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "64nqgyAK5KOYBq94dTaIAb", "sync": false}, {"__type__": "cc.Node", "_name": "timer", "_objFlags": 0, "_parent": {"__id__": 44}, "_children": [{"__id__": 65}, {"__id__": 69}], "_active": true, "_components": [{"__id__": 73}], "_prefab": {"__id__": 74}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 273, "height": 167}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [405, 282, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "lbMultiplier", "_objFlags": 0, "_parent": {"__id__": 64}, "_children": [], "_active": true, "_components": [{"__id__": 66}, {"__id__": 67}], "_prefab": {"__id__": 68}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 157, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-93.826, -26.837, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 65}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": false, "_string": "<PERSON><PERSON> chơi:  ", "_N$string": "<PERSON><PERSON> chơi:  ", "_fontSize": 40, "_lineHeight": 40, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "f92cbvNs3pBuIDcZJI7cvrJ", "_name": "", "_objFlags": 0, "node": {"__id__": 65}, "_enabled": true, "label": null, "surfix": "", "duration": 1.5, "fromValue": 0, "toValue": 0, "range": 0, "sign": 0, "canRun": false, "delta": 0, "formatCurrencyUnit": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "09+9qLIaRMZrjsh5YNEIkO", "sync": false}, {"__type__": "cc.Node", "_name": "lbTime", "_objFlags": 0, "_parent": {"__id__": 64}, "_children": [], "_active": true, "_components": [{"__id__": 70}, {"__id__": 71}], "_prefab": {"__id__": 72}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 37.5, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [80.074, -27.837, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": false, "_string": "15", "_N$string": "15", "_fontSize": 40, "_lineHeight": 40, "_enableWrapText": false, "_N$file": {"__uuid__": "23aae01c-fa56-4dbb-b8ec-e714f70ebabd"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "f92cbvNs3pBuIDcZJI7cvrJ", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "label": null, "surfix": "", "duration": 1.5, "fromValue": 0, "toValue": 0, "range": 0, "sign": 0, "canRun": false, "delta": 0, "formatCurrencyUnit": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "7ehCRtzI9CCp+CO3mr3bGe", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 64}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "2dd065ad-4a0e-45ac-90df-453d77f169b6"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "dd/svggx5JwLwULYF20bKG", "sync": false}, {"__type__": "cc.Node", "_name": "remaining", "_objFlags": 0, "_parent": {"__id__": 44}, "_children": [{"__id__": 76}], "_active": true, "_components": [{"__id__": 80}, {"__id__": 81}], "_prefab": {"__id__": 82}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 316.8, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-185, 180, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "lbRemaining", "_objFlags": 0, "_parent": {"__id__": 75}, "_children": [], "_active": true, "_components": [{"__id__": 77}, {"__id__": 78}], "_prefab": {"__id__": 79}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 37.5, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [322, 2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": false, "_string": "15", "_N$string": "15", "_fontSize": 40, "_lineHeight": 40, "_enableWrapText": false, "_N$file": {"__uuid__": "23aae01c-fa56-4dbb-b8ec-e714f70ebabd"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "f92cbvNs3pBuIDcZJI7cvrJ", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "label": null, "surfix": "", "duration": 1.5, "fromValue": 0, "toValue": 0, "range": 0, "sign": 0, "canRun": false, "delta": 0, "formatCurrencyUnit": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "02SfAShBlESIZZhlUKiO5F", "sync": false}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": false, "_string": "<PERSON><PERSON> lượt mở còn lại:  ", "_N$string": "<PERSON><PERSON> lượt mở còn lại:  ", "_fontSize": 36, "_lineHeight": 40, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "f92cbvNs3pBuIDcZJI7cvrJ", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "label": null, "surfix": "", "duration": 1.5, "fromValue": 0, "toValue": 0, "range": 0, "sign": 0, "canRun": false, "delta": 0, "formatCurrencyUnit": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "c2u9a5sdFNw6uRQE76WHs4", "sync": false}, {"__type__": "cc.Node", "_name": "balance", "_objFlags": 0, "_parent": {"__id__": 44}, "_children": [{"__id__": 84}, {"__id__": 87}], "_active": true, "_components": [{"__id__": 91}], "_prefab": {"__id__": 92}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 369, "height": 77}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-67, -267, 0, 0, 0, 0, 1, 1.5, 1.5, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "textTotalWin", "_objFlags": 0, "_parent": {"__id__": 83}, "_children": [], "_active": true, "_components": [{"__id__": 85}], "_prefab": {"__id__": 86}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 306, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 62, 0, 0, 0, 0, 1, 0.66667, 0.66667, 0.66667]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 84}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "da43d6c4-8807-4a3a-90b9-763279372bcc"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "78z+FsAhNGUqWrHgj15HOp", "sync": false}, {"__type__": "cc.Node", "_name": "lbWin", "_objFlags": 0, "_parent": {"__id__": 83}, "_children": [], "_active": true, "_components": [{"__id__": 88}, {"__id__": 89}], "_prefab": {"__id__": 90}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 144.22, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 2, 0, 0, 0, 0, 1, 1.906, 1.906, 1.906]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": false, "_string": "1,000,00,000", "_N$string": "1,000,00,000", "_fontSize": 22, "_lineHeight": 90, "_enableWrapText": false, "_N$file": {"__uuid__": "ec6765b1-0993-4ad0-bfea-92f32931155d"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "f92cbvNs3pBuIDcZJI7cvrJ", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "label": null, "surfix": "", "duration": 1.5, "fromValue": 0, "toValue": 0, "range": 0, "sign": 0, "canRun": false, "delta": 0, "formatCurrencyUnit": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "bcuN+OZLlGiZ3CRrxCxHUA", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 83}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "3e3d9d63-d222-4139-b483-e3abf059ef42"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "5cJg26mMREa4yt12hQmchT", "sync": false}, {"__type__": "cc.Node", "_name": "layout-pick", "_objFlags": 0, "_parent": {"__id__": 44}, "_children": [{"__id__": 94}, {"__id__": 117}, {"__id__": 140}, {"__id__": 163}], "_active": true, "_components": [{"__id__": 186}], "_prefab": {"__id__": 187}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1240, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [52.028, -6.343, 0, 0, 0, 0, 1, 0.9, 0.9, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "button", "_objFlags": 0, "_parent": {"__id__": 93}, "_children": [{"__id__": 95}, {"__id__": 102}], "_active": true, "_components": [{"__id__": 114}], "_prefab": {"__id__": 116}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-480, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "ngoctrai", "_objFlags": 0, "_parent": {"__id__": 94}, "_children": [{"__id__": 96}], "_active": true, "_components": [{"__id__": 100}], "_prefab": {"__id__": 101}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 143}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1.426, 1.426, 1.426]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_effect", "_objFlags": 0, "_parent": {"__id__": 95}, "_children": [], "_active": false, "_components": [{"__id__": 97}, {"__id__": 98}], "_prefab": {"__id__": 99}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 308, "height": 315}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.535, "y": 0.48}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-72.865, 1.774, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 96}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ee484f18-44be-4cc7-94cb-68115a81d5e4"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 96}, "_enabled": true, "_defaultClip": {"__uuid__": "33ad45e8-ca7a-4d86-8b57-daeb57649c00"}, "_clips": [{"__uuid__": "33ad45e8-ca7a-4d86-8b57-daeb57649c00"}], "playOnLoad": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "c1aotIyTVAGo2TlaPJobvp", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 95}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6fd85858-a54b-400e-ba4d-e1a5e785da95"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "484b46mOBD9Yv8RSYivp5c", "sync": false}, {"__type__": "cc.Node", "_name": "bg-coin", "_objFlags": 0, "_parent": {"__id__": 94}, "_children": [{"__id__": 103}, {"__id__": 107}], "_active": true, "_components": [{"__id__": 111}, {"__id__": 112}], "_prefab": {"__id__": 113}, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 287, "height": 86}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -15, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "labelVal", "_objFlags": 0, "_parent": {"__id__": 102}, "_children": [], "_active": false, "_components": [{"__id__": 104}, {"__id__": 105}], "_prefab": {"__id__": 106}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 103}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": false, "_string": "5.000", "_N$string": "5.000", "_fontSize": 50, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "23aae01c-fa56-4dbb-b8ec-e714f70ebabd"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "f92cbvNs3pBuIDcZJI7cvrJ", "_name": "", "_objFlags": 0, "node": {"__id__": 103}, "_enabled": true, "label": null, "surfix": "", "duration": 1.5, "fromValue": 0, "toValue": 0, "range": 0, "sign": 0, "canRun": false, "delta": 0, "formatCurrencyUnit": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "49wEOxfOdFdaZDowVivNz4", "sync": false}, {"__type__": "cc.Node", "_name": "labelMultiplier", "_objFlags": 0, "_parent": {"__id__": 102}, "_children": [], "_active": false, "_components": [{"__id__": 108}, {"__id__": 109}], "_prefab": {"__id__": 110}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 67, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-8.8, 22.2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 107}, "_enabled": true, "_materials": [], "_useOriginalSize": false, "_string": "4", "_N$string": "4", "_fontSize": 32, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "e8a096af-ed6a-4bf1-b63a-d054507be56a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "f92cbvNs3pBuIDcZJI7cvrJ", "_name": "", "_objFlags": 0, "node": {"__id__": 107}, "_enabled": true, "label": null, "surfix": "", "duration": 1.5, "fromValue": 0, "toValue": 0, "range": 0, "sign": 0, "canRun": false, "delta": 0, "formatCurrencyUnit": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "25Ldu9CRhOToxD04O6UYvB", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 102}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "99a7de19-1889-4630-8d2a-aa98452321f9"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 102}, "_enabled": true, "_defaultClip": null, "_clips": [{"__uuid__": "9ac0ab89-7b46-4075-8b9a-a0b65839aa04"}], "playOnLoad": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "1d4Y3V599DMbB7NHvtBAZc", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 94}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.05, "clickEvents": [{"__id__": 115}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 94}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 44}, "component": "", "_componentId": "bbb57j8bZFCnJBmJ+EWCoBg", "handler": "pickClicked", "customEventData": "1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "b0ecocl8VIHrr0/R9zRB88", "sync": false}, {"__type__": "cc.Node", "_name": "button", "_objFlags": 0, "_parent": {"__id__": 93}, "_children": [{"__id__": 118}, {"__id__": 125}], "_active": true, "_components": [{"__id__": 137}], "_prefab": {"__id__": 139}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "ngoctrai", "_objFlags": 0, "_parent": {"__id__": 117}, "_children": [{"__id__": 119}], "_active": true, "_components": [{"__id__": 123}], "_prefab": {"__id__": 124}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 143}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1.456, 1.456, 1.456]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_effect", "_objFlags": 0, "_parent": {"__id__": 118}, "_children": [], "_active": false, "_components": [{"__id__": 120}, {"__id__": 121}], "_prefab": {"__id__": 122}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 308, "height": 315}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.535, "y": 0.48}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-67.121, -4.537, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 119}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ee484f18-44be-4cc7-94cb-68115a81d5e4"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 119}, "_enabled": true, "_defaultClip": {"__uuid__": "33ad45e8-ca7a-4d86-8b57-daeb57649c00"}, "_clips": [{"__uuid__": "33ad45e8-ca7a-4d86-8b57-daeb57649c00"}], "playOnLoad": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "36BZ8nXcBKRamrdzRLeVQE", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 118}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6fd85858-a54b-400e-ba4d-e1a5e785da95"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "dbR0DK12lNG6279ILxQHdo", "sync": false}, {"__type__": "cc.Node", "_name": "bg-coin", "_objFlags": 0, "_parent": {"__id__": 117}, "_children": [{"__id__": 126}, {"__id__": 130}], "_active": true, "_components": [{"__id__": 134}, {"__id__": 135}], "_prefab": {"__id__": 136}, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 287, "height": 86}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -15, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "labelVal", "_objFlags": 0, "_parent": {"__id__": 125}, "_children": [], "_active": false, "_components": [{"__id__": 127}, {"__id__": 128}], "_prefab": {"__id__": 129}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 62.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 126}, "_enabled": true, "_materials": [], "_useOriginalSize": false, "_string": "5.000", "_N$string": "5.000", "_fontSize": 50, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "23aae01c-fa56-4dbb-b8ec-e714f70ebabd"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "f92cbvNs3pBuIDcZJI7cvrJ", "_name": "", "_objFlags": 0, "node": {"__id__": 126}, "_enabled": true, "label": null, "surfix": "", "duration": 1.5, "fromValue": 0, "toValue": 0, "range": 0, "sign": 0, "canRun": false, "delta": 0, "formatCurrencyUnit": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "5daQBz5H1MgbD0EI0HzKLK", "sync": false}, {"__type__": "cc.Node", "_name": "labelMultiplier", "_objFlags": 0, "_parent": {"__id__": 125}, "_children": [], "_active": false, "_components": [{"__id__": 131}, {"__id__": 132}], "_prefab": {"__id__": 133}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 67, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-8.8, 22.2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 130}, "_enabled": true, "_materials": [], "_useOriginalSize": false, "_string": "4", "_N$string": "4", "_fontSize": 32, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "e8a096af-ed6a-4bf1-b63a-d054507be56a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "f92cbvNs3pBuIDcZJI7cvrJ", "_name": "", "_objFlags": 0, "node": {"__id__": 130}, "_enabled": true, "label": null, "surfix": "", "duration": 1.5, "fromValue": 0, "toValue": 0, "range": 0, "sign": 0, "canRun": false, "delta": 0, "formatCurrencyUnit": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "0cj4pZw/hLIpAaHEh2a7eP", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 125}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "99a7de19-1889-4630-8d2a-aa98452321f9"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 125}, "_enabled": true, "_defaultClip": null, "_clips": [{"__uuid__": "9ac0ab89-7b46-4075-8b9a-a0b65839aa04"}], "playOnLoad": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "19sUyGyVlBB5xl6GtX+d1Z", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 117}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.05, "clickEvents": [{"__id__": 138}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 117}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 44}, "component": "", "_componentId": "bbb57j8bZFCnJBmJ+EWCoBg", "handler": "pickClicked", "customEventData": "2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "23PBEYnudHeJjo6LVtsQCF", "sync": false}, {"__type__": "cc.Node", "_name": "button", "_objFlags": 0, "_parent": {"__id__": 93}, "_children": [{"__id__": 141}, {"__id__": 148}], "_active": true, "_components": [{"__id__": 160}], "_prefab": {"__id__": 162}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "ngoctrai", "_objFlags": 0, "_parent": {"__id__": 140}, "_children": [{"__id__": 142}], "_active": true, "_components": [{"__id__": 146}], "_prefab": {"__id__": 147}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 143}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1.456, 1.456, 1.456]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_effect", "_objFlags": 0, "_parent": {"__id__": 141}, "_children": [], "_active": false, "_components": [{"__id__": 143}, {"__id__": 144}], "_prefab": {"__id__": 145}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 308, "height": 315}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.535, "y": 0.48}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-68.576, -8.903, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 142}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ee484f18-44be-4cc7-94cb-68115a81d5e4"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 142}, "_enabled": true, "_defaultClip": {"__uuid__": "33ad45e8-ca7a-4d86-8b57-daeb57649c00"}, "_clips": [{"__uuid__": "33ad45e8-ca7a-4d86-8b57-daeb57649c00"}], "playOnLoad": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "dbbTvZYHRE2JFNVR/LFR6r", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 141}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6fd85858-a54b-400e-ba4d-e1a5e785da95"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "72UMHC2FNAlYtxS8uUXV5N", "sync": false}, {"__type__": "cc.Node", "_name": "bg-coin", "_objFlags": 0, "_parent": {"__id__": 140}, "_children": [{"__id__": 149}, {"__id__": 153}], "_active": true, "_components": [{"__id__": 157}, {"__id__": 158}], "_prefab": {"__id__": 159}, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 287, "height": 86}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -15, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "labelVal", "_objFlags": 0, "_parent": {"__id__": 148}, "_children": [], "_active": false, "_components": [{"__id__": 150}, {"__id__": 151}], "_prefab": {"__id__": 152}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 62.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 149}, "_enabled": true, "_materials": [], "_useOriginalSize": false, "_string": "5.000", "_N$string": "5.000", "_fontSize": 50, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "23aae01c-fa56-4dbb-b8ec-e714f70ebabd"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "f92cbvNs3pBuIDcZJI7cvrJ", "_name": "", "_objFlags": 0, "node": {"__id__": 149}, "_enabled": true, "label": null, "surfix": "", "duration": 1.5, "fromValue": 0, "toValue": 0, "range": 0, "sign": 0, "canRun": false, "delta": 0, "formatCurrencyUnit": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "5eR6H+CoZD8pSnLG2E6SAU", "sync": false}, {"__type__": "cc.Node", "_name": "labelMultiplier", "_objFlags": 0, "_parent": {"__id__": 148}, "_children": [], "_active": false, "_components": [{"__id__": 154}, {"__id__": 155}], "_prefab": {"__id__": 156}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 67, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-8.8, 22.2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 153}, "_enabled": true, "_materials": [], "_useOriginalSize": false, "_string": "4", "_N$string": "4", "_fontSize": 32, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "e8a096af-ed6a-4bf1-b63a-d054507be56a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "f92cbvNs3pBuIDcZJI7cvrJ", "_name": "", "_objFlags": 0, "node": {"__id__": 153}, "_enabled": true, "label": null, "surfix": "", "duration": 1.5, "fromValue": 0, "toValue": 0, "range": 0, "sign": 0, "canRun": false, "delta": 0, "formatCurrencyUnit": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "9eN9dkuhxKd66DRFJGKNJ7", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 148}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "99a7de19-1889-4630-8d2a-aa98452321f9"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 148}, "_enabled": true, "_defaultClip": null, "_clips": [{"__uuid__": "9ac0ab89-7b46-4075-8b9a-a0b65839aa04"}], "playOnLoad": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "4fnGFGvOVL84WUaHzPlzZ2", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 140}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.05, "clickEvents": [{"__id__": 161}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 140}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 44}, "component": "", "_componentId": "bbb57j8bZFCnJBmJ+EWCoBg", "handler": "pickClicked", "customEventData": "3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "4fnz5XC/5Hx6M+VZ6CxSiH", "sync": false}, {"__type__": "cc.Node", "_name": "button", "_objFlags": 0, "_parent": {"__id__": 93}, "_children": [{"__id__": 164}, {"__id__": 171}], "_active": true, "_components": [{"__id__": 183}], "_prefab": {"__id__": 185}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [480, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "ngoctrai", "_objFlags": 0, "_parent": {"__id__": 163}, "_children": [{"__id__": 165}], "_active": true, "_components": [{"__id__": 169}], "_prefab": {"__id__": 170}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 143}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1.456, 1.456, 1.456]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_effect", "_objFlags": 0, "_parent": {"__id__": 164}, "_children": [], "_active": false, "_components": [{"__id__": 166}, {"__id__": 167}], "_prefab": {"__id__": 168}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 308, "height": 315}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.535, "y": 0.48}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-70.032, -0.171, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 165}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ee484f18-44be-4cc7-94cb-68115a81d5e4"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 165}, "_enabled": true, "_defaultClip": {"__uuid__": "33ad45e8-ca7a-4d86-8b57-daeb57649c00"}, "_clips": [{"__uuid__": "33ad45e8-ca7a-4d86-8b57-daeb57649c00"}], "playOnLoad": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "f5+i1r0OxP/qdyhTbhtpTt", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 164}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6fd85858-a54b-400e-ba4d-e1a5e785da95"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "d52DANhYhMvJkPBVJRZXs/", "sync": false}, {"__type__": "cc.Node", "_name": "bg-coin", "_objFlags": 0, "_parent": {"__id__": 163}, "_children": [{"__id__": 172}, {"__id__": 176}], "_active": true, "_components": [{"__id__": 180}, {"__id__": 181}], "_prefab": {"__id__": 182}, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 287, "height": 86}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -15, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "labelVal", "_objFlags": 0, "_parent": {"__id__": 171}, "_children": [], "_active": false, "_components": [{"__id__": 173}, {"__id__": 174}], "_prefab": {"__id__": 175}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 62.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 172}, "_enabled": true, "_materials": [], "_useOriginalSize": false, "_string": "5.000", "_N$string": "5.000", "_fontSize": 50, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "23aae01c-fa56-4dbb-b8ec-e714f70ebabd"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "f92cbvNs3pBuIDcZJI7cvrJ", "_name": "", "_objFlags": 0, "node": {"__id__": 172}, "_enabled": true, "label": null, "surfix": "", "duration": 1.5, "fromValue": 0, "toValue": 0, "range": 0, "sign": 0, "canRun": false, "delta": 0, "formatCurrencyUnit": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "09EwHWl05Cka9HlycZ+Cvv", "sync": false}, {"__type__": "cc.Node", "_name": "labelMultiplier", "_objFlags": 0, "_parent": {"__id__": 171}, "_children": [], "_active": false, "_components": [{"__id__": 177}, {"__id__": 178}], "_prefab": {"__id__": 179}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 67, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-8.8, 22.2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 176}, "_enabled": true, "_materials": [], "_useOriginalSize": false, "_string": "4", "_N$string": "4", "_fontSize": 32, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "e8a096af-ed6a-4bf1-b63a-d054507be56a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "f92cbvNs3pBuIDcZJI7cvrJ", "_name": "", "_objFlags": 0, "node": {"__id__": 176}, "_enabled": true, "label": null, "surfix": "", "duration": 1.5, "fromValue": 0, "toValue": 0, "range": 0, "sign": 0, "canRun": false, "delta": 0, "formatCurrencyUnit": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "76q5CSbIVPtJHu/jWIZ0/Y", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 171}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "99a7de19-1889-4630-8d2a-aa98452321f9"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 171}, "_enabled": true, "_defaultClip": null, "_clips": [{"__uuid__": "9ac0ab89-7b46-4075-8b9a-a0b65839aa04"}], "playOnLoad": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "f2EqV35ZlPyL7WnfwUieXQ", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 163}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.05, "clickEvents": [{"__id__": 184}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 163}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 44}, "component": "", "_componentId": "bbb57j8bZFCnJBmJ+EWCoBg", "handler": "pickClicked", "customEventData": "4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "03N+RSzTJEFLH0JKy9xXs5", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 93}, "_enabled": false, "_layoutSize": {"__type__": "cc.Size", "width": 1240, "height": 200}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 40, "_N$spacingY": 25, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "3dL1S9XpZHLZGaMSaUpIk2", "sync": false}, {"__type__": "cc.Node", "_name": "btnQuickPlay", "_objFlags": 0, "_parent": {"__id__": 44}, "_children": [], "_active": true, "_components": [{"__id__": 189}, {"__id__": 190}], "_prefab": {"__id__": 192}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 103, "height": 103}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [410.958, -285.912, 0, 0, 0, 0, 1, 2.385, 2.385, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 188}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a27e38f2-65b3-4cf6-b40e-10e1c6ee5baf"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 188}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.05, "clickEvents": [{"__id__": 191}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": {"__uuid__": "6ae61e38-9337-4464-ba6c-5f9261f0efa8"}, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 188}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "060a6/zuN5GgayVIQsDGq4s", "handler": "quickPlayClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "feufIO/VRBAJTZEWrQTlcY", "sync": false}, {"__type__": "bbb57j8bZFCnJBmJ+EWCoBg", "_name": "", "_objFlags": 0, "node": {"__id__": 44}, "_enabled": true, "btnPicks": [{"__id__": 114}, {"__id__": 137}, {"__id__": 160}, {"__id__": 183}], "lbiPrizes": [{"__id__": 105}, {"__id__": 128}, {"__id__": 151}, {"__id__": 174}], "lbMultipliers": [{"__id__": 108}, {"__id__": 131}, {"__id__": 154}, {"__id__": 177}], "spriteBGPrizes": [{"__id__": 111}, {"__id__": 134}, {"__id__": 157}, {"__id__": 180}], "nodeEffects": [{"__id__": 96}, {"__id__": 119}, {"__id__": 142}, {"__id__": 165}], "animationPicks": [{"__id__": 112}, {"__id__": 135}, {"__id__": 158}, {"__id__": 181}], "sfPicks": [{"__uuid__": "46ea657c-33f7-4784-98f0-91440f9dfea6"}, {"__uuid__": "7b2d2cbe-db6a-4d73-966c-189364a94d97"}, {"__uuid__": "8751a401-20b4-425c-9ddd-7af7e599dcd0"}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "b1mPc2fbFJzKwTgDGESQWe", "sync": false}, {"__type__": "cc.Node", "_name": "bonusGameResultView", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 196}, {"__id__": 201}, {"__id__": 204}, {"__id__": 207}, {"__id__": 210}, {"__id__": 213}], "_active": false, "_components": [{"__id__": 216}], "_prefab": {"__id__": 217}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1174, "height": 452}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 3.349, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "black", "_objFlags": 0, "_parent": {"__id__": 195}, "_children": [], "_active": true, "_components": [{"__id__": 197}, {"__id__": 198}], "_prefab": {"__id__": 200}, "_opacity": 100, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 3000, "height": 3000}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 196}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a95690f4-0cfe-4b25-840f-5c84d9abeba3"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 196}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 199}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 195}, "component": "", "_componentId": "7269ege3OpEV4lKd6fET93f", "handler": "closeClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "a9IrEj7vRBEKPyBbxzzRMj", "sync": false}, {"__type__": "cc.Node", "_name": "nen popup", "_objFlags": 0, "_parent": {"__id__": 195}, "_children": [], "_active": false, "_components": [{"__id__": 202}], "_prefab": {"__id__": 203}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 955, "height": 594}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -24, 0, 0, 0, 0, 1, 1.255, 1.255, 1.255]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 201}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "4b8b6720-d94b-4767-88f2-d4145f023b60"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "5f33d4pCRCZ4slKiIpifBs", "sync": false}, {"__type__": "cc.Node", "_name": "tex_<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 195}, "_children": [], "_active": true, "_components": [{"__id__": 205}], "_prefab": {"__id__": 206}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 206, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 112.569, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 204}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6383f731-e50b-4bb0-a313-bdb5912d187c"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "ebXXCktn9GsrVMoc7vVuAR", "sync": false}, {"__type__": "cc.Node", "_name": "bg_popup", "_objFlags": 0, "_parent": {"__id__": 195}, "_children": [], "_active": true, "_components": [{"__id__": 208}], "_prefab": {"__id__": 209}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 299, "height": 145}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 14.867, 0, 0, 0, 0, 1, 3.874, 3.874, 3.874]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 207}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "c0b74d13-8474-4ffc-bb55-76f9e7b3dd19"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "55MLuSgf9FjIhkHWiY0HP4", "sync": false}, {"__type__": "cc.Node", "_name": "lbWin", "_objFlags": 0, "_parent": {"__id__": 195}, "_children": [], "_active": true, "_components": [{"__id__": 211}], "_prefab": {"__id__": 212}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 549, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 31.393, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 210}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f5fae496-b355-4a2b-b128-a68a20d26f23"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "ddyD8CiylJIqFZXoQRmdbB", "sync": false}, {"__type__": "cc.Node", "_name": "lbWinVal", "_objFlags": 0, "_parent": {"__id__": 195}, "_children": [], "_active": true, "_components": [{"__id__": 214}], "_prefab": {"__id__": 215}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 700, "height": 156.25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -95, 0, 0, 0, 0, 1, 1.521, 1.521, 1.521]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 213}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": false, "_string": "1,000,000,000", "_N$string": "1,000,000,000", "_fontSize": 50, "_lineHeight": 100, "_enableWrapText": true, "_N$file": {"__uuid__": "ec6765b1-0993-4ad0-bfea-92f32931155d"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "860VtqvRhICIfmeghkR+1b", "sync": false}, {"__type__": "7269ege3OpEV4lKd6fET93f", "_name": "", "_objFlags": 0, "node": {"__id__": 195}, "_enabled": true, "lbResult": {"__id__": 214}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "e3+hEREDhArZp9kohFmFcX", "sync": false}, {"__type__": "060a6/zuN5GgayVIQsDGq4s", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "nodeStart": {"__id__": 9}, "nodeBonus": {"__id__": 44}, "nodePick": {"__id__": 93}, "nodeResult": {"__id__": 195}, "lbTime": {"__id__": 70}, "lbMultiplier": {"__id__": 56}, "lbRemaining": {"__id__": 77}, "lbiWin": {"__id__": 89}, "btnQuickPlay": {"__id__": 190}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "6b2fde29-91b3-4f94-971f-0d49559ddea5"}, "fileId": "6fl0ppGaVK6J1jykS3QEm+", "sync": false}]