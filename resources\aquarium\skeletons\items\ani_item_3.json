{"skeleton": {"hash": "ufS6YJ81JiPZROBzAy+zms7XIuU", "spine": "3.6.53", "width": 144, "height": 164}, "bones": [{"name": "root"}, {"name": "item_3h", "parent": "root", "length": 45.54, "rotation": 53.7, "x": -26.01, "y": -19.16}, {"name": "item_3d", "parent": "item_3h", "length": 40.47, "rotation": 34.18, "x": 59.88, "y": -49.88}, {"name": "item_3e", "parent": "item_3h", "length": 57.34, "rotation": -77.58, "x": 35.24, "y": 34.26}], "slots": [{"name": "item_3d", "bone": "item_3d", "attachment": "item_3d"}, {"name": "item_3h", "bone": "item_3h", "attachment": "item_3h"}, {"name": "item_3e", "bone": "item_3e"}], "skins": {"default": {"item_3d": {"item_3d": {"x": 8.31, "y": 30.47, "rotation": -87.88, "width": 104, "height": 107}}, "item_3e": {"item_3e": {"x": 29.99, "y": -0.42, "rotation": 23.89, "width": 73, "height": 44}}, "item_3h": {"item_3h": {"x": 29.73, "y": -9.81, "rotation": -53.7, "width": 144, "height": 164}}}}, "animations": {"animation": {"slots": {"item_3e": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}, {"time": 0.5, "name": "item_3e"}, {"time": 0.8333, "name": null}, {"time": 1.3333, "name": null}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "item_3h": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -4.49}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "item_3d": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -14.59}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 1.41, "y": -3.95}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1.079, "y": 1}, {"time": 1.3333, "x": 1, "y": 1}]}, "item_3e": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}}}, "win": {"slots": {"item_3e": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}, {"time": 0.5, "name": "item_3e"}, {"time": 0.8333, "name": null}, {"time": 1.3333, "name": null}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "item_3d": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -14.59}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 1.41, "y": -3.95}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1.079, "y": 1}, {"time": 1.3333, "x": 1, "y": 1}]}, "item_3e": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "item_3h": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -4.49}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": 2.28, "y": 15.93}, {"time": 0.6667, "x": -1.9, "y": -0.76}, {"time": 1, "x": 2.28, "y": 15.93}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}}}}}