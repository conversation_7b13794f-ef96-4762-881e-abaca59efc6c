{"skeleton": {"hash": "iULsHd6FzRDpnp2ltMH5Id07bJE", "spine": "3.6.53", "width": 450, "height": 618}, "bones": [{"name": "root"}, {"name": "v", "parent": "root", "length": 178.72, "rotation": 84.24, "x": 24.09, "y": -184.51}, {"name": "bb2", "parent": "v", "length": 14.33, "rotation": -149.57, "x": 215.89, "y": 118.51}, {"name": "bh3", "parent": "v", "x": 283.23, "y": -29.09}, {"name": "bh2", "parent": "bh3", "x": 57.94, "y": 22.31}, {"name": "bh1", "parent": "bh2", "x": 37.8, "y": 41.86}, {"name": "bo", "parent": "v", "rotation": 3.69, "x": 164.9, "y": 31.55}, {"name": "bo2", "parent": "bo", "x": 15.1, "y": -56.87}, {"name": "t1p", "parent": "v", "length": 39.64, "rotation": -126, "x": 340.06, "y": 62.39}, {"name": "t1t", "parent": "v", "length": 19.92, "rotation": 153.75, "x": 326.91, "y": 80.52}, {"name": "t2p", "parent": "t1p", "length": 47.5, "rotation": -60.22, "x": 61.58, "y": -8.72}, {"name": "t2t", "parent": "t1t", "length": 59.86, "rotation": 33.35, "x": 27.5, "y": 1.83}, {"name": "t3p", "parent": "t2p", "length": 29.28, "rotation": -6.24, "x": 0.97, "y": -6.27}], "slots": [{"name": "mermaid", "bone": "v", "attachment": "mermaid"}, {"name": "bo", "bone": "bo", "attachment": "bo"}, {"name": "t2p", "bone": "t2p", "attachment": "t2p"}, {"name": "t3p", "bone": "t3p", "attachment": "t3p"}, {"name": "t1p", "bone": "t1p", "attachment": "t1p"}, {"name": "t2t", "bone": "t2t", "attachment": "t2t"}, {"name": "t1t", "bone": "t1t", "attachment": "t1t"}, {"name": "bb2", "bone": "bb2", "attachment": "bb2", "blend": "additive"}, {"name": "bh1", "bone": "bh1", "attachment": "bh1", "blend": "additive"}, {"name": "bh2", "bone": "bh2", "attachment": "bh2", "blend": "additive"}, {"name": "bh3", "bone": "bh3", "attachment": "bh3", "blend": "additive"}], "skins": {"default": {"bb2": {"bb2": {"x": 6.71, "y": 4.17, "rotation": 65.32, "width": 54, "height": 70}}, "bh1": {"bh1": {"x": 2.41, "y": 13.26, "rotation": -84.24, "width": 91, "height": 49}}, "bh2": {"bh2": {"x": -3.12, "y": -0.05, "rotation": -91.55, "width": 72, "height": 67}}, "bh3": {"bh3": {"x": -4.91, "y": 0.22, "rotation": -84.24, "width": 45, "height": 55}}, "bo": {"bo": {"type": "mesh", "uvs": [0.86311, 0.12773, 0.89513, 0.21517, 1, 0.39007, 1, 0.60679, 0.8471, 0.83111, 0.69342, 0.94137, 0.42447, 1, 0.14271, 0.92616, 0, 0.71325, 0, 0.261, 0.10973, 0.1225, 0.18635, 0.07958, 0.32842, 0, 0.55574, 0, 0.83109, 0, 0.40846, 0.08971], "triangles": [5, 6, 8, 15, 4, 5, 15, 5, 8, 4, 15, 1, 13, 1, 15, 7, 8, 6, 4, 1, 3, 1, 13, 0, 0, 13, 14, 15, 9, 10, 10, 11, 15, 15, 8, 9, 1, 2, 3, 11, 12, 15, 15, 12, 13], "vertices": [2, 6, 48.72, -58.26, 0.10994, 7, 33.62, -1.39, 0.89006, 2, 6, 40.46, -62.21, 0.05662, 7, 25.36, -5.34, 0.94338, 2, 6, 24.11, -74.76, 0, 7, 9.01, -17.89, 1, 2, 6, 3.32, -75.51, 0.00469, 7, -11.78, -18.64, 0.99531, 2, 6, -18.83, -58.87, 0.21449, 7, -33.93, -2, 0.78551, 2, 6, -30.04, -41.74, 0.48019, 7, -45.14, 15.13, 0.51981, 2, 6, -36.77, -11.3, 0.86441, 7, -51.87, 45.57, 0.13559, 2, 6, -30.84, 21.05, 0.99912, 7, -45.94, 77.92, 0.00088, 1, 6, -11, 38.05, 1, 2, 6, 32.38, 39.61, 0.97904, 7, 17.28, 96.48, 0.02096, 2, 6, 46.12, 27.59, 0.92516, 7, 31.02, 84.46, 0.07484, 2, 6, 50.55, 19.01, 0.87338, 7, 35.45, 75.88, 0.12662, 2, 6, 58.77, 3.1, 0.73479, 7, 43.67, 59.97, 0.26521, 2, 6, 59.71, -22.8, 0.43915, 7, 44.61, 34.07, 0.56085, 2, 6, 60.84, -54.17, 0.15286, 7, 45.74, 2.7, 0.84714, 2, 6, 50.5, -6.33, 0.66075, 7, 35.4, 50.54, 0.33925], "hull": 15}}, "mermaid": {"mermaid": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0, 0.4349, 0.39282, 0.32315, 0.5071, 0.29051, 0.47967, 0.23903, 0.38459, 0.23903, 0.36265, 0.27293, 0.28128, 0.65587, 0.38185, 0.6835, 0.52447, 0.73623, 0.57018, 0.85802, 0.63418, 0.65085, 0.29499, 0.67722, 0.34162, 0.68224, 0.25111, 0.79273, 0.24197, 0.6496, 0.18803, 0.66592, 0.103, 0.43866, 0.1478, 0.45499, 0.08746, 0.35077, 0.25111, 0.28423, 0.18071, 0.54949, 0.67776, 0.72946, 0.73586, 0.80076, 0.8088, 0.79907, 0.86196, 0.7702, 0.9077, 0.58684, 0.96704, 0.53625, 0.41692, 0.53259, 0.47722, 0.63854, 0.42135, 0.69334, 0.44973, 0.66464, 0.42712, 0.47269, 0.28091, 0.48142, 0.29448, 0.49947, 0.31652, 0.52392, 0.33178, 0.54838, 0.33729, 0.55828, 0.3339, 0.57632, 0.32415, 0.58855, 0.30465, 0.59437, 0.27879, 0.59088, 0.25378, 0.5804, 0.22834, 0.54488, 0.20714, 0.51519, 0.2063, 0.4855, 0.21605, 0.47152, 0.24657, 0.46803, 0.2665], "triangles": [21, 2, 3, 19, 21, 3, 21, 23, 2, 20, 23, 21, 21, 46, 47, 47, 20, 21, 46, 21, 19, 18, 46, 19, 48, 20, 47, 45, 46, 18, 17, 19, 3, 18, 19, 17, 49, 20, 48, 16, 18, 17, 44, 45, 18, 22, 23, 20, 22, 20, 49, 16, 44, 18, 50, 22, 49, 14, 43, 44, 36, 35, 49, 50, 49, 35, 9, 2, 23, 9, 23, 22, 49, 47, 36, 16, 14, 44, 44, 46, 45, 42, 44, 43, 42, 43, 14, 48, 47, 49, 37, 36, 44, 46, 36, 47, 46, 44, 36, 37, 44, 38, 42, 41, 44, 41, 38, 44, 40, 38, 41, 39, 38, 40, 15, 14, 16, 8, 2, 9, 15, 42, 14, 10, 42, 15, 41, 42, 10, 4, 7, 8, 35, 4, 22, 35, 22, 50, 4, 35, 36, 4, 36, 37, 30, 38, 39, 32, 41, 10, 33, 34, 10, 32, 10, 34, 17, 15, 16, 33, 15, 17, 33, 10, 15, 30, 4, 38, 8, 9, 4, 4, 9, 22, 6, 7, 4, 5, 6, 4, 4, 37, 38, 31, 4, 30, 11, 34, 33, 13, 12, 33, 11, 33, 12, 13, 17, 3, 13, 33, 17, 40, 30, 39, 30, 32, 31, 31, 11, 24, 40, 41, 32, 40, 32, 30, 12, 24, 11, 25, 12, 13, 32, 34, 11, 11, 31, 32, 25, 24, 12, 26, 25, 13, 27, 25, 26, 28, 25, 27, 29, 24, 25, 29, 25, 28, 7, 1, 2, 7, 2, 8, 1, 7, 6, 1, 6, 5, 5, 4, 31, 13, 3, 0, 26, 13, 0, 27, 26, 0, 28, 27, 0, 29, 28, 0, 5, 31, 24, 1, 5, 24, 1, 24, 29, 1, 29, 0], "vertices": [-135.51, -198.56, -180.64, 249.17, 434.24, 311.16, 479.38, -136.57, 212.33, 92.09, 137.02, 135.04, 152.41, 151.35, 208.55, 180.3, 222.05, 181.66, 273.61, 171.52, 229.05, -6.17, 142.61, -27.37, 116.88, -53.82, 83.02, -112.31, 282.23, 1.46, 254.75, -13.23, 310.63, -9.87, 321.24, -58.77, 347.95, 8.66, 400.96, 6.62, 363.16, 105.59, 401, 102.02, 295.67, 138.54, 335.95, 172.7, 42.3, 23.12, 14.7, -61.06, -26.93, -97.5, -59.7, -100.04, -89.12, -89.95, -133.89, -11.53, 202.09, 45.22, 164.85, 43.12, 203.98, -0.85, 189.01, -27.15, 201.61, -12.9, 282.85, 82.11, 274.9, 77.36, 262.16, 67.91, 253.88, 56.01, 251.6, 44.72, 254.13, 40.5, 260.94, 33.03, 273.48, 28.76, 289.65, 27.76, 304.87, 30.87, 320.04, 37.14, 331.47, 54.36, 330.65, 67.7, 323.31, 80.39, 303.91, 84.76, 291.5, 85.09], "hull": 4}}, "t1p": {"t1p": {"x": 28.19, "y": -1.95, "rotation": 41.76, "width": 78, "height": 60}}, "t1t": {"t1t": {"x": 32.15, "y": 8.68, "rotation": 122.01, "width": 52, "height": 77}}, "t2p": {"t2p": {"x": 28.31, "y": -2.97, "rotation": 101.98, "width": 48, "height": 91}}, "t2t": {"t2t": {"x": 24.89, "y": 2.76, "rotation": 88.65, "width": 59, "height": 101}}, "t3p": {"t3p": {"x": 16.03, "y": -0.52, "rotation": 108.22, "width": 29, "height": 48}}}}, "animations": {"animation": {"bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "v": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "bo": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.7333, "x": 1.38, "y": 2.57}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2.2667, "x": 1.38, "y": 2.57}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": 1.055, "y": 1.009}, {"time": 1.5, "x": 1, "y": 1}, {"time": 2.2667, "x": 1.055, "y": 1.009}, {"time": 3, "x": 1, "y": 1}]}, "bo2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.7333, "x": 0.22, "y": -6.43}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2.2333, "x": 0.22, "y": -6.43}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "t1t": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.3333, "angle": -4.88}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "t2t": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.3333, "angle": 2.74}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1.3333, "x": 1.085, "y": 1.096}, {"time": 3, "x": 1, "y": 1}]}, "t1p": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.3333, "angle": 3.4}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "t2p": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.3333, "angle": 3.38}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "t3p": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.3333, "angle": -13.41}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "bb2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.5, "x": 8.52, "y": -0.35}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1.5, "x": 0.805, "y": 1}, {"time": 3, "x": 1, "y": 1}]}, "bh3": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.5, "angle": -3.35}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.5, "x": -3.03, "y": 12.9}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "bh2": {"rotate": [{"time": 0, "angle": 0}, {"time": 2.3, "angle": -6.73}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.5, "x": -1.66, "y": -2.84}, {"time": 1.5, "x": 2.84, "y": -15.26}, {"time": 2.3, "x": 0.07, "y": -0.62}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 0.915}, {"time": 3, "x": 1, "y": 1}]}, "bh1": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.5, "angle": -17.47}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.5, "x": -25.46, "y": 14.93}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}}, "deform": {"default": {"mermaid": {"mermaid": [{"time": 0}, {"time": 1.3333, "offset": 8, "vertices": [3.6492, -3.7189, -1.85547, -15.28082, -2.73512, -11.88983, -5.87627, -6.47395, -6.07721, -5.14494, -5.32709, 0.00958, 0, 0, -0.20587, -4.84687, 1.24263, -6.18276, 4.41399, -7.90678, -9.43681, 1.66655, 1.42511, -13.04255, -8.43416, -7.19121, -8.43416, -7.19121, 2.42918, -2.09257, -0.4595, -17.43645, 29.16859, 3.63413, 27.88466, -3.21196, 12.79729, -3.6427, 6.64137, 2.50833, 7.00348, -0.88673, -3.5668, 3.34984, -7.89827, 8.99137, -7.95284, 13.21515, -6.41563, 16.90686, 4.01369, 22.00121, 0, 0, 0, 0, 0, 0, 0, 0, -1.0629, -1.71712]}, {"time": 3}]}}}}}}