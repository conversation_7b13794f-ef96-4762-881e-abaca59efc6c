{"skeleton": {"hash": "d8Ajj5lIBQYUbbSMNnjDhegnLcg", "spine": "3.7.93", "width": 195.26, "height": 147.49, "images": "", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 11.44, "rotation": 90.65, "x": 0.04, "y": 4.99}, {"name": "bone2", "parent": "root", "x": 2.39, "y": -4.45}, {"name": "bone3", "parent": "bone2", "length": 7.92, "rotation": 90, "x": -0.97, "y": 39.28}, {"name": "bone4", "parent": "root", "x": -0.65, "y": -19.4}, {"name": "bone13", "parent": "root", "length": 19.73, "rotation": -1.55, "x": 3.55, "y": -4.35}], "slots": [{"name": "shadow", "bone": "root", "attachment": "shadow"}, {"name": "caidia", "bone": "root", "attachment": "caidia"}, {"name": "caito", "bone": "root", "attachment": "caito"}, {"name": "MoBat", "bone": "root", "attachment": "MoBat"}], "skins": {"default": {"MoBat": {"MoBat": {"type": "mesh", "uvs": [0, 1, 1, 1, 1, 0, 0, 0], "triangles": [0, 3, 2, 0, 2, 1], "vertices": [1, 5, -58.15, -13.84, 1, 1, 5, 58.81, -10.68, 1, 1, 5, 57.95, 21.3, 1, 1, 5, -59.01, 18.14, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 32}}, "caidia": {"caidia": {"type": "mesh", "uvs": [0, 0, 1, 0, 1, 1, 0, 1], "triangles": [3, 0, 1, 3, 1, 2], "vertices": [1, 1, 57.99, 88.36, 1, 1, 1, 55.87, -97.62, 1, 1, 1, -79.12, -96.09, 1, 1, 1, -77.01, 89.9, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 186, "height": 135}}, "caito": {"caito": {"type": "mesh", "uvs": [0, 1, 0, 0, 1, 0, 1, 1], "triangles": [0, 1, 2, 0, 2, 3], "vertices": [2, 2, -64.79, -40.39, 0.93659, 3, -79.68, 63.82, 0.06341, 2, 2, -64.79, 72.61, 0.07392, 3, 33.32, 63.82, 0.92608, 2, 2, 67.21, 72.61, 0.12612, 3, 33.32, -68.18, 0.87388, 2, 2, 67.21, -40.39, 0.95321, 3, -79.68, -68.18, 0.04679], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 132, "height": 113}}, "shadow": {"shadow": {"type": "mesh", "uvs": [0, 0.15527, 1, 0.15527, 1, 1, 0, 1], "triangles": [3, 0, 1, 3, 1, 2], "vertices": [1, 4, -95.46, 62.05, 1, 1, 4, 99.81, 62.05, 1, 1, 4, 99.81, -59.92, 1, 1, 4, -95.46, -59.92, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 119, "height": 88}}}}, "animations": {"Waiting": {"slots": {"MoBat": {"attachment": [{"time": 0, "name": null}]}}, "bones": {"bone4": {"scale": [{"time": 1.1667, "x": 1, "y": 1}]}}}, "XocXoc": {"slots": {"MoBat": {"attachment": [{"time": 0, "name": null}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1333, "angle": -4}, {"time": 0.2333, "angle": 0}, {"time": 0.3333, "angle": 4.94}, {"time": 0.4333, "angle": 0}, {"time": 0.5333, "angle": -4}, {"time": 0.6333, "angle": 0}, {"time": 0.7333, "angle": 4.94}, {"time": 0.8333, "angle": 0}, {"time": 0.9333, "angle": -5.21}, {"time": 1.1667, "angle": 0}, {"time": 1.3, "angle": -4}, {"time": 1.4, "angle": 0}, {"time": 1.5, "angle": 4.94}, {"time": 1.6, "angle": 0}, {"time": 1.7, "angle": -4}, {"time": 1.8, "angle": 0}, {"time": 1.9, "angle": 4.94}, {"time": 2, "angle": 0}, {"time": 2.1, "angle": -5.21}, {"time": 2.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1333, "x": 0, "y": 15.75}, {"time": 0.2333, "x": 0, "y": 8.55}, {"time": 0.3333, "x": 0, "y": 14.4}, {"time": 0.4333, "x": 0, "y": 8.55}, {"time": 0.5333, "x": 0, "y": 15.75}, {"time": 0.6333, "x": 0, "y": 8.55}, {"time": 0.7333, "x": 0, "y": 14.4}, {"time": 0.8333, "x": 0, "y": 8.55}, {"time": 0.9333, "x": 0, "y": 14.4}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.3, "x": 0, "y": 15.75}, {"time": 1.4, "x": 0, "y": 8.55}, {"time": 1.5, "x": 0, "y": 14.4}, {"time": 1.6, "x": 0, "y": 8.55}, {"time": 1.7, "x": 0, "y": 15.75}, {"time": 1.8, "x": 0, "y": 8.55}, {"time": 1.9, "x": 0, "y": 14.4}, {"time": 2, "x": 0, "y": 8.55}, {"time": 2.1, "x": 0, "y": 14.4}, {"time": 2.3333, "x": 0, "y": 0}], "scale": [{"time": 0.2333, "x": 1, "y": 1}, {"time": 0.3333, "x": 0.858, "y": 1}, {"time": 0.4333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6333, "x": 1, "y": 1}, {"time": 0.7333, "x": 0.858, "y": 1}, {"time": 0.8333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}, {"time": 1.5, "x": 0.858, "y": 1}, {"time": 1.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8, "x": 1, "y": 1}, {"time": 1.9, "x": 0.858, "y": 1}, {"time": 2, "x": 1, "y": 1}]}, "bone2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1333, "angle": -4.13}, {"time": 0.3333, "angle": 5.31}, {"time": 0.4333, "angle": 0.29}, {"time": 0.5333, "angle": -4.13}, {"time": 0.7333, "angle": 4.12}, {"time": 0.9333, "angle": -1.46}, {"time": 1.1667, "angle": 0}, {"time": 1.3, "angle": -4.13}, {"time": 1.5, "angle": 5.31}, {"time": 1.6, "angle": 0.29}, {"time": 1.7, "angle": -4.13}, {"time": 1.9, "angle": 4.12}, {"time": 2.1, "angle": -1.46}, {"time": 2.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1333, "x": 0, "y": 15.35}, {"time": 0.2333, "x": 0, "y": 7.3}, {"time": 0.3333, "x": 0, "y": 18.41}, {"time": 0.4333, "x": 0, "y": 7.64}, {"time": 0.5333, "x": 0, "y": 15.35}, {"time": 0.6333, "x": 0, "y": 7.3}, {"time": 0.7333, "x": 0, "y": 18.41}, {"time": 0.8333, "x": 0, "y": 7.64}, {"time": 0.9333, "x": 0, "y": 13.17}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.3, "x": 0, "y": 15.35}, {"time": 1.4, "x": 0, "y": 7.3}, {"time": 1.5, "x": 0, "y": 18.41}, {"time": 1.6, "x": 0, "y": 7.64}, {"time": 1.7, "x": 0, "y": 15.35}, {"time": 1.8, "x": 0, "y": 7.3}, {"time": 1.9, "x": 0, "y": 18.41}, {"time": 2, "x": 0, "y": 7.64}, {"time": 2.1, "x": 0, "y": 13.17}, {"time": 2.3333, "x": 0, "y": 0}], "scale": [{"time": 0.1333, "x": 1, "y": 1}, {"time": 0.3333, "x": 1, "y": 0.939}, {"time": 0.4333, "x": 1, "y": 0.953}, {"time": 0.5333, "x": 1, "y": 1}, {"time": 0.7333, "x": 1, "y": 0.939}, {"time": 0.9333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 0.939}, {"time": 1.6, "x": 1, "y": 0.953}, {"time": 1.7, "x": 1, "y": 1}, {"time": 1.9, "x": 1, "y": 0.939}, {"time": 2.1, "x": 1, "y": 1}]}, "bone4": {"scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.0667, "x": 0.94, "y": 0.94}, {"time": 0.1333, "x": 0.809, "y": 0.809}, {"time": 0.2333, "x": 0.894, "y": 0.894}, {"time": 0.3333, "x": 0.803, "y": 0.803}, {"time": 0.4333, "x": 0.91, "y": 0.91}, {"time": 0.5333, "x": 0.809, "y": 0.809}, {"time": 0.6333, "x": 0.894, "y": 0.894}, {"time": 0.7333, "x": 0.803, "y": 0.803}, {"time": 0.8333, "x": 0.91, "y": 0.91}, {"time": 0.9333, "x": 0.842, "y": 0.842}, {"time": 1.1667, "x": 1, "y": 1}, {"time": 1.2333, "x": 0.94, "y": 0.94}, {"time": 1.3, "x": 0.809, "y": 0.809}, {"time": 1.4, "x": 0.894, "y": 0.894}, {"time": 1.5, "x": 0.803, "y": 0.803}, {"time": 1.6, "x": 0.91, "y": 0.91}, {"time": 1.7, "x": 0.809, "y": 0.809}, {"time": 1.8, "x": 0.894, "y": 0.894}, {"time": 1.9, "x": 0.803, "y": 0.803}, {"time": 2, "x": 0.91, "y": 0.91}, {"time": 2.1, "x": 0.842, "y": 0.842}, {"time": 2.3333, "x": 1, "y": 1}]}}}, "mobat": {"slots": {"MoBat": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}]}}, "bones": {"bone2": {"translate": [{"time": 0.5, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 599.12, "curve": "stepped"}, {"time": 5.8333, "x": 0, "y": 599.12}, {"time": 6.1667, "x": 0, "y": 0}]}, "bone13": {"scale": [{"time": 0, "x": 0.256, "y": 0.256}, {"time": 0.5, "x": 1.229, "y": 1.229}, {"time": 0.8333, "x": 1.509, "y": 1.509}, {"time": 1.3333, "x": 1.691, "y": 1.691}]}, "bone4": {"scale": [{"time": 6.6667, "x": 1, "y": 1}]}}}}}