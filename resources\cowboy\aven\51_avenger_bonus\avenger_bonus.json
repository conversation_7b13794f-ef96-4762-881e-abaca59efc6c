{"skins": {"default": {"B": {"Layer 22": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 1, 83.56, -105.56, 1, 1, 1, -92.44, -105.56, 1, 1, 1, -92.44, 85.44, 1, 1, 1, 83.56, 85.44, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 176, "type": "mesh", "hull": 4, "height": 191}}, "Layer 14 copy": {"Layer 14 copy": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 10, -3.5, -21.44, 1, 1, 10, -0.96, 23.49, 1, 1, 10, 59.95, 20.05, 1, 1, 10, 57.4, -24.88, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 45, "type": "mesh", "hull": 4, "height": 61}}, "N": {"Layer 24": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 4, 58.95, -105.56, 1, 1, 4, -61.05, -105.56, 1, 1, 4, -61.05, 85.44, 1, 1, 4, 58.95, 85.44, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 120, "type": "mesh", "hull": 4, "height": 191}}, "O": {"Layer 23": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 5, 64.83, -105.56, 1, 1, 5, -73.17, -105.56, 1, 1, 5, -73.17, 85.44, 1, 1, 5, 64.83, 85.44, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 138, "type": "mesh", "hull": 4, "height": 191}}, "S": {"Layer 26": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 6, 79.63, -105.56, 1, 1, 6, -78.37, -105.56, 1, 1, 6, -78.37, 85.44, 1, 1, 6, 79.63, 85.44, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 158, "type": "mesh", "hull": 4, "height": 191}}, "U": {"Layer 25": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 7, 76.08, -121.56, 1, 1, 7, -64.92, -121.56, 1, 1, 7, -64.92, 85.44, 1, 1, 7, 76.08, 85.44, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 141, "type": "mesh", "hull": 4, "height": 207}}, "Layer 20": {"Layer 20": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 8, -88.88, -124.49, 1, 1, 8, -36.39, 145.45, 1, 1, 8, 219.81, 95.64, 1, 1, 8, 167.32, -174.31, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 275, "type": "mesh", "hull": 4, "height": 261}}, "Layer 14 copy 2": {"Layer 14 copy 2": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 9, 12.11, -37.51, 1, 1, 9, 7.85, 21.34, 1, 1, 9, 86.64, 27.05, 1, 1, 9, 90.91, -31.8, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 59, "type": "mesh", "hull": 4, "height": 79}}, "Layer 14": {"Layer 14": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 12, -47.52, -27.52, 1, 1, 12, -34.11, 45.25, 1, 1, 12, 52.43, 29.31, 1, 1, 12, 39.02, -43.47, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 74, "type": "mesh", "hull": 4, "height": 88}}, "Layer 14 copy 4": {"Layer 14 copy 4": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 11, -37.7, -32.29, 1, 1, 11, -27.23, 43.99, 1, 1, 11, 67.88, 30.94, 1, 1, 11, 57.41, -45.35, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "type": "mesh", "hull": 4, "height": 96}}, "Layer 16 copy 3": {"Layer 16 copy 3": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 15, -39.07, -28.16, 1, 1, 15, -26.26, 40.66, 1, 1, 15, 60.25, 24.57, 1, 1, 15, 47.45, -44.25, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 70, "type": "mesh", "hull": 4, "height": 88}}, "Layer 16 copy 2": {"Layer 16 copy 2": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 14, -22.76, -25.96, 1, 1, 14, -5.54, 42.92, 1, 1, 14, 78.86, 21.82, 1, 1, 14, 61.64, -47.06, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 71, "type": "mesh", "hull": 4, "height": 87}}, "Layer 18": {"Layer 18": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 9, -54.71, -42.35, 1, 1, 9, -58.9, 15.5, 1, 1, 9, 47.82, 23.23, 1, 1, 9, 52.01, -34.62, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 58, "type": "mesh", "hull": 4, "height": 107}}, "LIGHT": {"LIGHT": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "color": "6b0a86ff", "vertices": [1, 24, -64.54, -44.62, 1, 1, 24, -43.92, 61.4, 1, 1, 24, 62.09, 40.78, 1, 1, 24, 41.48, -65.23, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 90, "type": "mesh", "hull": 4, "height": 90}}, "Color Balance 8 copy 2": {"Color Balance 8 copy 2": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 2, -93.23, -125.87, 1, 1, 2, -29.48, 146.12, 1, 1, 2, 248.12, 81.06, 1, 1, 2, 184.37, -190.93, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 291, "type": "mesh", "hull": 4, "height": 297}}, "EFFECT": {"EFFECT-CROP-3_0011_EFFECT_00011": {"width": 224, "height": 246}, "EFFECT-CROP-3_0012_EFFECT_00010": {"width": 224, "height": 246}, "EFFECT-CROP-3_0010_EFFECT_00012": {"width": 224, "height": 246}, "EFFECT-CROP-3_0004_EFFECT_00018": {"width": 224, "height": 246}, "EFFECT-CROP-3_0005_EFFECT_00017": {"width": 224, "height": 246}, "EFFECT-CROP-3_0008_EFFECT_00014": {"width": 224, "height": 246}, "EFFECT-CROP-3_0021_EFFECT_00001": {"width": 224, "height": 246}, "EFFECT-CROP-3_0002_EFFECT_00020": {"width": 224, "height": 246}, "EFFECT-CROP-3_0009_EFFECT_00013": {"width": 224, "height": 246}, "EFFECT-CROP-3_0020_EFFECT_00002": {"width": 224, "height": 246}, "EFFECT-CROP-3_0001_EFFECT_00021": {"width": 224, "height": 246}, "EFFECT-CROP-3_0013_EFFECT_00009": {"width": 224, "height": 246}, "EFFECT-CROP-3_0019_EFFECT_00003": {"width": 224, "height": 246}, "EFFECT-CROP-3_0003_EFFECT_00019": {"width": 224, "height": 246}, "EFFECT-CROP-3_0000_EFFECT_00022": {"width": 1, "height": 1}, "EFFECT-CROP-3_0017_EFFECT_00005": {"width": 224, "height": 246}, "EFFECT-CROP-3_0018_EFFECT_00004": {"width": 224, "height": 246}, "EFFECT-CROP-3_0014_EFFECT_00008": {"width": 224, "height": 246}, "EFFECT-CROP-3_0022_EFFECT_00000": {"width": 224, "height": 246}, "EFFECT-CROP-3_0015_EFFECT_00007": {"width": 224, "height": 246}, "EFFECT-CROP-3_0016_EFFECT_00006": {"width": 224, "height": 246}, "EFFECT-CROP-3_0006_EFFECT_00016": {"width": 224, "height": 246}, "EFFECT-CROP-3_0007_EFFECT_00015": {"width": 224, "height": 246}}, "LIGHT4": {"LIGHT": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "color": "ff7700ff", "vertices": [1, 27, -64.78, -46.26, 1, 1, 27, -44.17, 59.76, 1, 1, 27, 61.85, 39.14, 1, 1, 27, 41.23, -66.87, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 90, "type": "mesh", "hull": 4, "height": 90}}, "LIGHT5": {"LIGHT": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "color": "54ff00ff", "vertices": [1, 23, -63.71, -36.55, 1, 1, 23, -43.1, 69.46, 1, 1, 23, 62.91, 48.85, 1, 1, 23, 42.3, -57.16, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 90, "type": "mesh", "hull": 4, "height": 90}}, "LIGHT2": {"LIGHT": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "color": "007da8ff", "vertices": [1, 25, -62.64, -44.68, 1, 1, 25, -42.03, 61.33, 1, 1, 25, 63.98, 40.72, 1, 1, 25, 43.37, -65.3, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 90, "type": "mesh", "hull": 4, "height": 90}}, "Layer 17 copy 2": {"Layer 17 copy 2": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 19, -31.66, -26.78, 1, 1, 19, -29.17, 24.16, 1, 1, 19, 53.73, 20.12, 1, 1, 19, 51.25, -30.82, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 51, "type": "mesh", "hull": 4, "height": 83}}, "LIGHT3": {"LIGHT": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "color": "f80000ff", "vertices": [1, 26, -65.38, -41.91, 1, 1, 26, -44.76, 64.11, 1, 1, 26, 61.25, 43.5, 1, 1, 26, 40.64, -62.52, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 90, "type": "mesh", "hull": 4, "height": 90}}, "Layer 17 copy 3": {"Layer 17 copy 3": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 17, -25, -18.14, 1, 1, 17, -5.08, 41.63, 1, 1, 17, 77.45, 14.11, 1, 1, 17, 57.53, -45.65, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 63, "type": "mesh", "hull": 4, "height": 87}}, "Layer 17 copy 4": {"Layer 17 copy 4": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 18, -39.63, -18.2, 1, 1, 18, -22.1, 39.18, 1, 1, 18, 60.15, 14.05, 1, 1, 18, 42.62, -43.33, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 60, "type": "mesh", "hull": 4, "height": 86}}, "Layer 14 copy 5": {"Layer 14 copy 5": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 13, -47.38, -22.1, 1, 1, 13, -29.89, 45.68, 1, 1, 13, 47.58, 25.69, 1, 1, 13, 30.08, -42.09, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 70, "type": "mesh", "hull": 4, "height": 80}}, "Layer 17 copy 5": {"Layer 17 copy 5": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 22, -33.55, -22.29, 1, 1, 22, -27.13, 25.28, 1, 1, 22, 46.21, 15.37, 1, 1, 22, 39.78, -32.2, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "type": "mesh", "hull": 4, "height": 74}}, "Layer 16 copy": {"Layer 16 copy": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 16, -28.94, -25, 1, 1, 16, -19.3, 34.22, 1, 1, 16, 54.72, 22.17, 1, 1, 16, 45.08, -37.05, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 60, "type": "mesh", "hull": 4, "height": 75}}, "Layer 17 copy 6": {"Layer 17 copy 6": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 21, -35.68, -20.33, 1, 1, 21, -22.18, 35.04, 1, 1, 21, 58.46, 15.37, 1, 1, 21, 44.95, -40, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 57, "type": "mesh", "hull": 4, "height": 83}}, "Layer 17 copy 7": {"Layer 17 copy 7": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 20, -22.37, -15.02, 1, 1, 20, 1.09, 41.29, 1, 1, 20, 78.63, 8.98, 1, 1, 20, 55.17, -47.33, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 61, "type": "mesh", "hull": 4, "height": 84}}, "LIGHT 1": {"LIGHT": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 28, -92.32, -66.39, 1, 1, 28, -61.4, 92.63, 1, 1, 28, 97.62, 61.71, 1, 1, 28, 66.7, -97.31, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 90, "type": "mesh", "hull": 4, "height": 90}}}}, "skeleton": {"images": "../images/", "width": 658.03, "spine": "3.6.53", "hash": "UrMaY9eLewmvvDzDA/7CBj1c708", "height": 718.45}, "slots": [{"attachment": "Color Balance 8 copy 2", "name": "Color Balance 8 copy 2", "bone": "root"}, {"attachment": "Layer 14 copy", "name": "Layer 14 copy", "bone": "root"}, {"attachment": "Layer 14 copy 2", "name": "Layer 14 copy 2", "bone": "root"}, {"attachment": "Layer 17 copy 5", "name": "Layer 17 copy 5", "bone": "root"}, {"attachment": "Layer 17 copy 6", "name": "Layer 17 copy 6", "bone": "root"}, {"attachment": "Layer 17 copy 7", "name": "Layer 17 copy 7", "bone": "root"}, {"attachment": "Layer 17 copy 2", "name": "Layer 17 copy 2", "bone": "root"}, {"attachment": "Layer 17 copy 4", "name": "Layer 17 copy 4", "bone": "root"}, {"attachment": "Layer 17 copy 3", "name": "Layer 17 copy 3", "bone": "root"}, {"attachment": "Layer 16 copy", "name": "Layer 16 copy", "bone": "root"}, {"attachment": "Layer 16 copy 3", "name": "Layer 16 copy 3", "bone": "root"}, {"attachment": "Layer 16 copy 2", "name": "Layer 16 copy 2", "bone": "root"}, {"attachment": "Layer 14 copy 5", "name": "Layer 14 copy 5", "bone": "root"}, {"attachment": "Layer 14", "name": "Layer 14", "bone": "root"}, {"attachment": "Layer 14 copy 4", "name": "Layer 14 copy 4", "bone": "root"}, {"attachment": "Layer 20", "name": "Layer 20", "bone": "root"}, {"attachment": "Layer 18", "name": "Layer 18", "bone": "root"}, {"attachment": "EFFECT-CROP-3_0022_EFFECT_00000", "blend": "additive", "name": "EFFECT", "bone": "EFFECT"}, {"attachment": "Layer 26", "name": "S", "bone": "root"}, {"attachment": "Layer 25", "name": "U", "bone": "root"}, {"attachment": "Layer 22", "name": "B", "bone": "root"}, {"attachment": "Layer 23", "name": "O", "bone": "root"}, {"attachment": "Layer 24", "name": "N", "bone": "root"}, {"color": "ffcf00ff", "attachment": "LIGHT", "blend": "additive", "name": "LIGHT 1", "bone": "root"}, {"attachment": "LIGHT", "blend": "additive", "name": "LIGHT", "bone": "root"}, {"attachment": "LIGHT", "blend": "additive", "name": "LIGHT2", "bone": "root"}, {"attachment": "LIGHT", "blend": "additive", "name": "LIGHT3", "bone": "root"}, {"attachment": "LIGHT", "blend": "additive", "name": "LIGHT4", "bone": "root"}, {"attachment": "LIGHT", "blend": "additive", "name": "LIGHT5", "bone": "root"}], "bones": [{"name": "root"}, {"parent": "root", "name": "B", "x": -224.09, "y": -84.95}, {"parent": "root", "rotation": 103.19, "name": "BONE INFINITY GLOVE", "length": 170.65, "x": 6.25, "y": 1.56}, {"scaleX": 2.921, "parent": "BONE INFINITY GLOVE", "scaleY": 2.921, "rotation": -103.19, "name": "EFFECT", "x": 134.26, "y": -11.22}, {"parent": "root", "name": "N", "x": 10.52, "y": -84.95}, {"parent": "root", "name": "O", "x": -104.36, "y": -84.95}, {"parent": "root", "name": "S", "x": 237.85, "y": -84.95}, {"parent": "root", "name": "U", "x": 125.39, "y": -84.95}, {"parent": "BONE INFINITY GLOVE", "rotation": -2.19, "name": "bone2", "length": 142.81, "x": 170.65}, {"parent": "bone2", "rotation": -15.15, "name": "bone3", "length": 64.82, "x": 64.05, "y": -137.64}, {"parent": "bone3", "rotation": 7.38, "name": "bone4", "length": 49.74, "x": 64.82}, {"parent": "bone2", "rotation": -3.19, "name": "bone5", "length": 48.23, "x": 161.49, "y": -78.31}, {"parent": "bone5", "rotation": 2.62, "name": "bone6", "length": 36.21, "x": 48.23}, {"parent": "bone6", "rotation": 4.03, "name": "bone7", "length": 30, "x": 36.21}, {"parent": "bone2", "rotation": 3.03, "name": "bone8", "length": 54.09, "x": 147.34, "y": -8.74}, {"parent": "bone8", "rotation": -3.5, "name": "bone9", "length": 40.98, "x": 54.09}, {"parent": "bone9", "rotation": -1.29, "name": "bone10", "length": 40.82, "x": 40.98}, {"parent": "bone2", "rotation": 7.43, "name": "bone11", "length": 53.33, "x": 140.06, "y": 47.08}, {"parent": "bone11", "rotation": -1.44, "name": "bone12", "length": 35.27, "x": 53.33}, {"parent": "bone12", "rotation": -14.2, "name": "bone13", "length": 38.46, "x": 35.27}, {"parent": "bone2", "rotation": 11.62, "name": "bone14", "length": 48.72, "x": 119.98, "y": 91.08}, {"parent": "bone14", "rotation": -8.91, "name": "bone15", "length": 39.54, "x": 48.72}, {"parent": "bone15", "rotation": -6.01, "name": "bone16", "length": 34.98, "x": 39.54}, {"parent": "bone2", "name": "bone17", "x": 80.69, "y": -170.46}, {"parent": "bone2", "name": "bone18", "x": 141.07, "y": -75.91}, {"parent": "bone2", "name": "bone19", "x": 138.22, "y": -16.31}, {"parent": "bone2", "name": "bone20", "x": 129.05, "y": 44.51}, {"parent": "bone2", "name": "bone21", "x": 120.13, "y": 92.33}, {"parent": "bone2", "name": "bone22", "x": 56.95, "y": 16.32}], "animations": {"avenger_bonus": {"slots": {"B": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffff00", "time": 1}, {"color": "ffffffff", "curve": "stepped", "time": 1.1}, {"color": "ffffffff", "curve": "stepped", "time": 2.5}, {"color": "ffffffff", "time": 2.9}, {"color": "ffffff00", "time": 3.0667}]}, "LIGHT 1": {"color": [{"color": "ffcf0000", "curve": "stepped", "time": 0}, {"color": "ffcf0000", "curve": "stepped", "time": 0.3333}, {"color": "ffcf0000", "time": 0.9}, {"color": "ffcf00ff", "curve": "stepped", "time": 1.1667}, {"color": "ffcf00ff", "time": 1.6667}, {"color": "ffcf0000", "time": 2}]}, "S": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffff00", "time": 1}, {"color": "ffffffff", "curve": "stepped", "time": 1.1}, {"color": "ffffffff", "time": 2.5}, {"color": "ffffff00", "time": 2.6667}]}, "U": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffff00", "time": 1}, {"color": "ffffffff", "curve": "stepped", "time": 1.1}, {"color": "ffffffff", "curve": "stepped", "time": 2.5}, {"color": "ffffffff", "time": 2.6}, {"color": "ffffff00", "time": 2.7667}]}, "LIGHT": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": "stepped", "time": 0.3333}, {"color": "ffffff00", "time": 0.4667}, {"color": "ffffffff", "curve": "stepped", "time": 0.6667}, {"color": "ffffffff", "time": 1.6667}, {"color": "ffffff00", "time": 2}]}, "EFFECT": {"attachment": [{"name": "EFFECT-CROP-3_0022_EFFECT_00000", "time": 1}, {"name": "EFFECT-CROP-3_0021_EFFECT_00001", "time": 1.0333}, {"name": "EFFECT-CROP-3_0020_EFFECT_00002", "time": 1.0667}, {"name": "EFFECT-CROP-3_0019_EFFECT_00003", "time": 1.1}, {"name": "EFFECT-CROP-3_0018_EFFECT_00004", "time": 1.1333}, {"name": "EFFECT-CROP-3_0017_EFFECT_00005", "time": 1.1667}, {"name": "EFFECT-CROP-3_0016_EFFECT_00006", "time": 1.2}, {"name": "EFFECT-CROP-3_0015_EFFECT_00007", "time": 1.2333}, {"name": "EFFECT-CROP-3_0014_EFFECT_00008", "time": 1.2667}, {"name": "EFFECT-CROP-3_0013_EFFECT_00009", "time": 1.3}, {"name": "EFFECT-CROP-3_0012_EFFECT_00010", "time": 1.3333}, {"name": "EFFECT-CROP-3_0011_EFFECT_00011", "time": 1.3667}, {"name": "EFFECT-CROP-3_0010_EFFECT_00012", "time": 1.4}, {"name": "EFFECT-CROP-3_0009_EFFECT_00013", "time": 1.4333}, {"name": "EFFECT-CROP-3_0008_EFFECT_00014", "time": 1.4667}, {"name": "EFFECT-CROP-3_0007_EFFECT_00015", "time": 1.5}, {"name": "EFFECT-CROP-3_0006_EFFECT_00016", "time": 1.5333}, {"name": "EFFECT-CROP-3_0005_EFFECT_00017", "time": 1.5667}, {"name": "EFFECT-CROP-3_0004_EFFECT_00018", "time": 1.6}, {"name": "EFFECT-CROP-3_0003_EFFECT_00019", "time": 1.6333}, {"name": "EFFECT-CROP-3_0002_EFFECT_00020", "time": 1.6667}, {"name": "EFFECT-CROP-3_0001_EFFECT_00021", "time": 1.7}, {"name": "EFFECT-CROP-3_0021_EFFECT_00001", "time": 1.7333}, {"name": "EFFECT-CROP-3_0020_EFFECT_00002", "time": 1.7667}, {"name": "EFFECT-CROP-3_0019_EFFECT_00003", "time": 1.8}, {"name": "EFFECT-CROP-3_0018_EFFECT_00004", "time": 1.8333}, {"name": "EFFECT-CROP-3_0017_EFFECT_00005", "time": 1.8667}, {"name": "EFFECT-CROP-3_0016_EFFECT_00006", "time": 1.9}, {"name": "EFFECT-CROP-3_0015_EFFECT_00007", "time": 1.9333}, {"name": "EFFECT-CROP-3_0014_EFFECT_00008", "time": 1.9667}, {"name": "EFFECT-CROP-3_0013_EFFECT_00009", "time": 2}, {"name": "EFFECT-CROP-3_0012_EFFECT_00010", "time": 2.0333}, {"name": "EFFECT-CROP-3_0011_EFFECT_00011", "time": 2.0667}, {"name": "EFFECT-CROP-3_0010_EFFECT_00012", "time": 2.1}, {"name": "EFFECT-CROP-3_0009_EFFECT_00013", "time": 2.1333}, {"name": "EFFECT-CROP-3_0008_EFFECT_00014", "time": 2.1667}, {"name": "EFFECT-CROP-3_0007_EFFECT_00015", "time": 2.2}, {"name": "EFFECT-CROP-3_0006_EFFECT_00016", "time": 2.2333}, {"name": "EFFECT-CROP-3_0005_EFFECT_00017", "time": 2.2667}, {"name": "EFFECT-CROP-3_0004_EFFECT_00018", "time": 2.3}, {"name": "EFFECT-CROP-3_0003_EFFECT_00019", "time": 2.3333}, {"name": "EFFECT-CROP-3_0002_EFFECT_00020", "time": 2.3667}, {"name": "EFFECT-CROP-3_0001_EFFECT_00021", "time": 2.4}, {"name": "EFFECT-CROP-3_0000_EFFECT_00022", "time": 2.4333}, {"name": "EFFECT-CROP-3_0021_EFFECT_00001", "time": 2.4667}, {"name": "EFFECT-CROP-3_0020_EFFECT_00002", "time": 2.5}, {"name": "EFFECT-CROP-3_0019_EFFECT_00003", "time": 2.5333}, {"name": "EFFECT-CROP-3_0018_EFFECT_00004", "time": 2.5667}, {"name": "EFFECT-CROP-3_0017_EFFECT_00005", "time": 2.6}, {"name": "EFFECT-CROP-3_0016_EFFECT_00006", "time": 2.6333}, {"name": "EFFECT-CROP-3_0015_EFFECT_00007", "time": 2.6667}, {"name": "EFFECT-CROP-3_0014_EFFECT_00008", "time": 2.7}, {"name": "EFFECT-CROP-3_0013_EFFECT_00009", "time": 2.7333}, {"name": "EFFECT-CROP-3_0012_EFFECT_00010", "time": 2.7667}, {"name": "EFFECT-CROP-3_0011_EFFECT_00011", "time": 2.8}, {"name": "EFFECT-CROP-3_0010_EFFECT_00012", "time": 2.8333}, {"name": "EFFECT-CROP-3_0009_EFFECT_00013", "time": 2.8667}, {"name": "EFFECT-CROP-3_0008_EFFECT_00014", "time": 2.9}, {"name": "EFFECT-CROP-3_0007_EFFECT_00015", "time": 2.9333}, {"name": "EFFECT-CROP-3_0006_EFFECT_00016", "time": 2.9667}, {"name": "EFFECT-CROP-3_0005_EFFECT_00017", "time": 3}, {"name": "EFFECT-CROP-3_0004_EFFECT_00018", "time": 3.0333}, {"name": "EFFECT-CROP-3_0003_EFFECT_00019", "time": 3.0667}, {"name": "EFFECT-CROP-3_0002_EFFECT_00020", "time": 3.1}, {"name": "EFFECT-CROP-3_0001_EFFECT_00021", "time": 3.1333}, {"name": "EFFECT-CROP-3_0000_EFFECT_00022", "time": 3.1667}]}, "LIGHT4": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": "stepped", "time": 0.3333}, {"color": "ffffff00", "time": 0.7667}, {"color": "ffffff80", "curve": "stepped", "time": 1.0333}, {"color": "ffffff80", "time": 1.6667}, {"color": "ffffff00", "time": 2}]}, "LIGHT5": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffff80", "time": 0.5333}, {"color": "ffffff80", "time": 1.6667}, {"color": "ffffff00", "time": 2}]}, "LIGHT2": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": "stepped", "time": 0.3333}, {"color": "ffffff00", "time": 0.5333}, {"color": "ffffffff", "curve": "stepped", "time": 0.7667}, {"color": "ffffffff", "time": 1.6667}, {"color": "ffffff00", "time": 2}]}, "LIGHT3": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": "stepped", "time": 0.3333}, {"color": "ffffff00", "time": 0.6667}, {"color": "ffffffff", "curve": "stepped", "time": 0.9}, {"color": "ffffffff", "time": 1.6667}, {"color": "ffffff00", "time": 2}]}, "N": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffff00", "time": 1}, {"color": "ffffffff", "curve": "stepped", "time": 1.1}, {"color": "ffffffff", "curve": "stepped", "time": 2.5}, {"color": "ffffffff", "time": 2.7}, {"color": "ffffff00", "time": 2.8667}]}, "O": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffff00", "time": 1}, {"color": "ffffffff", "curve": "stepped", "time": 1.1}, {"color": "ffffffff", "curve": "stepped", "time": 2.5}, {"color": "ffffffff", "time": 2.8}, {"color": "ffffff00", "time": 2.9667}]}}, "bones": {"bone15": {"rotate": [{"angle": -1.29, "time": 0.8333}], "translate": [{"curve": "stepped", "x": -21.37, "y": -5.27, "time": 0}, {"x": -21.37, "y": -5.27, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -21.37, "y": -5.27, "time": 1}, {"x": -21.37, "y": -5.27, "time": 2.4333}]}, "BONE INFINITY GLOVE": {"scale": [{"x": 0, "y": 0, "time": 0}, {"x": 1, "y": 1, "time": 0.1}, {"x": 1.08, "y": 1.08, "time": 0.1667}, {"x": 0.98, "y": 0.98, "time": 0.2333}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.3}, {"x": 1, "y": 1, "time": 2.9333}, {"x": 1.1, "y": 1.1, "time": 3.0333}, {"x": 0, "y": 0, "time": 3.1667}], "translate": [{"curve": "stepped", "x": 0, "y": -38.43, "time": 0}, {"x": 0, "y": -38.43, "time": 0.3333}, {"x": 0, "y": 54.53, "time": 0.8333}, {"x": 0, "y": -103.91, "time": 1}, {"x": 0, "y": -115.28, "time": 1.1667}, {"x": 0, "y": -103.91, "time": 1.6667}, {"x": 0, "y": -38.43, "time": 3.1667}]}, "B": {"scale": [{"x": 1, "y": 1, "time": 1.3333}, {"x": 1.4, "y": 1.4, "time": 1.4333}, {"x": 0.8, "y": 0.8, "time": 1.5}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.5667}, {"x": 1, "y": 1, "time": 2.5}], "translate": [{"curve": "stepped", "x": -450.74, "y": 0, "time": 0}, {"curve": "stepped", "x": -450.74, "y": 0, "time": 0.3333}, {"x": -450.74, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 1.1}, {"x": 17.55, "y": 0, "time": 1.1667}, {"x": -13.07, "y": 0, "time": 1.2333}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.3}, {"curve": "stepped", "x": 0, "y": 0, "time": 2.5}, {"x": 0, "y": 0, "time": 2.9}, {"x": 591.25, "y": 0, "time": 3.0667}]}, "bone16": {"rotate": [{"angle": -1.44, "time": 0.8333}], "translate": [{"curve": "stepped", "x": -24.97, "y": -3.93, "time": 0}, {"x": -24.97, "y": -3.93, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -24.97, "y": -3.93, "time": 1}, {"x": -24.97, "y": -3.93, "time": 2.4333}]}, "bone5": {"rotate": [{"angle": 0, "time": 0.6667}], "translate": [{"curve": "stepped", "x": -11.82, "y": 2.3, "time": 0}, {"x": -11.82, "y": 2.3, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -11.82, "y": 2.3, "time": 1}, {"x": -11.82, "y": 2.3, "time": 2.4333}]}, "bone10": {"rotate": [{"angle": 0, "time": 0.6667}], "translate": [{"curve": "stepped", "x": -35.95, "y": -0.85, "time": 0}, {"x": -35.95, "y": -0.85, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -35.95, "y": -0.85, "time": 1}, {"x": -35.95, "y": -0.85, "time": 2.4333}]}, "bone4": {"rotate": [{"curve": "stepped", "angle": 37.03, "time": 0}, {"angle": 37.03, "time": 0.3333}, {"angle": 0, "time": 0.6667}, {"angle": -9.94, "time": 0.8333}, {"curve": "stepped", "angle": 37.03, "time": 1}, {"angle": 37.03, "time": 2.4333}]}, "bone7": {"rotate": [{"angle": 0, "time": 0.6667}], "translate": [{"curve": "stepped", "x": -22.12, "y": -2.52, "time": 0}, {"x": -22.12, "y": -2.52, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -22.12, "y": -2.52, "time": 1}, {"x": -22.12, "y": -2.52, "time": 2.4333}]}, "bone6": {"rotate": [{"angle": 0, "time": 0.6667}], "translate": [{"curve": "stepped", "x": -28.15, "y": -0.81, "time": 0}, {"x": -28.15, "y": -0.81, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -28.15, "y": -0.81, "time": 1}, {"x": -28.15, "y": -0.81, "time": 2.4333}]}, "bone13": {"rotate": [{"angle": 0, "time": 0.6667}, {"angle": 3.71, "time": 0.8333}], "translate": [{"curve": "stepped", "x": -34.93, "y": 2.93, "time": 0}, {"x": -34.93, "y": 2.93, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -34.93, "y": 2.93, "time": 1}, {"x": -34.93, "y": 2.93, "time": 2.4333}]}, "bone9": {"rotate": [{"angle": 0, "time": 0.6667}], "translate": [{"curve": "stepped", "x": -25.38, "y": -2.25, "time": 0}, {"x": -25.38, "y": -2.25, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -25.38, "y": -2.25, "time": 1}, {"x": -25.38, "y": -2.25, "time": 2.4333}]}, "bone14": {"rotate": [{"curve": "stepped", "angle": -14.23, "time": 0}, {"angle": -14.23, "time": 0.3333}, {"angle": 0, "time": 0.6667}, {"angle": 7.12, "time": 0.8333}, {"curve": "stepped", "angle": -14.23, "time": 1}, {"angle": -14.23, "time": 2.4333}], "translate": [{"curve": "stepped", "x": -5.08, "y": -1.84, "time": 0}, {"x": -5.08, "y": -1.84, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -5.08, "y": -1.84, "time": 1}, {"x": -5.08, "y": -1.84, "time": 2.4333}]}, "bone8": {"rotate": [{"curve": "stepped", "angle": -2.5, "time": 0}, {"angle": -2.5, "time": 0.3333}, {"curve": "stepped", "angle": 0, "time": 0.6667}, {"angle": 0, "time": 0.8333}, {"curve": "stepped", "angle": -2.5, "time": 1}, {"angle": -2.5, "time": 2.4333}], "translate": [{"curve": "stepped", "x": -13.43, "y": -1.16, "time": 0}, {"x": -13.43, "y": -1.16, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -13.43, "y": -1.16, "time": 1}, {"x": -13.43, "y": -1.16, "time": 2.4333}]}, "N": {"scale": [{"x": 1, "y": 1, "time": 1.5333}, {"x": 1.4, "y": 1.4, "time": 1.6333}, {"x": 0.8, "y": 0.8, "time": 1.7}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.7667}, {"x": 1, "y": 1, "time": 2.5}], "translate": [{"curve": "stepped", "x": 548.41, "y": 0, "time": 0}, {"curve": "stepped", "x": 548.41, "y": 0, "time": 0.3333}, {"x": 548.41, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 1.1}, {"x": -10.9, "y": 0, "time": 1.1667}, {"x": 14.93, "y": 0, "time": 1.2333}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.3}, {"curve": "stepped", "x": 0, "y": 0, "time": 2.5}, {"x": 0, "y": 0, "time": 2.7}, {"x": 591.25, "y": 0, "time": 2.8667}]}, "bone11": {"rotate": [{"curve": "stepped", "angle": -1.84, "time": 0}, {"angle": -1.84, "time": 0.3333}, {"angle": 0, "time": 0.6667}, {"angle": 4.12, "time": 0.8333}, {"curve": "stepped", "angle": -1.84, "time": 1}, {"angle": -1.84, "time": 2.4333}], "translate": [{"curve": "stepped", "x": -8.33, "y": -4.04, "time": 0}, {"x": -8.33, "y": -4.04, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -8.33, "y": -4.04, "time": 1}, {"x": -8.33, "y": -4.04, "time": 2.4333}]}, "bone12": {"rotate": [{"angle": 0, "time": 0.6667}, {"angle": -3.75, "time": 0.8333}], "translate": [{"curve": "stepped", "x": -30.75, "y": -1.46, "time": 0}, {"x": -30.75, "y": -1.46, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -30.75, "y": -1.46, "time": 1}, {"x": -30.75, "y": -1.46, "time": 2.4333}]}, "O": {"scale": [{"x": 1, "y": 1, "time": 1.4333}, {"x": 1.4, "y": 1.4, "time": 1.5333}, {"x": 0.8, "y": 0.8, "time": 1.6}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.6667}, {"x": 1, "y": 1, "time": 2.5}], "translate": [{"curve": "stepped", "x": -450.74, "y": 0, "time": 0}, {"curve": "stepped", "x": -450.74, "y": 0, "time": 0.3333}, {"x": -450.74, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 1.1}, {"x": 17.55, "y": 0, "time": 1.1667}, {"x": -13.07, "y": 0, "time": 1.2333}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.3}, {"curve": "stepped", "x": 0, "y": 0, "time": 2.5}, {"x": 0, "y": 0, "time": 2.8}, {"x": 591.25, "y": 0, "time": 2.9667}]}, "S": {"scale": [{"x": 1, "y": 1, "time": 1.7333}, {"x": 1.4, "y": 1.4, "time": 1.8333}, {"x": 0.8, "y": 0.8, "time": 1.9}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.9667}, {"x": 1, "y": 1, "time": 2.5}], "translate": [{"curve": "stepped", "x": 548.41, "y": 0, "time": 0}, {"curve": "stepped", "x": 548.41, "y": 0, "time": 0.3333}, {"x": 548.41, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 1.1}, {"x": -10.9, "y": 0, "time": 1.1667}, {"x": 14.93, "y": 0, "time": 1.2333}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.3}, {"x": 0, "y": 0, "time": 2.5}, {"x": 591.25, "y": 0, "time": 2.6667}]}, "U": {"scale": [{"x": 1, "y": 1, "time": 1.6333}, {"x": 1.4, "y": 1.4, "time": 1.7333}, {"x": 0.8, "y": 0.8, "time": 1.8}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.8667}, {"x": 1, "y": 1, "time": 2.5}], "translate": [{"curve": "stepped", "x": 548.41, "y": 0, "time": 0}, {"curve": "stepped", "x": 548.41, "y": 0, "time": 0.3333}, {"x": 548.41, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 1.1}, {"x": -10.9, "y": 0, "time": 1.1667}, {"x": 14.93, "y": 0, "time": 1.2333}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.3}, {"curve": "stepped", "x": 0, "y": 0, "time": 2.5}, {"x": 0, "y": 0, "time": 2.6}, {"x": 591.25, "y": 0, "time": 2.7667}]}, "bone3": {"rotate": [{"curve": "stepped", "angle": 8.14, "time": 0}, {"angle": 8.14, "time": 0.3333}, {"angle": 0, "time": 0.6667}, {"angle": -7.67, "time": 0.8333}, {"curve": "stepped", "angle": 8.14, "time": 1}, {"angle": 8.14, "time": 2.4333}]}, "bone2": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 0.3333}, {"angle": 0, "time": 0.6667}, {"angle": -3.61, "time": 1.1667}, {"angle": 0, "time": 2.4333}], "translate": [{"curve": "stepped", "x": -4.53, "y": 1.06, "time": 0}, {"x": -4.53, "y": 1.06, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.6667}, {"x": -4.53, "y": 1.06, "time": 1}, {"x": -13.58, "y": 3.18, "time": 1.1667}, {"x": -4.53, "y": 1.06, "time": 2.4333}]}}}, "END": {"slots": {"B": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0}, {"color": "ffffffff", "time": 0.4}, {"color": "ffffff00", "time": 0.5667}]}, "LIGHT 1": {"color": [{"color": "ffcf0000", "curve": "stepped", "time": 0}, {"color": "ffcf0000", "time": 0.5667}, {"color": "ffcf00ff", "curve": "stepped", "time": 0.8333}, {"color": "ffcf00ff", "time": 1.3333}, {"color": "ffcf0000", "time": 1.6667}]}, "S": {"color": [{"color": "ffffffff", "time": 0}, {"color": "ffffff00", "time": 0.1667}]}, "U": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0}, {"color": "ffffffff", "time": 0.1}, {"color": "ffffff00", "time": 0.2667}]}, "LIGHT": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.1333}, {"color": "ffffffff", "curve": "stepped", "time": 0.3333}, {"color": "ffffffff", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}]}, "EFFECT": {"attachment": [{"name": "EFFECT-CROP-3_0022_EFFECT_00000", "time": 0.6667}, {"name": "EFFECT-CROP-3_0021_EFFECT_00001", "time": 0.7}, {"name": "EFFECT-CROP-3_0020_EFFECT_00002", "time": 0.7333}, {"name": "EFFECT-CROP-3_0019_EFFECT_00003", "time": 0.7667}, {"name": "EFFECT-CROP-3_0018_EFFECT_00004", "time": 0.8}, {"name": "EFFECT-CROP-3_0017_EFFECT_00005", "time": 0.8333}, {"name": "EFFECT-CROP-3_0016_EFFECT_00006", "time": 0.8667}, {"name": "EFFECT-CROP-3_0015_EFFECT_00007", "time": 0.9}, {"name": "EFFECT-CROP-3_0014_EFFECT_00008", "time": 0.9333}, {"name": "EFFECT-CROP-3_0013_EFFECT_00009", "time": 0.9667}, {"name": "EFFECT-CROP-3_0012_EFFECT_00010", "time": 1}, {"name": "EFFECT-CROP-3_0011_EFFECT_00011", "time": 1.0333}, {"name": "EFFECT-CROP-3_0010_EFFECT_00012", "time": 1.0667}, {"name": "EFFECT-CROP-3_0009_EFFECT_00013", "time": 1.1}, {"name": "EFFECT-CROP-3_0008_EFFECT_00014", "time": 1.1333}, {"name": "EFFECT-CROP-3_0007_EFFECT_00015", "time": 1.1667}, {"name": "EFFECT-CROP-3_0006_EFFECT_00016", "time": 1.2}, {"name": "EFFECT-CROP-3_0005_EFFECT_00017", "time": 1.2333}, {"name": "EFFECT-CROP-3_0004_EFFECT_00018", "time": 1.2667}, {"name": "EFFECT-CROP-3_0003_EFFECT_00019", "time": 1.3}, {"name": "EFFECT-CROP-3_0002_EFFECT_00020", "time": 1.3333}, {"name": "EFFECT-CROP-3_0001_EFFECT_00021", "time": 1.3667}, {"name": "EFFECT-CROP-3_0021_EFFECT_00001", "time": 1.4}, {"name": "EFFECT-CROP-3_0020_EFFECT_00002", "time": 1.4333}, {"name": "EFFECT-CROP-3_0019_EFFECT_00003", "time": 1.4667}, {"name": "EFFECT-CROP-3_0018_EFFECT_00004", "time": 1.5}, {"name": "EFFECT-CROP-3_0017_EFFECT_00005", "time": 1.5333}, {"name": "EFFECT-CROP-3_0016_EFFECT_00006", "time": 1.5667}, {"name": "EFFECT-CROP-3_0015_EFFECT_00007", "time": 1.6}, {"name": "EFFECT-CROP-3_0014_EFFECT_00008", "time": 1.6333}, {"name": "EFFECT-CROP-3_0013_EFFECT_00009", "time": 1.6667}, {"name": "EFFECT-CROP-3_0012_EFFECT_00010", "time": 1.7}, {"name": "EFFECT-CROP-3_0011_EFFECT_00011", "time": 1.7333}, {"name": "EFFECT-CROP-3_0010_EFFECT_00012", "time": 1.7667}, {"name": "EFFECT-CROP-3_0009_EFFECT_00013", "time": 1.8}, {"name": "EFFECT-CROP-3_0008_EFFECT_00014", "time": 1.8333}, {"name": "EFFECT-CROP-3_0007_EFFECT_00015", "time": 1.8667}, {"name": "EFFECT-CROP-3_0006_EFFECT_00016", "time": 1.9}, {"name": "EFFECT-CROP-3_0005_EFFECT_00017", "time": 1.9333}, {"name": "EFFECT-CROP-3_0004_EFFECT_00018", "time": 1.9667}, {"name": "EFFECT-CROP-3_0003_EFFECT_00019", "time": 2}, {"name": "EFFECT-CROP-3_0002_EFFECT_00020", "time": 2.0333}, {"name": "EFFECT-CROP-3_0001_EFFECT_00021", "time": 2.0667}, {"name": "EFFECT-CROP-3_0000_EFFECT_00022", "time": 2.1}]}, "LIGHT4": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.4333}, {"color": "ffffff80", "curve": "stepped", "time": 0.7}, {"color": "ffffff80", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}]}, "LIGHT5": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffff80", "time": 0.2}, {"color": "ffffff80", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}]}, "LIGHT2": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.2}, {"color": "ffffffff", "curve": "stepped", "time": 0.4333}, {"color": "ffffffff", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}]}, "LIGHT3": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffffff", "curve": "stepped", "time": 0.5667}, {"color": "ffffffff", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}]}, "N": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0}, {"color": "ffffffff", "time": 0.2}, {"color": "ffffff00", "time": 0.3667}]}, "O": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0}, {"color": "ffffffff", "time": 0.3}, {"color": "ffffff00", "time": 0.4667}]}}, "bones": {"BONE INFINITY GLOVE": {"scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 0.5}, {"x": 1.1, "y": 1.1, "time": 0.6}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.7333}, {"x": 0, "y": 0, "time": 2.1}], "translate": [{"x": 0, "y": -38.43, "time": 0}, {"x": 0, "y": 54.53, "time": 0.5}, {"x": 0, "y": -103.91, "time": 0.6667}, {"x": 0, "y": -115.28, "time": 0.8333}, {"x": 0, "y": -103.91, "time": 1.3333}, {"x": 0, "y": -38.43, "time": 2.1}]}, "B": {"scale": [{"x": 1, "y": 1, "time": 0}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 0.4}, {"x": 591.25, "y": 0, "time": 0.5667}]}, "bone15": {"rotate": [{"angle": -1.29, "time": 0.5}], "translate": [{"x": -21.37, "y": -5.27, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -21.37, "y": -5.27, "time": 0.6667}, {"x": -21.37, "y": -5.27, "time": 2.1}]}, "bone16": {"rotate": [{"angle": -1.44, "time": 0.5}], "translate": [{"x": -24.97, "y": -3.93, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -24.97, "y": -3.93, "time": 0.6667}, {"x": -24.97, "y": -3.93, "time": 2.1}]}, "bone5": {"rotate": [{"angle": 0, "time": 0.3333}], "translate": [{"x": -11.82, "y": 2.3, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -11.82, "y": 2.3, "time": 0.6667}, {"x": -11.82, "y": 2.3, "time": 2.1}]}, "bone10": {"rotate": [{"angle": 0, "time": 0.3333}], "translate": [{"x": -35.95, "y": -0.85, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -35.95, "y": -0.85, "time": 0.6667}, {"x": -35.95, "y": -0.85, "time": 2.1}]}, "bone4": {"rotate": [{"angle": 37.03, "time": 0}, {"angle": 0, "time": 0.3333}, {"angle": -9.94, "time": 0.5}, {"curve": "stepped", "angle": 37.03, "time": 0.6667}, {"angle": 37.03, "time": 2.1}]}, "bone7": {"rotate": [{"angle": 0, "time": 0.3333}], "translate": [{"x": -22.12, "y": -2.52, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -22.12, "y": -2.52, "time": 0.6667}, {"x": -22.12, "y": -2.52, "time": 2.1}]}, "bone6": {"rotate": [{"angle": 0, "time": 0.3333}], "translate": [{"x": -28.15, "y": -0.81, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -28.15, "y": -0.81, "time": 0.6667}, {"x": -28.15, "y": -0.81, "time": 2.1}]}, "bone9": {"rotate": [{"angle": 0, "time": 0.3333}], "translate": [{"x": -25.38, "y": -2.25, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -25.38, "y": -2.25, "time": 0.6667}, {"x": -25.38, "y": -2.25, "time": 2.1}]}, "bone13": {"rotate": [{"angle": 0, "time": 0.3333}, {"angle": 3.71, "time": 0.5}], "translate": [{"x": -34.93, "y": 2.93, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -34.93, "y": 2.93, "time": 0.6667}, {"x": -34.93, "y": 2.93, "time": 2.1}]}, "bone14": {"rotate": [{"angle": -14.23, "time": 0}, {"angle": 0, "time": 0.3333}, {"angle": 7.12, "time": 0.5}, {"curve": "stepped", "angle": -14.23, "time": 0.6667}, {"angle": -14.23, "time": 2.1}], "translate": [{"x": -5.08, "y": -1.84, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -5.08, "y": -1.84, "time": 0.6667}, {"x": -5.08, "y": -1.84, "time": 2.1}]}, "bone8": {"rotate": [{"angle": -2.5, "time": 0}, {"curve": "stepped", "angle": 0, "time": 0.3333}, {"angle": 0, "time": 0.5}, {"curve": "stepped", "angle": -2.5, "time": 0.6667}, {"angle": -2.5, "time": 2.1}], "translate": [{"x": -13.43, "y": -1.16, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -13.43, "y": -1.16, "time": 0.6667}, {"x": -13.43, "y": -1.16, "time": 2.1}]}, "bone11": {"rotate": [{"angle": -1.84, "time": 0}, {"angle": 0, "time": 0.3333}, {"angle": 4.12, "time": 0.5}, {"curve": "stepped", "angle": -1.84, "time": 0.6667}, {"angle": -1.84, "time": 2.1}], "translate": [{"x": -8.33, "y": -4.04, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -8.33, "y": -4.04, "time": 0.6667}, {"x": -8.33, "y": -4.04, "time": 2.1}]}, "N": {"scale": [{"x": 1, "y": 1, "time": 0}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 0.2}, {"x": 591.25, "y": 0, "time": 0.3667}]}, "bone12": {"rotate": [{"angle": 0, "time": 0.3333}, {"angle": -3.75, "time": 0.5}], "translate": [{"x": -30.75, "y": -1.46, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -30.75, "y": -1.46, "time": 0.6667}, {"x": -30.75, "y": -1.46, "time": 2.1}]}, "O": {"scale": [{"x": 1, "y": 1, "time": 0}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 0.3}, {"x": 591.25, "y": 0, "time": 0.4667}]}, "S": {"scale": [{"x": 1, "y": 1, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 591.25, "y": 0, "time": 0.1667}]}, "U": {"scale": [{"x": 1, "y": 1, "time": 0}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 0.1}, {"x": 591.25, "y": 0, "time": 0.2667}]}, "bone3": {"rotate": [{"angle": 8.14, "time": 0}, {"angle": 0, "time": 0.3333}, {"angle": -7.67, "time": 0.5}, {"curve": "stepped", "angle": 8.14, "time": 0.6667}, {"angle": 8.14, "time": 2.1}]}, "bone2": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 0.3333}, {"angle": -3.61, "time": 0.8333}, {"angle": 0, "time": 2.1}], "translate": [{"x": -4.53, "y": 1.06, "time": 0}, {"x": 0, "y": 0, "time": 0.3333}, {"x": -4.53, "y": 1.06, "time": 0.6667}, {"x": -13.58, "y": 3.18, "time": 0.8333}, {"x": -4.53, "y": 1.06, "time": 2.1}]}}}, "RUN": {"slots": {"B": {"color": [{"color": "ffffffff", "time": 0}]}, "LIGHT 1": {"color": [{"color": "ffcf0000", "curve": "stepped", "time": 0}, {"color": "ffcf0000", "time": 0.5667}, {"color": "ffcf00ff", "curve": "stepped", "time": 0.8333}, {"color": "ffcf00ff", "time": 1.3333}, {"color": "ffcf0000", "time": 1.6667}]}, "S": {"color": [{"color": "ffffffff", "time": 0}]}, "U": {"color": [{"color": "ffffffff", "time": 0}]}, "LIGHT": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.1333}, {"color": "ffffffff", "curve": "stepped", "time": 0.3333}, {"color": "ffffffff", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}]}, "EFFECT": {"attachment": [{"name": "EFFECT-CROP-3_0022_EFFECT_00000", "time": 0.6667}, {"name": "EFFECT-CROP-3_0021_EFFECT_00001", "time": 0.7}, {"name": "EFFECT-CROP-3_0020_EFFECT_00002", "time": 0.7333}, {"name": "EFFECT-CROP-3_0019_EFFECT_00003", "time": 0.7667}, {"name": "EFFECT-CROP-3_0018_EFFECT_00004", "time": 0.8}, {"name": "EFFECT-CROP-3_0017_EFFECT_00005", "time": 0.8333}, {"name": "EFFECT-CROP-3_0016_EFFECT_00006", "time": 0.8667}, {"name": "EFFECT-CROP-3_0015_EFFECT_00007", "time": 0.9}, {"name": "EFFECT-CROP-3_0014_EFFECT_00008", "time": 0.9333}, {"name": "EFFECT-CROP-3_0013_EFFECT_00009", "time": 0.9667}, {"name": "EFFECT-CROP-3_0012_EFFECT_00010", "time": 1}, {"name": "EFFECT-CROP-3_0011_EFFECT_00011", "time": 1.0333}, {"name": "EFFECT-CROP-3_0010_EFFECT_00012", "time": 1.0667}, {"name": "EFFECT-CROP-3_0009_EFFECT_00013", "time": 1.1}, {"name": "EFFECT-CROP-3_0008_EFFECT_00014", "time": 1.1333}, {"name": "EFFECT-CROP-3_0007_EFFECT_00015", "time": 1.1667}, {"name": "EFFECT-CROP-3_0006_EFFECT_00016", "time": 1.2}, {"name": "EFFECT-CROP-3_0005_EFFECT_00017", "time": 1.2333}, {"name": "EFFECT-CROP-3_0004_EFFECT_00018", "time": 1.2667}, {"name": "EFFECT-CROP-3_0003_EFFECT_00019", "time": 1.3}, {"name": "EFFECT-CROP-3_0002_EFFECT_00020", "time": 1.3333}, {"name": "EFFECT-CROP-3_0001_EFFECT_00021", "time": 1.3667}, {"name": "EFFECT-CROP-3_0021_EFFECT_00001", "time": 1.4}, {"name": "EFFECT-CROP-3_0020_EFFECT_00002", "time": 1.4333}, {"name": "EFFECT-CROP-3_0019_EFFECT_00003", "time": 1.4667}, {"name": "EFFECT-CROP-3_0018_EFFECT_00004", "time": 1.5}, {"name": "EFFECT-CROP-3_0017_EFFECT_00005", "time": 1.5333}, {"name": "EFFECT-CROP-3_0016_EFFECT_00006", "time": 1.5667}, {"name": "EFFECT-CROP-3_0015_EFFECT_00007", "time": 1.6}, {"name": "EFFECT-CROP-3_0014_EFFECT_00008", "time": 1.6333}, {"name": "EFFECT-CROP-3_0013_EFFECT_00009", "time": 1.6667}, {"name": "EFFECT-CROP-3_0012_EFFECT_00010", "time": 1.7}, {"name": "EFFECT-CROP-3_0011_EFFECT_00011", "time": 1.7333}, {"name": "EFFECT-CROP-3_0010_EFFECT_00012", "time": 1.7667}, {"name": "EFFECT-CROP-3_0009_EFFECT_00013", "time": 1.8}, {"name": "EFFECT-CROP-3_0008_EFFECT_00014", "time": 1.8333}, {"name": "EFFECT-CROP-3_0007_EFFECT_00015", "time": 1.8667}, {"name": "EFFECT-CROP-3_0006_EFFECT_00016", "time": 1.9}, {"name": "EFFECT-CROP-3_0005_EFFECT_00017", "time": 1.9333}, {"name": "EFFECT-CROP-3_0004_EFFECT_00018", "time": 1.9667}, {"name": "EFFECT-CROP-3_0003_EFFECT_00019", "time": 2}, {"name": "EFFECT-CROP-3_0002_EFFECT_00020", "time": 2.0333}, {"name": "EFFECT-CROP-3_0001_EFFECT_00021", "time": 2.0667}, {"name": "EFFECT-CROP-3_0000_EFFECT_00022", "time": 2.1}]}, "LIGHT4": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.4333}, {"color": "ffffff80", "curve": "stepped", "time": 0.7}, {"color": "ffffff80", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}]}, "LIGHT5": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffff80", "time": 0.2}, {"color": "ffffff80", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}]}, "LIGHT2": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.2}, {"color": "ffffffff", "curve": "stepped", "time": 0.4333}, {"color": "ffffffff", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}]}, "LIGHT3": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffffff", "curve": "stepped", "time": 0.5667}, {"color": "ffffffff", "time": 1.3333}, {"color": "ffffff00", "time": 1.6667}]}, "N": {"color": [{"color": "ffffffff", "time": 0}]}, "O": {"color": [{"color": "ffffffff", "time": 0}]}}, "bones": {"BONE INFINITY GLOVE": {"translate": [{"x": 0, "y": -38.43, "time": 0}, {"x": 0, "y": 54.53, "time": 0.5}, {"x": 0, "y": -103.91, "time": 0.6667}, {"x": 0, "y": -115.28, "time": 0.8333}, {"x": 0, "y": -103.91, "time": 1.3333}, {"x": 0, "y": -38.43, "time": 2.1}]}, "bone15": {"rotate": [{"angle": -1.29, "time": 0.5}], "translate": [{"x": -21.37, "y": -5.27, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -21.37, "y": -5.27, "time": 0.6667}, {"x": -21.37, "y": -5.27, "time": 2.1}]}, "B": {"scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 0.8333}, {"x": 1.4, "y": 1.4, "time": 0.9333}, {"x": 0.8, "y": 0.8, "time": 1}, {"x": 1, "y": 1, "time": 1.0667}], "translate": [{"x": 0, "y": 0, "time": 0}]}, "bone16": {"rotate": [{"angle": -1.44, "time": 0.5}], "translate": [{"x": -24.97, "y": -3.93, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -24.97, "y": -3.93, "time": 0.6667}, {"x": -24.97, "y": -3.93, "time": 2.1}]}, "bone5": {"rotate": [{"angle": 0, "time": 0.3333}], "translate": [{"x": -11.82, "y": 2.3, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -11.82, "y": 2.3, "time": 0.6667}, {"x": -11.82, "y": 2.3, "time": 2.1}]}, "bone10": {"rotate": [{"angle": 0, "time": 0.3333}], "translate": [{"x": -35.95, "y": -0.85, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -35.95, "y": -0.85, "time": 0.6667}, {"x": -35.95, "y": -0.85, "time": 2.1}]}, "bone4": {"rotate": [{"angle": 37.03, "time": 0}, {"angle": 0, "time": 0.3333}, {"angle": -9.94, "time": 0.5}, {"curve": "stepped", "angle": 37.03, "time": 0.6667}, {"angle": 37.03, "time": 2.1}]}, "bone7": {"rotate": [{"angle": 0, "time": 0.3333}], "translate": [{"x": -22.12, "y": -2.52, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -22.12, "y": -2.52, "time": 0.6667}, {"x": -22.12, "y": -2.52, "time": 2.1}]}, "bone6": {"rotate": [{"angle": 0, "time": 0.3333}], "translate": [{"x": -28.15, "y": -0.81, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -28.15, "y": -0.81, "time": 0.6667}, {"x": -28.15, "y": -0.81, "time": 2.1}]}, "bone9": {"rotate": [{"angle": 0, "time": 0.3333}], "translate": [{"x": -25.38, "y": -2.25, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -25.38, "y": -2.25, "time": 0.6667}, {"x": -25.38, "y": -2.25, "time": 2.1}]}, "bone13": {"rotate": [{"angle": 0, "time": 0.3333}, {"angle": 3.71, "time": 0.5}], "translate": [{"x": -34.93, "y": 2.93, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -34.93, "y": 2.93, "time": 0.6667}, {"x": -34.93, "y": 2.93, "time": 2.1}]}, "bone8": {"rotate": [{"angle": -2.5, "time": 0}, {"curve": "stepped", "angle": 0, "time": 0.3333}, {"angle": 0, "time": 0.5}, {"curve": "stepped", "angle": -2.5, "time": 0.6667}, {"angle": -2.5, "time": 2.1}], "translate": [{"x": -13.43, "y": -1.16, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -13.43, "y": -1.16, "time": 0.6667}, {"x": -13.43, "y": -1.16, "time": 2.1}]}, "bone14": {"rotate": [{"angle": -14.23, "time": 0}, {"angle": 0, "time": 0.3333}, {"angle": 7.12, "time": 0.5}, {"curve": "stepped", "angle": -14.23, "time": 0.6667}, {"angle": -14.23, "time": 2.1}], "translate": [{"x": -5.08, "y": -1.84, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -5.08, "y": -1.84, "time": 0.6667}, {"x": -5.08, "y": -1.84, "time": 2.1}]}, "bone11": {"rotate": [{"angle": -1.84, "time": 0}, {"angle": 0, "time": 0.3333}, {"angle": 4.12, "time": 0.5}, {"curve": "stepped", "angle": -1.84, "time": 0.6667}, {"angle": -1.84, "time": 2.1}], "translate": [{"x": -8.33, "y": -4.04, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -8.33, "y": -4.04, "time": 0.6667}, {"x": -8.33, "y": -4.04, "time": 2.1}]}, "N": {"scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 1.0333}, {"x": 1.4, "y": 1.4, "time": 1.1333}, {"x": 0.8, "y": 0.8, "time": 1.2}, {"x": 1, "y": 1, "time": 1.2667}], "translate": [{"x": 0, "y": 0, "time": 0}]}, "bone12": {"rotate": [{"angle": 0, "time": 0.3333}, {"angle": -3.75, "time": 0.5}], "translate": [{"x": -30.75, "y": -1.46, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": -30.75, "y": -1.46, "time": 0.6667}, {"x": -30.75, "y": -1.46, "time": 2.1}]}, "O": {"scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 0.9333}, {"x": 1.4, "y": 1.4, "time": 1.0333}, {"x": 0.8, "y": 0.8, "time": 1.1}, {"x": 1, "y": 1, "time": 1.1667}], "translate": [{"x": 0, "y": 0, "time": 0}]}, "S": {"scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 1.2333}, {"x": 1.4, "y": 1.4, "time": 1.3333}, {"x": 0.8, "y": 0.8, "time": 1.4}, {"x": 1, "y": 1, "time": 1.4667}], "translate": [{"x": 0, "y": 0, "time": 0}]}, "U": {"scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 1.1333}, {"x": 1.4, "y": 1.4, "time": 1.2333}, {"x": 0.8, "y": 0.8, "time": 1.3}, {"x": 1, "y": 1, "time": 1.3667}], "translate": [{"x": 0, "y": 0, "time": 0}]}, "bone3": {"rotate": [{"angle": 8.14, "time": 0}, {"angle": 0, "time": 0.3333}, {"angle": -7.67, "time": 0.5}, {"curve": "stepped", "angle": 8.14, "time": 0.6667}, {"angle": 8.14, "time": 2.1}]}, "bone2": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 0.3333}, {"angle": -3.61, "time": 0.8333}, {"angle": 0, "time": 2.1}], "translate": [{"x": -4.53, "y": 1.06, "time": 0}, {"x": 0, "y": 0, "time": 0.3333}, {"x": -4.53, "y": 1.06, "time": 0.6667}, {"x": -13.58, "y": 3.18, "time": 0.8333}, {"x": -4.53, "y": 1.06, "time": 2.1}]}}}, "START": {"slots": {"B": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffff00", "time": 1}, {"color": "ffffffff", "time": 1.1}]}, "LIGHT 1": {"color": [{"color": "ffcf0000", "curve": "stepped", "time": 0}, {"color": "ffcf0000", "curve": "stepped", "time": 0.3333}, {"color": "ffcf0000", "time": 0.9}, {"color": "ffcf00ff", "curve": "stepped", "time": 1.1667}, {"color": "ffcf00ff", "time": 1.6667}, {"color": "ffcf0000", "time": 2}]}, "S": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffff00", "time": 1}, {"color": "ffffffff", "time": 1.1}]}, "U": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffff00", "time": 1}, {"color": "ffffffff", "time": 1.1}]}, "LIGHT": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": "stepped", "time": 0.3333}, {"color": "ffffff00", "time": 0.4667}, {"color": "ffffffff", "curve": "stepped", "time": 0.6667}, {"color": "ffffffff", "time": 1.6667}, {"color": "ffffff00", "time": 2}]}, "EFFECT": {"attachment": [{"name": "EFFECT-CROP-3_0022_EFFECT_00000", "time": 1}, {"name": "EFFECT-CROP-3_0021_EFFECT_00001", "time": 1.0333}, {"name": "EFFECT-CROP-3_0020_EFFECT_00002", "time": 1.0667}, {"name": "EFFECT-CROP-3_0019_EFFECT_00003", "time": 1.1}, {"name": "EFFECT-CROP-3_0018_EFFECT_00004", "time": 1.1333}, {"name": "EFFECT-CROP-3_0017_EFFECT_00005", "time": 1.1667}, {"name": "EFFECT-CROP-3_0016_EFFECT_00006", "time": 1.2}, {"name": "EFFECT-CROP-3_0015_EFFECT_00007", "time": 1.2333}, {"name": "EFFECT-CROP-3_0014_EFFECT_00008", "time": 1.2667}, {"name": "EFFECT-CROP-3_0013_EFFECT_00009", "time": 1.3}, {"name": "EFFECT-CROP-3_0012_EFFECT_00010", "time": 1.3333}, {"name": "EFFECT-CROP-3_0011_EFFECT_00011", "time": 1.3667}, {"name": "EFFECT-CROP-3_0010_EFFECT_00012", "time": 1.4}, {"name": "EFFECT-CROP-3_0009_EFFECT_00013", "time": 1.4333}, {"name": "EFFECT-CROP-3_0008_EFFECT_00014", "time": 1.4667}, {"name": "EFFECT-CROP-3_0007_EFFECT_00015", "time": 1.5}, {"name": "EFFECT-CROP-3_0006_EFFECT_00016", "time": 1.5333}, {"name": "EFFECT-CROP-3_0005_EFFECT_00017", "time": 1.5667}, {"name": "EFFECT-CROP-3_0004_EFFECT_00018", "time": 1.6}, {"name": "EFFECT-CROP-3_0003_EFFECT_00019", "time": 1.6333}, {"name": "EFFECT-CROP-3_0002_EFFECT_00020", "time": 1.6667}, {"name": "EFFECT-CROP-3_0001_EFFECT_00021", "time": 1.7}, {"name": "EFFECT-CROP-3_0021_EFFECT_00001", "time": 1.7333}, {"name": "EFFECT-CROP-3_0020_EFFECT_00002", "time": 1.7667}, {"name": "EFFECT-CROP-3_0019_EFFECT_00003", "time": 1.8}, {"name": "EFFECT-CROP-3_0018_EFFECT_00004", "time": 1.8333}, {"name": "EFFECT-CROP-3_0017_EFFECT_00005", "time": 1.8667}, {"name": "EFFECT-CROP-3_0016_EFFECT_00006", "time": 1.9}, {"name": "EFFECT-CROP-3_0015_EFFECT_00007", "time": 1.9333}, {"name": "EFFECT-CROP-3_0014_EFFECT_00008", "time": 1.9667}, {"name": "EFFECT-CROP-3_0013_EFFECT_00009", "time": 2}, {"name": "EFFECT-CROP-3_0012_EFFECT_00010", "time": 2.0333}, {"name": "EFFECT-CROP-3_0011_EFFECT_00011", "time": 2.0667}, {"name": "EFFECT-CROP-3_0010_EFFECT_00012", "time": 2.1}, {"name": "EFFECT-CROP-3_0009_EFFECT_00013", "time": 2.1333}, {"name": "EFFECT-CROP-3_0008_EFFECT_00014", "time": 2.1667}, {"name": "EFFECT-CROP-3_0007_EFFECT_00015", "time": 2.2}, {"name": "EFFECT-CROP-3_0006_EFFECT_00016", "time": 2.2333}, {"name": "EFFECT-CROP-3_0005_EFFECT_00017", "time": 2.2667}, {"name": "EFFECT-CROP-3_0004_EFFECT_00018", "time": 2.3}, {"name": "EFFECT-CROP-3_0003_EFFECT_00019", "time": 2.3333}, {"name": "EFFECT-CROP-3_0002_EFFECT_00020", "time": 2.3667}, {"name": "EFFECT-CROP-3_0001_EFFECT_00021", "time": 2.4}, {"name": "EFFECT-CROP-3_0000_EFFECT_00022", "time": 2.4333}]}, "LIGHT4": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": "stepped", "time": 0.3333}, {"color": "ffffff00", "time": 0.7667}, {"color": "ffffff80", "curve": "stepped", "time": 1.0333}, {"color": "ffffff80", "time": 1.6667}, {"color": "ffffff00", "time": 2}]}, "LIGHT5": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffff80", "time": 0.5333}, {"color": "ffffff80", "time": 1.6667}, {"color": "ffffff00", "time": 2}]}, "LIGHT2": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": "stepped", "time": 0.3333}, {"color": "ffffff00", "time": 0.5333}, {"color": "ffffffff", "curve": "stepped", "time": 0.7667}, {"color": "ffffffff", "time": 1.6667}, {"color": "ffffff00", "time": 2}]}, "LIGHT3": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": "stepped", "time": 0.3333}, {"color": "ffffff00", "time": 0.6667}, {"color": "ffffffff", "curve": "stepped", "time": 0.9}, {"color": "ffffffff", "time": 1.6667}, {"color": "ffffff00", "time": 2}]}, "N": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffff00", "time": 1}, {"color": "ffffffff", "time": 1.1}]}, "O": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffff00", "time": 1}, {"color": "ffffffff", "time": 1.1}]}}, "bones": {"B": {"translate": [{"curve": "stepped", "x": -450.74, "y": 0, "time": 0}, {"curve": "stepped", "x": -450.74, "y": 0, "time": 0.3333}, {"x": -450.74, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 1.1}, {"x": 17.55, "y": 0, "time": 1.1667}, {"x": -13.07, "y": 0, "time": 1.2333}, {"x": 0, "y": 0, "time": 1.3}]}, "bone15": {"rotate": [{"angle": -1.29, "time": 0.8333}], "translate": [{"curve": "stepped", "x": -21.37, "y": -5.27, "time": 0}, {"x": -21.37, "y": -5.27, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -21.37, "y": -5.27, "time": 1}, {"x": -21.37, "y": -5.27, "time": 2.4333}]}, "BONE INFINITY GLOVE": {"scale": [{"x": 0, "y": 0, "time": 0}, {"x": 1, "y": 1, "time": 0.1}, {"x": 1.08, "y": 1.08, "time": 0.1667}, {"x": 0.98, "y": 0.98, "time": 0.2333}, {"x": 1, "y": 1, "time": 0.3}], "translate": [{"curve": "stepped", "x": 0, "y": -38.43, "time": 0}, {"x": 0, "y": -38.43, "time": 0.3333}, {"x": 0, "y": 54.53, "time": 0.8333}, {"x": 0, "y": -103.91, "time": 1}, {"x": 0, "y": -115.28, "time": 1.1667}, {"x": 0, "y": -103.91, "time": 1.6667}, {"x": 0, "y": -38.43, "time": 2.4333}]}, "bone16": {"rotate": [{"angle": -1.44, "time": 0.8333}], "translate": [{"curve": "stepped", "x": -24.97, "y": -3.93, "time": 0}, {"x": -24.97, "y": -3.93, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -24.97, "y": -3.93, "time": 1}, {"x": -24.97, "y": -3.93, "time": 2.4333}]}, "bone5": {"rotate": [{"angle": 0, "time": 0.6667}], "translate": [{"curve": "stepped", "x": -11.82, "y": 2.3, "time": 0}, {"x": -11.82, "y": 2.3, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -11.82, "y": 2.3, "time": 1}, {"x": -11.82, "y": 2.3, "time": 2.4333}]}, "bone10": {"rotate": [{"angle": 0, "time": 0.6667}], "translate": [{"curve": "stepped", "x": -35.95, "y": -0.85, "time": 0}, {"x": -35.95, "y": -0.85, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -35.95, "y": -0.85, "time": 1}, {"x": -35.95, "y": -0.85, "time": 2.4333}]}, "bone4": {"rotate": [{"curve": "stepped", "angle": 37.03, "time": 0}, {"angle": 37.03, "time": 0.3333}, {"angle": 0, "time": 0.6667}, {"angle": -9.94, "time": 0.8333}, {"curve": "stepped", "angle": 37.03, "time": 1}, {"angle": 37.03, "time": 2.4333}]}, "bone7": {"rotate": [{"angle": 0, "time": 0.6667}], "translate": [{"curve": "stepped", "x": -22.12, "y": -2.52, "time": 0}, {"x": -22.12, "y": -2.52, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -22.12, "y": -2.52, "time": 1}, {"x": -22.12, "y": -2.52, "time": 2.4333}]}, "bone6": {"rotate": [{"angle": 0, "time": 0.6667}], "translate": [{"curve": "stepped", "x": -28.15, "y": -0.81, "time": 0}, {"x": -28.15, "y": -0.81, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -28.15, "y": -0.81, "time": 1}, {"x": -28.15, "y": -0.81, "time": 2.4333}]}, "bone13": {"rotate": [{"angle": 0, "time": 0.6667}, {"angle": 3.71, "time": 0.8333}], "translate": [{"curve": "stepped", "x": -34.93, "y": 2.93, "time": 0}, {"x": -34.93, "y": 2.93, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -34.93, "y": 2.93, "time": 1}, {"x": -34.93, "y": 2.93, "time": 2.4333}]}, "bone9": {"rotate": [{"angle": 0, "time": 0.6667}], "translate": [{"curve": "stepped", "x": -25.38, "y": -2.25, "time": 0}, {"x": -25.38, "y": -2.25, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -25.38, "y": -2.25, "time": 1}, {"x": -25.38, "y": -2.25, "time": 2.4333}]}, "bone14": {"rotate": [{"curve": "stepped", "angle": -14.23, "time": 0}, {"angle": -14.23, "time": 0.3333}, {"angle": 0, "time": 0.6667}, {"angle": 7.12, "time": 0.8333}, {"curve": "stepped", "angle": -14.23, "time": 1}, {"angle": -14.23, "time": 2.4333}], "translate": [{"curve": "stepped", "x": -5.08, "y": -1.84, "time": 0}, {"x": -5.08, "y": -1.84, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -5.08, "y": -1.84, "time": 1}, {"x": -5.08, "y": -1.84, "time": 2.4333}]}, "bone8": {"rotate": [{"curve": "stepped", "angle": -2.5, "time": 0}, {"angle": -2.5, "time": 0.3333}, {"curve": "stepped", "angle": 0, "time": 0.6667}, {"angle": 0, "time": 0.8333}, {"curve": "stepped", "angle": -2.5, "time": 1}, {"angle": -2.5, "time": 2.4333}], "translate": [{"curve": "stepped", "x": -13.43, "y": -1.16, "time": 0}, {"x": -13.43, "y": -1.16, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -13.43, "y": -1.16, "time": 1}, {"x": -13.43, "y": -1.16, "time": 2.4333}]}, "bone11": {"rotate": [{"curve": "stepped", "angle": -1.84, "time": 0}, {"angle": -1.84, "time": 0.3333}, {"angle": 0, "time": 0.6667}, {"angle": 4.12, "time": 0.8333}, {"curve": "stepped", "angle": -1.84, "time": 1}, {"angle": -1.84, "time": 2.4333}], "translate": [{"curve": "stepped", "x": -8.33, "y": -4.04, "time": 0}, {"x": -8.33, "y": -4.04, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -8.33, "y": -4.04, "time": 1}, {"x": -8.33, "y": -4.04, "time": 2.4333}]}, "N": {"translate": [{"curve": "stepped", "x": 548.41, "y": 0, "time": 0}, {"curve": "stepped", "x": 548.41, "y": 0, "time": 0.3333}, {"x": 548.41, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 1.1}, {"x": -10.9, "y": 0, "time": 1.1667}, {"x": 14.93, "y": 0, "time": 1.2333}, {"x": 0, "y": 0, "time": 1.3}]}, "bone12": {"rotate": [{"angle": 0, "time": 0.6667}, {"angle": -3.75, "time": 0.8333}], "translate": [{"curve": "stepped", "x": -30.75, "y": -1.46, "time": 0}, {"x": -30.75, "y": -1.46, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 0.8333}, {"curve": "stepped", "x": -30.75, "y": -1.46, "time": 1}, {"x": -30.75, "y": -1.46, "time": 2.4333}]}, "O": {"translate": [{"curve": "stepped", "x": -450.74, "y": 0, "time": 0}, {"curve": "stepped", "x": -450.74, "y": 0, "time": 0.3333}, {"x": -450.74, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 1.1}, {"x": 17.55, "y": 0, "time": 1.1667}, {"x": -13.07, "y": 0, "time": 1.2333}, {"x": 0, "y": 0, "time": 1.3}]}, "S": {"translate": [{"curve": "stepped", "x": 548.41, "y": 0, "time": 0}, {"curve": "stepped", "x": 548.41, "y": 0, "time": 0.3333}, {"x": 548.41, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 1.1}, {"x": -10.9, "y": 0, "time": 1.1667}, {"x": 14.93, "y": 0, "time": 1.2333}, {"x": 0, "y": 0, "time": 1.3}]}, "U": {"translate": [{"curve": "stepped", "x": 548.41, "y": 0, "time": 0}, {"curve": "stepped", "x": 548.41, "y": 0, "time": 0.3333}, {"x": 548.41, "y": 0, "time": 1}, {"x": 0, "y": 0, "time": 1.1}, {"x": -10.9, "y": 0, "time": 1.1667}, {"x": 14.93, "y": 0, "time": 1.2333}, {"x": 0, "y": 0, "time": 1.3}]}, "bone3": {"rotate": [{"curve": "stepped", "angle": 8.14, "time": 0}, {"angle": 8.14, "time": 0.3333}, {"angle": 0, "time": 0.6667}, {"angle": -7.67, "time": 0.8333}, {"curve": "stepped", "angle": 8.14, "time": 1}, {"angle": 8.14, "time": 2.4333}]}, "bone2": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 0.3333}, {"angle": 0, "time": 0.6667}, {"angle": -3.61, "time": 1.1667}, {"angle": 0, "time": 2.4333}], "translate": [{"curve": "stepped", "x": -4.53, "y": 1.06, "time": 0}, {"x": -4.53, "y": 1.06, "time": 0.3333}, {"x": 0, "y": 0, "time": 0.6667}, {"x": -4.53, "y": 1.06, "time": 1}, {"x": -13.58, "y": 3.18, "time": 1.1667}, {"x": -4.53, "y": 1.06, "time": 2.4333}]}}}}}