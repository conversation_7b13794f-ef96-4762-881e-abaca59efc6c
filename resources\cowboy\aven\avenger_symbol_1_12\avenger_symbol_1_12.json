{"skeleton": {"hash": "iSRS3XdvdxsgkR4/lNxZRgLKWXg", "spine": "3.6.53", "width": 145, "height": 167, "images": "./HULK/"}, "bones": [{"name": "root"}, {"name": "CENTER", "parent": "root", "y": 74.7}, {"name": "BONUS", "parent": "CENTER", "y": -117.57}, {"name": "bone", "parent": "root", "length": 39.77, "rotation": 95.39, "x": -1.74, "y": -51.56}, {"name": "bone2", "parent": "bone", "length": 79.53, "rotation": -10.78, "x": 39.77}], "slots": [{"name": "BACK", "bone": "root", "attachment": "BACK"}, {"name": "HULK_BODY", "bone": "root", "attachment": "HULK_BODY"}, {"name": "BORDER", "bone": "root", "attachment": "BORDER"}, {"name": "HULK_HEAD", "bone": "root", "attachment": "HULK_HEAD"}], "skins": {"default": {"BACK": {"BACK": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 1, 55.5, -140.7, 1, 1, 1, -55.5, -140.7, 1, 1, 1, -55.5, -8.7, 1, 1, 1, 55.5, -8.7, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 111, "height": 132}}, "BORDER": {"BORDER": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 1, 72.5, -147.2, 1, 1, 1, -72.5, -147.2, 1, 1, 1, -72.5, -2.2, 1, 1, 1, 72.5, -2.2, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 145, "height": 145}}, "HULK_BODY": {"HULK_BODY": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, -12.66, -60.31, 1, 1, 3, -1.39, 59.15, 1, 1, 3, 81.25, 51.36, 1, 1, 3, 69.98, -68.11, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 120, "height": 83}}, "HULK_HEAD": {"HULK_HEAD": {"type": "mesh", "uvs": [0.47988, 7e-05, 0.3322, 0.03173, 0.14045, 0.13414, 0.02584, 0.298, 0, 0.48235, 0.02143, 0.69276, 0.1691, 0.91249, 0.34763, 1, 0.57465, 1, 0.80387, 0.97393, 0.9229, 0.85104, 0.96036, 0.65738, 0.97359, 0.50283, 0.99122, 0.33711, 1, 0.18069, 0.84355, 0.04663, 0.66722, 7e-05, 0.29032, 0.37994, 0.42698, 0.32035, 0.58787, 0.35014, 0.64738, 0.44697, 0.52616, 0.53635, 0.3873, 0.53821, 0.27269, 0.50097, 0.69318, 0.45066, 0.74369, 0.37556, 0.84865, 0.38158, 0.9018, 0.44358, 0.88661, 0.53765, 0.83347, 0.55689, 0.73984, 0.5291, 0.59061, 0.5923, 0.55889, 0.56934, 0.56176, 0.54313, 0.58339, 0.52166, 0.60523, 0.51485, 0.64678, 0.08784, 0.66993, 0.35179, 0.6703, 0.44921, 0.68123, 0.53743, 0.6703, 0.59488, 0.73466, 0.60514, 0.75287, 0.58052, 0.65938, 0.19992, 0.42072, 0.75531, 0.45029, 0.67085, 0.57421, 0.61731, 0.66856, 0.6304, 0.73052, 0.63397, 0.81642, 0.70773, 0.81078, 0.7791, 0.73897, 0.83264, 0.63899, 0.81123, 0.5038, 0.82907, 0.28612, 0.68008, 0.29745, 0.85041, 0.37447, 0.92696, 0.3269, 0.1825, 0.45375, 0.17867, 0.46281, 0.08106, 0.32463, 0.11169, 0.20684, 0.16336, 0.14794, 0.3643, 0.11849, 0.50593, 0.14341, 0.69922, 0.17286, 0.83892, 0.25214, 0.90016, 0.0913, 0.26479, 0.06186, 0.34325, 0.04826, 0.48296, 0.06865, 0.68773, 0.12823, 0.80285, 0.58935, 0.20376, 0.58692, 0.09044, 0.74056, 0.11104, 0.74543, 0.22436, 0.84055, 0.23672, 0.82835, 0.13371, 0.8942, 0.18934, 0.90883, 0.27999, 0.92103, 0.40773, 0.92354, 0.45605, 0.91176, 0.55381, 0.89701, 0.66339, 0.86824, 0.82222, 0.83179, 0.89839, 0.76656, 0.93404, 0.83386, 0.63521, 0.61458, 0.94608, 0.47848, 0.94395, 0.7154, 0.93118], "triangles": [59, 1, 0, 16, 73, 0, 74, 36, 16, 73, 59, 0, 16, 36, 73, 15, 74, 16, 60, 1, 59, 77, 74, 15, 1, 61, 2, 60, 61, 1, 58, 60, 59, 78, 77, 15, 57, 60, 58, 61, 60, 57, 14, 78, 15, 43, 36, 74, 72, 73, 36, 72, 36, 43, 75, 74, 77, 43, 74, 75, 76, 77, 78, 75, 77, 76, 67, 2, 61, 79, 78, 14, 76, 78, 79, 67, 3, 2, 18, 57, 58, 13, 79, 14, 68, 3, 67, 73, 58, 59, 73, 72, 58, 72, 18, 58, 19, 18, 72, 37, 43, 75, 62, 67, 61, 68, 67, 62, 25, 37, 75, 25, 75, 76, 17, 61, 57, 17, 57, 18, 62, 61, 17, 26, 76, 79, 80, 26, 79, 25, 76, 26, 13, 80, 79, 27, 26, 80, 19, 43, 37, 43, 19, 72, 38, 20, 37, 20, 19, 37, 37, 24, 38, 25, 24, 37, 12, 81, 80, 27, 80, 81, 4, 3, 68, 69, 4, 68, 69, 68, 62, 23, 62, 17, 63, 69, 62, 13, 12, 80, 23, 63, 62, 19, 20, 21, 19, 21, 18, 35, 34, 20, 30, 24, 25, 30, 25, 26, 29, 30, 26, 20, 34, 21, 39, 38, 24, 39, 24, 30, 26, 27, 29, 28, 27, 81, 82, 28, 81, 22, 17, 18, 22, 18, 21, 23, 17, 22, 33, 21, 34, 12, 82, 81, 28, 29, 27, 32, 21, 33, 39, 35, 20, 42, 30, 29, 31, 34, 35, 33, 34, 31, 32, 33, 31, 39, 20, 38, 40, 35, 39, 31, 35, 40, 42, 41, 39, 42, 39, 30, 40, 39, 41, 46, 32, 31, 47, 31, 40, 47, 40, 41, 46, 31, 47, 48, 47, 41, 82, 87, 29, 82, 29, 28, 42, 29, 87, 11, 82, 12, 83, 87, 82, 11, 83, 82, 45, 22, 21, 45, 21, 32, 45, 32, 46, 54, 23, 22, 54, 22, 45, 70, 69, 63, 5, 4, 69, 5, 69, 70, 64, 63, 23, 64, 23, 54, 70, 63, 64, 87, 41, 42, 49, 87, 83, 87, 48, 41, 49, 48, 87, 44, 54, 45, 49, 51, 48, 71, 70, 64, 52, 46, 47, 53, 45, 46, 53, 44, 45, 52, 47, 48, 51, 52, 48, 84, 49, 83, 50, 49, 84, 46, 52, 53, 49, 50, 51, 65, 64, 54, 71, 64, 65, 55, 54, 44, 65, 54, 55, 11, 84, 83, 10, 84, 11, 85, 50, 84, 85, 84, 10, 51, 50, 85, 66, 65, 55, 6, 71, 65, 6, 65, 66, 5, 70, 71, 6, 5, 71, 56, 55, 44, 56, 44, 53, 66, 55, 56, 90, 52, 51, 86, 90, 51, 85, 86, 51, 89, 56, 53, 88, 53, 52, 88, 52, 90, 89, 53, 88, 9, 86, 85, 9, 85, 10, 7, 66, 56, 6, 66, 7, 8, 89, 88, 7, 56, 89, 8, 7, 89, 8, 88, 90, 9, 8, 90, 9, 90, 86], "vertices": [1, 4, 106.28, 7.01, 1, 1, 4, 101.26, 21.08, 1, 1, 4, 87.67, 38.67, 1, 1, 4, 67.69, 48.07, 1, 1, 4, 46.16, 48.58, 1, 1, 4, 22.06, 44.2, 1, 1, 4, -1.96, 27.39, 1, 1, 4, -10.42, 9.02, 1, 1, 4, -8.33, -13.13, 1, 1, 4, -3.21, -35.21, 1, 1, 4, 12.08, -45.48, 1, 1, 4, 34.79, -47.03, 1, 1, 4, 52.76, -46.63, 1, 1, 4, 72.06, -46.55, 1, 1, 4, 90.2, -45.7, 1, 1, 4, 104.25, -28.98, 1, 1, 4, 108, -11.27, 1, 1, 4, 60.66, 21.37, 1, 1, 4, 68.8, 8.68, 1, 1, 4, 66.84, -7.34, 1, 1, 4, 56.21, -14.2, 1, 1, 4, 44.77, -3.35, 1, 1, 4, 43.28, 10.18, 1, 1, 4, 46.52, 21.77, 1, 1, 4, 56.2, -18.71, 1, 1, 4, 65.34, -22.82, 1, 1, 4, 65.61, -33.12, 1, 1, 4, 58.94, -38.98, 1, 1, 4, 47.94, -38.53, 1, 1, 4, 45.23, -33.55, 1, 1, 4, 47.57, -24.11, 1, 1, 4, 38.9, -10.24, 1, 1, 4, 41.26, -6.9, 1, 1, 4, 44.31, -6.89, 1, 1, 4, 46.99, -8.77, 1, 1, 4, 47.98, -10.83, 1, 1, 4, 97.68, -10.23, 1, 1, 4, 67.41, -15.36, 1, 1, 4, 56.16, -16.46, 1, 1, 4, 46.07, -18.49, 1, 1, 4, 39.34, -18.05, 1, 1, 4, 38.74, -24.44, 1, 1, 4, 41.75, -25.95, 1, 1, 4, 84.85, -12.68, 1, 1, 4, 18.51, 4.56, 1, 1, 4, 28.54, 2.59, 1, 1, 4, 35.86, -8.92, 1, 1, 4, 35.22, -18.26, 1, 1, 4, 35.38, -24.35, 1, 1, 4, 27.65, -33.53, 1, 1, 4, 19.35, -33.76, 1, 1, 4, 12.51, -27.34, 1, 1, 4, 14.06, -17.35, 1, 1, 4, 10.76, -4.35, 1, 1, 4, 25.96, 18.51, 1, 1, 4, 6.39, 15.55, 1, 1, 4, -1.74, 7.2, 1, 1, 4, 83.8, 19.95, 1, 1, 4, 85.41, 7.62, 1, 1, 4, 96.77, 7.8, 1, 1, 4, 91.96, 20.94, 1, 1, 4, 84.91, 31.87, 1, 1, 4, 61.16, 35.43, 1, 1, 4, 44.53, 36.76, 1, 1, 4, 22.44, 32.22, 1, 1, 4, 6.57, 27.83, 1, 1, 4, 0.23, 19.43, 1, 1, 4, 72.13, 42.04, 1, 1, 4, 62.8, 44.06, 1, 1, 4, 46.54, 43.86, 1, 1, 4, 23.07, 39.64, 1, 1, 4, 10.33, 32.58, 1, 1, 4, 83.76, -5.89, 1, 1, 4, 96.82, -4.42, 1, 1, 4, 95.86, -19.63, 1, 1, 4, 82.82, -21.34, 1, 1, 4, 82.27, -30.75, 1, 1, 4, 94.05, -28.44, 1, 1, 4, 88.23, -35.47, 1, 1, 4, 77.9, -37.89, 1, 1, 4, 63.26, -40.47, 1, 1, 4, 57.7, -41.24, 1, 1, 4, 46.3, -41.16, 1, 1, 4, 33.51, -40.91, 1, 1, 4, 14.9, -39.84, 1, 1, 4, 5.77, -37.11, 1, 1, 4, 1.05, -31.13, 1, 1, 4, 36.18, -34.44, 1, 1, 4, -1.74, -16.44, 1, 1, 4, -2.74, -3.13, 1, 1, 4, 0.91, -26.11, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 42, 44, 44, 46, 46, 34, 34, 36, 36, 38, 38, 40, 40, 42, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 48, 62, 64, 68, 66, 66, 64, 68, 70, 60, 84, 84, 82, 80, 78, 78, 76, 76, 74, 74, 86, 86, 72, 88, 106, 106, 104, 104, 102, 102, 100, 100, 98, 98, 96, 96, 94, 94, 92, 92, 90, 90, 88, 46, 108, 108, 110, 110, 112, 114, 34, 116, 36, 118, 116, 120, 114, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 134, 136, 136, 138, 138, 140, 140, 142, 144, 38, 144, 146, 148, 150, 150, 50, 52, 152, 152, 154, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 58, 174, 174, 98, 104, 176, 106, 178, 102, 180], "width": 98, "height": 116}}}}, "animations": {"symbolFx": {"bones": {"bone": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": 0, "y": 6.33}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 1, "x": 0, "y": -4.75}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 1.6667, "x": 0, "y": 6.33}, {"time": 2, "x": 0, "y": 0}, {"time": 2.3333, "x": 0, "y": -4.75}, {"time": 2.6667, "x": 0, "y": 0}]}, "BONUS": {"scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.3333, "x": 1.2, "y": 1.2}, {"time": 0.6667, "x": 1, "y": 1}, {"time": 1, "x": 1.2, "y": 1.2}, {"time": 1.3333, "x": 1, "y": 1}, {"time": 1.6667, "x": 1.2, "y": 1.2}, {"time": 2, "x": 1, "y": 1}, {"time": 2.3333, "x": 1.2, "y": 1.2}, {"time": 2.6667, "x": 1, "y": 1}]}}, "deform": {"default": {"HULK_HEAD": {"HULK_HEAD": [{"time": 0}, {"time": 0.6667, "offset": 10, "vertices": [-0.10931, 1.15837, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.14757, 1.56394, -0.14757, 1.56394, 0, 0, 0, 0, 0.42453, -4.50131, 0.6706, -7.11135, 0.54709, -5.80063, 0.26983, -2.86082, 0.27281, -2.89276, 0.48415, -5.13421, 0.42453, -4.50131, 0.37259, -3.95121, 0.33432, -3.5448, 0.13112, -1.39002, -0.02684, 0.28435, -0.02684, 0.28435, 0.08405, -0.89106, 0.15564, -1.65046, 0.41136, -4.36166, 0.41136, -4.36166, 0.41136, -4.36166, 0.41136, -4.36166, 0.41136, -4.36166, 1.01009, -10.70848, 0.56002, -5.93732, 0.46247, -4.90326, 0.54836, -5.81368, 0.45498, -4.82361, 0.1892, -2.0059, 0.1892, -2.0059, 0.63453, -6.72701, 0.52891, -5.60817, 0.52891, -5.60817, 0.59242, -6.28105, 0.37833, -4.01199, 0.35186, -3.73251, 0.05538, -0.58834, -0.15778, 1.67179, 0.23605, -2.50331, 0.49988, -5.3001, 0.52891, -5.60817, 0.42856, -4.54406, 0.32472, -3.44361, 0.32472, -3.44361, 0.34556, -3.66372, 0.50617, -5.36631, 0.55937, -5.93058, 0.73972, -7.84239, 0.17109, -1.81512, 0.17109, -1.81512, 0.17109, -1.81512, 0.17109, -1.81512, 0.07719, -0.81988, 0.17109, -1.81512, -0.28175, 2.98628, -0.28175, 2.98628, -0.28175, 2.98628, -0.28175, 2.98628, -0.28175, 2.98628, 0.59779, -6.33773, 0.79409, -8.41917, 0.76837, -8.14508, 0.34907, -3.7013, 0.06706, -0.71088, 0, 0, -0.30821, 3.26651, -0.22775, 2.41345, -0.30821, 3.26651, -0.30821, 3.26651, -0.30821, 3.26651, -0.30821, 3.26651, -0.30821, 3.26651, -0.30821, 3.26651, -0.26877, 2.84835, 0.08405, -0.89106, 0.31139, -5.53747, 0.49081, -5.20433, 0.28513, -3.02292]}, {"time": 1.3333}, {"time": 2, "offset": 10, "vertices": [-0.10931, 1.15837, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.14757, 1.56394, -0.14757, 1.56394, 0, 0, 0, 0, 0.42453, -4.50131, 0.6706, -7.11135, 0.54709, -5.80063, 0.26983, -2.86082, 0.27281, -2.89276, 0.48415, -5.13421, 0.42453, -4.50131, 0.37259, -3.95121, 0.33432, -3.5448, 0.13112, -1.39002, -0.02684, 0.28435, -0.02684, 0.28435, 0.08405, -0.89106, 0.15564, -1.65046, 0.41136, -4.36166, 0.41136, -4.36166, 0.41136, -4.36166, 0.41136, -4.36166, 0.41136, -4.36166, 1.01009, -10.70848, 0.56002, -5.93732, 0.46247, -4.90326, 0.54836, -5.81368, 0.45498, -4.82361, 0.1892, -2.0059, 0.1892, -2.0059, 0.63453, -6.72701, 0.52891, -5.60817, 0.52891, -5.60817, 0.59242, -6.28105, 0.37833, -4.01199, 0.35186, -3.73251, 0.05538, -0.58834, -0.15778, 1.67179, 0.23605, -2.50331, 0.49988, -5.3001, 0.52891, -5.60817, 0.42856, -4.54406, 0.32472, -3.44361, 0.32472, -3.44361, 0.34556, -3.66372, 0.50617, -5.36631, 0.55937, -5.93058, 0.73972, -7.84239, 0.17109, -1.81512, 0.17109, -1.81512, 0.17109, -1.81512, 0.17109, -1.81512, 0.07719, -0.81988, 0.17109, -1.81512, -0.28175, 2.98628, -0.28175, 2.98628, -0.28175, 2.98628, -0.28175, 2.98628, -0.28175, 2.98628, 0.59779, -6.33773, 0.79409, -8.41917, 0.76837, -8.14508, 0.34907, -3.7013, 0.06706, -0.71088, 0, 0, -0.30821, 3.26651, -0.22775, 2.41345, -0.30821, 3.26651, -0.30821, 3.26651, -0.30821, 3.26651, -0.30821, 3.26651, -0.30821, 3.26651, -0.30821, 3.26651, -0.26877, 2.84835, 0.08405, -0.89106, 0.31139, -5.53747, 0.49081, -5.20433, 0.28513, -3.02292]}, {"time": 2.6667}]}}}}, "idle": {"bones": {"bone": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1, "x": 0, "y": 0}]}, "BONUS": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.1, "x": 1, "y": 1}]}}, "deform": {"default": {"HULK_HEAD": {"HULK_HEAD": [{"time": 0, "curve": "stepped"}, {"time": 0.1}]}}}}}}