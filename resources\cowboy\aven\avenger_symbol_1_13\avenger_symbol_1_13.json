{"skeleton": {"hash": "OeR/H76+82dSXSc8I+8ceSOmHVM", "spine": "3.6.53", "width": 441.09, "height": 401.94, "images": "./INFINITY GLOIVE/"}, "bones": [{"name": "root"}, {"name": "BONE EFFECT", "parent": "root", "scaleX": 2.61, "scaleY": 2.61}, {"name": "CENTER", "parent": "root", "y": 3.46}, {"name": "JACKPOT", "parent": "CENTER", "y": -47.88}, {"name": "bone", "parent": "root", "length": 65.3, "rotation": 102.59, "x": 9.61, "y": -53.22}, {"name": "bone2", "parent": "bone", "length": 50.08, "rotation": 0.31, "x": 65.3}, {"name": "bone3", "parent": "bone2", "length": 25.31, "rotation": -22.22, "x": 16.47, "y": -34.38}, {"name": "bone4", "parent": "bone3", "length": 18.8, "rotation": 24.88, "x": 25.31}, {"name": "bone5", "parent": "bone2", "length": 12.37, "rotation": -3.45, "x": 50.29, "y": -18.83}, {"name": "bone6", "parent": "bone5", "length": 11.94, "rotation": -2.94, "x": 12.37}, {"name": "bone7", "parent": "bone6", "length": 8.5, "rotation": -1.95, "x": 11.94}, {"name": "bone8", "parent": "bone2", "length": 13.41, "rotation": 3.24, "x": 50.49, "y": 0.25}, {"name": "bone9", "parent": "bone8", "length": 10.29, "rotation": 1.1, "x": 13.41}, {"name": "bone10", "parent": "bone9", "length": 7.25, "rotation": -6.46, "x": 10.29}, {"name": "bone11", "parent": "bone2", "length": 12.46, "rotation": 9.47, "x": 46.19, "y": 17.93}, {"name": "bone12", "parent": "bone11", "length": 9.97, "rotation": -4.56, "x": 12.46}, {"name": "bone13", "parent": "bone12", "length": 7.83, "rotation": -12.85, "x": 9.97}, {"name": "bone14", "parent": "bone2", "length": 10.82, "rotation": 19.29, "x": 42.64, "y": 31.27}, {"name": "bone15", "parent": "bone14", "length": 9.33, "rotation": -13.1, "x": 10.82}, {"name": "bone16", "parent": "bone15", "length": 6.45, "rotation": -16.08, "x": 9.33}], "slots": [{"name": "BACK", "bone": "root", "attachment": "BACK"}, {"name": "BORDER", "bone": "root", "attachment": "BORDER"}, {"name": "JACKPOT EFFECT", "bone": "BONE EFFECT", "attachment": "1", "blend": "additive"}, {"name": "Color Balance 1 copy 18", "bone": "root", "attachment": "Color Balance 1 copy 18"}, {"name": "Color Balance 1 copy 16", "bone": "root", "attachment": "Color Balance 1 copy 16"}, {"name": "Color Balance 1 copy 15", "bone": "root", "attachment": "Color Balance 1 copy 15"}, {"name": "Color Balance 1 copy 14", "bone": "root", "attachment": "Color Balance 1 copy 14"}, {"name": "Color Balance 1 copy 13", "bone": "root", "attachment": "Color Balance 1 copy 13"}, {"name": "Color Balance 1 copy 12", "bone": "root", "attachment": "Color Balance 1 copy 12"}, {"name": "Color Balance 1 copy 11", "bone": "root", "attachment": "Color Balance 1 copy 11"}, {"name": "Color Balance 1 copy 10", "bone": "root", "attachment": "Color Balance 1 copy 10"}, {"name": "Color Balance 1 copy 9", "bone": "root", "attachment": "Color Balance 1 copy 9"}, {"name": "Color Balance 1 copy 8", "bone": "root", "attachment": "Color Balance 1 copy 8"}, {"name": "Color Balance 1 copy 7", "bone": "root", "attachment": "Color Balance 1 copy 7"}, {"name": "Color Balance 1 copy 6", "bone": "root", "attachment": "Color Balance 1 copy 6"}, {"name": "Color Balance 1 copy 5", "bone": "root", "attachment": "Color Balance 1 copy 5"}, {"name": "Color Balance 1 copy 4", "bone": "root", "attachment": "Color Balance 1 copy 4"}, {"name": "Color Balance 1 copy 3", "bone": "root", "attachment": "Color Balance 1 copy 3"}, {"name": "Color Balance 1 copy 2", "bone": "root", "attachment": "Color Balance 1 copy 2"}, {"name": "BONUS", "bone": "JACKPOT", "attachment": "BONUS"}, {"name": "Color Balance 1 copy", "bone": "root", "attachment": "Color Balance 1 copy"}], "skins": {"default": {"BACK": {"BACK": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, 55.69, -68.96, 1, 1, 2, -55.31, -68.96, 1, 1, 2, -55.31, 63.04, 1, 1, 2, 55.69, 63.04, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 111, "height": 132}}, "BONUS": {"BONUS": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, 62, -27.95, 1, 1, 3, -62, -27.95, 1, 1, 3, -62, 26.05, 1, 1, 3, 62, 26.05, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 124, "height": 54}}, "BORDER": {"BORDER": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76, -75, -76, -75, -76, 75, 76, 75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 152, "height": 150}}, "Color Balance 1 copy": {"Color Balance 1 copy": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 6, -10.92, -14.35, 1, 1, 6, -13.33, 3.48, 1, 1, 6, 17.39, 7.64, 1, 1, 6, 19.8, -10.2, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 18, "height": 31}}, "Color Balance 1 copy 10": {"Color Balance 1 copy 10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 13, -14.08, -10.45, 1, 1, 13, -10.15, 10.18, 1, 1, 13, 12.44, 5.88, 1, 1, 13, 8.51, -14.75, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 21, "height": 23}}, "Color Balance 1 copy 11": {"Color Balance 1 copy 11": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 14, -12.85, -4.87, 1, 1, 14, -5.24, 13.63, 1, 1, 14, 21.58, 2.59, 1, 1, 14, 13.96, -15.91, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 20, "height": 29}}, "Color Balance 1 copy 12": {"Color Balance 1 copy 12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 15, -13.8, -5.16, 1, 1, 15, -7.68, 13.88, 1, 1, 15, 19.93, 5, 1, 1, 15, 13.81, -14.04, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 20, "height": 29}}, "Color Balance 1 copy 13": {"Color Balance 1 copy 13": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 16, -17.04, -10.75, 1, 1, 16, -15.4, 8.17, 1, 1, 16, 13.49, 5.66, 1, 1, 16, 11.85, -13.27, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 19, "height": 29}}, "Color Balance 1 copy 14": {"Color Balance 1 copy 14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 17, -11.85, -2.7, 1, 1, 17, -1.19, 14.23, 1, 1, 17, 19.96, 0.91, 1, 1, 17, 9.31, -16.02, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 20, "height": 25}}, "Color Balance 1 copy 15": {"Color Balance 1 copy 15": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 18, -12.59, -6.6, 1, 1, 18, -6.38, 11.35, 1, 1, 18, 17.24, 3.17, 1, 1, 18, 11.03, -14.78, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 19, "height": 25}}, "Color Balance 1 copy 16": {"Color Balance 1 copy 16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 19, -16.19, -11.58, 1, 1, 19, -15.29, 5.4, 1, 1, 19, 11.67, 3.98, 1, 1, 19, 10.78, -13, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 17, "height": 27}}, "Color Balance 1 copy 18": {"Color Balance 1 copy 18": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 4, -13.52, -36.28, 1, 1, 4, 5.02, 46.67, 1, 1, 4, 84.07, 29.01, 1, 1, 4, 65.54, -53.94, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 85, "height": 81}}, "Color Balance 1 copy 2": {"Color Balance 1 copy 2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 5, -25.73, -31.65, 1, 1, 5, -7.64, 47.31, 1, 1, 5, 68.39, 29.88, 1, 1, 5, 50.3, -49.07, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 81, "height": 78}}, "Color Balance 1 copy 3": {"Color Balance 1 copy 3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 6, 6.2, -14.06, 1, 1, 6, 3.25, 7.74, 1, 1, 6, 31.99, 11.63, 1, 1, 6, 34.94, -10.18, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 22, "height": 29}}, "Color Balance 1 copy 4": {"Color Balance 1 copy 4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 7, -7.29, -3.98, 1, 1, 7, 5, 14.27, 1, 1, 7, 22.42, 2.53, 1, 1, 7, 10.12, -15.71, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 22, "height": 21}}, "Color Balance 1 copy 5": {"Color Balance 1 copy 5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 8, -8.36, -12.27, 1, 1, 8, -4.25, 12.39, 1, 1, 8, 21.39, 8.12, 1, 1, 8, 17.28, -16.54, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 25, "height": 26}}, "Color Balance 1 copy 6": {"Color Balance 1 copy 6": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 9, -4.95, -13.03, 1, 1, 9, -2, 12.8, 1, 1, 9, 21.85, 10.07, 1, 1, 9, 18.89, -15.76, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 26, "height": 24}}, "Color Balance 1 copy 7": {"Color Balance 1 copy 7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 10, -12.29, -11.92, 1, 1, 10, -10.46, 11, 1, 1, 10, 11.47, 9.25, 1, 1, 10, 9.64, -13.68, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 23, "height": 22}}, "Color Balance 1 copy 8": {"Color Balance 1 copy 8": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 11, -12.45, -10.05, 1, 1, 11, -5.22, 14.92, 1, 1, 11, 20.71, 7.41, 1, 1, 11, 13.48, -17.56, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 26, "height": 27}}, "Color Balance 1 copy 9": {"Color Balance 1 copy 9": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 12, -11.2, -8.93, 1, 1, 12, -4.38, 13.03, 1, 1, 12, 18.54, 5.92, 1, 1, 12, 11.72, -16.05, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 23, "height": 24}}, "JACKPOT EFFECT": {"1": {"width": 169, "height": 154}, "2": {"width": 169, "height": 154}, "3": {"width": 169, "height": 154}, "4": {"width": 169, "height": 154}, "5": {"width": 169, "height": 154}, "6": {"width": 169, "height": 154}, "7": {"width": 169, "height": 154}, "8": {"width": 169, "height": 154}, "9": {"width": 169, "height": 154}, "10": {"width": 169, "height": 154}, "11": {"width": 169, "height": 154}, "12": {"width": 169, "height": 154}, "13": {"width": 169, "height": 154}, "14": {"width": 169, "height": 154}, "15": {"width": 169, "height": 154}, "16": {"width": 169, "height": 154}}}}, "animations": {"symbolFx": {"slots": {"JACKPOT EFFECT": {"attachment": [{"time": 0, "name": "1"}, {"time": 0.0333, "name": "2"}, {"time": 0.0667, "name": "3"}, {"time": 0.1, "name": "4"}, {"time": 0.1333, "name": "5"}, {"time": 0.1667, "name": "6"}, {"time": 0.2, "name": "7"}, {"time": 0.2333, "name": "8"}, {"time": 0.3, "name": "9"}, {"time": 0.3333, "name": "10"}, {"time": 0.3667, "name": "11"}, {"time": 0.4, "name": "12"}, {"time": 0.4333, "name": "13"}, {"time": 0.4667, "name": "14"}, {"time": 0.5, "name": "15"}, {"time": 0.5333, "name": "16"}, {"time": 0.5667, "name": "2"}, {"time": 0.6, "name": "3"}, {"time": 0.6333, "name": "4"}, {"time": 0.6667, "name": "5"}, {"time": 0.7, "name": "6"}, {"time": 0.7333, "name": "7"}, {"time": 0.7667, "name": "8"}, {"time": 0.8333, "name": "9"}, {"time": 0.8667, "name": "10"}, {"time": 0.9, "name": "11"}, {"time": 0.9333, "name": "12"}, {"time": 0.9667, "name": "13"}, {"time": 1, "name": "14"}, {"time": 1.0333, "name": "15"}, {"time": 1.0667, "name": "16"}, {"time": 1.1, "name": "2"}, {"time": 1.1333, "name": "3"}, {"time": 1.1667, "name": "4"}, {"time": 1.2, "name": "5"}, {"time": 1.2333, "name": "6"}, {"time": 1.2667, "name": "7"}, {"time": 1.3, "name": "8"}, {"time": 1.3667, "name": "9"}, {"time": 1.4, "name": "10"}, {"time": 1.4333, "name": "11"}, {"time": 1.4667, "name": "12"}, {"time": 1.5, "name": "13"}, {"time": 1.5333, "name": "14"}, {"time": 1.5667, "name": "15"}, {"time": 1.6, "name": "16"}, {"time": 1.6333, "name": "2"}, {"time": 1.6667, "name": "3"}, {"time": 1.7, "name": "4"}, {"time": 1.7333, "name": "5"}, {"time": 1.7667, "name": "6"}, {"time": 1.8, "name": "7"}, {"time": 1.8333, "name": "8"}, {"time": 1.9, "name": "9"}, {"time": 1.9333, "name": "10"}, {"time": 1.9667, "name": "11"}, {"time": 2, "name": "12"}, {"time": 2.0333, "name": "13"}, {"time": 2.0667, "name": "14"}, {"time": 2.1, "name": "15"}, {"time": 2.1333, "name": "16"}, {"time": 2.1667, "name": "2"}, {"time": 2.2, "name": "3"}, {"time": 2.2333, "name": "4"}, {"time": 2.2667, "name": "5"}, {"time": 2.3, "name": "6"}, {"time": 2.3333, "name": "7"}, {"time": 2.3667, "name": "8"}, {"time": 2.4333, "name": "9"}, {"time": 2.4667, "name": "10"}, {"time": 2.5, "name": "11"}, {"time": 2.5333, "name": "12"}, {"time": 2.5667, "name": "13"}, {"time": 2.6, "name": "14"}, {"time": 2.6333, "name": "15"}, {"time": 2.6667, "name": "16"}]}}, "bones": {"bone6": {"translate": [{"time": 0, "x": -14.22, "y": 0.16}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5, "x": -14.22, "y": 0.16, "curve": "stepped"}, {"time": 1.3333, "x": -14.22, "y": 0.16}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.8333, "x": -14.22, "y": 0.16, "curve": "stepped"}, {"time": 2.6667, "x": -14.22, "y": 0.16}]}, "bone5": {"translate": [{"time": 0, "x": -5.33, "y": -0.2}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5, "x": -5.33, "y": -0.2, "curve": "stepped"}, {"time": 1.3333, "x": -5.33, "y": -0.2}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.8333, "x": -5.33, "y": -0.2, "curve": "stepped"}, {"time": 2.6667, "x": -5.33, "y": -0.2}]}, "bone7": {"translate": [{"time": 0, "x": -2.78, "y": 0.12}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5, "x": -2.78, "y": 0.12, "curve": "stepped"}, {"time": 1.3333, "x": -2.78, "y": 0.12}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.8333, "x": -2.78, "y": 0.12, "curve": "stepped"}, {"time": 2.6667, "x": -2.78, "y": 0.12}]}, "bone8": {"rotate": [{"time": 0, "angle": -4.07}, {"time": 0.3333, "angle": 0}, {"time": 0.5, "angle": -4.07, "curve": "stepped"}, {"time": 1.3333, "angle": -4.07}, {"time": 1.6667, "angle": 0}, {"time": 1.8333, "angle": -4.07, "curve": "stepped"}, {"time": 2.6667, "angle": -4.07}], "translate": [{"time": 0, "x": -4.26, "y": -0.85}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5, "x": -4.26, "y": -0.85, "curve": "stepped"}, {"time": 1.3333, "x": -4.26, "y": -0.85}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.8333, "x": -4.26, "y": -0.85, "curve": "stepped"}, {"time": 2.6667, "x": -4.26, "y": -0.85}]}, "bone9": {"translate": [{"time": 0, "x": -12.46, "y": -0.31}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5, "x": -12.46, "y": -0.31, "curve": "stepped"}, {"time": 1.3333, "x": -12.46, "y": -0.31}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.8333, "x": -12.46, "y": -0.31, "curve": "stepped"}, {"time": 2.6667, "x": -12.46, "y": -0.31}]}, "bone10": {"translate": [{"time": 0, "x": -4.02, "y": 0.42}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5, "x": -4.02, "y": 0.42, "curve": "stepped"}, {"time": 1.3333, "x": -4.02, "y": 0.42}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.8333, "x": -4.02, "y": 0.42, "curve": "stepped"}, {"time": 2.6667, "x": -4.02, "y": 0.42}]}, "bone3": {"rotate": [{"time": 0, "angle": 10.04}, {"time": 0.3333, "angle": -0.11}, {"time": 0.5, "angle": 10.04, "curve": "stepped"}, {"time": 1.3333, "angle": 10.04}, {"time": 1.6667, "angle": -0.11}, {"time": 1.8333, "angle": 10.04, "curve": "stepped"}, {"time": 2.6667, "angle": 10.04}]}, "bone4": {"rotate": [{"time": 0, "angle": 53.58}, {"time": 0.3333, "angle": -28.59}, {"time": 0.5, "angle": 53.58, "curve": "stepped"}, {"time": 1.3333, "angle": 53.58}, {"time": 1.6667, "angle": -28.59}, {"time": 1.8333, "angle": 53.58, "curve": "stepped"}, {"time": 2.6667, "angle": 53.58}]}, "bone11": {"rotate": [{"time": 0, "angle": -9.28}, {"time": 0.3333, "angle": 0}, {"time": 0.5, "angle": -9.28, "curve": "stepped"}, {"time": 1.3333, "angle": -9.28}, {"time": 1.6667, "angle": 0}, {"time": 1.8333, "angle": -9.28, "curve": "stepped"}, {"time": 2.6667, "angle": -9.28}], "translate": [{"time": 0, "x": -5.12, "y": -1.94}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5, "x": -5.12, "y": -1.94, "curve": "stepped"}, {"time": 1.3333, "x": -5.12, "y": -1.94}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.8333, "x": -5.12, "y": -1.94, "curve": "stepped"}, {"time": 2.6667, "x": -5.12, "y": -1.94}]}, "bone12": {"translate": [{"time": 0, "x": -9.26, "y": -0.56}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5, "x": -9.26, "y": -0.56, "curve": "stepped"}, {"time": 1.3333, "x": -9.26, "y": -0.56}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.8333, "x": -9.26, "y": -0.56, "curve": "stepped"}, {"time": 2.6667, "x": -9.26, "y": -0.56}]}, "bone13": {"translate": [{"time": 0, "x": -6.42, "y": 1.53}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5, "x": -6.42, "y": 1.53, "curve": "stepped"}, {"time": 1.3333, "x": -6.42, "y": 1.53}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.8333, "x": -6.42, "y": 1.53, "curve": "stepped"}, {"time": 2.6667, "x": -6.42, "y": 1.53}]}, "bone14": {"rotate": [{"time": 0, "angle": -15.81}, {"time": 0.3333, "angle": 0}, {"time": 0.5, "angle": -15.81, "curve": "stepped"}, {"time": 1.3333, "angle": -15.81}, {"time": 1.6667, "angle": 0}, {"time": 1.8333, "angle": -15.81, "curve": "stepped"}, {"time": 2.6667, "angle": -15.81}], "translate": [{"time": 0, "x": -4.36, "y": -4.67}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5, "x": -4.36, "y": -4.67, "curve": "stepped"}, {"time": 1.3333, "x": -4.36, "y": -4.67}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.8333, "x": -4.36, "y": -4.67, "curve": "stepped"}, {"time": 2.6667, "x": -4.36, "y": -4.67}]}, "bone15": {"translate": [{"time": 0, "x": -6.66, "y": 1.44}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5, "x": -6.66, "y": 1.44, "curve": "stepped"}, {"time": 1.3333, "x": -6.66, "y": 1.44}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.8333, "x": -6.66, "y": 1.44, "curve": "stepped"}, {"time": 2.6667, "x": -6.66, "y": 1.44}]}, "bone16": {"translate": [{"time": 0, "x": -4.22, "y": 1.15}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5, "x": -4.22, "y": 1.15, "curve": "stepped"}, {"time": 1.3333, "x": -4.22, "y": 1.15}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.8333, "x": -4.22, "y": 1.15, "curve": "stepped"}, {"time": 2.6667, "x": -4.22, "y": 1.15}]}, "bone": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": 0, "y": 15.66}, {"time": 0.5, "x": 0, "y": -3.92}, {"time": 0.6667, "x": 0, "y": -6.27}, {"time": 0.8333, "x": 0, "y": -3.92}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 1.6667, "x": 0, "y": 15.66}, {"time": 1.8333, "x": 0, "y": -3.92}, {"time": 2, "x": 0, "y": -6.27}, {"time": 2.1667, "x": 0, "y": -3.92}, {"time": 2.6667, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -2.37}, {"time": 1.3333, "angle": 0}, {"time": 2, "angle": -2.37}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5, "x": -3.51, "y": 0.78}, {"time": 0.6667, "x": -4.25, "y": 0.95}, {"time": 0.8333, "x": -3.51, "y": 0.78}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.8333, "x": -3.51, "y": 0.78}, {"time": 2, "x": -4.25, "y": 0.95}, {"time": 2.1667, "x": -3.51, "y": 0.78}, {"time": 2.6667, "x": 0, "y": 0}]}, "JACKPOT": {"scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.3333, "x": 1.2, "y": 1.2}, {"time": 0.6667, "x": 1, "y": 1}, {"time": 1, "x": 1.2, "y": 1.2}, {"time": 1.3333, "x": 1, "y": 1}, {"time": 1.6667, "x": 1.2, "y": 1.2}, {"time": 2, "x": 1, "y": 1}, {"time": 2.3333, "x": 1.2, "y": 1.2}, {"time": 2.6667, "x": 1, "y": 1}]}}}, "idle": {"slots": {"JACKPOT EFFECT": {"attachment": [{"time": 0, "name": null}, {"time": 0.0667, "name": null}]}}, "bones": {"bone11": {"rotate": [{"time": 0, "angle": -9.28, "curve": "stepped"}, {"time": 0.0667, "angle": -9.28}], "translate": [{"time": 0, "x": -5.12, "y": -1.94, "curve": "stepped"}, {"time": 0.0667, "x": -5.12, "y": -1.94}]}, "bone13": {"translate": [{"time": 0, "x": -6.42, "y": 1.53, "curve": "stepped"}, {"time": 0.0667, "x": -6.42, "y": 1.53}]}, "bone9": {"translate": [{"time": 0, "x": -12.46, "y": -0.31, "curve": "stepped"}, {"time": 0.0667, "x": -12.46, "y": -0.31}]}, "bone14": {"rotate": [{"time": 0, "angle": -15.81, "curve": "stepped"}, {"time": 0.0667, "angle": -15.81}], "translate": [{"time": 0, "x": -4.36, "y": -4.67, "curve": "stepped"}, {"time": 0.0667, "x": -4.36, "y": -4.67}]}, "bone10": {"translate": [{"time": 0, "x": -4.02, "y": 0.42, "curve": "stepped"}, {"time": 0.0667, "x": -4.02, "y": 0.42}]}, "bone2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.0667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0667, "x": 0, "y": 0}]}, "bone5": {"translate": [{"time": 0, "x": -5.33, "y": -0.2, "curve": "stepped"}, {"time": 0.0667, "x": -5.33, "y": -0.2}]}, "bone12": {"translate": [{"time": 0, "x": -9.26, "y": -0.56, "curve": "stepped"}, {"time": 0.0667, "x": -9.26, "y": -0.56}]}, "bone8": {"rotate": [{"time": 0, "angle": -4.07, "curve": "stepped"}, {"time": 0.0667, "angle": -4.07}], "translate": [{"time": 0, "x": -4.26, "y": -0.85, "curve": "stepped"}, {"time": 0.0667, "x": -4.26, "y": -0.85}]}, "bone7": {"translate": [{"time": 0, "x": -2.78, "y": 0.12, "curve": "stepped"}, {"time": 0.0667, "x": -2.78, "y": 0.12}]}, "bone16": {"translate": [{"time": 0, "x": -4.22, "y": 1.15, "curve": "stepped"}, {"time": 0.0667, "x": -4.22, "y": 1.15}]}, "bone15": {"translate": [{"time": 0, "x": -6.66, "y": 1.44, "curve": "stepped"}, {"time": 0.0667, "x": -6.66, "y": 1.44}]}, "JACKPOT": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.0667, "x": 1, "y": 1}]}, "bone3": {"rotate": [{"time": 0, "angle": 10.04, "curve": "stepped"}, {"time": 0.0667, "angle": 10.04}]}, "bone": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0667, "x": 0, "y": 0}]}, "bone6": {"translate": [{"time": 0, "x": -14.22, "y": 0.16, "curve": "stepped"}, {"time": 0.0667, "x": -14.22, "y": 0.16}]}, "bone4": {"rotate": [{"time": 0, "angle": 53.58, "curve": "stepped"}, {"time": 0.0667, "angle": 53.58}]}}}}}