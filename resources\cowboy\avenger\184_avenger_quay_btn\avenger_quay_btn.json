{"skeleton": {"hash": "i7rctAKa/4IJ2I60CtkdaoqbsWM", "spine": "3.8.99", "x": -203.48, "width": 407, "height": 134, "images": "", "audio": "/Users/<USER>/Documents/Slot ADVENTURE/Slot AVENGER/BUTTON SPIN"}, "bones": [{"name": "root"}, {"name": "CENTER", "parent": "root"}, {"name": "BUTTON", "parent": "CENTER", "y": 83.77}, {"name": "LIGHT", "parent": "BUTTON", "x": -178.86}], "slots": [{"name": "BUTTON SPIN FIX/BACK", "bone": "root", "attachment": "BUTTON SPIN FIX/BACK"}, {"name": "BUTTON SPIN FIX/D DOWN", "bone": "root", "attachment": "BUTTON SPIN FIX/D DOWN"}, {"name": "BUTTON SPIN FIX/BUTTON BLUE", "bone": "root", "attachment": "BUTTON SPIN FIX/BUTTON BLUE"}, {"name": "BUTTON SPIN FIX/BUTTON RED", "bone": "root", "color": "ffffff00", "attachment": "BUTTON SPIN FIX/BUTTON RED"}, {"name": "BUTTON SPIN FIX/GRAY BUTTON", "bone": "root", "color": "ffffff00", "attachment": "BUTTON SPIN FIX/GRAY BUTTON"}, {"name": "BUTTON SPIN FIX/LIGHT EFFECT", "bone": "root", "color": "ffffff00", "attachment": "BUTTON SPIN FIX/LIGHT EFFECT"}, {"name": "BUTTON SPIN FIX/DECK UP", "bone": "root", "attachment": "BUTTON SPIN FIX/DECK UP"}, {"name": "BUTTON SPIN FIX/SPIN", "bone": "BUTTON", "attachment": "BUTTON SPIN FIX/SPIN"}, {"name": "BUTTON SPIN FIX/STOP", "bone": "BUTTON"}, {"name": "BUTTON SPIN FIX/HOLD TO AUTO", "bone": "BUTTON"}], "skins": {"default": {"BUTTON SPIN FIX/BACK": {"BUTTON SPIN FIX/BACK": {"type": "mesh", "hull": 4, "width": 255, "height": 77, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 1, 127.52, 14, 1, 1, 1, -127.48, 14, 1, 1, 1, -127.48, 91, 1, 1, 1, 127.52, 91, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "BUTTON SPIN FIX/BUTTON BLUE": {"BUTTON SPIN FIX/BUTTON BLUE": {"type": "mesh", "hull": 4, "width": 271, "height": 134, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, 135.52, -83.77, 1, 1, 2, -135.48, -83.77, 1, 1, 2, -135.48, 50.23, 1, 1, 2, 135.52, 50.23, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "BUTTON SPIN FIX/BUTTON RED": {"BUTTON SPIN FIX/BUTTON RED": {"type": "mesh", "hull": 4, "width": 271, "height": 134, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, 135.52, -83.77, 1, 1, 2, -135.48, -83.77, 1, 1, 2, -135.48, 50.23, 1, 1, 2, 135.52, 50.23, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "BUTTON SPIN FIX/D DOWN": {"BUTTON SPIN FIX/D DOWN": {"type": "mesh", "hull": 4, "width": 378, "height": 55, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 1, 188.52, 54, 1, 1, 1, -189.48, 54, 1, 1, 1, -189.48, 109, 1, 1, 1, 188.52, 109, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "BUTTON SPIN FIX/DECK UP": {"BUTTON SPIN FIX/DECK UP": {"type": "mesh", "hull": 4, "width": 407, "height": 57, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 1, 203.52, 0, 1, 1, 1, -203.48, 0, 1, 1, 1, -203.48, 57, 1, 1, 1, 203.52, 57, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "BUTTON SPIN FIX/GRAY BUTTON": {"BUTTON SPIN FIX/GRAY BUTTON": {"type": "mesh", "hull": 4, "width": 271, "height": 133, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [135.52, 0.5, -135.48, 0.5, -135.48, 133.5, 135.52, 133.5], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "BUTTON SPIN FIX/HOLD TO AUTO": {"BUTTON SPIN FIX/HOLD TO AUTO": {"type": "mesh", "hull": 4, "width": 152, "height": 18, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [76, -37.97, -76, -37.97, -76, -19.97, 76, -19.97], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "BUTTON SPIN FIX/LIGHT EFFECT": {"BUTTON SPIN FIX/LIGHT EFFECT": {"type": "mesh", "hull": 4, "width": 289, "height": 75, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 1, 144.52, 0, 1, 1, 1, -144.48, 0, 1, 1, 1, -144.48, 75, 1, 1, 1, 144.52, 75, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "BUTTON SPIN FIX/SPIN": {"BUTTON SPIN FIX/SPIN": {"type": "mesh", "hull": 4, "width": 126, "height": 36, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [63, -18.77, -63, -18.77, -63, 17.23, 63, 17.23], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "BUTTON SPIN FIX/STOP": {"BUTTON SPIN FIX/STOP": {"type": "mesh", "hull": 4, "width": 134, "height": 36, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [65.52, -18.77, -68.48, -18.77, -68.48, 17.23, 65.52, 17.23], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}, "animations": {"stop_disable": {"slots": {"BUTTON SPIN FIX/BUTTON BLUE": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"time": 0.1667, "color": "ffffff00"}]}, "BUTTON SPIN FIX/BUTTON RED": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"time": 0.1667, "color": "ffffff00"}]}, "BUTTON SPIN FIX/GRAY BUTTON": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0}, {"time": 0.1667, "color": "ffffffff"}]}, "BUTTON SPIN FIX/SPIN": {"attachment": [{"name": null, "time": 0}, {"time": 0.1667, "name": null}]}, "BUTTON SPIN FIX/STOP": {"attachment": [{"name": "BUTTON SPIN FIX/STOP", "time": 0}, {"time": 0.1667, "name": "BUTTON SPIN FIX/STOP"}]}}, "bones": {"LIGHT": {"translate": [{"curve": "stepped", "time": 0}, {"time": 0.1667}]}}}, "hold": {"slots": {"BUTTON SPIN FIX/BUTTON BLUE": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0}, {"time": 1, "color": "ffffffff"}, {"time": 2.1667, "color": "ffffff00"}]}, "BUTTON SPIN FIX/BUTTON RED": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"time": 1, "color": "ffffff00"}, {"time": 2.1667, "color": "ffffffff"}]}, "BUTTON SPIN FIX/HOLD TO AUTO": {"attachment": [{"name": "BUTTON SPIN FIX/HOLD TO AUTO", "time": 0}, {"time": 2.1667, "name": "BUTTON SPIN FIX/HOLD TO AUTO"}]}, "BUTTON SPIN FIX/LIGHT EFFECT": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0}, {"time": 2.1667, "color": "ffffffff"}]}}, "bones": {"BUTTON": {"translate": [{"y": -19.04, "curve": "stepped", "time": 0}, {"time": 2.1667, "y": -19.04}]}}}, "spin": {"slots": {"BUTTON SPIN FIX/HOLD TO AUTO": {"attachment": [{"name": "BUTTON SPIN FIX/HOLD TO AUTO", "time": 0}, {"time": 1.3333, "name": "BUTTON SPIN FIX/HOLD TO AUTO"}]}}, "bones": {"LIGHT": {"translate": [{"time": 0}, {"time": 0.5, "x": 368.48, "curve": "stepped"}, {"time": 1.3333, "x": 368.48}]}}}, "stop_pressed": {"slots": {"BUTTON SPIN FIX/BUTTON BLUE": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"time": 0.1, "color": "ffffff00"}]}, "BUTTON SPIN FIX/BUTTON RED": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0}, {"time": 0.1, "color": "ffffffff"}]}, "BUTTON SPIN FIX/LIGHT EFFECT": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0}, {"time": 0.1, "color": "ffffffff"}]}, "BUTTON SPIN FIX/SPIN": {"attachment": [{"name": null, "time": 0}]}, "BUTTON SPIN FIX/STOP": {"attachment": [{"name": "BUTTON SPIN FIX/STOP", "time": 0}]}}, "bones": {"BUTTON": {"translate": [{"y": -19.04, "curve": "stepped", "time": 0}, {"time": 0.1, "y": -19.04}]}}}, "stop": {"slots": {"BUTTON SPIN FIX/BUTTON BLUE": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"time": 1.3333, "color": "ffffff00"}]}, "BUTTON SPIN FIX/BUTTON RED": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0}, {"time": 1.3333, "color": "ffffffff"}]}, "BUTTON SPIN FIX/SPIN": {"attachment": [{"name": null, "time": 0}, {"time": 1.3333, "name": null}]}, "BUTTON SPIN FIX/STOP": {"attachment": [{"name": "BUTTON SPIN FIX/STOP", "time": 0}, {"time": 1.3333, "name": "BUTTON SPIN FIX/STOP"}]}}, "bones": {"LIGHT": {"translate": [{"time": 0}, {"time": 0.5, "x": 368.48, "curve": "stepped"}, {"time": 1.3333, "x": 368.48}]}}}}}