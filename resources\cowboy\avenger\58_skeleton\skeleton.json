{"skins": {"default": {"BG": {"BG": {"triangles": [1, 2, 3, 1, 3, 0], "path": "Levels 1 copy 4", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 2, 130.51, -206.68, 1, 1, 2, -127.49, -207.32, 1, 1, 2, -128.51, 205.68, 1, 1, 2, 129.49, 206.32, 1], "width": 258, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 413}}, "STORMBREAKER": {"STORMBREAKER": {"triangles": [1, 2, 3, 1, 3, 0], "path": "Levels 1 copy 2", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 6, 2.99, -24.21, 1, 1, 6, 8.89, 52.56, 1, 1, 6, 111.59, 44.66, 1, 1, 6, 105.68, -32.11, 1], "width": 77, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 103}}, "icon_vang3": {"icon_vang": {"scaleX": 0.7138, "scaleY": 0.7138, "width": 68, "height": 89}}, "LIGHT": {"LIGHT": {"triangles": [1, 2, 3, 1, 3, 0], "path": "Vector Smart Object7 copy 3", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 7, 133.05, -21.05, 1, 1, 7, -132.95, -21.71, 1, 1, 7, -133.03, 8.29, 1, 1, 7, 132.97, 8.95, 1], "width": 266, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 30}}, "icon_vang2": {"icon_vang": {"scaleX": 0.7138, "scaleY": 0.7138, "width": 68, "height": 89}}, "LIGHT4": {"LIGHT": {"triangles": [1, 2, 3, 1, 3, 0], "path": "Vector Smart Object7 copy 3", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 10, 133.05, -21.05, 1, 1, 10, -132.95, -21.71, 1, 1, 10, -133.03, 8.29, 1, 1, 10, 132.97, 8.95, 1], "width": 266, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 30}}, "HIGHLIGHT": {"HIGHLIGHT": {"triangles": [1, 2, 3, 1, 3, 0], "path": "Vector Smart Object51 copy", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 2, 121.57, 173.3, 1, 1, 2, -123.43, 172.69, 1, 1, 2, -123.5, 200.69, 1, 1, 2, 121.5, 201.3, 1], "width": 245, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 28}}, "LIGHT2": {"LIGHT": {"triangles": [1, 2, 3, 1, 3, 0], "path": "Vector Smart Object7 copy 3", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 8, 133.05, -21.05, 1, 1, 8, -132.95, -21.71, 1, 1, 8, -133.03, 8.29, 1, 1, 8, 132.97, 8.95, 1], "width": 266, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 30}}, "icon_vang": {"icon_vang": {"scaleX": 0.7138, "scaleY": 0.7138, "width": 68, "height": 89}}, "LIGHT3": {"LIGHT": {"triangles": [1, 2, 3, 1, 3, 0], "path": "Vector Smart Object7 copy 3", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 9, 133.05, -21.05, 1, 1, 9, -132.95, -21.71, 1, 1, 9, -133.03, 8.29, 1, 1, 9, 132.97, 8.95, 1], "width": 266, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 30}}, "BORDER DOWN": {"BORDER DOWN": {"triangles": [1, 2, 3, 1, 3, 0], "path": "175819afbf67050 copy 14", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 2, 132.03, -118.56, 1, 1, 2, -132.32, -119.22, 1, 1, 2, -132.4, -84.44, 1, 1, 2, 131.94, -83.78, 1], "width": 266, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 35}}, "LIGHT UP 2": {"LOGO-AVENEGERS-NEW-OUT-ANIM_0000_Layer-512": {"triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 11, 215.59, -147.05, 1, 1, 11, -208.1, -234.44, 1, 1, 11, -286.34, 144.88, 1, 1, 11, 137.35, 232.27, 1], "width": 296, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 222}}, "THOR": {"THOR": {"triangles": [1, 2, 3, 1, 3, 0], "path": "Levels 1", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 5, -25.97, -87.88, 1, 1, 5, -59.55, 57.29, 1, 1, 5, 121.67, 99.2, 1, 1, 5, 155.25, -45.97, 1], "width": 149, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 186}}, "AVENGERS": {"AVENGERS": {"triangles": [1, 2, 3, 1, 3, 0], "path": "Levels 5 copy 4", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 1, 104, -57.29, 1, 1, 1, -117, -57.29, 1, 1, 1, -117, 48.71, 1, 1, 1, 104, 48.71, 1], "width": 221, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 106}}, "BORDER": {"BORDER": {"rotation": -0.14, "width": 264, "height": 417}}, "DARK ZONE": {"DARK ZONE": {"triangles": [1, 2, 3, 1, 3, 0], "path": "Vector Smart Object", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 2, 127.51, -206.68, 1, 1, 2, -130.49, -207.32, 1, 1, 2, -130.77, -93.32, 1, 1, 2, 127.23, -92.68, 1], "width": 258, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 114}}, "IRON MAN": {"IRON MAN": {"triangles": [1, 2, 3, 1, 3, 0], "path": "Levels 1 copy 3", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 3, -89.89, -117.74, 1, 1, 3, -88.89, 100.26, 1, 1, 3, 214.11, 98.87, 1, 1, 3, 213.11, -119.13, 1], "width": 218, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 303}}, "CAPTAIN": {"CAPTAIN": {"triangles": [1, 2, 3, 1, 3, 0], "path": "Levels 1 copy", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 4, -71.05, -64.09, 1, 1, 4, -33.12, 77.93, 1, 1, 4, 181.36, 20.66, 1, 1, 4, 143.44, -121.37, 1], "width": 147, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 222}}}}, "skeleton": {"images": "", "x": -254.97, "width": 501.21, "y": -283.05, "spine": "3.7-from-3.8.99", "audio": "", "hash": "cuKa8j306ZCI6HQZ9q+R88rbTck", "height": 491.88}, "slots": [{"attachment": "BG", "name": "BG", "bone": "root"}, {"attachment": "IRON MAN", "name": "IRON MAN", "bone": "root"}, {"attachment": "STORMBREAKER", "name": "STORMBREAKER", "bone": "root"}, {"attachment": "CAPTAIN", "name": "CAPTAIN", "bone": "root"}, {"attachment": "THOR", "name": "THOR", "bone": "root"}, {"attachment": "DARK ZONE", "name": "DARK ZONE", "bone": "root"}, {"attachment": "HIGHLIGHT", "name": "HIGHLIGHT", "bone": "root"}, {"attachment": "LIGHT", "name": "LIGHT", "bone": "root"}, {"attachment": "LIGHT", "name": "LIGHT4", "bone": "root"}, {"attachment": "LIGHT", "name": "LIGHT3", "bone": "root"}, {"attachment": "LIGHT", "name": "LIGHT2", "bone": "root"}, {"attachment": "LOGO-AVENEGERS-NEW-OUT-ANIM_0000_Layer-512", "name": "LIGHT UP 2", "bone": "root"}, {"attachment": "BORDER DOWN", "name": "BORDER DOWN", "bone": "root"}, {"attachment": "BORDER", "name": "BORDER", "bone": "root"}, {"attachment": "icon_vang", "name": "icon_vang", "bone": "bone"}, {"attachment": "icon_vang", "name": "icon_vang2", "bone": "bone2"}, {"attachment": "icon_vang", "name": "icon_vang3", "bone": "bone3"}, {"attachment": "AVENGERS", "name": "AVENGERS", "bone": "root"}], "bones": [{"name": "root", "length": 6}, {"parent": "root", "name": "AVENGERS", "y": -36.71}, {"parent": "root", "rotation": -0.14, "name": "BONE CENTER"}, {"parent": "BONE CENTER", "rotation": 90.4, "name": "BONE CENTER2", "length": 204.92, "x": 14.92, "y": -26.62}, {"parent": "BONE CENTER", "rotation": 105.09, "name": "BONE CENTER3", "length": 171.24, "x": -46.08, "y": -70.01}, {"parent": "BONE CENTER", "rotation": 77.12, "name": "BONE CENTER4", "length": 141.83, "x": 40.4, "y": -69.79}, {"parent": "BONE CENTER4", "rotation": 17.42, "name": "BONE CENTER5", "length": 98.05, "x": 13.7, "y": -63.4}, {"parent": "BONE CENTER", "name": "LIGHT", "x": 0.25, "y": -97.4}, {"parent": "BONE CENTER", "name": "LIGHT2", "x": 0.25, "y": -97.4}, {"parent": "BONE CENTER", "name": "LIGHT3", "x": 0.25, "y": -97.4}, {"parent": "BONE CENTER", "name": "LIGHT4", "x": 0.25, "y": -97.4}, {"parent": "BONE CENTER", "name": "LIGHT UP 2", "x": 31.14, "y": -49.05}, {"scaleX": 0.9356, "parent": "root", "scaleY": 0.9356, "name": "bone", "x": -105.57, "y": -115.68}, {"scaleX": 0.9356, "parent": "root", "scaleY": 0.9356, "name": "bone2", "x": -105.57, "y": -149.49}, {"scaleX": 0.9356, "parent": "root", "scaleY": 0.9356, "name": "bone3", "x": -105.57, "y": -183.99}], "animations": {"animation": {"slots": {"LIGHT": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0}, {"color": "ffffffff", "time": 0.2667}, {"color": "ffffff00", "curve": "stepped", "time": 0.4667}, {"color": "ffffff00", "time": 0.9667}, {"color": "ffffffff", "curve": "stepped", "time": 1}, {"color": "ffffffff", "time": 1.2667}, {"color": "ffffff00", "curve": "stepped", "time": 1.4667}, {"color": "ffffff00", "time": 1.9667}]}, "LIGHT4": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0.4667}, {"color": "ffffffff", "time": 0.7}, {"color": "ffffff00", "curve": "stepped", "time": 0.9}, {"color": "ffffff00", "time": 0.9667}, {"color": "ffffffff", "curve": "stepped", "time": 1.4667}, {"color": "ffffffff", "time": 1.7}, {"color": "ffffff00", "curve": "stepped", "time": 1.9}, {"color": "ffffff00", "time": 1.9667}]}, "LIGHT2": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0.1667}, {"color": "ffffffff", "time": 0.4}, {"color": "ffffff00", "curve": "stepped", "time": 0.6}, {"color": "ffffff00", "time": 0.9667}, {"color": "ffffffff", "curve": "stepped", "time": 1.1667}, {"color": "ffffffff", "time": 1.4}, {"color": "ffffff00", "curve": "stepped", "time": 1.6}, {"color": "ffffff00", "time": 1.9667}]}, "LIGHT3": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0.3}, {"color": "ffffffff", "time": 0.5667}, {"color": "ffffff00", "curve": "stepped", "time": 0.7667}, {"color": "ffffff00", "time": 0.9667}, {"color": "ffffffff", "curve": "stepped", "time": 1.3}, {"color": "ffffffff", "time": 1.5667}, {"color": "ffffff00", "curve": "stepped", "time": 1.7667}, {"color": "ffffff00", "time": 1.9667}]}}, "bones": {"LIGHT": {"translate": [{"time": 0}, {"x": 0.05, "y": -18.22, "time": 0.4667}, {"time": 1}, {"x": 0.05, "y": -18.22, "time": 1.4667}]}, "BONE CENTER3": {"rotate": [{"angle": 0, "time": 0}, {"angle": 0.9, "time": 0.4}, {"angle": 0, "time": 0.9}, {"angle": -0.39, "time": 1.4}, {"angle": 0, "time": 2}], "scale": [{"x": 1, "y": 1, "time": 0}, {"x": 1.008, "y": 1, "time": 0.4}, {"x": 1, "y": 1, "time": 0.9}, {"x": 0.989, "y": 1, "time": 1.4}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": [0.25, 0, 0.75, 1], "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": -0.01, "y": 2.46, "time": 0.4}, {"curve": [0.25, 0, 0.75, 1], "time": 0.9}, {"curve": [0.25, 0, 0.75, 1], "x": 0.96, "y": -3.55, "time": 1.4}, {"time": 2}]}, "LIGHT4": {"translate": [{"time": 0.4333}, {"x": 0.05, "y": -18.22, "time": 0.9}, {"time": 1.4333}, {"x": 0.05, "y": -18.22, "time": 1.9}]}, "BONE CENTER4": {"rotate": [{"angle": 0, "time": 0}, {"angle": 0.76, "time": 0.5667}, {"angle": 0, "time": 1.0667}, {"angle": -0.52, "time": 1.5667}, {"angle": 0, "time": 2}], "scale": [{"x": 1, "y": 1, "time": 0}, {"x": 1.021, "y": 1, "time": 0.5667}, {"x": 1, "y": 1, "time": 1.0667}, {"x": 0.991, "y": 1, "time": 1.5667}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": [0.25, 0, 0.75, 1], "time": 0}, {"curve": [0.25, 0, 0.75, 1], "x": -0.01, "y": 2.71, "time": 0.5667}, {"curve": [0.25, 0, 0.75, 1], "time": 1.0667}, {"curve": [0.25, 0, 0.75, 1], "x": 0.01, "y": -2.51, "time": 1.5667}, {"time": 2}]}, "LIGHT2": {"translate": [{"time": 0.1333}, {"x": 0.05, "y": -18.22, "time": 0.6}, {"time": 1.1333}, {"x": 0.05, "y": -18.22, "time": 1.6}]}, "bone": {"rotate": [{"angle": 0, "time": 0}, {"angle": 3.69, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"x": 1, "y": 1, "time": 0}, {"x": 1.07, "y": 1.07, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"time": 0}, {"y": 2.59, "time": 1}, {"time": 2}]}, "BONE CENTER2": {"scale": [{"x": 1, "y": 1, "time": 0}, {"x": 0.992, "y": 1, "time": 0.5}, {"x": 1, "y": 1, "time": 1}, {"x": 1.008, "y": 1, "time": 1.5}, {"x": 1, "y": 1, "time": 2}], "translate": [{"curve": [0.25, 0, 0.625, 0.5], "time": 0}, {"curve": [0.375, 0.5, 0.75, 1], "x": 0.01, "y": -2.72, "time": 0.5}, {"curve": [0.25, 0, 0.625, 0.5], "time": 1}, {"curve": [0.375, 0.5, 0.75, 1], "x": -0.01, "y": 2.72, "time": 1.5}, {"time": 2}]}, "LIGHT3": {"translate": [{"time": 0.3}, {"x": 0.05, "y": -18.22, "time": 0.7667}, {"time": 1.3}, {"x": 0.05, "y": -18.22, "time": 1.7667}]}, "BONE CENTER5": {"rotate": [{"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 0}, {"curve": [0.25, 0, 0.75, 1], "angle": -1.44, "time": 0.5}, {"curve": [0.25, 0, 0.75, 1], "angle": 0, "time": 1}, {"curve": [0.25, 0, 0.75, 1], "angle": 2.44, "time": 1.5}, {"angle": 0, "time": 2}]}, "LIGHT UP 2": {"scale": [{"x": 1, "y": 1, "time": 0}, {"x": 1.1, "y": 1.1, "time": 0.3333}, {"x": 1, "y": 1, "time": 0.6667}, {"x": 1.1, "y": 1.1, "time": 1}, {"x": 1, "y": 1, "time": 1.3333}, {"x": 1.1, "y": 1.1, "time": 1.6667}, {"x": 1, "y": 1, "time": 2}]}, "AVENGERS": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 0.7333}, {"angle": 3.21, "time": 0.8}, {"angle": -3.9, "time": 0.8667}, {"angle": 3.21, "time": 0.9333}, {"angle": -3.9, "time": 1}, {"angle": 0, "time": 1.0667}, {"angle": 1.81, "time": 1.1}, {"angle": -0.94, "time": 1.1333}, {"curve": "stepped", "angle": 0, "time": 1.2}, {"angle": 0, "time": 2}]}, "bone3": {"rotate": [{"angle": 0, "time": 0}, {"angle": 3.69, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"x": 1, "y": 1, "time": 0}, {"x": 1.07, "y": 1.07, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"time": 0}, {"y": 2.59, "time": 1}, {"time": 2}]}, "bone2": {"rotate": [{"angle": 0, "time": 0}, {"angle": 3.69, "time": 1}, {"angle": 0, "time": 2}], "scale": [{"x": 1, "y": 1, "time": 0}, {"x": 1.07, "y": 1.07, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"time": 0}, {"y": 2.59, "time": 1}, {"time": 2}]}}}}}