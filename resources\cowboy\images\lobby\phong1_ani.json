{"skeleton": {"hash": "eDB+Svgj/l1GgFQ+Ldk7vDbamqY", "spine": "3.6.53", "width": 323, "height": 410}, "bones": [{"name": "root"}, {"name": "bg", "parent": "root", "length": 112.9, "rotation": -179.28, "x": -2.07, "y": 0.73}, {"name": "body", "parent": "bg", "length": 68.41, "rotation": -109.83, "x": -14.84, "y": 127.41}, {"name": "body2", "parent": "body", "x": 68.48, "y": 35.58}, {"name": "body3", "parent": "body", "x": 90.28, "y": -11.82}, {"name": "face", "parent": "body", "length": 40.98, "rotation": 5.69, "x": 133.01, "y": 48.24}, {"name": "hair_L", "parent": "face", "length": 59.46, "rotation": 161.36, "x": 34.82, "y": 31.86}, {"name": "hair_R", "parent": "face", "length": 43.23, "rotation": -154.34, "x": 24.54, "y": -17.17}, {"name": "hand_L", "parent": "body", "length": 82.15, "rotation": 162.17, "x": 87.67, "y": 87.47}, {"name": "hand_L2", "parent": "hand_L", "length": 55.73, "rotation": 76.01, "x": 82.05, "y": -0.71}, {"name": "hand_R", "parent": "body", "length": 95.94, "rotation": -141.69, "x": 115.6, "y": 11.5}, {"name": "hand_R1", "parent": "hand_R", "length": 66.71, "rotation": 159.05, "x": 101.77, "y": 2.82}, {"name": "hand_R2", "parent": "hand_R1", "length": 39.22, "rotation": -0.48, "x": 73.83, "y": 0.22}, {"name": "hat", "parent": "face", "length": 57.96, "rotation": -120.51, "x": 72.93, "y": 27.87}], "slots": [{"name": "bg", "bone": "bg", "attachment": "bg"}, {"name": "hair_R", "bone": "hair_R", "attachment": "hair_R"}, {"name": "hand_R", "bone": "hand_R", "attachment": "hand_R"}, {"name": "body", "bone": "body", "attachment": "body"}, {"name": "face", "bone": "face", "attachment": "face"}, {"name": "hand_L", "bone": "hand_L", "attachment": "hand_L"}, {"name": "hair_L", "bone": "hair_L", "attachment": "hair_L"}, {"name": "hat", "bone": "hat", "attachment": "hat"}, {"name": "hand_R1", "bone": "hand_R1", "attachment": "hand_R1"}], "skins": {"default": {"bg": {"bg": {"x": -2.06, "y": 0.76, "rotation": 179.28, "width": 323, "height": 410}}, "body": {"body": {"type": "mesh", "uvs": [0.54409, 0.02109, 0.64199, 0.07141, 0.72135, 0.11295, 0.84175, 0.21737, 0.92345, 0.30721, 0.96847, 0.35719, 0.97949, 0.42627, 0.96357, 0.47881, 0.90232, 0.60131, 0.85087, 0.66343, 0.86189, 0.68881, 0.85944, 0.70806, 0.90477, 0.81131, 0.88762, 0.90231, 0.54126, 0.91789, 0.04717, 0.89026, 0.03837, 0.85761, 0.14036, 0.79481, 0.16673, 0.75713, 0.14563, 0.72824, 0.20014, 0.65163, 0.19486, 0.61521, 0.07354, 0.467, 0.03661, 0.33136, 0, 0.17169, 0.05373, 0.08278, 0.22186, 0.04929, 0.25096, 0.02735, 0.30431, 0.02157, 0.34311, 0, 0.46399, 0, 0.5604, 0.03698, 0.65311, 0.09525, 0.75467, 0.14212], "triangles": [1, 31, 0, 2, 32, 1, 3, 33, 2, 26, 23, 24, 25, 26, 24, 22, 23, 26, 6, 7, 4, 6, 4, 5, 8, 3, 4, 8, 4, 7, 28, 21, 22, 28, 20, 21, 9, 3, 8, 11, 9, 10, 28, 29, 30, 30, 31, 28, 31, 30, 0, 26, 27, 28, 22, 26, 28, 31, 20, 28, 18, 19, 20, 31, 32, 20, 15, 16, 17, 13, 11, 12, 20, 9, 14, 1, 32, 31, 32, 33, 20, 2, 33, 32, 33, 9, 20, 3, 9, 33, 14, 9, 11, 14, 11, 13, 18, 20, 14, 17, 18, 14, 15, 17, 14], "vertices": [2, 3, 65.92, 10.11, 0.71599, 4, 44.12, 57.5, 0.28401, 2, 3, 61.78, -3.76, 0.63046, 4, 39.98, 43.63, 0.36954, 2, 3, 58.31, -15.04, 0.53576, 4, 36.5, 32.35, 0.46424, 2, 3, 46.46, -34.44, 0.27655, 4, 24.65, 12.95, 0.72345, 2, 3, 35.41, -48.64, 0.055, 4, 13.6, -1.25, 0.945, 3, 2, 97.72, -20.92, 0.00223, 3, 29.24, -56.49, 0.00205, 4, 7.44, -9.1, 0.99572, 2, 2, 87.19, -25.97, 0.147, 4, -3.1, -14.15, 0.853, 2, 2, 78.22, -27.05, 0.37704, 4, -12.06, -15.24, 0.62296, 2, 2, 56.37, -26.85, 0.87391, 4, -33.91, -15.03, 0.12609, 2, 2, 44.49, -24.43, 0.97622, 4, -45.8, -12.61, 0.02378, 2, 2, 40.89, -27.07, 0.9909, 4, -49.39, -15.26, 0.0091, 2, 2, 37.74, -27.85, 0.99581, 4, -52.54, -16.04, 0.00419, 1, 2, 23.13, -38.67, 1, 1, 2, 8.01, -41.73, 1, 2, 2, -8.07, -3.32, 0.99962, 3, -76.55, -38.89, 0.00038, 2, 2, -23.09, 54.23, 0.92768, 3, -91.57, 18.65, 0.07232, 2, 2, -18.26, 57.02, 0.92696, 3, -86.73, 21.44, 0.07304, 2, 2, -4.28, 48.91, 0.88902, 3, -72.76, 13.33, 0.11098, 2, 2, 2.74, 47.99, 0.82711, 3, -65.74, 12.42, 0.17289, 2, 2, 6.49, 51.97, 0.7817, 3, -61.98, 16.4, 0.2183, 2, 2, 20.8, 50.01, 0.59557, 3, -47.68, 14.43, 0.40443, 2, 2, 26.37, 52.61, 0.44012, 3, -42.11, 17.03, 0.55988, 3, 2, 45.13, 74.51, 0.05005, 3, -23.35, 38.94, 0.94981, 4, -45.15, 86.33, 0.00013, 3, 2, 65.21, 86.16, 0.00035, 3, -3.26, 50.59, 0.98332, 4, -25.07, 97.98, 0.01633, 2, 3, 20.64, 63.52, 0.94504, 4, -1.16, 110.91, 0.05496, 2, 3, 36.87, 62.32, 0.92473, 4, 15.06, 109.71, 0.07527, 2, 3, 48.79, 45.09, 0.86662, 4, 26.98, 92.49, 0.13338, 2, 3, 53.42, 43, 0.8486, 4, 31.61, 90.39, 0.1514, 2, 3, 56.43, 37.27, 0.82509, 4, 34.62, 84.66, 0.17491, 2, 3, 61.38, 34.06, 0.80488, 4, 39.57, 81.45, 0.19512, 2, 3, 66.13, 20.35, 0.75646, 4, 44.32, 67.74, 0.24354, 2, 3, 64.04, 7.39, 0.70207, 4, 42.24, 54.78, 0.29793, 3, 2, 126.91, 29.24, 1e-05, 3, 58.43, -6.33, 0.60456, 4, 36.63, 41.06, 0.39543, 2, 3, 54.98, -20.43, 0.47773, 4, 33.18, 26.97, 0.52227], "hull": 31}}, "face": {"face": {"x": 20.16, "y": 5.25, "rotation": -76.58, "width": 61, "height": 58}}, "hair_L": {"hair_L": {"x": 28.45, "y": 0.72, "rotation": 122.06, "width": 69, "height": 80}}, "hair_R": {"hair_R": {"x": 16.22, "y": -2.96, "rotation": 77.76, "width": 45, "height": 72}}, "hand_L": {"hand_L": {"type": "mesh", "uvs": [0.74441, 0.02415, 0.84229, 0.10726, 0.8732, 0.24578, 0.83181, 0.3598, 0.76828, 0.41269, 0.72535, 0.41395, 0.61202, 0.49077, 0.56222, 0.55625, 0.47808, 0.60536, 0.47979, 0.63306, 0.43858, 0.67713, 0.37333, 0.71743, 0.60858, 0.92773, 0.66525, 0.92773, 0.75111, 0.90506, 0.90222, 0.89876, 1, 0.95795, 1, 1, 0.46434, 1, 0.30293, 0.9315, 0.08777, 0.75744, 0.01904, 0.67184, 0.00332, 0.57299, 0.01458, 0.54083, 0.05617, 0.50857, 0.10161, 0.45603, 0.16801, 0.43552, 0.20471, 0.3958, 0.26237, 0.35607, 0.36198, 0.26252, 0.37945, 0.21126, 0.43712, 0.16769, 0.43834, 0.08474, 0.46696, 0.03262, 0.52461, 0, 0.68259, 0, 0.03541, 0.68064, 0.3735, 0.4926, 0.7633, 0.07578, 0.33296, 0.48133, 0.16916, 0.57319, 0.44906, 0.92578], "triangles": [17, 18, 13, 18, 12, 13, 13, 14, 17, 14, 15, 17, 15, 16, 17, 19, 41, 18, 18, 41, 12, 41, 19, 40, 41, 40, 11, 41, 11, 12, 21, 36, 20, 40, 19, 20, 20, 36, 40, 40, 39, 11, 11, 37, 10, 11, 39, 37, 40, 36, 22, 10, 8, 9, 10, 37, 8, 36, 21, 22, 24, 22, 23, 22, 24, 40, 7, 8, 6, 24, 25, 40, 25, 26, 40, 40, 26, 39, 6, 8, 37, 6, 37, 38, 5, 6, 38, 31, 39, 29, 31, 29, 30, 39, 38, 37, 26, 27, 39, 38, 39, 31, 38, 31, 32, 34, 38, 32, 34, 32, 33, 27, 28, 39, 39, 28, 29, 4, 5, 3, 38, 35, 0, 35, 38, 34, 2, 3, 5, 2, 38, 1, 38, 2, 5, 1, 38, 0], "vertices": [1, 8, -19.5, -1.56, 1, 1, 8, -16.36, 12.93, 1, 1, 8, -3.25, 26.61, 1, 1, 8, 11.51, 32.59, 1, 1, 8, 21, 31.85, 1, 1, 8, 23.69, 28.56, 1, 2, 8, 38.72, 25.82, 0.99549, 9, 15.27, 48.46, 0.00451, 2, 8, 48.75, 27.2, 0.96446, 9, 19.03, 39.06, 0.03554, 2, 8, 59.06, 24.52, 0.8281, 9, 18.93, 28.41, 0.1719, 2, 8, 61.94, 26.91, 0.73973, 9, 21.94, 26.19, 0.26027, 2, 8, 69.15, 27.22, 0.56423, 9, 23.99, 19.27, 0.43577, 2, 8, 77.38, 25.33, 0.19297, 9, 24.14, 10.83, 0.80703, 1, 9, 60.86, 11.02, 1, 1, 9, 64.39, 15.37, 1, 1, 9, 67.38, 23.9, 1, 1, 9, 76.14, 36.05, 1, 1, 9, 88.45, 38.53, 1, 1, 9, 92.86, 34.95, 1, 1, 9, 59.43, -6.22, 1, 1, 9, 42.18, -12.8, 1, 1, 9, 10.51, -14.53, 1, 1, 9, -2.75, -12.53, 1, 2, 8, 83.81, -15.67, 0.09144, 9, -14.09, -5.32, 0.90856, 2, 8, 79.67, -17.39, 0.19959, 9, -16.76, -1.72, 0.80041, 2, 8, 73.71, -16.71, 0.45839, 9, -17.54, 4.22, 0.54161, 2, 8, 65.34, -17.38, 0.76801, 9, -20.21, 12.18, 0.23199, 2, 8, 59.18, -13.79, 0.92446, 9, -18.22, 19.03, 0.07554, 2, 8, 52.71, -14.11, 0.98777, 9, -20.09, 25.23, 0.01223, 2, 8, 44.99, -12.77, 0.99997, 9, -20.66, 33.05, 3e-05, 1, 8, 28.97, -12.48, 1, 1, 8, 22.4, -15.26, 1, 1, 8, 14.27, -14.23, 1, 1, 8, 5.25, -20.86, 1, 1, 8, -2.08, -22.83, 1, 1, 8, -9.03, -20.91, 1, 1, 8, -18.43, -8.41, 1, 1, 9, -0.8, -12.02, 1, 2, 8, 53.11, 7.1, 0.99098, 9, 0.58, 29.97, 0.00902, 1, 8, -15.05, 4.13, 1, 2, 8, 54.31, 2.98, 0.99882, 9, -3.13, 27.81, 0.00118, 2, 8, 73.96, -2.53, 0.85604, 9, -3.72, 7.41, 0.14396, 1, 9, 50.7, -1.08, 1], "hull": 36}}, "hand_R": {"hand_R": {"x": 50.59, "y": 2.5, "rotation": 70.8, "width": 69, "height": 121}}, "hand_R1": {"hand_R1": {"type": "mesh", "uvs": [0.55238, 0.01103, 0.49505, 0.09723, 0.54321, 0.09808, 0.56901, 0.10316, 0.57417, 0.14341, 0.53805, 0.15739, 0.50881, 0.19255, 0.45664, 0.28016, 0.51304, 0.28503, 0.56286, 0.2966, 0.59388, 0.31698, 0.5873, 0.34013, 0.64934, 0.34985, 0.68786, 0.36461, 0.68572, 0.39518, 0.67042, 0.41022, 0.63586, 0.42275, 0.69634, 0.42629, 0.92194, 0.47051, 0.94384, 0.48375, 0.92422, 0.4961, 0.89806, 0.5396, 0.83048, 0.57933, 0.77162, 0.59436, 0.78034, 0.61316, 0.81522, 0.62014, 0.79114, 0.71334, 0.78426, 0.86416, 0.73954, 0.91584, 0.62258, 0.96244, 0.5641, 0.9743, 0.3061, 0.94295, 0.1685, 0.89805, 0.17882, 0.84975, 0.29922, 0.7269, 0.27213, 0.70865, 0.35205, 0.58672, 0.30765, 0.58125, 0.23439, 0.56375, 0.10785, 0.51017, 0.01585, 0.48351, 0, 0.43144, 0.09841, 0.39887, 0.08797, 0.38261, 0.12278, 0.28059, 0.14787, 0.25937, 0.29187, 0.06075, 0.24003, 0.03948, 0.33795, 0, 0.50408, 0, 0.65561, 0.59831, 0.48923, 0.58303], "triangles": [30, 31, 51, 51, 34, 36, 30, 51, 29, 29, 50, 28, 29, 51, 50, 32, 33, 31, 33, 34, 31, 51, 31, 34, 27, 28, 26, 24, 50, 23, 50, 26, 28, 24, 26, 50, 34, 35, 36, 26, 24, 25, 51, 16, 50, 50, 17, 23, 50, 16, 17, 23, 17, 22, 36, 37, 51, 37, 7, 51, 51, 11, 16, 11, 8, 9, 8, 11, 7, 11, 51, 7, 37, 38, 7, 21, 22, 18, 45, 38, 42, 42, 44, 45, 38, 45, 7, 21, 18, 20, 18, 22, 17, 40, 42, 39, 38, 39, 42, 20, 18, 19, 40, 41, 42, 16, 12, 15, 16, 11, 12, 15, 12, 14, 42, 43, 44, 14, 12, 13, 11, 9, 10, 45, 46, 7, 6, 7, 46, 6, 46, 1, 1, 46, 48, 6, 1, 5, 4, 2, 3, 4, 5, 2, 5, 1, 2, 1, 49, 0, 1, 48, 49, 46, 47, 48], "vertices": [1, 12, 112.29, 2.61, 1, 1, 12, 94.69, 4.79, 1, 1, 12, 94.61, 2.38, 1, 1, 12, 93.63, 1.05, 1, 1, 12, 85.48, 0.47, 1, 1, 12, 82.57, 2.16, 1, 1, 12, 75.38, 3.35, 1, 1, 12, 57.51, 5.26, 1, 1, 12, 56.63, 2.41, 1, 1, 12, 54.38, -0.17, 1, 1, 12, 50.31, -1.89, 1, 1, 12, 45.6, -1.74, 1, 1, 12, 43.75, -4.92, 1, 1, 12, 40.83, -6.96, 1, 1, 12, 34.62, -7.09, 1, 1, 12, 31.54, -6.45, 1, 1, 12, 28.93, -4.82, 1, 1, 12, 28.33, -7.87, 1, 1, 12, 19.8, -19.49, 1, 2, 11, 90.82, -20.61, 0.00113, 12, 17.16, -20.69, 0.99887, 2, 11, 88.28, -19.71, 0.00588, 12, 14.62, -19.81, 0.99412, 2, 11, 79.42, -18.67, 0.08483, 12, 5.74, -18.84, 0.91517, 2, 11, 71.25, -15.54, 0.34033, 12, -2.45, -15.78, 0.65967, 2, 11, 68.11, -12.69, 0.60286, 12, -5.61, -12.96, 0.39714, 2, 11, 64.31, -13.25, 0.87433, 12, -9.41, -13.54, 0.12567, 2, 11, 62.95, -15.03, 0.92894, 12, -10.76, -15.34, 0.07106, 1, 11, 44, -14.41, 1, 1, 11, 13.39, -15, 1, 1, 11, 2.83, -13.08, 1, 1, 11, -6.8, -7.53, 1, 1, 11, -9.3, -4.68, 1, 1, 11, -3.33, 8.41, 1, 1, 11, 5.57, 15.57, 1, 1, 11, 15.39, 15.35, 1, 1, 11, 40.5, 10.09, 1, 1, 11, 44.16, 11.56, 1, 2, 11, 69.02, 8.32, 0.46968, 12, -4.88, 8.06, 0.53032, 2, 11, 70.06, 10.58, 0.25297, 12, -3.86, 10.33, 0.74703, 2, 11, 73.5, 14.35, 0.05999, 12, -0.45, 14.12, 0.94001, 1, 12, 10.17, 20.87, 1, 1, 12, 15.4, 25.68, 1, 1, 12, 25.93, 26.88, 1, 1, 12, 32.73, 22.22, 1, 1, 12, 36.01, 22.87, 1, 1, 12, 56.77, 21.94, 1, 1, 12, 61.13, 20.85, 1, 1, 12, 101.69, 15.23, 1, 1, 12, 105.91, 17.99, 1, 1, 12, 114.11, 13.41, 1, 1, 12, 114.43, 5.11, 1, 2, 11, 67.13, -6.92, 0.76382, 12, -6.64, -7.19, 0.23618, 2, 11, 69.98, 1.49, 0.51302, 12, -3.86, 1.24, 0.48698], "hull": 50}}, "hat": {"hat": {"x": 20.41, "y": -7.06, "rotation": 43.93, "width": 115, "height": 78}}}}, "animations": {"animation": {"bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1}]}, "bg": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1}]}, "body": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1}]}, "body2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.7333, "x": 1.42, "y": 1.87}, {"time": 1.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1}]}, "body3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.7333, "x": 1.67, "y": -1.84}, {"time": 1.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1}]}, "hand_R": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.7333, "angle": -5.79}, {"time": 1.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1}]}, "hand_R1": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.7333, "angle": -5.79}, {"time": 1.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1}]}, "hand_R2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.7333, "angle": -5.79}, {"time": 1.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1}]}, "hand_L": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.7333, "angle": -2.42}, {"time": 1.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1}]}, "hand_L2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.7333, "angle": -2.42}, {"time": 1.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1}]}, "face": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.7333, "angle": 3.32}, {"time": 1.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1}]}, "hair_L": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.7333, "angle": -4.13}, {"time": 1.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": 1, "y": 1.081}, {"time": 1.5, "x": 1, "y": 1}]}, "hair_R": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.7333, "angle": 5.46}, {"time": 1.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1}]}, "hat": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.7333, "angle": -3.5}, {"time": 1.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.7333, "x": -1.45, "y": -2.15}, {"time": 1.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1}]}}}}}