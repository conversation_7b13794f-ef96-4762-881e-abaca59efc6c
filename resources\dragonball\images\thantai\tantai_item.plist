<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>thantai_symbol_1_5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{116,113}</string>
                <key>spriteSourceSize</key>
                <string>{116,113}</string>
                <key>textureRect</key>
                <string>{{577,1},{116,113}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>thantai_symbol_1_6.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{115,107}</string>
                <key>spriteSourceSize</key>
                <string>{115,107}</string>
                <key>textureRect</key>
                <string>{{692,1},{115,107}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>thantai_symbol_2_0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{132,125}</string>
                <key>spriteSourceSize</key>
                <string>{132,125}</string>
                <key>textureRect</key>
                <string>{{299,1},{132,125}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>thantai_symbol_2_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{142,124}</string>
                <key>spriteSourceSize</key>
                <string>{142,124}</string>
                <key>textureRect</key>
                <string>{{433,1},{142,124}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>thantai_symbol_2_9.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{150,125}</string>
                <key>spriteSourceSize</key>
                <string>{150,125}</string>
                <key>textureRect</key>
                <string>{{1,1},{150,125}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>thantai_symbol_3_10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{144,125}</string>
                <key>spriteSourceSize</key>
                <string>{144,125}</string>
                <key>textureRect</key>
                <string>{{153,1},{144,125}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>thantai_symbol_3_4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{111,103}</string>
                <key>spriteSourceSize</key>
                <string>{111,103}</string>
                <key>textureRect</key>
                <string>{{801,1},{111,103}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>tantai_item.png</string>
            <key>size</key>
            <string>{905,127}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:307f66a6cee2ce7510cd03a4c39ab9e0:3b1b5bfa6f09a7543d06a5fe652add72:61bda74b1bce575f6f0d838db998152a$</string>
            <key>textureFileName</key>
            <string>tantai_item.png</string>
        </dict>
    </dict>
</plist>
