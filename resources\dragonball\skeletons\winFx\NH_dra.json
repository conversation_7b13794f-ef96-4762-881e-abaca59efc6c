{"skeleton": {"hash": "Aium/DjKDezFXMt0/TSjVRQEpx4", "spine": "3.6.53", "width": 1280, "height": 740.33}, "bones": [{"name": "root"}, {"name": "nh_dra", "parent": "root", "length": 91.13, "rotation": 88, "x": -23.67, "y": -100.01}, {"name": "1", "parent": "nh_dra", "x": -76.99, "y": 141.43}, {"name": "2", "parent": "nh_dra", "x": -138.02, "y": 68.3}, {"name": "3", "parent": "nh_dra", "x": -68.77, "y": -63.86}, {"name": "4", "parent": "nh_dra", "x": -107.64, "y": -134.09}, {"name": "5", "parent": "nh_dra", "x": -100.08, "y": 286.85}, {"name": "6", "parent": "nh_dra", "x": -50.81, "y": 28.96}, {"name": "7", "parent": "nh_dra", "x": -31.69, "y": -245.88}, {"name": "bg_NH", "parent": "root", "length": 387.59, "x": -218.53, "y": -111.66}, {"name": "nh_dra2", "parent": "nh_dra", "length": 174.85, "rotation": -0.08, "x": 91.13}, {"name": "nh_dra3", "parent": "nh_dra2", "length": 120.05, "rotation": 13.79, "x": 194.48, "y": 43.1}, {"name": "nh_dra4", "parent": "nh_dra2", "length": 107.22, "rotation": -14.56, "x": 197.36, "y": -36.27}, {"name": "nh_dra6", "parent": "nh_dra", "length": 133.62, "rotation": -70, "x": 0.33, "y": -282.91}, {"name": "nh_dra7", "parent": "nh_dra6", "length": 126.89, "rotation": 78.71, "x": 133.62}, {"name": "nh_dra8", "parent": "nh_dra7", "length": 81.05, "rotation": 65.8, "x": 126.89}, {"name": "nh_dra11", "parent": "nh_dra", "length": 76.38, "rotation": 108.93, "x": 166.35, "y": 91.63}, {"name": "nh_dra12", "parent": "nh_dra", "length": 138.67, "rotation": 109.33, "x": 29.23, "y": 103.81}, {"name": "nh_dra13", "parent": "nh_dra12", "length": 104.28, "rotation": -51.99, "x": 138.67}, {"name": "nh_dra14", "parent": "nh_dra13", "length": 163.62, "rotation": -76.59, "x": 104.28}, {"name": "nh_dra16", "parent": "nh_dra", "length": 72.49, "rotation": 117.07, "x": 4.26, "y": 303.21}, {"name": "nh_dra17", "parent": "nh_dra16", "length": 80.66, "rotation": 61.17, "x": 73.44, "y": -0.45}, {"name": "nh_dra18", "parent": "nh_dra2", "length": 31.24, "rotation": -107.66, "x": 87.18, "y": -36.9}, {"name": "nh_dra19", "parent": "nh_dra18", "length": 42.71, "rotation": -28.12, "x": 31.24}, {"name": "nh_dra20", "parent": "nh_dra19", "length": 39.84, "rotation": 18.39, "x": 43.21, "y": 0.56}, {"name": "nh_dra21", "parent": "nh_dra20", "length": 40.89, "rotation": 34.77, "x": 39.84}, {"name": "nh_dra22", "parent": "nh_dra21", "length": 42.53, "rotation": 47.64, "x": 40.89}, {"name": "nh_dra23", "parent": "nh_dra22", "length": 58.99, "rotation": 32.67, "x": 42.53}, {"name": "nh_dra24", "parent": "nh_dra23", "length": 64.86, "rotation": 3.07, "x": 58.99}, {"name": "nh_dra25", "parent": "nh_dra24", "length": 58, "rotation": -24.9, "x": 66.42, "y": -2.23}, {"name": "nh_dra26", "parent": "nh_dra25", "length": 68.66, "rotation": -32.7, "x": 58}, {"name": "nh_dra27", "parent": "nh_dra26", "length": 70.27, "rotation": -27.38, "x": 69.96, "y": -0.78}, {"name": "nh_dra28", "parent": "nh_dra27", "length": 62.76, "rotation": -32.41, "x": 70.27}, {"name": "nh_dra29", "parent": "nh_dra28", "length": 55, "rotation": -32.61, "x": 62.76}, {"name": "nh_dra30", "parent": "nh_dra2", "length": 31.39, "rotation": 100.61, "x": 89.31, "y": 30.86}, {"name": "nh_dra31", "parent": "nh_dra30", "length": 39.42, "rotation": 27.66, "x": 31.39}, {"name": "nh_dra32", "parent": "nh_dra31", "length": 47.19, "rotation": 8.81, "x": 39.42}, {"name": "nh_dra33", "parent": "nh_dra32", "length": 56.78, "rotation": -34.77, "x": 47.19}, {"name": "nh_dra34", "parent": "nh_dra33", "length": 55.72, "rotation": -23.11, "x": 56.78}, {"name": "nh_dra35", "parent": "nh_dra34", "length": 52.48, "rotation": -53.59, "x": 55.72}, {"name": "nh_dra36", "parent": "nh_dra35", "length": 59.07, "rotation": -33.37, "x": 52.48}, {"name": "nh_dra37", "parent": "nh_dra36", "length": 71.93, "rotation": -10.36, "x": 59.07}, {"name": "nh_dra38", "parent": "nh_dra37", "length": 63.81, "rotation": 33.55, "x": 71.93}, {"name": "nh_dra39", "parent": "nh_dra38", "length": 53.63, "rotation": 45.25, "x": 63.81}, {"name": "nh_dra40", "parent": "nh_dra39", "length": 55.75, "rotation": 40.2, "x": 53.63}, {"name": "nh_dra41", "parent": "nh_dra40", "length": 69.69, "rotation": 28.96, "x": 55.75}, {"name": "nh_t", "parent": "bg_NH", "length": 191.29, "rotation": 0.63, "x": 92.43, "y": 107.12}], "slots": [{"name": "nh_dra", "bone": "nh_dra", "attachment": "nh_dra"}, {"name": "nh_t", "bone": "nh_t", "attachment": "nh_t"}, {"name": "1", "bone": "1", "attachment": "1"}, {"name": "2", "bone": "2", "attachment": "2"}, {"name": "3", "bone": "3", "attachment": "3"}, {"name": "4", "bone": "4", "attachment": "4"}, {"name": "5", "bone": "5", "attachment": "5"}, {"name": "6", "bone": "6", "attachment": "6"}, {"name": "7", "bone": "7", "attachment": "7"}, {"name": "bg_NH", "bone": "bg_NH", "attachment": "bg_NH"}], "skins": {"default": {"1": {"1": {"x": 1.47, "y": -5.5, "rotation": -88, "width": 118, "height": 120}}, "2": {"2": {"x": 3.8, "y": 0.54, "rotation": -88, "width": 118, "height": 120}}, "3": {"3": {"x": 0.43, "y": -6.09, "rotation": -88, "width": 118, "height": 120}}, "4": {"4": {"x": -3.4, "y": -2.38, "rotation": -88, "width": 118, "height": 120}}, "5": {"5": {"x": 3.33, "y": -1.57, "rotation": -88, "width": 118, "height": 120}}, "6": {"6": {"x": 8.28, "y": -6.95, "rotation": -88, "width": 118, "height": 120}}, "7": {"7": {"x": -1.39, "y": -2.94, "rotation": -88, "width": 118, "height": 120}}, "bg_NH": {"bg_NH": {"x": 171.38, "y": -1.59, "width": 644, "height": 117}}, "nh_dra": {"nh_dra": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0, 0.51322, 0.51282, 0.52691, 0.51913, 0.54922, 0.55339, 0.56646, 0.58854, 0.58573, 0.59936, 0.61311, 0.59215, 0.62578, 0.57141, 0.63542, 0.53356, 0.63846, 0.48939, 0.63745, 0.44252, 0.63593, 0.39745, 0.64505, 0.33795, 0.66635, 0.29379, 0.68764, 0.26584, 0.71908, 0.24421, 0.75356, 0.2361, 0.78702, 0.25503, 0.81034, 0.28748, 0.82708, 0.32984, 0.83215, 0.365, 0.82353, 0.36319, 0.81237, 0.31722, 0.78905, 0.27576, 0.75964, 0.25683, 0.7353, 0.25413, 0.70285, 0.27035, 0.67598, 0.3046, 0.65469, 0.34517, 0.64607, 0.40466, 0.64657, 0.45604, 0.64505, 0.51913, 0.63897, 0.55609, 0.63238, 0.58133, 0.61717, 0.60657, 0.59491, 0.61555, 0.57053, 0.60656, 0.55176, 0.58313, 0.53615, 0.5467, 0.52124, 0.52696, 0.51206, 0.52242, 0.45719, 0.52077, 0.45985, 0.50775, 0.44821, 0.50479, 0.42824, 0.51603, 0.41226, 0.5397, 0.39629, 0.56573, 0.38531, 0.58171, 0.369, 0.59472, 0.35236, 0.59768, 0.33072, 0.59946, 0.31308, 0.59295, 0.30077, 0.58112, 0.29312, 0.56396, 0.28946, 0.53852, 0.28746, 0.50006, 0.29245, 0.45982, 0.29378, 0.4474, 0.3031, 0.40717, 0.30843, 0.37995, 0.31142, 0.34445, 0.31184, 0.31093, 0.30672, 0.27716, 0.29224, 0.25035, 0.27294, 0.23212, 0.2458, 0.22462, 0.21957, 0.23212, 0.19484, 0.25839, 0.17765, 0.28252, 0.17162, 0.29699, 0.17554, 0.30718, 0.18308, 0.29485, 0.19996, 0.27126, 0.22439, 0.2466, 0.24671, 0.24124, 0.27234, 0.24981, 0.28802, 0.27126, 0.29918, 0.29806, 0.30159, 0.33774, 0.29767, 0.37955, 0.28742, 0.43209, 0.27897, 0.49964, 0.28018, 0.5361, 0.28569, 0.57238, 0.29596, 0.59457, 0.31232, 0.60936, 0.33589, 0.61824, 0.36446, 0.61479, 0.38248, 0.60394, 0.39801, 0.5852, 0.41244, 0.55956, 0.42825, 0.53442, 0.43989, 0.52406, 0.4496, 0.52012], "triangles": [38, 1, 89, 38, 0, 1, 78, 82, 75, 75, 76, 78, 83, 74, 75, 83, 73, 74, 70, 2, 69, 70, 69, 76, 75, 70, 76, 71, 2, 70, 71, 70, 75, 74, 71, 75, 72, 2, 71, 72, 71, 74, 73, 72, 74, 1, 2, 72, 1, 72, 73, 85, 1, 73, 77, 68, 67, 69, 68, 77, 76, 69, 77, 76, 77, 78, 67, 68, 2, 69, 2, 68, 78, 77, 67, 17, 66, 67, 78, 67, 66, 79, 78, 66, 79, 66, 65, 79, 81, 78, 3, 67, 2, 3, 18, 67, 16, 65, 66, 80, 79, 65, 15, 64, 65, 80, 65, 64, 81, 80, 64, 63, 81, 64, 63, 64, 15, 81, 79, 80, 82, 81, 63, 62, 82, 63, 46, 62, 63, 61, 82, 62, 81, 82, 78, 83, 82, 61, 60, 83, 61, 82, 83, 75, 61, 62, 46, 47, 60, 61, 59, 83, 60, 84, 83, 59, 58, 84, 59, 47, 59, 60, 59, 48, 58, 84, 73, 83, 85, 84, 58, 85, 73, 84, 48, 57, 58, 85, 58, 57, 56, 86, 85, 56, 85, 57, 86, 56, 55, 87, 86, 55, 1, 85, 86, 1, 86, 87, 57, 50, 56, 56, 53, 55, 1, 88, 89, 1, 87, 88, 87, 55, 54, 54, 55, 53, 88, 87, 54, 88, 54, 53, 89, 53, 52, 88, 53, 89, 56, 50, 52, 50, 51, 52, 56, 52, 53, 91, 51, 50, 90, 52, 51, 90, 51, 91, 89, 52, 90, 39, 90, 91, 38, 89, 90, 48, 49, 57, 93, 49, 48, 57, 49, 50, 92, 49, 93, 50, 49, 92, 91, 50, 92, 92, 93, 40, 39, 91, 92, 94, 47, 95, 59, 47, 48, 94, 48, 47, 93, 48, 94, 44, 93, 94, 15, 46, 63, 47, 61, 46, 95, 47, 46, 44, 94, 95, 45, 46, 14, 13, 4, 45, 96, 46, 45, 44, 96, 45, 44, 45, 43, 96, 95, 46, 41, 93, 44, 95, 96, 44, 22, 25, 21, 3, 22, 21, 24, 25, 22, 23, 22, 3, 24, 22, 23, 24, 30, 25, 23, 3, 0, 23, 34, 24, 0, 36, 23, 20, 19, 3, 21, 26, 20, 27, 20, 26, 3, 21, 20, 26, 21, 25, 29, 27, 26, 30, 26, 25, 19, 18, 3, 28, 18, 19, 27, 19, 20, 28, 19, 27, 29, 18, 28, 29, 28, 27, 18, 17, 67, 29, 17, 18, 30, 17, 29, 30, 29, 26, 17, 16, 66, 30, 16, 17, 15, 16, 30, 24, 31, 30, 24, 32, 31, 16, 15, 65, 31, 15, 30, 14, 15, 31, 32, 14, 31, 13, 14, 32, 24, 33, 32, 13, 32, 33, 12, 13, 33, 13, 45, 14, 15, 14, 46, 12, 5, 13, 34, 12, 33, 34, 11, 12, 12, 11, 6, 35, 11, 34, 23, 35, 34, 12, 6, 5, 34, 33, 24, 10, 6, 11, 35, 10, 11, 36, 10, 35, 9, 10, 36, 23, 36, 35, 9, 7, 10, 37, 9, 36, 37, 36, 0, 39, 8, 38, 38, 8, 9, 38, 9, 37, 38, 37, 0, 38, 90, 39, 40, 39, 92, 8, 7, 9, 39, 7, 8, 40, 7, 39, 7, 40, 6, 41, 6, 40, 10, 7, 6, 6, 41, 5, 41, 40, 93, 13, 5, 4, 43, 45, 4, 43, 4, 5, 42, 43, 5, 42, 5, 41, 43, 42, 41, 41, 44, 43], "vertices": [3, 33, 566.16, -30.02, 0.02372, 13, 316.15, -272.96, 0.53441, 14, -231.93, -232.45, 0.44187, 3, 45, 557.45, 262.86, 0.0229, 19, -312.72, 190.97, 0.03136, 21, 171.12, -231.89, 0.94574, 2, 44, 306.02, -211.63, 0.1523, 45, 116.52, -306.34, 0.8477, 4, 31, 381.5, 159, 0.04207, 32, 177.53, 301.05, 0.29452, 33, -65.55, 315.45, 0.66332, 14, 483.14, -316.58, 9e-05, 2, 12, -103.66, -29.03, 0.00141, 22, 0.57, 2.86, 0.99859, 2, 12, -102.99, -47.12, 0.00345, 22, 18.6, 4.5, 0.99655, 2, 23, 24.17, 2.64, 0.99944, 25, -42.48, 39.21, 0.00056, 5, 1, 128.7, -108.44, 0.00647, 10, 37.73, -108.39, 0.00328, 22, 83.12, -25.43, 0.00013, 23, 57.74, 2.02, 0.00155, 24, 14.25, -3.19, 0.98857, 3, 23, 80.06, 15.09, 0.00067, 24, 39.55, 2.16, 0.48707, 25, 0.99, 1.94, 0.51226, 2, 25, 36.37, 3.88, 0.62269, 26, -0.18, 5.95, 0.37731, 1, 26, 21.51, 2.01, 1, 2, 27, 11.52, 2.83, 0.99984, 28, -47.25, 5.37, 0.00016, 2, 12, -41.58, -177.78, 3e-05, 27, 43.52, 1.39, 0.99997, 2, 12, -9.61, -166.87, 0.00154, 28, 18.34, 4.29, 0.99846, 2, 12, 20.92, -155.71, 0.00613, 28, 50.74, 7, 0.99387, 3, 12, 65.3, -154.63, 0.0198, 28, 93.83, -3.69, 0.15636, 29, 25.48, 10.22, 0.82383, 4, 12, 103.58, -171.64, 0.0062, 28, 126.26, -30.2, 0.00196, 29, 66.05, -0.18, 0.24202, 30, 6.87, 4.2, 0.74981, 3, 12, 130.66, -191.99, 0.00816, 28, 147.01, -56.98, 0.0019, 30, 40.6, 7.36, 0.98994, 3, 12, 157.11, -226.08, 0.00214, 30, 83.11, -0.06, 0.10883, 31, 11.35, 6.68, 0.88903, 3, 12, 175.35, -266.69, 0.00156, 31, 55.77, 9.67, 0.94602, 32, -17.43, 0.39, 0.05242, 2, 12, 174.56, -311.64, 9e-05, 32, 26.69, 9.02, 0.99991, 3, 32, 64.1, 2.88, 0.4775, 33, -0.43, 3.14, 0.52249, 14, 305.94, -51.3, 1e-05, 2, 33, 36.61, 7.3, 0.9996, 14, 273.14, -69, 0.0004, 5, 29, 113.87, -213.21, 9e-05, 30, 162.19, -149.25, 2e-05, 33, 61.93, 0.85, 0.97457, 14, 247.25, -72.49, 0.01531, 15, -16.79, -139.49, 0.01001, 7, 29, 110.15, -202.74, 0.00183, 30, 153.41, -142.44, 0.0014, 31, 139.25, -87.41, 0.00138, 32, 105.09, -36.82, 0.01068, 33, 55.5, -8.21, 0.95375, 14, 249.82, -61.68, 0.00119, 15, -5.88, -137.42, 0.02976, 6, 29, 133.53, -175.3, 0.00089, 30, 158.26, -106.72, 0.00075, 31, 127.13, -53.46, 0.00104, 32, 76.66, -14.65, 0.03715, 33, 19.61, -4.85, 0.95376, 15, 17.69, -164.7, 0.00642, 5, 29, 147.12, -135.32, 0.00066, 30, 148.1, -65.74, 0.00078, 31, 99.26, -21.75, 0.00566, 32, 36.14, -2.82, 0.99053, 15, 55.13, -184.2, 0.00236, 6, 29, 142.7, -95.54, 0.0013, 30, 122.89, -34.64, 0.00282, 31, 62.58, -5.73, 0.68769, 32, -3.42, -8.96, 0.30226, 33, -50.93, -43.21, 0.00255, 15, 95.13, -185.89, 0.00339, 5, 29, 130.68, -66.73, 0.00095, 31, 31.62, -1.78, 0.99756, 32, -31.68, -22.22, 0.00015, 33, -67.58, -69.61, 0.00025, 15, 125.43, -178.38, 0.0011, 6, 29, 101.84, -34.64, 0.00656, 30, 55.61, -5.46, 0.94233, 31, -10.59, -10.76, 0.04724, 32, -62.49, -52.42, 0.00042, 33, -77.26, -111.66, 0.0004, 15, 161.54, -154.76, 0.00305, 6, 29, 64.52, -14.68, 0.16142, 30, 13.42, -8.84, 0.82556, 31, -46.5, -33.16, 0.00354, 32, -80.81, -90.58, 0.00095, 33, -72.13, -153.67, 0.00058, 15, 186.93, -120.9, 0.00797, 6, 29, 26.27, -3.14, 0.99163, 30, -25, -19.78, 0.00615, 31, -75.58, -60.55, 0.00012, 32, -90.68, -129.29, 0.00015, 33, -59.59, -191.6, 9e-05, 15, 204.15, -84.85, 0.00186, 7, 28, 45.85, -6.1, 0.86029, 29, -17.03, -12.18, 0.13196, 30, -56.56, -50.78, 0.00257, 31, -89.35, -102.58, 4e-05, 32, -79.77, -172.16, 0.00021, 33, -27.3, -221.83, 5e-05, 15, 201.8, -40.68, 0.00488, 7, 27, 68.26, -7.12, 0.14478, 28, 8.88, -7.61, 0.84096, 29, -49.93, -29.11, 0.00783, 30, -75.1, -82.8, 0.00092, 31, -91.08, -139.54, 4e-05, 32, -61.42, -204.29, 0.00014, 15, 190.07, -5.59, 0.00534, 2, 27, 22.82, -8.67, 0.99975, 29, -91.54, -47.44, 0.00025, 5, 26, 40.48, -4.8, 0.58535, 27, -4.31, -2.94, 0.4126, 13, -24.56, 173.84, 0.00145, 14, 139.49, 189.16, 0.0002, 15, 177.71, 66.04, 0.00041, 5, 26, 20.9, -9.03, 0.98408, 27, -23.08, 4.07, 0.01087, 13, -38.2, 159.16, 0.00422, 14, 122.43, 199.66, 0.00036, 15, 180.29, 85.91, 0.00046, 3, 1, 117.99, -173.75, 9e-05, 25, 40.58, -6.94, 0.63077, 26, -5.34, -4.45, 0.36914, 3, 1, 110.54, -145.51, 0.0071, 24, 55.52, -2.2, 0.03964, 25, 11.62, -10.75, 0.95326, 5, 1, 115.91, -114.09, 0.02806, 10, 24.95, -114.06, 0.00825, 23, 70.86, -2.82, 0.01024, 24, 25.17, -11.93, 0.92641, 25, -18.86, -1.43, 0.02703, 5, 1, 131.93, -89.49, 0.02272, 10, 40.94, -89.43, 0.02339, 22, 64.09, -28.12, 0.00727, 23, 42.23, -9.32, 0.55493, 24, -4.05, -9.07, 0.39169, 4, 1, 157.46, -68.61, 0.00338, 10, 66.43, -68.51, 0.01641, 22, 36.42, -10.18, 0.24737, 23, 9.37, -6.54, 0.73284, 4, 1, 170.99, -49.04, 0.00048, 10, 79.94, -48.93, 0.00717, 22, 13.66, -3.25, 0.9921, 24, -57.96, 6.92, 0.00025, 5, 1, 173.85, -37.19, 0.00026, 10, 82.78, -37.07, 0.00177, 22, 1.5, -4.14, 0.99655, 23, -24.28, -17.67, 0.00134, 24, -69.79, 3.99, 9e-05, 5, 10, 81.41, 33.16, 0.07615, 34, 3.71, 7.34, 0.91418, 35, -21.11, 19.35, 0.00746, 36, -56.86, 28.39, 0.00207, 17, -114.22, -111.87, 0.00014, 3, 11, -103.69, 12.06, 0.00083, 34, -1.05, -1.43, 0.99894, 16, -63.44, 5.12, 0.00023, 3, 11, -98.58, 26.22, 0.00528, 34, 13.38, -5.75, 0.98713, 16, -49.8, -1.26, 0.0076, 4, 11, -101.32, 52.89, 0.00309, 34, 39.86, -1.53, 0.11872, 35, 6.79, -5.29, 0.80202, 16, -22.99, -0.95, 0.07617, 3, 35, 33.35, -3.61, 0.56791, 36, -6.55, -2.64, 0.20495, 16, 1.54, 9.39, 0.22714, 2, 36, 21.16, -3.85, 0.94542, 16, 26.56, 21.37, 0.05458, 3, 36, 39.24, -5.65, 0.73039, 37, -3.31, -9.18, 0.20403, 16, 43.35, 28.28, 0.06558, 2, 37, 18.9, -3.66, 0.96841, 16, 66.05, 31.17, 0.03159, 3, 37, 40.24, -5.35, 0.91661, 39, -31.67, -62.17, 0.00099, 16, 87.05, 27.01, 0.08241, 4, 37, 67.71, -9.02, 0.09213, 38, 13.6, -4, 0.86125, 39, -21.78, -36.27, 0.01193, 16, 113.91, 20.17, 0.03469, 3, 38, 36.65, -3.54, 0.96745, 39, -8.47, -17.45, 0.0253, 16, 134.15, 9.11, 0.00725, 3, 38, 53.92, -8.33, 0.39339, 39, 5.63, -6.4, 0.59985, 16, 146.75, -3.63, 0.00677, 4, 38, 66.22, -18.19, 0.01329, 39, 20.87, -2.35, 0.97892, 40, -25.1, -19.34, 0.00197, 16, 152.52, -18.3, 0.00582, 3, 39, 39.54, -5.36, 0.85105, 40, -7.86, -11.59, 0.14255, 16, 151.67, -37.19, 0.0064, 3, 38, 83.53, -61.43, 0.00043, 40, 18.99, -4.35, 0.99748, 16, 146.05, -64.42, 0.00209, 2, 40, 48.62, -5.7, 0.8168, 41, -9.25, -7.48, 0.1832, 2, 40, 57.73, -5.85, 0.52008, 41, -0.26, -5.99, 0.47992, 2, 11, 7.93, 193.83, 0.00119, 41, 31.04, -7.19, 0.99881, 2, 11, 25.74, 183.18, 0.00293, 41, 51.78, -6.82, 0.99707, 3, 11, 49.99, 174.24, 0.00696, 41, 77.1, -1.6, 0.34819, 42, 3.42, -4.18, 0.64485, 2, 11, 73.51, 168.82, 0.01818, 42, 26.78, -10.28, 0.98182, 3, 11, 98.66, 170.31, 0.01959, 42, 51.95, -9.52, 0.93775, 43, -15.11, 1.72, 0.04266, 3, 11, 121.31, 184.53, 0.00604, 42, 75.01, 4.05, 0.10911, 43, 10.76, -5.11, 0.88485, 3, 11, 139.18, 206.06, 0.00141, 43, 38.69, -3.44, 0.97283, 44, -13.64, 7.02, 0.02576, 2, 11, 151.52, 238.98, 0.0004, 44, 19.86, -3.64, 0.9996, 4, 11, 153.04, 272.96, 9e-05, 43, 97.01, 32.14, 0.00102, 44, 53.88, -3.44, 0.57692, 45, -3.31, -2.1, 0.42198, 1, 45, 33.3, -6.54, 1, 2, 44, 112.45, 24.2, 0.00208, 45, 61.33, -6.28, 0.99792, 3, 45, 73.81, -2.77, 0.99972, 20, -54.33, -327.79, 8e-05, 21, -348.38, -45.92, 0.0002, 7, 40, 131.36, 160.52, 0.00062, 41, 42.24, 170.91, 0.00335, 42, 69.72, 158.84, 0.00027, 45, 74.34, 6.1, 0.98132, 19, 233.63, 162.36, 0.01391, 20, -55.77, -319.02, 0.00019, 21, -341.39, -40.43, 0.00033, 7, 40, 141.75, 152.53, 0.00078, 41, 53.9, 164.92, 0.00394, 42, 76.13, 147.4, 0.00103, 43, 113.36, 95.02, 0.00029, 44, 106.95, 34.03, 0.00185, 45, 61.27, 5, 0.9868, 19, 245.4, 156.59, 0.00532, 7, 40, 162.18, 134.13, 0.0004, 41, 77.31, 150.49, 0.00216, 42, 87.66, 122.44, 0.00091, 43, 103.75, 69.26, 0.00031, 44, 82.98, 20.56, 0.0019, 45, 33.78, 4.81, 0.99245, 19, 269.06, 142.6, 0.00187, 6, 40, 185.01, 106.36, 0.00065, 41, 104.76, 127.28, 0.00417, 42, 97.71, 87.92, 0.00338, 44, 49.37, 7.8, 0.60813, 45, -1.81, 9.92, 0.38197, 19, 296.95, 119.89, 0.0017, 7, 40, 193.7, 78.88, 0.00051, 41, 118.25, 101.8, 0.0037, 42, 94.86, 59.24, 0.00524, 43, 63.94, 19.65, 0.01798, 44, 20.55, 8.36, 0.95939, 45, -26.76, 24.36, 0.01217, 19, 310.9, 94.67, 0.00101, 6, 40, 193.21, 45.49, 0.00049, 41, 123.78, 68.88, 0.00404, 43, 32.71, 7.83, 0.93738, 44, -10.93, 19.49, 0.05045, 45, -48.91, 49.33, 0.00684, 19, 317.03, 61.85, 0.00081, 7, 40, 181.43, 23.08, 0.00041, 41, 116.22, 44.71, 0.00326, 42, 61.61, 12.78, 0.37252, 43, 7.53, 10.56, 0.6099, 44, -28.4, 37.82, 0.00856, 45, -55.32, 73.83, 0.00468, 19, 309.92, 37.55, 0.00067, 6, 40, 164.85, 5.71, 0.00033, 41, 103.03, 24.64, 0.00128, 42, 39.54, 3.35, 0.99245, 44, -39.56, 59.08, 0.00284, 45, -54.79, 97.83, 0.00249, 19, 297.1, 17.24, 0.0006, 7, 40, 137.23, -2.21, 0.00077, 41, 77.29, 11.88, 0.17077, 42, 11.03, 6.94, 0.81879, 43, -32.22, 42.37, 0.00278, 44, -38.24, 87.78, 0.002, 45, -39.74, 122.31, 0.00329, 19, 271.6, 4.01, 0.0016, 7, 40, 106.71, -2.41, 0.00304, 41, 47.3, 6.2, 0.97732, 42, -17.1, 18.78, 0.00573, 43, -43.62, 70.68, 0.00235, 44, -28.67, 116.76, 0.00204, 45, -17.34, 143.04, 0.00468, 19, 241.72, -2.22, 0.00483, 6, 40, 67.2, 4.06, 0.22424, 41, 7.27, 5.46, 0.75022, 43, -52.12, 109.81, 0.00021, 44, -9.91, 152.13, 0.00061, 45, 16.2, 164.9, 0.00417, 19, 201.71, -3.7, 0.02055, 2, 40, 17.43, 6.41, 0.81647, 19, 152.46, -11.26, 0.18353, 3, 39, 45.88, 4.83, 0.66971, 40, -8.17, 0.4, 0.24639, 19, 128.56, -22.21, 0.0839, 3, 39, 19.11, 8.79, 0.90361, 18, 91.83, -112.73, 0.00246, 19, 106.77, -38.26, 0.09393, 5, 38, 57.77, 2.48, 0.4802, 39, -0.78, 3.12, 0.50013, 17, 98.62, -122.61, 0.00082, 18, 71.94, -107.06, 0.00569, 19, 96.64, -56.29, 0.01316, 4, 38, 34.98, 8.2, 0.96904, 17, 81.8, -106.2, 0.01192, 18, 48.65, -110.21, 0.01696, 19, 94.3, -79.67, 0.00207, 5, 37, 63.61, 5.47, 0.31234, 38, 4.14, 7.71, 0.64337, 17, 54.9, -91.12, 0.03334, 18, 20.2, -122.11, 0.01051, 19, 99.29, -110.11, 0.00044, 3, 37, 27.18, 9.52, 0.96426, 17, 19.26, -82.6, 0.03538, 18, -8.46, -144.95, 0.00036, 7, 1, 109.41, 126.52, 0.00151, 10, 18.09, 126.55, 0.00117, 34, 107.16, 52.37, 0.00021, 35, 91.43, 11.21, 0.00282, 36, 53.11, 3.11, 0.27024, 37, 3.09, 5.93, 0.7034, 17, -5.1, -83.18, 0.02065, 5, 1, 123.58, 107.13, 0.00514, 10, 32.3, 107.17, 0.00733, 34, 85.5, 41.98, 0.00319, 36, 29.51, 7.63, 0.97491, 17, -28.09, -90.14, 0.00943, 6, 1, 142.68, 89.32, 0.00303, 10, 51.41, 89.4, 0.00947, 34, 64.51, 26.46, 0.00574, 35, 41.62, 8.06, 0.39088, 36, 3.41, 7.63, 0.58803, 17, -51.21, -102.26, 0.00285, 6, 1, 161.48, 69.73, 0.00085, 10, 70.24, 69.83, 0.01544, 34, 41.81, 11.56, 0.08511, 35, 14.6, 5.4, 0.88164, 36, -23.7, 9.14, 0.01613, 17, -75.92, -113.52, 0.00084, 6, 1, 169.45, 55.09, 0.00027, 10, 78.24, 55.2, 0.01809, 34, 25.96, 6.4, 0.74025, 35, -1.84, 8.19, 0.23704, 36, -39.52, 14.41, 0.00387, 17, -92.38, -116.19, 0.00049, 4, 10, 81.52, 42.89, 0.01914, 34, 13.25, 5.43, 0.9787, 36, -50.31, 21.19, 0.00199, 17, -105.08, -115.2, 0.00017], "hull": 4}}, "nh_t": {"nh_t": {"x": 87.15, "y": 17.84, "rotation": -0.63, "width": 570, "height": 150}}}}, "animations": {"animation": {"bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1, "x": 27.29, "y": -0.04}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra6": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1, "x": -4.52, "y": 43.43}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra7": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": -37.6}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra8": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra11": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra12": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra13": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1, "x": -29.12, "y": 7.51}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra14": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": 22.95}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1, "x": -54.9, "y": 18.65}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra16": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": 4.7}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra17": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": 6.54}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1, "x": -54.82, "y": -20.17}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "7": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": -27.67, "y": 16.45}, {"time": 1.3333, "x": -1.38, "y": 39.62}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": -1.06, "y": -25.19}, {"time": 1.3333, "x": 5.85, "y": 26.33}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 7.15, "y": -38.45}, {"time": 1.3333, "x": 18.11, "y": -47.75}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "6": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": -27.98, "y": -30}, {"time": 1.3333, "x": 0.42, "y": -39.66}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 17.4, "y": 0.61}, {"time": 1.3333, "x": 32.3, "y": -10.48}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": -16.2, "y": 20.72}, {"time": 1.3333, "x": -10.64, "y": 27.69}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0.88, "y": -25.13}, {"time": 1.3333, "x": 20.24, "y": -25.42}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "bg_NH": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_t": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.5, "x": -46.23, "y": 24.12}, {"time": 1, "x": 0, "y": 0}, {"time": 1.5, "x": -46.23, "y": 24.12}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.5, "x": 1.406, "y": 1.406}, {"time": 1, "x": 1, "y": 1}, {"time": 1.5, "x": 1.406, "y": 1.406}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra38": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -6.37}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra39": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -6.37}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra40": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -6.37}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra41": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -6.37}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra25": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra26": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra27": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 5.82}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra28": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 5.82}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra29": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 5.82}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra24": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.3333, "angle": -9.8}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra37": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.3333, "angle": 11.84}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra35": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.3333, "angle": 8.44}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra23": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.3333, "angle": -5.11}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra18": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra19": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra20": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra21": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra22": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra30": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra31": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra32": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra33": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra34": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "nh_dra36": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}}}}}