{"skeleton": {"hash": "GHuMz2FANca8E30ujggk4Uv+OXs", "spine": "3.6.53", "width": 154, "height": 162}, "bones": [{"name": "root"}, {"name": "g", "parent": "root", "length": 15.4, "rotation": 91.12, "x": 0.21, "y": 4.92}, {"name": "ng", "parent": "g", "length": 31.42, "rotation": -2.78, "x": 23.54, "y": -1.07}, {"name": "ng4", "parent": "ng", "length": 19.04, "rotation": 179.77, "x": 56.44, "y": 1.61}, {"name": "ng2", "parent": "ng4", "rotation": -179.77, "x": 47.66, "y": -25.65}, {"name": "ng3", "parent": "ng4", "rotation": -179.77, "x": 46.53, "y": 27.49}, {"name": "t", "parent": "g", "length": 21.39, "rotation": -145.23, "x": 148, "y": -13.69}, {"name": "t2", "parent": "t", "length": 18.01, "rotation": -35.89, "x": 21.39}, {"name": "t3", "parent": "t2", "length": 15.75, "rotation": 2.49, "x": 18.01}, {"name": "t4", "parent": "t3", "length": 10.34, "rotation": 38.93, "x": 15.75}], "slots": [{"name": "g", "bone": "g", "attachment": "g"}, {"name": "ng", "bone": "ng", "attachment": "ng"}, {"name": "t", "bone": "t", "attachment": "t"}], "skins": {"default": {"g": {"g": {"x": 77.03, "y": -3.3, "rotation": -91.12, "width": 154, "height": 162}}, "ng": {"ng": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 2, -7.71, -46.22, 0.00125, 5, -17.52, -20.52, 0.99875, 2, 2, -10.25, 41.75, 0.01545, 4, -19.13, 14.3, 0.98455, 2, 3, -15.11, -42.56, 0.80183, 4, 62.84, 16.66, 0.19817, 2, 3, -17.99, 45.39, 0.76283, 5, 64.45, -18.15, 0.23717], "hull": 4}}, "t": {"t": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [3, 6, 64.95, -15.04, 0, 8, 26.65, 12.21, 0.0398, 9, 16.15, 2.65, 0.9602, 3, 6, 36.81, -53.92, 0.11086, 7, 44.1, -34.65, 0.27827, 9, -15.61, -33.35, 0.61087, 3, 6, -20.71, -12.3, 0.95936, 7, -26.9, -34.65, 0.02396, 9, -68.84, 13.63, 0.01668, 3, 6, 7.43, 26.59, 0.86524, 7, -26.9, 13.35, 0.13333, 8, -44.28, 15.29, 0.00143], "hull": 4}}}}, "animations": {"animation": {"bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "g": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ng": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1, "x": 3.49, "y": -0.07, "curve": [0.699, 0.27, 0.496, 0.46]}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.942, "y": 1, "curve": [0.699, 0.27, 0.496, 0.46]}, {"time": 2, "x": 1, "y": 1}]}, "ng2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1, "x": 0.17, "y": -5, "curve": [0.699, 0.27, 0.496, 0.46]}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ng3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1, "x": -0.21, "y": 6.1, "curve": [0.699, 0.27, 0.496, 0.46]}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "t": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 1}, {"time": 1, "angle": 0}, {"time": 1.5, "angle": -2.4}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "t2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 1}, {"time": 1, "angle": 0}, {"time": 1.5, "angle": -2.4}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "t3": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 6.21}, {"time": 1, "angle": 0}, {"time": 1.5, "angle": 10}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "t4": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 1}, {"time": 1, "angle": 0}, {"time": 1.5, "angle": -20.83}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ng4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}}}}}