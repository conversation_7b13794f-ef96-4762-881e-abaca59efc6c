<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>bonus.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{178,155}</string>
                <key>spriteSourceSize</key>
                <string>{178,155}</string>
                <key>textureRect</key>
                <string>{{0,0},{178,155}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>freespin.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{178,146}</string>
                <key>spriteSourceSize</key>
                <string>{178,146}</string>
                <key>textureRect</key>
                <string>{{0,155},{178,146}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>item_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{178,146}</string>
                <key>spriteSourceSize</key>
                <string>{178,146}</string>
                <key>textureRect</key>
                <string>{{0,301},{178,146}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>item_2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{178,155}</string>
                <key>spriteSourceSize</key>
                <string>{178,155}</string>
                <key>textureRect</key>
                <string>{{0,447},{178,155}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>item_3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{178,146}</string>
                <key>spriteSourceSize</key>
                <string>{178,146}</string>
                <key>textureRect</key>
                <string>{{0,602},{178,146}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>item_4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{178,146}</string>
                <key>spriteSourceSize</key>
                <string>{178,146}</string>
                <key>textureRect</key>
                <string>{{0,748},{178,146}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>jackpot.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{178,146}</string>
                <key>spriteSourceSize</key>
                <string>{178,146}</string>
                <key>textureRect</key>
                <string>{{0,894},{178,146}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>itemblur.png</string>
            <key>size</key>
            <string>{178,1040}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:2681bc393799297b5d5587b04f11a24d:dcde77b06bd4b7031b9fc2caccc4f961:82c2199236a1974eed20cd3bdf9693b1$</string>
            <key>textureFileName</key>
            <string>itemblur.png</string>
        </dict>
    </dict>
</plist>
