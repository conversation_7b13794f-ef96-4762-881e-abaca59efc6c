{"skeleton": {"hash": "6ZyvIUlbbS1uvrOjH59IJVnmyyE", "spine": "3.7.94", "width": 1289.86, "height": 720, "images": "", "audio": ""}, "bones": [{"name": "root"}, {"name": "ball", "parent": "root", "color": "ff0000ff"}, {"name": "stop", "parent": "root", "color": "e6ff00ff"}, {"name": "effect", "parent": "root", "scaleX": 2.002, "scaleY": 2.002, "color": "0036ffff"}, {"name": "hover", "parent": "root", "scaleX": 0.764, "scaleY": 0.764, "color": "00ff0eff"}, {"name": "hover2", "parent": "root", "scaleX": 0.764, "scaleY": 0.764, "color": "00ff0eff"}, {"name": "hover3", "parent": "root", "scaleX": 0.764, "scaleY": 0.764, "color": "00ff0eff"}, {"name": "fire_effect", "parent": "root", "x": 20.62, "y": 10.3, "scaleX": -1.659, "scaleY": 1.659, "color": "0051ffff"}], "slots": [{"name": "Image/Background copy", "bone": "root"}, {"name": "Image/red_effect", "bone": "effect", "color": "ffffff00", "attachment": "Image/red_effect"}, {"name": "Image/cau3", "bone": "ball", "attachment": "Image/cau3"}, {"name": "Image/cau3_off", "bone": "ball"}, {"name": "Image/cau3_red", "bone": "ball"}, {"name": "Image/stop", "bone": "stop", "color": "ffffff00", "attachment": "Image/stop"}, {"name": "Image/hover1", "bone": "hover", "color": "ffffff00", "attachment": "Image/hover1"}, {"name": "Image/hover2", "bone": "hover2", "color": "ffffff00", "attachment": "Image/hover1"}, {"name": "Image/hover3", "bone": "hover3", "color": "ffffff00", "attachment": "Image/hover1"}], "skins": {"default": {"Image/cau3": {"Image/cau3": {"x": 0.5, "y": 0.5, "width": 151, "height": 151}}, "Image/cau3_off": {"Image/cau3_off": {"x": 0.5, "y": 0.5, "width": 127, "height": 127}}, "Image/cau3_red": {"Image/cau3_red": {"x": 0.5, "y": 0.5, "width": 127, "height": 127}}, "Image/hover1": {"Image/hover1": {"x": 0.5, "y": 0.5, "width": 67, "height": 67}}, "Image/hover2": {"Image/hover1": {"x": 0.5, "y": 0.5, "width": 67, "height": 67}}, "Image/hover3": {"Image/hover1": {"x": 0.5, "y": 0.5, "width": 67, "height": 67}}, "Image/red_effect": {"Image/red_effect": {"width": 84, "height": 84}}, "Image/stop": {"Image/stop": {"width": 54, "height": 54}}}}, "animations": {"anim_disable": {"slots": {"Image/cau3": {"attachment": [{"time": 0, "name": null}]}, "Image/cau3_off": {"attachment": [{"time": 0, "name": "Image/cau3_off"}]}}}, "anim_hover": {"slots": {"Image/hover1": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3333, "color": "fffffffa"}, {"time": 0.8333, "color": "ffffff00"}]}, "Image/hover2": {"color": [{"time": 0.3333, "color": "ffffff00"}, {"time": 0.6667, "color": "fffffffa"}, {"time": 1.1667, "color": "ffffff00"}]}, "Image/red_effect": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffffd9", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff75", "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "color": "ffffffd9", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "color": "ffffff00"}]}}, "bones": {"hover": {"scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.8333, "x": 1.853, "y": 1.853}]}, "hover2": {"scale": [{"time": 0.3333, "x": 1, "y": 1}, {"time": 1.1667, "x": 1.853, "y": 1.853}]}}}, "anim_idle_static": {}, "anim_idletostop": {"slots": {"Image/cau3_red": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Image/cau3_red"}]}, "Image/stop": {"color": [{"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffffff"}]}}, "bones": {"stop": {"scale": [{"time": 0.1667, "x": 0.772, "y": 0.772, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0.994, "y": 0.994, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 1, "y": 1}]}}}, "anim_stop_static": {"slots": {"Image/cau3": {"attachment": [{"time": 0, "name": null}]}, "Image/cau3_red": {"attachment": [{"time": 0, "name": "Image/cau3_red"}]}, "Image/stop": {"color": [{"time": 0, "color": "ffffffff"}]}}}}}