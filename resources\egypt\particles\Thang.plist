<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>angle</key>
    <integer>90</integer>
    <key>angleVariance</key>
    <integer>30</integer>
    <key>blendFuncDestination</key>
    <integer>771</integer>
    <key>blendFuncSource</key>
    <integer>770</integer>
    <key>duration</key>
    <real>0.2</real>
    <key>emitterType</key>
    <integer>0</integer>
    <key>finishColorAlpha</key>
    <integer>1</integer>
    <key>finishColorBlue</key>
    <real>0.7490196078431373</real>
    <key>finishColorGreen</key>
    <integer>1</integer>
    <key>finishColorRed</key>
    <integer>1</integer>
    <key>finishColorVarianceAlpha</key>
    <integer>0</integer>
    <key>finishColorVarianceBlue</key>
    <integer>0</integer>
    <key>finishColorVarianceGreen</key>
    <integer>0</integer>
    <key>finishColorVarianceRed</key>
    <integer>0</integer>
    <key>finishParticleSize</key>
    <integer>-1</integer>
    <key>finishParticleSizeVariance</key>
    <integer>0</integer>
    <key>gravityx</key>
    <integer>0</integer>
    <key>gravityy</key>
    <integer>-2400</integer>
    <key>maxParticles</key>
    <integer>81</integer>
    <key>maxRadius</key>
    <integer>0</integer>
    <key>maxRadiusVariance</key>
    <integer>0</integer>
    <key>minRadius</key>
    <integer>0</integer>
    <key>particleLifespan</key>
    <real>1.5</real>
    <key>particleLifespanVariance</key>
    <real>0.5</real>
    <key>radialAccelVariance</key>
    <integer>50</integer>
    <key>radialAcceleration</key>
    <integer>0</integer>
    <key>rotatePerSecond</key>
    <integer>0</integer>
    <key>rotatePerSecondVariance</key>
    <integer>0</integer>
    <key>rotationEnd</key>
    <integer>500</integer>
    <key>rotationEndVariance</key>
    <integer>0</integer>
    <key>rotationStart</key>
    <integer>0</integer>
    <key>rotationStartVariance</key>
    <integer>51</integer>
    <key>sourcePositionVariancex</key>
    <integer>30</integer>
    <key>sourcePositionVariancey</key>
    <integer>20</integer>
    <key>sourcePositionx</key>
    <real>471.61616309891394</real>
    <key>sourcePositiony</key>
    <real>494.88377417322573</real>
    <key>speed</key>
    <integer>850</integer>
    <key>speedVariance</key>
    <integer>200</integer>
    <key>startColorAlpha</key>
    <integer>1</integer>
    <key>startColorBlue</key>
    <real>0.7490196078431373</real>
    <key>startColorGreen</key>
    <integer>1</integer>
    <key>startColorRed</key>
    <integer>1</integer>
    <key>startColorVarianceAlpha</key>
    <integer>0</integer>
    <key>startColorVarianceBlue</key>
    <integer>0</integer>
    <key>startColorVarianceGreen</key>
    <integer>0</integer>
    <key>startColorVarianceRed</key>
    <integer>0</integer>
    <key>startParticleSize</key>
    <integer>40</integer>
    <key>startParticleSizeVariance</key>
    <integer>0</integer>
    <key>tangentialAccelVariance</key>
    <integer>0</integer>
    <key>tangentialAcceleration</key>
    <integer>0</integer>
    <key>emissionRate</key>
    <integer>54</integer>
    <key>spriteFrameUuid</key>
    <string>df428e69-e925-4e84-899f-63d5f5feb63b</string>
  </dict>
</plist>