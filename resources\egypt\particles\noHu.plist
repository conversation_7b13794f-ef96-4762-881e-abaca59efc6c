<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>angle</key>
    <integer>0</integer>
    <key>angleVariance</key>
    <integer>0</integer>
    <key>blendFuncDestination</key>
    <integer>771</integer>
    <key>blendFuncSource</key>
    <integer>770</integer>
    <key>duration</key>
    <integer>-1</integer>
    <key>emitterType</key>
    <integer>0</integer>
    <key>finishColorAlpha</key>
    <integer>1</integer>
    <key>finishColorBlue</key>
    <real>0.7490196078431373</real>
    <key>finishColorGreen</key>
    <integer>1</integer>
    <key>finishColorRed</key>
    <integer>1</integer>
    <key>finishColorVarianceAlpha</key>
    <integer>0</integer>
    <key>finishColorVarianceBlue</key>
    <integer>0</integer>
    <key>finishColorVarianceGreen</key>
    <integer>0</integer>
    <key>finishColorVarianceRed</key>
    <integer>0</integer>
    <key>finishParticleSize</key>
    <integer>-1</integer>
    <key>finishParticleSizeVariance</key>
    <integer>0</integer>
    <key>gravityx</key>
    <integer>0</integer>
    <key>gravityy</key>
    <integer>-906</integer>
    <key>maxParticles</key>
    <integer>277</integer>
    <key>maxRadius</key>
    <integer>0</integer>
    <key>maxRadiusVariance</key>
    <integer>0</integer>
    <key>minRadius</key>
    <integer>0</integer>
    <key>particleLifespan</key>
    <real>1.5</real>
    <key>particleLifespanVariance</key>
    <real>0.5</real>
    <key>radialAccelVariance</key>
    <integer>50</integer>
    <key>radialAcceleration</key>
    <integer>0</integer>
    <key>rotatePerSecond</key>
    <integer>0</integer>
    <key>rotatePerSecondVariance</key>
    <integer>0</integer>
    <key>rotationEnd</key>
    <integer>500</integer>
    <key>rotationEndVariance</key>
    <integer>0</integer>
    <key>rotationStart</key>
    <integer>0</integer>
    <key>rotationStartVariance</key>
    <integer>51</integer>
    <key>sourcePositionVariancex</key>
    <integer>608</integer>
    <key>sourcePositionVariancey</key>
    <integer>1</integer>
    <key>sourcePositionx</key>
    <real>578.6816971771159</real>
    <key>sourcePositiony</key>
    <real>20.245004330041276</real>
    <key>speed</key>
    <integer>0</integer>
    <key>speedVariance</key>
    <integer>337</integer>
    <key>startColorAlpha</key>
    <integer>1</integer>
    <key>startColorBlue</key>
    <real>0.7490196078431373</real>
    <key>startColorGreen</key>
    <integer>1</integer>
    <key>startColorRed</key>
    <integer>1</integer>
    <key>startColorVarianceAlpha</key>
    <integer>0</integer>
    <key>startColorVarianceBlue</key>
    <integer>0</integer>
    <key>startColorVarianceGreen</key>
    <integer>0</integer>
    <key>startColorVarianceRed</key>
    <integer>0</integer>
    <key>startParticleSize</key>
    <integer>40</integer>
    <key>startParticleSizeVariance</key>
    <integer>0</integer>
    <key>tangentialAccelVariance</key>
    <integer>0</integer>
    <key>tangentialAcceleration</key>
    <integer>0</integer>
    <key>emissionRate</key>
    <real>184.66666666666666</real>
    <key>spriteFrameUuid</key>
    <string>df428e69-e925-4e84-899f-63d5f5feb63b</string>
  </dict>
</plist>