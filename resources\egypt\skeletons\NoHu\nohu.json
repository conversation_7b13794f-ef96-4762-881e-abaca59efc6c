{"skeleton": {"hash": "9ig2HtKJApa4vdeJ0MDW25oiLXw", "spine": "3.6.53", "width": 1280, "height": 720}, "bones": [{"name": "root"}, {"name": "t", "parent": "root", "y": 13.07}, {"name": "body", "parent": "t", "x": -310.58, "y": 109.52}, {"name": "belt", "parent": "body", "x": 10.49, "y": -34.7}, {"name": "rb", "parent": "belt", "length": 31.24, "rotation": -64.92, "x": -21.08, "y": -36.32}, {"name": "Layer 4", "parent": "rb", "length": 30.25, "rotation": 33.76, "x": 31.24}, {"name": "Layer 5", "parent": "Layer 4", "length": 33.72, "rotation": 30.14, "x": 30.25}, {"name": "Layer 6", "parent": "Layer 5", "length": 30.86, "rotation": -68.42, "x": 34.33, "y": -0.59}, {"name": "Layer 7", "parent": "Layer 6", "length": 33.68, "rotation": -44.83, "x": 30.86}, {"name": "Layer 8", "parent": "Layer 7", "length": 25.95, "rotation": 28.27, "x": 33.68}, {"name": "body2", "parent": "t", "x": 303.31, "y": 109.52, "scaleX": -1.014}, {"name": "belt2", "parent": "body2", "x": 10.49, "y": -34.7}, {"name": "rb14", "parent": "belt2", "length": 31.24, "rotation": -64.92, "x": -21.08, "y": -36.32}, {"name": "Layer 9", "parent": "rb14", "length": 30.25, "rotation": 33.76, "x": 31.24}, {"name": "Layer 10", "parent": "Layer 9", "length": 33.72, "rotation": 30.14, "x": 30.25}, {"name": "Layer 11", "parent": "Layer 10", "length": 30.86, "rotation": -68.42, "x": 34.33, "y": -0.59}, {"name": "Layer 12", "parent": "Layer 11", "length": 33.68, "rotation": -44.83, "x": 30.86}, {"name": "Layer 13", "parent": "Layer 12", "length": 25.95, "rotation": 28.27, "x": 33.68}, {"name": "fr", "parent": "t", "length": 32.74, "rotation": -163.78, "x": 218.34, "y": 221.48}, {"name": "fr2", "parent": "t", "length": 32.74, "rotation": 21.47, "x": -223.44, "y": 235.91}, {"name": "hand2", "parent": "body", "length": 76.77, "rotation": -79.96, "x": 23.87, "y": 59.63}, {"name": "h2", "parent": "hand2", "length": 28.01, "rotation": 30.12, "x": 73.9, "y": -0.85}, {"name": "hand3", "parent": "body2", "length": 76.77, "rotation": -79.96, "x": 23.87, "y": 59.63}, {"name": "h3", "parent": "hand3", "length": 28.01, "rotation": 30.12, "x": 73.9, "y": -0.85}, {"name": "handL", "parent": "body", "length": 56.77, "rotation": -111.43, "x": -58.41, "y": 65.65}, {"name": "handL2", "parent": "handL", "length": 91.72, "rotation": 33.64, "x": 56.77}, {"name": "handL3", "parent": "handL2", "length": 34.92, "rotation": 7.96, "x": 91.72}, {"name": "handL4", "parent": "body2", "length": 56.77, "rotation": -111.43, "x": -58.41, "y": 65.65}, {"name": "handL5", "parent": "handL4", "length": 91.72, "rotation": 33.64, "x": 56.77}, {"name": "handL6", "parent": "handL5", "length": 34.92, "rotation": 7.96, "x": 91.72}, {"name": "head", "parent": "body", "length": 35.43, "rotation": 99.78, "x": -8.18, "y": 89.11}, {"name": "head2", "parent": "head", "length": 23.82, "rotation": -30.51, "x": 35.43}, {"name": "head3", "parent": "head2", "length": 24.1, "rotation": -56.28, "x": 23.82}, {"name": "head4", "parent": "head3", "length": 24.94, "rotation": -32.74, "x": 24.1}, {"name": "head5", "parent": "body2", "length": 35.43, "rotation": 99.78, "x": -8.18, "y": 89.11}, {"name": "head6", "parent": "head5", "length": 23.82, "rotation": -30.51, "x": 35.43}, {"name": "head7", "parent": "head6", "length": 24.1, "rotation": -56.28, "x": 23.82}, {"name": "head8", "parent": "head7", "length": 24.94, "rotation": -32.74, "x": 24.1}, {"name": "l2", "parent": "t", "length": 138.96, "rotation": 113.45, "x": 294.42, "y": 79.58, "scaleX": 0.556, "scaleY": 0.556}, {"name": "nen tien", "parent": "t", "length": 249.52, "rotation": 0.21, "x": -142.19, "y": -28.52}, {"name": "l3", "parent": "nen tien", "length": 169.36, "rotation": 76.05, "x": -156.23, "y": 117.91, "scaleX": 0.629, "scaleY": 0.629}, {"name": "l4", "parent": "t", "length": 138.96, "rotation": 113.45, "x": -13.74, "y": 143.16, "scaleX": 0.663, "scaleY": 0.663}, {"name": "light", "parent": "t", "x": 178.78, "y": 193.71}, {"name": "light2", "parent": "t", "x": -435.95, "y": 144.88}, {"name": "light3", "parent": "t", "x": 6.01, "y": -89.25}, {"name": "light4", "parent": "t", "x": 391.38, "y": 36.25, "scaleX": 0.495, "scaleY": 0.495}, {"name": "light5", "parent": "t", "x": -192.3, "y": 173.34, "scaleX": 0.481, "scaleY": 0.481}, {"name": "m1", "parent": "head4", "length": 37.81, "rotation": 5.91, "x": 14.58, "y": -3.53}, {"name": "m2", "parent": "head4", "length": 29.85, "rotation": -21.98, "x": 20.31, "y": -13.82}, {"name": "m3", "parent": "head8", "length": 37.81, "rotation": 5.91, "x": 14.58, "y": -3.53}, {"name": "m4", "parent": "head8", "length": 29.85, "rotation": -21.98, "x": 20.31, "y": -13.82}, {"name": "nohu", "parent": "nen tien", "length": 93.27, "rotation": 91.47, "x": 93.11, "y": 56.35}, {"name": "pants", "parent": "belt", "length": 49.18, "rotation": -39.54, "x": -6.63, "y": -35.12}, {"name": "pants2", "parent": "belt2", "length": 49.18, "rotation": -39.54, "x": -6.63, "y": -35.12}, {"name": "rb2", "parent": "rb", "length": 16.9, "rotation": -23.94, "x": -0.46, "y": -15.71}, {"name": "rb3", "parent": "rb2", "length": 20.56, "rotation": -10.61, "x": 16.9}, {"name": "rb4", "parent": "rb3", "length": 21.78, "rotation": -32.39, "x": 20.56}, {"name": "rb5", "parent": "rb4", "length": 28.87, "rotation": -22.48, "x": 21.78}, {"name": "rb6", "parent": "rb5", "length": 25.15, "rotation": 15.52, "x": 28.87}, {"name": "rb7", "parent": "rb", "length": 13.67, "rotation": 73.45, "x": 7.6, "y": 46.95}, {"name": "rb8", "parent": "rb7", "length": 17.23, "rotation": -34.09, "x": 13.67}, {"name": "rb9", "parent": "rb8", "length": 21.46, "rotation": -0.6, "x": 17.23}, {"name": "rb10", "parent": "rb9", "length": 15.78, "rotation": 36.03, "x": 21.46}, {"name": "rb11", "parent": "rb", "length": 22.8, "rotation": -9.6, "x": 72.26, "y": 25.64}, {"name": "rb12", "parent": "rb11", "length": 21.35, "rotation": -38.8, "x": 22.8}, {"name": "rb13", "parent": "rb12", "length": 27.67, "rotation": -41.37, "x": 21.35}, {"name": "rb15", "parent": "rb14", "length": 16.9, "rotation": -23.94, "x": -0.46, "y": -15.71}, {"name": "rb16", "parent": "rb15", "length": 20.56, "rotation": -10.61, "x": 16.9}, {"name": "rb17", "parent": "rb16", "length": 21.78, "rotation": -32.39, "x": 20.56}, {"name": "rb18", "parent": "rb17", "length": 28.87, "rotation": -22.48, "x": 21.78}, {"name": "rb19", "parent": "rb18", "length": 25.15, "rotation": 15.52, "x": 28.87}, {"name": "rb20", "parent": "rb14", "length": 13.67, "rotation": 73.45, "x": 7.6, "y": 46.95}, {"name": "rb21", "parent": "rb20", "length": 17.23, "rotation": -34.09, "x": 13.67}, {"name": "rb22", "parent": "rb21", "length": 21.46, "rotation": -0.6, "x": 17.23}, {"name": "rb23", "parent": "rb22", "length": 15.78, "rotation": 36.03, "x": 21.46}, {"name": "rb24", "parent": "rb14", "length": 22.8, "rotation": -9.6, "x": 72.26, "y": 25.64}, {"name": "rb25", "parent": "rb24", "length": 21.35, "rotation": -38.8, "x": 22.8}, {"name": "rb26", "parent": "rb25", "length": 27.67, "rotation": -41.37, "x": 21.35}, {"name": "stick1", "parent": "handL3", "length": 156.92, "rotation": 62.59, "x": 21.72, "y": -2.83}, {"name": "stick2", "parent": "stick1", "length": 114.93, "rotation": -67, "x": 158.74, "y": -3.5}, {"name": "stick3", "parent": "handL6", "length": 156.92, "rotation": 62.59, "x": 21.72, "y": -2.83}, {"name": "stick4", "parent": "stick3", "length": 114.93, "rotation": -67, "x": 158.74, "y": -3.5}, {"name": "vang", "parent": "nen tien", "length": 76.78, "rotation": 89.11, "x": 223.7, "y": 185.64}, {"name": "vang2", "parent": "nen tien", "length": 95.21, "rotation": 86.49, "x": 31.71, "y": 173.55}, {"name": "vang3", "parent": "nen tien", "length": 70.99, "rotation": 77.9, "x": -103.71, "y": 132}], "slots": [{"name": "l", "bone": "l3", "attachment": "l"}, {"name": "l2", "bone": "l2", "attachment": "l"}, {"name": "l3", "bone": "l4", "attachment": "l"}, {"name": "nen tien", "bone": "nen tien", "attachment": "nentien"}, {"name": "vang", "bone": "vang", "attachment": "vang"}, {"name": "vang3", "bone": "vang3", "attachment": "vang"}, {"name": "vang2", "bone": "vang2", "attachment": "vang"}, {"name": "hand2", "bone": "hand2", "attachment": "hand2"}, {"name": "hand3", "bone": "hand3", "attachment": "hand2"}, {"name": "h2", "bone": "h2", "attachment": "h2"}, {"name": "h3", "bone": "h3", "attachment": "h2"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "head2", "bone": "head5", "attachment": "head"}, {"name": "m2", "bone": "m2", "attachment": "m2"}, {"name": "m4", "bone": "m4", "attachment": "m2"}, {"name": "m1", "bone": "m1", "attachment": "m1"}, {"name": "m3", "bone": "m3", "attachment": "m1"}, {"name": "pants", "bone": "pants", "attachment": "pants"}, {"name": "pants2", "bone": "pants2", "attachment": "pants"}, {"name": "rb", "bone": "rb", "attachment": "rb"}, {"name": "rb2", "bone": "rb14", "attachment": "rb"}, {"name": "body", "bone": "body", "attachment": "body"}, {"name": "body2", "bone": "body2", "attachment": "body"}, {"name": "belt", "bone": "belt", "attachment": "belt"}, {"name": "belt2", "bone": "belt2", "attachment": "belt"}, {"name": "stick1", "bone": "stick1", "attachment": "stick1"}, {"name": "stick2", "bone": "stick3", "attachment": "stick1"}, {"name": "handL", "bone": "handL", "attachment": "handL"}, {"name": "handL2", "bone": "handL4", "attachment": "handL"}, {"name": "nohu", "bone": "nohu", "attachment": "nohu"}, {"name": "so", "bone": "t", "attachment": "so"}, {"name": "fr1", "bone": "fr", "color": "ffffff00", "attachment": "fr3"}, {"name": "fr2", "bone": "fr2", "color": "ffffff00", "attachment": "fr3"}, {"name": "light", "bone": "light", "attachment": "light"}, {"name": "light2", "bone": "light2", "attachment": "light"}, {"name": "light4", "bone": "light4", "attachment": "light"}, {"name": "light3", "bone": "light3", "attachment": "light"}, {"name": "light5", "bone": "light5", "attachment": "light"}], "skins": {"default": {"belt": {"belt": {"x": -16.92, "y": -11.34, "width": 102, "height": 94}}, "belt2": {"belt": {"x": -16.92, "y": -11.34, "width": 102, "height": 94}}, "body": {"body": {"x": -16.43, "y": 19.46, "width": 108, "height": 139}}, "body2": {"body": {"x": -16.43, "y": 19.46, "width": 108, "height": 139}}, "fr1": {"fr1": {"x": 24.63, "y": -0.1, "rotation": -111.64, "width": 74, "height": 92}, "fr2": {"x": 53.71, "y": 4.12, "rotation": -111.64, "width": 97, "height": 140}, "fr3": {"x": 80.26, "y": -3.95, "rotation": -111.64, "width": 120, "height": 215}}, "fr2": {"fr1": {"x": 24.63, "y": -0.1, "rotation": -111.64, "width": 74, "height": 92}, "fr2": {"x": 53.71, "y": 4.12, "rotation": -111.64, "width": 97, "height": 140}, "fr3": {"x": 80.26, "y": -3.95, "rotation": -111.64, "width": 120, "height": 215}}, "h2": {"h2": {"x": 15.11, "y": 5.12, "rotation": 49.84, "width": 82, "height": 107}}, "h3": {"h2": {"x": 15.11, "y": 5.12, "rotation": 49.84, "width": 82, "height": 107}}, "hand2": {"hand2": {"x": 33.83, "y": 0.81, "rotation": 79.96, "width": 50, "height": 117}}, "hand3": {"hand2": {"x": 33.83, "y": 0.81, "rotation": 79.96, "width": 50, "height": 117}}, "handL": {"handL": {"type": "mesh", "uvs": [0.68284, 0.0684, 0.74759, 0.09254, 0.92746, 0.12176, 1, 0.14082, 1, 0.19419, 0.85192, 0.26662, 0.85551, 0.31109, 0.76917, 0.35302, 0.68643, 0.40512, 0.56053, 0.44578, 0.56053, 0.46865, 0.62888, 0.53218, 0.61089, 0.66433, 0.71162, 0.75835, 0.72241, 0.79647, 0.85191, 0.84349, 0.88789, 0.87779, 0.82687, 0.94618, 0.72687, 1, 0.39051, 1, 0.21778, 0.93494, 0.19051, 0.90122, 0.20869, 0.72783, 0.07687, 0.64274, 0, 0.53838, 0, 0.04298, 0.09646, 0, 0.41303, 0], "triangles": [19, 14, 18, 18, 14, 17, 14, 19, 12, 17, 15, 16, 17, 14, 15, 21, 22, 20, 19, 20, 22, 14, 12, 13, 12, 19, 22, 22, 10, 12, 10, 23, 9, 10, 22, 23, 12, 10, 11, 9, 24, 26, 9, 23, 24, 26, 27, 9, 24, 25, 26, 9, 0, 8, 9, 27, 0, 7, 0, 1, 7, 8, 0, 7, 5, 6, 7, 1, 5, 5, 2, 4, 5, 1, 2, 2, 3, 4], "vertices": [1, 24, -23.18, 8.7, 1, 1, 24, -20.1, 15.26, 1, 1, 24, -19.23, 30.48, 1, 1, 24, -17.4, 37.2, 1, 2, 24, -6.57, 41.45, 0.99993, 25, -29.78, 69.6, 7e-05, 2, 24, 12.29, 36.6, 0.9807, 25, -16.76, 55.11, 0.0193, 2, 24, 21.22, 40.4, 0.94744, 25, -7.22, 53.33, 0.05256, 2, 24, 32.16, 37.55, 0.87313, 25, 0.3, 44.9, 0.12687, 2, 24, 45.06, 35.77, 0.68912, 25, 10.06, 36.27, 0.31088, 2, 24, 56.85, 29.98, 0.33265, 25, 16.67, 24.92, 0.66735, 2, 24, 61.49, 31.8, 0.16909, 25, 21.54, 23.87, 0.83091, 2, 24, 72.46, 41.76, 0.01448, 25, 36.19, 26.08, 0.98552, 2, 25, 64.06, 18.63, 0.99894, 26, -24.82, 22.28, 0.00106, 2, 25, 85.73, 21.88, 0.70301, 26, -2.9, 22.5, 0.29699, 2, 25, 94.03, 20.93, 0.34963, 26, 5.18, 20.41, 0.65037, 2, 25, 106.15, 28.51, 0.03315, 26, 18.24, 26.24, 0.96685, 2, 25, 114.05, 29.64, 0.00531, 26, 26.22, 26.26, 0.99469, 1, 26, 38.59, 16.71, 1, 1, 26, 46.95, 5.44, 1, 1, 26, 38.02, -18.88, 1, 2, 25, 115.31, -23.43, 0.00631, 26, 20.12, -26.47, 0.99369, 2, 25, 107.68, -23.93, 0.04377, 26, 12.49, -25.91, 0.95623, 2, 25, 71.03, -14.56, 0.99939, 26, -22.5, -11.56, 0.00061, 1, 25, 50.76, -20.56, 1, 1, 25, 27.27, -21.53, 1, 1, 24, -9.13, -42.27, 1, 1, 24, -20.56, -38.78, 1, 1, 24, -29.47, -16.09, 1], "hull": 28}}, "handL2": {"handL": {"type": "mesh", "uvs": [0.68284, 0.0684, 0.74759, 0.09254, 0.92746, 0.12176, 1, 0.14082, 1, 0.19419, 0.85192, 0.26662, 0.85551, 0.31109, 0.76917, 0.35302, 0.68643, 0.40512, 0.56053, 0.44578, 0.56053, 0.46865, 0.62888, 0.53218, 0.61089, 0.66433, 0.71162, 0.75835, 0.72241, 0.79647, 0.85191, 0.84349, 0.88789, 0.87779, 0.82687, 0.94618, 0.72687, 1, 0.39051, 1, 0.21778, 0.93494, 0.19051, 0.90122, 0.20869, 0.72783, 0.07687, 0.64274, 0, 0.53838, 0, 0.04298, 0.09646, 0, 0.41303, 0], "triangles": [19, 14, 18, 18, 14, 17, 14, 19, 12, 17, 15, 16, 17, 14, 15, 21, 22, 20, 19, 20, 22, 14, 12, 13, 12, 19, 22, 22, 10, 12, 10, 23, 9, 10, 22, 23, 12, 10, 11, 9, 24, 26, 9, 23, 24, 26, 27, 9, 24, 25, 26, 9, 0, 8, 9, 27, 0, 7, 0, 1, 7, 8, 0, 7, 5, 6, 7, 1, 5, 5, 2, 4, 5, 1, 2, 2, 3, 4], "vertices": [1, 27, -23.18, 8.7, 1, 1, 27, -20.1, 15.26, 1, 1, 27, -19.23, 30.48, 1, 1, 27, -17.4, 37.2, 1, 2, 27, -6.57, 41.45, 0.99993, 28, -29.78, 69.6, 7e-05, 2, 27, 12.29, 36.6, 0.9807, 28, -16.76, 55.11, 0.0193, 2, 27, 21.22, 40.4, 0.94744, 28, -7.22, 53.33, 0.05256, 2, 27, 32.16, 37.55, 0.87313, 28, 0.3, 44.9, 0.12687, 2, 27, 45.06, 35.77, 0.68912, 28, 10.06, 36.27, 0.31088, 2, 27, 56.85, 29.98, 0.33265, 28, 16.67, 24.92, 0.66735, 2, 27, 61.49, 31.8, 0.16909, 28, 21.54, 23.87, 0.83091, 2, 27, 72.46, 41.76, 0.01448, 28, 36.19, 26.08, 0.98552, 2, 28, 64.06, 18.63, 0.99894, 29, -24.82, 22.28, 0.00106, 2, 28, 85.73, 21.88, 0.70301, 29, -2.9, 22.5, 0.29699, 2, 28, 94.03, 20.93, 0.34963, 29, 5.18, 20.41, 0.65037, 2, 28, 106.15, 28.51, 0.03315, 29, 18.24, 26.24, 0.96685, 2, 28, 114.05, 29.64, 0.00531, 29, 26.22, 26.26, 0.99469, 1, 29, 38.59, 16.71, 1, 1, 29, 46.95, 5.44, 1, 1, 29, 38.02, -18.88, 1, 2, 28, 115.31, -23.43, 0.00631, 29, 20.12, -26.47, 0.99369, 2, 28, 107.68, -23.93, 0.04377, 29, 12.49, -25.91, 0.95623, 2, 28, 71.03, -14.56, 0.99939, 29, -22.5, -11.56, 0.00061, 1, 28, 50.76, -20.56, 1, 1, 28, 27.27, -21.53, 1, 1, 27, -9.13, -42.27, 1, 1, 27, -20.56, -38.78, 1, 1, 27, -29.47, -16.09, 1], "hull": 28}}, "head": {"head": {"type": "mesh", "uvs": [0.59294, 0.06761, 0.64683, 0.15266, 0.66756, 0.21961, 0.69036, 0.18885, 0.71523, 0.2178, 0.73181, 0.2739, 0.77534, 0.3119, 0.82508, 0.34085, 0.845, 0.40245, 0.85166, 0.4396, 0.81742, 0.51388, 0.76415, 0.56671, 0.73561, 0.57826, 0.6871, 0.57248, 0.71754, 0.7161, 0.72705, 0.83166, 0.70708, 0.87293, 0.64144, 0.89191, 0.43123, 0.8754, 0.1763, 0.85147, 0.09548, 0.83123, 0.04781, 0.72266, 0.04159, 0.62314, 0.0789, 0.48199, 0.15974, 0.37704, 0.36286, 0.27571, 0.37737, 0.22142, 0.36079, 0.14542, 0.32763, 0.09114, 0.26545, 0.05314, 0.34421, 0.0278, 0.48516, 0, 0.54112, 0, 0.43126, 0.05133, 0.50174, 0.11466, 0.53076, 0.16171, 0.53697, 0.10742, 0.5121, 0.0459, 0.81908, 0.35706, 0.8049, 0.36201, 0.80868, 0.38347, 0.39127, 0.2968, 0.3304, 0.37357, 0.2971, 0.5081, 0.30376, 0.64925, 0.34086, 0.74417, 0.43884, 0.83166, 0.46262, 0.84734, 0.60054, 0.55185, 0.58152, 0.59394, 0.58342, 0.65915, 0.60244, 0.74086, 0.6462, 0.79782, 0.69757, 0.83991, 0.66332, 0.53782, 0.70613, 0.54525, 0.5311, 0.51801, 0.48449, 0.44785, 0.47593, 0.38099, 0.62813, 0.47922, 0.61291, 0.4396, 0.70518, 0.48995, 0.38842, 0.36696, 0.37891, 0.4462, 0.39698, 0.57001, 0.44074, 0.68226, 0.51588, 0.76315, 0.41291, 0.40955, 0.45905, 0.42166, 0.50949, 0.36858, 0.53739, 0.40024, 0.54705, 0.42911, 0.59534, 0.40955, 0.605, 0.36579, 0.64471, 0.33692, 0.67154, 0.34623, 0.71554, 0.34064, 0.75525, 0.36206, 0.7091, 0.31457, 0.68549, 0.30433, 0.67369, 0.27825, 0.63612, 0.25683, 0.58354, 0.22517, 0.57388, 0.24659, 0.58891, 0.2885, 0.62325, 0.30712, 0.64364, 0.3155, 0.7692, 0.40397, 0.74988, 0.4319, 0.7091, 0.43935, 0.64256, 0.38441, 0.69944, 0.40304], "triangles": [61, 59, 89, 54, 59, 61, 88, 55, 61, 54, 61, 55, 48, 59, 54, 88, 10, 55, 10, 11, 55, 13, 54, 55, 12, 55, 11, 13, 55, 12, 13, 48, 54, 9, 40, 8, 10, 88, 9, 80, 4, 5, 79, 80, 5, 78, 79, 5, 78, 5, 6, 76, 78, 6, 79, 75, 86, 75, 79, 78, 75, 78, 76, 7, 39, 6, 38, 39, 7, 77, 76, 6, 77, 6, 39, 40, 39, 38, 38, 7, 8, 40, 38, 8, 91, 75, 76, 91, 76, 77, 90, 75, 91, 87, 77, 39, 87, 39, 40, 91, 77, 87, 88, 91, 87, 89, 91, 88, 89, 60, 91, 40, 9, 87, 9, 88, 87, 89, 88, 61, 37, 31, 32, 33, 30, 31, 33, 31, 37, 37, 32, 0, 28, 29, 30, 28, 30, 33, 36, 37, 0, 34, 33, 37, 34, 37, 36, 27, 28, 33, 27, 33, 34, 36, 0, 1, 35, 34, 36, 35, 36, 1, 2, 3, 4, 26, 27, 34, 26, 34, 35, 82, 35, 1, 82, 1, 2, 83, 35, 82, 81, 82, 2, 83, 82, 81, 80, 2, 4, 81, 2, 80, 84, 83, 81, 41, 26, 35, 41, 35, 83, 25, 26, 41, 85, 84, 81, 86, 85, 81, 80, 86, 81, 79, 86, 80, 75, 74, 86, 73, 84, 85, 74, 73, 85, 74, 85, 86, 69, 41, 83, 69, 83, 84, 69, 84, 73, 69, 58, 41, 90, 73, 74, 90, 74, 75, 70, 69, 73, 72, 70, 73, 72, 73, 90, 71, 70, 72, 60, 72, 90, 70, 57, 58, 70, 58, 69, 57, 70, 71, 60, 89, 59, 91, 60, 90, 60, 71, 72, 59, 71, 60, 71, 59, 56, 41, 42, 25, 58, 62, 41, 62, 42, 41, 42, 24, 25, 67, 62, 58, 68, 67, 58, 63, 42, 62, 63, 62, 67, 68, 58, 57, 43, 24, 42, 43, 42, 63, 23, 24, 43, 56, 57, 71, 48, 56, 59, 63, 57, 64, 68, 63, 67, 57, 63, 68, 64, 57, 56, 43, 63, 64, 49, 56, 48, 44, 43, 64, 13, 50, 49, 65, 64, 56, 65, 56, 49, 65, 49, 50, 44, 64, 65, 44, 21, 22, 49, 48, 13, 14, 50, 13, 14, 51, 50, 45, 44, 65, 66, 65, 50, 66, 50, 51, 52, 51, 14, 44, 22, 43, 43, 22, 23, 44, 19, 21, 19, 20, 21, 52, 14, 15, 46, 45, 65, 46, 65, 66, 53, 52, 15, 47, 46, 66, 45, 19, 44, 16, 53, 15, 18, 45, 46, 18, 46, 47, 19, 45, 18, 52, 66, 51, 17, 52, 53, 17, 53, 16, 17, 66, 52, 47, 66, 17, 18, 47, 17], "vertices": [3, 32, 28.63, 61, 0.96624, 33, -29.18, 53.76, 0.03376, 47, -56.9, 58.3, 0, 3, 32, 33.68, 43.34, 0.87583, 33, -15.38, 41.64, 0.12417, 47, -44.06, 45.16, 0, 3, 32, 34.16, 30.24, 0.65887, 33, -7.89, 30.88, 0.34113, 47, -37.41, 33.86, 0, 3, 32, 39.14, 35.06, 0.58581, 33, -6.31, 37.63, 0.41419, 47, -35.32, 40.47, 0, 3, 32, 41.91, 28.81, 0.55744, 33, -0.6, 33.86, 0.44256, 47, -29.92, 36.28, 0, 2, 32, 42.19, 17.86, 0.34072, 33, 5.56, 24.81, 0.65928, 4, 31, 57.96, -34.62, 0, 32, 47.75, 9.18, 0.07438, 33, 14.93, 20.52, 0.91525, 47, -15.45, 21.79, 0.01037, 4, 31, 56, -45.39, 0, 32, 55.62, 1.58, 0.00263, 33, 25.66, 18.38, 0.92213, 47, -4.91, 18.83, 0.07524, 4, 31, 47.57, -58.99, 0, 32, 62.25, -12.99, 0, 33, 39.12, 9.71, 0.56035, 47, 7.84, 9.16, 0.43965, 2, 33, 52.16, 4.95, 0.01105, 47, 20.44, 3.42, 0.98895, 6, 30, 28.3, -61.67, 0.00014, 31, 25.17, -56.75, 3e-05, 32, 47.95, -30.38, 0, 33, 36.5, -12.65, 0.01537, 47, 3.53, -12.92, 0.13587, 48, 14.57, 7.14, 0.84859, 6, 30, 20.69, -49.1, 0.03135, 31, 12.23, -49.79, 0.00666, 32, 34.98, -37.28, 0.00128, 33, 29.32, -25.47, 9e-05, 47, -4.61, -25.16, 0.00244, 48, 12.71, -7.43, 0.95818, 6, 30, 19.34, -44.08, 0.07285, 31, 8.52, -46.15, 0.01675, 32, 29.89, -38.34, 0.00691, 33, 25.61, -29.11, 2e-05, 47, -8.58, -28.51, 0.00172, 48, 10.64, -12.2, 0.90175, 5, 30, 21.79, -36.36, 0.27468, 31, 6.7, -38.25, 0.05504, 32, 22.32, -35.47, 0.02921, 47, -16.6, -29.59, 0.0006, 48, 3.92, -16.73, 0.64046, 5, 30, -5.81, -36.69, 0.85135, 31, -16.91, -52.55, 0.0035, 32, 21.1, -63.05, 1e-05, 47, -4.57, -54.43, 0.00017, 48, 25.73, -33.64, 0.14498, 3, 30, -27.6, -34.53, 0.9491, 47, 2.73, -75.07, 9e-05, 48, 41.44, -48.9, 0.05081, 3, 30, -34.73, -29.95, 0.95945, 47, 1.62, -83.47, 8e-05, 48, 44.17, -56.91, 0.04047, 3, 30, -36.43, -18.67, 0.97096, 47, -7.87, -89.8, 6e-05, 48, 38.48, -66.8, 0.02898, 3, 30, -27.46, 14.98, 0.99998, 47, -42.14, -95.99, 0, 48, 10.52, -87.56, 2e-05, 2, 30, -15.85, 55.67, 0.94034, 31, -72.44, 21.92, 0.05966, 2, 30, -9.82, 68.16, 0.91203, 31, -73.58, 35.75, 0.08797, 2, 30, 11.74, 72.42, 0.8289, 31, -57.18, 50.37, 0.1711, 2, 30, 30.45, 70.24, 0.70511, 31, -39.95, 57.98, 0.29489, 2, 30, 55.69, 59.64, 0.45822, 31, -12.82, 61.67, 0.54178, 3, 30, 72.98, 43.12, 0.24407, 31, 10.45, 56.21, 0.73398, 32, -54.17, 20.09, 0.02195, 3, 30, 86.15, 6.84, 0.00583, 31, 40.23, 31.64, 0.42013, 32, -17.21, 31.22, 0.57403, 5, 30, 95.86, 2.74, 1e-05, 31, 50.67, 33.03, 0.16692, 32, -12.57, 40.68, 0.83295, 33, -52.84, 14.39, 0.00011, 47, -83.48, 20.84, 0, 4, 31, 63.14, 40.68, 0.03797, 32, -12, 55.29, 0.96202, 33, -60.27, 26.98, 1e-05, 47, -89.93, 33.96, 0, 3, 31, 70.8, 49.43, 0.00586, 32, -15.03, 66.52, 0.99414, 47, -97.93, 42.4, 0, 2, 32, -23.41, 75.82, 1, 47, -109.72, 46.61, 0, 3, 31, 82.96, 51.1, 0.0007, 32, -9.67, 77.56, 0.9993, 47, -98.46, 54.67, 0, 3, 32, 14.17, 77.46, 0.99447, 33, -50.24, 59.78, 0.00553, 47, -77.43, 65.9, 0, 3, 32, 23.17, 75.38, 0.99, 33, -41.55, 62.9, 0.01, 47, -68.53, 68.35, 0, 4, 31, 83.89, 36.1, 0.0025, 32, 3.33, 70, 0.99537, 33, -55.33, 47.65, 0.00213, 47, -83.43, 54.19, 0, 4, 31, 76.81, 20.98, 0.00939, 32, 11.96, 55.72, 0.96967, 33, -40.34, 40.31, 0.02094, 47, -69.05, 45.73, 0, 4, 31, 70.18, 13.36, 0.01393, 32, 14.63, 45.98, 0.93976, 33, -32.83, 33.56, 0.04631, 47, -62.07, 38.43, 0, 4, 31, 80.14, 16.03, 0.0037, 32, 17.94, 55.75, 0.96435, 33, -35.33, 43.56, 0.03195, 47, -63.81, 48.59, 0, 4, 31, 89.57, 23.98, 0.00057, 32, 16.55, 68, 0.98825, 33, -43.12, 53.12, 0.01119, 47, -70.85, 58.72, 0, 4, 31, 52.89, -45.98, 0, 32, 54.38, -1.35, 0.00095, 33, 26.2, 15.25, 0.89901, 47, -4.61, 15.67, 0.10005, 4, 31, 51.13, -43.87, 0, 32, 51.65, -1.63, 0.00179, 33, 24.06, 13.53, 0.9124, 47, -6.88, 14.12, 0.08582, 3, 31, 47.8, -47, 0, 33, 27.14, 10.15, 0.85028, 47, -4.06, 10.51, 0.14972, 3, 30, 81.43, 2.9, 0.0039, 31, 38.16, 25.85, 0.41909, 32, -13.54, 26.28, 0.57701, 3, 30, 68.84, 15.26, 0.0609, 31, 21.03, 30.11, 0.76484, 32, -26.59, 14.4, 0.17426, 3, 30, 44.71, 25, 0.4025, 31, -4.69, 26.25, 0.59603, 32, -37.66, -9.14, 0.00147, 2, 30, 18.24, 28.45, 0.87068, 31, -29.25, 15.78, 0.12932, 2, 30, -0.48, 25.46, 0.97869, 31, -43.87, 3.71, 0.02131, 3, 30, -19.52, 12.34, 0.99995, 47, -43.12, -87.68, 0, 48, 5.95, -80.55, 4e-05, 3, 30, -23.11, 8.98, 0.99945, 47, -38.56, -89.5, 1e-05, 48, 10.85, -80.15, 0.00055, 6, 30, 28.06, -22.94, 0.37638, 31, 5.29, -23.51, 0.19617, 32, 9.27, -28.46, 0.08671, 33, 2.92, -31.95, 0.00257, 47, -31.42, -29.61, 0.00016, 48, -9.34, -23.33, 0.33801, 5, 30, 20.75, -18.5, 0.62946, 31, -3.26, -23.39, 0.11153, 32, 4.43, -35.5, 0.02961, 47, -32.33, -38.12, 0.00015, 48, -6.38, -31.35, 0.22925, 5, 30, 8.55, -16.71, 0.83742, 31, -14.67, -28.04, 0.02089, 32, 1.96, -47.58, 0.0035, 47, -28.76, -49.92, 0.00013, 48, 2.05, -40.34, 0.13806, 4, 30, -7.2, -17.18, 0.92791, 31, -28.01, -36.44, 0.0007, 47, -21.64, -63.98, 9e-05, 48, 14.68, -49.78, 0.0713, 3, 30, -19.03, -22.47, 0.94583, 47, -11.83, -72.44, 9e-05, 48, 27.23, -53.01, 0.05408, 3, 30, -28.31, -29.47, 0.95386, 47, -1.55, -77.86, 8e-05, 48, 38.85, -53.3, 0.04605, 6, 30, 28.91, -33.6, 0.19726, 31, 11.44, -32.26, 0.08674, 32, 19.96, -28.2, 0.07256, 33, 11.78, -25.96, 0.00998, 47, -22.13, -24.31, 0.00029, 48, -3.37, -14.45, 0.63316, 5, 30, 26.33, -40.33, 0.11212, 31, 12.63, -39.37, 0.03374, 32, 26.54, -31.16, 0.02375, 47, -14.94, -23.79, 0.00062, 48, 2.84, -10.79, 0.82977, 6, 30, 36.31, -12.74, 0.23395, 31, 7.22, -10.53, 0.58719, 32, -0.46, -19.65, 0.08173, 33, -10.02, -29.8, 7e-05, 47, -44.16, -26.49, 3e-05, 48, -22.15, -26.18, 0.09703, 1, 31, 16.9, 1.36, 1, 2, 31, 28.22, 7.15, 0.41745, 32, -3.5, 7.63, 0.58255, 6, 30, 40.81, -29.76, 0.07593, 31, 19.74, -22.91, 0.14752, 32, 16.79, -16.1, 0.30797, 33, 2.57, -17.49, 0.116, 47, -30.67, -15.17, 3e-05, 48, -15.08, -10.05, 0.35255, 6, 30, 48.62, -28.55, 0.0193, 31, 25.86, -17.91, 0.08797, 32, 16.03, -8.24, 0.64827, 33, -2.33, -11.29, 0.11747, 47, -35.08, -8.62, 0, 48, -21.94, -6.14, 0.12698, 5, 30, 36.66, -41.94, 0.02511, 31, 22.35, -35.51, 0.02128, 32, 28.72, -20.94, 0.04259, 33, 15.22, -15.11, 0.09035, 48, -4.24, -3.1, 0.82066, 3, 30, 68.44, 5.62, 0.0153, 31, 25.59, 21.59, 0.68378, 32, -16.98, 13.46, 0.30092, 3, 30, 53.95, 9.71, 0.07946, 31, 11.03, 17.76, 0.88485, 32, -21.87, -0.78, 0.03569, 2, 30, 30.38, 10.74, 0.82412, 31, -9.8, 6.69, 0.17588, 2, 30, 8.25, 7.23, 0.99946, 31, -27.09, -7.57, 0.00054, 3, 30, -8.92, -2.39, 0.99098, 47, -34.3, -71.82, 2e-05, 48, 6.82, -62.43, 0.009, 3, 30, 59.82, 3, 0.01244, 31, 19.49, 14.97, 0.8401, 32, -14.85, 4.71, 0.14746, 2, 31, 20.05, 7.04, 0.89745, 32, -7.94, 0.77, 0.10255, 3, 31, 32.37, 2.8, 0.10863, 32, 2.42, 8.67, 0.89137, 47, -55.09, -0.2, 0, 1, 32, 5.56, 1.81, 1, 6, 30, 52.42, -18.18, 0.0043, 31, 23.87, -7.04, 0.2087, 32, 5.89, -3.87, 0.76975, 33, -13.22, -13.1, 0.00063, 47, -46.08, -9.59, 0, 48, -31.37, -11.89, 0.01662, 6, 30, 54.71, -26.66, 0.00203, 31, 30.14, -13.19, 0.01446, 32, 14.48, -2.06, 0.95616, 33, -6.97, -6.93, 0.01237, 47, -39.38, -3.91, 0, 48, -27.88, -3.83, 0.01498, 3, 32, 17.89, 5.65, 0.96549, 33, -8.27, 1.39, 0.03451, 47, -40.03, 4.49, 0, 3, 32, 25.51, 9.49, 0.59619, 33, -3.95, 8.74, 0.40381, 47, -35.16, 11.49, 0, 3, 32, 29.42, 6.78, 0.31604, 33, 0.82, 8.58, 0.68396, 47, -30.43, 10.96, 0, 2, 32, 36.73, 6.17, 0.11917, 33, 7.29, 12.03, 0.88083, 4, 31, 47.92, -34.85, 0, 32, 42.37, 0.7, 0.01649, 33, 14.99, 10.47, 0.97399, 47, -16.15, 11.76, 0.00952, 2, 32, 36.81, 11.22, 0.24375, 33, 4.63, 16.31, 0.75625, 2, 32, 33.45, 13.98, 0.38962, 33, 0.31, 16.81, 0.61038, 3, 32, 32.66, 19.22, 0.51949, 33, -3.19, 20.79, 0.48051, 47, -33.49, 23.44, 0, 3, 32, 27.53, 24.56, 0.70396, 33, -10.39, 22.51, 0.29604, 47, -40.54, 25.7, 0, 4, 31, 62.05, 0.97, 0.00595, 32, 20.42, 32.34, 0.86166, 33, -20.58, 25.21, 0.13239, 47, -50.5, 29.17, 0, 4, 31, 57.7, 1.03, 0.00903, 32, 17.96, 28.75, 0.87124, 33, -20.71, 20.86, 0.11973, 47, -50.96, 24.85, 0, 4, 31, 51.17, -4.1, 0.00307, 32, 18.59, 20.48, 0.85245, 33, -15.7, 14.25, 0.14448, 47, -46.47, 17.87, 0, 3, 32, 23.32, 15.77, 0.73542, 33, -9.18, 12.85, 0.26458, 47, -40.07, 15.98, 0, 3, 32, 26.24, 13.47, 0.61602, 33, -5.48, 12.49, 0.38398, 47, -36.41, 15.34, 0, 2, 33, 19.87, 3.8, 0.98866, 47, -11.8, 4.74, 0.01134, 2, 33, 18.45, -2.29, 0.89903, 48, -6.04, 9.99, 0.10097, 5, 30, 45.97, -44.2, 0.00209, 31, 31.52, -32.73, 0.00567, 32, 31.5, -11.76, 0.02766, 33, 12.6, -5.89, 0.70654, 48, -10.12, 4.46, 0.25804, 3, 32, 23.14, 0.82, 0.73791, 33, -1.25, 0.18, 0.26209, 47, -33.13, 2.74, 0, 5, 30, 53, -43.8, 1e-05, 31, 37.37, -28.82, 4e-05, 33, 8.78, 0.03, 0.99965, 47, -23.14, 1.83, 0, 48, -15.88, 8.53, 0.0003], "hull": 33}}, "head2": {"head": {"type": "mesh", "uvs": [0.59294, 0.06761, 0.64683, 0.15266, 0.66756, 0.21961, 0.69036, 0.18885, 0.71523, 0.2178, 0.73181, 0.2739, 0.77534, 0.3119, 0.82508, 0.34085, 0.845, 0.40245, 0.85166, 0.4396, 0.81742, 0.51388, 0.76415, 0.56671, 0.73561, 0.57826, 0.6871, 0.57248, 0.71754, 0.7161, 0.72705, 0.83166, 0.70708, 0.87293, 0.64144, 0.89191, 0.43123, 0.8754, 0.1763, 0.85147, 0.09548, 0.83123, 0.04781, 0.72266, 0.04159, 0.62314, 0.0789, 0.48199, 0.15974, 0.37704, 0.36286, 0.27571, 0.37737, 0.22142, 0.36079, 0.14542, 0.32763, 0.09114, 0.26545, 0.05314, 0.34421, 0.0278, 0.48516, 0, 0.54112, 0, 0.43126, 0.05133, 0.50174, 0.11466, 0.53076, 0.16171, 0.53697, 0.10742, 0.5121, 0.0459, 0.81908, 0.35706, 0.8049, 0.36201, 0.80868, 0.38347, 0.39127, 0.2968, 0.3304, 0.37357, 0.2971, 0.5081, 0.30376, 0.64925, 0.34086, 0.74417, 0.43884, 0.83166, 0.46262, 0.84734, 0.60054, 0.55185, 0.58152, 0.59394, 0.58342, 0.65915, 0.60244, 0.74086, 0.6462, 0.79782, 0.69757, 0.83991, 0.66332, 0.53782, 0.70613, 0.54525, 0.5311, 0.51801, 0.48449, 0.44785, 0.47593, 0.38099, 0.62813, 0.47922, 0.61291, 0.4396, 0.70518, 0.48995, 0.38842, 0.36696, 0.37891, 0.4462, 0.39698, 0.57001, 0.44074, 0.68226, 0.51588, 0.76315, 0.41291, 0.40955, 0.45905, 0.42166, 0.50949, 0.36858, 0.53739, 0.40024, 0.54705, 0.42911, 0.59534, 0.40955, 0.605, 0.36579, 0.64471, 0.33692, 0.67154, 0.34623, 0.71554, 0.34064, 0.75525, 0.36206, 0.7091, 0.31457, 0.68549, 0.30433, 0.67369, 0.27825, 0.63612, 0.25683, 0.58354, 0.22517, 0.57388, 0.24659, 0.58891, 0.2885, 0.62325, 0.30712, 0.64364, 0.3155, 0.7692, 0.40397, 0.74988, 0.4319, 0.7091, 0.43935, 0.64256, 0.38441, 0.69944, 0.40304], "triangles": [61, 59, 89, 54, 59, 61, 88, 55, 61, 54, 61, 55, 48, 59, 54, 88, 10, 55, 10, 11, 55, 13, 54, 55, 12, 55, 11, 13, 55, 12, 13, 48, 54, 9, 40, 8, 10, 88, 9, 80, 4, 5, 79, 80, 5, 78, 79, 5, 78, 5, 6, 76, 78, 6, 79, 75, 86, 75, 79, 78, 75, 78, 76, 7, 39, 6, 38, 39, 7, 77, 76, 6, 77, 6, 39, 40, 39, 38, 38, 7, 8, 40, 38, 8, 91, 75, 76, 91, 76, 77, 90, 75, 91, 87, 77, 39, 87, 39, 40, 91, 77, 87, 88, 91, 87, 89, 91, 88, 89, 60, 91, 40, 9, 87, 9, 88, 87, 89, 88, 61, 37, 31, 32, 33, 30, 31, 33, 31, 37, 37, 32, 0, 28, 29, 30, 28, 30, 33, 36, 37, 0, 34, 33, 37, 34, 37, 36, 27, 28, 33, 27, 33, 34, 36, 0, 1, 35, 34, 36, 35, 36, 1, 2, 3, 4, 26, 27, 34, 26, 34, 35, 82, 35, 1, 82, 1, 2, 83, 35, 82, 81, 82, 2, 83, 82, 81, 80, 2, 4, 81, 2, 80, 84, 83, 81, 41, 26, 35, 41, 35, 83, 25, 26, 41, 85, 84, 81, 86, 85, 81, 80, 86, 81, 79, 86, 80, 75, 74, 86, 73, 84, 85, 74, 73, 85, 74, 85, 86, 69, 41, 83, 69, 83, 84, 69, 84, 73, 69, 58, 41, 90, 73, 74, 90, 74, 75, 70, 69, 73, 72, 70, 73, 72, 73, 90, 71, 70, 72, 60, 72, 90, 70, 57, 58, 70, 58, 69, 57, 70, 71, 60, 89, 59, 91, 60, 90, 60, 71, 72, 59, 71, 60, 71, 59, 56, 41, 42, 25, 58, 62, 41, 62, 42, 41, 42, 24, 25, 67, 62, 58, 68, 67, 58, 63, 42, 62, 63, 62, 67, 68, 58, 57, 43, 24, 42, 43, 42, 63, 23, 24, 43, 56, 57, 71, 48, 56, 59, 63, 57, 64, 68, 63, 67, 57, 63, 68, 64, 57, 56, 43, 63, 64, 49, 56, 48, 44, 43, 64, 13, 50, 49, 65, 64, 56, 65, 56, 49, 65, 49, 50, 44, 64, 65, 44, 21, 22, 49, 48, 13, 14, 50, 13, 14, 51, 50, 45, 44, 65, 66, 65, 50, 66, 50, 51, 52, 51, 14, 44, 22, 43, 43, 22, 23, 44, 19, 21, 19, 20, 21, 52, 14, 15, 46, 45, 65, 46, 65, 66, 53, 52, 15, 47, 46, 66, 45, 19, 44, 16, 53, 15, 18, 45, 46, 18, 46, 47, 19, 45, 18, 52, 66, 51, 17, 52, 53, 17, 53, 16, 17, 66, 52, 47, 66, 17, 18, 47, 17], "vertices": [3, 36, 28.63, 61, 0.96624, 37, -29.18, 53.76, 0.03376, 49, -56.9, 58.3, 0, 3, 36, 33.68, 43.34, 0.87583, 37, -15.38, 41.64, 0.12417, 49, -44.06, 45.16, 0, 3, 36, 34.16, 30.24, 0.65887, 37, -7.89, 30.88, 0.34113, 49, -37.41, 33.86, 0, 3, 36, 39.14, 35.06, 0.58581, 37, -6.31, 37.63, 0.41419, 49, -35.32, 40.47, 0, 3, 36, 41.91, 28.81, 0.55744, 37, -0.6, 33.86, 0.44256, 49, -29.92, 36.28, 0, 2, 36, 42.19, 17.86, 0.34072, 37, 5.56, 24.81, 0.65928, 4, 35, 57.96, -34.62, 0, 36, 47.75, 9.18, 0.07438, 37, 14.93, 20.52, 0.91525, 49, -15.45, 21.79, 0.01037, 4, 35, 56, -45.39, 0, 36, 55.62, 1.58, 0.00263, 37, 25.66, 18.38, 0.92213, 49, -4.91, 18.83, 0.07524, 4, 35, 47.57, -58.99, 0, 36, 62.25, -12.99, 0, 37, 39.12, 9.71, 0.56035, 49, 7.84, 9.16, 0.43965, 2, 37, 52.16, 4.95, 0.01105, 49, 20.44, 3.42, 0.98895, 6, 34, 28.3, -61.67, 0.00014, 35, 25.17, -56.75, 3e-05, 36, 47.95, -30.38, 0, 37, 36.5, -12.65, 0.01537, 49, 3.53, -12.92, 0.13587, 50, 14.57, 7.14, 0.84859, 6, 34, 20.69, -49.1, 0.03135, 35, 12.23, -49.79, 0.00666, 36, 34.98, -37.28, 0.00128, 37, 29.32, -25.47, 9e-05, 49, -4.61, -25.16, 0.00244, 50, 12.71, -7.43, 0.95818, 6, 34, 19.34, -44.08, 0.07285, 35, 8.52, -46.15, 0.01675, 36, 29.89, -38.34, 0.00691, 37, 25.61, -29.11, 2e-05, 49, -8.58, -28.51, 0.00172, 50, 10.64, -12.2, 0.90175, 5, 34, 21.79, -36.36, 0.27468, 35, 6.7, -38.25, 0.05504, 36, 22.32, -35.47, 0.02921, 49, -16.6, -29.59, 0.0006, 50, 3.92, -16.73, 0.64046, 5, 34, -5.81, -36.69, 0.85135, 35, -16.91, -52.55, 0.0035, 36, 21.1, -63.05, 1e-05, 49, -4.57, -54.43, 0.00017, 50, 25.73, -33.64, 0.14498, 3, 34, -27.6, -34.53, 0.9491, 49, 2.73, -75.07, 9e-05, 50, 41.44, -48.9, 0.05081, 3, 34, -34.73, -29.95, 0.95945, 49, 1.62, -83.47, 8e-05, 50, 44.17, -56.91, 0.04047, 3, 34, -36.43, -18.67, 0.97096, 49, -7.87, -89.8, 6e-05, 50, 38.48, -66.8, 0.02898, 3, 34, -27.46, 14.98, 0.99998, 49, -42.14, -95.99, 0, 50, 10.52, -87.56, 2e-05, 2, 34, -15.85, 55.67, 0.94034, 35, -72.44, 21.92, 0.05966, 2, 34, -9.82, 68.16, 0.91203, 35, -73.58, 35.75, 0.08797, 2, 34, 11.74, 72.42, 0.8289, 35, -57.18, 50.37, 0.1711, 2, 34, 30.45, 70.24, 0.70511, 35, -39.95, 57.98, 0.29489, 2, 34, 55.69, 59.64, 0.45822, 35, -12.82, 61.67, 0.54178, 3, 34, 72.98, 43.12, 0.24407, 35, 10.45, 56.21, 0.73398, 36, -54.17, 20.09, 0.02195, 3, 34, 86.15, 6.84, 0.00583, 35, 40.23, 31.64, 0.42013, 36, -17.21, 31.22, 0.57403, 5, 34, 95.86, 2.74, 1e-05, 35, 50.67, 33.03, 0.16692, 36, -12.57, 40.68, 0.83295, 37, -52.84, 14.39, 0.00011, 49, -83.48, 20.84, 0, 4, 35, 63.14, 40.68, 0.03797, 36, -12, 55.29, 0.96202, 37, -60.27, 26.98, 1e-05, 49, -89.93, 33.96, 0, 3, 35, 70.8, 49.43, 0.00586, 36, -15.03, 66.52, 0.99414, 49, -97.93, 42.4, 0, 2, 36, -23.41, 75.82, 1, 49, -109.72, 46.61, 0, 3, 35, 82.96, 51.1, 0.0007, 36, -9.67, 77.56, 0.9993, 49, -98.46, 54.67, 0, 3, 36, 14.17, 77.46, 0.99447, 37, -50.24, 59.78, 0.00553, 49, -77.43, 65.9, 0, 3, 36, 23.17, 75.38, 0.99, 37, -41.55, 62.9, 0.01, 49, -68.53, 68.35, 0, 4, 35, 83.89, 36.1, 0.0025, 36, 3.33, 70, 0.99537, 37, -55.33, 47.65, 0.00213, 49, -83.43, 54.19, 0, 4, 35, 76.81, 20.98, 0.00939, 36, 11.96, 55.72, 0.96967, 37, -40.34, 40.31, 0.02094, 49, -69.05, 45.73, 0, 4, 35, 70.18, 13.36, 0.01393, 36, 14.63, 45.98, 0.93976, 37, -32.83, 33.56, 0.04631, 49, -62.07, 38.43, 0, 4, 35, 80.14, 16.03, 0.0037, 36, 17.94, 55.75, 0.96435, 37, -35.33, 43.56, 0.03195, 49, -63.81, 48.59, 0, 4, 35, 89.57, 23.98, 0.00057, 36, 16.55, 68, 0.98825, 37, -43.12, 53.12, 0.01119, 49, -70.85, 58.72, 0, 4, 35, 52.89, -45.98, 0, 36, 54.38, -1.35, 0.00095, 37, 26.2, 15.25, 0.89901, 49, -4.61, 15.67, 0.10005, 4, 35, 51.13, -43.87, 0, 36, 51.65, -1.63, 0.00179, 37, 24.06, 13.53, 0.9124, 49, -6.88, 14.12, 0.08582, 3, 35, 47.8, -47, 0, 37, 27.14, 10.15, 0.85028, 49, -4.06, 10.51, 0.14972, 3, 34, 81.43, 2.9, 0.0039, 35, 38.16, 25.85, 0.41909, 36, -13.54, 26.28, 0.57701, 3, 34, 68.84, 15.26, 0.0609, 35, 21.03, 30.11, 0.76484, 36, -26.59, 14.4, 0.17426, 3, 34, 44.71, 25, 0.4025, 35, -4.69, 26.25, 0.59603, 36, -37.66, -9.14, 0.00147, 2, 34, 18.24, 28.45, 0.87068, 35, -29.25, 15.78, 0.12932, 2, 34, -0.48, 25.46, 0.97869, 35, -43.87, 3.71, 0.02131, 3, 34, -19.52, 12.34, 0.99995, 49, -43.12, -87.68, 0, 50, 5.95, -80.55, 4e-05, 3, 34, -23.11, 8.98, 0.99945, 49, -38.56, -89.5, 1e-05, 50, 10.85, -80.15, 0.00055, 6, 34, 28.06, -22.94, 0.37638, 35, 5.29, -23.51, 0.19617, 36, 9.27, -28.46, 0.08671, 37, 2.92, -31.95, 0.00257, 49, -31.42, -29.61, 0.00016, 50, -9.34, -23.33, 0.33801, 5, 34, 20.75, -18.5, 0.62946, 35, -3.26, -23.39, 0.11153, 36, 4.43, -35.5, 0.02961, 49, -32.33, -38.12, 0.00015, 50, -6.38, -31.35, 0.22925, 5, 34, 8.55, -16.71, 0.83742, 35, -14.67, -28.04, 0.02089, 36, 1.96, -47.58, 0.0035, 49, -28.76, -49.92, 0.00013, 50, 2.05, -40.34, 0.13806, 4, 34, -7.2, -17.18, 0.92791, 35, -28.01, -36.44, 0.0007, 49, -21.64, -63.98, 9e-05, 50, 14.68, -49.78, 0.0713, 3, 34, -19.03, -22.47, 0.94583, 49, -11.83, -72.44, 9e-05, 50, 27.23, -53.01, 0.05408, 3, 34, -28.31, -29.47, 0.95386, 49, -1.55, -77.86, 8e-05, 50, 38.85, -53.3, 0.04605, 6, 34, 28.91, -33.6, 0.19726, 35, 11.44, -32.26, 0.08674, 36, 19.96, -28.2, 0.07256, 37, 11.78, -25.96, 0.00998, 49, -22.13, -24.31, 0.00029, 50, -3.37, -14.45, 0.63316, 5, 34, 26.33, -40.33, 0.11212, 35, 12.63, -39.37, 0.03374, 36, 26.54, -31.16, 0.02375, 49, -14.94, -23.79, 0.00062, 50, 2.84, -10.79, 0.82977, 6, 34, 36.31, -12.74, 0.23395, 35, 7.22, -10.53, 0.58719, 36, -0.46, -19.65, 0.08173, 37, -10.02, -29.8, 7e-05, 49, -44.16, -26.49, 3e-05, 50, -22.15, -26.18, 0.09703, 1, 35, 16.9, 1.36, 1, 2, 35, 28.22, 7.15, 0.41745, 36, -3.5, 7.63, 0.58255, 6, 34, 40.81, -29.76, 0.07593, 35, 19.74, -22.91, 0.14752, 36, 16.79, -16.1, 0.30797, 37, 2.57, -17.49, 0.116, 49, -30.67, -15.17, 3e-05, 50, -15.08, -10.05, 0.35255, 6, 34, 48.62, -28.55, 0.0193, 35, 25.86, -17.91, 0.08797, 36, 16.03, -8.24, 0.64827, 37, -2.33, -11.29, 0.11747, 49, -35.08, -8.62, 0, 50, -21.94, -6.14, 0.12698, 5, 34, 36.66, -41.94, 0.02511, 35, 22.35, -35.51, 0.02128, 36, 28.72, -20.94, 0.04259, 37, 15.22, -15.11, 0.09035, 50, -4.24, -3.1, 0.82066, 3, 34, 68.44, 5.62, 0.0153, 35, 25.59, 21.59, 0.68378, 36, -16.98, 13.46, 0.30092, 3, 34, 53.95, 9.71, 0.07946, 35, 11.03, 17.76, 0.88485, 36, -21.87, -0.78, 0.03569, 2, 34, 30.38, 10.74, 0.82412, 35, -9.8, 6.69, 0.17588, 2, 34, 8.25, 7.23, 0.99946, 35, -27.09, -7.57, 0.00054, 3, 34, -8.92, -2.39, 0.99098, 49, -34.3, -71.82, 2e-05, 50, 6.82, -62.43, 0.009, 3, 34, 59.82, 3, 0.01244, 35, 19.49, 14.97, 0.8401, 36, -14.85, 4.71, 0.14746, 2, 35, 20.05, 7.04, 0.89745, 36, -7.94, 0.77, 0.10255, 3, 35, 32.37, 2.8, 0.10863, 36, 2.42, 8.67, 0.89137, 49, -55.09, -0.2, 0, 1, 36, 5.56, 1.81, 1, 6, 34, 52.42, -18.18, 0.0043, 35, 23.87, -7.04, 0.2087, 36, 5.89, -3.87, 0.76975, 37, -13.22, -13.1, 0.00063, 49, -46.08, -9.59, 0, 50, -31.37, -11.89, 0.01662, 6, 34, 54.71, -26.66, 0.00203, 35, 30.14, -13.19, 0.01446, 36, 14.48, -2.06, 0.95616, 37, -6.97, -6.93, 0.01237, 49, -39.38, -3.91, 0, 50, -27.88, -3.83, 0.01498, 3, 36, 17.89, 5.65, 0.96549, 37, -8.27, 1.39, 0.03451, 49, -40.03, 4.49, 0, 3, 36, 25.51, 9.49, 0.59619, 37, -3.95, 8.74, 0.40381, 49, -35.16, 11.49, 0, 3, 36, 29.42, 6.78, 0.31604, 37, 0.82, 8.58, 0.68396, 49, -30.43, 10.96, 0, 2, 36, 36.73, 6.17, 0.11917, 37, 7.29, 12.03, 0.88083, 4, 35, 47.92, -34.85, 0, 36, 42.37, 0.7, 0.01649, 37, 14.99, 10.47, 0.97399, 49, -16.15, 11.76, 0.00952, 2, 36, 36.81, 11.22, 0.24375, 37, 4.63, 16.31, 0.75625, 2, 36, 33.45, 13.98, 0.38962, 37, 0.31, 16.81, 0.61038, 3, 36, 32.66, 19.22, 0.51949, 37, -3.19, 20.79, 0.48051, 49, -33.49, 23.44, 0, 3, 36, 27.53, 24.56, 0.70396, 37, -10.39, 22.51, 0.29604, 49, -40.54, 25.7, 0, 4, 35, 62.05, 0.97, 0.00595, 36, 20.42, 32.34, 0.86166, 37, -20.58, 25.21, 0.13239, 49, -50.5, 29.17, 0, 4, 35, 57.7, 1.03, 0.00903, 36, 17.96, 28.75, 0.87124, 37, -20.71, 20.86, 0.11973, 49, -50.96, 24.85, 0, 4, 35, 51.17, -4.1, 0.00307, 36, 18.59, 20.48, 0.85245, 37, -15.7, 14.25, 0.14448, 49, -46.47, 17.87, 0, 3, 36, 23.32, 15.77, 0.73542, 37, -9.18, 12.85, 0.26458, 49, -40.07, 15.98, 0, 3, 36, 26.24, 13.47, 0.61602, 37, -5.48, 12.49, 0.38398, 49, -36.41, 15.34, 0, 2, 37, 19.87, 3.8, 0.98866, 49, -11.8, 4.74, 0.01134, 2, 37, 18.45, -2.29, 0.89903, 50, -6.04, 9.99, 0.10097, 5, 34, 45.97, -44.2, 0.00209, 35, 31.52, -32.73, 0.00567, 36, 31.5, -11.76, 0.02766, 37, 12.6, -5.89, 0.70654, 50, -10.12, 4.46, 0.25804, 3, 36, 23.14, 0.82, 0.73791, 37, -1.25, 0.18, 0.26209, 49, -33.13, 2.74, 0, 5, 34, 53, -43.8, 1e-05, 35, 37.37, -28.82, 4e-05, 37, 8.78, 0.03, 0.99965, 49, -23.14, 1.83, 0, 50, -15.88, 8.53, 0.0003], "hull": 33}}, "l": {"l": {"x": -18.05, "y": -1.07, "rotation": -76.26, "width": 611, "height": 589}}, "l2": {"l": {"x": -3.9, "y": 3.88, "rotation": -113.45, "width": 611, "height": 589}}, "l3": {"l": {"x": -3.9, "y": 3.88, "rotation": -113.45, "width": 611, "height": 589}}, "light": {"light": {"width": 177, "height": 159}}, "light2": {"light": {"width": 177, "height": 159}}, "light3": {"light": {"width": 177, "height": 159}}, "light4": {"light": {"width": 177, "height": 159}}, "light5": {"light": {"width": 177, "height": 159}}, "m1": {"m1": {"x": 19.23, "y": -5.58, "rotation": 13.83, "width": 55, "height": 39}}, "m2": {"m2": {"x": 11.79, "y": -2.08, "rotation": 41.73, "width": 62, "height": 45}}, "m3": {"m1": {"x": 19.23, "y": -5.58, "rotation": 13.83, "width": 55, "height": 39}}, "m4": {"m2": {"x": 11.79, "y": -2.08, "rotation": 41.73, "width": 62, "height": 45}}, "nen tien": {"nentien": {"x": 131.59, "y": -1.99, "rotation": -0.21, "width": 421, "height": 66}}, "nohu": {"nohu": {"path": "nohu", "x": 28.85, "y": -51.99, "rotation": -91.68, "width": 445, "height": 250}}, "pants": {"pants": {"x": 37.75, "y": -28.13, "rotation": 39.54, "width": 205, "height": 191}}, "pants2": {"pants": {"x": 37.75, "y": -28.13, "rotation": 39.54, "width": 205, "height": 191}}, "rb": {"rb": {"type": "mesh", "uvs": [0.61388, 0.03143, 0.65352, 0.03143, 0.72002, 0.02983, 0.77245, 0.07149, 0.82361, 0.11316, 0.86197, 0.12598, 0.89522, 0.10675, 0.91185, 0.08431, 0.94126, 0.09553, 1, 0.142, 1, 0.18206, 0.96556, 0.21411, 0.91952, 0.21892, 0.86069, 0.20289, 0.83, 0.17725, 0.74176, 0.12758, 0.71874, 0.11155, 0.67782, 0.142, 0.61771, 0.1404, 0.56272, 0.12437, 0.52303, 0.06991, 0.5397, 0.14265, 0.58466, 0.22714, 0.62756, 0.26554, 0.6909, 0.24506, 0.78081, 0.25018, 0.85641, 0.29371, 0.90545, 0.38587, 0.90953, 0.53182, 0.87276, 0.58045, 0.87889, 0.6291, 0.84824, 0.71359, 0.89523, 0.69566, 0.90545, 0.94145, 0.82985, 1, 0.77264, 1, 0.69295, 0.93889, 0.66639, 0.88512, 0.6766, 0.80319, 0.57648, 0.87488, 0.52949, 0.89024, 0.48862, 0.77503, 0.49679, 0.71102, 0.54992, 0.70334, 0.61326, 0.67774, 0.63778, 0.63934, 0.62143, 0.57533, 0.56831, 0.49597, 0.47841, 0.44732, 0.42324, 0.38075, 0.42528, 0.43708, 0.3885, 0.52669, 0.26999, 0.64702, 0.15148, 0.72383, 0.09018, 0.83392, 0.06771, 0.91584, 0, 0.8544, 0, 0.73663, 0.04932, 0.54717, 0.176, 0.48828, 0.26182, 0.39356, 0.27817, 0.27834, 0.2516, 0.08121, 0.28634, 0, 0.50701, 0, 0.53075, 0, 0.57177, 0], "triangles": [38, 39, 43, 38, 43, 44, 41, 43, 39, 43, 41, 42, 40, 41, 39, 44, 45, 38, 24, 46, 47, 45, 46, 29, 11, 8, 9, 10, 11, 9, 8, 6, 7, 12, 8, 11, 12, 6, 8, 5, 6, 12, 13, 5, 12, 14, 4, 5, 15, 4, 14, 13, 14, 5, 16, 1, 2, 16, 2, 3, 15, 16, 3, 15, 3, 4, 66, 20, 65, 0, 20, 66, 0, 19, 20, 18, 0, 1, 17, 18, 1, 19, 0, 18, 16, 17, 1, 53, 58, 59, 57, 58, 53, 54, 57, 53, 56, 57, 54, 55, 56, 54, 51, 60, 50, 59, 60, 51, 52, 59, 51, 53, 59, 52, 60, 61, 49, 60, 49, 50, 20, 61, 62, 63, 20, 62, 33, 36, 38, 37, 38, 36, 33, 38, 31, 35, 36, 33, 34, 35, 33, 45, 29, 30, 31, 45, 30, 38, 45, 31, 31, 32, 33, 29, 27, 28, 29, 46, 27, 23, 24, 47, 25, 46, 24, 46, 25, 27, 26, 27, 25, 23, 49, 22, 23, 48, 49, 47, 48, 23, 20, 64, 65, 21, 61, 20, 20, 63, 64, 49, 21, 22, 49, 61, 21], "vertices": [3, 4, -1.49, 45.79, 0.10296, 59, -3.7, 8.38, 0.89704, 61, -36.28, -3.17, 0, 3, 4, 2.01, 53.26, 0.00082, 59, 4.46, 7.16, 0.99917, 61, -28.88, 0.46, 1e-05, 3, 59, 18.18, 5.37, 0.2511, 60, 0.72, 6.97, 0.74867, 61, -16.58, 6.8, 0.00022, 2, 60, 13.55, 5.44, 0.87883, 61, -3.75, 5.4, 0.12117, 1, 61, 8.85, 3.88, 1, 2, 61, 16.95, 5.49, 0.68309, 62, -0.42, 7.09, 0.31691, 2, 61, 21.75, 11.41, 0.03887, 62, 6.94, 9.05, 0.96113, 1, 62, 10.99, 12.13, 1, 1, 62, 16.7, 9.25, 1, 1, 62, 27.41, -0.45, 1, 1, 62, 26.27, -7, 1, 1, 62, 18.3, -11.01, 1, 2, 60, 51.7, -3.44, 1e-05, 62, 8.73, -10.16, 0.99999, 3, 60, 39.51, -6.32, 0.00038, 61, 22.34, -6.09, 0.50305, 62, -2.87, -5.44, 0.49657, 2, 60, 31.92, -5.24, 0.00107, 61, 14.74, -5.08, 0.99893, 4, 59, 20.24, -11.35, 0.00056, 60, 11.8, -5.72, 0.93644, 61, -5.37, -5.77, 0.063, 62, -25.1, 11.12, 0, 2, 59, 15.9, -8.01, 0.1852, 60, 6.34, -5.38, 0.8148, 2, 59, 6.73, -11.74, 0.83351, 60, 0.84, -13.61, 0.16649, 3, 4, 15.23, 38.85, 0.02957, 59, -5.59, -9.63, 0.96925, 60, -10.56, -18.77, 0.00117, 3, 4, 7.97, 29.62, 0.18804, 5, -2.89, 37.55, 0.00026, 59, -16.51, -5.3, 0.8117, 4, 4, -3.72, 25.97, 0.57965, 5, -14.63, 41.02, 0.00832, 6, -18.22, 58, 5e-05, 59, -23.33, 4.87, 0.41199, 5, 4, 8.69, 23.99, 0.78711, 5, -5.42, 32.48, 0.10847, 6, -14.54, 45.99, 0.00999, 59, -21.69, -7.59, 0.09443, 61, -41.99, -26.55, 0, 5, 4, 25.36, 26.51, 0.42888, 5, 9.84, 25.31, 0.42693, 6, -4.94, 32.14, 0.13561, 59, -14.53, -22.85, 0.00857, 61, -27.41, -35.01, 0, 5, 4, 34.92, 31.9, 0.12036, 5, 20.78, 24.48, 0.41819, 6, 4.1, 25.92, 0.46087, 59, -6.64, -30.47, 0.00058, 61, -16.59, -36.8, 0, 4, 4, 37.42, 45.27, 0.01718, 5, 30.29, 34.2, 0.16931, 6, 17.21, 29.56, 0.81352, 61, -6.27, -27.94, 0, 5, 4, 46.12, 61.85, 0.00012, 5, 46.74, 43.15, 0.03199, 6, 35.92, 29.04, 0.92582, 7, -26.97, 12.38, 0.04207, 61, 10.89, -20.46, 0, 4, 5, 63.93, 45.1, 0.00138, 6, 51.77, 22.1, 0.6331, 7, -14.68, 24.57, 0.36552, 61, 28.19, -20.01, 0, 2, 6, 62.24, 6.98, 0.19582, 7, 3.22, 28.74, 0.80418, 2, 7, 26.21, 21.03, 0.98078, 8, -18.13, 11.63, 0.01922, 2, 7, 31.08, 11.04, 0.72187, 8, -7.63, 7.98, 0.27813, 3, 7, 39.09, 9.39, 0.23395, 8, -0.79, 12.46, 0.76403, 9, -24.46, 27.3, 0.00202, 2, 8, 14.62, 12.42, 0.8613, 9, -10.91, 19.96, 0.1387, 2, 8, 7.89, 20.1, 0.77247, 9, -13.2, 29.92, 0.22753, 2, 8, 44.21, 38.81, 0.08395, 9, 27.65, 29.2, 0.91605, 2, 8, 59.53, 28.48, 0.00689, 9, 36.25, 12.84, 0.99311, 1, 9, 35.43, 0.97, 1, 3, 9, 24.15, -14.86, 0.95403, 61, 44.9, -131.13, 0, 65, 9.17, 32.97, 0.04597, 4, 8, 56.13, -10.36, 0.0005, 9, 14.86, -19.75, 0.88053, 61, 36, -125.55, 0, 65, 10.35, 22.54, 0.11897, 5, 8, 42.85, -14.01, 0.06374, 9, 1.44, -16.69, 0.3466, 61, 31.91, -112.41, 0, 64, 30.68, 6.64, 0.06135, 65, 2.62, 11.15, 0.52832, 1, 65, 26.53, 13, 1, 1, 65, 36.46, 11.13, 1, 2, 61, -5.24, -125.45, 0, 65, 35.96, -9.79, 1, 3, 61, -8.4, -115.17, 0, 64, 31.43, -33.76, 0.00179, 65, 29.88, -18.67, 0.99821, 3, 61, 0.95, -109.15, 0, 64, 25.89, -24.12, 0.04537, 65, 19.35, -15.1, 0.95463, 3, 63, 27.28, -21.19, 0.03718, 64, 16.77, -13.7, 0.45606, 65, 5.62, -13.31, 0.50676, 4, 5, 54.7, -27.52, 0.00047, 63, 22.5, -14.57, 0.28623, 64, 8.9, -11.54, 0.6377, 65, -1.71, -16.89, 0.07559, 5, 5, 46.3, -20.19, 0.02869, 6, 3.74, -25.52, 0.02141, 63, 11.35, -15.01, 0.77347, 64, 0.49, -18.87, 0.17482, 65, -3.18, -27.95, 0.00161, 4, 5, 30.02, -14.63, 0.56498, 6, -7.54, -12.54, 0.14959, 63, -4.29, -22.15, 0.28056, 64, -7.24, -34.23, 0.00486, 4, 4, 49.09, -9, 0.0093, 5, 9.84, -17.4, 0.93404, 55, 20.39, 30.52, 0.05435, 63, -17.07, -38.01, 0.00231, 5, 4, 34.22, -14.7, 0.22395, 5, -5.69, -13.88, 0.24144, 54, 31.29, 14.99, 0.00467, 55, 11.38, 17.38, 0.47502, 56, -17.06, 9.76, 0.05492, 4, 4, 42.87, -18.28, 0.07235, 5, -0.49, -21.66, 0.06048, 55, 20.53, 19.34, 0.52361, 56, -10.38, 16.31, 0.34356, 5, 4, 53.1, -31.52, 0.00426, 5, 0.66, -38.35, 0.003, 55, 36.46, 14.24, 0.11398, 57, -22.62, 12.87, 0.00668, 56, 5.8, 20.54, 0.87208, 3, 57, 8.25, 20.2, 0.86061, 56, 37.13, 15.51, 0.11035, 58, -14.47, 24.98, 0.02904, 2, 57, 35.99, 21.01, 0.1911, 58, 12.48, 18.34, 0.8089, 2, 57, 55.4, 31.96, 0.0004, 58, 34.11, 23.7, 0.9996, 1, 58, 46.58, 30.86, 1, 1, 58, 50.46, 13.91, 1, 1, 58, 37.59, -0.8, 1, 3, 57, 42.44, -14.62, 0.06561, 58, 9.16, -17.72, 0.93439, 61, -103.93, -131.79, 0, 3, 57, 14.46, -12.02, 0.93421, 56, 30.54, -16.63, 0.06579, 61, -84.59, -111.4, 0, 5, 54, 32.74, -18.62, 0.02058, 55, 18.99, -15.39, 0.30511, 57, -8.44, -18.46, 0.02492, 56, 6.92, -13.83, 0.64939, 61, -75.51, -89.42, 0, 4, 54, 13.69, -14.84, 0.6167, 55, -0.43, -15.18, 0.36724, 56, -9.59, -24.06, 0.01606, 61, -80.89, -70.75, 0, 2, 4, -25.95, -25.96, 0.14659, 54, -19.14, -19.71, 0.85341, 3, 4, -35.1, -13.7, 0.29607, 54, -32.47, -12.21, 0.70233, 59, -70.3, 23.65, 0.00159, 4, 4, -15.64, 27.87, 0.61254, 5, -23.49, 49.22, 0.00029, 54, -31.56, 33.68, 0.00329, 59, -24.9, 16.84, 0.38388, 3, 4, -13.54, 32.34, 0.50225, 5, -19.26, 51.78, 0.00012, 59, -20.02, 16.11, 0.49764, 3, 4, -9.93, 40.07, 0.31904, 59, -11.58, 14.84, 0.68096, 61, -46.44, -2.35, 0], "hull": 67}}, "rb2": {"rb": {"type": "mesh", "uvs": [0.61388, 0.03143, 0.65352, 0.03143, 0.72002, 0.02983, 0.77245, 0.07149, 0.82361, 0.11316, 0.86197, 0.12598, 0.89522, 0.10675, 0.91185, 0.08431, 0.94126, 0.09553, 1, 0.142, 1, 0.18206, 0.96556, 0.21411, 0.91952, 0.21892, 0.86069, 0.20289, 0.83, 0.17725, 0.74176, 0.12758, 0.71874, 0.11155, 0.67782, 0.142, 0.61771, 0.1404, 0.56272, 0.12437, 0.52303, 0.06991, 0.5397, 0.14265, 0.58466, 0.22714, 0.62756, 0.26554, 0.6909, 0.24506, 0.78081, 0.25018, 0.85641, 0.29371, 0.90545, 0.38587, 0.90953, 0.53182, 0.87276, 0.58045, 0.87889, 0.6291, 0.84824, 0.71359, 0.89523, 0.69566, 0.90545, 0.94145, 0.82985, 1, 0.77264, 1, 0.69295, 0.93889, 0.66639, 0.88512, 0.6766, 0.80319, 0.57648, 0.87488, 0.52949, 0.89024, 0.48862, 0.77503, 0.49679, 0.71102, 0.54992, 0.70334, 0.61326, 0.67774, 0.63778, 0.63934, 0.62143, 0.57533, 0.56831, 0.49597, 0.47841, 0.44732, 0.42324, 0.38075, 0.42528, 0.43708, 0.3885, 0.52669, 0.26999, 0.64702, 0.15148, 0.72383, 0.09018, 0.83392, 0.06771, 0.91584, 0, 0.8544, 0, 0.73663, 0.04932, 0.54717, 0.176, 0.48828, 0.26182, 0.39356, 0.27817, 0.27834, 0.2516, 0.08121, 0.28634, 0, 0.50701, 0, 0.53075, 0, 0.57177, 0], "triangles": [38, 39, 43, 38, 43, 44, 41, 43, 39, 43, 41, 42, 40, 41, 39, 44, 45, 38, 24, 46, 47, 45, 46, 29, 11, 8, 9, 10, 11, 9, 8, 6, 7, 12, 8, 11, 12, 6, 8, 5, 6, 12, 13, 5, 12, 14, 4, 5, 15, 4, 14, 13, 14, 5, 16, 1, 2, 16, 2, 3, 15, 16, 3, 15, 3, 4, 66, 20, 65, 0, 20, 66, 0, 19, 20, 18, 0, 1, 17, 18, 1, 19, 0, 18, 16, 17, 1, 53, 58, 59, 57, 58, 53, 54, 57, 53, 56, 57, 54, 55, 56, 54, 51, 60, 50, 59, 60, 51, 52, 59, 51, 53, 59, 52, 60, 61, 49, 60, 49, 50, 20, 61, 62, 63, 20, 62, 33, 36, 38, 37, 38, 36, 33, 38, 31, 35, 36, 33, 34, 35, 33, 45, 29, 30, 31, 45, 30, 38, 45, 31, 31, 32, 33, 29, 27, 28, 29, 46, 27, 23, 24, 47, 25, 46, 24, 46, 25, 27, 26, 27, 25, 23, 49, 22, 23, 48, 49, 47, 48, 23, 20, 64, 65, 21, 61, 20, 20, 63, 64, 49, 21, 22, 49, 61, 21], "vertices": [3, 12, -1.49, 45.79, 0.10296, 71, -3.7, 8.38, 0.89704, 73, -36.28, -3.17, 0, 3, 12, 2.01, 53.26, 0.00082, 71, 4.46, 7.16, 0.99917, 73, -28.88, 0.46, 1e-05, 3, 71, 18.18, 5.37, 0.2511, 72, 0.72, 6.97, 0.74867, 73, -16.58, 6.8, 0.00022, 2, 72, 13.55, 5.44, 0.87883, 73, -3.75, 5.4, 0.12117, 1, 73, 8.85, 3.88, 1, 2, 73, 16.95, 5.49, 0.68309, 74, -0.42, 7.09, 0.31691, 2, 73, 21.75, 11.41, 0.03887, 74, 6.94, 9.05, 0.96113, 1, 74, 10.99, 12.13, 1, 1, 74, 16.7, 9.25, 1, 1, 74, 27.41, -0.45, 1, 1, 74, 26.27, -7, 1, 1, 74, 18.3, -11.01, 1, 2, 72, 51.7, -3.44, 1e-05, 74, 8.73, -10.16, 0.99999, 3, 72, 39.51, -6.32, 0.00038, 73, 22.34, -6.09, 0.50305, 74, -2.87, -5.44, 0.49657, 2, 72, 31.92, -5.24, 0.00107, 73, 14.74, -5.08, 0.99893, 4, 71, 20.24, -11.35, 0.00056, 72, 11.8, -5.72, 0.93644, 73, -5.37, -5.77, 0.063, 74, -25.1, 11.12, 0, 2, 71, 15.9, -8.01, 0.1852, 72, 6.34, -5.38, 0.8148, 2, 71, 6.73, -11.74, 0.83351, 72, 0.84, -13.61, 0.16649, 3, 12, 15.23, 38.85, 0.02957, 71, -5.59, -9.63, 0.96925, 72, -10.56, -18.77, 0.00117, 3, 12, 7.97, 29.62, 0.18804, 13, -2.89, 37.55, 0.00026, 71, -16.51, -5.3, 0.8117, 4, 12, -3.72, 25.97, 0.57965, 13, -14.63, 41.02, 0.00832, 14, -18.22, 58, 5e-05, 71, -23.33, 4.87, 0.41199, 5, 12, 8.69, 23.99, 0.78711, 13, -5.42, 32.48, 0.10847, 14, -14.54, 45.99, 0.00999, 71, -21.69, -7.59, 0.09443, 73, -41.99, -26.55, 0, 5, 12, 25.36, 26.51, 0.42888, 13, 9.84, 25.31, 0.42693, 14, -4.94, 32.14, 0.13561, 71, -14.53, -22.85, 0.00857, 73, -27.41, -35.01, 0, 5, 12, 34.92, 31.9, 0.12036, 13, 20.78, 24.48, 0.41819, 14, 4.1, 25.92, 0.46087, 71, -6.64, -30.47, 0.00058, 73, -16.59, -36.8, 0, 4, 12, 37.42, 45.27, 0.01718, 13, 30.29, 34.2, 0.16931, 14, 17.21, 29.56, 0.81352, 73, -6.27, -27.94, 0, 5, 12, 46.12, 61.85, 0.00012, 13, 46.74, 43.15, 0.03199, 14, 35.92, 29.04, 0.92582, 15, -26.97, 12.38, 0.04207, 73, 10.89, -20.46, 0, 4, 13, 63.93, 45.1, 0.00138, 14, 51.77, 22.1, 0.6331, 15, -14.68, 24.57, 0.36552, 73, 28.19, -20.01, 0, 2, 14, 62.24, 6.98, 0.19582, 15, 3.22, 28.74, 0.80418, 2, 15, 26.21, 21.03, 0.98078, 16, -18.13, 11.63, 0.01922, 2, 15, 31.08, 11.04, 0.72187, 16, -7.63, 7.98, 0.27813, 3, 15, 39.09, 9.39, 0.23395, 16, -0.79, 12.46, 0.76403, 17, -24.46, 27.3, 0.00202, 2, 16, 14.62, 12.42, 0.8613, 17, -10.91, 19.96, 0.1387, 2, 16, 7.89, 20.1, 0.77247, 17, -13.2, 29.92, 0.22753, 2, 16, 44.21, 38.81, 0.08395, 17, 27.65, 29.2, 0.91605, 2, 16, 59.53, 28.48, 0.00689, 17, 36.25, 12.84, 0.99311, 1, 17, 35.43, 0.97, 1, 3, 17, 24.15, -14.86, 0.95403, 73, 44.9, -131.13, 0, 77, 9.17, 32.97, 0.04597, 4, 16, 56.13, -10.36, 0.0005, 17, 14.86, -19.75, 0.88053, 73, 36, -125.55, 0, 77, 10.35, 22.54, 0.11897, 5, 16, 42.85, -14.01, 0.06374, 17, 1.44, -16.69, 0.3466, 73, 31.91, -112.41, 0, 76, 30.68, 6.64, 0.06135, 77, 2.62, 11.15, 0.52832, 1, 77, 26.53, 13, 1, 1, 77, 36.46, 11.13, 1, 2, 73, -5.24, -125.45, 0, 77, 35.96, -9.79, 1, 3, 73, -8.4, -115.17, 0, 76, 31.43, -33.76, 0.00179, 77, 29.88, -18.67, 0.99821, 3, 73, 0.95, -109.15, 0, 76, 25.89, -24.12, 0.04537, 77, 19.35, -15.1, 0.95463, 3, 75, 27.28, -21.19, 0.03718, 76, 16.77, -13.7, 0.45606, 77, 5.62, -13.31, 0.50676, 4, 13, 54.7, -27.52, 0.00047, 75, 22.5, -14.57, 0.28623, 76, 8.9, -11.54, 0.6377, 77, -1.71, -16.89, 0.07559, 5, 13, 46.3, -20.19, 0.02869, 14, 3.74, -25.52, 0.02141, 75, 11.35, -15.01, 0.77347, 76, 0.49, -18.87, 0.17482, 77, -3.18, -27.95, 0.00161, 4, 13, 30.02, -14.63, 0.56498, 14, -7.54, -12.54, 0.14959, 75, -4.29, -22.15, 0.28056, 76, -7.24, -34.23, 0.00486, 4, 12, 49.09, -9, 0.0093, 13, 9.84, -17.4, 0.93404, 67, 20.39, 30.52, 0.05435, 75, -17.07, -38.01, 0.00231, 5, 12, 34.22, -14.7, 0.22395, 13, -5.69, -13.88, 0.24144, 66, 31.29, 14.99, 0.00467, 67, 11.38, 17.38, 0.47502, 68, -17.06, 9.76, 0.05492, 4, 12, 42.87, -18.28, 0.07235, 13, -0.49, -21.66, 0.06048, 67, 20.53, 19.34, 0.52361, 68, -10.38, 16.31, 0.34356, 5, 12, 53.1, -31.52, 0.00426, 13, 0.66, -38.35, 0.003, 67, 36.46, 14.24, 0.11398, 69, -22.62, 12.87, 0.00668, 68, 5.8, 20.54, 0.87208, 3, 69, 8.25, 20.2, 0.86061, 68, 37.13, 15.51, 0.11035, 70, -14.47, 24.98, 0.02904, 2, 69, 35.99, 21.01, 0.1911, 70, 12.48, 18.34, 0.8089, 2, 69, 55.4, 31.96, 0.0004, 70, 34.11, 23.7, 0.9996, 1, 70, 46.58, 30.86, 1, 1, 70, 50.46, 13.91, 1, 1, 70, 37.59, -0.8, 1, 3, 69, 42.44, -14.62, 0.06561, 70, 9.16, -17.72, 0.93439, 73, -103.93, -131.79, 0, 3, 69, 14.46, -12.02, 0.93421, 68, 30.54, -16.63, 0.06579, 73, -84.59, -111.4, 0, 5, 66, 32.74, -18.62, 0.02058, 67, 18.99, -15.39, 0.30511, 69, -8.44, -18.46, 0.02492, 68, 6.92, -13.83, 0.64939, 73, -75.51, -89.42, 0, 4, 66, 13.69, -14.84, 0.6167, 67, -0.43, -15.18, 0.36724, 68, -9.59, -24.06, 0.01606, 73, -80.89, -70.75, 0, 2, 12, -25.95, -25.96, 0.14659, 66, -19.14, -19.71, 0.85341, 3, 12, -35.1, -13.7, 0.29607, 66, -32.47, -12.21, 0.70233, 71, -70.3, 23.65, 0.00159, 4, 12, -15.64, 27.87, 0.61254, 13, -23.49, 49.22, 0.00029, 66, -31.56, 33.68, 0.00329, 71, -24.9, 16.84, 0.38388, 3, 12, -13.54, 32.34, 0.50225, 13, -19.26, 51.78, 0.00012, 71, -20.02, 16.11, 0.49764, 3, 12, -9.93, 40.07, 0.31904, 71, -11.58, 14.84, 0.68096, 73, -46.44, -2.35, 0], "hull": 67}}, "so": {"so": {"x": -15.33, "y": -29.71, "width": 330, "height": 61}}, "stick1": {"stick1": {"type": "mesh", "uvs": [0.02298, 0.06645, 0.04205, 0.02861, 0.07491, 0.03163, 0.98315, 0.39701, 0.98669, 0.40911, 1, 0.93128, 1, 0.98618, 0.98559, 1, 0.96765, 1, 0.94971, 0.98285, 0.93266, 1, 0.91335, 1, 0.87374, 0.97094, 0.87469, 0.85376, 0.87605, 0.42918, 0.10474, 0.14865, 0.02979, 0.1019, 0.96406, 0.44051, 0.97303, 0.42804, 0.98276, 0.42736], "triangles": [7, 8, 6, 8, 9, 6, 10, 11, 9, 11, 12, 9, 9, 5, 6, 5, 9, 13, 9, 12, 13, 13, 17, 5, 5, 17, 19, 17, 18, 19, 4, 5, 19, 13, 14, 17, 17, 14, 18, 18, 14, 3, 4, 19, 18, 4, 18, 3, 14, 2, 3, 16, 2, 15, 2, 14, 15, 2, 0, 1, 2, 16, 0], "vertices": [1, 78, -41.1, -0.58, 1, 1, 78, -40.62, 8.59, 1, 1, 78, -34.16, 10.5, 1, 2, 78, 167.51, 6.95, 0.28083, 79, -4.51, 11.8, 0.71917, 2, 78, 169.16, 4.88, 0.20091, 79, -1.97, 12.51, 0.79909, 1, 79, 114.26, 13.35, 1, 1, 79, 126.34, 13.18, 1, 1, 79, 129.34, 10.2, 1, 1, 79, 129.29, 6.54, 1, 1, 79, 125.46, 2.93, 1, 1, 79, 129.19, -0.6, 1, 1, 79, 129.13, -4.54, 1, 1, 79, 122.63, -12.53, 1, 1, 79, 96.85, -11.97, 1, 2, 78, 149.99, -8.29, 0.55174, 79, 2.69, -10.22, 0.44826, 1, 78, -18.84, -11.07, 1, 1, 78, -36.88, -7.29, 1, 2, 78, 167.41, -2.95, 0.00946, 79, 4.54, 7.85, 0.99054, 2, 78, 168.1, 0.2, 0.06301, 79, 1.92, 9.71, 0.93699, 2, 78, 169.89, 1.07, 0.0685, 79, 1.81, 11.69, 0.9315], "hull": 17}}, "stick2": {"stick1": {"type": "mesh", "uvs": [0.02298, 0.06645, 0.04205, 0.02861, 0.07491, 0.03163, 0.98315, 0.39701, 0.98669, 0.40911, 1, 0.93128, 1, 0.98618, 0.98559, 1, 0.96765, 1, 0.94971, 0.98285, 0.93266, 1, 0.91335, 1, 0.87374, 0.97094, 0.87469, 0.85376, 0.87605, 0.42918, 0.10474, 0.14865, 0.02979, 0.1019, 0.96406, 0.44051, 0.97303, 0.42804, 0.98276, 0.42736], "triangles": [7, 8, 6, 8, 9, 6, 10, 11, 9, 11, 12, 9, 9, 5, 6, 5, 9, 13, 9, 12, 13, 13, 17, 5, 5, 17, 19, 17, 18, 19, 4, 5, 19, 13, 14, 17, 17, 14, 18, 18, 14, 3, 4, 19, 18, 4, 18, 3, 14, 2, 3, 16, 2, 15, 2, 14, 15, 2, 0, 1, 2, 16, 0], "vertices": [1, 80, -41.1, -0.58, 1, 1, 80, -40.62, 8.59, 1, 1, 80, -34.16, 10.5, 1, 2, 80, 167.51, 6.95, 0.28083, 81, -4.51, 11.8, 0.71917, 2, 80, 169.16, 4.88, 0.20091, 81, -1.97, 12.51, 0.79909, 1, 81, 114.26, 13.35, 1, 1, 81, 126.34, 13.18, 1, 1, 81, 129.34, 10.2, 1, 1, 81, 129.29, 6.54, 1, 1, 81, 125.46, 2.93, 1, 1, 81, 129.19, -0.6, 1, 1, 81, 129.13, -4.54, 1, 1, 81, 122.63, -12.53, 1, 1, 81, 96.85, -11.97, 1, 2, 80, 149.99, -8.29, 0.55174, 81, 2.69, -10.22, 0.44826, 1, 80, -18.84, -11.07, 1, 1, 80, -36.88, -7.29, 1, 2, 80, 167.41, -2.95, 0.00946, 81, 4.54, 7.85, 0.99054, 2, 80, 168.1, 0.2, 0.06301, 81, 1.92, 9.71, 0.93699, 2, 80, 169.89, 1.07, 0.0685, 81, 1.81, 11.69, 0.9315], "hull": 17}}, "vang": {"vang": {"x": -41.98, "y": -42.66, "rotation": -89.32, "width": 368, "height": 242}}, "vang2": {"vang": {"x": -14.43, "y": -29.43, "rotation": -86.7, "width": 368, "height": 242}}, "vang3": {"vang": {"x": 22.41, "y": -92.23, "scaleX": -1.043, "rotation": -78.11, "width": 368, "height": 242}}}}, "animations": {"stay": {"slots": {"fr1": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.2, "color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "fr1"}, {"time": 0.8333, "name": "fr1"}, {"time": 1, "name": "fr1"}, {"time": 1.0667, "name": "fr2"}, {"time": 1.1333, "name": "fr3"}, {"time": 1.2, "name": "fr1"}, {"time": 1.2667, "name": "fr2"}, {"time": 1.3333, "name": "fr3"}, {"time": 1.4, "name": "fr1"}, {"time": 1.4667, "name": "fr2"}, {"time": 1.5333, "name": "fr3"}, {"time": 1.6, "name": "fr1"}, {"time": 1.6667, "name": "fr2"}, {"time": 1.7333, "name": "fr3"}, {"time": 1.8, "name": "fr1"}, {"time": 1.8667, "name": "fr2"}, {"time": 1.9333, "name": "fr3"}, {"time": 2, "name": "fr1"}, {"time": 2.0667, "name": "fr2"}, {"time": 2.1333, "name": "fr3"}, {"time": 3, "name": "fr1"}]}, "fr2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.2, "color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "fr1"}, {"time": 0.8333, "name": "fr1"}, {"time": 1, "name": "fr1"}, {"time": 1.0667, "name": "fr2"}, {"time": 1.1333, "name": "fr3"}, {"time": 1.2, "name": "fr1"}, {"time": 1.2667, "name": "fr2"}, {"time": 1.3333, "name": "fr3"}, {"time": 1.4, "name": "fr1"}, {"time": 1.4667, "name": "fr2"}, {"time": 1.5333, "name": "fr3"}, {"time": 1.6, "name": "fr1"}, {"time": 1.6667, "name": "fr2"}, {"time": 1.7333, "name": "fr3"}, {"time": 1.8, "name": "fr1"}, {"time": 1.8667, "name": "fr2"}, {"time": 1.9333, "name": "fr3"}, {"time": 2, "name": "fr1"}, {"time": 2.0667, "name": "fr2"}, {"time": 2.1333, "name": "fr3"}, {"time": 3, "name": "fr1"}]}, "light": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 2.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 3, "color": "ffffffff"}]}, "light2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff"}, {"time": 3, "color": "ffffff00"}]}, "light3": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}]}, "light4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff"}, {"time": 3, "color": "ffffff00"}]}, "light5": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "body": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.1667, "x": -1.24, "y": 10.01}, {"time": 2.1667, "x": 1.69, "y": -7.51}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "handL": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 5.66}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "handL2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 5.66}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "handL3": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 5.66}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "stick1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "hand2": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.1667, "angle": 11.72}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "h2": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.1667, "angle": 11.72}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "belt": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.5, "angle": 4.74}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "pants": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.5, "angle": 3.1}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "head": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": 16.7}, {"time": 1.5, "angle": 15.17}, {"time": 2, "angle": 16.7}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "head2": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": 16.71}, {"time": 1.5, "angle": 15.18}, {"time": 2, "angle": 16.71}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "head3": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": 16.71}, {"time": 1.5, "angle": 15.18}, {"time": 2, "angle": 16.71}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "head4": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": 16.72}, {"time": 1.5, "angle": 15.19}, {"time": 2, "angle": 16.72}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "m1": {"rotate": [{"time": 0, "angle": -9.14, "curve": "stepped"}, {"time": 0.5, "angle": -9.14}, {"time": 1, "angle": 1.07}, {"time": 1.5, "angle": -0.46}, {"time": 3, "angle": -9.14}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "m2": {"rotate": [{"time": 0, "angle": 8.96, "curve": "stepped"}, {"time": 0.5, "angle": 8.96}, {"time": 1, "angle": -24.51}, {"time": 1.5, "angle": -26.04}, {"time": 3, "angle": 8.96}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 2.03}, {"time": 2, "angle": -0.93}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "Layer 4": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 2.03}, {"time": 2, "angle": -0.93}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "Layer 5": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 2.03}, {"time": 2, "angle": -0.93}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "Layer 6": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 2.03}, {"time": 2, "angle": -0.93}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "Layer 7": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 2.03}, {"time": 2, "angle": -0.93}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "Layer 8": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 2.03}, {"time": 2, "angle": -0.93}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "l2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 1.1667, "x": -28.69, "y": -48.65}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1}, {"time": 1.1667, "x": 1.447, "y": 1.447}, {"time": 3, "x": 1, "y": 1}]}, "rb2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -2.67}, {"time": 2, "angle": 4.92}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb3": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -2.67}, {"time": 2, "angle": 4.92}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb4": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -2.67}, {"time": 2, "angle": 4.92}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb5": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -2.67}, {"time": 2, "angle": 4.92}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb6": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -2.67}, {"time": 2, "angle": 4.92}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb7": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -5.26}, {"time": 2, "angle": 5.08}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb8": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -5.25}, {"time": 2, "angle": 5.09}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb9": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -5.26}, {"time": 2, "angle": 5.08}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb10": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -5.26}, {"time": 2, "angle": 5.08}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb11": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb12": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb13": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "stick2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -16.51}, {"time": 1.3333, "angle": -4.49}, {"time": 2.1667, "angle": -16.51}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "nen tien": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "vang2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1.5, "x": 1.067, "y": 1.067}, {"time": 3, "x": 1, "y": 1}]}, "vang": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.5, "x": -5.17, "y": -3.67}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "l3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 1.1667, "x": 10.36, "y": -30.89}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1}, {"time": 1.1667, "x": 1.378, "y": 1.378}, {"time": 3, "x": 1, "y": 1}]}, "nohu": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 4.46}, {"time": 0.5333, "angle": -0.19}, {"time": 0.8333, "angle": -4}, {"time": 1.0667, "angle": 0, "curve": "stepped"}, {"time": 1.5333, "angle": 0, "curve": "stepped"}, {"time": 2.0333, "angle": 0, "curve": "stepped"}, {"time": 2.5333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.2667, "x": -2.71, "y": 8.83}, {"time": 0.5333, "x": -1.96, "y": -1.01}, {"time": 0.8333, "x": -2.04, "y": 13.12}, {"time": 1.0667, "x": 0, "y": 0}, {"time": 1.2667, "x": 0, "y": 13.53}, {"time": 1.5333, "x": 0, "y": -1.6}, {"time": 1.7667, "x": 0, "y": 13.53}, {"time": 2.0333, "x": 0, "y": -1.6}, {"time": 2.2667, "x": 0, "y": 13.53}, {"time": 2.5333, "x": 0, "y": -1.6}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0667, "x": 1, "y": 1}, {"time": 1.2667, "x": 1.252, "y": 1.252}, {"time": 1.5333, "x": 0.789, "y": 0.789}, {"time": 1.7667, "x": 1.252, "y": 1.252}, {"time": 2.0333, "x": 0.789, "y": 0.789}, {"time": 2.2667, "x": 1.252, "y": 1.252}, {"time": 2.5333, "x": 0.789, "y": 0.789}, {"time": 3, "x": 1, "y": 1}]}, "vang3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.5, "x": 4.48, "y": 15.46}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "stick4": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -16.51}, {"time": 1.3333, "angle": -4.49}, {"time": 2.1667, "angle": -16.51}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "stick3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "handL6": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 5.66}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "handL5": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 5.66}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "handL4": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 5.66}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "h3": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.1667, "angle": 11.72}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "hand3": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.1667, "angle": 11.72}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "pants2": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.5, "angle": 3.1}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "Layer 13": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 2.03}, {"time": 2, "angle": -0.93}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "Layer 12": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 2.03}, {"time": 2, "angle": -0.93}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "Layer 11": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 2.03}, {"time": 2, "angle": -0.93}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "Layer 10": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 2.03}, {"time": 2, "angle": -0.93}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "Layer 9": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 2.03}, {"time": 2, "angle": -0.93}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb19": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -2.67}, {"time": 2, "angle": 4.92}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb18": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -2.67}, {"time": 2, "angle": 4.92}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb17": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -2.67}, {"time": 2, "angle": 4.92}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb16": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -2.67}, {"time": 2, "angle": 4.92}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb15": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -2.67}, {"time": 2, "angle": 4.92}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb23": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -5.26}, {"time": 2, "angle": 5.08}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb22": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -5.26}, {"time": 2, "angle": 5.08}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb21": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -5.25}, {"time": 2, "angle": 5.09}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb20": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -5.26}, {"time": 2, "angle": 5.08}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb26": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb25": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb24": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "rb14": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 2.03}, {"time": 2, "angle": -0.93}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "belt2": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.5, "angle": 4.74}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "m3": {"rotate": [{"time": 0, "angle": -9.14, "curve": "stepped"}, {"time": 0.5, "angle": -9.14}, {"time": 1, "angle": 1.07}, {"time": 1.5, "angle": -0.46}, {"time": 3, "angle": -9.14}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "m4": {"rotate": [{"time": 0, "angle": 8.96, "curve": "stepped"}, {"time": 0.5, "angle": 8.96}, {"time": 1, "angle": -24.51}, {"time": 1.5, "angle": -26.04}, {"time": 3, "angle": 8.96}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "head8": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": 16.72}, {"time": 1.5, "angle": 15.19}, {"time": 2, "angle": 16.72}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "head7": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": 16.71}, {"time": 1.5, "angle": 15.18}, {"time": 2, "angle": 16.71}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "head6": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": 16.71}, {"time": 1.5, "angle": 15.18}, {"time": 2, "angle": 16.71}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "head5": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": 16.7}, {"time": 1.5, "angle": 15.17}, {"time": 2, "angle": 16.7}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "body2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.1667, "x": -1.24, "y": 10.01}, {"time": 2.1667, "x": 1.69, "y": -7.51}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "l4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 1.1667, "x": -2.18, "y": -8.03}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1}, {"time": 1.1667, "x": 1.702, "y": 1.702}, {"time": 3, "x": 1, "y": 1}]}, "fr": {"rotate": [{"time": 0, "angle": 313.69, "curve": "stepped"}, {"time": 0.8333, "angle": 313.69, "curve": "stepped"}, {"time": 1, "angle": 313.69, "curve": "stepped"}, {"time": 1.2, "angle": 313.69, "curve": "stepped"}, {"time": 1.4, "angle": 313.69, "curve": "stepped"}, {"time": 1.6, "angle": 313.69, "curve": "stepped"}, {"time": 1.8, "angle": 313.69, "curve": "stepped"}, {"time": 2, "angle": 313.69, "curve": "stepped"}, {"time": 3, "angle": 313.69}], "translate": [{"time": 0, "x": 39.27, "y": 100.48, "curve": "stepped"}, {"time": 0.8333, "x": 39.27, "y": 100.48, "curve": "stepped"}, {"time": 1, "x": 39.27, "y": 100.48}, {"time": 1.2, "x": 33.61, "y": 93.4}, {"time": 1.4, "x": 33.61, "y": 91.28}, {"time": 1.6, "x": 32.19, "y": 82.08, "curve": "stepped"}, {"time": 1.8, "x": 32.19, "y": 82.08, "curve": "stepped"}, {"time": 2, "x": 32.19, "y": 82.08}, {"time": 3, "x": 39.27, "y": 100.48}], "scale": [{"time": 0, "x": 0.494, "y": 0.494, "curve": "stepped"}, {"time": 0.8333, "x": 0.494, "y": 0.494}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}, {"time": 3, "x": 0.494, "y": 0.494}]}, "fr2": {"rotate": [{"time": 0, "angle": -3.82, "curve": "stepped"}, {"time": 0.8333, "angle": -3.82, "curve": "stepped"}, {"time": 1, "angle": -3.82, "curve": "stepped"}, {"time": 1.2, "angle": -3.82, "curve": "stepped"}, {"time": 1.4, "angle": -3.82, "curve": "stepped"}, {"time": 1.6, "angle": -3.82, "curve": "stepped"}, {"time": 1.8, "angle": -3.82, "curve": "stepped"}, {"time": 2, "angle": -3.82, "curve": "stepped"}, {"time": 3, "angle": -3.82}], "translate": [{"time": 0, "x": -32.88, "y": 89.38, "curve": "stepped"}, {"time": 0.8333, "x": -32.88, "y": 89.38, "curve": "stepped"}, {"time": 1, "x": -32.88, "y": 89.38, "curve": "stepped"}, {"time": 1.2, "x": -32.88, "y": 89.38, "curve": "stepped"}, {"time": 1.4, "x": -32.88, "y": 89.38, "curve": "stepped"}, {"time": 1.6, "x": -32.88, "y": 89.38, "curve": "stepped"}, {"time": 1.8, "x": -32.88, "y": 89.38, "curve": "stepped"}, {"time": 2, "x": -32.88, "y": 89.38, "curve": "stepped"}, {"time": 3, "x": -32.88, "y": 89.38}], "scale": [{"time": 0, "x": 0.541, "y": 0.541, "curve": "stepped"}, {"time": 0.8333, "x": 0.541, "y": 0.541}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}, {"time": 3, "x": 0.541, "y": 0.541}]}, "light3": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.8333, "angle": -81.44}, {"time": 1.6667, "angle": 170.23}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.633, "y": 0.633}, {"time": 1.6667, "x": 0.622, "y": 0.622}, {"time": 3, "x": 0.633, "y": 0.633}]}, "light": {"rotate": [{"time": 0, "angle": -17.3}, {"time": 0.6667, "angle": -140.31}, {"time": 1.5, "angle": 132.2}, {"time": 3, "angle": -17.3}]}, "light2": {"rotate": [{"time": 0, "angle": -119.26}, {"time": 1.3333, "angle": 0}, {"time": 2.1667, "angle": -41.35}, {"time": 3, "angle": -119.26}], "translate": [{"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 1.3333, "x": 1, "y": 1}]}, "light4": {"rotate": [{"time": 0, "angle": -119.26}, {"time": 1.3333, "angle": 0}, {"time": 2.1667, "angle": -41.35}, {"time": 3, "angle": -119.26}], "translate": [{"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 1.3333, "x": 1, "y": 1}]}, "light5": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.8333, "angle": -81.44}, {"time": 1.6667, "angle": 170.23}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.633, "y": 0.633}, {"time": 1.6667, "x": 0.622, "y": 0.622}, {"time": 3, "x": 0.633, "y": 0.633}]}}}}}