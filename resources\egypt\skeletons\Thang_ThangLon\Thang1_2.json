{"skeleton": {"hash": "dfGd0knYk0SiwdmbRWgVxYGhBUM", "spine": "3.6.53", "width": 1280, "height": 720}, "bones": [{"name": "root"}, {"name": "t", "parent": "root", "y": 13.07}, {"name": "l4", "parent": "t", "length": 138.96, "rotation": 113.45, "x": -13.74, "y": 143.16, "scaleX": 0.663, "scaleY": 0.663}, {"name": "light", "parent": "t", "x": 178.78, "y": 193.71}, {"name": "light2", "parent": "t", "rotation": -28.57, "x": -31.46, "y": 253.27}, {"name": "light3", "parent": "t", "rotation": -95.78, "x": -286.37, "y": 90.34}, {"name": "light4", "parent": "t", "rotation": -95.78, "x": 323.99, "y": 34.85, "scaleX": 0.506, "scaleY": 0.506}, {"name": "light5", "parent": "t", "rotation": -136.27, "x": -143.03, "y": 133.11, "scaleX": 0.506, "scaleY": 0.506}, {"name": "nen tien", "parent": "t", "length": 249.52, "rotation": 0.21, "x": -142.19, "y": -28.52}, {"name": "text", "parent": "nen tien", "length": 93.27, "rotation": 91.47, "x": 93.11, "y": 56.35}, {"name": "vang", "parent": "nen tien", "length": 76.78, "rotation": 89.11, "x": 187.06, "y": 228.7}], "slots": [{"name": "l3", "bone": "l4", "attachment": "l"}, {"name": "nen tien", "bone": "nen tien", "attachment": "nentien"}, {"name": "vang", "bone": "vang", "attachment": "vang"}, {"name": "text", "bone": "text", "attachment": "thanglon"}, {"name": "so", "bone": "t", "attachment": "so"}, {"name": "light", "bone": "light", "attachment": "light"}, {"name": "light2", "bone": "light2", "attachment": "light"}, {"name": "light3", "bone": "light3", "attachment": "light"}, {"name": "light4", "bone": "light4", "attachment": "light"}, {"name": "light5", "bone": "light5", "attachment": "light"}], "skins": {"default": {"l3": {"l": {"x": -3.9, "y": 3.88, "rotation": -113.45, "width": 611, "height": 589}}, "light": {"light": {"width": 177, "height": 159}}, "light2": {"light": {"width": 177, "height": 159}}, "light3": {"light": {"width": 177, "height": 159}}, "light4": {"light": {"width": 177, "height": 159}}, "light5": {"light": {"width": 177, "height": 159}}, "nen tien": {"nentien": {"x": 131.59, "y": -1.99, "rotation": -0.21, "width": 421, "height": 66}}, "so": {"so": {"x": -15.33, "y": -29.71, "width": 330, "height": 61}}, "text": {"thang": {"x": 28.85, "y": -51.99, "rotation": -91.68, "width": 400, "height": 226}, "thanglon": {"x": 18.37, "y": -51.99, "rotation": -91.68, "width": 603, "height": 226}}, "vang": {"gs": {"x": -41.98, "y": -42.66, "rotation": -89.32, "width": 699, "height": 190}, "vang": {"x": -41.98, "y": -42.66, "rotation": -89.32, "width": 368, "height": 242}}}}, "animations": {"play1": {"slots": {"light": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4667, "color": "ffffff00"}, {"time": 1.5, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}]}, "light2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00", "curve": "stepped"}, {"time": 2.5, "color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}]}, "light3": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 2.3333, "color": "ffffffff"}, {"time": 3, "color": "ffffff00"}]}, "light4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1667, "color": "ffffff00"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 2.5, "color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}]}, "light5": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "nen tien": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "vang": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.5, "x": -5.17, "y": -3.67}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "text": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2667, "angle": 4.04}, {"time": 0.5, "angle": -0.16}, {"time": 0.7667, "angle": -2.49}, {"time": 1, "angle": 0}, {"time": 1.2667, "angle": 4.04}, {"time": 1.5, "angle": -0.16}, {"time": 1.7667, "angle": -2.49}, {"time": 2, "angle": 0}, {"time": 2.2667, "angle": 4.04}, {"time": 2.5333, "angle": -0.16}, {"time": 2.7667, "angle": -2.49}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.2667, "x": 0, "y": 13.53}, {"time": 0.5, "x": 0, "y": 0}, {"time": 0.7667, "x": 0, "y": 13.53}, {"time": 1, "x": 0, "y": 0}, {"time": 1.2667, "x": 0, "y": 13.53}, {"time": 1.5, "x": 0, "y": 0}, {"time": 1.7667, "x": 0, "y": 13.53}, {"time": 2, "x": 0, "y": 0}, {"time": 2.2667, "x": 0, "y": 13.53}, {"time": 2.5333, "x": 0, "y": 0}, {"time": 2.7667, "x": 0, "y": 13.53}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "l4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 1.1667, "x": -2.18, "y": -8.03}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1}, {"time": 1.1667, "x": 1.702, "y": 1.702}, {"time": 3, "x": 1, "y": 1}]}, "light": {"rotate": [{"time": 0, "angle": -17.3}, {"time": 0.6667, "angle": -140.31, "curve": "stepped"}, {"time": 1.4667, "angle": -140.31}, {"time": 1.5, "angle": -17.3}, {"time": 2.3333, "angle": -140.31, "curve": "stepped"}, {"time": 3, "angle": -140.31}]}, "light2": {"rotate": [{"time": 0, "angle": -140.31}, {"time": 0.5, "angle": -178.35}, {"time": 1, "angle": 143.61}, {"time": 1.5, "angle": 78.13}, {"time": 2.5, "angle": -178.35}, {"time": 3, "angle": -140.31}], "scale": [{"time": 0.5, "x": 2.404, "y": 2.404}, {"time": 1, "x": 0.266, "y": 0.266}, {"time": 1.5, "x": 0.161, "y": 0.161}, {"time": 2.5, "x": 2.404, "y": 2.404}]}, "light3": {"rotate": [{"time": 0, "angle": -140.31}, {"time": 0.1667, "angle": 170.34}, {"time": 0.5, "angle": 78.22, "curve": "stepped"}, {"time": 2, "angle": 78.22}, {"time": 2.3333, "angle": -29.96}, {"time": 3, "angle": -140.31}], "scale": [{"time": 0.5, "x": 0.361, "y": 0.361, "curve": "stepped"}, {"time": 2, "x": 0.361, "y": 0.361}]}, "light4": {"rotate": [{"time": 0, "angle": -140.31, "curve": "stepped"}, {"time": 1.1667, "angle": -140.31}, {"time": 1.8333, "angle": 108.2}, {"time": 2.5, "angle": -12.98}, {"time": 3, "angle": -140.31}], "translate": [{"time": 1.1667, "x": 0, "y": 0}], "scale": [{"time": 1.1667, "x": 1, "y": 1}, {"time": 1.8333, "x": 2.02, "y": 2.02}, {"time": 2.5, "x": 0.839, "y": 0.839}]}, "light5": {"rotate": [{"time": 0, "angle": -140.31}, {"time": 0.8333, "angle": 113.82}, {"time": 1.5, "angle": -27.95}, {"time": 3, "angle": -140.31}], "scale": [{"time": 0.3333, "x": 0.669, "y": 0.669}, {"time": 0.8333, "x": 2.231, "y": 2.231}, {"time": 1.5, "x": 0.961, "y": 0.961}]}}}, "play2": {"slots": {"light": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4667, "color": "ffffff00"}, {"time": 1.5, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}]}, "light2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00", "curve": "stepped"}, {"time": 2.5, "color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}]}, "light3": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 2.3333, "color": "ffffffff"}, {"time": 3, "color": "ffffff00"}]}, "light4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1667, "color": "ffffff00"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 2.5, "color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}]}, "light5": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}]}, "text": {"attachment": [{"time": 0, "name": "thang"}, {"time": 3, "name": "thang"}]}, "vang": {"attachment": [{"time": 0, "name": "gs"}, {"time": 3, "name": "gs"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "nen tien": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "vang": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": -119.23, "y": -98.71}, {"time": 1, "x": -124.18, "y": -97.56}, {"time": 2, "x": -113.8, "y": -97.68}, {"time": 3, "x": -119.23, "y": -98.71}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "text": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": 0, "y": 13.53}, {"time": 0.6667, "x": 0, "y": -0.3}, {"time": 1, "x": 0, "y": 13.53}, {"time": 1.3333, "x": 0, "y": -0.3}, {"time": 2.1667, "x": -1.81, "y": 20.59}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "l4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": -10.26, "y": -96.4}, {"time": 1.1667, "x": -0.3, "y": -50.69}, {"time": 3, "x": -10.26, "y": -96.4}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1.1667, "x": 1.702, "y": 1.702}, {"time": 3, "x": 1, "y": 1}]}, "light": {"rotate": [{"time": 0, "angle": -17.3}, {"time": 0.6667, "angle": -140.31, "curve": "stepped"}, {"time": 1.4667, "angle": -140.31}, {"time": 1.5, "angle": -17.3}, {"time": 2.3333, "angle": -140.31, "curve": "stepped"}, {"time": 3, "angle": -140.31}]}, "light2": {"rotate": [{"time": 0, "angle": -140.31}, {"time": 0.5, "angle": -178.35}, {"time": 1, "angle": 143.61}, {"time": 1.5, "angle": 78.13}, {"time": 2.5, "angle": -178.35}, {"time": 3, "angle": -140.31}], "scale": [{"time": 0.5, "x": 2.404, "y": 2.404}, {"time": 1, "x": 0.266, "y": 0.266}, {"time": 1.5, "x": 0.161, "y": 0.161}, {"time": 2.5, "x": 2.404, "y": 2.404}]}, "light3": {"rotate": [{"time": 0, "angle": -140.31}, {"time": 0.1667, "angle": 170.34}, {"time": 0.5, "angle": 78.22, "curve": "stepped"}, {"time": 2, "angle": 78.22}, {"time": 2.3333, "angle": -29.96}, {"time": 3, "angle": -140.31}], "scale": [{"time": 0.5, "x": 0.361, "y": 0.361, "curve": "stepped"}, {"time": 2, "x": 0.361, "y": 0.361}]}, "light4": {"rotate": [{"time": 0, "angle": -140.31, "curve": "stepped"}, {"time": 1.1667, "angle": -140.31}, {"time": 1.8333, "angle": 108.2}, {"time": 2.5, "angle": -12.98}, {"time": 3, "angle": -140.31}], "translate": [{"time": 1.1667, "x": 0, "y": 0}], "scale": [{"time": 1.1667, "x": 1, "y": 1}, {"time": 1.8333, "x": 2.02, "y": 2.02}, {"time": 2.5, "x": 0.839, "y": 0.839}]}, "light5": {"rotate": [{"time": 0, "angle": -140.31}, {"time": 0.8333, "angle": 113.82}, {"time": 1.5, "angle": -27.95}, {"time": 3, "angle": -140.31}], "scale": [{"time": 0.3333, "x": 0.669, "y": 0.669}, {"time": 0.8333, "x": 2.231, "y": 2.231}, {"time": 1.5, "x": 0.961, "y": 0.961}]}}}}}