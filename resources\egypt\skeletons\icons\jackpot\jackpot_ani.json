{"skeleton": {"hash": "xoPW6PeIMadOCTdyqSvvigQ5VUs", "spine": "3.6.53", "width": 149.42, "height": 158.53}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": -0.62, "y": -22.27}, {"name": "bg", "parent": "bone", "rotation": 85.45, "x": -51.95, "y": 70.3}, {"name": "item", "parent": "bone", "length": 30.23, "rotation": 116.16, "x": 7.31, "y": -17.52}, {"name": "item2", "parent": "item", "length": 24.52, "rotation": -26.72, "x": 30.23}, {"name": "item3", "parent": "item2", "length": 22.36, "rotation": -24.64, "x": 24.52}, {"name": "item4", "parent": "item3", "length": 25.49, "rotation": -62.12, "x": 22.36}, {"name": "item5", "parent": "item4", "length": 31.4, "rotation": -16.71, "x": 24.28, "y": -0.42}, {"name": "item6", "parent": "item4", "length": 27.51, "rotation": -32.79, "x": 26.34, "y": -17.67}, {"name": "item7", "parent": "item3", "length": 21.16, "rotation": 42.21, "x": 18.52, "y": 17.66}, {"name": "item8", "parent": "item3", "length": 49.52, "rotation": 57.44, "x": 16.91, "y": 12.69}, {"name": "item9", "parent": "item3", "length": 34.1, "rotation": 54.45, "x": 39.14, "y": 2.9}, {"name": "sprites_01", "parent": "bone", "length": 70.6, "rotation": 89.53, "x": -5.5, "y": -40.04, "scaleX": 1.787, "scaleY": 1.966}, {"name": "text", "parent": "bone", "length": 27.74, "rotation": 90.78, "x": -4.02, "y": -37.66}, {"name": "text2", "parent": "text", "length": 75.15, "rotation": -91.46, "x": 15.33, "y": 15.22}], "slots": [{"name": "bg", "bone": "bg", "attachment": "bg"}, {"name": "border", "bone": "root", "attachment": "border"}, {"name": "sprites_01", "bone": "sprites_01"}, {"name": "item", "bone": "item", "attachment": "item"}, {"name": "text", "bone": "text", "attachment": "text"}, {"name": "text2", "bone": "text2", "attachment": "text", "blend": "additive"}], "skins": {"default": {"bg": {"bg": {"x": -43.64, "y": -56.13, "rotation": -85.45, "width": 145, "height": 132}}, "border": {"border": {"width": 147, "height": 134}}, "item": {"item": {"type": "mesh", "uvs": [0.54094, 0.06752, 0.59045, 0.14586, 0.63527, 0.17322, 0.65533, 0.20555, 0.70439, 0.2361, 0.75768, 0.25379, 0.77576, 0.28652, 0.78814, 0.31483, 0.89758, 0.33694, 0.95659, 0.3334, 0.97582, 0.36516, 0.98426, 0.40849, 0.98426, 0.46141, 0.97335, 0.55175, 0.96046, 0.60076, 0.91456, 0.63602, 0.86373, 0.6332, 0.77987, 0.60631, 0.69684, 0.57378, 0.69153, 0.59711, 0.72967, 0.68596, 0.77287, 0.74003, 0.80109, 0.83282, 0.81616, 0.92865, 0.8106, 0.97141, 0.7535, 1, 0.18908, 1, 0.04618, 1, 0, 0.93658, 0, 0.72674, 0.08224, 0.47433, 0.18483, 0.36328, 0.2669, 0.31167, 0.2669, 0.24773, 0.23955, 0.17993, 0.17635, 0.11368, 0.15214, 0.08243, 0.16553, 0.05418, 0.22544, 0.03098, 0.42053, 0, 0.46134, 0, 0.31157, 0.03214, 0.39646, 0.09363, 0.38307, 0.0557, 0.39109, 0.02399, 0.96046, 0.49746, 0.31917, 0.3186, 0.32821, 0.37066, 0.34086, 0.40761, 0.38964, 0.4328, 0.41855, 0.40425, 0.4312, 0.36898, 0.47457, 0.37738, 0.50167, 0.41433, 0.54142, 0.40593, 0.55045, 0.34379, 0.57937, 0.3102, 0.64803, 0.3018, 0.6932, 0.31692, 0.73656, 0.31188, 0.76005, 0.35555, 0.91183, 0.37738, 0.91725, 0.48151, 0.72934, 0.46807, 0.68055, 0.48823, 0.82691, 0.55876, 0.90099, 0.57388, 0.96614, 0.5214, 0.65706, 0.5235, 0.71307, 0.39921, 0.57394, 0.55708, 0.57756, 0.65953, 0.60647, 0.77542, 0.63719, 0.81908, 0.75825, 0.9652, 0.3535, 0.53021, 0.39506, 0.6377, 0.45469, 0.73007, 0.51612, 0.78717, 0.5107, 0.84596, 0.44204, 0.87115, 0.33724, 0.84596, 0.23064, 0.96184, 0.12764, 0.65282, 0.26677, 0.68976, 0.33363, 0.7519, 0.46011, 0.78717, 0.57756, 0.48991], "triangles": [44, 41, 39, 43, 41, 44, 39, 43, 44, 40, 42, 43, 3, 56, 2, 1, 42, 0, 56, 1, 2, 40, 43, 39, 0, 42, 40, 56, 42, 1, 56, 51, 42, 38, 39, 41, 41, 43, 42, 35, 37, 38, 36, 37, 35, 34, 38, 41, 34, 41, 42, 35, 38, 34, 33, 34, 42, 46, 33, 42, 32, 33, 46, 51, 46, 42, 52, 51, 55, 50, 51, 52, 47, 46, 51, 50, 47, 51, 48, 47, 50, 49, 48, 50, 75, 48, 49, 47, 31, 32, 47, 32, 46, 31, 48, 30, 47, 48, 31, 75, 30, 48, 63, 60, 62, 64, 69, 63, 64, 87, 69, 67, 45, 12, 68, 87, 64, 13, 67, 12, 70, 87, 68, 65, 63, 62, 64, 63, 65, 18, 64, 65, 68, 64, 18, 66, 65, 62, 67, 66, 62, 67, 62, 45, 66, 67, 13, 19, 68, 18, 14, 66, 13, 17, 18, 65, 16, 65, 66, 17, 65, 16, 15, 66, 14, 16, 66, 15, 19, 70, 68, 61, 8, 9, 61, 9, 10, 60, 7, 8, 61, 60, 8, 60, 69, 58, 63, 69, 60, 11, 62, 61, 11, 61, 10, 62, 11, 12, 60, 61, 62, 45, 62, 12, 57, 3, 4, 57, 56, 3, 59, 4, 5, 59, 5, 6, 58, 57, 4, 59, 6, 7, 59, 58, 4, 60, 59, 7, 56, 55, 51, 60, 58, 59, 56, 57, 55, 69, 57, 58, 69, 55, 57, 54, 52, 55, 53, 52, 54, 55, 69, 54, 69, 87, 54, 50, 52, 53, 87, 53, 54, 49, 53, 75, 53, 49, 50, 70, 53, 87, 70, 76, 75, 70, 75, 53, 84, 83, 75, 76, 70, 71, 83, 30, 75, 84, 75, 76, 29, 30, 83, 85, 84, 76, 19, 71, 70, 71, 19, 20, 77, 76, 71, 85, 76, 77, 72, 71, 20, 78, 77, 71, 78, 86, 77, 85, 77, 86, 71, 72, 78, 73, 72, 20, 73, 20, 21, 73, 21, 22, 81, 85, 86, 79, 86, 78, 80, 81, 86, 80, 86, 79, 82, 28, 29, 82, 83, 84, 81, 82, 84, 81, 84, 85, 82, 29, 83, 74, 73, 22, 74, 22, 23, 24, 74, 23, 26, 27, 28, 82, 26, 28, 25, 73, 74, 25, 74, 24, 79, 78, 72, 72, 73, 79, 25, 79, 73, 80, 79, 25, 82, 81, 80, 25, 82, 80, 26, 82, 25], "vertices": [1, 11, 28.45, -10.06, 1, 2, 6, 8.15, 33.6, 0.00187, 11, 14.28, -10.35, 0.99813, 2, 6, 14.44, 29.03, 0.0673, 11, 7.38, -13.94, 0.9327, 2, 6, 17.12, 23.85, 0.23052, 11, 1.56, -14.01, 0.76948, 2, 6, 24, 18.76, 0.62326, 11, -6.07, -17.89, 0.37674, 2, 6, 31.59, 15.65, 0.82647, 11, -12.26, -23.28, 0.17353, 3, 6, 33.97, 10.42, 0.84237, 7, 6.16, 13.17, 0.05918, 11, -17.99, -23.07, 0.09844, 3, 6, 35.56, 5.93, 0.57426, 7, 8.97, 9.32, 0.39419, 11, -22.72, -22.48, 0.03155, 2, 6, 51.25, 1.74, 0.00512, 7, 25.2, 9.82, 0.99488, 1, 7, 33.37, 12.43, 1, 1, 7, 37.28, 8.3, 1, 2, 7, 40.1, 2.04, 0.95077, 8, 26.9, 26.46, 0.04923, 2, 7, 42.11, -5.97, 0.69007, 8, 31.04, 19.32, 0.30993, 2, 7, 43.99, -20.02, 0.11572, 8, 36.75, 6.33, 0.88428, 2, 7, 44.03, -27.89, 0.01068, 8, 38.96, -1.22, 0.98932, 1, 8, 35.97, -9.31, 1, 3, 3, 7.94, -51.4, 2e-05, 4, 3.2, -55.93, 4e-05, 8, 29.37, -12.63, 0.99994, 4, 3, 17.07, -42.33, 0.01336, 4, 7.28, -43.73, 0.01369, 5, 2.57, -46.94, 0.00286, 8, 16.75, -15.1, 0.97009, 5, 3, 26.93, -33.77, 0.16033, 4, 12.24, -31.64, 0.13495, 5, 2.03, -33.88, 0.05061, 6, 20.45, -33.81, 0.00105, 8, 3.79, -16.75, 0.65307, 5, 3, 24, -31.47, 0.33451, 4, 8.59, -30.91, 0.18385, 5, -1.59, -34.73, 0.04576, 6, 19.51, -37.41, 0.00014, 8, 4.95, -20.29, 0.43575, 4, 3, 9.13, -30.32, 0.78649, 4, -5.22, -36.57, 0.07343, 5, -11.77, -45.64, 0.0047, 8, 16.69, -29.5, 0.13538, 4, 3, -1.21, -32.23, 0.91623, 4, -13.59, -42.92, 0.02099, 5, -16.74, -54.9, 9e-05, 8, 26.34, -33.66, 0.06269, 3, 3, -16, -29.52, 0.98095, 4, -28.03, -47.15, 0.00098, 8, 37.14, -44.13, 0.01806, 2, 3, -30.38, -24.89, 0.99784, 8, 46.53, -55.96, 0.00216, 2, 3, -36.02, -21.22, 0.99947, 8, 49.18, -62.14, 0.00053, 1, 3, -36.37, -11.82, 1, 3, 3, -0.29, 61.63, 0.64929, 4, -54.97, 41.33, 0.21379, 9, -88.88, 62.75, 0.13692, 3, 3, 8.85, 80.23, 0.56744, 4, -55.17, 62.05, 0.26507, 9, -82.82, 82.57, 0.16749, 3, 3, 20.68, 81.88, 0.5411, 4, -45.34, 68.84, 0.28141, 9, -71.4, 86.08, 0.17749, 3, 3, 50.06, 67.45, 0.35195, 4, -12.61, 69.16, 0.39, 9, -40.1, 76.5, 0.25805, 4, 3, 80.15, 39.38, 0.07449, 4, 26.88, 57.62, 0.45561, 9, -5.94, 53.58, 0.46596, 10, 13.4, 54.58, 0.00394, 4, 3, 89.14, 18.39, 0.01187, 4, 44.35, 42.91, 0.33364, 9, 6.28, 34.29, 0.55671, 10, 20.12, 32.75, 0.09778, 4, 3, 91.12, 4.16, 0.00046, 4, 52.52, 31.09, 0.13958, 9, 10.5, 20.56, 0.36262, 10, 20.58, 18.39, 0.49734, 3, 4, 62.49, 31.19, 0.02227, 9, 20.04, 17.64, 0.04526, 10, 29.02, 13.07, 0.93247, 2, 4, 73.03, 35.26, 0.00021, 10, 40.08, 10.78, 0.99979, 1, 10, 53.71, 13.02, 1, 1, 10, 59.71, 13.39, 1, 1, 10, 62.4, 9.4, 1, 2, 10, 60.82, 0.12, 0.99065, 11, 55.78, 27.07, 0.00935, 3, 9, 50.48, -14.97, 0.00329, 10, 49.82, -26.39, 0.1496, 11, 46.17, 0.03, 0.84711, 2, 10, 46.67, -31.39, 0.04407, 11, 43.28, -5.13, 0.95593, 3, 9, 50.3, 1.61, 0.00655, 10, 54.01, -10.35, 0.77082, 11, 49.52, 16.26, 0.22263, 3, 9, 37.53, -7.36, 0.14683, 10, 39.33, -15.64, 0.28995, 11, 35.13, 10.21, 0.56323, 3, 9, 43.76, -7.23, 0.04641, 10, 45.37, -17.16, 0.36717, 11, 41.24, 9.01, 0.58642, 3, 9, 48.15, -9.79, 0.01418, 10, 48.93, -20.78, 0.29906, 11, 44.99, 5.58, 0.68676, 2, 7, 40.12, -12.26, 0.4128, 8, 30.88, 12.72, 0.5872, 3, 4, 51.51, 23.5, 0.08986, 9, 7.25, 13.62, 0.3438, 10, 15.62, 12.56, 0.56634, 5, 3, 78.94, 0.24, 0.00021, 4, 43.4, 22.11, 0.17078, 5, 7.94, 27.98, 0.01484, 9, -0.9, 14.75, 0.65041, 10, 8.05, 15.78, 0.16376, 5, 3, 72.96, 1.13, 0.0007, 4, 37.66, 20.22, 0.24068, 5, 3.51, 23.86, 0.0554, 9, -6.95, 14.68, 0.66573, 10, 2.2, 17.31, 0.03748, 4, 4, 33.79, 13.11, 0.24007, 5, 2.97, 15.79, 0.20815, 9, -12.78, 9.06, 0.51138, 10, -4.9, 13.42, 0.04041, 4, 4, 38.29, 8.96, 0.08583, 5, 8.78, 13.89, 0.24045, 9, -9.74, 3.75, 0.47718, 10, -3.37, 7.5, 0.19654, 4, 4, 43.81, 7.18, 0.01677, 5, 14.54, 14.57, 0.0361, 9, -5.02, 0.39, 0.45692, 10, 0.31, 3.01, 0.49022, 5, 5, 16.03, 8.33, 0.40203, 6, -10.32, -1.7, 5e-05, 9, -8.11, -5.24, 0.00643, 10, -4.15, -1.61, 0.58808, 11, -9.02, 21.96, 0.00341, 3, 5, 12.49, 2.32, 0.89869, 9, -14.77, -7.31, 0.0107, 10, -11.12, -1.86, 0.09061, 6, 3, 60.37, -25.08, 1e-05, 4, 38.2, -8.85, 0.00194, 5, 16.13, -2.34, 0.88098, 6, -0.84, -6.6, 0.09998, 7, -22.29, -13.14, 0.00334, 8, -28.84, -5.41, 0.01374, 5, 5, 25.46, 0.6, 0.11714, 6, 0.92, 3.02, 0.72774, 9, -6.32, -17.3, 0.00274, 10, -5.59, -13.71, 0.05814, 11, -9.83, 9.8, 0.09424, 4, 6, 5.35, 8.06, 0.58534, 9, -2.53, -22.84, 0.00421, 10, -3.39, -20.05, 0.03341, 11, -7.3, 3.58, 0.37704, 2, 6, 15.36, 8.91, 0.70066, 11, -11.02, -5.75, 0.29934, 2, 6, 21.79, 6.24, 0.88338, 11, -16.28, -10.31, 0.11662, 3, 6, 28.11, 6.74, 0.90977, 7, 1.61, 7.95, 0.00819, 11, -18.67, -16.18, 0.08205, 3, 6, 31.19, -0.23, 0.22925, 7, 6.56, 2.17, 0.76577, 11, -26.28, -15.83, 0.00499, 1, 7, 28.74, 4.2, 1, 2, 7, 33.44, -11.36, 0.46376, 8, 24.21, 11.73, 0.53624, 6, 3, 39.66, -45.27, 0.00043, 4, 28.77, -36.19, 0.00105, 5, 18.96, -31.12, 0.00197, 6, 25.92, -17.55, 0.00114, 7, 6.5, -15.94, 0.00123, 8, -0.41, -0.13, 0.99417, 6, 3, 39.95, -37.53, 0.02859, 4, 25.56, -29.15, 0.06711, 5, 13.1, -26.06, 0.09963, 6, 18.71, -20.37, 0.04246, 7, 0.4, -20.71, 0.03452, 8, -4.95, -6.39, 0.72769, 4, 3, 20.72, -51.73, 0.00186, 4, 14.76, -50.48, 0.00196, 5, 12.18, -49.95, 8e-05, 8, 18.93, -5.27, 0.99609, 1, 8, 29.4, -1.92, 1, 2, 7, 41.83, -15.68, 0.26125, 8, 33.47, 9.9, 0.73875, 6, 3, 36.52, -32.05, 0.08284, 4, 20.02, -25.8, 0.15947, 5, 6.68, -25.32, 0.13396, 6, 15.05, -25.7, 0.0261, 7, -1.57, -26.87, 0.0054, 8, -5.14, -12.86, 0.59225, 4, 4, 39.49, -33.73, 0.0003, 5, 27.68, -24.42, 0.00871, 7, 1.61, -6.09, 0.78581, 8, -7.84, 7.98, 0.20517, 6, 3, 37.13, -18.92, 0.12234, 4, 14.67, -13.8, 0.50695, 5, -3.2, -16.65, 0.1514, 6, 2.77, -30.37, 0.00845, 7, -11.99, -34.88, 0.00028, 8, -12.93, -23.44, 0.21058, 4, 3, 22.55, -12.34, 0.66987, 4, -1.31, -14.48, 0.22059, 5, -17.43, -23.93, 0.01097, 8, -4.46, -37, 0.09856, 3, 3, 4.48, -8.14, 0.97244, 4, -19.35, -18.85, 0.00727, 8, 8.23, -50.54, 0.02029, 3, 3, -3.6, -9.13, 0.98574, 4, -26.11, -23.37, 0.00154, 8, 15.5, -54.2, 0.01273, 2, 3, -31.8, -14.84, 0.99961, 8, 42.13, -65.11, 0.00039, 4, 3, 54.98, 7.92, 0.01986, 4, 18.55, 18.2, 0.65595, 5, -13.01, 14.06, 0.02957, 9, -25.78, 18.52, 0.29463, 3, 3, 37.27, 9.91, 0.22269, 4, 1.84, 12.02, 0.68916, 9, -43.57, 17.66, 0.08814, 3, 3, 20.53, 8.5, 0.93666, 4, -12.49, 3.23, 0.0451, 9, -59.88, 13.61, 0.01824, 3, 3, 8.61, 4.43, 0.99684, 4, -21.31, -5.76, 9e-05, 9, -71.01, 7.7, 0.00307, 3, 3, 0.72, 9.18, 0.99001, 4, -30.48, -5.07, 0.00261, 9, -79.54, 11.13, 0.00737, 3, 3, 1.58, 19.85, 0.93826, 4, -34.51, 4.85, 0.03201, 9, -80.39, 21.8, 0.02973, 3, 3, 11.81, 31.75, 0.76633, 4, -30.73, 20.08, 0.14757, 9, -72.19, 35.18, 0.0861, 3, 3, 2.4, 53.6, 0.68655, 4, -48.95, 35.37, 0.19047, 9, -84.95, 55.25, 0.12298, 3, 3, 52.25, 45.75, 0.2449, 4, -0.89, 50.77, 0.44797, 9, -34.49, 55.43, 0.30713, 3, 3, 38.19, 30.18, 0.35059, 4, -6.46, 30.54, 0.44427, 9, -45.9, 37.83, 0.20514, 3, 3, 25.21, 25.76, 0.61484, 4, -16.06, 20.75, 0.27366, 9, -58, 31.39, 0.1115, 3, 3, 12.19, 11.72, 0.95766, 4, -21.39, 2.36, 0.02309, 9, -68.63, 15.46, 0.01925, 6, 3, 46.3, -24.01, 0.02401, 4, 25.15, -14.22, 0.21533, 5, 6.51, -12.66, 0.46414, 6, 3.78, -19.93, 0.0703, 7, -14.03, -24.58, 0.01418, 8, -17.74, -14.11, 0.21204], "hull": 41}}, "sprites_01": {"sprite_01": {"x": 36.26, "y": 4.69, "rotation": -89.53, "width": 114, "height": 96}, "sprite_02": {"x": 36.26, "y": 4.69, "rotation": -89.53, "width": 114, "height": 96}, "sprite_03": {"x": 36.26, "y": 4.69, "rotation": -89.53, "width": 114, "height": 96}, "sprite_04": {"x": 34.27, "y": 1.84, "rotation": -89.53, "width": 114, "height": 96}, "sprite_05": {"x": 34.7, "y": 4.69, "rotation": -89.53, "width": 114, "height": 96}, "sprite_06": {"x": 34.28, "y": 2.62, "rotation": -89.53, "width": 114, "height": 96}, "sprite_07": {"x": 33.86, "y": 4.69, "rotation": -89.53, "width": 114, "height": 94}, "sprite_08": {"x": 31.99, "y": 2.36, "rotation": -89.53, "width": 114, "height": 94}}, "text": {"text": {"x": 29.38, "y": -5.13, "scaleX": 1.02, "scaleY": 1.02, "rotation": -90.78, "width": 139, "height": 77}}, "text2": {"text": {"x": 19.99, "y": 14.56, "scaleX": 1.02, "scaleY": 1.02, "rotation": 0.67, "width": 139, "height": 77}}}}, "animations": {"stay": {"slots": {"sprites_01": {"attachment": [{"time": 0, "name": null}, {"time": 3, "name": null}]}, "text2": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}, {"time": 1.3333, "color": "ffffff58"}, {"time": 1.6667, "color": "ffffff00"}, {"time": 2, "color": "ffffff2e"}, {"time": 2.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}, {"time": 1.1667, "x": 0, "y": 2.93}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 1.5, "x": 0, "y": 2.93}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.8333, "x": 0, "y": 2.93}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "bg": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "text": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}, {"time": 1, "x": 0, "y": -6.56}, {"time": 1.3333, "x": -8.16, "y": 3.32}, {"time": 1.6667, "x": 0, "y": -6.56}, {"time": 2, "x": -8.16, "y": 3.32}, {"time": 2.3333, "x": 0, "y": -6.56}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}, {"time": 1, "x": 1.267, "y": 1.267, "curve": "stepped"}, {"time": 1.6667, "x": 1.267, "y": 1.267, "curve": "stepped"}, {"time": 2.3333, "x": 1.267, "y": 1.267}, {"time": 2.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "item": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": 9.74, "curve": "stepped"}, {"time": 2.1667, "angle": 9.74}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1, "x": 0, "y": 5.57, "curve": "stepped"}, {"time": 2.1667, "x": 0, "y": 5.57}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 1.045, "y": 1, "curve": "stepped"}, {"time": 2.1667, "x": 1.045, "y": 1}, {"time": 3, "x": 1, "y": 1}]}, "item2": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": 9.74, "curve": "stepped"}, {"time": 2.1667, "angle": 9.74}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "item3": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": 9.74, "curve": "stepped"}, {"time": 2.1667, "angle": 9.74}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "item4": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": 9.74}, {"time": 1.3333, "angle": 14.77}, {"time": 2.1667, "angle": 9.74}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "item5": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.5, "angle": 18.69}, {"time": 2.5, "angle": 5.03}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "item6": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.5, "angle": -8}, {"time": 2, "angle": -0.2}, {"time": 2.5, "angle": -8}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "text2": {"translate": [{"time": 0.5, "x": 0.95, "y": -0.02}, {"time": 1.3333, "x": 1.31, "y": -0.03}, {"time": 2, "x": 2.04, "y": -0.05}]}}, "deform": {"default": {"item": {"item": [{"time": 0}, {"time": 1, "offset": 126, "vertices": [5.30907, -3.80672, 6.18942, -2.09017, 4.54118, -4.69604, 16.50005, -6.24819, 17.60556, -1.15677, 15.11376, -9.10269, 18.2514, -6.2894, 19.29254, -0.68471, 16.82946, -9.45711, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.28989, -2.70834, 4.89385, -1.33736, 3.73532, -3.433], "curve": "stepped"}, {"time": 2.1667, "offset": 126, "vertices": [5.30907, -3.80672, 6.18942, -2.09017, 4.54118, -4.69604, 16.50005, -6.24819, 17.60556, -1.15677, 15.11376, -9.10269, 18.2514, -6.2894, 19.29254, -0.68471, 16.82946, -9.45711, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.28989, -2.70834, 4.89385, -1.33736, 3.73532, -3.433]}, {"time": 3}]}}}}, "win": {"slots": {"sprites_01": {"attachment": [{"time": 0, "name": "sprite_01"}, {"time": 0.1333, "name": "sprite_02"}, {"time": 0.2667, "name": "sprite_03"}, {"time": 0.4, "name": "sprite_04"}, {"time": 0.5333, "name": "sprite_05"}, {"time": 0.6667, "name": "sprite_06"}, {"time": 0.8, "name": "sprite_07"}, {"time": 0.9, "name": "sprite_08"}, {"time": 1.0333, "name": "sprite_01"}, {"time": 1.1667, "name": "sprite_02"}, {"time": 1.3, "name": "sprite_03"}, {"time": 1.4333, "name": "sprite_04"}, {"time": 1.5667, "name": "sprite_05"}, {"time": 1.7, "name": "sprite_06"}, {"time": 1.8333, "name": "sprite_07"}, {"time": 1.9667, "name": "sprite_08"}, {"time": 2.1, "name": "sprite_01"}, {"time": 2.2333, "name": "sprite_02"}, {"time": 2.3667, "name": "sprite_03"}, {"time": 2.5, "name": "sprite_04"}, {"time": 2.6333, "name": "sprite_05"}, {"time": 2.7333, "name": "sprite_06"}, {"time": 2.8667, "name": "sprite_07"}, {"time": 3, "name": "sprite_08"}]}, "text2": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}, {"time": 1.3333, "color": "ffffff8d"}, {"time": 1.6667, "color": "ffffff00"}, {"time": 2, "color": "ffffff8d"}, {"time": 2.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}, {"time": 1.1667, "x": 0, "y": 2.93}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 1.5, "x": 0, "y": 2.93}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.8333, "x": 0, "y": 2.93}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "bg": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "text": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}, {"time": 1, "x": 0, "y": -6.56}, {"time": 1.3333, "x": -8.16, "y": 3.32}, {"time": 1.6667, "x": 0, "y": -6.56}, {"time": 2, "x": -8.16, "y": 3.32}, {"time": 2.3333, "x": 0, "y": -6.56}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1}, {"time": 1, "x": 1.267, "y": 1.267, "curve": "stepped"}, {"time": 1.6667, "x": 1.267, "y": 1.267, "curve": "stepped"}, {"time": 2.3333, "x": 1.267, "y": 1.267}, {"time": 3, "x": 1, "y": 1}]}, "item": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": 9.74, "curve": "stepped"}, {"time": 2.1667, "angle": 9.74}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1, "x": 0, "y": 5.57, "curve": "stepped"}, {"time": 2.1667, "x": 0, "y": 5.57}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 1.045, "y": 1, "curve": "stepped"}, {"time": 2.1667, "x": 1.045, "y": 1}, {"time": 3, "x": 1, "y": 1}]}, "item2": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": 9.74, "curve": "stepped"}, {"time": 2.1667, "angle": 9.74}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "item3": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": 9.74, "curve": "stepped"}, {"time": 2.1667, "angle": 9.74}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "item4": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": 9.74, "curve": "stepped"}, {"time": 2.1667, "angle": 9.74}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "item5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "item6": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}}, "deform": {"default": {"item": {"item": [{"time": 0}, {"time": 1, "offset": 126, "vertices": [5.30907, -3.80672, 6.18942, -2.09017, 4.54118, -4.69604, 16.50005, -6.24819, 17.60556, -1.15677, 15.11376, -9.10269, 18.2514, -6.2894, 19.29254, -0.68471, 16.82946, -9.45711, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.28989, -2.70834, 4.89385, -1.33736, 3.73532, -3.433], "curve": "stepped"}, {"time": 2.1667, "offset": 126, "vertices": [5.30907, -3.80672, 6.18942, -2.09017, 4.54118, -4.69604, 16.50005, -6.24819, 17.60556, -1.15677, 15.11376, -9.10269, 18.2514, -6.2894, 19.29254, -0.68471, 16.82946, -9.45711, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.28989, -2.70834, 4.89385, -1.33736, 3.73532, -3.433]}, {"time": 3}]}}}}}}