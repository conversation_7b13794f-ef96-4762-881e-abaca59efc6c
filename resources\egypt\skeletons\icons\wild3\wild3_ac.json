{"skeleton": {"hash": "oJ27TQvvCukLEt4+8ecS69fL3i0", "spine": "3.6.53", "width": 377.5, "height": 633, "images": "./images/"}, "bones": [{"name": "root"}, {"name": "princes", "parent": "root", "x": 5.27, "y": 199.66}, {"name": "eyes", "parent": "princes", "x": 16.06, "y": 266.76}, {"name": "g", "parent": "root", "y": 240.42}, {"name": "hand", "parent": "princes", "length": 68.71, "rotation": 104.35, "x": -105.22, "y": 15.86}, {"name": "hand2", "parent": "hand", "length": 60, "rotation": -7.68, "x": 68.71}, {"name": "hand3", "parent": "hand2", "length": 48.49, "rotation": -14.93, "x": 60}, {"name": "hand4", "parent": "hand3", "length": 29.14, "rotation": -64.75, "x": 48.49}, {"name": "princes2", "parent": "princes", "x": 46.19, "y": 94.55}, {"name": "princes3", "parent": "princes2", "x": -88.71}, {"name": "princes4", "parent": "princes", "length": 67.54, "rotation": 84.85, "x": -0.05, "y": 6.53}, {"name": "princes5", "parent": "princes", "length": 64.28, "rotation": 92.16, "x": 6.01, "y": 73.8}, {"name": "princes6", "parent": "princes", "length": 55.55, "rotation": 96.89, "x": 3.59, "y": 138.03}, {"name": "princes7", "parent": "princes", "length": 17.57, "rotation": 90, "x": -3.08, "y": 193.18}, {"name": "princes8", "parent": "princes", "length": 93.32, "rotation": 90, "x": -3.08, "y": 210.75}, {"name": "princes9", "parent": "princes", "length": 127.23, "rotation": -56.46, "x": 78.73, "y": 158.64}, {"name": "princes10", "parent": "princes9", "length": 43.7, "rotation": -67.23, "x": 127.23}, {"name": "princes11", "parent": "princes10", "length": 50.68, "rotation": -7.43, "x": 44.37, "y": -1.01}, {"name": "princes12", "parent": "princes11", "length": 35.79, "rotation": -77.18, "x": 50.68}, {"name": "princes13", "parent": "princes12", "length": 38.48, "rotation": 54.46, "x": 35.79}, {"name": "princes14", "parent": "princes4", "length": 106.61, "rotation": 176.65, "x": 140.92, "y": 97.27}, {"name": "princes15", "parent": "princes4", "length": 120.96, "rotation": 171.53, "x": -79.5, "y": -63.14}, {"name": "princes16", "parent": "princes15", "x": -81.27, "y": -78.92}, {"name": "princes17", "parent": "princes16", "length": 96.11, "rotation": 34.68, "x": 125.81, "y": -40.6}, {"name": "sand", "parent": "root", "y": 292.54}, {"name": "stick", "parent": "hand", "length": 484.97, "rotation": 0.73, "x": -180.64, "y": -44.07}], "slots": [{"name": "images/g0_01", "bone": "g"}, {"name": "princes", "bone": "princes", "attachment": "princes"}, {"name": "stick", "bone": "stick", "attachment": "stick"}, {"name": "hand", "bone": "hand", "attachment": "hand"}, {"name": "eyes", "bone": "eyes", "attachment": "eyes"}, {"name": "0_03", "bone": "sand"}], "skins": {"default": {"0_03": {"0_03": {"width": 292, "height": 504}, "0_04": {"width": 292, "height": 504}, "0_05": {"width": 292, "height": 504}, "0_06": {"width": 292, "height": 504}, "1_01": {"width": 292, "height": 504}, "1_04": {"width": 292, "height": 504}, "2_02": {"width": 292, "height": 504}, "2_06": {"width": 292, "height": 504}, "3_03": {"width": 292, "height": 504}, "4_01": {"width": 292, "height": 504}, "4_05": {"width": 292, "height": 504}, "5_03": {"width": 292, "height": 504}, "6_01": {"width": 292, "height": 504}, "6_06": {"width": 292, "height": 504}}, "eyes": {"eyes": {"x": -18.88, "y": 1.82, "width": 71, "height": 29}}, "hand": {"hand": {"x": 94.72, "y": 6.7, "rotation": -104.35, "width": 120, "height": 224}}, "images/g0_01": {"images/g0_01": {"width": 186, "height": 481}, "images/g0_02": {"width": 186, "height": 481}, "images/g0_03": {"width": 186, "height": 481}, "images/g0_04": {"width": 186, "height": 481}, "images/g0_05": {"width": 186, "height": 481}, "images/g0_06": {"width": 186, "height": 483}, "images/g0_07": {"width": 186, "height": 483}, "images/g0_08": {"width": 186, "height": 483}, "images/g0_09": {"width": 186, "height": 483}, "images/g0_10": {"width": 186, "height": 483}, "images/g1_01": {"width": 186, "height": 481}, "images/g1_02": {"width": 186, "height": 481}, "images/g1_03": {"width": 186, "height": 481}, "images/g1_04": {"width": 186, "height": 481}, "images/g1_05": {"width": 186, "height": 481}, "images/g1_06": {"width": 186, "height": 483}, "images/g1_07": {"width": 186, "height": 483}, "images/g1_08": {"width": 186, "height": 483}, "images/g1_09": {"width": 186, "height": 483}, "images/g1_10": {"width": 186, "height": 483}, "images/g2_01": {"width": 186, "height": 481}, "images/g2_02": {"width": 186, "height": 481}, "images/g2_03": {"width": 186, "height": 481}, "images/g2_04": {"width": 186, "height": 481}, "images/g2_05": {"width": 186, "height": 481}, "images/g2_06": {"width": 186, "height": 483}, "images/g2_07": {"width": 186, "height": 483}, "images/g2_08": {"width": 186, "height": 483}, "images/g2_09": {"width": 186, "height": 483}, "images/g2_10": {"width": 186, "height": 483}, "images/g3_01": {"width": 186, "height": 481}, "images/g3_02": {"width": 186, "height": 481}, "images/g3_03": {"width": 186, "height": 481}, "images/g3_04": {"width": 186, "height": 481}, "images/g3_05": {"width": 186, "height": 481}, "images/g3_06": {"width": 186, "height": 483}, "images/g3_07": {"width": 186, "height": 483}, "images/g3_08": {"width": 186, "height": 483}, "images/g3_09": {"width": 186, "height": 483}, "images/g3_10": {"width": 186, "height": 483}, "images/g4_01": {"width": 186, "height": 481}, "images/g4_02": {"width": 186, "height": 481}, "images/g4_03": {"width": 186, "height": 481}, "images/g4_04": {"width": 186, "height": 481}, "images/g4_05": {"width": 186, "height": 481}}, "princes": {"princes": {"type": "mesh", "uvs": [0.78717, 0.04687, 0.78717, 0.10751, 0.68385, 0.17354, 0.71989, 0.28269, 0.78717, 0.30829, 0.90731, 0.3878, 1, 0.49695, 1, 0.57645, 1, 0.7732, 1, 0.95512, 0.87848, 1, 0.18647, 1, 0, 0.93895, 0, 0.83231, 0.07942, 0.72124, 0.13723, 0.62891, 0.1162, 0.60926, 0.10919, 0.58469, 0.12847, 0.42059, 0.15125, 0.35378, 0.23009, 0.30956, 0.25461, 0.26143, 0.28265, 0.18968, 0.21958, 0.15529, 0.16351, 0.07568, 0.2003, 0, 0.83523, 0, 0.90251, 0.65057, 0.38245, 0.31017, 0.32441, 0.32644, 0.26637, 0.32931, 0.35172, 0.35612, 0.38757, 0.36282, 0.41147, 0.39633, 0.49853, 0.42792, 0.50365, 0.48153, 0.54974, 0.40782, 0.58046, 0.38293, 0.58559, 0.35229, 0.62655, 0.33697, 0.64021, 0.31974, 0.56169, 0.30251, 0.59583, 0.31783, 0.6863, 0.3207, 0.80579, 0.4126, 0.93553, 0.52174, 0.95772, 0.54759, 0.88261, 0.61173, 0.76312, 0.67683, 0.70337, 0.68162, 0.72215, 0.63375, 0.75458, 0.61843, 0.80921, 0.56674, 0.82969, 0.53897, 0.70166, 0.44802, 0.71361, 0.48536, 0.67947, 0.52174, 0.62655, 0.53323, 0.56851, 0.52844, 0.52584, 0.51025, 0.48487, 0.51025, 0.45926, 0.52461, 0.39952, 0.53131, 0.34489, 0.5294, 0.30563, 0.50834, 0.28856, 0.49015, 0.28856, 0.44898, 0.21516, 0.34846, 0.18784, 0.36569, 0.1776, 0.42984, 0.14688, 0.54376, 0.14688, 0.59546, 0.17248, 0.61365, 0.21004, 0.61652, 0.25271, 0.60694, 0.28173, 0.55142, 0.33977, 0.55908, 0.32782, 0.5811, 0.28856, 0.63088, 0.2254, 0.70747, 0.66582, 0.56386, 0.65557, 0.57918, 0.65045, 0.60599, 0.66923, 0.6529, 0.70679, 0.70172, 0.74605, 0.7448, 0.73751, 0.82043, 0.67435, 0.90564, 0.56339, 0.7515, 0.46268, 0.90851, 0.36538, 0.74384, 0.34319, 0.8817, 0.28002, 0.95925, 0.3705, 0.67396, 0.38757, 0.71129, 0.53949, 0.72757, 0.55998, 0.69023], "triangles": [16, 17, 71, 70, 75, 73, 70, 72, 71, 71, 17, 70, 17, 18, 70, 75, 64, 76, 76, 64, 63, 75, 65, 64, 75, 70, 65, 70, 69, 65, 70, 18, 69, 61, 62, 33, 62, 63, 33, 32, 33, 31, 31, 33, 66, 63, 66, 33, 60, 61, 34, 34, 61, 33, 65, 66, 64, 63, 64, 66, 65, 69, 66, 67, 66, 69, 67, 69, 68, 67, 30, 66, 31, 30, 29, 31, 66, 30, 69, 18, 68, 68, 18, 19, 41, 33, 28, 68, 19, 67, 31, 28, 32, 33, 32, 28, 31, 29, 28, 20, 67, 19, 67, 20, 30, 29, 30, 21, 30, 20, 21, 28, 29, 22, 41, 28, 22, 22, 29, 21, 41, 22, 2, 2, 22, 25, 1, 2, 0, 24, 25, 23, 0, 2, 25, 25, 22, 23, 0, 25, 26, 27, 46, 7, 27, 47, 46, 47, 45, 46, 47, 53, 45, 46, 6, 7, 46, 45, 6, 53, 44, 45, 44, 5, 45, 45, 5, 6, 44, 43, 4, 43, 3, 4, 44, 4, 5, 3, 40, 2, 10, 92, 89, 10, 11, 92, 92, 91, 89, 89, 87, 10, 87, 86, 10, 10, 86, 8, 8, 86, 85, 9, 10, 8, 85, 27, 8, 11, 13, 14, 11, 14, 79, 79, 14, 15, 11, 79, 92, 79, 72, 73, 72, 79, 15, 92, 79, 91, 27, 85, 48, 11, 12, 13, 91, 90, 89, 89, 90, 95, 89, 88, 87, 89, 95, 88, 95, 90, 94, 87, 88, 86, 91, 79, 90, 88, 84, 86, 86, 84, 85, 27, 7, 8, 95, 96, 88, 88, 83, 84, 88, 96, 83, 85, 84, 48, 95, 94, 96, 93, 61, 96, 79, 78, 90, 90, 93, 94, 90, 78, 93, 61, 93, 62, 58, 96, 61, 61, 60, 59, 96, 94, 93, 79, 74, 78, 79, 73, 74, 83, 49, 84, 84, 49, 48, 96, 82, 83, 49, 50, 48, 49, 83, 50, 48, 47, 27, 47, 48, 51, 78, 77, 93, 93, 77, 62, 62, 77, 76, 62, 76, 63, 78, 74, 77, 15, 71, 72, 15, 16, 71, 75, 74, 73, 70, 73, 72, 74, 75, 77, 77, 75, 76, 60, 35, 59, 60, 34, 35, 33, 41, 34, 47, 51, 52, 80, 56, 51, 51, 56, 52, 52, 56, 55, 52, 53, 47, 52, 55, 53, 80, 57, 56, 53, 54, 44, 56, 57, 54, 58, 54, 57, 58, 59, 36, 37, 54, 58, 37, 58, 36, 39, 54, 37, 56, 54, 55, 39, 37, 38, 39, 43, 54, 39, 40, 43, 59, 35, 36, 53, 55, 54, 35, 34, 36, 54, 43, 44, 36, 34, 41, 38, 37, 36, 38, 36, 41, 38, 42, 39, 38, 41, 42, 39, 42, 40, 43, 40, 3, 2, 40, 42, 42, 41, 2, 82, 58, 57, 82, 96, 58, 58, 61, 59, 50, 51, 48, 83, 82, 50, 82, 81, 50, 81, 80, 50, 50, 80, 51, 82, 57, 81, 81, 57, 80], "vertices": [2, 14, 155.05, -109.83, 0.99992, 15, -157.19, 137.82, 8e-05, 2, 14, 116.67, -109.83, 0.99661, 15, -125.2, 116.61, 0.00339, 3, 13, 92.44, -73.16, 0.00535, 14, 74.87, -73.16, 0.93562, 15, -110.63, 62.95, 0.05903, 4, 12, 68.41, -88.13, 0.03409, 13, 23.35, -85.95, 0.06978, 14, 5.78, -85.95, 0.32771, 15, -45.97, 35.44, 0.56842, 4, 12, 49.46, -109.9, 0.00604, 13, 7.14, -109.83, 0.02721, 14, -10.43, -109.83, 0.09433, 15, -19.26, 46.39, 0.87241, 1, 15, 46.25, 54.13, 1, 2, 15, 122.02, 43.39, 0.94627, 16, -42.02, 11.99, 0.05373, 4, 21, -135.63, 97.41, 0.00476, 15, 163.97, 15.58, 0.12592, 16, -0.15, 39.9, 0.78358, 17, -49.44, 34.81, 0.08575, 3, 21, -14.6, 126.73, 0.44864, 16, 103.48, 108.98, 0.0759, 17, 44.38, 116.71, 0.47547, 3, 21, 97.32, 153.85, 0.81088, 16, 199.29, 172.86, 0.00072, 17, 131.13, 192.44, 0.1884, 2, 21, 135.09, 118.61, 0.8651, 17, 180.9, 178.63, 0.1349, 1, 23, 121.67, -84.95, 1, 1, 23, 61.81, -132.83, 1, 3, 20, 296.9, -45.02, 0.0079, 22, 186.63, -130.55, 0.00037, 23, -1.18, -108.57, 0.99173, 4, 1, -144.5, -61.08, 0.05547, 20, 223.2, -27.53, 0.13984, 22, 111.66, -119.71, 0.05819, 23, -56.65, -56.99, 0.74651, 5, 1, -123.97, -2.63, 0.11183, 9, -81.46, -97.18, 0.0001, 20, 162.36, -15.87, 0.51341, 22, 50.02, -113.52, 0.12655, 23, -103.82, -16.84, 0.2481, 4, 1, -131.44, 9.81, 0.07865, 20, 151.16, -25.09, 0.67904, 22, 39.69, -123.7, 0.09266, 23, -118.11, -19.33, 0.14965, 4, 1, -133.93, 25.36, 0.04784, 20, 136.15, -29.85, 0.8082, 22, 25.16, -129.78, 0.0559, 23, -133.51, -16.06, 0.08806, 1, 20, 32.41, -38.43, 1, 3, 20, -10.62, -36.69, 0.973, 13, -21.65, 115.92, 0.01353, 14, -39.22, 115.92, 0.01347, 4, 20, -42.44, -13.14, 0.66447, 12, 72.4, 86.53, 0.04192, 13, 6.34, 87.93, 0.11751, 14, -11.23, 87.93, 0.1761, 4, 20, -73.85, -9.03, 0.31461, 12, 101.59, 74.24, 0.01921, 13, 36.8, 79.22, 0.15564, 14, 19.23, 79.22, 0.51054, 3, 20, -120.25, -5.9, 0.0581, 13, 82.23, 69.27, 0.03509, 14, 64.65, 69.27, 0.90681, 3, 20, -138.47, -31.26, 0.01253, 13, 104, 91.66, 0.00532, 14, 86.42, 91.66, 0.98216, 2, 20, -185.36, -58.4, 0.00013, 14, 136.81, 111.57, 0.99987, 1, 14, 184.72, 98.5, 1, 1, 14, 184.72, -126.89, 1, 3, 21, -81.89, 74.82, 0.06708, 16, 58.09, 37.13, 0.34575, 17, 8.67, 39.59, 0.58717, 5, 9, 5.6, 104.58, 0.00593, 20, -50.05, 40.41, 0.21961, 12, 65.52, 32.88, 0.22585, 13, 5.96, 33.84, 0.33336, 14, -11.62, 33.84, 0.21525, 6, 9, -15.01, 94.28, 0.01132, 20, -36.82, 21.55, 0.47433, 11, 117.35, 59.15, 0.00014, 12, 57.77, 54.58, 0.19161, 13, -4.35, 54.45, 0.1772, 14, -21.92, 54.45, 0.14541, 5, 9, -35.61, 92.46, 0.00277, 20, -31.97, 1.44, 0.69461, 12, 58.43, 75.25, 0.07153, 13, -6.16, 75.05, 0.10736, 14, -23.74, 75.05, 0.12373, 6, 9, -5.31, 75.5, 0.05348, 20, -19.67, 33.92, 0.43494, 11, 98.21, 50.17, 0.01262, 12, 37.95, 47.2, 0.36859, 13, -23.13, 44.75, 0.0978, 14, -40.71, 44.75, 0.03257, 6, 9, 7.42, 71.25, 0.06798, 20, -17.35, 47.13, 0.27366, 11, 93.49, 37.62, 0.02819, 12, 32.21, 35.08, 0.54857, 13, -27.37, 32.03, 0.07216, 14, -44.95, 32.03, 0.00943, 5, 9, 15.9, 50.04, 0.16107, 20, 2.37, 58.66, 0.15978, 11, 71.98, 29.94, 0.16678, 12, 10.14, 29.2, 0.50766, 13, -48.58, 23.54, 0.00471, 2, 8, -41.91, 30.05, 0.00033, 11, 50.83, -0.19, 0.99967, 4, 8, -40.09, -3.89, 0.00498, 11, 16.85, -0.73, 0.99501, 18, 127.47, -59.26, 1e-05, 19, 5.07, -109.05, 1e-05, 5, 8, -23.73, 42.77, 0.12342, 11, 62.86, -18.84, 0.38508, 12, -2.97, -18.66, 0.38226, 13, -55.86, -25.54, 0.00104, 15, -13.32, -58.67, 0.1082, 6, 8, -12.82, 58.53, 0.09488, 11, 78.19, -30.33, 0.11483, 12, 11.36, -31.38, 0.48844, 13, -40.1, -36.45, 0.01857, 14, -57.67, -36.45, 0.00376, 15, -20.43, -40.88, 0.27951, 6, 8, -11, 77.92, 0.02839, 11, 97.5, -32.88, 0.01748, 12, 30.4, -35.51, 0.49539, 13, -20.71, -38.27, 0.08921, 14, -38.28, -38.27, 0.04323, 15, -35.59, -28.65, 0.3263, 6, 8, 3.54, 87.62, 0.01281, 11, 106.64, -47.78, 0.00465, 12, 38.28, -51.11, 0.26364, 13, -11.01, -52.81, 0.10392, 14, -28.59, -52.81, 0.11276, 15, -35.63, -11.17, 0.50221, 6, 8, 8.39, 98.52, 0.00282, 11, 117.36, -53.04, 0.00016, 12, 48.52, -57.23, 0.17134, 13, -0.1, -57.66, 0.11602, 14, -17.68, -57.66, 0.19935, 15, -42.05, -1.1, 0.51033, 5, 8, -19.48, 109.43, 0.00029, 12, 62.7, -30.87, 0.19577, 13, 10.8, -29.79, 0.29293, 14, -6.77, -29.79, 0.33189, 15, -66.54, -18.31, 0.17912, 5, 8, -7.36, 99.74, 0.00331, 12, 51.62, -41.74, 0.26683, 13, 1.11, -41.91, 0.19334, 14, -16.47, -41.91, 0.20864, 15, -51.76, -13.56, 0.32788, 5, 8, 24.75, 97.92, 0.0005, 12, 45.96, -73.4, 0.08038, 13, -0.71, -74.02, 0.06922, 14, -18.28, -74.02, 0.1674, 15, -32.5, 12.2, 0.6825, 1, 15, 39.43, 15.42, 1, 2, 15, 122.46, 15.64, 0.87676, 16, -16.27, 1.65, 0.12324, 4, 21, -149.86, 78.52, 0.00018, 15, 140.45, 13.16, 0.41108, 16, -7.02, 17.28, 0.57863, 17, -53.33, 11.49, 0.01012, 3, 21, -104.12, 62.16, 0.01253, 16, 41.55, 17.61, 0.61678, 17, -5.21, 18.1, 0.37069, 4, 21, -54.08, 30.64, 0.10004, 16, 99.37, 5.18, 0.01107, 17, 53.73, 13.25, 0.60075, 18, -12.24, 5.92, 0.28815, 4, 21, -46.14, 10.74, 0.17743, 17, 69.97, -0.74, 0.10294, 18, 5, 18.64, 0.65741, 19, -2.73, 35.89, 0.06222, 5, 8, 37.48, -100.24, 0.00887, 10, -4.66, -84.48, 0.00639, 15, 139.7, -86.67, 0.00228, 17, 42.76, -15.64, 0.27386, 18, 13.5, -11.2, 0.7086, 7, 8, 48.99, -90.55, 0.02008, 10, 6.03, -95.08, 0.0137, 15, 137.98, -71.72, 0.00765, 16, 70.29, -17.85, 0.00068, 17, 27.88, -13.35, 0.66985, 18, 7.95, -25.19, 0.28628, 19, -36.68, 8.01, 0.00175, 7, 8, 68.39, -57.82, 0.05834, 10, 40.36, -111.45, 0.01918, 15, 121.42, -37.48, 0.13919, 16, 32.31, -19.87, 0.61784, 17, -9.53, -20.26, 0.1055, 18, 6.4, -63.2, 0.05629, 19, -68.51, -12.81, 0.00366, 7, 8, 75.66, -40.25, 0.05247, 10, 58.51, -117.12, 0.01152, 15, 110.79, -21.71, 0.46877, 16, 13.65, -23.56, 0.43244, 17, -27.55, -26.34, 0.00872, 18, 8.32, -82.12, 0.02412, 19, -82.79, -25.38, 0.00196, 9, 8, 30.21, 17.32, 0.39737, 10, 111.77, -66.69, 0.00015, 11, 35.39, -71.77, 0.00508, 12, -34.71, -69.15, 0.0083, 15, 37.69, -27.78, 0.58349, 16, -9.04, -93.31, 0.00283, 17, -41.02, -98.44, 4e-05, 18, 75.64, -111.26, 0.00248, 19, -67.38, -97.09, 0.00026, 7, 8, 34.45, -6.31, 0.46533, 10, 88.62, -73.03, 0.03126, 15, 59.73, -37.3, 0.4359, 16, 8.27, -76.68, 0.02973, 17, -26.01, -79.7, 0.00608, 18, 60.7, -92.46, 0.02366, 19, -60.77, -74.01, 0.00803, 7, 8, 22.33, -29.34, 0.48664, 10, 64.59, -63.03, 0.15247, 15, 72.23, -60.13, 0.16023, 16, 34.15, -73.99, 0.05103, 17, -0.69, -73.69, 0.03035, 18, 60.45, -66.44, 0.0756, 19, -39.73, -58.69, 0.04368, 8, 8, 3.54, -36.61, 0.41913, 10, 55.67, -44.97, 0.34292, 11, -17.5, -43.1, 0.0114, 15, 67.91, -79.8, 0.03958, 16, 50.63, -85.58, 0.0218, 17, 17.14, -83.06, 0.02043, 18, 73.54, -51.13, 0.06754, 19, -19.67, -60.44, 0.07719, 8, 8, -17.06, -33.58, 0.26962, 10, 56.83, -24.18, 0.57249, 11, -13.69, -22.62, 0.07744, 15, 54.01, -95.3, 0.00225, 16, 59.53, -104.41, 0.00485, 17, 28.41, -100.57, 0.00477, 18, 93.12, -44.03, 0.02315, 19, -2.51, -72.24, 0.04542, 7, 8, -32.21, -22.07, 0.09375, 10, 66.94, -8.06, 0.51343, 11, -1.61, -7.92, 0.38349, 16, 58.36, -123.4, 0.00046, 17, 29.7, -119.55, 0.00039, 18, 111.92, -46.99, 0.00288, 19, 6.01, -89.26, 0.0056, 5, 9, 41.96, -22.07, 0.04855, 10, 65.64, 6.43, 0.55992, 20, 69.84, 95.09, 0.00102, 22, -52.03, -11.26, 0.01875, 11, -1.07, 6.62, 0.37176, 5, 9, 32.87, -31.16, 0.14164, 10, 55.77, 14.67, 0.61754, 20, 80.17, 87.44, 0.01158, 22, -41.06, -17.96, 0.10926, 11, -9.81, 16.04, 0.11998, 7, 1, -30.86, 59.15, 0.01091, 9, 11.66, -35.4, 0.34594, 10, 49.64, 35.41, 0.22668, 20, 87.5, 67.09, 0.0903, 22, -31.94, -37.57, 0.28278, 23, -128, 92.26, 0.00166, 11, -13.25, 37.4, 0.04173, 7, 1, -50.25, 60.36, 0.02025, 9, -7.73, -34.19, 0.39992, 10, 49.11, 54.83, 0.06676, 20, 89.17, 47.73, 0.25828, 22, -28.56, -56.71, 0.2484, 23, -136.1, 74.6, 0.0034, 11, -11.3, 56.73, 0.00299, 6, 1, -64.19, 73.69, 0.00599, 9, -21.67, -20.86, 0.43559, 10, 61.14, 69.91, 0.01723, 20, 78.05, 31.98, 0.44265, 22, -38.23, -73.39, 0.09839, 23, -153.55, 66.38, 0.00016, 5, 1, -70.25, 85.21, 0.00086, 9, -27.73, -9.34, 0.40059, 10, 72.06, 76.98, 0.00388, 20, 67.55, 24.28, 0.56048, 22, -47.99, -81.99, 0.03419, 5, 9, -27.73, 16.71, 0.32665, 20, 41.78, 20.43, 0.64694, 22, -73.32, -88.13, 0.00025, 11, 40.32, 74.8, 0.00633, 12, -17.71, 76.52, 0.01983, 4, 20, -17.3, -14.75, 0.913, 12, 48.58, 94.75, 0.01086, 13, -18.28, 93.23, 0.03663, 14, -35.86, 93.23, 0.03952, 3, 20, -5.08, -22.72, 0.97487, 13, -29.19, 102.93, 0.01251, 14, -46.77, 102.93, 0.01262, 1, 20, 35.62, -20.32, 1, 4, 1, -120.55, 51.27, 0.00344, 20, 108.55, -20.45, 0.98269, 22, -3.17, -122.88, 0.00341, 23, -152.89, 5.73, 0.01046, 5, 1, -120.55, 18.55, 0.06872, 9, -78.03, -76, 0.00036, 20, 140.91, -15.61, 0.72693, 22, 28.63, -115.18, 0.08472, 23, -122.35, -6.03, 0.11928, 5, 1, -111.46, 7.03, 0.10649, 9, -68.94, -87.52, 0.00228, 20, 150.96, -4.92, 0.57922, 22, 37.69, -103.63, 0.13204, 23, -108.34, -1.69, 0.17997, 6, 1, -98.13, 5.21, 0.13671, 9, -55.61, -89.34, 0.00878, 20, 150.79, 8.53, 0.49663, 22, 36.31, -90.25, 0.18104, 23, -101.85, 10.1, 0.17681, 19, 136.3, -78.31, 3e-05, 6, 1, -82.98, 11.27, 0.15613, 9, -40.46, -83.28, 0.02759, 20, 142.55, 22.62, 0.43919, 22, 26.86, -76.95, 0.25043, 23, -102.06, 26.42, 0.12658, 19, 120.03, -77.07, 7e-05, 6, 1, -72.68, 46.42, 0.0482, 9, -30.16, -48.13, 0.15377, 10, 33.21, 75.91, 0.00618, 20, 106.27, 27.61, 0.55638, 22, -9.73, -75.22, 0.21849, 23, -131.16, 48.66, 0.01697, 6, 1, -52.07, 41.57, 0.07088, 9, -9.55, -52.98, 0.19919, 10, 30.23, 54.96, 0.02906, 20, 108.02, 48.71, 0.284, 22, -9.87, -54.05, 0.39804, 23, -119.23, 66.15, 0.01883, 6, 1, -56.32, 27.63, 0.13283, 9, -13.8, -66.92, 0.10618, 10, 15.97, 57.93, 0.00328, 20, 122.44, 46.57, 0.2909, 22, 4.68, -54.89, 0.42255, 23, -107.74, 57.18, 0.04426, 7, 1, -70.25, -3.88, 0.24194, 9, -27.73, -98.42, 0.01998, 20, 155.66, 37.45, 0.26964, 21, -42.69, -139.94, 0.00019, 22, 38.58, -61.02, 0.29665, 23, -83.35, 32.85, 0.16791, 19, 115.28, -57.87, 0.00368, 7, 1, -92.67, -52.36, 0.14763, 9, -50.15, -146.91, 0.00017, 20, 206.92, 22.43, 0.15637, 21, 9.71, -150.32, 0.00032, 22, 90.98, -71.39, 0.11384, 23, -46.16, -5.5, 0.5768, 19, 156.78, -24.24, 0.00487, 7, 8, 17.48, -56.01, 0.24576, 10, 37.6, -60.59, 0.25222, 15, 91.78, -78.9, 0.05808, 16, 59.03, -63.23, 0.04628, 17, 22.58, -59.8, 0.07327, 18, 52.08, -40.67, 0.18438, 19, -23.63, -36.89, 0.14001, 7, 8, 13.85, -65.7, 0.17088, 10, 27.62, -57.84, 0.25842, 15, 97.85, -87.29, 0.03142, 16, 69.11, -60.88, 0.029, 17, 32.28, -56.16, 0.07031, 18, 50.68, -30.41, 0.22633, 19, -16.09, -29.79, 0.21364, 8, 1, 58.22, 11.88, 0.00034, 8, 12.03, -82.67, 0.06778, 10, 10.56, -57.55, 0.15698, 15, 110.99, -98.18, 0.00954, 16, 84.24, -52.98, 0.00978, 17, 46.26, -46.38, 0.05012, 18, 44.24, -14.6, 0.3254, 19, -6.97, -15.36, 0.38006, 3, 21, -60.96, -5.32, 0.02856, 18, 24.29, 8.38, 0.88653, 19, 0.13, 14.23, 0.08491, 5, 21, -34.06, 14.91, 0.36684, 16, 123.57, -2.72, 0.00103, 17, 78.75, 8.54, 0.14806, 18, -2.1, 29.27, 0.43209, 19, 1.79, 47.85, 0.05197, 4, 21, -10.84, 34.88, 0.63146, 16, 138.53, 24, 0.00892, 17, 90.13, 36.98, 0.24316, 18, -27.3, 46.67, 0.11646, 4, 21, 36.4, 43.21, 0.86153, 16, 180.04, 48.04, 0.00317, 17, 128.19, 66.18, 0.13258, 18, -47.33, 90.26, 0.00272, 2, 21, 94.1, 34.12, 0.96247, 17, 183.56, 84.76, 0.03753, 6, 1, 27.31, -80.23, 0.06644, 21, 8.55, -27.14, 0.67453, 22, 89.82, 51.79, 0.00072, 23, 22.97, 96.45, 0.08656, 18, 27.78, 81.15, 0.00199, 19, 61.38, 53.69, 0.16975, 3, 21, 113.56, -38.49, 0.44407, 23, 102.87, 27.37, 0.5559, 19, 137.29, 127.12, 3e-05, 6, 1, -42.98, -75.38, 0.20346, 20, 222.35, 74.98, 0.01938, 21, 20.39, -96.6, 0.07747, 22, 101.66, -17.68, 0.05132, 23, -6.82, 32.6, 0.57224, 19, 122.33, 18.34, 0.07613, 1, 23, 71.79, -6.12, 1, 2, 21, 160.04, -93.94, 0.00311, 23, 109.53, -44.68, 0.99689, 7, 1, -41.16, -31.15, 0.42278, 9, 1.36, -125.7, 0.00369, 20, 178.33, 70.24, 0.07794, 21, -23.03, -105.25, 0.01776, 22, 58.24, -26.33, 0.20359, 23, -47.44, 50.19, 0.21328, 19, 101.2, -20.56, 0.06097, 7, 1, -35.1, -54.78, 0.34928, 9, 7.42, -149.33, 0.00023, 20, 200.81, 79.73, 0.03651, 21, -1.49, -93.79, 0.06524, 22, 79.78, -14.87, 0.09967, 23, -23.21, 47.35, 0.33706, 19, 106.18, 3.32, 0.11201, 7, 1, 18.83, -65.08, 0.13025, 20, 203.03, 134.59, 0.00025, 21, -4.17, -38.95, 0.42294, 22, 77.09, 39.97, 0.00461, 23, 5.79, 93.98, 0.10263, 18, 42.43, 71.83, 0.00733, 19, 62.31, 36.35, 0.332, 7, 1, 26.1, -41.45, 0.08309, 20, 178.58, 138.29, 7e-05, 21, -28.86, -37.45, 0.19999, 22, 52.41, 41.48, 0.0018, 23, -13.66, 109.26, 0.03109, 18, 47.23, 47.58, 0.03024, 19, 45.37, 18.34, 0.65372], "hull": 27, "edges": [50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 24, 26, 24, 22, 20, 22, 20, 18, 18, 16, 16, 54, 16, 14, 54, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 50, 52, 0, 52, 56, 58, 58, 60, 60, 40, 58, 62, 62, 64, 64, 66, 66, 68, 68, 70, 68, 72, 72, 74, 74, 76, 76, 78, 78, 80, 82, 84, 84, 80, 80, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 60, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 130, 128, 152, 152, 154, 154, 156, 156, 158, 112, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 176, 178, 180, 182, 182, 184, 156, 186, 186, 188, 190, 192, 192, 166], "width": 355, "height": 633}}, "stick": {"stick": {"x": 242.38, "y": 12.82, "rotation": -105.08, "width": 203, "height": 584}}}}, "animations": {"appear": {"slots": {"0_03": {"color": [{"time": 1.3333, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 1.3333, "name": "0_03"}, {"time": 1.4, "name": "0_04"}, {"time": 1.4667, "name": "0_05"}, {"time": 1.5333, "name": "0_06"}, {"time": 1.6, "name": "1_01"}, {"time": 1.6667, "name": "1_04"}, {"time": 1.7333, "name": "2_02"}, {"time": 1.8, "name": "2_06"}, {"time": 1.8667, "name": "3_03"}, {"time": 1.9333, "name": "4_01"}, {"time": 2, "name": "4_05"}, {"time": 2.0667, "name": "5_03"}, {"time": 2.1333, "name": "6_01"}, {"time": 2.2, "name": "6_06"}, {"time": 2.3333, "name": null}]}, "eyes": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}, {"time": 0.4667, "name": "eyes"}, {"time": 0.7333, "name": null}, {"time": 1.3333, "name": null}, {"time": 1.7667, "name": "eyes"}, {"time": 2.0667, "name": null}, {"time": 2.3333, "name": null}]}, "hand": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3333, "color": "ffffffff"}, {"time": 3, "color": "ffffff00"}]}, "images/g0_01": {"color": [{"time": 2.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.5667, "color": "ffffff00"}, {"time": 3, "color": "ffffffff"}], "attachment": [{"time": 2.3333, "name": "images/g3_06"}, {"time": 2.5667, "name": "images/g0_01"}, {"time": 3, "name": "images/g0_01"}]}, "princes": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3333, "color": "ffffffff"}, {"time": 3, "color": "ffffff00"}]}, "stick": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3333, "color": "ffffffff"}, {"time": 3, "color": "ffffff00"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": "stepped"}, {"time": 0.4667, "angle": 0, "curve": "stepped"}, {"time": 0.7333, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0, "curve": "stepped"}, {"time": 1.7667, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": -27.68}, {"time": 0.4, "x": 0, "y": -13.26, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": -13.26}, {"time": 3, "x": -3.29, "y": 28.17}], "scale": [{"time": 0, "x": 0.87, "y": 0.87}, {"time": 0.4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1}, {"time": 3, "x": 0.661, "y": 0.661}]}, "hand": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4667, "angle": 0, "curve": "stepped"}, {"time": 0.7333, "angle": 0}, {"time": 1.3333, "angle": -14.47}, {"time": 1.7667, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "hand2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": "stepped"}, {"time": 0.4667, "angle": 0, "curve": "stepped"}, {"time": 0.7333, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "hand3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": "stepped"}, {"time": 0.4667, "angle": 0, "curve": "stepped"}, {"time": 0.7333, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "hand4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": "stepped"}, {"time": 0.4667, "angle": 0, "curve": "stepped"}, {"time": 0.7333, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "stick": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4667, "angle": 0, "curve": "stepped"}, {"time": 0.7333, "angle": 0, "curve": "stepped"}, {"time": 1.7667, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "eyes": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "sand": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.3333, "x": 25.69, "y": -16.22, "curve": "stepped"}, {"time": 2.3333, "x": 25.69, "y": -16.22, "curve": "stepped"}, {"time": 3, "x": 25.69, "y": -16.22}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1.3333, "x": 1.485, "y": 1.485, "curve": "stepped"}, {"time": 2.3333, "x": 1.485, "y": 1.485, "curve": "stepped"}, {"time": 3, "x": 1.485, "y": 1.485}]}, "princes2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.5667, "x": 6.36, "y": 11.56}, {"time": 1.3333, "x": -0.58, "y": 0.58}, {"time": 2, "x": 6.36, "y": 11.56}, {"time": 2.3333, "x": 0, "y": 0}, {"time": 3, "x": 6.36, "y": 11.56}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.5667, "x": -16.76, "y": 0}, {"time": 1.3333, "x": 2.31, "y": 0}, {"time": 2, "x": -16.76, "y": 0}, {"time": 2.3333, "x": 0, "y": 0}, {"time": 3, "x": -16.76, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes6": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes7": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes8": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes9": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes10": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes11": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes12": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes13": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes14": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes15": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes16": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes17": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "g": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}}}, "stay": {"slots": {"0_03": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 3, "name": null}]}, "eyes": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 3, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}, {"time": 3, "name": null}]}, "hand": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}]}, "images/g0_01": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 3, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "images/g0_01"}, {"time": 0.0667, "name": "images/g0_02"}, {"time": 0.1333, "name": "images/g0_03"}, {"time": 0.2, "name": "images/g0_04"}, {"time": 0.2667, "name": "images/g0_05"}, {"time": 0.3333, "name": "images/g0_06"}, {"time": 0.4333, "name": "images/g0_07"}, {"time": 0.5, "name": "images/g0_08"}, {"time": 0.5667, "name": "images/g0_09"}, {"time": 0.6333, "name": "images/g0_10"}, {"time": 0.7, "name": "images/g1_02"}, {"time": 0.7667, "name": "images/g1_03"}, {"time": 0.8333, "name": "images/g1_04"}, {"time": 0.9, "name": "images/g1_05"}, {"time": 0.9667, "name": "images/g1_06"}, {"time": 1.0333, "name": "images/g1_07"}, {"time": 1.1, "name": "images/g1_08"}, {"time": 1.1667, "name": "images/g1_09"}, {"time": 1.2333, "name": "images/g1_10"}, {"time": 1.3, "name": "images/g2_01"}, {"time": 1.3667, "name": "images/g2_02"}, {"time": 1.4333, "name": "images/g2_03"}, {"time": 1.5, "name": "images/g2_04"}, {"time": 1.5667, "name": "images/g2_05"}, {"time": 1.6333, "name": "images/g2_06"}, {"time": 1.7, "name": "images/g2_07"}, {"time": 1.7667, "name": "images/g2_08"}, {"time": 1.8333, "name": "images/g2_09"}, {"time": 1.9, "name": "images/g2_10"}, {"time": 1.9667, "name": "images/g3_01"}, {"time": 2.0667, "name": "images/g3_02"}, {"time": 2.1333, "name": "images/g3_03"}, {"time": 2.2, "name": "images/g3_04"}, {"time": 2.2667, "name": "images/g3_05"}, {"time": 2.3333, "name": "images/g3_06"}, {"time": 2.4, "name": "images/g3_07"}, {"time": 2.4667, "name": "images/g3_08"}, {"time": 2.5333, "name": "images/g3_09"}, {"time": 2.6, "name": "images/g3_10"}, {"time": 2.6667, "name": "images/g4_01"}, {"time": 2.7333, "name": "images/g4_02"}, {"time": 2.8, "name": "images/g4_03"}, {"time": 2.8667, "name": "images/g4_04"}, {"time": 2.9333, "name": "images/g4_05"}, {"time": 3, "name": "images/g0_01"}]}, "princes": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.5333, "color": "ffffff00"}, {"time": 3, "color": "ffffff00"}]}, "stick": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}]}}, "bones": {"princes12": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes8": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes10": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes7": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "stick": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "hand3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes17": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "hand2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": -13.26}, {"time": 0.5333, "x": -3.29, "y": 28.17}, {"time": 3, "x": 0, "y": -13.26}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.5333, "x": 0.661, "y": 0.661}, {"time": 3, "x": 1, "y": 1}]}, "eyes": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "sand": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 25.69, "y": -16.22, "curve": "stepped"}, {"time": 3, "x": 25.69, "y": -16.22}], "scale": [{"time": 0, "x": 1.485, "y": 1.485, "curve": "stepped"}, {"time": 3, "x": 1.485, "y": 1.485}]}, "princes15": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes13": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes11": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes9": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.5333, "x": 6.36, "y": 11.56}, {"time": 1.5, "x": -0.58, "y": 0.58}, {"time": 2.2667, "x": 6.36, "y": 11.56}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.5333, "x": -16.76, "y": 0}, {"time": 1.5, "x": 2.31, "y": 0}, {"time": 2.2667, "x": -16.76, "y": 0}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes14": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes16": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "hand4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "hand": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "princes6": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}}}}}