<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
 <dict>
  <key>frames</key>
  <dict>
   <key>boderChat.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{304,49}</string>
    <key>spriteSourceSize</key>
    <string>{304,49}</string>
    <key>textureRect</key>
    <string>{{670,301},{304,49}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>boderChonDai.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{213,56}</string>
    <key>spriteSourceSize</key>
    <string>{213,56}</string>
    <key>textureRect</key>
    <string>{{610,1236},{213,56}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>boderChonDaiCopy.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{138,56}</string>
    <key>spriteSourceSize</key>
    <string>{138,56}</string>
    <key>textureRect</key>
    <string>{{1081,363},{138,56}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>boderChonNgay.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{379,76}</string>
    <key>spriteSourceSize</key>
    <string>{379,76}</string>
    <key>textureRect</key>
    <string>{{670,71},{379,76}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key><EMAIL></key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{180,39}</string>
    <key>spriteSourceSize</key>
    <string>{180,39}</string>
    <key>textureRect</key>
    <string>{{835,1212},{180,39}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>boderCopy.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{405,494}</string>
    <key>spriteSourceSize</key>
    <string>{407,497}</string>
    <key>textureRect</key>
    <string>{{835,716},{405,494}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>boderCopy2.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{405,584}</string>
    <key>spriteSourceSize</key>
    <string>{407,587}</string>
    <key>textureRect</key>
    <string>{{0,363},{405,584}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>boderTenTien.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{269,41}</string>
    <key>spriteSourceSize</key>
    <string>{269,41}</string>
    <key>textureRect</key>
    <string>{{610,1294},{269,41}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>bpdere.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{668,361}</string>
    <key>spriteSourceSize</key>
    <string>{668,361}</string>
    <key>textureRect</key>
    <string>{{0,0},{668,361}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>butonDatCuocCopy.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{161,58}</string>
    <key>spriteSourceSize</key>
    <string>{161,59}</string>
    <key>textureRect</key>
    <string>{{1051,71},{161,58}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>buttonBackCopy.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{108,36}</string>
    <key>spriteSourceSize</key>
    <string>{108,37}</string>
    <key>textureRect</key>
    <string>{{1081,475},{108,36}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>buttonBackCopy2.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{70,36}</string>
    <key>spriteSourceSize</key>
    <string>{70,37}</string>
    <key>textureRect</key>
    <string>{{961,1253},{70,36}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>buttonChon.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{258,90}</string>
    <key>spriteSourceSize</key>
    <string>{258,91}</string>
    <key>textureRect</key>
    <string>{{670,209},{258,90}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>buttonChonOff.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{244,90}</string>
    <key>spriteSourceSize</key>
    <string>{244,91}</string>
    <key>textureRect</key>
    <string>{{930,209},{244,90}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>buttonGui.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{93,52}</string>
    <key>spriteSourceSize</key>
    <string>{93,53}</string>
    <key>textureRect</key>
    <string>{{1081,421},{93,52}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>chonDai.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{33,25}</string>
    <key>spriteSourceSize</key>
    <string>{33,25}</string>
    <key>textureRect</key>
    <string>{{367,983},{33,25}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>daDatCuocOn221112Copy6.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{124,31}</string>
    <key>spriteSourceSize</key>
    <string>{124,31}</string>
    <key>textureRect</key>
    <string>{{1081,612},{124,31}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>daDatCuocOn22111Copy5.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{124,31}</string>
    <key>spriteSourceSize</key>
    <string>{124,31}</string>
    <key>textureRect</key>
    <string>{{1081,579},{124,31}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>daDatCuocOn22111Copy55.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{124,31}</string>
    <key>spriteSourceSize</key>
    <string>{124,31}</string>
    <key>textureRect</key>
    <string>{{1081,513},{124,31}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>daDatCuocOn22111Copy6.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{124,31}</string>
    <key>spriteSourceSize</key>
    <string>{124,31}</string>
    <key>textureRect</key>
    <string>{{1081,546},{124,31}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>daDatCuocOn2212Copy3.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{124,31}</string>
    <key>spriteSourceSize</key>
    <string>{124,31}</string>
    <key>textureRect</key>
    <string>{{1081,645},{124,31}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>daDatCuocOn221Copy2.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{124,30}</string>
    <key>spriteSourceSize</key>
    <string>{124,31}</string>
    <key>textureRect</key>
    <string>{{835,1253},{124,30}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>daDatCuocOn223Copy2.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{124,30}</string>
    <key>spriteSourceSize</key>
    <string>{124,31}</string>
    <key>textureRect</key>
    <string>{{881,1294},{124,30}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>daDatCuocOn22Copy.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{124,30}</string>
    <key>spriteSourceSize</key>
    <string>{124,31}</string>
    <key>textureRect</key>
    <string>{{1081,678},{124,30}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>ghiSoDe.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{512,69}</string>
    <key>spriteSourceSize</key>
    <string>{512,69}</string>
    <key>textureRect</key>
    <string>{{670,0},{512,69}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>headerXoSo.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{476,58}</string>
    <key>spriteSourceSize</key>
    <string>{476,58}</string>
    <key>textureRect</key>
    <string>{{670,149},{476,58}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>iconBackTron1.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{57,57}</string>
    <key>spriteSourceSize</key>
    <string>{57,57}</string>
    <key>textureRect</key>
    <string>{{1148,149},{57,57}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>iconInputnumber.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{34,32}</string>
    <key>spriteSourceSize</key>
    <string>{34,32}</string>
    <key>textureRect</key>
    <string>{{367,949},{34,32}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>inboxMienBacCopy2.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{365,442}</string>
    <key>spriteSourceSize</key>
    <string>{365,442}</string>
    <key>textureRect</key>
    <string>{{0,949},{365,442}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>inboxMienNamCopy.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{426,518}</string>
    <key>spriteSourceSize</key>
    <string>{426,519}</string>
    <key>textureRect</key>
    <string>{{407,716},{426,518}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>lineNext.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{614,13}</string>
    <key>spriteSourceSize</key>
    <string>{614,13}</string>
    <key>textureRect</key>
    <string>{{407,1362},{614,13}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>muitenChonDaiCopy.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{33,25}</string>
    <key>spriteSourceSize</key>
    <string>{33,25}</string>
    <key>textureRect</key>
    <string>{{367,983},{33,25}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>numberOffCopy4.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{201,60}</string>
    <key>spriteSourceSize</key>
    <string>{201,61}</string>
    <key>textureRect</key>
    <string>{{976,301},{201,60}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>numberOffCopy4Gray.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{201,61}</string>
    <key>spriteSourceSize</key>
    <string>{201,61}</string>
    <key>textureRect</key>
    <string>{{407,1236},{201,61}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>numberOn1.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{201,61}</string>
    <key>spriteSourceSize</key>
    <string>{201,61}</string>
    <key>textureRect</key>
    <string>{{407,1299},{201,61}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>thongBao.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{672,351}</string>
    <key>spriteSourceSize</key>
    <string>{672,351}</string>
    <key>textureRect</key>
    <string>{{407,363},{672,351}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
  </dict>
  <key>metadata</key>
  <dict>
   <key>format</key>
   <integer>3</integer>
   <key>size</key>
   <string>{1242,1393}</string>
   <key>textureFileName</key>
   <string>xoso.png</string>
  </dict>
 </dict>
</plist>
