[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false}, {"__type__": "cc.Node", "_name": "lbDate", "_objFlags": 0, "_parent": null, "_children": [], "_active": true, "_level": 1, "_components": [{"__id__": 2}, {"__id__": 3}], "_prefab": {"__id__": 4}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120.45, "height": 27.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 75, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_useOriginalSize": true, "_string": "22/02/2020", "_N$string": "22/02/2020", "_fontSize": 22, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "4b37fjQPb9J3ZBfn/wa2X3a", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "lbOpenDate": {"__id__": 2}, "dateValue": "", "strDateUI": "", "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "970ccfcf-3e79-4107-8b94-47797127ed6f"}, "fileId": "71g/1ZhKhMJb6t5j6Dpl6C", "sync": false}]