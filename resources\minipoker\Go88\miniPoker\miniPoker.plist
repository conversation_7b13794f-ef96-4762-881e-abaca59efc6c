<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
 <dict>
  <key>frames</key>
  <dict>
   <key>BG_check.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{34,34}</string>
    <key>spriteSourceSize</key>
    <string>{34,34}</string>
    <key>textureRect</key>
    <string>{{553,832},{34,34}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>borderJackpot.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{243,54}</string>
    <key>spriteSourceSize</key>
    <string>{243,54}</string>
    <key>textureRect</key>
    <string>{{0,706},{243,54}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>borderTable.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{753,502}</string>
    <key>spriteSourceSize</key>
    <string>{753,502}</string>
    <key>textureRect</key>
    <string>{{0,0},{753,502}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>btn100.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{96,50}</string>
    <key>spriteSourceSize</key>
    <string>{96,50}</string>
    <key>textureRect</key>
    <string>{{252,814},{96,50}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>btn100On.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{96,50}</string>
    <key>spriteSourceSize</key>
    <string>{96,50}</string>
    <key>textureRect</key>
    <string>{{154,814},{96,50}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>btn10K.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{96,50}</string>
    <key>spriteSourceSize</key>
    <string>{96,50}</string>
    <key>textureRect</key>
    <string>{{350,814},{96,50}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>btn10KOn.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{96,50}</string>
    <key>spriteSourceSize</key>
    <string>{96,50}</string>
    <key>textureRect</key>
    <string>{{651,780},{96,50}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>btn1K.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{96,50}</string>
    <key>spriteSourceSize</key>
    <string>{96,50}</string>
    <key>textureRect</key>
    <string>{{553,780},{96,50}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>btn1KOn.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{96,50}</string>
    <key>spriteSourceSize</key>
    <string>{96,50}</string>
    <key>textureRect</key>
    <string>{{628,706},{96,50}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>btnSieuToc.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{152,50}</string>
    <key>spriteSourceSize</key>
    <string>{152,50}</string>
    <key>textureRect</key>
    <string>{{308,762},{152,50}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>btnSieuTocOn.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{152,50}</string>
    <key>spriteSourceSize</key>
    <string>{152,50}</string>
    <key>textureRect</key>
    <string>{{0,762},{152,50}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>btnTuQuay.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{152,50}</string>
    <key>spriteSourceSize</key>
    <string>{152,50}</string>
    <key>textureRect</key>
    <string>{{0,814},{152,50}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>btnTuQuayOn.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{152,50}</string>
    <key>spriteSourceSize</key>
    <string>{152,50}</string>
    <key>textureRect</key>
    <string>{{245,706},{152,50}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>button_sieutoc_disable.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{152,50}</string>
    <key>spriteSourceSize</key>
    <string>{152,50}</string>
    <key>textureRect</key>
    <string>{{399,706},{152,50}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>button_tuquay_disable.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{152,50}</string>
    <key>spriteSourceSize</key>
    <string>{152,50}</string>
    <key>textureRect</key>
    <string>{{154,762},{152,50}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>daucheck.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{31,27}</string>
    <key>spriteSourceSize</key>
    <string>{31,27}</string>
    <key>textureRect</key>
    <string>{{462,836},{31,27}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>icBXH.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{73,72}</string>
    <key>spriteSourceSize</key>
    <string>{73,73}</string>
    <key>textureRect</key>
    <string>{{553,706},{73,72}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>icBxh.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{63,62}</string>
    <key>spriteSourceSize</key>
    <string>{63,63}</string>
    <key>textureRect</key>
    <string>{{694,504},{63,62}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>icClose.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{73,72}</string>
    <key>spriteSourceSize</key>
    <string>{73,73}</string>
    <key>textureRect</key>
    <string>{{462,762},{73,72}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>icHoidap.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{63,62}</string>
    <key>spriteSourceSize</key>
    <string>{63,63}</string>
    <key>textureRect</key>
    <string>{{694,568},{63,62}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>icI.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{63,62}</string>
    <key>spriteSourceSize</key>
    <string>{63,63}</string>
    <key>textureRect</key>
    <string>{{694,632},{63,62}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
   <key>pokerBoard.png</key>
   <dict>
    <key>aliases</key>
    <array/>
    <key>spriteOffset</key>
    <string>{0,0}</string>
    <key>spriteSize</key>
    <string>{692,200}</string>
    <key>spriteSourceSize</key>
    <string>{692,200}</string>
    <key>textureRect</key>
    <string>{{0,504},{692,200}}</string>
    <key>textureRotated</key>
    <false/>
   </dict>
  </dict>
  <key>metadata</key>
  <dict>
   <key>format</key>
   <integer>3</integer>
   <key>size</key>
   <string>{759,868}</string>
   <key>textureFileName</key>
   <string>miniPoker.png</string>
  </dict>
 </dict>
</plist>
