[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false}, {"__type__": "cc.Node", "_name": "chip", "_objFlags": 0, "_parent": null, "_children": [], "_active": true, "_level": 2, "_components": [{"__id__": 2}, {"__id__": 3}], "_prefab": {"__id__": 4}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-103, 40, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "0d704e1a-e46c-4913-9180-0d01147e1cad"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "8c38fbHisRGHJ9Ebpzn3dyy", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "spriteChip": {"__id__": 2}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "66a12290-f89c-4623-b774-889cd9c79db8"}, "fileId": "40BrJhSixAvb9k3jZtdTlc", "sync": false}]