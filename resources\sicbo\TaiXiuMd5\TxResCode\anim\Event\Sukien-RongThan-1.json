{"skeleton": {"hash": "QA+fmv0qoDYLrDIFohDWuXe4z78", "spine": "3.6.53", "width": 416.1, "height": 328.98, "images": ""}, "bones": [{"name": "root"}, {"name": "bone5", "parent": "root", "length": 145.77, "rotation": 151.15, "x": 88.69, "y": -102.07}, {"name": "bone6", "parent": "bone5", "length": 103.33, "rotation": -47.36, "x": 146.77, "y": 0.04}, {"name": "bone7", "parent": "bone6", "length": 94.53, "rotation": -104.34, "x": 83.36, "y": -30.62}, {"name": "bone8", "parent": "bone6", "length": 40.04, "rotation": 101.07, "x": 49.03, "y": -67.1}, {"name": "bone9", "parent": "bone8", "length": 39.23, "rotation": 1.99, "x": 40.81, "y": 0.62}, {"name": "bone10", "parent": "bone5", "length": 87.41, "rotation": 32.05, "x": 112.91, "y": 29.64}, {"name": "bone11", "parent": "bone6", "length": 81.97, "rotation": 27.04, "x": 86.97, "y": 31.69}, {"name": "bone12", "parent": "bone6", "length": 100.13, "rotation": 17.08, "x": 129.36, "y": -33.46}, {"name": "bone14", "parent": "bone7", "length": 55.74, "rotation": -44.46, "x": 56.47, "y": -28.2}, {"name": "bone17", "parent": "root", "length": 129.59, "rotation": -175.45, "x": 218.06, "y": 41.54}], "slots": [{"name": "Ron<PERSON><PERSON>/images/Rong/1", "bone": "bone10", "attachment": "Ron<PERSON><PERSON>/images/Rong/1"}, {"name": "<PERSON><PERSON><PERSON>/images/Rong/4", "bone": "bone11", "attachment": "<PERSON><PERSON><PERSON>/images/Rong/4"}, {"name": "<PERSON><PERSON><PERSON>/images/Rong/5", "bone": "bone5", "attachment": "<PERSON><PERSON><PERSON>/images/Rong/5"}, {"name": "<PERSON><PERSON><PERSON>/images/Rong/6", "bone": "bone12", "attachment": "<PERSON><PERSON><PERSON>/images/Rong/6"}, {"name": "<PERSON><PERSON><PERSON>/images/Rong/7", "bone": "bone8", "attachment": "<PERSON><PERSON><PERSON>/images/Rong/7"}, {"name": "<PERSON><PERSON><PERSON>/images/Rong/8", "bone": "bone7", "attachment": "<PERSON><PERSON><PERSON>/images/Rong/8"}, {"name": "<PERSON><PERSON><PERSON>/images/Rong/9", "bone": "bone17", "attachment": "<PERSON><PERSON><PERSON>/images/Rong/9"}], "skins": {"default": {"Rongthan/images/Rong/1": {"Rongthan/images/Rong/1": {"x": 68.14, "y": -20.13, "rotation": 176.8, "width": 148, "height": 148}}, "Rongthan/images/Rong/4": {"Rongthan/images/Rong/4": {"x": 37.84, "y": -2.03, "rotation": -130.84, "width": 71, "height": 86}}, "Rongthan/images/Rong/5": {"Rongthan/images/Rong/5": {"type": "mesh", "uvs": [0.7009, 0.08609, 0.59813, 0.37708, 0.60411, 0.47833, 0.72994, 0.58117, 1, 0.66604, 1, 1, 0.50207, 1, 0.14018, 0.662, 0, 0.32868, 0.20943, 0, 0.48644, 0], "triangles": [6, 7, 2, 6, 2, 3, 3, 4, 5, 6, 3, 5, 1, 10, 0, 9, 10, 1, 8, 9, 1, 7, 8, 1, 7, 1, 2], "vertices": [2, 2, 102.44, -90.73, 0.99917, 1, 149.43, -136.78, 0.00083, 2, 2, 39.67, -52.25, 0.78857, 1, 135.21, -64.54, 0.21143, 2, 2, 15.67, -47.7, 0.37968, 1, 122.3, -43.8, 0.62032, 2, 2, -14.95, -68.43, 0.01516, 1, 86.31, -35.33, 0.98484, 1, 1, 24.88, -45.82, 1, 1, 1, -13.96, 24.68, 1, 2, 2, -101.13, 3.88, 0.00647, 1, 81.12, 77.05, 0.99353, 2, 2, -3.21, 61.07, 0.99147, 1, 189.52, 43.77, 0.00853, 1, 2, 82.09, 71.6, 1, 1, 2, 148.13, 8.37, 1, 1, 2, 133.73, -50.28, 1], "hull": 11, "edges": [10, 8, 8, 6, 2, 0, 0, 20, 18, 20, 10, 12, 14, 12, 6, 12, 4, 14, 2, 4, 4, 6, 2, 16, 16, 18, 14, 16], "width": 218, "height": 241}}, "Rongthan/images/Rong/6": {"Rongthan/images/Rong/6": {"x": 44.15, "y": -10.76, "rotation": -120.88, "width": 92, "height": 104}}, "Rongthan/images/Rong/7": {"Rongthan/images/Rong/7": {"type": "mesh", "uvs": [0.31359, 0.10267, 0.50645, 0.07537, 0.70777, 0.04687, 1, 0.0761, 0.92345, 0.59961, 0.80655, 0.81123, 0.70228, 1, 0.42733, 1, 0, 0.53299, 0, 0], "triangles": [7, 8, 9, 4, 2, 3, 4, 1, 2, 5, 1, 4, 7, 9, 0, 5, 0, 1, 0, 6, 7, 5, 6, 0], "vertices": [60.01, -31.77, 40.14, -24.66, 19.39, -17.25, -8.65, -2, 14.32, 27.74, 32.11, 35.82, 47.98, 43.04, 75.17, 30.43, 103.69, -18.81, 88, -52.66], "hull": 10, "edges": [18, 0, 4, 6, 6, 8, 12, 14, 16, 18, 14, 16, 12, 0, 4, 8, 0, 2, 2, 4, 8, 10, 10, 12, 2, 10, 14, 18], "width": 109, "height": 70}}, "Rongthan/images/Rong/8": {"Rongthan/images/Rong/8": {"type": "mesh", "uvs": [1, 0.47632, 0.90651, 0.53595, 0.82953, 0.58505, 0.8119, 0.64431, 0.82299, 0.71032, 0.96034, 0.81523, 0.91453, 1, 0.7759, 1, 0.39647, 0.93542, 0.27653, 0.89051, 0.37902, 0.67542, 0.36376, 0.62342, 0.12171, 0.69197, 0.05676, 0.60367, 0, 0.52651, 0, 0, 1, 0], "triangles": [15, 16, 0, 14, 15, 0, 1, 14, 0, 1, 13, 14, 1, 11, 13, 2, 11, 1, 12, 13, 11, 3, 11, 2, 10, 11, 3, 4, 8, 10, 4, 10, 3, 9, 10, 8, 7, 8, 4, 7, 4, 5, 6, 7, 5], "vertices": [1, 3, 121.99, -2.44, 1, 2, 9, 24.5, 48, 0.25908, 3, 107.58, -11.1, 0.74092, 2, 9, 21.03, 34.6, 0.47242, 3, 95.71, -18.23, 0.52758, 1, 9, 25.09, 26.67, 1, 1, 9, 32.98, 21.21, 1, 1, 9, 58.64, 25.66, 1, 1, 9, 72.3, 1.96, 1, 1, 9, 57.11, -13.24, 1, 1, 9, 8.99, -48.29, 1, 1, 9, -8.69, -56.9, 1, 1, 9, -19.21, -23.92, 1, 2, 9, -26.14, -20.33, 0.49678, 3, 23.57, -24.41, 0.50322, 2, 9, -45.74, -53.79, 0.01582, 3, -13.85, -34.56, 0.98418, 2, 9, -61.78, -51.98, 0.00738, 3, -24.04, -22.03, 0.99262, 1, 3, -32.94, -11.08, 1, 1, 3, -33.65, 64.2, 1, 1, 3, 121.34, 65.67, 1], "hull": 17, "edges": [30, 32, 28, 30, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 0, 32, 8, 10, 12, 14, 10, 12, 4, 6, 20, 6, 6, 8, 22, 4, 0, 28, 0, 2, 2, 4, 24, 26, 26, 28, 2, 26], "width": 155, "height": 143}}, "Rongthan/images/Rong/9": {"Rongthan/images/Rong/9": {"x": 83.84, "y": 5.74, "rotation": 175.45, "width": 227, "height": 140}}}}, "animations": {"Attack": {"slots": {"Rongthan/images/Rong/1": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "color": "ffffffff", "curve": "stepped"}, {"time": 6, "color": "ffffffff", "curve": "stepped"}, {"time": 15.0667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 18.0667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Ron<PERSON><PERSON>/images/Rong/1"}, {"time": 3, "name": "Ron<PERSON><PERSON>/images/Rong/1"}]}, "Rongthan/images/Rong/4": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "color": "ffffffff", "curve": "stepped"}, {"time": 6, "color": "ffffffff", "curve": "stepped"}, {"time": 15.0667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 18.0667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "<PERSON><PERSON><PERSON>/images/Rong/4"}, {"time": 3, "name": "<PERSON><PERSON><PERSON>/images/Rong/4"}]}, "Rongthan/images/Rong/5": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "color": "ffffffff", "curve": "stepped"}, {"time": 6, "color": "ffffffff", "curve": "stepped"}, {"time": 15.0667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 18.0667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "<PERSON><PERSON><PERSON>/images/Rong/5"}, {"time": 3, "name": "<PERSON><PERSON><PERSON>/images/Rong/5"}]}, "Rongthan/images/Rong/6": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "color": "ffffffff", "curve": "stepped"}, {"time": 6, "color": "ffffffff", "curve": "stepped"}, {"time": 15.0667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 18.0667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "<PERSON><PERSON><PERSON>/images/Rong/6"}, {"time": 3, "name": "<PERSON><PERSON><PERSON>/images/Rong/6"}]}, "Rongthan/images/Rong/7": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "color": "ffffffff", "curve": "stepped"}, {"time": 6, "color": "ffffffff", "curve": "stepped"}, {"time": 15.0667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 18.0667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "<PERSON><PERSON><PERSON>/images/Rong/7"}, {"time": 3, "name": "<PERSON><PERSON><PERSON>/images/Rong/7"}]}, "Rongthan/images/Rong/8": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "color": "ffffffff", "curve": "stepped"}, {"time": 6, "color": "ffffffff", "curve": "stepped"}, {"time": 15.0667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 18.0667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "<PERSON><PERSON><PERSON>/images/Rong/8"}, {"time": 3, "name": "<PERSON><PERSON><PERSON>/images/Rong/8"}]}, "Rongthan/images/Rong/9": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 3, "color": "ffffffff", "curve": "stepped"}, {"time": 6, "color": "ffffffff"}, {"time": 6.3667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 15.0667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 18.0667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "<PERSON><PERSON><PERSON>/images/Rong/9"}, {"time": 3, "name": "<PERSON><PERSON><PERSON>/images/Rong/9"}]}}, "bones": {"bone5": {"rotate": [{"time": 0, "angle": 0, "curve": [0.208, 0.28, 0.75, 1]}, {"time": 0.5, "angle": -0.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1333, "angle": 0.86, "curve": [0.25, 0, 0.811, 0.76]}, {"time": 3, "angle": 0, "curve": [0.208, 0.28, 0.75, 1]}, {"time": 3.5, "angle": -0.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.1333, "angle": 0.86, "curve": [0.25, 0, 0.811, 0.76]}, {"time": 6, "angle": 0, "curve": [0.208, 0.28, 0.75, 1]}, {"time": 6.5, "angle": -0.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.1333, "angle": 0.86, "curve": [0.25, 0, 0.811, 0.76]}, {"time": 9, "angle": 0, "curve": "stepped"}, {"time": 9.0333, "angle": 0, "curve": [0.208, 0.28, 0.75, 1]}, {"time": 9.5333, "angle": -0.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.1667, "angle": 0.86, "curve": [0.25, 0, 0.811, 0.76]}, {"time": 12.0333, "angle": 0, "curve": "stepped"}, {"time": 12.0667, "angle": 0, "curve": [0.208, 0.28, 0.75, 1]}, {"time": 12.5667, "angle": -0.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 14.2, "angle": 0.86, "curve": [0.25, 0, 0.811, 0.76]}, {"time": 15.0667, "angle": 0, "curve": [0.208, 0.28, 0.75, 1]}, {"time": 15.5667, "angle": -0.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 17.2, "angle": 0.86, "curve": [0.25, 0, 0.811, 0.76]}, {"time": 18.0667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0, "y": -5.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.5, "x": 0, "y": -5.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.5, "x": 0, "y": -5.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.0333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.5333, "x": 0, "y": -5.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 12.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 13.5667, "x": 0, "y": -5.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 15.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 16.5667, "x": 0, "y": -5.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 18.0667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 15.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 18.0667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 18.0667, "x": 0, "y": 0}]}, "bone6": {"rotate": [{"time": 0, "angle": 1.07, "curve": [0.193, 0.24, 0.75, 1]}, {"time": 1.1, "angle": -1.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5333, "angle": 1.99, "curve": [0.25, 0, 0.814, 0.75]}, {"time": 3, "angle": 1.07, "curve": "stepped"}, {"time": 6, "angle": 1.07, "curve": [0.193, 0.24, 0.75, 1]}, {"time": 7.1, "angle": -1.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.5333, "angle": 1.99, "curve": [0.25, 0, 0.814, 0.75]}, {"time": 9, "angle": 1.07, "curve": "stepped"}, {"time": 9.0333, "angle": 1.07, "curve": [0.193, 0.24, 0.75, 1]}, {"time": 10.1333, "angle": -1.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.5667, "angle": 1.99, "curve": [0.25, 0, 0.814, 0.75]}, {"time": 12.0333, "angle": 1.07, "curve": "stepped"}, {"time": 12.0667, "angle": 1.07, "curve": [0.193, 0.24, 0.75, 1]}, {"time": 13.1667, "angle": -1.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 14.6, "angle": 1.99, "curve": [0.25, 0, 0.814, 0.75]}, {"time": 15.0667, "angle": 1.07, "curve": [0.193, 0.24, 0.75, 1]}, {"time": 16.1667, "angle": -1.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 17.6, "angle": 1.99, "curve": [0.25, 0, 0.814, 0.75]}, {"time": 18.0667, "angle": 1.07}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 18.0667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 15.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 18.0667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 18.0667, "x": 0, "y": 0}]}, "bone7": {"rotate": [{"time": 0, "angle": -1.96, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": -4, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": -1.96, "curve": [0.283, 0, 0.624, 0.39]}, {"time": 3.3333, "angle": 15.38, "curve": [0.323, 0.29, 0.757, 1]}, {"time": 4.6, "angle": -4, "curve": [0.25, 0, 0.75, 1]}, {"time": 6, "angle": -1.96, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6, "angle": -4, "curve": [0.25, 0, 0.75, 1]}, {"time": 9, "angle": -1.96, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.0333, "angle": -1.96, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.6333, "angle": -4, "curve": [0.25, 0, 0.75, 1]}, {"time": 12.0333, "angle": -1.96, "curve": [0.25, 0, 0.75, 1]}, {"time": 12.0667, "angle": -1.96, "curve": [0.25, 0, 0.75, 1]}, {"time": 13.6667, "angle": -4, "curve": [0.25, 0, 0.75, 1]}, {"time": 15.0667, "angle": -1.96, "curve": [0.25, 0, 0.75, 1]}, {"time": 16.6667, "angle": -4, "curve": [0.25, 0, 0.75, 1]}, {"time": 18.0667, "angle": -1.96}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 18.0667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 15.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 18.0667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 18.0667, "x": 0, "y": 0}]}, "bone10": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": -2.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.1, "angle": -2.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 6, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1, "angle": -2.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 9, "angle": 0, "curve": "stepped"}, {"time": 9.0333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.1333, "angle": -2.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 12.0333, "angle": 0, "curve": "stepped"}, {"time": 12.0667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 13.1667, "angle": -2.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 15.0667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 16.1667, "angle": -2.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 18.0667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 18.0667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 15.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 18.0667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 18.0667, "x": 0, "y": 0}]}, "bone12": {"rotate": [{"time": 0, "angle": 1.13, "curve": [0.216, 0.25, 0.75, 1]}, {"time": 0.8, "angle": -1.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.4333, "angle": 3.03, "curve": [0.25, 0, 0.814, 0.76]}, {"time": 3, "angle": 1.13, "curve": [0.216, 0.25, 0.75, 1]}, {"time": 3.8, "angle": -1.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.4333, "angle": 3.03, "curve": [0.25, 0, 0.814, 0.76]}, {"time": 6, "angle": 1.13, "curve": [0.216, 0.25, 0.75, 1]}, {"time": 6.8, "angle": -1.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.4333, "angle": 3.03, "curve": [0.25, 0, 0.814, 0.76]}, {"time": 9, "angle": 1.13, "curve": "stepped"}, {"time": 9.0333, "angle": 1.13, "curve": [0.216, 0.25, 0.75, 1]}, {"time": 9.8333, "angle": -1.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.4667, "angle": 3.03, "curve": [0.25, 0, 0.814, 0.76]}, {"time": 12.0333, "angle": 1.13, "curve": "stepped"}, {"time": 12.0667, "angle": 1.13, "curve": [0.216, 0.25, 0.75, 1]}, {"time": 12.8667, "angle": -1.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 14.5, "angle": 3.03, "curve": [0.25, 0, 0.814, 0.76]}, {"time": 15.0667, "angle": 1.13, "curve": [0.216, 0.25, 0.75, 1]}, {"time": 15.8667, "angle": -1.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 17.5, "angle": 3.03, "curve": [0.25, 0, 0.814, 0.76]}, {"time": 18.0667, "angle": 1.13}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 18.0667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 15.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 18.0667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 18.0667, "x": 0, "y": 0}]}, "bone11": {"rotate": [{"time": 0, "angle": 1.94, "curve": [0.201, 0.23, 0.75, 1]}, {"time": 1.1, "angle": -4.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.7667, "angle": 2.92, "curve": [0.25, 0, 0.837, 0.82]}, {"time": 3, "angle": 1.94, "curve": [0.201, 0.23, 0.75, 1]}, {"time": 4.1, "angle": -4.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.7667, "angle": 2.92, "curve": [0.25, 0, 0.837, 0.82]}, {"time": 6, "angle": 1.94, "curve": [0.201, 0.23, 0.75, 1]}, {"time": 7.1, "angle": -4.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.7667, "angle": 2.92, "curve": [0.25, 0, 0.837, 0.82]}, {"time": 9, "angle": 1.94, "curve": "stepped"}, {"time": 9.0333, "angle": 1.94, "curve": [0.201, 0.23, 0.75, 1]}, {"time": 10.1333, "angle": -4.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.8, "angle": 2.92, "curve": [0.25, 0, 0.837, 0.82]}, {"time": 12.0333, "angle": 1.94, "curve": "stepped"}, {"time": 12.0667, "angle": 1.94, "curve": [0.201, 0.23, 0.75, 1]}, {"time": 13.1667, "angle": -4.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 14.8333, "angle": 2.92, "curve": [0.25, 0, 0.837, 0.82]}, {"time": 15.0667, "angle": 1.94, "curve": [0.201, 0.23, 0.75, 1]}, {"time": 16.1667, "angle": -4.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 17.8333, "angle": 2.92, "curve": [0.25, 0, 0.837, 0.82]}, {"time": 18.0667, "angle": 1.94}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 18.0667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 15.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 18.0667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 18.0667, "x": 0, "y": 0}]}, "bone8": {"rotate": [{"time": 0, "angle": 0, "curve": [0.186, 0.23, 0.75, 1]}, {"time": 1.2, "angle": -4.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5333, "angle": 2.79, "curve": [0.25, 0, 0.818, 0.76]}, {"time": 3, "angle": 0, "curve": [0.186, 0.23, 0.75, 1]}, {"time": 4.2, "angle": -4.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.5333, "angle": 2.79, "curve": [0.25, 0, 0.818, 0.76]}, {"time": 6, "angle": 0, "curve": [0.186, 0.23, 0.75, 1]}, {"time": 7.2, "angle": -4.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.5333, "angle": 2.79, "curve": [0.25, 0, 0.818, 0.76]}, {"time": 9, "angle": 0, "curve": "stepped"}, {"time": 9.0333, "angle": 0, "curve": [0.186, 0.23, 0.75, 1]}, {"time": 10.2333, "angle": -4.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 11.5667, "angle": 2.79, "curve": [0.25, 0, 0.818, 0.76]}, {"time": 12.0333, "angle": 0, "curve": "stepped"}, {"time": 12.0667, "angle": 0, "curve": [0.186, 0.23, 0.75, 1]}, {"time": 13.2667, "angle": -4.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 14.6, "angle": 2.79, "curve": [0.25, 0, 0.818, 0.76]}, {"time": 15.0667, "angle": 0, "curve": [0.186, 0.23, 0.75, 1]}, {"time": 16.2667, "angle": -4.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 17.6, "angle": 2.79, "curve": [0.25, 0, 0.818, 0.76]}, {"time": 18.0667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 18.0667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 15.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 18.0667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 18.0667, "x": 0, "y": 0}]}, "bone14": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 11.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": 0, "curve": "stepped"}, {"time": 6, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.3333, "angle": 11.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 9, "angle": 0, "curve": "stepped"}, {"time": 9.0333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.3667, "angle": 11.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 12.0333, "angle": 0, "curve": "stepped"}, {"time": 12.0667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 13.4, "angle": 11.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 15.0667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 16.4, "angle": 11.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 18.0667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 3.89, "y": 0.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.3333, "x": 3.89, "y": 0.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.0333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.3667, "x": 3.89, "y": 0.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 12.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 13.4, "x": 3.89, "y": 0.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 15.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 16.4, "x": 3.89, "y": 0.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 18.0667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 12.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 15.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 18.0667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 12.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 15.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 18.0667, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0, "curve": "stepped"}, {"time": 6, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6, "x": 0, "y": 0}]}, "bone9": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}]}, "bone17": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}, {"time": 6, "angle": 354.79}], "translate": [{"time": 0, "x": -173.49, "y": -7, "curve": "stepped"}, {"time": 3, "x": -173.49, "y": -7}, {"time": 6, "x": 269.53, "y": 17.89}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}, {"time": 4.8333, "x": 1, "y": 1}]}}, "deform": {"default": {"Rongthan/images/Rong/5": {"Rongthan/images/Rong/5": [{"time": 0, "curve": "stepped"}, {"time": 3}]}, "Rongthan/images/Rong/7": {"Rongthan/images/Rong/7": [{"time": 0, "curve": "stepped"}, {"time": 3}]}, "Rongthan/images/Rong/8": {"Rongthan/images/Rong/8": [{"time": 0, "curve": "stepped"}, {"time": 3}]}}}}, "Idle": {"slots": {"Rongthan/images/Rong/1": {"color": [{"time": 0, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Ron<PERSON><PERSON>/images/Rong/1"}]}, "Rongthan/images/Rong/4": {"color": [{"time": 0, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "<PERSON><PERSON><PERSON>/images/Rong/4"}]}, "Rongthan/images/Rong/5": {"color": [{"time": 0, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "<PERSON><PERSON><PERSON>/images/Rong/5"}]}, "Rongthan/images/Rong/6": {"color": [{"time": 0, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "<PERSON><PERSON><PERSON>/images/Rong/6"}]}, "Rongthan/images/Rong/7": {"color": [{"time": 0, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "<PERSON><PERSON><PERSON>/images/Rong/7"}]}, "Rongthan/images/Rong/8": {"color": [{"time": 0, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "<PERSON><PERSON><PERSON>/images/Rong/8"}]}, "Rongthan/images/Rong/9": {"color": [{"time": 0, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "<PERSON><PERSON><PERSON>/images/Rong/9"}]}}, "bones": {"bone5": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone6": {"rotate": [{"time": 0, "angle": 1.07}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone7": {"rotate": [{"time": 0, "angle": -1.96}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone10": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone12": {"rotate": [{"time": 0, "angle": 1.13}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone11": {"rotate": [{"time": 0, "angle": 1.94}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone8": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone14": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone9": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}}, "deform": {"default": {"Rongthan/images/Rong/5": {"Rongthan/images/Rong/5": [{"time": 0}]}, "Rongthan/images/Rong/7": {"Rongthan/images/Rong/7": [{"time": 0}]}, "Rongthan/images/Rong/8": {"Rongthan/images/Rong/8": [{"time": 0}]}}}}}}