{"skeleton": {"hash": "+/L4kszqKtLs9qNJr4qZecKBwHs", "spine": "3.7.23-beta", "width": 790, "height": 166.04, "images": "./images/", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone3", "parent": "root", "x": 18.36, "y": 84.7}, {"name": "73", "parent": "bone3", "x": -60.14, "y": 0.18}, {"name": "bone", "parent": "root", "length": 100, "x": -382.34}, {"name": "bone2", "parent": "root", "length": 100, "x": 407.66, "scaleX": -1}, {"name": "than", "parent": "bone", "x": 258.16, "y": 75.98}, {"name": "<PERSON><PERSON><PERSON>", "parent": "than", "length": 20.63, "rotation": 33.18, "x": 2.77, "y": 3.16}, {"name": "canhsau2", "parent": "<PERSON><PERSON><PERSON>", "length": 18.15, "rotation": 6.63, "x": 20.63}, {"name": "canhsau3", "parent": "canhsau2", "length": 17.71, "rotation": 19.78, "x": 18.15}, {"name": "canhsau4", "parent": "canhsau3", "length": 23.91, "rotation": 29.62, "x": 17.71}, {"name": "than14", "parent": "bone2", "x": 258.16, "y": 75.98}, {"name": "canhsau5", "parent": "than14", "length": 20.63, "rotation": 33.18, "x": 2.77, "y": 3.16}, {"name": "canhsau6", "parent": "canhsau5", "length": 18.15, "rotation": 6.63, "x": 20.63}, {"name": "canhsau7", "parent": "canhsau6", "length": 17.71, "rotation": 19.78, "x": 18.15}, {"name": "canhsau8", "parent": "canhsau7", "length": 23.91, "rotation": 29.62, "x": 17.71}, {"name": "than5", "parent": "than", "length": 31.68, "rotation": -154.8, "x": -0.56}, {"name": "canhtruoc8", "parent": "than5", "length": 39.5, "rotation": -47.72, "x": 17.68, "y": -8.39}, {"name": "than18", "parent": "than14", "length": 31.68, "rotation": -154.8, "x": -0.56}, {"name": "canhtruoc9", "parent": "than18", "length": 39.5, "rotation": -47.72, "x": 20.57, "y": -2.25}, {"name": "canhtruoc13", "parent": "canhtruoc8", "length": 32.73, "rotation": 10.75, "x": 39.5}, {"name": "canhtruoc14", "parent": "canhtruoc13", "length": 32.64, "rotation": 0.76, "x": 32.73}, {"name": "canhtruoc15", "parent": "canhtruoc14", "length": 38.78, "rotation": 7.71, "x": 32.64}, {"name": "canhtruoc16", "parent": "canhtruoc15", "length": 33.36, "rotation": -5.92, "x": 38.78}, {"name": "canhtruoc17", "parent": "canhtruoc16", "length": 24.91, "rotation": -9.55, "x": 33.36}, {"name": "canhtruoc18", "parent": "canhtruoc9", "length": 32.73, "rotation": 10.75, "x": 39.5}, {"name": "canhtruoc19", "parent": "canhtruoc18", "length": 32.64, "rotation": 0.76, "x": 32.73}, {"name": "canhtruoc20", "parent": "canhtruoc19", "length": 38.78, "rotation": 7.71, "x": 32.64}, {"name": "canhtruoc21", "parent": "canhtruoc20", "length": 33.36, "rotation": -5.92, "x": 38.78}, {"name": "canhtruoc22", "parent": "canhtruoc21", "length": 24.91, "rotation": -9.55, "x": 33.36}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "root", "y": 39.51}, {"name": "than2", "parent": "than", "length": 19.96, "rotation": 99.73}, {"name": "than3", "parent": "than2", "length": 19.67, "rotation": -9.73, "x": 19.96}, {"name": "than4", "parent": "than3", "length": 18.4, "rotation": -82.98, "x": 19.67}, {"name": "than6", "parent": "than5", "length": 30.77, "rotation": -15.74, "x": 31.68}, {"name": "than7", "parent": "than6", "length": 20.05, "rotation": -3.83, "x": 30.77}, {"name": "than8", "parent": "than7", "length": 32.63, "rotation": 22.88, "x": 11.29, "y": 10.7}, {"name": "than9", "parent": "than8", "length": 36.6, "rotation": -23.52, "x": 33.17, "y": 0.92}, {"name": "than10", "parent": "than9", "length": 28.81, "rotation": -15.61, "x": 36.6}, {"name": "than11", "parent": "than10", "length": 31.42, "rotation": -11.9, "x": 28.81}, {"name": "than12", "parent": "than11", "length": 26.87, "rotation": 4.09, "x": 31.75, "y": 0.14}, {"name": "than13", "parent": "than12", "length": 31.47, "rotation": 35.44, "x": 26.87}, {"name": "than15", "parent": "than14", "length": 19.96, "rotation": 99.73}, {"name": "than16", "parent": "than15", "length": 19.67, "rotation": -9.73, "x": 19.96}, {"name": "than17", "parent": "than16", "length": 18.4, "rotation": -82.98, "x": 19.67}, {"name": "than19", "parent": "than18", "length": 30.77, "rotation": -15.74, "x": 31.68}, {"name": "than20", "parent": "than19", "length": 20.05, "rotation": -3.83, "x": 30.77}, {"name": "than21", "parent": "than20", "length": 32.63, "rotation": 22.88, "x": 11.29, "y": 10.7}, {"name": "than22", "parent": "than21", "length": 36.6, "rotation": -23.52, "x": 33.17, "y": 0.92}, {"name": "than23", "parent": "than22", "length": 28.81, "rotation": -15.61, "x": 36.6}, {"name": "than24", "parent": "than23", "length": 31.42, "rotation": -11.9, "x": 28.81}, {"name": "than25", "parent": "than24", "length": 26.87, "rotation": 4.09, "x": 31.75, "y": 0.14}, {"name": "than26", "parent": "than25", "length": 31.47, "rotation": 35.44, "x": 26.87}, {"name": "trieu3", "parent": "bone3", "rotation": -180, "x": 58.2, "y": 4.25, "scaleY": -1}], "slots": [{"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "canhsau2", "bone": "canhsau5", "attachment": "canhsau2"}, {"name": "than", "bone": "than", "attachment": "than"}, {"name": "than2", "bone": "than14", "attachment": "than2"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "canhtruoc8", "attachment": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "canhtruoc2", "bone": "canhtruoc9", "attachment": "canhtruoc2"}, {"name": "trieu2", "bone": "bone2", "attachment": "Trieu2"}, {"name": "trieu4", "bone": "trieu3"}, {"name": "trieu", "bone": "bone", "attachment": "trieu"}, {"name": "trieu3", "bone": "73"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "skins": {"default": {"canhsau": {"canhsau": {"type": "mesh", "uvs": [0, 0.89149, 0, 0.67244, 0.08068, 0.54238, 0.11388, 0.38151, 0.09175, 0.25487, 0.12495, 0.07689, 0.24668, 0, 0.37211, 0, 0.49753, 0, 0.65615, 0.05556, 0.81477, 0.11112, 0.95864, 0.2891, 0.97932, 0.42258, 1, 0.55607, 0.98301, 0.68271, 0.96602, 0.80934, 0.84428, 0.96679, 0.71517, 0.98339, 0.58606, 1, 0.43851, 1, 0.29095, 1, 0.16553, 0.92914, 0.72651, 0.58193, 0.36206, 0.25883, 0.28956, 0.67006, 0.63311, 0.28797, 0.50979, 0.75978, 0.43916, 0.48926, 0.72653, 0.82287, 0.79141, 0.38698, 0.61929, 0.50033, 0.25726, 0.46877, 0.10751, 0.74123, 0.4769, 0.16059, 0.30041, 0.84997, 0.86487, 0.59694, 0.46481, 0.36099, 0.39296, 0.64209, 0.82614, 0.73817, 0.67016, 0.7039, 0.55999, 0.61338], "triangles": [33, 7, 8, 33, 8, 9, 23, 6, 7, 23, 7, 33, 5, 6, 23, 4, 5, 23, 25, 33, 9, 25, 9, 10, 29, 25, 10, 36, 23, 33, 36, 33, 25, 3, 4, 23, 11, 29, 10, 29, 11, 12, 31, 3, 23, 31, 23, 36, 30, 25, 29, 35, 29, 12, 27, 31, 36, 30, 36, 25, 27, 36, 30, 22, 30, 29, 35, 12, 13, 22, 29, 35, 40, 27, 30, 40, 30, 22, 37, 27, 40, 14, 35, 13, 39, 40, 22, 38, 22, 35, 38, 35, 14, 39, 22, 38, 15, 38, 14, 16, 38, 15, 2, 3, 31, 37, 31, 27, 24, 31, 37, 2, 31, 24, 26, 37, 40, 26, 40, 39, 28, 39, 38, 34, 24, 37, 34, 37, 26, 28, 38, 16, 28, 26, 39, 17, 28, 16, 28, 18, 26, 19, 34, 26, 18, 19, 26, 28, 17, 18, 32, 2, 24, 1, 2, 32, 32, 24, 34, 0, 1, 32, 21, 32, 34, 0, 32, 21, 20, 21, 34, 20, 34, 19], "vertices": [2, 6, -12.42, -8.15, 0.99955, 7, -33.76, -4.28, 0.00045, 3, 6, -0.79, 9.64, 0.99692, 7, -20.16, 12.05, 0.00291, 9, -31.18, 45.68, 0.00018, 4, 6, 12.19, 16.22, 0.70051, 7, -6.5, 17.09, 0.2392, 8, -17.42, 24.42, 0.02545, 9, -18.47, 38.59, 0.03484, 4, 6, 23.23, 27.65, 0.15378, 7, 5.78, 27.16, 0.39596, 8, -2.45, 29.75, 0.16487, 9, -2.82, 35.82, 0.28539, 4, 6, 28.29, 39.02, 0.0337, 7, 12.12, 37.87, 0.24373, 8, 7.14, 37.68, 0.1574, 9, 9.43, 37.98, 0.56517, 4, 6, 40.24, 51.83, 0.00129, 7, 25.46, 49.22, 0.11504, 8, 23.54, 43.84, 0.0784, 9, 26.74, 35.23, 0.80527, 3, 7, 38.65, 47.94, 0.06428, 8, 35.52, 38.17, 0.03938, 9, 34.35, 24.38, 0.89634, 3, 7, 47.33, 40.71, 0.02664, 8, 41.23, 28.44, 0.01286, 9, 34.5, 13.1, 0.9605, 3, 7, 56, 33.49, 0.00274, 8, 46.95, 18.7, 0.00011, 9, 34.66, 1.81, 0.99715, 2, 8, 49.53, 3.66, 0, 9, 29.47, -12.54, 1, 2, 8, 52.1, -11.38, 0.03542, 9, 24.28, -26.89, 0.96458, 3, 7, 69.93, -14.62, 0.02547, 8, 43.77, -31.28, 0.2415, 9, 7.2, -40.08, 0.73304, 3, 7, 63.07, -25.76, 0.08701, 8, 33.55, -39.44, 0.39254, 9, -5.72, -42.12, 0.52045, 3, 7, 56.21, -36.9, 0.19015, 8, 23.32, -47.6, 0.4957, 9, -18.64, -44.16, 0.31415, 4, 6, 72.71, -39.61, 0.00161, 7, 47.17, -45.36, 0.30726, 8, 11.95, -52.5, 0.51731, 9, -30.95, -42.8, 0.17381, 4, 6, 64.71, -49.06, 0.01112, 7, 38.13, -53.82, 0.40965, 8, 0.59, -57.4, 0.49086, 9, -43.25, -41.44, 0.08837, 4, 6, 47.19, -55.84, 0.04753, 7, 19.94, -58.53, 0.51278, 8, -18.13, -55.68, 0.41183, 9, -58.68, -30.7, 0.02786, 4, 6, 36.58, -50.83, 0.09738, 7, 9.98, -52.33, 0.55062, 8, -25.4, -46.48, 0.34049, 9, -60.45, -19.1, 0.01151, 4, 6, 25.97, -45.82, 0.20581, 7, 0.02, -46.13, 0.56351, 8, -32.67, -37.27, 0.2294, 9, -62.22, -7.5, 0.00129, 3, 6, 14.86, -38.55, 0.41498, 7, -10.18, -37.63, 0.48121, 8, -39.4, -25.82, 0.10381, 3, 6, 3.74, -31.29, 0.66451, 7, -20.38, -29.13, 0.30683, 8, -46.12, -14.37, 0.02866, 3, 6, -1.95, -19.35, 0.89233, 7, -24.65, -16.62, 0.10524, 8, -45.9, -1.15, 0.00243, 4, 6, 58.74, -18.8, 0.00185, 7, 35.69, -23.07, 0.24654, 8, 8.7, -27.65, 0.57375, 9, -21.5, -19.58, 0.17787, 4, 6, 48.44, 25.39, 0.00264, 7, 30.56, 22.01, 0.08616, 8, 19.12, 16.51, 0.18372, 9, 9.39, 13.65, 0.72747, 2, 6, 21.15, -4.43, 0.39223, 7, 0.01, -4.46, 0.60777, 3, 7, 47.49, 4.22, 0.00027, 8, 29.03, -5.96, 0.06677, 9, 6.9, -10.78, 0.93296, 4, 6, 32.98, -22.56, 0.14664, 7, 9.66, -23.84, 0.64105, 8, -16.05, -19.56, 0.20952, 9, -39.02, -0.32, 0.00279, 1, 8, 3.36, -0.79, 1, 4, 6, 45.95, -38.36, 0.05531, 7, 20.73, -41.02, 0.50849, 8, -11.46, -39.48, 0.40323, 9, -44.86, -19.91, 0.03297, 3, 7, 52.29, -12.28, 0.04401, 8, 27.96, -23.11, 0.34635, 9, -2.51, -25.16, 0.60963, 3, 7, 33.35, -10.81, 0.11708, 8, 10.64, -15.32, 0.72248, 9, -13.71, -9.82, 0.16044, 4, 6, 29.4, 13.5, 0.11531, 7, 10.28, 12.4, 0.61298, 8, -3.21, 14.33, 0.17511, 9, -11.11, 22.8, 0.0966, 2, 6, 3.66, -1.24, 0.99975, 7, -17, 0.72, 0.00025, 3, 7, 44.6, 22.71, 0.00488, 8, 32.57, 12.42, 0.00563, 9, 19.06, 3.45, 0.98949, 3, 6, 12.42, -19.57, 0.68054, 7, -10.41, -18.49, 0.29978, 8, -33.14, -7.73, 0.01968, 4, 6, 68.37, -26.83, 0.00047, 7, 44.33, -32.16, 0.2375, 8, 13.75, -39.12, 0.52331, 9, -22.78, -32.05, 0.23872, 3, 7, 31.32, 8.47, 0.01133, 8, 15.26, 3.52, 0.65806, 9, -0.39, 4.27, 0.33062, 3, 6, 30.42, -7.25, 0.04089, 7, 8.89, -8.33, 0.91859, 8, -11.53, -4.71, 0.04052, 4, 6, 57.95, -36.39, 0.01349, 7, 32.88, -40.45, 0.39745, 8, 0.17, -43.05, 0.4934, 9, -36.52, -28.76, 0.09566, 4, 6, 48.02, -25.92, 0.0304, 7, 24.22, -28.91, 0.45884, 8, -4.07, -29.26, 0.45809, 9, -33.4, -14.67, 0.05268, 4, 6, 44.53, -13.15, 0.01101, 7, 22.23, -15.82, 0.45305, 8, -1.52, -16.26, 0.50929, 9, -24.75, -4.64, 0.02664], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 0], "width": 90, "height": 97}}, "canhsau2": {"canhsau2": {"type": "mesh", "uvs": [1e-05, 0.91235, 0, 0.67244, 0.08068, 0.54238, 0.11388, 0.38151, 0.09175, 0.25487, 0.12495, 0.07689, 0.24668, 0, 0.37211, 0, 0.49753, 0, 0.65615, 0.05556, 0.81477, 0.11112, 0.95864, 0.2891, 0.97932, 0.42258, 1, 0.55607, 0.98301, 0.68271, 0.96602, 0.80934, 0.84428, 0.96679, 0.71517, 0.98339, 0.58606, 1, 0.43851, 1, 0.21218, 1, 0, 1, 0.72651, 0.58193, 0.36206, 0.25883, 0.28956, 0.67006, 0.63311, 0.28797, 0.50979, 0.75978, 0.43916, 0.48926, 0.72653, 0.82287, 0.79141, 0.38698, 0.61929, 0.50033, 0.25726, 0.46877, 0.10751, 0.74123, 0.4769, 0.16059, 0.30041, 0.84997, 0.86487, 0.59694, 0.46481, 0.36099, 0.39296, 0.64209, 0.82614, 0.73817, 0.67016, 0.7039, 0.55999, 0.61338], "triangles": [33, 7, 8, 33, 8, 9, 23, 6, 7, 23, 7, 33, 5, 6, 23, 4, 5, 23, 25, 33, 9, 25, 9, 10, 29, 25, 10, 36, 23, 33, 36, 33, 25, 3, 4, 23, 11, 29, 10, 29, 11, 12, 31, 3, 23, 31, 23, 36, 30, 25, 29, 35, 29, 12, 27, 31, 36, 30, 36, 25, 27, 36, 30, 22, 30, 29, 35, 12, 13, 22, 29, 35, 40, 27, 30, 40, 30, 22, 37, 27, 40, 14, 35, 13, 39, 40, 22, 38, 22, 35, 38, 35, 14, 39, 22, 38, 15, 38, 14, 16, 38, 15, 2, 3, 31, 37, 31, 27, 24, 31, 37, 2, 31, 24, 26, 37, 40, 26, 40, 39, 28, 39, 38, 34, 24, 37, 34, 37, 26, 28, 38, 16, 28, 26, 39, 17, 28, 16, 28, 18, 26, 19, 34, 26, 18, 19, 26, 28, 17, 18, 32, 2, 24, 1, 2, 32, 32, 24, 34, 0, 1, 32, 20, 32, 34, 0, 32, 20, 21, 0, 20, 20, 34, 19], "vertices": [2, 11, -3.64, 5.27, 0.99955, 12, -23.5, 8.04, 0.00045, 3, 11, 9.09, 24.75, 0.99692, 12, -8.6, 25.92, 0.00291, 14, -13.13, 45.93, 0.00018, 4, 11, 22.08, 31.34, 0.70051, 12, 5.06, 30.96, 0.2392, 13, -1.84, 33.56, 0.02545, 14, -0.41, 38.84, 0.03484, 4, 11, 33.12, 42.76, 0.15378, 12, 17.34, 41.03, 0.39596, 13, 13.13, 38.89, 0.16487, 14, 15.23, 36.07, 0.28539, 4, 11, 38.17, 54.13, 0.0337, 12, 23.67, 51.75, 0.24373, 13, 22.71, 46.82, 0.1574, 14, 27.49, 38.23, 0.56517, 4, 11, 50.12, 66.95, 0.00129, 12, 37.02, 63.1, 0.11504, 13, 39.11, 52.98, 0.0784, 14, 44.79, 35.49, 0.80527, 3, 12, 50.21, 61.81, 0.06428, 13, 51.09, 47.31, 0.03938, 14, 52.4, 24.63, 0.89634, 3, 12, 58.88, 54.59, 0.02664, 13, 56.8, 37.58, 0.01286, 14, 52.56, 13.35, 0.9605, 3, 12, 67.56, 47.36, 0.00274, 13, 62.52, 27.84, 0.00011, 14, 52.72, 2.06, 0.99715, 2, 13, 65.1, 12.8, 0, 14, 47.52, -12.29, 1, 2, 13, 67.68, -2.24, 0.03542, 14, 42.33, -26.64, 0.96458, 3, 12, 81.49, -0.75, 0.02547, 13, 59.34, -22.14, 0.2415, 14, 25.25, -39.83, 0.73304, 3, 12, 74.63, -11.89, 0.08701, 13, 49.12, -30.3, 0.39254, 14, 12.33, -41.87, 0.52045, 3, 12, 67.77, -23.03, 0.19015, 13, 38.89, -38.46, 0.4957, 14, -0.59, -43.91, 0.31415, 4, 11, 82.6, -24.5, 0.00161, 12, 58.73, -31.49, 0.30726, 13, 27.52, -43.36, 0.51731, 14, -12.89, -42.55, 0.17381, 4, 11, 74.59, -33.94, 0.01112, 12, 49.69, -39.94, 0.40965, 13, 16.16, -48.26, 0.49086, 14, -25.2, -41.19, 0.08837, 4, 11, 57.07, -40.73, 0.04753, 12, 31.5, -44.66, 0.51278, 13, -2.56, -46.54, 0.41183, 14, -40.62, -30.45, 0.02786, 4, 11, 46.46, -35.72, 0.09738, 12, 21.54, -38.46, 0.55062, 13, -9.83, -37.34, 0.34049, 14, -42.39, -18.85, 0.01151, 4, 11, 35.85, -30.71, 0.20581, 12, 11.58, -32.26, 0.56351, 13, -17.1, -28.13, 0.2294, 14, -44.17, -7.25, 0.00129, 3, 11, 24.74, -23.44, 0.41498, 12, 1.38, -23.76, 0.48121, 13, -23.82, -16.68, 0.10381, 3, 11, 7.69, -12.29, 0.66451, 12, -14.27, -10.72, 0.30683, 13, -34.14, 0.89, 0.02866, 3, 11, -8.29, -1.84, 0.89233, 12, -28.94, 1.51, 0.10524, 13, -43.8, 17.36, 0.00243, 4, 11, 68.62, -3.68, 0.00185, 12, 47.25, -9.2, 0.24654, 13, 24.27, -18.51, 0.57375, 14, -3.44, -19.33, 0.17787, 4, 11, 58.32, 40.5, 0.00264, 12, 42.12, 35.88, 0.08616, 13, 34.7, 25.65, 0.18372, 14, 27.44, 13.9, 0.72747, 2, 11, 31.03, 10.68, 0.39223, 12, 11.57, 9.41, 0.60777, 3, 12, 59.05, 18.09, 0.00027, 13, 44.61, 3.18, 0.06677, 14, 24.95, -10.53, 0.93296, 4, 11, 42.86, -7.45, 0.14664, 12, 21.22, -9.97, 0.64105, 13, -0.48, -10.42, 0.20952, 14, -20.96, -0.07, 0.00279, 1, 13, 18.93, 8.35, 1, 4, 11, 55.84, -23.25, 0.05531, 12, 32.29, -27.15, 0.50849, 13, 4.11, -30.34, 0.40323, 14, -26.81, -19.66, 0.03297, 3, 12, 63.84, 1.59, 0.04401, 13, 43.53, -13.97, 0.34635, 14, 15.55, -24.91, 0.60963, 3, 12, 44.91, 3.06, 0.11708, 13, 26.21, -6.18, 0.72248, 14, 4.34, -9.57, 0.16044, 4, 11, 39.28, 28.61, 0.11531, 12, 21.83, 26.27, 0.61298, 13, 12.36, 23.47, 0.17511, 14, 6.95, 23.05, 0.0966, 2, 11, 13.54, 13.87, 0.99975, 12, -5.44, 14.6, 0.00025, 3, 12, 56.16, 36.58, 0.00488, 13, 48.15, 21.56, 0.00563, 14, 37.11, 3.7, 0.98949, 3, 11, 22.3, -4.46, 0.68054, 12, 1.15, -4.62, 0.29978, 13, -17.57, 1.41, 0.01968, 4, 11, 78.25, -11.72, 0.00047, 12, 55.89, -18.29, 0.2375, 13, 29.32, -29.98, 0.52331, 14, -4.72, -31.8, 0.23872, 3, 12, 42.88, 22.34, 0.01133, 13, 30.83, 12.66, 0.65806, 14, 17.66, 4.52, 0.33062, 3, 11, 40.3, 7.86, 0.04089, 12, 20.45, 5.54, 0.91859, 13, 4.04, 4.43, 0.04052, 4, 11, 67.83, -21.28, 0.01349, 12, 44.44, -26.58, 0.39745, 13, 15.74, -33.91, 0.4934, 14, -18.47, -28.51, 0.09566, 4, 11, 57.9, -10.81, 0.0304, 12, 35.78, -15.04, 0.45884, 13, 11.5, -20.12, 0.45809, 14, -15.34, -14.42, 0.05268, 4, 11, 54.41, 1.96, 0.01101, 12, 33.79, -1.95, 0.45305, 13, 14.05, -7.12, 0.50929, 14, -6.7, -4.39, 0.02664], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 0], "width": 62, "height": 96}}, "canhtruoc": {"canhtruoc": {"type": "mesh", "uvs": [0.99409, 0.73969, 0.95414, 0.69056, 0.91694, 0.56609, 0.90179, 0.458, 0.96241, 0.46127, 0.95552, 0.32043, 0.91143, 0.22871, 0.84944, 0.17631, 0.81913, 0.12062, 0.81637, 0.01253, 0.75062, 0.02611, 0.65861, 0.01396, 0.54105, 0.05852, 0.45245, 0.09903, 0.30534, 0.17471, 0.16941, 0.13481, 0.0704, 0.22259, 0.02173, 0.45, 0.03348, 0.64152, 0.13077, 0.73316, 0.21136, 0.80909, 0.35261, 0.89829, 0.42616, 0.94474, 0.52453, 0.97316, 0.61746, 1, 0.71312, 1, 0.85408, 1, 0.91113, 1, 0.96315, 1, 1, 0.87691, 0.89435, 0.29839, 0.82387, 0.50586, 0.87086, 0.70934, 0.7601, 0.30637, 0.59397, 0.32632, 0.47315, 0.39415, 0.33219, 0.46996, 0.2164, 0.4899, 0.40938, 0.7253, 0.54866, 0.7253, 0.6913, 0.71732, 0.77185, 0.70136, 0.71312, 0.56172, 0.5688, 0.56571, 0.33386, 0.60561, 0.23485, 0.65748], "triangles": [19, 18, 45, 18, 17, 37, 17, 16, 37, 37, 16, 14, 14, 16, 15, 38, 20, 45, 45, 44, 38, 43, 44, 36, 20, 19, 45, 18, 37, 45, 45, 37, 44, 37, 36, 44, 37, 14, 36, 36, 14, 35, 21, 20, 38, 21, 38, 22, 22, 38, 39, 43, 38, 44, 23, 22, 39, 14, 13, 35, 35, 13, 34, 23, 39, 24, 39, 38, 43, 39, 43, 40, 24, 39, 40, 43, 36, 35, 42, 43, 34, 43, 35, 34, 13, 12, 34, 34, 12, 11, 8, 34, 11, 24, 40, 25, 40, 43, 42, 26, 25, 41, 25, 40, 41, 40, 42, 41, 41, 42, 31, 42, 33, 31, 42, 34, 33, 33, 34, 8, 33, 8, 7, 10, 8, 11, 10, 9, 8, 31, 33, 30, 33, 7, 30, 32, 26, 41, 31, 3, 2, 3, 31, 30, 3, 5, 4, 3, 30, 5, 30, 6, 5, 30, 7, 6, 2, 32, 31, 32, 41, 31, 1, 32, 2, 28, 27, 29, 27, 26, 29, 29, 26, 32, 29, 32, 0, 32, 1, 0], "vertices": [1, 16, -5.46, -9.27, 1, 1, 16, 4.98, -10.15, 1, 3, 16, 17.66, -18.1, 0.9586, 19, -24.84, -13.7, 0.04109, 20, -57.74, -12.93, 0.00031, 3, 16, 24.98, -26.53, 0.7635, 19, -19.22, -23.35, 0.22825, 20, -52.26, -22.66, 0.00825, 3, 16, 11.81, -31.64, 0.74377, 19, -33.11, -25.92, 0.25571, 20, -66.18, -25.04, 0.00052, 3, 16, 18.57, -43.78, 0.70693, 19, -28.73, -39.11, 0.2847, 20, -61.97, -38.28, 0.00837, 3, 16, 31.51, -48.15, 0.60492, 19, -16.84, -45.81, 0.3572, 20, -50.17, -45.14, 0.03787, 4, 16, 46.82, -47.36, 0.37538, 19, -1.65, -47.89, 0.48877, 20, -35.01, -47.43, 0.13584, 21, -73.41, -37.92, 1e-05, 4, 16, 55.43, -49.69, 0.22299, 19, 6.38, -51.79, 0.53657, 20, -27.04, -51.44, 0.23888, 21, -66.04, -42.96, 0.00157, 4, 16, 60.08, -59.23, 0.16555, 19, 9.17, -62.03, 0.54128, 20, -24.39, -61.71, 0.29019, 21, -64.8, -53.5, 0.00299, 4, 16, 73.72, -52.14, 0.10854, 19, 23.89, -57.61, 0.50315, 20, -9.6, -57.48, 0.36983, 21, -49.58, -51.29, 0.01848, 4, 16, 93.98, -45.03, 0.02252, 19, 45.12, -54.4, 0.31749, 20, 11.67, -54.56, 0.54874, 21, -28.11, -51.25, 0.11125, 5, 16, 117.61, -30.5, 1e-05, 19, 71.05, -44.54, 0.074, 20, 37.72, -45.04, 0.4639, 21, -1.01, -45.32, 0.44877, 22, -34.9, -49.18, 0.01332, 4, 19, 90.45, -36.44, 0.00687, 20, 57.23, -37.21, 0.16669, 21, 19.37, -40.17, 0.70633, 22, -15.16, -41.96, 0.1201, 4, 20, 89.46, -23.38, 0.00024, 21, 53.17, -30.8, 0.19541, 22, 17.49, -29.15, 0.69743, 23, -10.81, -31.38, 0.10692, 3, 21, 85.01, -32.89, 4e-05, 22, 49.38, -27.94, 0.1512, 23, 20.43, -24.9, 0.84876, 2, 22, 70.77, -15.76, 0.00046, 23, 39.51, -9.33, 0.99954, 1, 23, 43.08, 15.42, 1, 2, 22, 72.69, 26.15, 0.02122, 23, 34.45, 32.31, 0.97878, 3, 21, 90.64, 26.17, 0.00042, 22, 48.88, 31.38, 0.29806, 23, 10.1, 33.52, 0.70152, 3, 21, 71.46, 32.52, 0.05076, 22, 29.15, 35.72, 0.72855, 23, -10.07, 34.53, 0.22069, 3, 20, 65.11, 44.12, 0.04174, 21, 38.1, 39.36, 0.57412, 22, -4.73, 39.08, 0.38414, 4, 19, 79.54, 45.95, 0.00573, 20, 47.42, 45.32, 0.17553, 21, 20.73, 42.92, 0.70376, 22, -22.38, 40.83, 0.11497, 5, 16, 86.84, 53.77, 1e-05, 19, 56.54, 44, 0.09056, 20, 24.39, 43.68, 0.47259, 21, -2.31, 44.39, 0.43057, 22, -45.45, 39.91, 0.00627, 4, 16, 65.83, 47.91, 0.02358, 19, 34.8, 42.16, 0.36062, 20, 2.64, 42.13, 0.48758, 21, -24.08, 45.77, 0.12822, 4, 16, 45.24, 39.37, 0.21942, 19, 12.98, 37.61, 0.59143, 20, -19.24, 37.87, 0.17494, 21, -46.33, 44.49, 0.0142, 3, 16, 14.9, 26.79, 0.90203, 19, -19.17, 30.91, 0.0971, 20, -51.48, 31.6, 0.00088, 2, 16, 2.62, 21.7, 0.98995, 19, -32.19, 28.2, 0.01005, 1, 16, -8.58, 17.06, 1, 1, 16, -11.89, 2.63, 1, 3, 16, 32.57, -40.31, 0.60808, 19, -14.33, -38.31, 0.35512, 20, -47.57, -37.68, 0.0368, 3, 16, 39.95, -15.24, 0.54529, 19, -2.4, -15.06, 0.43705, 20, -35.33, -14.59, 0.01766, 1, 16, 22.2, -1.02, 1, 4, 16, 61.16, -27.61, 0.14682, 19, 16.13, -31.17, 0.6153, 20, -17.01, -30.94, 0.23464, 21, -53.36, -24, 0.00324, 5, 16, 96.17, -10.98, 0.00015, 19, 53.63, -21.36, 0.08895, 20, 20.61, -21.64, 0.74143, 21, -14.83, -19.83, 0.1694, 22, -51.27, -25.25, 7e-05, 4, 19, 79.83, -9.11, 0.00031, 20, 46.98, -9.74, 0.0637, 21, 12.9, -11.57, 0.91937, 22, -24.55, -14.18, 0.01661, 2, 21, 45.26, -2.27, 0.00657, 22, 6.68, -1.59, 0.99343, 2, 22, 33, 4.66, 0.69309, 23, -1.13, 4.53, 0.30691, 4, 19, 87.76, 25.69, 4e-05, 20, 55.37, 24.95, 0.05966, 21, 25.87, 21.68, 0.80101, 22, -15.08, 20.23, 0.1393, 3, 19, 55.99, 19.07, 0.04081, 20, 23.51, 18.76, 0.66024, 21, -6.53, 19.82, 0.29895, 4, 16, 60.55, 15.73, 0.02339, 19, 23.61, 11.53, 0.81417, 20, -8.96, 11.65, 0.1577, 21, -39.66, 17.13, 0.00474, 3, 16, 43.81, 7.1, 0.16167, 19, 5.55, 6.17, 0.83629, 20, -27.09, 6.53, 0.00204, 2, 19, 21.74, -4.44, 0.97369, 20, -11.04, -4.29, 0.02631, 2, 20, 21.89, 2.51, 0.98961, 21, -10.32, 3.93, 0.01039, 3, 20, 74.88, 16.8, 0.00059, 21, 44.11, 10.98, 0.27456, 22, 4.17, 11.47, 0.72485, 3, 21, 66.85, 17.38, 0.03162, 22, 26.13, 20.18, 0.81496, 23, -10.48, 18.7, 0.15341], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 56, 58, 0, 58, 60, 62, 62, 64, 64, 58, 66, 68, 68, 70, 70, 72, 72, 74, 44, 46, 46, 48, 40, 42, 42, 44, 36, 38, 38, 40, 48, 50, 50, 52, 52, 54, 54, 56], "width": 233, "height": 98}}, "canhtruoc2": {"canhtruoc2": {"type": "mesh", "uvs": [0.99409, 0.73969, 0.95414, 0.69056, 0.95736, 0.5774, 0.92081, 0.458, 0.96241, 0.46127, 0.95552, 0.32043, 0.91143, 0.22871, 0.84944, 0.17631, 0.81913, 0.12062, 0.81637, 0.01253, 0.75062, 0.02611, 0.65861, 0.01396, 0.54105, 0.05852, 0.43956, 0.08152, 0.28876, 0.06965, 0.11785, 0, 0, 0.15255, 0.02173, 0.45, 0.03348, 0.64152, 0.13077, 0.73316, 0.21136, 0.80909, 0.35261, 0.89829, 0.42616, 0.94474, 0.52453, 0.97316, 0.61746, 1, 0.71312, 1, 0.85408, 1, 0.91113, 1, 0.96315, 1, 1, 0.87691, 0.89435, 0.29839, 0.82387, 0.50586, 0.87086, 0.70934, 0.7601, 0.30637, 0.59397, 0.32632, 0.47315, 0.39415, 0.33219, 0.46996, 0.2164, 0.4899, 0.40938, 0.7253, 0.54866, 0.7253, 0.6913, 0.71732, 0.77185, 0.70136, 0.71312, 0.56172, 0.5688, 0.56571, 0.33386, 0.60561, 0.23485, 0.65748], "triangles": [17, 16, 37, 14, 16, 15, 14, 37, 16, 18, 17, 37, 19, 18, 45, 35, 36, 14, 14, 36, 37, 43, 44, 36, 37, 36, 44, 45, 37, 44, 18, 37, 45, 20, 19, 45, 45, 44, 38, 38, 20, 45, 35, 13, 34, 35, 14, 13, 43, 38, 44, 21, 20, 38, 22, 38, 39, 21, 38, 22, 23, 22, 39, 34, 12, 11, 13, 12, 34, 8, 34, 11, 43, 35, 34, 42, 43, 34, 43, 36, 35, 39, 43, 40, 39, 38, 43, 24, 39, 40, 23, 39, 24, 10, 9, 8, 10, 8, 11, 33, 8, 7, 33, 7, 30, 33, 34, 8, 30, 31, 33, 42, 34, 33, 42, 33, 31, 41, 42, 31, 40, 43, 42, 40, 42, 41, 25, 40, 41, 24, 40, 25, 26, 25, 41, 30, 7, 6, 30, 6, 5, 3, 30, 5, 3, 5, 4, 30, 3, 31, 31, 3, 2, 31, 32, 41, 31, 2, 32, 1, 32, 2, 32, 1, 0, 29, 32, 0, 32, 26, 41, 29, 26, 32, 27, 26, 29, 28, 27, 29], "vertices": [1, 18, 3.8, -23.9, 1, 1, 18, 14.26, -23.25, 1, 3, 18, 19.31, -33.15, 0.9586, 24, -26.03, -28.8, 0.04109, 25, -59.13, -28.02, 0.00031, 3, 18, 32.62, -38.82, 0.7635, 24, -14, -36.86, 0.22825, 25, -47.22, -36.23, 0.00825, 3, 18, 24.14, -43.52, 0.74377, 24, -23.21, -39.89, 0.25571, 25, -56.47, -39.14, 0.00052, 3, 18, 32.6, -54.54, 0.70693, 24, -16.95, -52.3, 0.2847, 25, -50.38, -51.63, 0.00837, 3, 18, 46.03, -56.99, 0.60492, 24, -4.22, -57.2, 0.3572, 25, -37.7, -56.7, 0.03787, 4, 18, 61.07, -53.98, 0.37538, 24, 11.11, -57.05, 0.48877, 25, -22.37, -56.76, 0.13584, 26, -62.13, -48.86, 1e-05, 4, 18, 69.93, -55.04, 0.22299, 24, 19.62, -59.75, 0.53657, 25, -13.9, -59.57, 0.23888, 26, -54.11, -52.78, 0.00157, 4, 18, 75.92, -63.8, 0.16555, 24, 23.87, -69.47, 0.54128, 25, -9.78, -69.35, 0.29019, 26, -51.35, -63.03, 0.00299, 4, 18, 88.38, -54.79, 0.10854, 24, 37.8, -62.95, 0.50315, 25, 4.23, -63.01, 0.36983, 26, -36.61, -58.63, 0.01848, 4, 18, 107.39, -44.81, 0.02252, 24, 58.34, -56.69, 0.31749, 25, 24.85, -57.03, 0.54874, 26, -15.37, -55.46, 0.11125, 5, 18, 128.66, -27, 1e-05, 24, 82.55, -43.16, 0.074, 25, 49.25, -43.82, 0.4639, 26, 10.57, -45.66, 0.44877, 27, -23.35, -48.32, 0.01332, 4, 24, 103.98, -32.91, 0.00687, 25, 70.81, -33.86, 0.16669, 26, 33.28, -38.67, 0.70633, 27, -1.48, -39.03, 0.1201, 4, 25, 104.34, -23.3, 0.00024, 26, 67.93, -32.72, 0.19541, 27, 32.37, -29.53, 0.69743, 28, 3.92, -29.29, 0.10692, 3, 26, 108.3, -31.36, 4e-05, 27, 72.39, -24.02, 0.1512, 28, 42.47, -17.21, 0.84876, 2, 27, 94.05, -1.48, 0.00046, 28, 60.1, 8.61, 0.99954, 1, 28, 42.3, 32.25, 1, 2, 27, 72.15, 41.85, 0.02122, 28, 31.31, 47.71, 0.97878, 3, 26, 90.85, 38.4, 0.00042, 27, 47.83, 43.57, 0.29806, 28, 7.04, 45.37, 0.70152, 3, 26, 70.95, 41.9, 0.05076, 27, 27.68, 44.99, 0.72855, 28, -13.06, 43.43, 0.22069, 3, 25, 63.38, 48.38, 0.04174, 26, 36.95, 43.81, 0.57412, 27, -6.33, 43.39, 0.38414, 4, 24, 77.8, 47.59, 0.00573, 25, 45.7, 46.99, 0.17553, 26, 19.25, 44.81, 0.70376, 27, -24.05, 42.56, 0.11497, 5, 18, 85.95, 51.9, 1e-05, 24, 55.32, 42.32, 0.09056, 25, 23.15, 42.02, 0.47259, 26, -3.76, 42.91, 0.43057, 27, -46.74, 38.29, 0.00627, 4, 18, 66.02, 43.04, 0.02358, 24, 34.08, 37.34, 0.36062, 25, 1.85, 37.32, 0.48758, 26, -25.5, 41.11, 0.12822, 4, 18, 46.89, 31.6, 0.21942, 24, 13.16, 29.67, 0.59143, 25, -19.17, 29.93, 0.17494, 26, -47.33, 36.61, 0.0142, 3, 18, 18.7, 14.74, 0.90203, 24, -17.68, 18.36, 0.0971, 25, -50.16, 19.04, 0.00088, 2, 18, 7.29, 7.92, 0.98995, 24, -30.17, 13.79, 0.01005, 1, 18, -3.11, 1.7, 1, 1, 18, -4.29, -13.06, 1, 3, 18, 45.94, -49.08, 0.60808, 24, -2.83, -49.42, 0.35512, 25, -36.21, -48.94, 0.0368, 3, 18, 49.6, -23.2, 0.54529, 24, 5.59, -24.68, 0.43705, 25, -27.46, -24.32, 0.01766, 1, 18, 29.97, -11.71, 1, 4, 18, 72.39, -32.35, 0.14682, 24, 26.27, -37.92, 0.6153, 25, -6.96, -37.83, 0.23464, 26, -44.32, -32.17, 0.00324, 5, 18, 104.6, -10.81, 0.00015, 24, 61.94, -22.76, 0.08895, 25, 28.91, -23.15, 0.74143, 26, -6.81, -22.44, 0.1694, 27, -43.03, -27.02, 7e-05, 4, 24, 86.09, -6.83, 0.00031, 25, 53.26, -7.55, 0.0637, 26, 19.42, -10.25, 0.91937, 27, -18.19, -12.19, 0.01661, 2, 26, 50.1, 3.66, 0.00657, 27, 10.88, 4.81, 0.99343, 2, 27, 36.01, 14.82, 0.69309, 28, 0.15, 15.05, 0.30691, 4, 24, 88.87, 28.75, 4e-05, 25, 56.52, 28, 0.05966, 26, 27.42, 24.54, 0.80101, 27, -13.83, 23.24, 0.1393, 3, 24, 58.4, 17.58, 0.04081, 25, 25.9, 17.24, 0.66024, 26, -4.36, 17.98, 0.29895, 4, 18, 65.47, 10.44, 0.02339, 24, 27.46, 5.41, 0.81417, 25, -5.19, 5.48, 0.1577, 26, -36.75, 10.51, 0.00474, 3, 18, 50.17, -0.54, 0.16167, 24, 10.38, -2.52, 0.83629, 25, -22.38, -2.22, 0.00204, 2, 24, 27.94, -10.66, 0.97369, 25, -4.93, -10.59, 0.02631, 2, 25, 26.66, 0.93, 0.98961, 26, -5.8, 1.72, 0.01039, 3, 25, 77.01, 22.77, 0.00059, 26, 47.03, 16.6, 0.27456, 27, 6.49, 17.37, 0.72485, 3, 26, 68.59, 26.24, 0.03162, 27, 26.95, 29.18, 0.81496, 28, -11.16, 27.71, 0.15341], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 56, 58, 0, 58, 60, 62, 62, 64, 64, 58, 66, 68, 68, 70, 70, 72, 72, 74, 44, 46, 46, 48, 40, 42, 42, 44, 36, 38, 38, 40, 48, 50, 50, 52, 52, 54, 54, 56], "width": 191, "height": 98}}, "phuonghoang": {"phuonghoang": {"y": 1.93, "width": 188, "height": 71}}, "than": {"than": {"type": "mesh", "uvs": [0.87816, 0.3706, 0.88309, 0.27344, 0.8498, 0.25506, 0.80665, 0.17891, 0.78815, 0.08963, 0.82144, 0.01873, 0.88062, 0, 0.94474, 0, 0.98789, 0.05286, 1, 0.10801, 1, 0.1579, 0.9657, 0.1579, 0.93734, 0.18416, 0.9546, 0.26031, 0.9694, 0.39424, 0.96693, 0.55705, 0.94351, 0.67259, 0.88802, 0.76187, 0.83254, 0.75925, 0.75733, 0.74874, 0.72404, 0.71986, 0.68828, 0.82752, 0.63033, 0.91943, 0.53046, 0.96932, 0.41826, 0.95357, 0.33318, 0.96407, 0.23085, 0.92205, 0.18276, 0.89579, 0.12358, 0.88004, 0.07179, 0.90105, 0.02987, 0.86428, 0.00028, 0.77237, 0.00015, 0.70941, 0, 0.6332, 0.0533, 0.52554, 0.11495, 0.4205, 0.20372, 0.32596, 0.2888, 0.33121, 0.41333, 0.2997, 0.51936, 0.37848, 0.53909, 0.42837, 0.65992, 0.39424, 0.77336, 0.38636, 0.91515, 0.12902, 0.90898, 0.25506, 0.91638, 0.42312, 0.81774, 0.52291, 0.71664, 0.5623, 0.64019, 0.58331, 0.68951, 0.6437, 0.57115, 0.77763, 0.43799, 0.80389, 0.32702, 0.74349, 0.22961, 0.66996, 0.13591, 0.60694, 0.07426, 0.76975, 0.19262, 0.78288, 0.26414, 0.8354, 0.37017, 0.90367, 0.4984, 0.89842, 0.57731, 0.85641, 0.63527, 0.70507, 0.53786, 0.60169, 0.42936, 0.56755, 0.35414, 0.54654, 0.274, 0.49928, 0.20865, 0.48877, 0.14331, 0.53341, 0.34551, 0.41787, 0.45155, 0.46514, 0.65499, 0.48615, 0.76349, 0.48089, 0.80048, 0.66734, 0.87199, 0.65158, 0.91515, 0.57805], "triangles": [67, 34, 35, 54, 34, 67, 33, 34, 54, 32, 33, 54, 55, 32, 54, 55, 54, 53, 31, 32, 55, 30, 31, 55, 28, 55, 56, 30, 55, 28, 29, 30, 28, 66, 36, 37, 65, 66, 37, 35, 36, 66, 66, 67, 35, 53, 66, 65, 54, 67, 66, 53, 54, 66, 56, 55, 53, 27, 28, 56, 68, 37, 38, 69, 68, 38, 68, 65, 37, 64, 68, 69, 65, 68, 64, 53, 65, 64, 52, 53, 64, 52, 64, 63, 56, 53, 52, 52, 63, 51, 57, 56, 52, 57, 27, 56, 57, 52, 58, 26, 27, 57, 26, 57, 58, 63, 64, 69, 63, 69, 62, 51, 63, 62, 58, 52, 51, 58, 51, 59, 24, 58, 59, 25, 26, 58, 25, 58, 24, 51, 62, 50, 60, 50, 21, 51, 50, 60, 59, 51, 60, 22, 60, 21, 23, 59, 60, 23, 60, 22, 24, 59, 23, 61, 62, 48, 61, 48, 49, 20, 49, 72, 61, 49, 20, 50, 62, 61, 21, 61, 20, 50, 61, 21, 39, 69, 38, 69, 39, 40, 70, 40, 41, 70, 41, 71, 70, 62, 40, 48, 70, 47, 62, 69, 40, 48, 62, 70, 49, 48, 47, 71, 41, 42, 0, 71, 42, 45, 46, 71, 47, 70, 71, 47, 71, 46, 72, 47, 46, 72, 46, 73, 49, 47, 72, 19, 20, 72, 18, 72, 73, 19, 72, 18, 43, 6, 7, 43, 7, 8, 43, 8, 9, 11, 43, 9, 11, 9, 10, 12, 43, 11, 5, 6, 43, 4, 5, 43, 3, 4, 43, 43, 2, 3, 43, 44, 2, 43, 12, 44, 44, 12, 13, 1, 2, 44, 44, 0, 1, 45, 44, 14, 13, 14, 44, 15, 45, 14, 16, 74, 15, 44, 45, 0, 45, 71, 0, 74, 46, 45, 15, 74, 45, 73, 46, 74, 73, 74, 16, 17, 73, 16, 18, 73, 17], "vertices": [4, 15, 8.64, -11.22, 0.63208, 30, 8.6, 11.87, 0.36264, 31, -13.2, 9.78, 0.00244, 33, -19.13, -17.05, 0.00283, 3, 15, 1.98, -22.15, 0.02092, 30, 20.91, 8.37, 0.35556, 31, -0.47, 8.41, 0.62352, 2, 30, 24.86, 17.11, 0.04388, 31, 1.94, 17.7, 0.95612, 2, 31, 11.91, 29.74, 0.98221, 32, -30.46, -4.07, 0.01779, 2, 31, 23.61, 34.9, 0.94587, 32, -34.15, 8.17, 0.05413, 2, 31, 32.9, 25.61, 0.85417, 32, -23.8, 16.26, 0.14583, 2, 31, 35.35, 9.1, 0.46355, 32, -7.11, 16.67, 0.53645, 2, 31, 35.35, -8.79, 0.0026, 32, 10.64, 14.49, 0.9974, 1, 32, 21.75, 6.14, 1, 1, 32, 24.22, -1.44, 1, 1, 32, 23.42, -7.92, 1, 2, 31, 14.67, -14.64, 0.06871, 32, 13.92, -6.76, 0.93129, 4, 30, 29.88, -8.53, 0.0121, 31, 11.23, -6.73, 0.69361, 32, 5.65, -9.2, 0.29429, 39, -165.8, -122.38, 0, 2, 30, 19.24, -11.59, 0.61892, 31, 1.25, -11.54, 0.38108, 1, 30, 1.25, -12.69, 1, 3, 15, -3.37, 21.43, 0.13159, 30, -19.66, -8.41, 0.86556, 33, -39.55, 11.12, 0.00285, 4, 15, 8.99, 32.34, 0.40977, 30, -33.47, 0.59, 0.52732, 33, -30.61, 24.97, 0.06256, 35, -64.43, 38.18, 0.00035, 4, 15, 27.97, 36.33, 0.47462, 30, -42.38, 17.82, 0.24329, 33, -13.42, 33.97, 0.2689, 35, -45.24, 41.06, 0.01319, 4, 15, 41.83, 29.43, 0.31004, 30, -39.43, 33.02, 0.0981, 33, 1.79, 31.08, 0.53493, 35, -31.81, 33.37, 0.05693, 5, 15, 60.24, 19.25, 0.0336, 30, -34.53, 53.47, 0.00852, 33, 22.27, 26.27, 0.67808, 35, -14.02, 22.15, 0.27931, 36, -51.74, 0.64, 0.00048, 5, 15, 67.03, 11.87, 0.00214, 30, -29.23, 61.99, 0.00072, 33, 30.81, 21.01, 0.38505, 35, -7.67, 14.39, 0.5961, 36, -42.82, -3.94, 0.01598, 3, 33, 42.96, 33.29, 0.01568, 35, 7.83, 22.02, 0.74052, 36, -31.65, 9.24, 0.24381, 3, 35, 27.78, 24.88, 0.18135, 36, -14.5, 19.83, 0.81865, 39, -115, -3.92, 0, 2, 36, 13.83, 23.91, 1, 39, -90.63, 11.1, 0, 4, 36, 44.83, 19.13, 0.33751, 37, 2.78, 20.64, 0.65542, 38, -29.73, 14.83, 0.00707, 39, -60.28, 19.04, 0, 4, 36, 68.6, 18.44, 0.00165, 37, 25.86, 26.37, 0.62, 38, -8.33, 25.19, 0.37734, 39, -38.2, 27.85, 0.00101, 4, 37, 54.93, 26.22, 0.03262, 38, 20.15, 31.04, 0.72446, 39, -9.37, 31.66, 0.20071, 40, -11.16, 46.8, 0.04221, 4, 37, 68.75, 25.31, 0.00035, 38, 33.86, 33, 0.40698, 39, 4.45, 32.63, 0.40158, 40, 0.66, 39.59, 0.19109, 3, 38, 49.9, 37.42, 0.09842, 39, 20.76, 35.9, 0.30133, 40, 15.84, 32.78, 0.60025, 3, 38, 62.2, 45.5, 0.01657, 39, 33.6, 43.08, 0.11844, 40, 30.47, 31.19, 0.86498, 3, 38, 74.85, 45.53, 0.00193, 39, 46.22, 42.21, 0.0472, 40, 40.24, 23.16, 0.95087, 2, 39, 57.86, 33.4, 0.00608, 40, 44.61, 9.24, 0.99392, 1, 40, 42.24, 1.34, 1, 1, 40, 39.36, -8.22, 1, 2, 39, 54.05, -1.96, 0.0052, 40, 21.01, -17.36, 0.9948, 5, 34, 163.9, -34.73, 0.00046, 37, 98.82, -32.4, 0.00011, 38, 75.19, -17.27, 0.00577, 39, 42.09, -20.45, 0.38616, 40, 0.54, -25.49, 0.60749, 6, 34, 138.04, -44.62, 0.01102, 35, 95.26, -100.25, 0.00234, 37, 76.76, -49.14, 0.02261, 38, 57.05, -38.19, 0.16649, 39, 22.5, -40.03, 0.71861, 40, -26.77, -30.08, 0.07894, 7, 34, 114.49, -41.61, 0.04799, 35, 74.74, -88.31, 0.017, 36, 73.72, -65.23, 0.00471, 37, 53.31, -52.83, 0.11696, 38, 34.86, -46.65, 0.40013, 39, -0.23, -46.89, 0.41148, 40, -49.27, -22.48, 0.00173, 6, 34, 79.5, -42.31, 0.19115, 35, 42.23, -75.36, 0.08361, 36, 38.75, -66.32, 0.06021, 37, 19.92, -63.29, 0.2748, 38, 4.35, -63.77, 0.31738, 39, -31.89, -61.79, 0.07284, 6, 34, 51.08, -29.14, 0.42052, 35, 21.16, -52.17, 0.17042, 36, 10.17, -53.46, 0.09747, 37, -11.06, -58.6, 0.18579, 38, -26.93, -65.57, 0.11848, 39, -63.22, -61.35, 0.00733, 6, 34, 46.24, -22.09, 0.5395, 35, 19.45, -43.79, 0.18309, 36, 5.26, -46.47, 0.07928, 37, -17.68, -53.19, 0.12532, 38, -34.52, -61.64, 0.07044, 39, -70.5, -56.89, 0.00237, 5, 33, 41.44, -24, 0.15783, 34, 12.25, -23.24, 0.83959, 37, -49.99, -63.8, 0.00183, 38, -63.95, -78.68, 0.00075, 39, -101.07, -71.8, 0, 3, 15, 35.98, -21.8, 0.17245, 33, 10.05, -19.82, 0.78909, 34, -19.35, -21.16, 0.03845, 2, 31, 18.45, -0.54, 0.62826, 32, 0.38, -1.28, 0.37174, 3, 15, -5.59, -21.25, 0.00074, 30, 22.07, 0.84, 0.18248, 31, 1.94, 1.18, 0.81677, 1, 15, 1.92, -0.45, 1, 2, 15, 32.39, -0.34, 0.38197, 33, 0.78, -0.14, 0.61803, 3, 33, 29.45, 0.32, 0.71017, 34, -1.34, 0.23, 0.28503, 35, -15.71, -4.73, 0.0048, 2, 34, 20.16, 0.87, 0.9544, 35, 4.35, -12.5, 0.0456, 4, 33, 38.67, 9.59, 0.07018, 34, 7.24, 10.1, 0.11875, 35, -3.97, 1.02, 0.81102, 36, -34.09, -14.72, 4e-05, 2, 35, 33.43, 0.68, 0.07798, 36, 0.33, -0.12, 0.92202, 3, 36, 37.64, 0.08, 0.35934, 37, 0.98, 0.35, 0.64066, 39, -59.3, -1.31, 0, 3, 34, 109.17, 13.18, 0.00038, 35, 91.14, -35.76, 0.00017, 38, 4.32, -0.84, 0.99945, 3, 38, 33.12, 0.67, 0.27522, 39, 1.4, 0.43, 0.72445, 40, -20.5, 15.12, 0.00033, 2, 39, 28.81, 0.87, 0.17002, 40, 2.09, -0.42, 0.82998, 3, 38, 68.15, 29.35, 0.0083, 39, 38.39, 26.54, 0.0792, 40, 24.78, 14.94, 0.9125, 3, 38, 36.98, 18.29, 0.32891, 39, 6.51, 17.73, 0.54344, 40, -6.3, 26.25, 0.12765, 4, 37, 47.9, 13.35, 0.05015, 38, 15.92, 17, 0.86293, 39, -14.59, 17.95, 0.0786, 40, -23.37, 38.66, 0.00832, 3, 36, 57.63, 11.45, 0.03124, 37, 17.17, 16.69, 0.84856, 38, -14.83, 13.93, 0.12021, 3, 36, 21.93, 13.88, 0.98581, 37, -17.87, 9.42, 0.01419, 39, -79.21, 5.11, 0, 2, 36, -0.48, 10.31, 1, 39, -98.35, -7.07, 0, 2, 35, 13.17, 0.87, 0.98895, 36, -18.32, -8.03, 0.01105, 6, 34, 48.81, 0.47, 0.33561, 35, 30.59, -24.01, 0.40523, 36, 7.58, -23.89, 0.16856, 37, -21.52, -30.81, 0.06553, 38, -42.9, -40.53, 0.02504, 39, -77.36, -35.25, 2e-05, 6, 34, 78.5, -6.95, 0.1426, 35, 55.05, -42.39, 0.09263, 36, 37.35, -30.98, 0.17467, 37, 9.06, -29.63, 0.39958, 38, -13.22, -33.07, 0.17781, 39, -47.23, -29.92, 0.01272, 6, 34, 99.11, -11.75, 0.06447, 35, 72.18, -54.82, 0.02991, 36, 58.01, -35.54, 0.02837, 37, 30.19, -28.47, 0.31235, 38, 7.22, -27.57, 0.49342, 39, -26.45, -25.89, 0.07148, 7, 34, 120.75, -20.11, 0.02397, 35, 88.87, -70.94, 0.00796, 36, 79.75, -43.66, 0.00093, 37, 53.31, -30.43, 0.07243, 38, 30.24, -24.73, 0.48658, 39, -3.28, -24.69, 0.40794, 40, -38.88, -2.64, 0.00019, 6, 34, 138.76, -23.26, 0.00632, 35, 104.24, -80.85, 0.00127, 37, 71.48, -28.43, 0.01252, 38, 47.61, -19.02, 0.13146, 39, 14.45, -20.23, 0.80104, 40, -21.85, -9.29, 0.04739, 5, 34, 157.48, -19.23, 0.00028, 37, 88.32, -19.32, 3e-05, 38, 62.21, -6.63, 0.00156, 39, 29.9, -8.92, 0.51349, 40, -2.7, -9.03, 0.48463, 6, 34, 99.85, -28.76, 0.09145, 35, 66.25, -70.78, 0.03799, 36, 58.94, -52.54, 0.0237, 37, 35.66, -44.59, 0.22474, 38, 15.9, -42.22, 0.44385, 39, -18.83, -41.12, 0.17828, 6, 34, 71.02, -19.7, 0.24736, 35, 43.21, -51.22, 0.12893, 36, 30.01, -43.8, 0.12676, 37, 5.44, -43.96, 0.28202, 38, -13.8, -47.83, 0.19358, 39, -48.86, -44.6, 0.02135, 5, 33, 44.77, -12.35, 0.06008, 34, 14.8, -11.39, 0.93715, 37, -50.85, -51.71, 0.00195, 38, -67.29, -67.03, 0.00082, 39, -103.57, -59.94, 0, 3, 15, 43.74, -11.77, 0.02782, 33, 14.8, -8.05, 0.94644, 34, -15.39, -9.1, 0.02575, 4, 15, 44.8, 14.73, 0.14316, 30, -26.05, 39.8, 0.03051, 33, 8.64, 17.73, 0.73484, 35, -29.69, 18.52, 0.0915, 4, 15, 25.87, 21.35, 0.55051, 30, -27.39, 19.79, 0.20653, 33, -11.38, 18.98, 0.23237, 35, -48.21, 26.23, 0.01059, 5, 15, 10.87, 17.77, 0.472, 30, -19.93, 6.29, 0.5005, 33, -24.84, 11.46, 0.0274, 35, -63.39, 23.52, 0.0001, 39, -176.24, -71.47, 0], "hull": 43, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 0, 84, 86, 18, 86, 88, 88, 90, 90, 92, 92, 94, 96, 94, 100, 102, 102, 104, 106, 104, 106, 108, 62, 64, 64, 66, 108, 64, 98, 122, 122, 100], "width": 279, "height": 131}}, "than2": {"than2": {"type": "mesh", "uvs": [0.87816, 0.3706, 0.88309, 0.27344, 0.8498, 0.25506, 0.80665, 0.17891, 0.78815, 0.08963, 0.82144, 0.01873, 0.88062, 0, 0.94474, 0, 0.98789, 0.05286, 1, 0.10801, 1, 0.1579, 0.9657, 0.1579, 0.93734, 0.18416, 0.9546, 0.26031, 1, 0.38373, 1, 0.54917, 0.9546, 0.66208, 0.88309, 0.73299, 0.82761, 0.72248, 0.77952, 0.71723, 0.73143, 0.63583, 0.68828, 0.82752, 0.63033, 0.91943, 0.53046, 0.96932, 0.41826, 0.95357, 0.33318, 0.96407, 0.23085, 0.92205, 0.18276, 0.89579, 0.12358, 0.88004, 0.07179, 0.90105, 0.02987, 0.86428, 0.00028, 0.77237, 0.00015, 0.70941, 0, 0.6332, 0, 0.47375, 0, 0.32987, 0.00616, 0.06379, 0.22497, 0.03151, 0.3495, 0, 0.491, 0.23143, 0.56017, 0.24478, 0.65992, 0.28657, 0.76719, 0.29445, 0.91515, 0.12902, 0.90898, 0.25506, 0.91638, 0.42312, 0.81774, 0.52291, 0.71664, 0.5623, 0.64019, 0.58331, 0.68951, 0.6437, 0.57115, 0.77763, 0.43799, 0.80389, 0.32702, 0.74349, 0.22961, 0.66996, 0.13591, 0.60694, 0.07426, 0.76975, 0.19262, 0.78288, 0.26414, 0.8354, 0.37017, 0.90367, 0.4984, 0.89842, 0.57731, 0.85641, 0.63527, 0.70507, 0.53786, 0.60169, 0.42936, 0.56755, 0.35414, 0.54654, 0.274, 0.49928, 0.20865, 0.48877, 0.14331, 0.53341, 0.34551, 0.41787, 0.45155, 0.46514, 0.65499, 0.48615, 0.76349, 0.48089, 0.79925, 0.62795, 0.87199, 0.65158, 0.91515, 0.57805], "triangles": [66, 34, 35, 67, 34, 66, 67, 33, 34, 54, 33, 67, 32, 33, 54, 55, 32, 54, 55, 54, 53, 31, 32, 55, 30, 31, 55, 28, 55, 56, 30, 55, 28, 29, 30, 28, 37, 35, 36, 68, 35, 37, 35, 68, 66, 65, 66, 68, 53, 66, 65, 54, 67, 66, 53, 54, 66, 56, 55, 53, 27, 28, 56, 39, 68, 37, 39, 37, 38, 64, 68, 69, 65, 68, 64, 53, 65, 64, 52, 53, 64, 52, 64, 63, 56, 53, 52, 52, 63, 51, 57, 56, 52, 57, 27, 56, 57, 52, 58, 26, 27, 57, 26, 57, 58, 63, 64, 69, 62, 63, 69, 51, 63, 62, 58, 52, 51, 58, 51, 59, 24, 58, 59, 25, 26, 58, 25, 58, 24, 51, 62, 50, 60, 50, 21, 51, 50, 60, 59, 51, 60, 22, 60, 21, 23, 59, 60, 23, 60, 22, 24, 59, 23, 20, 49, 47, 61, 62, 48, 61, 48, 49, 50, 62, 61, 20, 21, 61, 50, 61, 21, 20, 61, 49, 69, 68, 39, 69, 39, 40, 41, 69, 40, 70, 41, 71, 70, 69, 41, 70, 62, 69, 48, 70, 47, 70, 48, 62, 49, 48, 47, 71, 41, 42, 71, 42, 0, 46, 71, 45, 47, 70, 71, 47, 71, 46, 72, 47, 46, 72, 46, 74, 20, 47, 72, 19, 20, 72, 18, 19, 72, 73, 18, 72, 43, 6, 7, 43, 7, 8, 43, 8, 9, 11, 43, 9, 11, 9, 10, 12, 43, 11, 5, 6, 43, 4, 5, 43, 3, 4, 43, 43, 2, 3, 43, 44, 2, 43, 12, 44, 44, 12, 13, 1, 2, 44, 44, 0, 1, 44, 13, 45, 14, 45, 13, 45, 14, 15, 16, 74, 15, 44, 45, 0, 71, 0, 45, 74, 46, 45, 15, 74, 45, 73, 72, 74, 73, 74, 16, 17, 73, 16, 18, 73, 17], "vertices": [4, 17, 8.64, -11.22, 0.63208, 41, 8.6, 11.87, 0.36264, 42, -13.2, 9.78, 0.00244, 44, -19.13, -17.05, 0.00283, 3, 17, 1.98, -22.15, 0.02092, 41, 20.91, 8.37, 0.35556, 42, -0.47, 8.41, 0.62352, 2, 41, 24.86, 17.11, 0.04388, 42, 1.94, 17.7, 0.95612, 2, 42, 11.91, 29.74, 0.98221, 43, -30.46, -4.07, 0.01779, 2, 42, 23.61, 34.9, 0.94587, 43, -34.15, 8.17, 0.05413, 2, 42, 32.9, 25.61, 0.85417, 43, -23.8, 16.26, 0.14583, 2, 42, 35.35, 9.1, 0.46355, 43, -7.11, 16.67, 0.53645, 2, 42, 35.35, -8.79, 0.0026, 43, 10.64, 14.49, 0.9974, 1, 43, 21.75, 6.14, 1, 1, 43, 24.22, -1.44, 1, 1, 43, 23.42, -7.92, 1, 2, 42, 14.67, -14.64, 0.06871, 43, 13.92, -6.76, 0.93129, 4, 41, 29.88, -8.53, 0.0121, 42, 11.23, -6.73, 0.69361, 43, 5.65, -9.2, 0.29429, 50, -165.8, -122.38, 0, 2, 41, 19.24, -11.59, 0.61892, 42, 1.25, -11.54, 0.38108, 1, 41, 1.16, -21.34, 1, 3, 17, -12.16, 24.42, 0.13159, 41, -20.2, -17.68, 0.86556, 44, -48.82, 11.62, 0.00285, 4, 17, 5.6, 32.41, 0.40977, 41, -32.64, -2.7, 0.52732, 44, -33.89, 24.12, 0.06256, 46, -67.81, 38.44, 0.00035, 4, 17, 27.61, 32.32, 0.47462, 41, -38.42, 18.54, 0.24329, 44, -12.68, 30.01, 0.2689, 46, -45.84, 37.08, 0.01319, 4, 17, 41.03, 24.49, 0.31004, 41, -34.45, 33.56, 0.0981, 44, 2.36, 26.1, 0.53493, 46, -32.9, 28.48, 0.05693, 5, 17, 52.87, 18.15, 0.0336, 41, -31.5, 46.67, 0.00852, 44, 15.48, 23.22, 0.67808, 46, -21.43, 21.48, 0.27931, 47, -58.27, -2.94, 0.00048, 5, 17, 60.47, 2.79, 0.00214, 41, -18.73, 58.09, 0.00072, 44, 26.96, 10.5, 0.38505, 46, -14.74, 5.7, 0.5961, 47, -45.83, -14.73, 0.01598, 3, 44, 42.96, 33.29, 0.01568, 46, 7.83, 22.02, 0.74052, 47, -31.65, 9.24, 0.24381, 3, 46, 27.78, 24.88, 0.18135, 47, -14.5, 19.83, 0.81865, 50, -115, -3.92, 0, 2, 47, 13.83, 23.91, 1, 50, -90.63, 11.1, 0, 4, 47, 44.83, 19.13, 0.33751, 48, 2.78, 20.64, 0.65542, 49, -29.73, 14.83, 0.00707, 50, -60.28, 19.04, 0, 4, 47, 68.6, 18.44, 0.00165, 48, 25.86, 26.37, 0.62, 49, -8.33, 25.19, 0.37734, 50, -38.2, 27.85, 0.00101, 4, 48, 54.93, 26.22, 0.03262, 49, 20.15, 31.04, 0.72446, 50, -9.37, 31.66, 0.20071, 51, -11.16, 46.8, 0.04221, 4, 48, 68.75, 25.31, 0.00035, 49, 33.86, 33, 0.40698, 50, 4.45, 32.63, 0.40158, 51, 0.66, 39.59, 0.19109, 3, 49, 49.9, 37.42, 0.09842, 50, 20.76, 35.9, 0.30133, 51, 15.84, 32.78, 0.60025, 3, 49, 62.2, 45.5, 0.01657, 50, 33.6, 43.08, 0.11844, 51, 30.47, 31.19, 0.86498, 3, 49, 74.85, 45.53, 0.00193, 50, 46.22, 42.21, 0.0472, 51, 40.24, 23.16, 0.95087, 2, 50, 57.86, 33.4, 0.00608, 51, 44.61, 9.24, 0.99392, 1, 51, 42.24, 1.34, 1, 1, 51, 39.36, -8.22, 1, 2, 50, 70.3, -3.69, 0.0052, 51, 33.25, -28.2, 0.9948, 5, 45, 194.65, -49.69, 0.00046, 48, 132.53, -38.16, 0.00011, 49, 109.36, -15.95, 0.00577, 50, 76.26, -21.57, 0.38616, 51, 27.73, -46.22, 0.60749, 6, 45, 189.52, -84.21, 0.01102, 46, 127.31, -156.74, 0.00234, 48, 137.27, -72.73, 0.02261, 49, 121.12, -48.8, 0.16649, 50, 85.66, -55.18, 0.71861, 51, 15.9, -79.05, 0.07894, 7, 45, 128.36, -82.43, 0.04799, 46, 71.64, -131.31, 0.017, 47, 88.04, -105.89, 0.00471, 48, 78.04, -88.14, 0.11696, 49, 66.35, -76.09, 0.40013, 50, 29.08, -78.5, 0.41148, 51, -43.72, -65.24, 0.00173, 6, 45, 93.37, -83.13, 0.19115, 46, 39.14, -118.36, 0.08361, 47, 53.07, -106.98, 0.06021, 48, 44.66, -98.6, 0.2748, 49, 35.84, -93.21, 0.31738, 50, -2.58, -93.4, 0.07284, 6, 45, 57.06, -49.08, 0.42052, 46, 18.92, -72.87, 0.17042, 47, 16.38, -73.34, 0.09747, 48, 0.27, -76.08, 0.18579, 49, -12.25, -80.33, 0.11848, 50, -49.62, -77.13, 0.00733, 6, 45, 38.03, -45.45, 0.5395, 46, 2.8, -62.12, 0.18309, 47, -2.69, -69.92, 0.07928, 48, -19.02, -77.92, 0.12532, 49, -30.74, -86.11, 0.07044, 50, -68.48, -81.57, 0.00237, 5, 44, 39.12, -37.91, 0.15783, 45, 10.87, -37.27, 0.83959, 48, -47.39, -77.66, 0.00183, 49, -58.55, -91.71, 0.00075, 50, -96.61, -85.18, 0, 3, 17, 32.41, -33.43, 0.17245, 44, 9.77, -31.98, 0.78909, 45, -18.81, -33.31, 0.03845, 2, 42, 18.45, -0.54, 0.62826, 43, 0.38, -1.28, 0.37174, 3, 17, -5.59, -21.25, 0.00074, 41, 22.07, 0.84, 0.18248, 42, 1.94, 1.18, 0.81677, 1, 17, 1.92, -0.45, 1, 2, 17, 32.39, -0.34, 0.38197, 44, 0.78, -0.14, 0.61803, 3, 44, 29.45, 0.32, 0.71017, 45, -1.34, 0.23, 0.28503, 46, -15.71, -4.73, 0.0048, 2, 45, 20.16, 0.87, 0.9544, 46, 4.35, -12.5, 0.0456, 4, 44, 38.67, 9.59, 0.07018, 45, 7.24, 10.1, 0.11875, 46, -3.97, 1.02, 0.81102, 47, -34.09, -14.72, 4e-05, 2, 46, 33.43, 0.68, 0.07798, 47, 0.33, -0.12, 0.92202, 3, 47, 37.64, 0.08, 0.35934, 48, 0.98, 0.35, 0.64066, 50, -59.3, -1.31, 0, 3, 45, 109.17, 13.18, 0.00038, 46, 91.14, -35.76, 0.00017, 49, 4.32, -0.84, 0.99945, 3, 49, 33.12, 0.67, 0.27522, 50, 1.4, 0.43, 0.72445, 51, -20.5, 15.12, 0.00033, 2, 50, 28.81, 0.87, 0.17002, 51, 2.09, -0.42, 0.82998, 3, 49, 68.15, 29.35, 0.0083, 50, 38.39, 26.54, 0.0792, 51, 24.78, 14.94, 0.9125, 3, 49, 36.98, 18.29, 0.32891, 50, 6.51, 17.73, 0.54344, 51, -6.3, 26.25, 0.12765, 4, 48, 47.9, 13.35, 0.05015, 49, 15.92, 17, 0.86293, 50, -14.59, 17.95, 0.0786, 51, -23.37, 38.66, 0.00832, 3, 47, 57.63, 11.45, 0.03124, 48, 17.17, 16.69, 0.84856, 49, -14.83, 13.93, 0.12021, 3, 47, 21.93, 13.88, 0.98581, 48, -17.87, 9.42, 0.01419, 50, -79.21, 5.11, 0, 2, 47, -0.48, 10.31, 1, 50, -98.35, -7.07, 0, 2, 46, 13.17, 0.87, 0.98895, 47, -18.32, -8.03, 0.01105, 6, 45, 48.81, 0.47, 0.33561, 46, 30.59, -24.01, 0.40523, 47, 7.58, -23.89, 0.16856, 48, -21.52, -30.81, 0.06553, 49, -42.9, -40.53, 0.02504, 50, -77.36, -35.25, 2e-05, 6, 45, 78.5, -6.95, 0.1426, 46, 55.05, -42.39, 0.09263, 47, 37.35, -30.98, 0.17467, 48, 9.06, -29.63, 0.39958, 49, -13.22, -33.07, 0.17781, 50, -47.23, -29.92, 0.01272, 6, 45, 99.11, -11.75, 0.06447, 46, 72.18, -54.82, 0.02991, 47, 58.01, -35.54, 0.02837, 48, 30.19, -28.47, 0.31235, 49, 7.22, -27.57, 0.49342, 50, -26.45, -25.89, 0.07148, 7, 45, 120.75, -20.11, 0.02397, 46, 88.87, -70.94, 0.00796, 47, 79.75, -43.66, 0.00093, 48, 53.31, -30.43, 0.07243, 49, 30.24, -24.73, 0.48658, 50, -3.28, -24.69, 0.40794, 51, -38.88, -2.64, 0.00019, 6, 45, 138.76, -23.26, 0.00632, 46, 104.24, -80.85, 0.00127, 48, 71.48, -28.43, 0.01252, 49, 47.61, -19.02, 0.13146, 50, 14.45, -20.23, 0.80104, 51, -21.85, -9.29, 0.04739, 5, 45, 157.48, -19.23, 0.00028, 48, 88.32, -19.32, 3e-05, 49, 62.21, -6.63, 0.00156, 50, 29.9, -8.92, 0.51349, 51, -2.7, -9.03, 0.48463, 6, 45, 99.85, -28.76, 0.09145, 46, 66.25, -70.78, 0.03799, 47, 58.94, -52.54, 0.0237, 48, 35.66, -44.59, 0.22474, 49, 15.9, -42.22, 0.44385, 50, -18.83, -41.12, 0.17828, 6, 45, 71.02, -19.7, 0.24736, 46, 43.21, -51.22, 0.12893, 47, 30.01, -43.8, 0.12676, 48, 5.44, -43.96, 0.28202, 49, -13.8, -47.83, 0.19358, 50, -48.86, -44.6, 0.02135, 5, 44, 44.77, -12.35, 0.06008, 45, 14.8, -11.39, 0.93715, 48, -50.85, -51.71, 0.00195, 49, -67.29, -67.03, 0.00082, 50, -103.57, -59.94, 0, 3, 17, 43.74, -11.77, 0.02782, 44, 14.8, -8.05, 0.94644, 45, -15.39, -9.1, 0.02575, 4, 17, 42.91, 9.91, 0.14316, 41, -20.91, 39.27, 0.03051, 44, 8.13, 12.59, 0.73484, 46, -31.85, 13.82, 0.0915, 4, 17, 25.87, 21.35, 0.55051, 41, -27.39, 19.79, 0.20653, 44, -11.38, 18.98, 0.23237, 46, -48.21, 26.23, 0.01059, 5, 17, 10.87, 17.77, 0.472, 41, -19.93, 6.29, 0.5005, 44, -24.84, 11.46, 0.0274, 46, -63.39, 23.52, 0.0001, 50, -176.24, -71.47, 0], "hull": 43, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 0, 84, 86, 18, 86, 88, 88, 90, 90, 92, 92, 94, 96, 94, 100, 102, 102, 104, 106, 104, 106, 108, 62, 64, 64, 66, 108, 64, 98, 122, 122, 100], "width": 220, "height": 113}}, "trieu": {"trieu": {"x": 340.03, "y": 87.92, "width": 177, "height": 113}}, "trieu2": {"Trieu2": {"x": 340.03, "y": 95.07, "width": 134, "height": 109}}, "trieu3": {"trieu": {"x": -0.53, "y": 3.04, "width": 177, "height": 113}}, "trieu4": {"Trieu2": {"x": 8.94, "y": 6.12, "width": 134, "height": 109}}}}, "animations": {"Trieuhoi1": {"slots": {"canhsau": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "<PERSON><PERSON><PERSON>"}, {"time": 2, "name": "<PERSON><PERSON><PERSON>"}]}, "canhsau2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "canhsau2"}, {"time": 2, "name": "canhsau2"}]}, "canhtruoc": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"time": 2, "name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "canhtruoc2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "canhtruoc2"}, {"time": 2, "name": "canhtruoc2"}]}, "phuonghoang": {"color": [{"time": 0.8667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}, {"time": 0.8667, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"time": 2, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, "than": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "than"}, {"time": 2, "name": "than"}]}, "than2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "than2"}, {"time": 2, "name": "than2"}]}, "trieu": {"attachment": [{"time": 0, "name": "trieu"}, {"time": 2, "name": "trieu"}], "color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "color": "ffffffff"}]}, "trieu2": {"attachment": [{"time": 0, "name": "Trieu2"}, {"time": 2, "name": "Trieu2"}], "color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "color": "ffffffff"}]}, "trieu3": {"attachment": [{"time": 0, "name": null}, {"time": 2, "name": null}]}, "trieu4": {"attachment": [{"time": 0, "name": null}, {"time": 2, "name": null}]}}, "bones": {"than": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": -0.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 0, "y": -17.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "x": 0, "y": 3.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": -0.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": 0, "y": -17.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "x": 0, "y": 3.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": -0.54}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than2": {"rotate": [{"time": 0, "angle": -0.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -3.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -0.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": -3.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -0.87}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than3": {"rotate": [{"time": 0, "angle": -2.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": -0.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -7.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -2.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -0.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": -7.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -2.63}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than4": {"rotate": [{"time": 0, "angle": -4.92, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": -2.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": -7.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -4.92, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -2.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "angle": -7.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -4.92}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than8": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "angle": 0.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": -5.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 9.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -3.84, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 2.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than9": {"rotate": [{"time": 0, "angle": 0.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 0.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": -4.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": 10.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -3.38, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 2.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0.46}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than10": {"rotate": [{"time": 0, "angle": 1.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": 1.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -3.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": 11.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -2.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 1.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 2.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 1.37}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than11": {"rotate": [{"time": 0, "angle": 2.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 2.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -2.62, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": 12.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": -1.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 2.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 2.43}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than12": {"rotate": [{"time": 0, "angle": 1.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 1.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -3.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 11.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": -2.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 1.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 2.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 1.37}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than13": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 0.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -5.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 9.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -3.84, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 2.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "canhsau": {"rotate": [{"time": 0, "angle": 8.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -40.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 8.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -40.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 8.91}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "canhsau2": {"rotate": [{"time": 0, "angle": 3.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": -0.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -45.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 3.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -0.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": -45.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 3.06}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "canhsau3": {"rotate": [{"time": 0, "angle": -9.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 8.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -40.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -9.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 8.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": -40.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -9.29}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "canhsau4": {"rotate": [{"time": 0, "angle": -24.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 8.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": -40.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -24.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": 8.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "angle": -40.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -24.45}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "canhtruoc8": {"rotate": [{"time": 0, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 19.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 19.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -5.43}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "canhtruoc13": {"rotate": [{"time": 0, "angle": 1.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 1.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 1.67}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "canhtruoc14": {"rotate": [{"time": 0, "angle": 8.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 8.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 8.77}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "canhtruoc15": {"rotate": [{"time": 0, "angle": 15.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 15.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 15.88}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "canhtruoc16": {"rotate": [{"time": 0, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 22.98}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "canhtruoc17": {"rotate": [{"time": 0, "angle": 15.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 15.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 15.88}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": -566.67, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": -1, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 2, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than14": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": -0.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 0, "y": -17.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "x": 0, "y": 3.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": -0.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": 0, "y": -17.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "x": 0, "y": 3.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": -0.54}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than17": {"rotate": [{"time": 0, "angle": -4.92, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": -2.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": -7.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -4.92, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -2.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "angle": -7.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -4.92}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than16": {"rotate": [{"time": 0, "angle": -2.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": -0.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -7.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -2.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -0.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": -7.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -2.63}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than15": {"rotate": [{"time": 0, "angle": -0.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -3.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -0.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": -3.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -0.87}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than26": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 0.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -5.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 9.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -3.84, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 2.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than25": {"rotate": [{"time": 0, "angle": 1.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 1.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -3.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 11.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": -2.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 1.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 2.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 1.37}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than24": {"rotate": [{"time": 0, "angle": 2.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 2.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -2.62, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": 12.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": -1.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 2.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 2.43}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than23": {"rotate": [{"time": 0, "angle": 1.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": 1.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -3.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": 11.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -2.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 1.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 2.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 1.37}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than22": {"rotate": [{"time": 0, "angle": 0.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 0.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": -4.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": 10.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -3.38, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 2.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0.46}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than21": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "angle": 0.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": -5.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 9.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -3.84, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 2.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "canhtruoc9": {"rotate": [{"time": 0, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 19.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 19.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -5.43}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "canhtruoc22": {"rotate": [{"time": 0, "angle": 15.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 15.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 15.88}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "canhtruoc21": {"rotate": [{"time": 0, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 22.98}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "canhtruoc20": {"rotate": [{"time": 0, "angle": 15.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 15.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 15.88}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "canhtruoc19": {"rotate": [{"time": 0, "angle": 8.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 8.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 8.77}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "canhtruoc18": {"rotate": [{"time": 0, "angle": 1.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 1.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 1.67}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "canhsau5": {"rotate": [{"time": 0, "angle": 8.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -40.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 8.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -40.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 8.91}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "canhsau8": {"rotate": [{"time": 0, "angle": -24.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 8.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": -40.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -24.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": 8.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "angle": -40.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -24.45}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "canhsau7": {"rotate": [{"time": 0, "angle": -9.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 8.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -40.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -9.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 8.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": -40.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -9.29}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "canhsau6": {"rotate": [{"time": 0, "angle": 3.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": -0.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -45.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 3.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -0.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": -45.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 3.06}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "bone2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 548.25, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 3, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": -1, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "phuonghoang": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -3.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": -0.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "x": 0, "y": 14.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "x": 0, "y": -21.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 0, "y": 11.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "x": 0, "y": -2.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "x": 0.936, "y": 0.936, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "x": 1.096, "y": 1.096, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 0.93, "y": 0.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "x": 1.05, "y": 1.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than6": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than7": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than18": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than19": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "than20": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "bone3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "73": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "trieu3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}}, "deform": {"default": {"canhsau": {"canhsau": [{"time": 0, "curve": "stepped"}, {"time": 2}]}, "canhsau2": {"canhsau2": [{"time": 0, "curve": "stepped"}, {"time": 2}]}, "canhtruoc": {"canhtruoc": [{"time": 0, "curve": "stepped"}, {"time": 2}]}, "canhtruoc2": {"canhtruoc2": [{"time": 0, "curve": "stepped"}, {"time": 2}]}, "than": {"than": [{"time": 0, "curve": "stepped"}, {"time": 2}]}, "than2": {"than2": [{"time": 0, "curve": "stepped"}, {"time": 2}]}}}, "drawOrder": [{"time": 0}, {"time": 2}]}, "Trieuhoi2": {"slots": {"canhsau": {"attachment": [{"time": 0, "name": "<PERSON><PERSON><PERSON>"}, {"time": 3, "name": "<PERSON><PERSON><PERSON>"}]}, "canhsau2": {"attachment": [{"time": 0, "name": "canhsau2"}, {"time": 3, "name": "canhsau2"}]}, "canhtruoc": {"attachment": [{"time": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"time": 3, "name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "canhtruoc2": {"attachment": [{"time": 0, "name": "canhtruoc2"}, {"time": 3, "name": "canhtruoc2"}]}, "phuonghoang": {"attachment": [{"time": 0, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"time": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, "than": {"attachment": [{"time": 0, "name": "than"}, {"time": 3, "name": "than"}]}, "than2": {"attachment": [{"time": 0, "name": "than2"}, {"time": 3, "name": "than2"}]}, "trieu": {"attachment": [{"time": 0, "name": null}, {"time": 3, "name": null}]}, "trieu2": {"attachment": [{"time": 0, "name": null}, {"time": 3, "name": null}]}, "trieu3": {"attachment": [{"time": 0, "name": "trieu"}, {"time": 3, "name": "trieu"}]}, "trieu4": {"attachment": [{"time": 0, "name": "Trieu2"}, {"time": 3, "name": "Trieu2"}]}}, "bones": {"than11": {"rotate": [{"time": 0, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": 2.43}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than12": {"rotate": [{"time": 0, "angle": 1.37, "curve": [0.325, 0.31, 0.663, 0.65]}, {"time": 0.1667, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 1.37, "curve": [0.325, 0.31, 0.663, 0.65]}, {"time": 1.1667, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 1.37, "curve": [0.325, 0.31, 0.663, 0.65]}, {"time": 2.1667, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": 1.37}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "canhsau8": {"rotate": [{"time": 0, "angle": -24.45, "curve": [0.382, 0.56, 0.74, 1]}, {"time": 0.3667, "angle": 8.91, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.8667, "angle": -40.81, "curve": [0.336, 0.34, 0.671, 0.68]}, {"time": 1, "angle": -24.45, "curve": [0.382, 0.56, 0.74, 1]}, {"time": 1.3667, "angle": 8.91, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1.8667, "angle": -40.81, "curve": [0.336, 0.34, 0.671, 0.68]}, {"time": 2, "angle": -24.45, "curve": [0.382, 0.56, 0.74, 1]}, {"time": 2.3667, "angle": 8.91, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2.8667, "angle": -40.81, "curve": [0.336, 0.34, 0.671, 0.68]}, {"time": 3, "angle": -24.45}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "canhsau6": {"rotate": [{"time": 0, "angle": 3.06, "curve": [0.364, 0.64, 0.701, 1]}, {"time": 0.1333, "angle": -0.81, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.6333, "angle": -45.86, "curve": [0.356, 0.41, 0.711, 0.82]}, {"time": 1, "angle": 3.06, "curve": [0.364, 0.64, 0.701, 1]}, {"time": 1.1333, "angle": -0.81, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1.6333, "angle": -45.86, "curve": [0.356, 0.41, 0.711, 0.82]}, {"time": 2, "angle": 3.06, "curve": [0.364, 0.64, 0.701, 1]}, {"time": 2.1333, "angle": -0.81, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2.6333, "angle": -45.86, "curve": [0.356, 0.41, 0.711, 0.82]}, {"time": 3, "angle": 3.06}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than8": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.5, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1, "angle": 0}, {"time": 1.5, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2, "angle": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2.5, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "canhsau7": {"rotate": [{"time": 0, "angle": -9.29, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 0.2333, "angle": 8.91, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.7333, "angle": -40.81, "curve": [0.343, 0.36, 0.686, 0.73]}, {"time": 1, "angle": -9.29, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 1.2333, "angle": 8.91, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1.7333, "angle": -40.81, "curve": [0.343, 0.36, 0.686, 0.73]}, {"time": 2, "angle": -9.29, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 2.2333, "angle": 8.91, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2.7333, "angle": -40.81, "curve": [0.343, 0.36, 0.686, 0.73]}, {"time": 3, "angle": -9.29}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "canhsau5": {"rotate": [{"time": 0, "angle": 8.91, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.5, "angle": -40.81, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1, "angle": 8.91, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1.5, "angle": -40.81, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2, "angle": 8.91, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2.5, "angle": -40.81, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 3, "angle": 8.91}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than10": {"rotate": [{"time": 0, "angle": 1.37, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 0.8333, "angle": 2.43, "curve": [0.337, 0.35, 0.675, 0.69]}, {"time": 1, "angle": 1.37, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 1.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 2.43, "curve": [0.337, 0.35, 0.675, 0.69]}, {"time": 2, "angle": 1.37, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 2.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "angle": 2.43, "curve": [0.337, 0.35, 0.675, 0.69]}, {"time": 3, "angle": 1.37}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": -0.54, "curve": [0.333, 0.33, 0.758, 1]}, {"time": 0.3667, "x": 0, "y": -17.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "x": 0, "y": 3.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": -0.54, "curve": [0.333, 0.33, 0.758, 1]}, {"time": 1.3667, "x": 0, "y": -17.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "x": 0, "y": 3.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": -0.54, "curve": [0.333, 0.33, 0.758, 1]}, {"time": 2.3667, "x": 0, "y": -17.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8667, "x": 0, "y": 3.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 0, "y": -0.54}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than9": {"rotate": [{"time": 0, "angle": 0.46, "curve": [0.371, 0.63, 0.71, 1]}, {"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 2.43, "curve": [0.351, 0.39, 0.701, 0.78]}, {"time": 1, "angle": 0.46, "curve": [0.371, 0.63, 0.71, 1]}, {"time": 1.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 2.43, "curve": [0.351, 0.39, 0.701, 0.78]}, {"time": 2, "angle": 0.46, "curve": [0.371, 0.63, 0.71, 1]}, {"time": 2.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "angle": 2.43, "curve": [0.351, 0.39, 0.701, 0.78]}, {"time": 3, "angle": 0.46}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than4": {"rotate": [{"time": 0, "angle": -4.92, "curve": [0.343, 0.37, 0.679, 0.71]}, {"time": 0.1333, "angle": -2.69, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 0.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": -7.35, "curve": [0.336, 0.34, 0.671, 0.68]}, {"time": 1, "angle": -4.92, "curve": [0.343, 0.37, 0.679, 0.71]}, {"time": 1.1333, "angle": -2.69, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 1.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "angle": -7.35, "curve": [0.336, 0.34, 0.671, 0.68]}, {"time": 2, "angle": -4.92, "curve": [0.343, 0.37, 0.679, 0.71]}, {"time": 2.1333, "angle": -2.69, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 2.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8667, "angle": -7.35, "curve": [0.336, 0.34, 0.671, 0.68]}, {"time": 3, "angle": -4.92}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than2": {"rotate": [{"time": 0, "angle": -0.87, "curve": [0.364, 0.64, 0.701, 1]}, {"time": 0.1333, "angle": 0}, {"time": 0.6333, "angle": -3.53, "curve": [0.356, 0.41, 0.711, 0.82]}, {"time": 1, "angle": -0.87, "curve": [0.364, 0.64, 0.701, 1]}, {"time": 1.1333, "angle": 0}, {"time": 1.6333, "angle": -3.53, "curve": [0.356, 0.41, 0.711, 0.82]}, {"time": 2, "angle": -0.87, "curve": [0.364, 0.64, 0.701, 1]}, {"time": 2.1333, "angle": 0}, {"time": 2.6333, "angle": -3.53, "curve": [0.356, 0.41, 0.711, 0.82]}, {"time": 3, "angle": -0.87}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than3": {"rotate": [{"time": 0, "angle": -2.63, "curve": [0.352, 0.41, 0.688, 0.76]}, {"time": 0.1333, "angle": -0.87, "curve": [0.364, 0.64, 0.701, 1]}, {"time": 0.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -7.35, "curve": [0.343, 0.36, 0.686, 0.73]}, {"time": 1, "angle": -2.63, "curve": [0.352, 0.41, 0.688, 0.76]}, {"time": 1.1333, "angle": -0.87, "curve": [0.364, 0.64, 0.701, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": -7.35, "curve": [0.343, 0.36, 0.686, 0.73]}, {"time": 2, "angle": -2.63, "curve": [0.352, 0.41, 0.688, 0.76]}, {"time": 2.1333, "angle": -0.87, "curve": [0.364, 0.64, 0.701, 1]}, {"time": 2.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.7333, "angle": -7.35, "curve": [0.343, 0.36, 0.686, 0.73]}, {"time": 3, "angle": -2.63}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "canhsau2": {"rotate": [{"time": 0, "angle": 3.06, "curve": [0.364, 0.64, 0.701, 1]}, {"time": 0.1333, "angle": -0.81, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.6333, "angle": -45.86, "curve": [0.356, 0.41, 0.711, 0.82]}, {"time": 1, "angle": 3.06, "curve": [0.364, 0.64, 0.701, 1]}, {"time": 1.1333, "angle": -0.81, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1.6333, "angle": -45.86, "curve": [0.356, 0.41, 0.711, 0.82]}, {"time": 2, "angle": 3.06, "curve": [0.364, 0.64, 0.701, 1]}, {"time": 2.1333, "angle": -0.81, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2.6333, "angle": -45.86, "curve": [0.356, 0.41, 0.711, 0.82]}, {"time": 3, "angle": 3.06}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "canhsau3": {"rotate": [{"time": 0, "angle": -9.29, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 0.2333, "angle": 8.91, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.7333, "angle": -40.81, "curve": [0.343, 0.36, 0.686, 0.73]}, {"time": 1, "angle": -9.29, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 1.2333, "angle": 8.91, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1.7333, "angle": -40.81, "curve": [0.343, 0.36, 0.686, 0.73]}, {"time": 2, "angle": -9.29, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 2.2333, "angle": 8.91, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2.7333, "angle": -40.81, "curve": [0.343, 0.36, 0.686, 0.73]}, {"time": 3, "angle": -9.29}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "canhsau4": {"rotate": [{"time": 0, "angle": -24.45, "curve": [0.382, 0.56, 0.74, 1]}, {"time": 0.3667, "angle": 8.91, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.8667, "angle": -40.81, "curve": [0.336, 0.34, 0.671, 0.68]}, {"time": 1, "angle": -24.45, "curve": [0.382, 0.56, 0.74, 1]}, {"time": 1.3667, "angle": 8.91, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1.8667, "angle": -40.81, "curve": [0.336, 0.34, 0.671, 0.68]}, {"time": 2, "angle": -24.45, "curve": [0.382, 0.56, 0.74, 1]}, {"time": 2.3667, "angle": 8.91, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2.8667, "angle": -40.81, "curve": [0.336, 0.34, 0.671, 0.68]}, {"time": 3, "angle": -24.45}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "canhsau": {"rotate": [{"time": 0, "angle": 8.91, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.5, "angle": -40.81, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1, "angle": 8.91, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1.5, "angle": -40.81, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2, "angle": 8.91, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2.5, "angle": -40.81, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 3, "angle": 8.91}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than13": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.5, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1, "angle": 0}, {"time": 1.5, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2, "angle": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2.5, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "canhtruoc8": {"rotate": [{"time": 0, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 19.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -5.43}, {"time": 1.5, "angle": 19.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "angle": 19.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": -5.43}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "canhtruoc13": {"rotate": [{"time": 0, "angle": 1.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 1.67}, {"time": 1.1333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 1.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": 1.67}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "canhtruoc14": {"rotate": [{"time": 0, "angle": 8.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 8.77}, {"time": 1.2333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 8.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.7333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": 8.77}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "canhtruoc15": {"rotate": [{"time": 0, "angle": 15.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 15.88}, {"time": 1.3667, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 15.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3667, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8667, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": 15.88}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "canhtruoc16": {"rotate": [{"time": 0, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 22.98}, {"time": 1.5, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": 22.98}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "canhtruoc17": {"rotate": [{"time": 0, "angle": 15.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 15.88}, {"time": 1.1333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 15.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": 15.88}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than17": {"rotate": [{"time": 0, "angle": -4.92, "curve": [0.343, 0.37, 0.679, 0.71]}, {"time": 0.1333, "angle": -2.69, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 0.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": -7.35, "curve": [0.336, 0.34, 0.671, 0.68]}, {"time": 1, "angle": -4.92, "curve": [0.343, 0.37, 0.679, 0.71]}, {"time": 1.1333, "angle": -2.69, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 1.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "angle": -7.35, "curve": [0.336, 0.34, 0.671, 0.68]}, {"time": 2, "angle": -4.92, "curve": [0.343, 0.37, 0.679, 0.71]}, {"time": 2.1333, "angle": -2.69, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 2.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8667, "angle": -7.35, "curve": [0.336, 0.34, 0.671, 0.68]}, {"time": 3, "angle": -4.92}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than16": {"rotate": [{"time": 0, "angle": -2.63, "curve": [0.352, 0.41, 0.688, 0.76]}, {"time": 0.1333, "angle": -0.87, "curve": [0.364, 0.64, 0.701, 1]}, {"time": 0.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -7.35, "curve": [0.343, 0.36, 0.686, 0.73]}, {"time": 1, "angle": -2.63, "curve": [0.352, 0.41, 0.688, 0.76]}, {"time": 1.1333, "angle": -0.87, "curve": [0.364, 0.64, 0.701, 1]}, {"time": 1.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": -7.35, "curve": [0.343, 0.36, 0.686, 0.73]}, {"time": 2, "angle": -2.63, "curve": [0.352, 0.41, 0.688, 0.76]}, {"time": 2.1333, "angle": -0.87, "curve": [0.364, 0.64, 0.701, 1]}, {"time": 2.2333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.7333, "angle": -7.35, "curve": [0.343, 0.36, 0.686, 0.73]}, {"time": 3, "angle": -2.63}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than15": {"rotate": [{"time": 0, "angle": -0.87, "curve": [0.364, 0.64, 0.701, 1]}, {"time": 0.1333, "angle": 0}, {"time": 0.6333, "angle": -3.53, "curve": [0.356, 0.41, 0.711, 0.82]}, {"time": 1, "angle": -0.87, "curve": [0.364, 0.64, 0.701, 1]}, {"time": 1.1333, "angle": 0}, {"time": 1.6333, "angle": -3.53, "curve": [0.356, 0.41, 0.711, 0.82]}, {"time": 2, "angle": -0.87, "curve": [0.364, 0.64, 0.701, 1]}, {"time": 2.1333, "angle": 0}, {"time": 2.6333, "angle": -3.53, "curve": [0.356, 0.41, 0.711, 0.82]}, {"time": 3, "angle": -0.87}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than26": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.5, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1, "angle": 0}, {"time": 1.5, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2, "angle": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2.5, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than25": {"rotate": [{"time": 0, "angle": 1.37, "curve": [0.325, 0.31, 0.663, 0.65]}, {"time": 0.1667, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 1.37, "curve": [0.325, 0.31, 0.663, 0.65]}, {"time": 1.1667, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 1.37, "curve": [0.325, 0.31, 0.663, 0.65]}, {"time": 2.1667, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": 1.37}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than24": {"rotate": [{"time": 0, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": 2.43}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than23": {"rotate": [{"time": 0, "angle": 1.37, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 0.8333, "angle": 2.43, "curve": [0.337, 0.35, 0.675, 0.69]}, {"time": 1, "angle": 1.37, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 1.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 2.43, "curve": [0.337, 0.35, 0.675, 0.69]}, {"time": 2, "angle": 1.37, "curve": [0.382, 0.57, 0.735, 1]}, {"time": 2.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "angle": 2.43, "curve": [0.337, 0.35, 0.675, 0.69]}, {"time": 3, "angle": 1.37}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than22": {"rotate": [{"time": 0, "angle": 0.46, "curve": [0.371, 0.63, 0.71, 1]}, {"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 2.43, "curve": [0.351, 0.39, 0.701, 0.78]}, {"time": 1, "angle": 0.46, "curve": [0.371, 0.63, 0.71, 1]}, {"time": 1.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 2.43, "curve": [0.351, 0.39, 0.701, 0.78]}, {"time": 2, "angle": 0.46, "curve": [0.371, 0.63, 0.71, 1]}, {"time": 2.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "angle": 2.43, "curve": [0.351, 0.39, 0.701, 0.78]}, {"time": 3, "angle": 0.46}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than21": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.5, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1, "angle": 0}, {"time": 1.5, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2, "angle": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2.5, "angle": 2.43, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "canhtruoc22": {"rotate": [{"time": 0, "angle": 15.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 15.88}, {"time": 1.1333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 15.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": 15.88}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "canhtruoc21": {"rotate": [{"time": 0, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 22.98}, {"time": 1.5, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": 22.98}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "canhtruoc20": {"rotate": [{"time": 0, "angle": 15.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 15.88}, {"time": 1.3667, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 15.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3667, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8667, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": 15.88}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "canhtruoc19": {"rotate": [{"time": 0, "angle": 8.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 8.77}, {"time": 1.2333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 8.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.7333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": 8.77}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "canhtruoc18": {"rotate": [{"time": 0, "angle": 1.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 1.67}, {"time": 1.1333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 1.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1333, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6333, "angle": 22.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": 1.67}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "canhtruoc9": {"rotate": [{"time": 0, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 19.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -5.43}, {"time": 1.5, "angle": 19.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -5.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "angle": 19.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": -5.43}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than14": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": -0.54, "curve": [0.333, 0.33, 0.758, 1]}, {"time": 0.3667, "x": 0, "y": -17.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "x": 0, "y": 3.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": -0.54, "curve": [0.333, 0.33, 0.758, 1]}, {"time": 1.3667, "x": 0, "y": -17.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "x": 0, "y": 3.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": -0.54, "curve": [0.333, 0.33, 0.758, 1]}, {"time": 2.3667, "x": 0, "y": -17.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8667, "x": 0, "y": 3.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 0, "y": -0.54}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "phuonghoang": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4333, "x": 1, "y": 1}, {"time": 1.5333, "x": 0.935, "y": 0.935}, {"time": 1.6333, "x": 1, "y": 1}, {"time": 1.7333, "x": 0.935, "y": 0.935}, {"time": 1.8333, "x": 1, "y": 1}, {"time": 1.9333, "x": 0.935, "y": 0.935}, {"time": 2.0333, "x": 1, "y": 1}, {"time": 2.1333, "x": 0.935, "y": 0.935}, {"time": 2.2333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "73": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "trieu3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "bone3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "x": 1.06, "y": 1.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1.06, "y": 1.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 1.06, "y": 1.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": 1.06, "y": 1.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "bone2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than6": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than7": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than18": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than19": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "than20": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}}, "deform": {"default": {"canhsau": {"canhsau": [{"time": 0, "curve": "stepped"}, {"time": 3}]}, "canhsau2": {"canhsau2": [{"time": 0, "curve": "stepped"}, {"time": 3}]}, "canhtruoc": {"canhtruoc": [{"time": 0, "curve": "stepped"}, {"time": 3}]}, "canhtruoc2": {"canhtruoc2": [{"time": 0, "curve": "stepped"}, {"time": 3}]}, "than": {"than": [{"time": 0, "curve": "stepped"}, {"time": 3}]}, "than2": {"than2": [{"time": 0, "curve": "stepped"}, {"time": 3}]}}}, "drawOrder": [{"time": 0}, {"time": 3}]}}}