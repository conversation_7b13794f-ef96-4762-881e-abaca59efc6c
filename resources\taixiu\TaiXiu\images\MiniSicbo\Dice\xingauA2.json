{"skeleton": {"hash": "GOQISrDE3NtzHt4zGRd4BTWBeq4", "spine": "3.7.93", "width": 322.37, "height": 372.36, "fps": 60, "images": "./images/", "audio": "/Users/<USER>/Downloads/XX spine 3.7.93"}, "bones": [{"name": "root", "x": -36.99, "y": -38.89}, {"name": "no", "parent": "root", "rotation": 30.97, "x": 49.23, "y": 353.64, "scaleX": 1.401, "scaleY": 1.401, "shearX": 0.9, "shearY": 22.94}, {"name": "no2", "parent": "root", "rotation": 30.97, "x": 60.61, "y": 385.73, "scaleX": 1.401, "scaleY": 1.401, "shearX": 0.9, "shearY": 22.94}, {"name": "effect", "parent": "root", "rotation": -59.74, "x": 64.38, "y": 819.41, "scaleX": 1.182, "scaleY": 1.182, "shearX": 7.96, "shearY": -128.48}, {"name": "bone4", "parent": "effect", "scaleX": 1.313, "scaleY": 1.313}, {"name": "effect2", "parent": "root", "rotation": 63.46, "x": 64.38, "y": 819.41, "scaleX": 1.182, "scaleY": 1.182, "shearX": 7.96, "shearY": -128.48}, {"name": "bone5", "parent": "effect2", "scaleX": 1.551, "scaleY": 1.551}, {"name": "effect3", "parent": "root", "rotation": 18.84, "x": 180.16, "y": 725.32, "scaleX": 1.182, "scaleY": 1.182, "shearX": 7.96, "shearY": -128.48}, {"name": "bone6", "parent": "effect3", "rotation": -145.92, "x": 96.44, "y": -203.5, "scaleX": 1.313, "scaleY": 1.313}, {"name": "bone7", "parent": "root", "x": 41.37, "y": 123.32}, {"name": "control5", "parent": "bone7", "x": -5.52, "y": 110.62}, {"name": "control13", "parent": "control5", "x": 2.58, "y": 1.03}, {"name": "giua10", "parent": "control13", "x": 6.51, "y": -111.52}, {"name": "mat tren10", "parent": "control13", "x": 5.8, "y": 195.19, "color": "00e3ffff"}, {"name": "ben trai10", "parent": "control13", "x": -155.77, "y": -14.09, "color": "f0ff00ff"}, {"name": "ben phai10", "parent": "control13", "x": 162.89, "y": -5.79, "color": "ff0000ff"}, {"name": "mat duoi10", "parent": "control13", "x": 5.52, "y": -205.31, "color": "00b827ff"}], "slots": [{"name": "Rectangle 1 copy 21", "bone": "mat duoi10", "attachment": "Rectangle 1 copy 3"}, {"name": "Rectangle 1 copy 20", "bone": "mat tren10", "attachment": "mat tren 1"}, {"name": "10", "bone": "giua10", "attachment": "1"}], "skins": {"default": {"10": {"1": {"type": "mesh", "uvs": [1, 1, 0.49791, 1, 0, 1, 0, 0, 0.49984, 0, 1, 0], "triangles": [1, 2, 3, 1, 4, 0, 4, 1, 3, 4, 5, 0], "vertices": [1, 15, -3.04, -60.77, 1, 1, 12, -1.94, -63.41, 1, 1, 14, 2.49, -41.95, 1, 1, 14, -0.1, 103.29, 1, 1, 12, -1.06, 80.99, 1, 1, 15, 2.14, 89.52, 1], "hull": 6, "edges": [4, 6, 0, 10, 6, 8, 8, 10, 0, 2, 2, 4, 8, 2], "width": 458, "height": 230}, "2": {"type": "<PERSON><PERSON><PERSON>", "parent": "1", "width": 458, "height": 230}, "3": {"type": "<PERSON><PERSON><PERSON>", "parent": "1", "width": 458, "height": 230}, "4": {"type": "<PERSON><PERSON><PERSON>", "parent": "1", "width": 458, "height": 230}}, "Rectangle 1 copy 20": {"mat tren 1": {"type": "<PERSON><PERSON><PERSON>", "parent": "mat tren 5", "width": 324, "height": 195}, "mat tren 2": {"type": "<PERSON><PERSON><PERSON>", "parent": "mat tren 5", "width": 324, "height": 195}, "mat tren 3": {"type": "<PERSON><PERSON><PERSON>", "parent": "mat tren 5", "width": 324, "height": 195}, "mat tren 4": {"type": "<PERSON><PERSON><PERSON>", "parent": "mat tren 5", "width": 324, "height": 195}, "mat tren 5": {"type": "mesh", "uvs": [0.99846, 0.49991, 0.5048, 1, 0.00154, 0.50359, 0.49815, 0], "triangles": [2, 3, 1, 1, 3, 0], "vertices": [1, 15, 3, 87.54, 1, 1, 12, 1.07, 77.24, 1, 1, 14, -0.71, 100.49, 1, 1, 13, -0.38, 2.24, 1], "hull": 4, "edges": [2, 4, 4, 6, 6, 0, 0, 2], "width": 324, "height": 195}, "mat tren 6": {"type": "<PERSON><PERSON><PERSON>", "parent": "mat tren 5", "width": 324, "height": 195}}, "Rectangle 1 copy 21": {"Rectangle 1 copy 3": {"type": "mesh", "uvs": [1, 0.50463, 0.49662, 1, 0, 0.50883, 0.49914, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 15, -0.92, -59.26, 1, 1, 12, -3.31, -62.05, 1, 1, 14, -0.2, -40.32, 1, 1, 16, -1.5, 241.04, 1], "hull": 4, "edges": [4, 6, 6, 0, 0, 2, 2, 4], "width": 324, "height": 195}, "mat duoi 2": {"type": "<PERSON><PERSON><PERSON>", "parent": "Rectangle 1 copy 3", "width": 324, "height": 195}}}}, "animations": {"1": {"bones": {"giua10": {"scale": [{"time": 0, "x": 1, "y": 0.312}]}, "mat tren10": {"translate": [{"time": 0, "x": -3.41, "y": 20.46}], "scale": [{"time": 0, "x": 1, "y": 0.312}]}, "mat duoi10": {"scale": [{"time": 0, "x": 1, "y": 0.312}]}, "ben trai10": {"translate": [{"time": 0, "x": 0, "y": 47.74}], "scale": [{"time": 0, "x": 1, "y": 0.312}]}, "ben phai10": {"translate": [{"time": 0, "x": 6.82, "y": 37.51}], "scale": [{"time": 0, "x": 1, "y": 0.312}]}}}, "2": {"slots": {"Rectangle 1 copy 20": {"attachment": [{"time": 0, "name": "mat tren 2"}]}}, "bones": {"giua10": {"scale": [{"time": 0, "x": 1, "y": 0.312}]}, "mat tren10": {"translate": [{"time": 0, "x": -3.41, "y": 20.46}], "scale": [{"time": 0, "x": 1, "y": 0.312}]}, "mat duoi10": {"scale": [{"time": 0, "x": 1, "y": 0.312}]}, "ben trai10": {"translate": [{"time": 0, "x": 0, "y": 47.74}], "scale": [{"time": 0, "x": 1, "y": 0.312}]}, "ben phai10": {"translate": [{"time": 0, "x": 6.82, "y": 37.51}], "scale": [{"time": 0, "x": 1, "y": 0.312}]}}}, "3": {"slots": {"Rectangle 1 copy 20": {"attachment": [{"time": 0, "name": "mat tren 3"}]}}, "bones": {"giua10": {"scale": [{"time": 0, "x": 1, "y": 0.312}]}, "mat tren10": {"translate": [{"time": 0, "x": -3.41, "y": 20.46}], "scale": [{"time": 0, "x": 1, "y": 0.312}]}, "mat duoi10": {"scale": [{"time": 0, "x": 1, "y": 0.312}]}, "ben trai10": {"translate": [{"time": 0, "x": 0, "y": 47.74}], "scale": [{"time": 0, "x": 1, "y": 0.312}]}, "ben phai10": {"translate": [{"time": 0, "x": 6.82, "y": 37.51}], "scale": [{"time": 0, "x": 1, "y": 0.312}]}}}, "4": {"slots": {"Rectangle 1 copy 20": {"attachment": [{"time": 0, "name": "mat tren 4"}]}}, "bones": {"giua10": {"scale": [{"time": 0, "x": 1, "y": 0.312}]}, "mat tren10": {"translate": [{"time": 0, "x": -3.41, "y": 20.46}], "scale": [{"time": 0, "x": 1, "y": 0.312}]}, "mat duoi10": {"scale": [{"time": 0, "x": 1, "y": 0.312}]}, "ben trai10": {"translate": [{"time": 0, "x": 0, "y": 47.74}], "scale": [{"time": 0, "x": 1, "y": 0.312}]}, "ben phai10": {"translate": [{"time": 0, "x": 6.82, "y": 37.51}], "scale": [{"time": 0, "x": 1, "y": 0.312}]}}}, "5": {"slots": {"Rectangle 1 copy 20": {"attachment": [{"time": 0, "name": "mat tren 5"}]}}, "bones": {"giua10": {"scale": [{"time": 0, "x": 1, "y": 0.312}]}, "mat tren10": {"translate": [{"time": 0, "x": -3.41, "y": 20.46}], "scale": [{"time": 0, "x": 1, "y": 0.312}]}, "mat duoi10": {"scale": [{"time": 0, "x": 1, "y": 0.312}]}, "ben trai10": {"translate": [{"time": 0, "x": 0, "y": 47.74}], "scale": [{"time": 0, "x": 1, "y": 0.312}]}, "ben phai10": {"translate": [{"time": 0, "x": 6.82, "y": 37.51}], "scale": [{"time": 0, "x": 1, "y": 0.312}]}}}, "6": {"slots": {"Rectangle 1 copy 20": {"attachment": [{"time": 0, "name": "mat tren 6"}]}}, "bones": {"giua10": {"scale": [{"time": 0, "x": 1, "y": 0.312}]}, "mat tren10": {"translate": [{"time": 0, "x": -3.41, "y": 20.46}], "scale": [{"time": 0, "x": 1, "y": 0.312}]}, "mat duoi10": {"scale": [{"time": 0, "x": 1, "y": 0.312}]}, "ben trai10": {"translate": [{"time": 0, "x": 0, "y": 47.74}], "scale": [{"time": 0, "x": 1, "y": 0.312}]}, "ben phai10": {"translate": [{"time": 0, "x": 6.82, "y": 37.51}], "scale": [{"time": 0, "x": 1, "y": 0.312}]}}}, "xi ngau bay 1": {"slots": {"10": {"attachment": [{"time": 0.0667, "name": "2"}, {"time": 0.1667, "name": "3"}, {"time": 0.2833, "name": "4"}, {"time": 0.4, "name": "1"}, {"time": 0.6667, "name": "3"}, {"time": 0.9333, "name": "1"}, {"time": 1.0833, "name": "2"}, {"time": 1.15, "name": "3"}, {"time": 1.2167, "name": "4"}, {"time": 1.2833, "name": "1"}, {"time": 1.3333, "name": "3"}, {"time": 1.6, "name": "1"}, {"time": 1.7333, "name": "2"}, {"time": 1.7667, "name": "3"}, {"time": 1.8167, "name": "4"}, {"time": 1.85, "name": "1"}, {"time": 1.9, "name": "2"}, {"time": 1.9833, "name": "3"}, {"time": 2.05, "name": "4"}, {"time": 2.15, "name": "1"}]}, "Rectangle 1 copy 20": {"attachment": [{"time": 1.65, "name": "mat tren 5"}, {"time": 1.6667, "name": "mat tren 1"}, {"time": 1.85, "name": "mat tren 2"}, {"time": 1.9, "name": "mat tren 1"}, {"time": 1.9333, "name": "mat tren 5"}, {"time": 2.2333, "name": "mat tren 1"}]}, "Rectangle 1 copy 21": {"attachment": [{"time": 1.05, "name": "mat duoi 2"}, {"time": 1.5, "name": "Rectangle 1 copy 3"}, {"time": 1.6667, "name": "mat duoi 2"}]}}, "bones": {"giua10": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4167, "angle": -6.49}, {"time": 0.4333, "angle": -10.43}, {"time": 0.45, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.0667, "x": 130.24, "y": 47.4}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.1667, "x": 130.24, "y": 47.4}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.2833, "x": 130.24, "y": 47.4}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.4, "x": 130.24, "y": 47.4}, {"time": 0.45, "x": 0, "y": 0}, {"time": 0.5167, "x": 0, "y": 96.49}, {"time": 0.6, "x": 0, "y": 162.37}, {"time": 0.65, "x": 0, "y": 197.23}, {"time": 0.6667, "x": 0, "y": 231.18}, {"time": 0.7, "x": 0, "y": 162.37}, {"time": 0.75, "x": 0, "y": 96.49}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.8333, "x": 0, "y": 96.49}, {"time": 0.8833, "x": 0, "y": 162.37}, {"time": 0.9167, "x": 0, "y": 197.23}, {"time": 0.9333, "x": 0, "y": 231.18}, {"time": 0.9667, "x": 0, "y": 162.37}, {"time": 1, "x": 0, "y": 96.49}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": 130.24, "y": 47.4}, {"time": 1.1167, "x": 0, "y": 0}, {"time": 1.1333, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 1.15, "x": 130.24, "y": 47.4}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.2, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 1.2167, "x": 130.24, "y": 47.4}, {"time": 1.25, "x": 0, "y": 0}, {"time": 1.2667, "x": -126.68, "y": 22.81, "curve": "stepped"}, {"time": 1.2833, "x": 122.89, "y": 11.33, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.3, "x": 0.96, "y": 212.74, "curve": [0.174, 0.55, 0.493, 1]}, {"time": 1.3333, "x": 0, "y": 231.18}, {"time": 1.3667, "x": 0, "y": 162.37}, {"time": 1.4167, "x": 0, "y": 96.49}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.5, "x": 0, "y": 96.49}, {"time": 1.55, "x": 0, "y": 162.37}, {"time": 1.5833, "x": 0, "y": 197.23}, {"time": 1.6, "x": 0, "y": 231.18}, {"time": 1.6333, "x": 0, "y": 162.37}, {"time": 1.6667, "x": 0, "y": 96.49}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": 130.24, "y": 47.4}, {"time": 1.75, "x": 0, "y": 0}, {"time": 1.7667, "x": 130.24, "y": 47.4}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": 130.24, "y": 47.4}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": 130.24, "y": 47.4, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.8667, "x": -85.63, "y": 48.42, "curve": [0.264, 0.62, 0.595, 1]}, {"time": 1.9, "x": 130.24, "y": 47.4}, {"time": 1.9167, "x": 0, "y": 0}, {"time": 1.9333, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 1.9833, "x": 130.24, "y": 47.4}, {"time": 2, "x": 0, "y": 0}, {"time": 2.05, "x": 130.24, "y": 47.4}, {"time": 2.1, "x": 0, "y": 0}, {"time": 2.1333, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 2.15, "x": 87.25, "y": 41.76, "curve": [0.072, 0.47, 0.371, 1]}, {"time": 2.2833, "x": 0, "y": 0}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 1.232}, {"time": 0.55, "x": 1, "y": 1.374}, {"time": 0.5667, "x": 1, "y": 1.478}, {"time": 0.5833, "x": 1, "y": 1.18}, {"time": 0.6, "x": 1, "y": 1.107}, {"time": 0.6167, "x": 1, "y": 1.066}, {"time": 0.6333, "x": 1, "y": 0.83}, {"time": 0.65, "x": 1, "y": 0.666}, {"time": 0.6667, "x": 1, "y": 0.102}, {"time": 0.7, "x": 1, "y": 0.969}, {"time": 0.7167, "x": 1, "y": 1.357}, {"time": 0.7333, "x": 1, "y": 1.37}, {"time": 0.75, "x": 1, "y": 1.384}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 1.306}, {"time": 0.85, "x": 1, "y": 1.38}, {"time": 0.8667, "x": 1, "y": 1.385}, {"time": 0.8833, "x": 1, "y": 1.062}, {"time": 0.9, "x": 1, "y": 0.879}, {"time": 0.9167, "x": 1, "y": 0.509}, {"time": 0.9333, "x": 1, "y": 0.102}, {"time": 0.9667, "x": 1, "y": 1.005}, {"time": 0.9833, "x": 1, "y": 1.431}, {"time": 1, "x": 1, "y": 1.362}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.2833, "x": 1, "y": 0.31}, {"time": 1.3, "x": 1, "y": 0.406}, {"time": 1.3333, "x": 1, "y": 0.398}, {"time": 1.3667, "x": 1, "y": 1.169}, {"time": 1.3833, "x": 1, "y": 1.463}, {"time": 1.4, "x": 1, "y": 1.474}, {"time": 1.4167, "x": 1, "y": 1.232}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 1.232}, {"time": 1.5167, "x": 1, "y": 1.44}, {"time": 1.5333, "x": 1, "y": 1.443}, {"time": 1.55, "x": 1, "y": 1.104}, {"time": 1.5833, "x": 1, "y": 0.509}, {"time": 1.6, "x": 1, "y": 0.102}, {"time": 1.6333, "x": 1, "y": 0.913}, {"time": 1.65, "x": 1, "y": 1.431}, {"time": 1.6667, "x": 1, "y": 1.362}, {"time": 1.7167, "x": 1, "y": 1.203}, {"time": 1.7333, "x": 1, "y": 1.2}, {"time": 1.85, "x": 1, "y": 1.184}, {"time": 1.8667, "x": 1, "y": 1.203}, {"time": 1.9, "x": 1, "y": 1.199}, {"time": 1.9333, "x": 1, "y": 1.176}, {"time": 2, "x": 1, "y": 0.465, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.465}, {"time": 2.4, "x": 1, "y": 0.312}]}, "mat tren10": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.0667, "x": -115.92, "y": -23.14}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.1667, "x": -115.92, "y": -23.14}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.2833, "x": -115.92, "y": -23.14}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.4, "x": -115.92, "y": -23.14}, {"time": 0.45, "x": -1.86, "y": 0}, {"time": 0.4833, "x": -1.86, "y": -80.05}, {"time": 0.5, "x": -1.86, "y": -109.69}, {"time": 0.5167, "x": -1.86, "y": -121.4}, {"time": 0.65, "x": -1.86, "y": -101.97, "curve": "stepped"}, {"time": 0.6667, "x": -1.86, "y": -101.97}, {"time": 0.75, "x": -1.86, "y": -121.4}, {"time": 0.7667, "x": -1.86, "y": -79.61}, {"time": 0.7833, "x": -1.86, "y": 0}, {"time": 0.8167, "x": -1.86, "y": -86.04}, {"time": 0.8333, "x": -1.86, "y": -121.4}, {"time": 0.9167, "x": -1.86, "y": -101.97, "curve": "stepped"}, {"time": 0.9333, "x": -1.86, "y": -101.97}, {"time": 1, "x": -1.86, "y": -121.4}, {"time": 1.0167, "x": -1.12, "y": -93.49}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": -107.63, "y": -43.68}, {"time": 1.1167, "x": 2.04, "y": -24.06}, {"time": 1.1333, "x": 116.49, "y": -55.7, "curve": "stepped"}, {"time": 1.15, "x": -103.59, "y": -28.59}, {"time": 1.1667, "x": -12.33, "y": -37.73}, {"time": 1.1833, "x": 62.07, "y": -66.2}, {"time": 1.2, "x": 121.71, "y": -73.43, "curve": "stepped"}, {"time": 1.2167, "x": -116.33, "y": -67.24}, {"time": 1.2333, "x": -63.06, "y": -55.74}, {"time": 1.25, "x": -9.44, "y": -49.64}, {"time": 1.2667, "x": 110.15, "y": -102.6, "curve": "stepped"}, {"time": 1.2833, "x": -100.12, "y": -98.8, "curve": [0.049, 0.46, 0.343, 1]}, {"time": 1.3333, "x": -1.86, "y": -101.97}, {"time": 1.4167, "x": -1.86, "y": -121.4}, {"time": 1.4333, "x": -1.86, "y": -79.61}, {"time": 1.45, "x": -1.86, "y": 0}, {"time": 1.4833, "x": -1.86, "y": -86.04}, {"time": 1.5, "x": -1.86, "y": -121.4}, {"time": 1.5833, "x": -1.86, "y": -101.97, "curve": "stepped"}, {"time": 1.6, "x": -1.86, "y": -101.97}, {"time": 1.6667, "x": -1.86, "y": -121.4}, {"time": 1.6833, "x": -1.12, "y": -80.61}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": -115.92, "y": -9.43}, {"time": 1.75, "x": 0, "y": 0}, {"time": 1.7667, "x": -115.92, "y": -14}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": -115.92, "y": -14}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": -115.92, "y": -20.84, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.8667, "x": 81.6, "y": -15.26, "curve": [0.264, 0.62, 0.595, 1]}, {"time": 1.9, "x": -115.92, "y": -23.14}, {"time": 1.9167, "x": 0, "y": 0}, {"time": 1.9333, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 1.9833, "x": -115.92, "y": -23.14}, {"time": 2, "x": 0, "y": 0}, {"time": 2.05, "x": -115.92, "y": -23.14}, {"time": 2.1, "x": 0, "y": 0}, {"time": 2.1333, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 2.15, "x": -115.92, "y": -23.14, "curve": [0.184, 0.33, 0.514, 0.66]}, {"time": 2.2, "x": -55.34, "y": -12.43, "curve": [0.251, 0.34, 0.582, 0.68]}, {"time": 2.2167, "x": -32.92, "y": -16.99, "curve": [0.168, 0.55, 0.487, 1]}, {"time": 2.2833, "x": -10.23, "y": 10.23}, {"time": 2.4, "x": -3.41, "y": 20.46}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 0.75, "x": 1, "y": 1.466}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1.466}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.4167, "x": 1, "y": 1.466}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1.466}, {"time": 1.7167, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 1, "y": 1}, {"time": 2, "x": 1, "y": 0.401, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.401}, {"time": 2.4, "x": 1, "y": 0.312}]}, "mat duoi10": {"translate": [{"time": 0.45, "x": 0, "y": 0}, {"time": 0.65, "x": 0, "y": -107.61}, {"time": 0.6667, "x": 0, "y": -131.1}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.9, "x": 0, "y": -100.71}, {"time": 0.9167, "x": 0, "y": -107.61, "curve": "stepped"}, {"time": 0.9333, "x": 0, "y": -107.61}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.3, "x": 69.53, "y": -156.51}, {"time": 1.3333, "x": 0, "y": -129.56}, {"time": 1.35, "x": -5.74, "y": -137.59}, {"time": 1.3667, "x": -4.79, "y": -96.12}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.5833, "x": 0, "y": -107.61}, {"time": 1.6, "x": 0, "y": -123.27}, {"time": 1.7167, "x": 0, "y": 0}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.65, "x": 1, "y": 0.933}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.9167, "x": 1, "y": 0.933}, {"time": 1.05, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5833, "x": 1, "y": 0.933}, {"time": 1.7167, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 1, "y": 1}, {"time": 2, "x": 1, "y": 0.401, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.401}, {"time": 2.4, "x": 1, "y": 0.312}]}, "bone4": {"rotate": [{"time": 0.1667, "angle": 0}, {"time": 0.3167, "angle": 120}, {"time": 0.4667, "angle": -120}, {"time": 0.6, "angle": 0}, {"time": 0.75, "angle": 120}, {"time": 0.9, "angle": -120}, {"time": 1.0333, "angle": 0, "curve": "stepped"}, {"time": 1.25, "angle": 0}, {"time": 1.3, "angle": 120}, {"time": 1.45, "angle": -120}, {"time": 1.5833, "angle": 0}, {"time": 1.7333, "angle": 120}, {"time": 1.8167, "angle": -120}, {"time": 1.9, "angle": 0}]}, "bone5": {"rotate": [{"time": 0.3333, "angle": 0}, {"time": 0.4833, "angle": 120}, {"time": 0.6333, "angle": -120}, {"time": 0.7833, "angle": 0}, {"time": 0.9167, "angle": 120}, {"time": 1.0667, "angle": -120}, {"time": 1.2167, "angle": 0, "curve": "stepped"}, {"time": 1.4167, "angle": 0}, {"time": 1.4667, "angle": 120}, {"time": 1.5, "angle": 0}, {"time": 1.6167, "angle": -120}, {"time": 1.75, "angle": 0}, {"time": 1.8333, "angle": 120}, {"time": 1.9167, "angle": -120}, {"time": 2.0167, "angle": 0}]}, "bone6": {"rotate": [{"time": 0.2667, "angle": 0}, {"time": 0.4, "angle": 120}, {"time": 0.55, "angle": -120}, {"time": 0.7, "angle": 0}, {"time": 0.8333, "angle": 120}, {"time": 0.9833, "angle": -120}, {"time": 1.1333, "angle": 0, "curve": "stepped"}, {"time": 1.35, "angle": 0}, {"time": 1.3833, "angle": 120}, {"time": 1.4167, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0}, {"time": 1.5333, "angle": -120}, {"time": 1.6833, "angle": 0}, {"time": 1.7667, "angle": 120}, {"time": 1.8667, "angle": -120}, {"time": 1.9833, "angle": 0}]}, "no": {"scale": [{"time": 0, "x": 0.605, "y": 0.605}, {"time": 0.0833, "x": 1, "y": 1}, {"time": 1.9833, "x": 1.052, "y": 1.052}, {"time": 2.1, "x": 1.87, "y": 1.87}, {"time": 2.3167, "x": 1.052, "y": 1.052}, {"time": 2.4, "x": 0.605, "y": 0.605}]}, "no2": {"scale": [{"time": 2.1333, "x": 1, "y": 1}, {"time": 2.2833, "x": 4.931, "y": 4.931}, {"time": 2.4, "x": 1, "y": 1}]}, "ben trai10": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4167, "angle": -5.7}, {"time": 0.45, "angle": -1.2, "curve": "stepped"}, {"time": 1.85, "angle": -1.2}, {"time": 2.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.0667, "x": 34.74, "y": -72.38}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.1667, "x": 34.74, "y": -73.59}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.2833, "x": 34.74, "y": -69.94}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.4, "x": 34.74, "y": -69.94, "curve": [0.049, 0.46, 0.343, 1]}, {"time": 0.45, "x": 0, "y": 0}, {"time": 0.6, "x": 0.3, "y": 14.43, "curve": "stepped"}, {"time": 0.7, "x": 0.3, "y": 14.43}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.8833, "x": 0.3, "y": 14.43}, {"time": 0.9167, "x": 0.3, "y": 10.66}, {"time": 0.9333, "x": 0.3, "y": 29.85}, {"time": 0.9667, "x": 0.3, "y": 14.43}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": 46.73, "y": -72.38}, {"time": 1.1167, "x": 0, "y": 0}, {"time": 1.1333, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 1.15, "x": 34.74, "y": -73.59}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.2, "x": 34.47, "y": 95.69, "curve": "stepped"}, {"time": 1.2167, "x": 34.74, "y": -69.94}, {"time": 1.25, "x": -12.45, "y": 13.15}, {"time": 1.2667, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 1.2833, "x": 25.07, "y": -81.99, "curve": [0.078, 0.35, 0.388, 0.74]}, {"time": 1.3333, "x": 2.37, "y": 28.22, "curve": [0.285, 0.42, 0.618, 0.76]}, {"time": 1.35, "x": 0.74, "y": 0.97, "curve": [0.294, 0.64, 0.626, 1]}, {"time": 1.3667, "x": 0.3, "y": 14.43}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.55, "x": 0.3, "y": 14.43}, {"time": 1.6, "x": 0.3, "y": 24.07}, {"time": 1.6333, "x": 0.3, "y": 14.43}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": 34.74, "y": -72.38}, {"time": 1.75, "x": 0, "y": -12.18}, {"time": 1.7667, "x": 34.74, "y": -73.59}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": 34.74, "y": -69.94}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": 34.74, "y": -69.94, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.8667, "x": 35.32, "y": 55.99, "curve": [0.174, 0.55, 0.493, 1]}, {"time": 1.9, "x": 34.74, "y": -72.38}, {"time": 1.9167, "x": 0, "y": -7.61}, {"time": 1.9333, "x": 35.53, "y": 85.59, "curve": "stepped"}, {"time": 1.9833, "x": 34.74, "y": -73.59}, {"time": 2, "x": 0, "y": 22.33}, {"time": 2.05, "x": 34.74, "y": -69.94}, {"time": 2.1, "x": 0, "y": 30.47}, {"time": 2.1333, "x": 35.53, "y": 139.21, "curve": "stepped"}, {"time": 2.15, "x": 34.74, "y": -69.94, "curve": [0.071, 0.36, 0.379, 0.77]}, {"time": 2.2167, "x": 1.92, "y": 8.74, "curve": [0.264, 0.62, 0.595, 1]}, {"time": 2.2833, "x": 0, "y": 24.85}, {"time": 2.4, "x": 0, "y": 47.74}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 0.783}, {"time": 0.55, "x": 1, "y": 0.978}, {"time": 0.5667, "x": 1, "y": 0.967}, {"time": 0.5833, "x": 1, "y": 0.842}, {"time": 0.6, "x": 1, "y": 0.892}, {"time": 0.6167, "x": 1, "y": 0.881}, {"time": 0.6333, "x": 1, "y": 0.706}, {"time": 0.65, "x": 1, "y": 0.599}, {"time": 0.6667, "x": 1, "y": 0.068}, {"time": 0.7, "x": 1, "y": 0.781}, {"time": 0.7167, "x": 1, "y": 0.95}, {"time": 0.7333, "x": 1, "y": 0.962}, {"time": 0.75, "x": 1, "y": 0.879}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 0.83}, {"time": 0.85, "x": 1, "y": 0.84}, {"time": 0.8667, "x": 1, "y": 0.846}, {"time": 0.8833, "x": 1, "y": 0.856}, {"time": 0.9, "x": 1, "y": 0.814}, {"time": 0.9167, "x": 1, "y": 0.457}, {"time": 0.9333, "x": 1, "y": 0.068}, {"time": 0.9667, "x": 1, "y": 0.81}, {"time": 0.9833, "x": 1, "y": 0.845}, {"time": 1, "x": 1, "y": 0.865}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.2833, "x": 1, "y": 0.269}, {"time": 1.3, "x": 1, "y": 0.346}, {"time": 1.3333, "x": 1, "y": 0.264}, {"time": 1.3667, "x": 1, "y": 0.942}, {"time": 1.3833, "x": 1, "y": 1.013}, {"time": 1.4, "x": 1, "y": 0.916}, {"time": 1.4167, "x": 1, "y": 0.783}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 0.783}, {"time": 1.5167, "x": 1, "y": 0.876}, {"time": 1.5333, "x": 1, "y": 0.902}, {"time": 1.55, "x": 1, "y": 0.89}, {"time": 1.5833, "x": 1, "y": 0.457}, {"time": 1.6, "x": 1, "y": 0.068}, {"time": 1.6333, "x": 1, "y": 0.735}, {"time": 1.65, "x": 1, "y": 0.845}, {"time": 1.6667, "x": 1, "y": 0.865}, {"time": 1.7167, "x": 1, "y": 1.121}, {"time": 1.7333, "x": 1, "y": 1.215}, {"time": 1.8167, "x": 1, "y": 1.138}, {"time": 1.85, "x": 1, "y": 1.161}, {"time": 1.8667, "x": 1, "y": 1.028}, {"time": 1.9, "x": 1, "y": 1.193}, {"time": 1.9333, "x": 1, "y": 1.157}, {"time": 1.9833, "x": 1, "y": 1.028}, {"time": 2, "x": 1, "y": 0.446, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.446}, {"time": 2.4, "x": 1, "y": 0.312}]}, "ben phai10": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4167, "angle": -7.27}, {"time": 0.4333, "angle": -10.19}, {"time": 0.45, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": -29.09, "y": -64.8, "curve": "stepped"}, {"time": 0.0667, "x": -33.86, "y": 90.93}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": -29.09, "y": -63.09, "curve": "stepped"}, {"time": 0.1667, "x": -33.86, "y": 90.93}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": -29.09, "y": -64.3, "curve": "stepped"}, {"time": 0.2833, "x": -33.86, "y": 90.93}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": -29.09, "y": -65.52, "curve": "stepped"}, {"time": 0.4, "x": -33.86, "y": 90.93}, {"time": 0.45, "x": 0, "y": 0}, {"time": 0.6, "x": 0, "y": 12.49, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 12.49}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.8833, "x": 0, "y": 12.49}, {"time": 0.9167, "x": 0, "y": 2.93}, {"time": 0.9333, "x": 0, "y": 19.24}, {"time": 0.9667, "x": 0, "y": 12.49}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": -33.86, "y": 90.93}, {"time": 1.1167, "x": 0, "y": 0}, {"time": 1.1333, "x": -29.09, "y": -63.09, "curve": "stepped"}, {"time": 1.15, "x": -37.95, "y": 83.63}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.2, "x": -29.09, "y": -64.3, "curve": "stepped"}, {"time": 1.2167, "x": -33.86, "y": 90.93}, {"time": 1.25, "x": 0, "y": 0}, {"time": 1.2667, "x": -32.53, "y": -89.86, "curve": "stepped"}, {"time": 1.2833, "x": -23.7, "y": 66.88}, {"time": 1.3, "x": 56.2, "y": 30}, {"time": 1.3333, "x": -2.39, "y": 20.45}, {"time": 1.35, "x": -1.19, "y": 0.21}, {"time": 1.3667, "x": 0, "y": 12.49}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.55, "x": 0, "y": 12.49, "curve": "stepped"}, {"time": 1.6333, "x": 0, "y": 12.49}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": -33.86, "y": 90.93}, {"time": 1.75, "x": 0, "y": 0}, {"time": 1.7667, "x": -33.86, "y": 90.93}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": -33.86, "y": 90.93}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": -23.7, "y": 87.55}, {"time": 1.8667, "x": -16.75, "y": -45.96}, {"time": 1.9, "x": -33.86, "y": 90.93}, {"time": 1.9167, "x": 0, "y": 0}, {"time": 1.9333, "x": -29.09, "y": -63.09, "curve": "stepped"}, {"time": 1.9833, "x": -33.86, "y": 90.93}, {"time": 2, "x": 0, "y": 33.81}, {"time": 2.05, "x": -33.86, "y": 139.34}, {"time": 2.1, "x": 0, "y": 30.47}, {"time": 2.1333, "x": -29.09, "y": -65.52, "curve": "stepped"}, {"time": 2.15, "x": -23.7, "y": 125.57}, {"time": 2.2167, "x": -2.39, "y": 37.31}, {"time": 2.2833, "x": 0, "y": 36.33}, {"time": 2.4, "x": 6.82, "y": 37.51}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 0.791}, {"time": 0.55, "x": 1, "y": 0.957}, {"time": 0.5667, "x": 1, "y": 0.934}, {"time": 0.5833, "x": 1, "y": 0.801}, {"time": 0.6, "x": 1, "y": 0.833}, {"time": 0.6167, "x": 1, "y": 0.831}, {"time": 0.6333, "x": 1, "y": 0.674}, {"time": 0.65, "x": 1, "y": 0.584}, {"time": 0.6667, "x": 1, "y": 0.08}, {"time": 0.7, "x": 1, "y": 0.729}, {"time": 0.7167, "x": 1, "y": 0.913}, {"time": 0.7333, "x": 1, "y": 0.947}, {"time": 0.75, "x": 1, "y": 0.889}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 0.839}, {"time": 0.85, "x": 1, "y": 0.828}, {"time": 0.8667, "x": 1, "y": 0.814}, {"time": 0.8833, "x": 1, "y": 0.799}, {"time": 0.9, "x": 1, "y": 0.773}, {"time": 0.9167, "x": 1, "y": 0.446}, {"time": 0.9333, "x": 1, "y": 0.08}, {"time": 0.9667, "x": 1, "y": 0.756}, {"time": 0.9833, "x": 1, "y": 0.823}, {"time": 1, "x": 1, "y": 0.875}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.2833, "x": 1, "y": 0.284}, {"time": 1.3, "x": 1, "y": 0.367}, {"time": 1.3333, "x": 1, "y": 0.311}, {"time": 1.3667, "x": 1, "y": 0.879}, {"time": 1.3833, "x": 1, "y": 0.975}, {"time": 1.4, "x": 1, "y": 0.904}, {"time": 1.4167, "x": 1, "y": 0.791}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 0.791}, {"time": 1.5167, "x": 1, "y": 0.864}, {"time": 1.5333, "x": 1, "y": 0.868}, {"time": 1.55, "x": 1, "y": 0.831}, {"time": 1.5833, "x": 1, "y": 0.446}, {"time": 1.6, "x": 1, "y": 0.08}, {"time": 1.6333, "x": 1, "y": 0.686}, {"time": 1.65, "x": 1, "y": 0.823}, {"time": 1.6667, "x": 1, "y": 0.875}, {"time": 1.7167, "x": 1, "y": 1.122}, {"time": 1.7333, "x": 1, "y": 1.124}, {"time": 1.85, "x": 1, "y": 1}, {"time": 1.8667, "x": 1, "y": 1.155}, {"time": 1.9, "x": 1, "y": 0.996}, {"time": 1.9333, "x": 1, "y": 1.11}, {"time": 1.9833, "x": 1, "y": 0.983}, {"time": 2, "x": 1, "y": 0.422, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.422}, {"time": 2.4, "x": 1, "y": 0.312}]}, "control13": {"rotate": [{"time": 0, "angle": 0, "curve": [0.019, 0.54, 0.519, 0.83]}, {"time": 0.15, "angle": 120, "curve": [0.629, 0.07, 0.987, 0.73]}, {"time": 0.3167, "angle": -120, "curve": [0.481, 0, 0.448, 0.99]}, {"time": 0.45, "angle": 0}, {"time": 0.6667, "angle": 180}, {"time": 0.7833, "angle": 0}, {"time": 0.9333, "angle": 180}, {"time": 1.05, "angle": 0}, {"time": 1.3333, "angle": 180}, {"time": 1.45, "angle": 0}, {"time": 1.6, "angle": 180}, {"time": 1.7167, "angle": 0}, {"time": 1.95, "angle": 120}, {"time": 2.2, "angle": -120}, {"time": 2.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1, "x": 0, "y": 235.01, "curve": "stepped"}, {"time": 2, "x": 0, "y": 235.01}, {"time": 2.0333, "x": 0, "y": 329.15}, {"time": 2.1333, "x": 0, "y": 0}, {"time": 2.2, "x": 0, "y": 150.21}, {"time": 2.2833, "x": 0, "y": 0}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 0.75, "x": 1, "y": 1.466}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1.466}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.4167, "x": 1, "y": 1.466}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1.466}, {"time": 1.7167, "x": 1, "y": 1}]}, "control5": {"scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.1667, "x": 1.332, "y": 1.332, "curve": "stepped"}, {"time": 2, "x": 1.332, "y": 1.332}, {"time": 2.1333, "x": 1.348, "y": 1.348}, {"time": 2.2833, "x": 1, "y": 1}]}}, "deform": {"default": {"Rectangle 1 copy 20": {"mat tren 5": [{"time": 1.6, "offset": 6, "vertices": [6.24777, -4.31001]}]}}}}, "xi ngau bay 2": {"slots": {"10": {"attachment": [{"time": 0.0667, "name": "2"}, {"time": 0.1667, "name": "3"}, {"time": 0.2833, "name": "4"}, {"time": 0.4, "name": "1"}, {"time": 0.6667, "name": "3"}, {"time": 0.9333, "name": "1"}, {"time": 1.0833, "name": "2"}, {"time": 1.15, "name": "3"}, {"time": 1.2167, "name": "4"}, {"time": 1.2833, "name": "1"}, {"time": 1.3333, "name": "3"}, {"time": 1.6, "name": "1"}, {"time": 1.7333, "name": "2"}, {"time": 1.7667, "name": "3"}, {"time": 1.8167, "name": "4"}, {"time": 1.85, "name": "1"}, {"time": 1.9, "name": "2"}, {"time": 1.9833, "name": "3"}, {"time": 2.05, "name": "4"}, {"time": 2.15, "name": "1"}]}, "Rectangle 1 copy 20": {"attachment": [{"time": 1.65, "name": "mat tren 5"}, {"time": 1.6667, "name": "mat tren 1"}, {"time": 1.85, "name": "mat tren 2"}, {"time": 1.9, "name": "mat tren 1"}, {"time": 1.9333, "name": "mat tren 5"}, {"time": 2.2333, "name": "mat tren 2"}]}, "Rectangle 1 copy 21": {"attachment": [{"time": 1.05, "name": "mat duoi 2"}, {"time": 1.5, "name": "Rectangle 1 copy 3"}, {"time": 1.6667, "name": "mat duoi 2"}]}}, "bones": {"giua10": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4167, "angle": -6.49}, {"time": 0.4333, "angle": -10.43}, {"time": 0.45, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.0667, "x": 130.24, "y": 47.4}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.1667, "x": 130.24, "y": 47.4}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.2833, "x": 130.24, "y": 47.4}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.4, "x": 130.24, "y": 47.4}, {"time": 0.45, "x": 0, "y": 0}, {"time": 0.5167, "x": 0, "y": 96.49}, {"time": 0.6, "x": 0, "y": 162.37}, {"time": 0.65, "x": 0, "y": 197.23}, {"time": 0.6667, "x": 0, "y": 231.18}, {"time": 0.7, "x": 0, "y": 162.37}, {"time": 0.75, "x": 0, "y": 96.49}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.8333, "x": 0, "y": 96.49}, {"time": 0.8833, "x": 0, "y": 162.37}, {"time": 0.9167, "x": 0, "y": 197.23}, {"time": 0.9333, "x": 0, "y": 231.18}, {"time": 0.9667, "x": 0, "y": 162.37}, {"time": 1, "x": 0, "y": 96.49}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": 130.24, "y": 47.4}, {"time": 1.1167, "x": 0, "y": 0}, {"time": 1.1333, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 1.15, "x": 130.24, "y": 47.4}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.2, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 1.2167, "x": 130.24, "y": 47.4}, {"time": 1.25, "x": 0, "y": 0}, {"time": 1.2667, "x": -126.68, "y": 22.81, "curve": "stepped"}, {"time": 1.2833, "x": 122.89, "y": 11.33, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.3, "x": 0.96, "y": 212.74, "curve": [0.174, 0.55, 0.493, 1]}, {"time": 1.3333, "x": 0, "y": 231.18}, {"time": 1.3667, "x": 0, "y": 162.37}, {"time": 1.4167, "x": 0, "y": 96.49}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.5, "x": 0, "y": 96.49}, {"time": 1.55, "x": 0, "y": 162.37}, {"time": 1.5833, "x": 0, "y": 197.23}, {"time": 1.6, "x": 0, "y": 231.18}, {"time": 1.6333, "x": 0, "y": 162.37}, {"time": 1.6667, "x": 0, "y": 96.49}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": 130.24, "y": 47.4}, {"time": 1.75, "x": 0, "y": 0}, {"time": 1.7667, "x": 130.24, "y": 47.4}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": 130.24, "y": 47.4}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": 130.24, "y": 47.4, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.8667, "x": -85.63, "y": 48.42, "curve": [0.264, 0.62, 0.595, 1]}, {"time": 1.9, "x": 130.24, "y": 47.4}, {"time": 1.9167, "x": 0, "y": 0}, {"time": 1.9333, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 1.9833, "x": 130.24, "y": 47.4}, {"time": 2, "x": 0, "y": 0}, {"time": 2.05, "x": 130.24, "y": 47.4}, {"time": 2.1, "x": 0, "y": 0}, {"time": 2.1333, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 2.15, "x": 87.25, "y": 41.76, "curve": [0.072, 0.47, 0.371, 1]}, {"time": 2.2833, "x": 0, "y": 0}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 1.232}, {"time": 0.55, "x": 1, "y": 1.374}, {"time": 0.5667, "x": 1, "y": 1.478}, {"time": 0.5833, "x": 1, "y": 1.18}, {"time": 0.6, "x": 1, "y": 1.107}, {"time": 0.6167, "x": 1, "y": 1.066}, {"time": 0.6333, "x": 1, "y": 0.83}, {"time": 0.65, "x": 1, "y": 0.666}, {"time": 0.6667, "x": 1, "y": 0.102}, {"time": 0.7, "x": 1, "y": 0.969}, {"time": 0.7167, "x": 1, "y": 1.357}, {"time": 0.7333, "x": 1, "y": 1.37}, {"time": 0.75, "x": 1, "y": 1.384}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 1.306}, {"time": 0.85, "x": 1, "y": 1.38}, {"time": 0.8667, "x": 1, "y": 1.385}, {"time": 0.8833, "x": 1, "y": 1.062}, {"time": 0.9, "x": 1, "y": 0.879}, {"time": 0.9167, "x": 1, "y": 0.509}, {"time": 0.9333, "x": 1, "y": 0.102}, {"time": 0.9667, "x": 1, "y": 1.005}, {"time": 0.9833, "x": 1, "y": 1.431}, {"time": 1, "x": 1, "y": 1.362}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.2833, "x": 1, "y": 0.31}, {"time": 1.3, "x": 1, "y": 0.406}, {"time": 1.3333, "x": 1, "y": 0.398}, {"time": 1.3667, "x": 1, "y": 1.169}, {"time": 1.3833, "x": 1, "y": 1.463}, {"time": 1.4, "x": 1, "y": 1.474}, {"time": 1.4167, "x": 1, "y": 1.232}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 1.232}, {"time": 1.5167, "x": 1, "y": 1.44}, {"time": 1.5333, "x": 1, "y": 1.443}, {"time": 1.55, "x": 1, "y": 1.104}, {"time": 1.5833, "x": 1, "y": 0.509}, {"time": 1.6, "x": 1, "y": 0.102}, {"time": 1.6333, "x": 1, "y": 0.913}, {"time": 1.65, "x": 1, "y": 1.431}, {"time": 1.6667, "x": 1, "y": 1.362}, {"time": 1.7167, "x": 1, "y": 1.203}, {"time": 1.7333, "x": 1, "y": 1.2}, {"time": 1.85, "x": 1, "y": 1.184}, {"time": 1.8667, "x": 1, "y": 1.203}, {"time": 1.9, "x": 1, "y": 1.199}, {"time": 1.9333, "x": 1, "y": 1.176}, {"time": 2, "x": 1, "y": 0.465, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.465}, {"time": 2.4, "x": 1, "y": 0.312}]}, "mat tren10": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.0667, "x": -115.92, "y": -23.14}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.1667, "x": -115.92, "y": -23.14}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.2833, "x": -115.92, "y": -23.14}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.4, "x": -115.92, "y": -23.14}, {"time": 0.45, "x": -1.86, "y": 0}, {"time": 0.4833, "x": -1.86, "y": -80.05}, {"time": 0.5, "x": -1.86, "y": -109.69}, {"time": 0.5167, "x": -1.86, "y": -121.4}, {"time": 0.65, "x": -1.86, "y": -101.97, "curve": "stepped"}, {"time": 0.6667, "x": -1.86, "y": -101.97}, {"time": 0.75, "x": -1.86, "y": -121.4}, {"time": 0.7667, "x": -1.86, "y": -79.61}, {"time": 0.7833, "x": -1.86, "y": 0}, {"time": 0.8167, "x": -1.86, "y": -86.04}, {"time": 0.8333, "x": -1.86, "y": -121.4}, {"time": 0.9167, "x": -1.86, "y": -101.97, "curve": "stepped"}, {"time": 0.9333, "x": -1.86, "y": -101.97}, {"time": 1, "x": -1.86, "y": -121.4}, {"time": 1.0167, "x": -1.12, "y": -93.49}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": -107.63, "y": -43.68}, {"time": 1.1167, "x": 2.04, "y": -24.06}, {"time": 1.1333, "x": 116.49, "y": -55.7, "curve": "stepped"}, {"time": 1.15, "x": -103.59, "y": -28.59}, {"time": 1.1667, "x": -12.33, "y": -37.73}, {"time": 1.1833, "x": 62.07, "y": -66.2}, {"time": 1.2, "x": 121.71, "y": -73.43, "curve": "stepped"}, {"time": 1.2167, "x": -116.33, "y": -67.24}, {"time": 1.2333, "x": -63.06, "y": -55.74}, {"time": 1.25, "x": -9.44, "y": -49.64}, {"time": 1.2667, "x": 110.15, "y": -102.6, "curve": "stepped"}, {"time": 1.2833, "x": -100.12, "y": -98.8, "curve": [0.049, 0.46, 0.343, 1]}, {"time": 1.3333, "x": -1.86, "y": -101.97}, {"time": 1.4167, "x": -1.86, "y": -121.4}, {"time": 1.4333, "x": -1.86, "y": -79.61}, {"time": 1.45, "x": -1.86, "y": 0}, {"time": 1.4833, "x": -1.86, "y": -86.04}, {"time": 1.5, "x": -1.86, "y": -121.4}, {"time": 1.5833, "x": -1.86, "y": -101.97, "curve": "stepped"}, {"time": 1.6, "x": -1.86, "y": -101.97}, {"time": 1.6667, "x": -1.86, "y": -121.4}, {"time": 1.6833, "x": -1.12, "y": -80.61}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": -115.92, "y": -9.43}, {"time": 1.75, "x": 0, "y": 0}, {"time": 1.7667, "x": -115.92, "y": -14}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": -115.92, "y": -14}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": -115.92, "y": -20.84, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.8667, "x": 81.6, "y": -15.26, "curve": [0.264, 0.62, 0.595, 1]}, {"time": 1.9, "x": -115.92, "y": -23.14}, {"time": 1.9167, "x": 0, "y": 0}, {"time": 1.9333, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 1.9833, "x": -115.92, "y": -23.14}, {"time": 2, "x": 0, "y": 0}, {"time": 2.05, "x": -115.92, "y": -23.14}, {"time": 2.1, "x": 0, "y": 0}, {"time": 2.1333, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 2.15, "x": -115.92, "y": -23.14, "curve": [0.184, 0.33, 0.514, 0.66]}, {"time": 2.2, "x": -55.34, "y": -12.43, "curve": [0.251, 0.34, 0.582, 0.68]}, {"time": 2.2167, "x": -32.92, "y": -16.99, "curve": [0.168, 0.55, 0.487, 1]}, {"time": 2.2833, "x": -10.23, "y": 10.23}, {"time": 2.4, "x": -3.41, "y": 20.46}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 0.75, "x": 1, "y": 1.466}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1.466}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.4167, "x": 1, "y": 1.466}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1.466}, {"time": 1.7167, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 1, "y": 1}, {"time": 2, "x": 1, "y": 0.401, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.401}, {"time": 2.4, "x": 1, "y": 0.312}]}, "mat duoi10": {"translate": [{"time": 0.45, "x": 0, "y": 0}, {"time": 0.65, "x": 0, "y": -107.61}, {"time": 0.6667, "x": 0, "y": -131.1}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.9, "x": 0, "y": -100.71}, {"time": 0.9167, "x": 0, "y": -107.61, "curve": "stepped"}, {"time": 0.9333, "x": 0, "y": -107.61}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.3, "x": 69.53, "y": -156.51}, {"time": 1.3333, "x": 0, "y": -129.56}, {"time": 1.35, "x": -5.74, "y": -137.59}, {"time": 1.3667, "x": -4.79, "y": -96.12}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.5833, "x": 0, "y": -107.61}, {"time": 1.6, "x": 0, "y": -123.27}, {"time": 1.7167, "x": 0, "y": 0}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.65, "x": 1, "y": 0.933}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.9167, "x": 1, "y": 0.933}, {"time": 1.05, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5833, "x": 1, "y": 0.933}, {"time": 1.7167, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 1, "y": 1}, {"time": 2, "x": 1, "y": 0.401, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.401}, {"time": 2.4, "x": 1, "y": 0.312}]}, "bone4": {"rotate": [{"time": 0.1667, "angle": 0}, {"time": 0.3167, "angle": 120}, {"time": 0.4667, "angle": -120}, {"time": 0.6, "angle": 0}, {"time": 0.75, "angle": 120}, {"time": 0.9, "angle": -120}, {"time": 1.0333, "angle": 0, "curve": "stepped"}, {"time": 1.25, "angle": 0}, {"time": 1.3, "angle": 120}, {"time": 1.45, "angle": -120}, {"time": 1.5833, "angle": 0}, {"time": 1.7333, "angle": 120}, {"time": 1.8167, "angle": -120}, {"time": 1.9, "angle": 0}]}, "bone5": {"rotate": [{"time": 0.3333, "angle": 0}, {"time": 0.4833, "angle": 120}, {"time": 0.6333, "angle": -120}, {"time": 0.7833, "angle": 0}, {"time": 0.9167, "angle": 120}, {"time": 1.0667, "angle": -120}, {"time": 1.2167, "angle": 0, "curve": "stepped"}, {"time": 1.4167, "angle": 0}, {"time": 1.4667, "angle": 120}, {"time": 1.5, "angle": 0}, {"time": 1.6167, "angle": -120}, {"time": 1.75, "angle": 0}, {"time": 1.8333, "angle": 120}, {"time": 1.9167, "angle": -120}, {"time": 2.0167, "angle": 0}]}, "bone6": {"rotate": [{"time": 0.2667, "angle": 0}, {"time": 0.4, "angle": 120}, {"time": 0.55, "angle": -120}, {"time": 0.7, "angle": 0}, {"time": 0.8333, "angle": 120}, {"time": 0.9833, "angle": -120}, {"time": 1.1333, "angle": 0, "curve": "stepped"}, {"time": 1.35, "angle": 0}, {"time": 1.3833, "angle": 120}, {"time": 1.4167, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0}, {"time": 1.5333, "angle": -120}, {"time": 1.6833, "angle": 0}, {"time": 1.7667, "angle": 120}, {"time": 1.8667, "angle": -120}, {"time": 1.9833, "angle": 0}]}, "no": {"scale": [{"time": 0, "x": 0.605, "y": 0.605}, {"time": 0.0833, "x": 1, "y": 1}, {"time": 1.9833, "x": 1.052, "y": 1.052}, {"time": 2.1, "x": 1.87, "y": 1.87}, {"time": 2.3167, "x": 1.052, "y": 1.052}, {"time": 2.4, "x": 0.605, "y": 0.605}]}, "no2": {"scale": [{"time": 2.1333, "x": 1, "y": 1}, {"time": 2.2833, "x": 4.931, "y": 4.931}, {"time": 2.4, "x": 1, "y": 1}]}, "ben trai10": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4167, "angle": -5.7}, {"time": 0.45, "angle": -1.2, "curve": "stepped"}, {"time": 1.85, "angle": -1.2}, {"time": 2.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.0667, "x": 34.74, "y": -72.38}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.1667, "x": 34.74, "y": -73.59}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.2833, "x": 34.74, "y": -69.94}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.4, "x": 34.74, "y": -69.94, "curve": [0.049, 0.46, 0.343, 1]}, {"time": 0.45, "x": 0, "y": 0}, {"time": 0.6, "x": 0.3, "y": 14.43, "curve": "stepped"}, {"time": 0.7, "x": 0.3, "y": 14.43}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.8833, "x": 0.3, "y": 14.43}, {"time": 0.9167, "x": 0.3, "y": 10.66}, {"time": 0.9333, "x": 0.3, "y": 29.85}, {"time": 0.9667, "x": 0.3, "y": 14.43}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": 46.73, "y": -72.38}, {"time": 1.1167, "x": 0, "y": 0}, {"time": 1.1333, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 1.15, "x": 34.74, "y": -73.59}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.2, "x": 34.47, "y": 95.69, "curve": "stepped"}, {"time": 1.2167, "x": 34.74, "y": -69.94}, {"time": 1.25, "x": -12.45, "y": 13.15}, {"time": 1.2667, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 1.2833, "x": 25.07, "y": -81.99, "curve": [0.078, 0.35, 0.388, 0.74]}, {"time": 1.3333, "x": 2.37, "y": 28.22, "curve": [0.285, 0.42, 0.618, 0.76]}, {"time": 1.35, "x": 0.74, "y": 0.97, "curve": [0.294, 0.64, 0.626, 1]}, {"time": 1.3667, "x": 0.3, "y": 14.43}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.55, "x": 0.3, "y": 14.43}, {"time": 1.6, "x": 0.3, "y": 24.07}, {"time": 1.6333, "x": 0.3, "y": 14.43}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": 34.74, "y": -72.38}, {"time": 1.75, "x": 0, "y": -12.18}, {"time": 1.7667, "x": 34.74, "y": -73.59}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": 34.74, "y": -69.94}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": 34.74, "y": -69.94, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.8667, "x": 35.32, "y": 55.99, "curve": [0.174, 0.55, 0.493, 1]}, {"time": 1.9, "x": 34.74, "y": -72.38}, {"time": 1.9167, "x": 0, "y": -7.61}, {"time": 1.9333, "x": 35.53, "y": 85.59, "curve": "stepped"}, {"time": 1.9833, "x": 34.74, "y": -73.59}, {"time": 2, "x": 0, "y": 22.33}, {"time": 2.05, "x": 34.74, "y": -69.94}, {"time": 2.1, "x": 0, "y": 30.47}, {"time": 2.1333, "x": 35.53, "y": 139.21, "curve": "stepped"}, {"time": 2.15, "x": 34.74, "y": -69.94, "curve": [0.071, 0.36, 0.379, 0.77]}, {"time": 2.2167, "x": 1.92, "y": 8.74, "curve": [0.264, 0.62, 0.595, 1]}, {"time": 2.2833, "x": 0, "y": 24.85}, {"time": 2.4, "x": 0, "y": 47.74}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 0.783}, {"time": 0.55, "x": 1, "y": 0.978}, {"time": 0.5667, "x": 1, "y": 0.967}, {"time": 0.5833, "x": 1, "y": 0.842}, {"time": 0.6, "x": 1, "y": 0.892}, {"time": 0.6167, "x": 1, "y": 0.881}, {"time": 0.6333, "x": 1, "y": 0.706}, {"time": 0.65, "x": 1, "y": 0.599}, {"time": 0.6667, "x": 1, "y": 0.068}, {"time": 0.7, "x": 1, "y": 0.781}, {"time": 0.7167, "x": 1, "y": 0.95}, {"time": 0.7333, "x": 1, "y": 0.962}, {"time": 0.75, "x": 1, "y": 0.879}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 0.83}, {"time": 0.85, "x": 1, "y": 0.84}, {"time": 0.8667, "x": 1, "y": 0.846}, {"time": 0.8833, "x": 1, "y": 0.856}, {"time": 0.9, "x": 1, "y": 0.814}, {"time": 0.9167, "x": 1, "y": 0.457}, {"time": 0.9333, "x": 1, "y": 0.068}, {"time": 0.9667, "x": 1, "y": 0.81}, {"time": 0.9833, "x": 1, "y": 0.845}, {"time": 1, "x": 1, "y": 0.865}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.2833, "x": 1, "y": 0.269}, {"time": 1.3, "x": 1, "y": 0.346}, {"time": 1.3333, "x": 1, "y": 0.264}, {"time": 1.3667, "x": 1, "y": 0.942}, {"time": 1.3833, "x": 1, "y": 1.013}, {"time": 1.4, "x": 1, "y": 0.916}, {"time": 1.4167, "x": 1, "y": 0.783}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 0.783}, {"time": 1.5167, "x": 1, "y": 0.876}, {"time": 1.5333, "x": 1, "y": 0.902}, {"time": 1.55, "x": 1, "y": 0.89}, {"time": 1.5833, "x": 1, "y": 0.457}, {"time": 1.6, "x": 1, "y": 0.068}, {"time": 1.6333, "x": 1, "y": 0.735}, {"time": 1.65, "x": 1, "y": 0.845}, {"time": 1.6667, "x": 1, "y": 0.865}, {"time": 1.7167, "x": 1, "y": 1.121}, {"time": 1.7333, "x": 1, "y": 1.215}, {"time": 1.8167, "x": 1, "y": 1.138}, {"time": 1.85, "x": 1, "y": 1.161}, {"time": 1.8667, "x": 1, "y": 1.028}, {"time": 1.9, "x": 1, "y": 1.193}, {"time": 1.9333, "x": 1, "y": 1.157}, {"time": 1.9833, "x": 1, "y": 1.028}, {"time": 2, "x": 1, "y": 0.446, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.446}, {"time": 2.4, "x": 1, "y": 0.312}]}, "ben phai10": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4167, "angle": -7.27}, {"time": 0.4333, "angle": -10.19}, {"time": 0.45, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": -29.09, "y": -64.8, "curve": "stepped"}, {"time": 0.0667, "x": -33.86, "y": 90.93}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": -29.09, "y": -63.09, "curve": "stepped"}, {"time": 0.1667, "x": -33.86, "y": 90.93}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": -29.09, "y": -64.3, "curve": "stepped"}, {"time": 0.2833, "x": -33.86, "y": 90.93}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": -29.09, "y": -65.52, "curve": "stepped"}, {"time": 0.4, "x": -33.86, "y": 90.93}, {"time": 0.45, "x": 0, "y": 0}, {"time": 0.6, "x": 0, "y": 12.49, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 12.49}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.8833, "x": 0, "y": 12.49}, {"time": 0.9167, "x": 0, "y": 2.93}, {"time": 0.9333, "x": 0, "y": 19.24}, {"time": 0.9667, "x": 0, "y": 12.49}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": -33.86, "y": 90.93}, {"time": 1.1167, "x": 0, "y": 0}, {"time": 1.1333, "x": -29.09, "y": -63.09, "curve": "stepped"}, {"time": 1.15, "x": -37.95, "y": 83.63}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.2, "x": -29.09, "y": -64.3, "curve": "stepped"}, {"time": 1.2167, "x": -33.86, "y": 90.93}, {"time": 1.25, "x": 0, "y": 0}, {"time": 1.2667, "x": -32.53, "y": -89.86, "curve": "stepped"}, {"time": 1.2833, "x": -23.7, "y": 66.88}, {"time": 1.3, "x": 56.2, "y": 30}, {"time": 1.3333, "x": -2.39, "y": 20.45}, {"time": 1.35, "x": -1.19, "y": 0.21}, {"time": 1.3667, "x": 0, "y": 12.49}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.55, "x": 0, "y": 12.49, "curve": "stepped"}, {"time": 1.6333, "x": 0, "y": 12.49}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": -33.86, "y": 90.93}, {"time": 1.75, "x": 0, "y": 0}, {"time": 1.7667, "x": -33.86, "y": 90.93}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": -33.86, "y": 90.93}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": -23.7, "y": 87.55}, {"time": 1.8667, "x": -16.75, "y": -45.96}, {"time": 1.9, "x": -33.86, "y": 90.93}, {"time": 1.9167, "x": 0, "y": 0}, {"time": 1.9333, "x": -29.09, "y": -63.09, "curve": "stepped"}, {"time": 1.9833, "x": -33.86, "y": 90.93}, {"time": 2, "x": 0, "y": 33.81}, {"time": 2.05, "x": -33.86, "y": 139.34}, {"time": 2.1, "x": 0, "y": 30.47}, {"time": 2.1333, "x": -29.09, "y": -65.52, "curve": "stepped"}, {"time": 2.15, "x": -23.7, "y": 125.57}, {"time": 2.2167, "x": -2.39, "y": 37.31}, {"time": 2.2833, "x": 0, "y": 36.33}, {"time": 2.4, "x": 6.82, "y": 37.51}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 0.791}, {"time": 0.55, "x": 1, "y": 0.957}, {"time": 0.5667, "x": 1, "y": 0.934}, {"time": 0.5833, "x": 1, "y": 0.801}, {"time": 0.6, "x": 1, "y": 0.833}, {"time": 0.6167, "x": 1, "y": 0.831}, {"time": 0.6333, "x": 1, "y": 0.674}, {"time": 0.65, "x": 1, "y": 0.584}, {"time": 0.6667, "x": 1, "y": 0.08}, {"time": 0.7, "x": 1, "y": 0.729}, {"time": 0.7167, "x": 1, "y": 0.913}, {"time": 0.7333, "x": 1, "y": 0.947}, {"time": 0.75, "x": 1, "y": 0.889}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 0.839}, {"time": 0.85, "x": 1, "y": 0.828}, {"time": 0.8667, "x": 1, "y": 0.814}, {"time": 0.8833, "x": 1, "y": 0.799}, {"time": 0.9, "x": 1, "y": 0.773}, {"time": 0.9167, "x": 1, "y": 0.446}, {"time": 0.9333, "x": 1, "y": 0.08}, {"time": 0.9667, "x": 1, "y": 0.756}, {"time": 0.9833, "x": 1, "y": 0.823}, {"time": 1, "x": 1, "y": 0.875}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.2833, "x": 1, "y": 0.284}, {"time": 1.3, "x": 1, "y": 0.367}, {"time": 1.3333, "x": 1, "y": 0.311}, {"time": 1.3667, "x": 1, "y": 0.879}, {"time": 1.3833, "x": 1, "y": 0.975}, {"time": 1.4, "x": 1, "y": 0.904}, {"time": 1.4167, "x": 1, "y": 0.791}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 0.791}, {"time": 1.5167, "x": 1, "y": 0.864}, {"time": 1.5333, "x": 1, "y": 0.868}, {"time": 1.55, "x": 1, "y": 0.831}, {"time": 1.5833, "x": 1, "y": 0.446}, {"time": 1.6, "x": 1, "y": 0.08}, {"time": 1.6333, "x": 1, "y": 0.686}, {"time": 1.65, "x": 1, "y": 0.823}, {"time": 1.6667, "x": 1, "y": 0.875}, {"time": 1.7167, "x": 1, "y": 1.122}, {"time": 1.7333, "x": 1, "y": 1.124}, {"time": 1.85, "x": 1, "y": 1}, {"time": 1.8667, "x": 1, "y": 1.155}, {"time": 1.9, "x": 1, "y": 0.996}, {"time": 1.9333, "x": 1, "y": 1.11}, {"time": 1.9833, "x": 1, "y": 0.983}, {"time": 2, "x": 1, "y": 0.422, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.422}, {"time": 2.4, "x": 1, "y": 0.312}]}, "control13": {"rotate": [{"time": 0, "angle": 0, "curve": [0.019, 0.54, 0.519, 0.83]}, {"time": 0.15, "angle": 120, "curve": [0.629, 0.07, 0.987, 0.73]}, {"time": 0.3167, "angle": -120, "curve": [0.481, 0, 0.448, 0.99]}, {"time": 0.45, "angle": 0}, {"time": 0.6667, "angle": 180}, {"time": 0.7833, "angle": 0}, {"time": 0.9333, "angle": 180}, {"time": 1.05, "angle": 0}, {"time": 1.3333, "angle": 180}, {"time": 1.45, "angle": 0}, {"time": 1.6, "angle": 180}, {"time": 1.7167, "angle": 0}, {"time": 1.95, "angle": 120}, {"time": 2.2, "angle": -120}, {"time": 2.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1, "x": 0, "y": 235.01, "curve": "stepped"}, {"time": 2, "x": 0, "y": 235.01}, {"time": 2.0333, "x": 0, "y": 329.15}, {"time": 2.1333, "x": 0, "y": 0}, {"time": 2.2, "x": 0, "y": 150.21}, {"time": 2.2833, "x": 0, "y": 0}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 0.75, "x": 1, "y": 1.466}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1.466}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.4167, "x": 1, "y": 1.466}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1.466}, {"time": 1.7167, "x": 1, "y": 1}]}, "control5": {"scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.1667, "x": 1.332, "y": 1.332, "curve": "stepped"}, {"time": 2, "x": 1.332, "y": 1.332}, {"time": 2.1333, "x": 1.348, "y": 1.348}, {"time": 2.2833, "x": 1, "y": 1}]}}, "deform": {"default": {"Rectangle 1 copy 20": {"mat tren 5": [{"time": 1.6, "offset": 6, "vertices": [6.24777, -4.31001]}]}}}}, "xi ngau bay 3": {"slots": {"10": {"attachment": [{"time": 0.0667, "name": "2"}, {"time": 0.1667, "name": "3"}, {"time": 0.2833, "name": "4"}, {"time": 0.4, "name": "1"}, {"time": 0.6667, "name": "3"}, {"time": 0.9333, "name": "1"}, {"time": 1.0833, "name": "2"}, {"time": 1.15, "name": "3"}, {"time": 1.2167, "name": "4"}, {"time": 1.2833, "name": "1"}, {"time": 1.3333, "name": "3"}, {"time": 1.6, "name": "1"}, {"time": 1.7333, "name": "2"}, {"time": 1.7667, "name": "3"}, {"time": 1.8167, "name": "4"}, {"time": 1.85, "name": "1"}, {"time": 1.9, "name": "2"}, {"time": 1.9833, "name": "3"}, {"time": 2.05, "name": "4"}, {"time": 2.15, "name": "1"}]}, "Rectangle 1 copy 20": {"attachment": [{"time": 1.65, "name": "mat tren 5"}, {"time": 1.6667, "name": "mat tren 1"}, {"time": 1.85, "name": "mat tren 2"}, {"time": 1.9, "name": "mat tren 1"}, {"time": 1.9333, "name": "mat tren 5"}, {"time": 2.2333, "name": "mat tren 3"}]}, "Rectangle 1 copy 21": {"attachment": [{"time": 1.05, "name": "mat duoi 2"}, {"time": 1.5, "name": "Rectangle 1 copy 3"}, {"time": 1.6667, "name": "mat duoi 2"}]}}, "bones": {"giua10": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4167, "angle": -6.49}, {"time": 0.4333, "angle": -10.43}, {"time": 0.45, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.0667, "x": 130.24, "y": 47.4}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.1667, "x": 130.24, "y": 47.4}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.2833, "x": 130.24, "y": 47.4}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.4, "x": 130.24, "y": 47.4}, {"time": 0.45, "x": 0, "y": 0}, {"time": 0.5167, "x": 0, "y": 96.49}, {"time": 0.6, "x": 0, "y": 162.37}, {"time": 0.65, "x": 0, "y": 197.23}, {"time": 0.6667, "x": 0, "y": 231.18}, {"time": 0.7, "x": 0, "y": 162.37}, {"time": 0.75, "x": 0, "y": 96.49}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.8333, "x": 0, "y": 96.49}, {"time": 0.8833, "x": 0, "y": 162.37}, {"time": 0.9167, "x": 0, "y": 197.23}, {"time": 0.9333, "x": 0, "y": 231.18}, {"time": 0.9667, "x": 0, "y": 162.37}, {"time": 1, "x": 0, "y": 96.49}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": 130.24, "y": 47.4}, {"time": 1.1167, "x": 0, "y": 0}, {"time": 1.1333, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 1.15, "x": 130.24, "y": 47.4}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.2, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 1.2167, "x": 130.24, "y": 47.4}, {"time": 1.25, "x": 0, "y": 0}, {"time": 1.2667, "x": -126.68, "y": 22.81, "curve": "stepped"}, {"time": 1.2833, "x": 122.89, "y": 11.33, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.3, "x": 0.96, "y": 212.74, "curve": [0.174, 0.55, 0.493, 1]}, {"time": 1.3333, "x": 0, "y": 231.18}, {"time": 1.3667, "x": 0, "y": 162.37}, {"time": 1.4167, "x": 0, "y": 96.49}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.5, "x": 0, "y": 96.49}, {"time": 1.55, "x": 0, "y": 162.37}, {"time": 1.5833, "x": 0, "y": 197.23}, {"time": 1.6, "x": 0, "y": 231.18}, {"time": 1.6333, "x": 0, "y": 162.37}, {"time": 1.6667, "x": 0, "y": 96.49}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": 130.24, "y": 47.4}, {"time": 1.75, "x": 0, "y": 0}, {"time": 1.7667, "x": 130.24, "y": 47.4}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": 130.24, "y": 47.4}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": 130.24, "y": 47.4, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.8667, "x": -85.63, "y": 48.42, "curve": [0.264, 0.62, 0.595, 1]}, {"time": 1.9, "x": 130.24, "y": 47.4}, {"time": 1.9167, "x": 0, "y": 0}, {"time": 1.9333, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 1.9833, "x": 130.24, "y": 47.4}, {"time": 2, "x": 0, "y": 0}, {"time": 2.05, "x": 130.24, "y": 47.4}, {"time": 2.1, "x": 0, "y": 0}, {"time": 2.1333, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 2.15, "x": 87.25, "y": 41.76, "curve": [0.072, 0.47, 0.371, 1]}, {"time": 2.2833, "x": 0, "y": 0}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 1.232}, {"time": 0.55, "x": 1, "y": 1.374}, {"time": 0.5667, "x": 1, "y": 1.478}, {"time": 0.5833, "x": 1, "y": 1.18}, {"time": 0.6, "x": 1, "y": 1.107}, {"time": 0.6167, "x": 1, "y": 1.066}, {"time": 0.6333, "x": 1, "y": 0.83}, {"time": 0.65, "x": 1, "y": 0.666}, {"time": 0.6667, "x": 1, "y": 0.102}, {"time": 0.7, "x": 1, "y": 0.969}, {"time": 0.7167, "x": 1, "y": 1.357}, {"time": 0.7333, "x": 1, "y": 1.37}, {"time": 0.75, "x": 1, "y": 1.384}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 1.306}, {"time": 0.85, "x": 1, "y": 1.38}, {"time": 0.8667, "x": 1, "y": 1.385}, {"time": 0.8833, "x": 1, "y": 1.062}, {"time": 0.9, "x": 1, "y": 0.879}, {"time": 0.9167, "x": 1, "y": 0.509}, {"time": 0.9333, "x": 1, "y": 0.102}, {"time": 0.9667, "x": 1, "y": 1.005}, {"time": 0.9833, "x": 1, "y": 1.431}, {"time": 1, "x": 1, "y": 1.362}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.2833, "x": 1, "y": 0.31}, {"time": 1.3, "x": 1, "y": 0.406}, {"time": 1.3333, "x": 1, "y": 0.398}, {"time": 1.3667, "x": 1, "y": 1.169}, {"time": 1.3833, "x": 1, "y": 1.463}, {"time": 1.4, "x": 1, "y": 1.474}, {"time": 1.4167, "x": 1, "y": 1.232}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 1.232}, {"time": 1.5167, "x": 1, "y": 1.44}, {"time": 1.5333, "x": 1, "y": 1.443}, {"time": 1.55, "x": 1, "y": 1.104}, {"time": 1.5833, "x": 1, "y": 0.509}, {"time": 1.6, "x": 1, "y": 0.102}, {"time": 1.6333, "x": 1, "y": 0.913}, {"time": 1.65, "x": 1, "y": 1.431}, {"time": 1.6667, "x": 1, "y": 1.362}, {"time": 1.7167, "x": 1, "y": 1.203}, {"time": 1.7333, "x": 1, "y": 1.2}, {"time": 1.85, "x": 1, "y": 1.184}, {"time": 1.8667, "x": 1, "y": 1.203}, {"time": 1.9, "x": 1, "y": 1.199}, {"time": 1.9333, "x": 1, "y": 1.176}, {"time": 2, "x": 1, "y": 0.465, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.465}, {"time": 2.4, "x": 1, "y": 0.312}]}, "mat tren10": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.0667, "x": -115.92, "y": -23.14}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.1667, "x": -115.92, "y": -23.14}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.2833, "x": -115.92, "y": -23.14}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.4, "x": -115.92, "y": -23.14}, {"time": 0.45, "x": -1.86, "y": 0}, {"time": 0.4833, "x": -1.86, "y": -80.05}, {"time": 0.5, "x": -1.86, "y": -109.69}, {"time": 0.5167, "x": -1.86, "y": -121.4}, {"time": 0.65, "x": -1.86, "y": -101.97, "curve": "stepped"}, {"time": 0.6667, "x": -1.86, "y": -101.97}, {"time": 0.75, "x": -1.86, "y": -121.4}, {"time": 0.7667, "x": -1.86, "y": -79.61}, {"time": 0.7833, "x": -1.86, "y": 0}, {"time": 0.8167, "x": -1.86, "y": -86.04}, {"time": 0.8333, "x": -1.86, "y": -121.4}, {"time": 0.9167, "x": -1.86, "y": -101.97, "curve": "stepped"}, {"time": 0.9333, "x": -1.86, "y": -101.97}, {"time": 1, "x": -1.86, "y": -121.4}, {"time": 1.0167, "x": -1.12, "y": -93.49}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": -107.63, "y": -43.68}, {"time": 1.1167, "x": 2.04, "y": -24.06}, {"time": 1.1333, "x": 116.49, "y": -55.7, "curve": "stepped"}, {"time": 1.15, "x": -103.59, "y": -28.59}, {"time": 1.1667, "x": -12.33, "y": -37.73}, {"time": 1.1833, "x": 62.07, "y": -66.2}, {"time": 1.2, "x": 121.71, "y": -73.43, "curve": "stepped"}, {"time": 1.2167, "x": -116.33, "y": -67.24}, {"time": 1.2333, "x": -63.06, "y": -55.74}, {"time": 1.25, "x": -9.44, "y": -49.64}, {"time": 1.2667, "x": 110.15, "y": -102.6, "curve": "stepped"}, {"time": 1.2833, "x": -100.12, "y": -98.8, "curve": [0.049, 0.46, 0.343, 1]}, {"time": 1.3333, "x": -1.86, "y": -101.97}, {"time": 1.4167, "x": -1.86, "y": -121.4}, {"time": 1.4333, "x": -1.86, "y": -79.61}, {"time": 1.45, "x": -1.86, "y": 0}, {"time": 1.4833, "x": -1.86, "y": -86.04}, {"time": 1.5, "x": -1.86, "y": -121.4}, {"time": 1.5833, "x": -1.86, "y": -101.97, "curve": "stepped"}, {"time": 1.6, "x": -1.86, "y": -101.97}, {"time": 1.6667, "x": -1.86, "y": -121.4}, {"time": 1.6833, "x": -1.12, "y": -80.61}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": -115.92, "y": -9.43}, {"time": 1.75, "x": 0, "y": 0}, {"time": 1.7667, "x": -115.92, "y": -14}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": -115.92, "y": -14}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": -115.92, "y": -20.84, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.8667, "x": 81.6, "y": -15.26, "curve": [0.264, 0.62, 0.595, 1]}, {"time": 1.9, "x": -115.92, "y": -23.14}, {"time": 1.9167, "x": 0, "y": 0}, {"time": 1.9333, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 1.9833, "x": -115.92, "y": -23.14}, {"time": 2, "x": 0, "y": 0}, {"time": 2.05, "x": -115.92, "y": -23.14}, {"time": 2.1, "x": 0, "y": 0}, {"time": 2.1333, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 2.15, "x": -115.92, "y": -23.14, "curve": [0.184, 0.33, 0.514, 0.66]}, {"time": 2.2, "x": -55.34, "y": -12.43, "curve": [0.251, 0.34, 0.582, 0.68]}, {"time": 2.2167, "x": -32.92, "y": -16.99, "curve": [0.168, 0.55, 0.487, 1]}, {"time": 2.2833, "x": -10.23, "y": 10.23}, {"time": 2.4, "x": -3.41, "y": 20.46}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 0.75, "x": 1, "y": 1.466}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1.466}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.4167, "x": 1, "y": 1.466}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1.466}, {"time": 1.7167, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 1, "y": 1}, {"time": 2, "x": 1, "y": 0.401, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.401}, {"time": 2.4, "x": 1, "y": 0.312}]}, "mat duoi10": {"translate": [{"time": 0.45, "x": 0, "y": 0}, {"time": 0.65, "x": 0, "y": -107.61}, {"time": 0.6667, "x": 0, "y": -131.1}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.9, "x": 0, "y": -100.71}, {"time": 0.9167, "x": 0, "y": -107.61, "curve": "stepped"}, {"time": 0.9333, "x": 0, "y": -107.61}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.3, "x": 69.53, "y": -156.51}, {"time": 1.3333, "x": 0, "y": -129.56}, {"time": 1.35, "x": -5.74, "y": -137.59}, {"time": 1.3667, "x": -4.79, "y": -96.12}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.5833, "x": 0, "y": -107.61}, {"time": 1.6, "x": 0, "y": -123.27}, {"time": 1.7167, "x": 0, "y": 0}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.65, "x": 1, "y": 0.933}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.9167, "x": 1, "y": 0.933}, {"time": 1.05, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5833, "x": 1, "y": 0.933}, {"time": 1.7167, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 1, "y": 1}, {"time": 2, "x": 1, "y": 0.401, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.401}, {"time": 2.4, "x": 1, "y": 0.312}]}, "bone4": {"rotate": [{"time": 0.1667, "angle": 0}, {"time": 0.3167, "angle": 120}, {"time": 0.4667, "angle": -120}, {"time": 0.6, "angle": 0}, {"time": 0.75, "angle": 120}, {"time": 0.9, "angle": -120}, {"time": 1.0333, "angle": 0, "curve": "stepped"}, {"time": 1.25, "angle": 0}, {"time": 1.3, "angle": 120}, {"time": 1.45, "angle": -120}, {"time": 1.5833, "angle": 0}, {"time": 1.7333, "angle": 120}, {"time": 1.8167, "angle": -120}, {"time": 1.9, "angle": 0}]}, "bone5": {"rotate": [{"time": 0.3333, "angle": 0}, {"time": 0.4833, "angle": 120}, {"time": 0.6333, "angle": -120}, {"time": 0.7833, "angle": 0}, {"time": 0.9167, "angle": 120}, {"time": 1.0667, "angle": -120}, {"time": 1.2167, "angle": 0, "curve": "stepped"}, {"time": 1.4167, "angle": 0}, {"time": 1.4667, "angle": 120}, {"time": 1.5, "angle": 0}, {"time": 1.6167, "angle": -120}, {"time": 1.75, "angle": 0}, {"time": 1.8333, "angle": 120}, {"time": 1.9167, "angle": -120}, {"time": 2.0167, "angle": 0}]}, "bone6": {"rotate": [{"time": 0.2667, "angle": 0}, {"time": 0.4, "angle": 120}, {"time": 0.55, "angle": -120}, {"time": 0.7, "angle": 0}, {"time": 0.8333, "angle": 120}, {"time": 0.9833, "angle": -120}, {"time": 1.1333, "angle": 0, "curve": "stepped"}, {"time": 1.35, "angle": 0}, {"time": 1.3833, "angle": 120}, {"time": 1.4167, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0}, {"time": 1.5333, "angle": -120}, {"time": 1.6833, "angle": 0}, {"time": 1.7667, "angle": 120}, {"time": 1.8667, "angle": -120}, {"time": 1.9833, "angle": 0}]}, "no": {"scale": [{"time": 0, "x": 0.605, "y": 0.605}, {"time": 0.0833, "x": 1, "y": 1}, {"time": 1.9833, "x": 1.052, "y": 1.052}, {"time": 2.1, "x": 1.87, "y": 1.87}, {"time": 2.3167, "x": 1.052, "y": 1.052}, {"time": 2.4, "x": 0.605, "y": 0.605}]}, "no2": {"scale": [{"time": 2.1333, "x": 1, "y": 1}, {"time": 2.2833, "x": 4.931, "y": 4.931}, {"time": 2.4, "x": 1, "y": 1}]}, "ben trai10": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4167, "angle": -5.7}, {"time": 0.45, "angle": -1.2, "curve": "stepped"}, {"time": 1.85, "angle": -1.2}, {"time": 2.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.0667, "x": 34.74, "y": -72.38}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.1667, "x": 34.74, "y": -73.59}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.2833, "x": 34.74, "y": -69.94}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.4, "x": 34.74, "y": -69.94, "curve": [0.049, 0.46, 0.343, 1]}, {"time": 0.45, "x": 0, "y": 0}, {"time": 0.6, "x": 0.3, "y": 14.43, "curve": "stepped"}, {"time": 0.7, "x": 0.3, "y": 14.43}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.8833, "x": 0.3, "y": 14.43}, {"time": 0.9167, "x": 0.3, "y": 10.66}, {"time": 0.9333, "x": 0.3, "y": 29.85}, {"time": 0.9667, "x": 0.3, "y": 14.43}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": 46.73, "y": -72.38}, {"time": 1.1167, "x": 0, "y": 0}, {"time": 1.1333, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 1.15, "x": 34.74, "y": -73.59}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.2, "x": 34.47, "y": 95.69, "curve": "stepped"}, {"time": 1.2167, "x": 34.74, "y": -69.94}, {"time": 1.25, "x": -12.45, "y": 13.15}, {"time": 1.2667, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 1.2833, "x": 25.07, "y": -81.99, "curve": [0.078, 0.35, 0.388, 0.74]}, {"time": 1.3333, "x": 2.37, "y": 28.22, "curve": [0.285, 0.42, 0.618, 0.76]}, {"time": 1.35, "x": 0.74, "y": 0.97, "curve": [0.294, 0.64, 0.626, 1]}, {"time": 1.3667, "x": 0.3, "y": 14.43}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.55, "x": 0.3, "y": 14.43}, {"time": 1.6, "x": 0.3, "y": 24.07}, {"time": 1.6333, "x": 0.3, "y": 14.43}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": 34.74, "y": -72.38}, {"time": 1.75, "x": 0, "y": -12.18}, {"time": 1.7667, "x": 34.74, "y": -73.59}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": 34.74, "y": -69.94}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": 34.74, "y": -69.94, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.8667, "x": 35.32, "y": 55.99, "curve": [0.174, 0.55, 0.493, 1]}, {"time": 1.9, "x": 34.74, "y": -72.38}, {"time": 1.9167, "x": 0, "y": -7.61}, {"time": 1.9333, "x": 35.53, "y": 85.59, "curve": "stepped"}, {"time": 1.9833, "x": 34.74, "y": -73.59}, {"time": 2, "x": 0, "y": 22.33}, {"time": 2.05, "x": 34.74, "y": -69.94}, {"time": 2.1, "x": 0, "y": 30.47}, {"time": 2.1333, "x": 35.53, "y": 139.21, "curve": "stepped"}, {"time": 2.15, "x": 34.74, "y": -69.94, "curve": [0.071, 0.36, 0.379, 0.77]}, {"time": 2.2167, "x": 1.92, "y": 8.74, "curve": [0.264, 0.62, 0.595, 1]}, {"time": 2.2833, "x": 0, "y": 24.85}, {"time": 2.4, "x": 0, "y": 47.74}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 0.783}, {"time": 0.55, "x": 1, "y": 0.978}, {"time": 0.5667, "x": 1, "y": 0.967}, {"time": 0.5833, "x": 1, "y": 0.842}, {"time": 0.6, "x": 1, "y": 0.892}, {"time": 0.6167, "x": 1, "y": 0.881}, {"time": 0.6333, "x": 1, "y": 0.706}, {"time": 0.65, "x": 1, "y": 0.599}, {"time": 0.6667, "x": 1, "y": 0.068}, {"time": 0.7, "x": 1, "y": 0.781}, {"time": 0.7167, "x": 1, "y": 0.95}, {"time": 0.7333, "x": 1, "y": 0.962}, {"time": 0.75, "x": 1, "y": 0.879}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 0.83}, {"time": 0.85, "x": 1, "y": 0.84}, {"time": 0.8667, "x": 1, "y": 0.846}, {"time": 0.8833, "x": 1, "y": 0.856}, {"time": 0.9, "x": 1, "y": 0.814}, {"time": 0.9167, "x": 1, "y": 0.457}, {"time": 0.9333, "x": 1, "y": 0.068}, {"time": 0.9667, "x": 1, "y": 0.81}, {"time": 0.9833, "x": 1, "y": 0.845}, {"time": 1, "x": 1, "y": 0.865}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.2833, "x": 1, "y": 0.269}, {"time": 1.3, "x": 1, "y": 0.346}, {"time": 1.3333, "x": 1, "y": 0.264}, {"time": 1.3667, "x": 1, "y": 0.942}, {"time": 1.3833, "x": 1, "y": 1.013}, {"time": 1.4, "x": 1, "y": 0.916}, {"time": 1.4167, "x": 1, "y": 0.783}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 0.783}, {"time": 1.5167, "x": 1, "y": 0.876}, {"time": 1.5333, "x": 1, "y": 0.902}, {"time": 1.55, "x": 1, "y": 0.89}, {"time": 1.5833, "x": 1, "y": 0.457}, {"time": 1.6, "x": 1, "y": 0.068}, {"time": 1.6333, "x": 1, "y": 0.735}, {"time": 1.65, "x": 1, "y": 0.845}, {"time": 1.6667, "x": 1, "y": 0.865}, {"time": 1.7167, "x": 1, "y": 1.121}, {"time": 1.7333, "x": 1, "y": 1.215}, {"time": 1.8167, "x": 1, "y": 1.138}, {"time": 1.85, "x": 1, "y": 1.161}, {"time": 1.8667, "x": 1, "y": 1.028}, {"time": 1.9, "x": 1, "y": 1.193}, {"time": 1.9333, "x": 1, "y": 1.157}, {"time": 1.9833, "x": 1, "y": 1.028}, {"time": 2, "x": 1, "y": 0.446, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.446}, {"time": 2.4, "x": 1, "y": 0.312}]}, "ben phai10": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4167, "angle": -7.27}, {"time": 0.4333, "angle": -10.19}, {"time": 0.45, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": -29.09, "y": -64.8, "curve": "stepped"}, {"time": 0.0667, "x": -33.86, "y": 90.93}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": -29.09, "y": -63.09, "curve": "stepped"}, {"time": 0.1667, "x": -33.86, "y": 90.93}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": -29.09, "y": -64.3, "curve": "stepped"}, {"time": 0.2833, "x": -33.86, "y": 90.93}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": -29.09, "y": -65.52, "curve": "stepped"}, {"time": 0.4, "x": -33.86, "y": 90.93}, {"time": 0.45, "x": 0, "y": 0}, {"time": 0.6, "x": 0, "y": 12.49, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 12.49}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.8833, "x": 0, "y": 12.49}, {"time": 0.9167, "x": 0, "y": 2.93}, {"time": 0.9333, "x": 0, "y": 19.24}, {"time": 0.9667, "x": 0, "y": 12.49}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": -33.86, "y": 90.93}, {"time": 1.1167, "x": 0, "y": 0}, {"time": 1.1333, "x": -29.09, "y": -63.09, "curve": "stepped"}, {"time": 1.15, "x": -37.95, "y": 83.63}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.2, "x": -29.09, "y": -64.3, "curve": "stepped"}, {"time": 1.2167, "x": -33.86, "y": 90.93}, {"time": 1.25, "x": 0, "y": 0}, {"time": 1.2667, "x": -32.53, "y": -89.86, "curve": "stepped"}, {"time": 1.2833, "x": -23.7, "y": 66.88}, {"time": 1.3, "x": 56.2, "y": 30}, {"time": 1.3333, "x": -2.39, "y": 20.45}, {"time": 1.35, "x": -1.19, "y": 0.21}, {"time": 1.3667, "x": 0, "y": 12.49}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.55, "x": 0, "y": 12.49, "curve": "stepped"}, {"time": 1.6333, "x": 0, "y": 12.49}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": -33.86, "y": 90.93}, {"time": 1.75, "x": 0, "y": 0}, {"time": 1.7667, "x": -33.86, "y": 90.93}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": -33.86, "y": 90.93}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": -23.7, "y": 87.55}, {"time": 1.8667, "x": -16.75, "y": -45.96}, {"time": 1.9, "x": -33.86, "y": 90.93}, {"time": 1.9167, "x": 0, "y": 0}, {"time": 1.9333, "x": -29.09, "y": -63.09, "curve": "stepped"}, {"time": 1.9833, "x": -33.86, "y": 90.93}, {"time": 2, "x": 0, "y": 33.81}, {"time": 2.05, "x": -33.86, "y": 139.34}, {"time": 2.1, "x": 0, "y": 30.47}, {"time": 2.1333, "x": -29.09, "y": -65.52, "curve": "stepped"}, {"time": 2.15, "x": -23.7, "y": 125.57}, {"time": 2.2167, "x": -2.39, "y": 37.31}, {"time": 2.2833, "x": 0, "y": 36.33}, {"time": 2.4, "x": 6.82, "y": 37.51}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 0.791}, {"time": 0.55, "x": 1, "y": 0.957}, {"time": 0.5667, "x": 1, "y": 0.934}, {"time": 0.5833, "x": 1, "y": 0.801}, {"time": 0.6, "x": 1, "y": 0.833}, {"time": 0.6167, "x": 1, "y": 0.831}, {"time": 0.6333, "x": 1, "y": 0.674}, {"time": 0.65, "x": 1, "y": 0.584}, {"time": 0.6667, "x": 1, "y": 0.08}, {"time": 0.7, "x": 1, "y": 0.729}, {"time": 0.7167, "x": 1, "y": 0.913}, {"time": 0.7333, "x": 1, "y": 0.947}, {"time": 0.75, "x": 1, "y": 0.889}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 0.839}, {"time": 0.85, "x": 1, "y": 0.828}, {"time": 0.8667, "x": 1, "y": 0.814}, {"time": 0.8833, "x": 1, "y": 0.799}, {"time": 0.9, "x": 1, "y": 0.773}, {"time": 0.9167, "x": 1, "y": 0.446}, {"time": 0.9333, "x": 1, "y": 0.08}, {"time": 0.9667, "x": 1, "y": 0.756}, {"time": 0.9833, "x": 1, "y": 0.823}, {"time": 1, "x": 1, "y": 0.875}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.2833, "x": 1, "y": 0.284}, {"time": 1.3, "x": 1, "y": 0.367}, {"time": 1.3333, "x": 1, "y": 0.311}, {"time": 1.3667, "x": 1, "y": 0.879}, {"time": 1.3833, "x": 1, "y": 0.975}, {"time": 1.4, "x": 1, "y": 0.904}, {"time": 1.4167, "x": 1, "y": 0.791}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 0.791}, {"time": 1.5167, "x": 1, "y": 0.864}, {"time": 1.5333, "x": 1, "y": 0.868}, {"time": 1.55, "x": 1, "y": 0.831}, {"time": 1.5833, "x": 1, "y": 0.446}, {"time": 1.6, "x": 1, "y": 0.08}, {"time": 1.6333, "x": 1, "y": 0.686}, {"time": 1.65, "x": 1, "y": 0.823}, {"time": 1.6667, "x": 1, "y": 0.875}, {"time": 1.7167, "x": 1, "y": 1.122}, {"time": 1.7333, "x": 1, "y": 1.124}, {"time": 1.85, "x": 1, "y": 1}, {"time": 1.8667, "x": 1, "y": 1.155}, {"time": 1.9, "x": 1, "y": 0.996}, {"time": 1.9333, "x": 1, "y": 1.11}, {"time": 1.9833, "x": 1, "y": 0.983}, {"time": 2, "x": 1, "y": 0.422, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.422}, {"time": 2.4, "x": 1, "y": 0.312}]}, "control13": {"rotate": [{"time": 0, "angle": 0, "curve": [0.019, 0.54, 0.519, 0.83]}, {"time": 0.15, "angle": 120, "curve": [0.629, 0.07, 0.987, 0.73]}, {"time": 0.3167, "angle": -120, "curve": [0.481, 0, 0.448, 0.99]}, {"time": 0.45, "angle": 0}, {"time": 0.6667, "angle": 180}, {"time": 0.7833, "angle": 0}, {"time": 0.9333, "angle": 180}, {"time": 1.05, "angle": 0}, {"time": 1.3333, "angle": 180}, {"time": 1.45, "angle": 0}, {"time": 1.6, "angle": 180}, {"time": 1.7167, "angle": 0}, {"time": 1.95, "angle": 120}, {"time": 2.2, "angle": -120}, {"time": 2.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1, "x": 0, "y": 235.01, "curve": "stepped"}, {"time": 2, "x": 0, "y": 235.01}, {"time": 2.0333, "x": 0, "y": 329.15}, {"time": 2.1333, "x": 0, "y": 0}, {"time": 2.2, "x": 0, "y": 150.21}, {"time": 2.2833, "x": 0, "y": 0}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 0.75, "x": 1, "y": 1.466}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1.466}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.4167, "x": 1, "y": 1.466}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1.466}, {"time": 1.7167, "x": 1, "y": 1}]}, "control5": {"scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.1667, "x": 1.332, "y": 1.332, "curve": "stepped"}, {"time": 2, "x": 1.332, "y": 1.332}, {"time": 2.1333, "x": 1.348, "y": 1.348}, {"time": 2.2833, "x": 1, "y": 1}]}}, "deform": {"default": {"Rectangle 1 copy 20": {"mat tren 5": [{"time": 1.6, "offset": 6, "vertices": [6.24777, -4.31001]}]}}}}, "xi ngau bay 4": {"slots": {"10": {"attachment": [{"time": 0.0667, "name": "2"}, {"time": 0.1667, "name": "3"}, {"time": 0.2833, "name": "4"}, {"time": 0.4, "name": "1"}, {"time": 0.6667, "name": "3"}, {"time": 0.9333, "name": "1"}, {"time": 1.0833, "name": "2"}, {"time": 1.15, "name": "3"}, {"time": 1.2167, "name": "4"}, {"time": 1.2833, "name": "1"}, {"time": 1.3333, "name": "3"}, {"time": 1.6, "name": "1"}, {"time": 1.7333, "name": "2"}, {"time": 1.7667, "name": "3"}, {"time": 1.8167, "name": "4"}, {"time": 1.85, "name": "1"}, {"time": 1.9, "name": "2"}, {"time": 1.9833, "name": "3"}, {"time": 2.05, "name": "4"}, {"time": 2.15, "name": "1"}]}, "Rectangle 1 copy 20": {"attachment": [{"time": 1.65, "name": "mat tren 5"}, {"time": 1.6667, "name": "mat tren 1"}, {"time": 1.85, "name": "mat tren 2"}, {"time": 1.9, "name": "mat tren 1"}, {"time": 1.9333, "name": "mat tren 5"}, {"time": 2.2333, "name": "mat tren 4"}]}, "Rectangle 1 copy 21": {"attachment": [{"time": 1.05, "name": "mat duoi 2"}, {"time": 1.5, "name": "Rectangle 1 copy 3"}, {"time": 1.6667, "name": "mat duoi 2"}]}}, "bones": {"giua10": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4167, "angle": -6.49}, {"time": 0.4333, "angle": -10.43}, {"time": 0.45, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.0667, "x": 130.24, "y": 47.4}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.1667, "x": 130.24, "y": 47.4}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.2833, "x": 130.24, "y": 47.4}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.4, "x": 130.24, "y": 47.4}, {"time": 0.45, "x": 0, "y": 0}, {"time": 0.5167, "x": 0, "y": 96.49}, {"time": 0.6, "x": 0, "y": 162.37}, {"time": 0.65, "x": 0, "y": 197.23}, {"time": 0.6667, "x": 0, "y": 231.18}, {"time": 0.7, "x": 0, "y": 162.37}, {"time": 0.75, "x": 0, "y": 96.49}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.8333, "x": 0, "y": 96.49}, {"time": 0.8833, "x": 0, "y": 162.37}, {"time": 0.9167, "x": 0, "y": 197.23}, {"time": 0.9333, "x": 0, "y": 231.18}, {"time": 0.9667, "x": 0, "y": 162.37}, {"time": 1, "x": 0, "y": 96.49}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": 130.24, "y": 47.4}, {"time": 1.1167, "x": 0, "y": 0}, {"time": 1.1333, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 1.15, "x": 130.24, "y": 47.4}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.2, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 1.2167, "x": 130.24, "y": 47.4}, {"time": 1.25, "x": 0, "y": 0}, {"time": 1.2667, "x": -126.68, "y": 22.81, "curve": "stepped"}, {"time": 1.2833, "x": 122.89, "y": 11.33, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.3, "x": 0.96, "y": 212.74, "curve": [0.174, 0.55, 0.493, 1]}, {"time": 1.3333, "x": 0, "y": 231.18}, {"time": 1.3667, "x": 0, "y": 162.37}, {"time": 1.4167, "x": 0, "y": 96.49}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.5, "x": 0, "y": 96.49}, {"time": 1.55, "x": 0, "y": 162.37}, {"time": 1.5833, "x": 0, "y": 197.23}, {"time": 1.6, "x": 0, "y": 231.18}, {"time": 1.6333, "x": 0, "y": 162.37}, {"time": 1.6667, "x": 0, "y": 96.49}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": 130.24, "y": 47.4}, {"time": 1.75, "x": 0, "y": 0}, {"time": 1.7667, "x": 130.24, "y": 47.4}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": 130.24, "y": 47.4}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": 130.24, "y": 47.4, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.8667, "x": -85.63, "y": 48.42, "curve": [0.264, 0.62, 0.595, 1]}, {"time": 1.9, "x": 130.24, "y": 47.4}, {"time": 1.9167, "x": 0, "y": 0}, {"time": 1.9333, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 1.9833, "x": 130.24, "y": 47.4}, {"time": 2, "x": 0, "y": 0}, {"time": 2.05, "x": 130.24, "y": 47.4}, {"time": 2.1, "x": 0, "y": 0}, {"time": 2.1333, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 2.15, "x": 87.25, "y": 41.76, "curve": [0.072, 0.47, 0.371, 1]}, {"time": 2.2833, "x": 0, "y": 0}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 1.232}, {"time": 0.55, "x": 1, "y": 1.374}, {"time": 0.5667, "x": 1, "y": 1.478}, {"time": 0.5833, "x": 1, "y": 1.18}, {"time": 0.6, "x": 1, "y": 1.107}, {"time": 0.6167, "x": 1, "y": 1.066}, {"time": 0.6333, "x": 1, "y": 0.83}, {"time": 0.65, "x": 1, "y": 0.666}, {"time": 0.6667, "x": 1, "y": 0.102}, {"time": 0.7, "x": 1, "y": 0.969}, {"time": 0.7167, "x": 1, "y": 1.357}, {"time": 0.7333, "x": 1, "y": 1.37}, {"time": 0.75, "x": 1, "y": 1.384}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 1.306}, {"time": 0.85, "x": 1, "y": 1.38}, {"time": 0.8667, "x": 1, "y": 1.385}, {"time": 0.8833, "x": 1, "y": 1.062}, {"time": 0.9, "x": 1, "y": 0.879}, {"time": 0.9167, "x": 1, "y": 0.509}, {"time": 0.9333, "x": 1, "y": 0.102}, {"time": 0.9667, "x": 1, "y": 1.005}, {"time": 0.9833, "x": 1, "y": 1.431}, {"time": 1, "x": 1, "y": 1.362}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.2833, "x": 1, "y": 0.31}, {"time": 1.3, "x": 1, "y": 0.406}, {"time": 1.3333, "x": 1, "y": 0.398}, {"time": 1.3667, "x": 1, "y": 1.169}, {"time": 1.3833, "x": 1, "y": 1.463}, {"time": 1.4, "x": 1, "y": 1.474}, {"time": 1.4167, "x": 1, "y": 1.232}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 1.232}, {"time": 1.5167, "x": 1, "y": 1.44}, {"time": 1.5333, "x": 1, "y": 1.443}, {"time": 1.55, "x": 1, "y": 1.104}, {"time": 1.5833, "x": 1, "y": 0.509}, {"time": 1.6, "x": 1, "y": 0.102}, {"time": 1.6333, "x": 1, "y": 0.913}, {"time": 1.65, "x": 1, "y": 1.431}, {"time": 1.6667, "x": 1, "y": 1.362}, {"time": 1.7167, "x": 1, "y": 1.203}, {"time": 1.7333, "x": 1, "y": 1.2}, {"time": 1.85, "x": 1, "y": 1.184}, {"time": 1.8667, "x": 1, "y": 1.203}, {"time": 1.9, "x": 1, "y": 1.199}, {"time": 1.9333, "x": 1, "y": 1.176}, {"time": 2, "x": 1, "y": 0.465, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.465}, {"time": 2.4, "x": 1, "y": 0.312}]}, "mat tren10": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.0667, "x": -115.92, "y": -23.14}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.1667, "x": -115.92, "y": -23.14}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.2833, "x": -115.92, "y": -23.14}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.4, "x": -115.92, "y": -23.14}, {"time": 0.45, "x": -1.86, "y": 0}, {"time": 0.4833, "x": -1.86, "y": -80.05}, {"time": 0.5, "x": -1.86, "y": -109.69}, {"time": 0.5167, "x": -1.86, "y": -121.4}, {"time": 0.65, "x": -1.86, "y": -101.97, "curve": "stepped"}, {"time": 0.6667, "x": -1.86, "y": -101.97}, {"time": 0.75, "x": -1.86, "y": -121.4}, {"time": 0.7667, "x": -1.86, "y": -79.61}, {"time": 0.7833, "x": -1.86, "y": 0}, {"time": 0.8167, "x": -1.86, "y": -86.04}, {"time": 0.8333, "x": -1.86, "y": -121.4}, {"time": 0.9167, "x": -1.86, "y": -101.97, "curve": "stepped"}, {"time": 0.9333, "x": -1.86, "y": -101.97}, {"time": 1, "x": -1.86, "y": -121.4}, {"time": 1.0167, "x": -1.12, "y": -93.49}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": -107.63, "y": -43.68}, {"time": 1.1167, "x": 2.04, "y": -24.06}, {"time": 1.1333, "x": 116.49, "y": -55.7, "curve": "stepped"}, {"time": 1.15, "x": -103.59, "y": -28.59}, {"time": 1.1667, "x": -12.33, "y": -37.73}, {"time": 1.1833, "x": 62.07, "y": -66.2}, {"time": 1.2, "x": 121.71, "y": -73.43, "curve": "stepped"}, {"time": 1.2167, "x": -116.33, "y": -67.24}, {"time": 1.2333, "x": -63.06, "y": -55.74}, {"time": 1.25, "x": -9.44, "y": -49.64}, {"time": 1.2667, "x": 110.15, "y": -102.6, "curve": "stepped"}, {"time": 1.2833, "x": -100.12, "y": -98.8, "curve": [0.049, 0.46, 0.343, 1]}, {"time": 1.3333, "x": -1.86, "y": -101.97}, {"time": 1.4167, "x": -1.86, "y": -121.4}, {"time": 1.4333, "x": -1.86, "y": -79.61}, {"time": 1.45, "x": -1.86, "y": 0}, {"time": 1.4833, "x": -1.86, "y": -86.04}, {"time": 1.5, "x": -1.86, "y": -121.4}, {"time": 1.5833, "x": -1.86, "y": -101.97, "curve": "stepped"}, {"time": 1.6, "x": -1.86, "y": -101.97}, {"time": 1.6667, "x": -1.86, "y": -121.4}, {"time": 1.6833, "x": -1.12, "y": -80.61}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": -115.92, "y": -9.43}, {"time": 1.75, "x": 0, "y": 0}, {"time": 1.7667, "x": -115.92, "y": -14}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": -115.92, "y": -14}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": -115.92, "y": -20.84, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.8667, "x": 81.6, "y": -15.26, "curve": [0.264, 0.62, 0.595, 1]}, {"time": 1.9, "x": -115.92, "y": -23.14}, {"time": 1.9167, "x": 0, "y": 0}, {"time": 1.9333, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 1.9833, "x": -115.92, "y": -23.14}, {"time": 2, "x": 0, "y": 0}, {"time": 2.05, "x": -115.92, "y": -23.14}, {"time": 2.1, "x": 0, "y": 0}, {"time": 2.1333, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 2.15, "x": -115.92, "y": -23.14, "curve": [0.184, 0.33, 0.514, 0.66]}, {"time": 2.2, "x": -55.34, "y": -12.43, "curve": [0.251, 0.34, 0.582, 0.68]}, {"time": 2.2167, "x": -32.92, "y": -16.99, "curve": [0.168, 0.55, 0.487, 1]}, {"time": 2.2833, "x": -10.23, "y": 10.23}, {"time": 2.4, "x": -3.41, "y": 20.46}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 0.75, "x": 1, "y": 1.466}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1.466}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.4167, "x": 1, "y": 1.466}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1.466}, {"time": 1.7167, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 1, "y": 1}, {"time": 2, "x": 1, "y": 0.401, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.401}, {"time": 2.4, "x": 1, "y": 0.312}]}, "mat duoi10": {"translate": [{"time": 0.45, "x": 0, "y": 0}, {"time": 0.65, "x": 0, "y": -107.61}, {"time": 0.6667, "x": 0, "y": -131.1}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.9, "x": 0, "y": -100.71}, {"time": 0.9167, "x": 0, "y": -107.61, "curve": "stepped"}, {"time": 0.9333, "x": 0, "y": -107.61}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.3, "x": 69.53, "y": -156.51}, {"time": 1.3333, "x": 0, "y": -129.56}, {"time": 1.35, "x": -5.74, "y": -137.59}, {"time": 1.3667, "x": -4.79, "y": -96.12}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.5833, "x": 0, "y": -107.61}, {"time": 1.6, "x": 0, "y": -123.27}, {"time": 1.7167, "x": 0, "y": 0}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.65, "x": 1, "y": 0.933}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.9167, "x": 1, "y": 0.933}, {"time": 1.05, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5833, "x": 1, "y": 0.933}, {"time": 1.7167, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 1, "y": 1}, {"time": 2, "x": 1, "y": 0.401, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.401}, {"time": 2.4, "x": 1, "y": 0.312}]}, "bone4": {"rotate": [{"time": 0.1667, "angle": 0}, {"time": 0.3167, "angle": 120}, {"time": 0.4667, "angle": -120}, {"time": 0.6, "angle": 0}, {"time": 0.75, "angle": 120}, {"time": 0.9, "angle": -120}, {"time": 1.0333, "angle": 0, "curve": "stepped"}, {"time": 1.25, "angle": 0}, {"time": 1.3, "angle": 120}, {"time": 1.45, "angle": -120}, {"time": 1.5833, "angle": 0}, {"time": 1.7333, "angle": 120}, {"time": 1.8167, "angle": -120}, {"time": 1.9, "angle": 0}]}, "bone5": {"rotate": [{"time": 0.3333, "angle": 0}, {"time": 0.4833, "angle": 120}, {"time": 0.6333, "angle": -120}, {"time": 0.7833, "angle": 0}, {"time": 0.9167, "angle": 120}, {"time": 1.0667, "angle": -120}, {"time": 1.2167, "angle": 0, "curve": "stepped"}, {"time": 1.4167, "angle": 0}, {"time": 1.4667, "angle": 120}, {"time": 1.5, "angle": 0}, {"time": 1.6167, "angle": -120}, {"time": 1.75, "angle": 0}, {"time": 1.8333, "angle": 120}, {"time": 1.9167, "angle": -120}, {"time": 2.0167, "angle": 0}]}, "bone6": {"rotate": [{"time": 0.2667, "angle": 0}, {"time": 0.4, "angle": 120}, {"time": 0.55, "angle": -120}, {"time": 0.7, "angle": 0}, {"time": 0.8333, "angle": 120}, {"time": 0.9833, "angle": -120}, {"time": 1.1333, "angle": 0, "curve": "stepped"}, {"time": 1.35, "angle": 0}, {"time": 1.3833, "angle": 120}, {"time": 1.4167, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0}, {"time": 1.5333, "angle": -120}, {"time": 1.6833, "angle": 0}, {"time": 1.7667, "angle": 120}, {"time": 1.8667, "angle": -120}, {"time": 1.9833, "angle": 0}]}, "no": {"scale": [{"time": 0, "x": 0.605, "y": 0.605}, {"time": 0.0833, "x": 1, "y": 1}, {"time": 1.9833, "x": 1.052, "y": 1.052}, {"time": 2.1, "x": 1.87, "y": 1.87}, {"time": 2.3167, "x": 1.052, "y": 1.052}, {"time": 2.4, "x": 0.605, "y": 0.605}]}, "no2": {"scale": [{"time": 2.1333, "x": 1, "y": 1}, {"time": 2.2833, "x": 4.931, "y": 4.931}, {"time": 2.4, "x": 1, "y": 1}]}, "ben trai10": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4167, "angle": -5.7}, {"time": 0.45, "angle": -1.2, "curve": "stepped"}, {"time": 1.85, "angle": -1.2}, {"time": 2.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.0667, "x": 34.74, "y": -72.38}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.1667, "x": 34.74, "y": -73.59}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.2833, "x": 34.74, "y": -69.94}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.4, "x": 34.74, "y": -69.94, "curve": [0.049, 0.46, 0.343, 1]}, {"time": 0.45, "x": 0, "y": 0}, {"time": 0.6, "x": 0.3, "y": 14.43, "curve": "stepped"}, {"time": 0.7, "x": 0.3, "y": 14.43}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.8833, "x": 0.3, "y": 14.43}, {"time": 0.9167, "x": 0.3, "y": 10.66}, {"time": 0.9333, "x": 0.3, "y": 29.85}, {"time": 0.9667, "x": 0.3, "y": 14.43}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": 46.73, "y": -72.38}, {"time": 1.1167, "x": 0, "y": 0}, {"time": 1.1333, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 1.15, "x": 34.74, "y": -73.59}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.2, "x": 34.47, "y": 95.69, "curve": "stepped"}, {"time": 1.2167, "x": 34.74, "y": -69.94}, {"time": 1.25, "x": -12.45, "y": 13.15}, {"time": 1.2667, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 1.2833, "x": 25.07, "y": -81.99, "curve": [0.078, 0.35, 0.388, 0.74]}, {"time": 1.3333, "x": 2.37, "y": 28.22, "curve": [0.285, 0.42, 0.618, 0.76]}, {"time": 1.35, "x": 0.74, "y": 0.97, "curve": [0.294, 0.64, 0.626, 1]}, {"time": 1.3667, "x": 0.3, "y": 14.43}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.55, "x": 0.3, "y": 14.43}, {"time": 1.6, "x": 0.3, "y": 24.07}, {"time": 1.6333, "x": 0.3, "y": 14.43}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": 34.74, "y": -72.38}, {"time": 1.75, "x": 0, "y": -12.18}, {"time": 1.7667, "x": 34.74, "y": -73.59}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": 34.74, "y": -69.94}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": 34.74, "y": -69.94, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.8667, "x": 35.32, "y": 55.99, "curve": [0.174, 0.55, 0.493, 1]}, {"time": 1.9, "x": 34.74, "y": -72.38}, {"time": 1.9167, "x": 0, "y": -7.61}, {"time": 1.9333, "x": 35.53, "y": 85.59, "curve": "stepped"}, {"time": 1.9833, "x": 34.74, "y": -73.59}, {"time": 2, "x": 0, "y": 22.33}, {"time": 2.05, "x": 34.74, "y": -69.94}, {"time": 2.1, "x": 0, "y": 30.47}, {"time": 2.1333, "x": 35.53, "y": 139.21, "curve": "stepped"}, {"time": 2.15, "x": 34.74, "y": -69.94, "curve": [0.071, 0.36, 0.379, 0.77]}, {"time": 2.2167, "x": 1.92, "y": 8.74, "curve": [0.264, 0.62, 0.595, 1]}, {"time": 2.2833, "x": 0, "y": 24.85}, {"time": 2.4, "x": 0, "y": 47.74}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 0.783}, {"time": 0.55, "x": 1, "y": 0.978}, {"time": 0.5667, "x": 1, "y": 0.967}, {"time": 0.5833, "x": 1, "y": 0.842}, {"time": 0.6, "x": 1, "y": 0.892}, {"time": 0.6167, "x": 1, "y": 0.881}, {"time": 0.6333, "x": 1, "y": 0.706}, {"time": 0.65, "x": 1, "y": 0.599}, {"time": 0.6667, "x": 1, "y": 0.068}, {"time": 0.7, "x": 1, "y": 0.781}, {"time": 0.7167, "x": 1, "y": 0.95}, {"time": 0.7333, "x": 1, "y": 0.962}, {"time": 0.75, "x": 1, "y": 0.879}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 0.83}, {"time": 0.85, "x": 1, "y": 0.84}, {"time": 0.8667, "x": 1, "y": 0.846}, {"time": 0.8833, "x": 1, "y": 0.856}, {"time": 0.9, "x": 1, "y": 0.814}, {"time": 0.9167, "x": 1, "y": 0.457}, {"time": 0.9333, "x": 1, "y": 0.068}, {"time": 0.9667, "x": 1, "y": 0.81}, {"time": 0.9833, "x": 1, "y": 0.845}, {"time": 1, "x": 1, "y": 0.865}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.2833, "x": 1, "y": 0.269}, {"time": 1.3, "x": 1, "y": 0.346}, {"time": 1.3333, "x": 1, "y": 0.264}, {"time": 1.3667, "x": 1, "y": 0.942}, {"time": 1.3833, "x": 1, "y": 1.013}, {"time": 1.4, "x": 1, "y": 0.916}, {"time": 1.4167, "x": 1, "y": 0.783}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 0.783}, {"time": 1.5167, "x": 1, "y": 0.876}, {"time": 1.5333, "x": 1, "y": 0.902}, {"time": 1.55, "x": 1, "y": 0.89}, {"time": 1.5833, "x": 1, "y": 0.457}, {"time": 1.6, "x": 1, "y": 0.068}, {"time": 1.6333, "x": 1, "y": 0.735}, {"time": 1.65, "x": 1, "y": 0.845}, {"time": 1.6667, "x": 1, "y": 0.865}, {"time": 1.7167, "x": 1, "y": 1.121}, {"time": 1.7333, "x": 1, "y": 1.215}, {"time": 1.8167, "x": 1, "y": 1.138}, {"time": 1.85, "x": 1, "y": 1.161}, {"time": 1.8667, "x": 1, "y": 1.028}, {"time": 1.9, "x": 1, "y": 1.193}, {"time": 1.9333, "x": 1, "y": 1.157}, {"time": 1.9833, "x": 1, "y": 1.028}, {"time": 2, "x": 1, "y": 0.446, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.446}, {"time": 2.4, "x": 1, "y": 0.312}]}, "ben phai10": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4167, "angle": -7.27}, {"time": 0.4333, "angle": -10.19}, {"time": 0.45, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": -29.09, "y": -64.8, "curve": "stepped"}, {"time": 0.0667, "x": -33.86, "y": 90.93}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": -29.09, "y": -63.09, "curve": "stepped"}, {"time": 0.1667, "x": -33.86, "y": 90.93}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": -29.09, "y": -64.3, "curve": "stepped"}, {"time": 0.2833, "x": -33.86, "y": 90.93}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": -29.09, "y": -65.52, "curve": "stepped"}, {"time": 0.4, "x": -33.86, "y": 90.93}, {"time": 0.45, "x": 0, "y": 0}, {"time": 0.6, "x": 0, "y": 12.49, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 12.49}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.8833, "x": 0, "y": 12.49}, {"time": 0.9167, "x": 0, "y": 2.93}, {"time": 0.9333, "x": 0, "y": 19.24}, {"time": 0.9667, "x": 0, "y": 12.49}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": -33.86, "y": 90.93}, {"time": 1.1167, "x": 0, "y": 0}, {"time": 1.1333, "x": -29.09, "y": -63.09, "curve": "stepped"}, {"time": 1.15, "x": -37.95, "y": 83.63}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.2, "x": -29.09, "y": -64.3, "curve": "stepped"}, {"time": 1.2167, "x": -33.86, "y": 90.93}, {"time": 1.25, "x": 0, "y": 0}, {"time": 1.2667, "x": -32.53, "y": -89.86, "curve": "stepped"}, {"time": 1.2833, "x": -23.7, "y": 66.88}, {"time": 1.3, "x": 56.2, "y": 30}, {"time": 1.3333, "x": -2.39, "y": 20.45}, {"time": 1.35, "x": -1.19, "y": 0.21}, {"time": 1.3667, "x": 0, "y": 12.49}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.55, "x": 0, "y": 12.49, "curve": "stepped"}, {"time": 1.6333, "x": 0, "y": 12.49}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": -33.86, "y": 90.93}, {"time": 1.75, "x": 0, "y": 0}, {"time": 1.7667, "x": -33.86, "y": 90.93}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": -33.86, "y": 90.93}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": -23.7, "y": 87.55}, {"time": 1.8667, "x": -16.75, "y": -45.96}, {"time": 1.9, "x": -33.86, "y": 90.93}, {"time": 1.9167, "x": 0, "y": 0}, {"time": 1.9333, "x": -29.09, "y": -63.09, "curve": "stepped"}, {"time": 1.9833, "x": -33.86, "y": 90.93}, {"time": 2, "x": 0, "y": 33.81}, {"time": 2.05, "x": -33.86, "y": 139.34}, {"time": 2.1, "x": 0, "y": 30.47}, {"time": 2.1333, "x": -29.09, "y": -65.52, "curve": "stepped"}, {"time": 2.15, "x": -23.7, "y": 125.57}, {"time": 2.2167, "x": -2.39, "y": 37.31}, {"time": 2.2833, "x": 0, "y": 36.33}, {"time": 2.4, "x": 6.82, "y": 37.51}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 0.791}, {"time": 0.55, "x": 1, "y": 0.957}, {"time": 0.5667, "x": 1, "y": 0.934}, {"time": 0.5833, "x": 1, "y": 0.801}, {"time": 0.6, "x": 1, "y": 0.833}, {"time": 0.6167, "x": 1, "y": 0.831}, {"time": 0.6333, "x": 1, "y": 0.674}, {"time": 0.65, "x": 1, "y": 0.584}, {"time": 0.6667, "x": 1, "y": 0.08}, {"time": 0.7, "x": 1, "y": 0.729}, {"time": 0.7167, "x": 1, "y": 0.913}, {"time": 0.7333, "x": 1, "y": 0.947}, {"time": 0.75, "x": 1, "y": 0.889}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 0.839}, {"time": 0.85, "x": 1, "y": 0.828}, {"time": 0.8667, "x": 1, "y": 0.814}, {"time": 0.8833, "x": 1, "y": 0.799}, {"time": 0.9, "x": 1, "y": 0.773}, {"time": 0.9167, "x": 1, "y": 0.446}, {"time": 0.9333, "x": 1, "y": 0.08}, {"time": 0.9667, "x": 1, "y": 0.756}, {"time": 0.9833, "x": 1, "y": 0.823}, {"time": 1, "x": 1, "y": 0.875}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.2833, "x": 1, "y": 0.284}, {"time": 1.3, "x": 1, "y": 0.367}, {"time": 1.3333, "x": 1, "y": 0.311}, {"time": 1.3667, "x": 1, "y": 0.879}, {"time": 1.3833, "x": 1, "y": 0.975}, {"time": 1.4, "x": 1, "y": 0.904}, {"time": 1.4167, "x": 1, "y": 0.791}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 0.791}, {"time": 1.5167, "x": 1, "y": 0.864}, {"time": 1.5333, "x": 1, "y": 0.868}, {"time": 1.55, "x": 1, "y": 0.831}, {"time": 1.5833, "x": 1, "y": 0.446}, {"time": 1.6, "x": 1, "y": 0.08}, {"time": 1.6333, "x": 1, "y": 0.686}, {"time": 1.65, "x": 1, "y": 0.823}, {"time": 1.6667, "x": 1, "y": 0.875}, {"time": 1.7167, "x": 1, "y": 1.122}, {"time": 1.7333, "x": 1, "y": 1.124}, {"time": 1.85, "x": 1, "y": 1}, {"time": 1.8667, "x": 1, "y": 1.155}, {"time": 1.9, "x": 1, "y": 0.996}, {"time": 1.9333, "x": 1, "y": 1.11}, {"time": 1.9833, "x": 1, "y": 0.983}, {"time": 2, "x": 1, "y": 0.422, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.422}, {"time": 2.4, "x": 1, "y": 0.312}]}, "control13": {"rotate": [{"time": 0, "angle": 0, "curve": [0.019, 0.54, 0.519, 0.83]}, {"time": 0.15, "angle": 120, "curve": [0.629, 0.07, 0.987, 0.73]}, {"time": 0.3167, "angle": -120, "curve": [0.481, 0, 0.448, 0.99]}, {"time": 0.45, "angle": 0}, {"time": 0.6667, "angle": 180}, {"time": 0.7833, "angle": 0}, {"time": 0.9333, "angle": 180}, {"time": 1.05, "angle": 0}, {"time": 1.3333, "angle": 180}, {"time": 1.45, "angle": 0}, {"time": 1.6, "angle": 180}, {"time": 1.7167, "angle": 0}, {"time": 1.95, "angle": 120}, {"time": 2.2, "angle": -120}, {"time": 2.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1, "x": 0, "y": 235.01, "curve": "stepped"}, {"time": 2, "x": 0, "y": 235.01}, {"time": 2.0333, "x": 0, "y": 329.15}, {"time": 2.1333, "x": 0, "y": 0}, {"time": 2.2, "x": 0, "y": 150.21}, {"time": 2.2833, "x": 0, "y": 0}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 0.75, "x": 1, "y": 1.466}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1.466}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.4167, "x": 1, "y": 1.466}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1.466}, {"time": 1.7167, "x": 1, "y": 1}]}, "control5": {"scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.1667, "x": 1.332, "y": 1.332, "curve": "stepped"}, {"time": 2, "x": 1.332, "y": 1.332}, {"time": 2.1333, "x": 1.348, "y": 1.348}, {"time": 2.2833, "x": 1, "y": 1}]}}, "deform": {"default": {"Rectangle 1 copy 20": {"mat tren 5": [{"time": 1.6, "offset": 6, "vertices": [6.24777, -4.31001]}]}}}}, "xi ngau bay 5": {"slots": {"10": {"attachment": [{"time": 0.0667, "name": "2"}, {"time": 0.1667, "name": "3"}, {"time": 0.2833, "name": "4"}, {"time": 0.4, "name": "1"}, {"time": 0.6667, "name": "3"}, {"time": 0.9333, "name": "1"}, {"time": 1.0833, "name": "2"}, {"time": 1.15, "name": "3"}, {"time": 1.2167, "name": "4"}, {"time": 1.2833, "name": "1"}, {"time": 1.3333, "name": "3"}, {"time": 1.6, "name": "1"}, {"time": 1.7333, "name": "2"}, {"time": 1.7667, "name": "3"}, {"time": 1.8167, "name": "4"}, {"time": 1.85, "name": "1"}, {"time": 1.9, "name": "2"}, {"time": 1.9833, "name": "3"}, {"time": 2.05, "name": "4"}, {"time": 2.15, "name": "1"}]}, "Rectangle 1 copy 20": {"attachment": [{"time": 0, "name": "mat tren 5"}, {"time": 1.6667, "name": "mat tren 1"}, {"time": 1.85, "name": "mat tren 2"}, {"time": 1.9, "name": "mat tren 1"}, {"time": 1.9333, "name": "mat tren 5"}]}, "Rectangle 1 copy 21": {"attachment": [{"time": 1.05, "name": "mat duoi 2"}, {"time": 1.5, "name": "Rectangle 1 copy 3"}, {"time": 1.6667, "name": "mat duoi 2"}]}}, "bones": {"giua10": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4167, "angle": -6.49}, {"time": 0.4333, "angle": -10.43}, {"time": 0.45, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.0667, "x": 130.24, "y": 47.4}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.1667, "x": 130.24, "y": 47.4}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.2833, "x": 130.24, "y": 47.4}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.4, "x": 130.24, "y": 47.4}, {"time": 0.45, "x": 0, "y": 0}, {"time": 0.5167, "x": 0, "y": 96.49}, {"time": 0.6, "x": 0, "y": 162.37}, {"time": 0.65, "x": 0, "y": 197.23}, {"time": 0.6667, "x": 0, "y": 231.18}, {"time": 0.7, "x": 0, "y": 162.37}, {"time": 0.75, "x": 0, "y": 96.49}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.8333, "x": 0, "y": 96.49}, {"time": 0.8833, "x": 0, "y": 162.37}, {"time": 0.9167, "x": 0, "y": 197.23}, {"time": 0.9333, "x": 0, "y": 231.18}, {"time": 0.9667, "x": 0, "y": 162.37}, {"time": 1, "x": 0, "y": 96.49}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": 130.24, "y": 47.4}, {"time": 1.1167, "x": 0, "y": 0}, {"time": 1.1333, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 1.15, "x": 130.24, "y": 47.4}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.2, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 1.2167, "x": 130.24, "y": 47.4}, {"time": 1.25, "x": 0, "y": 0}, {"time": 1.2667, "x": -126.68, "y": 22.81, "curve": "stepped"}, {"time": 1.2833, "x": 122.89, "y": 11.33, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.3, "x": 0.96, "y": 212.74, "curve": [0.174, 0.55, 0.493, 1]}, {"time": 1.3333, "x": 0, "y": 231.18}, {"time": 1.3667, "x": 0, "y": 162.37}, {"time": 1.4167, "x": 0, "y": 96.49}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.5, "x": 0, "y": 96.49}, {"time": 1.55, "x": 0, "y": 162.37}, {"time": 1.5833, "x": 0, "y": 197.23}, {"time": 1.6, "x": 0, "y": 231.18}, {"time": 1.6333, "x": 0, "y": 162.37}, {"time": 1.6667, "x": 0, "y": 96.49}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": 130.24, "y": 47.4}, {"time": 1.75, "x": 0, "y": 0}, {"time": 1.7667, "x": 130.24, "y": 47.4}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": 130.24, "y": 47.4}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": 130.24, "y": 47.4, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.8667, "x": -85.63, "y": 48.42, "curve": [0.264, 0.62, 0.595, 1]}, {"time": 1.9, "x": 130.24, "y": 47.4}, {"time": 1.9167, "x": 0, "y": 0}, {"time": 1.9333, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 1.9833, "x": 130.24, "y": 47.4}, {"time": 2, "x": 0, "y": 0}, {"time": 2.05, "x": 130.24, "y": 47.4}, {"time": 2.1, "x": 0, "y": 0}, {"time": 2.1333, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 2.15, "x": 87.25, "y": 41.76, "curve": [0.072, 0.47, 0.371, 1]}, {"time": 2.2833, "x": 0, "y": 0}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 1.232}, {"time": 0.55, "x": 1, "y": 1.374}, {"time": 0.5667, "x": 1, "y": 1.478}, {"time": 0.5833, "x": 1, "y": 1.18}, {"time": 0.6, "x": 1, "y": 1.107}, {"time": 0.6167, "x": 1, "y": 1.066}, {"time": 0.6333, "x": 1, "y": 0.83}, {"time": 0.65, "x": 1, "y": 0.666}, {"time": 0.6667, "x": 1, "y": 0.102}, {"time": 0.7, "x": 1, "y": 0.969}, {"time": 0.7167, "x": 1, "y": 1.357}, {"time": 0.7333, "x": 1, "y": 1.37}, {"time": 0.75, "x": 1, "y": 1.384}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 1.306}, {"time": 0.85, "x": 1, "y": 1.38}, {"time": 0.8667, "x": 1, "y": 1.385}, {"time": 0.8833, "x": 1, "y": 1.062}, {"time": 0.9, "x": 1, "y": 0.879}, {"time": 0.9167, "x": 1, "y": 0.509}, {"time": 0.9333, "x": 1, "y": 0.102}, {"time": 0.9667, "x": 1, "y": 1.005}, {"time": 0.9833, "x": 1, "y": 1.431}, {"time": 1, "x": 1, "y": 1.362}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.2833, "x": 1, "y": 0.31}, {"time": 1.3, "x": 1, "y": 0.406}, {"time": 1.3333, "x": 1, "y": 0.398}, {"time": 1.3667, "x": 1, "y": 1.169}, {"time": 1.3833, "x": 1, "y": 1.463}, {"time": 1.4, "x": 1, "y": 1.474}, {"time": 1.4167, "x": 1, "y": 1.232}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 1.232}, {"time": 1.5167, "x": 1, "y": 1.44}, {"time": 1.5333, "x": 1, "y": 1.443}, {"time": 1.55, "x": 1, "y": 1.104}, {"time": 1.5833, "x": 1, "y": 0.509}, {"time": 1.6, "x": 1, "y": 0.102}, {"time": 1.6333, "x": 1, "y": 0.913}, {"time": 1.65, "x": 1, "y": 1.431}, {"time": 1.6667, "x": 1, "y": 1.362}, {"time": 1.7167, "x": 1, "y": 1.203}, {"time": 1.7333, "x": 1, "y": 1.2}, {"time": 1.85, "x": 1, "y": 1.184}, {"time": 1.8667, "x": 1, "y": 1.203}, {"time": 1.9, "x": 1, "y": 1.199}, {"time": 1.9333, "x": 1, "y": 1.176}, {"time": 2, "x": 1, "y": 0.465, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.465}, {"time": 2.4, "x": 1, "y": 0.312}]}, "mat tren10": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.0667, "x": -115.92, "y": -23.14}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.1667, "x": -115.92, "y": -23.14}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.2833, "x": -115.92, "y": -23.14}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.4, "x": -115.92, "y": -23.14}, {"time": 0.45, "x": -1.86, "y": 0}, {"time": 0.4833, "x": -1.86, "y": -80.05}, {"time": 0.5, "x": -1.86, "y": -109.69}, {"time": 0.5167, "x": -1.86, "y": -121.4}, {"time": 0.65, "x": -1.86, "y": -101.97, "curve": "stepped"}, {"time": 0.6667, "x": -1.86, "y": -101.97}, {"time": 0.75, "x": -1.86, "y": -121.4}, {"time": 0.7667, "x": -1.86, "y": -79.61}, {"time": 0.7833, "x": -1.86, "y": 0}, {"time": 0.8167, "x": -1.86, "y": -86.04}, {"time": 0.8333, "x": -1.86, "y": -121.4}, {"time": 0.9167, "x": -1.86, "y": -101.97, "curve": "stepped"}, {"time": 0.9333, "x": -1.86, "y": -101.97}, {"time": 1, "x": -1.86, "y": -121.4}, {"time": 1.0167, "x": -1.12, "y": -93.49}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": -107.63, "y": -43.68}, {"time": 1.1167, "x": 2.04, "y": -24.06}, {"time": 1.1333, "x": 116.49, "y": -55.7, "curve": "stepped"}, {"time": 1.15, "x": -103.59, "y": -28.59}, {"time": 1.1667, "x": -12.33, "y": -37.73}, {"time": 1.1833, "x": 62.07, "y": -66.2}, {"time": 1.2, "x": 121.71, "y": -73.43, "curve": "stepped"}, {"time": 1.2167, "x": -116.33, "y": -67.24}, {"time": 1.2333, "x": -63.06, "y": -55.74}, {"time": 1.25, "x": -9.44, "y": -49.64}, {"time": 1.2667, "x": 110.15, "y": -102.6, "curve": "stepped"}, {"time": 1.2833, "x": -100.12, "y": -98.8, "curve": [0.049, 0.46, 0.343, 1]}, {"time": 1.3333, "x": -1.86, "y": -101.97}, {"time": 1.4167, "x": -1.86, "y": -121.4}, {"time": 1.4333, "x": -1.86, "y": -79.61}, {"time": 1.45, "x": -1.86, "y": 0}, {"time": 1.4833, "x": -1.86, "y": -86.04}, {"time": 1.5, "x": -1.86, "y": -121.4}, {"time": 1.5833, "x": -1.86, "y": -101.97, "curve": "stepped"}, {"time": 1.6, "x": -1.86, "y": -101.97}, {"time": 1.6667, "x": -1.86, "y": -121.4}, {"time": 1.6833, "x": -1.12, "y": -80.61}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": -115.92, "y": -9.43}, {"time": 1.75, "x": 0, "y": 0}, {"time": 1.7667, "x": -115.92, "y": -14}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": -115.92, "y": -14}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": -115.92, "y": -20.84, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.8667, "x": 81.6, "y": -15.26, "curve": [0.264, 0.62, 0.595, 1]}, {"time": 1.9, "x": -115.92, "y": -23.14}, {"time": 1.9167, "x": 0, "y": 0}, {"time": 1.9333, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 1.9833, "x": -115.92, "y": -23.14}, {"time": 2, "x": 0, "y": 0}, {"time": 2.05, "x": -115.92, "y": -23.14}, {"time": 2.1, "x": 0, "y": 0}, {"time": 2.1333, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 2.15, "x": -115.92, "y": -23.14, "curve": [0.184, 0.33, 0.514, 0.66]}, {"time": 2.2, "x": -55.34, "y": -12.43, "curve": [0.251, 0.34, 0.582, 0.68]}, {"time": 2.2167, "x": -32.92, "y": -16.99, "curve": [0.168, 0.55, 0.487, 1]}, {"time": 2.2833, "x": -10.23, "y": 10.23}, {"time": 2.4, "x": -3.41, "y": 20.46}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 0.75, "x": 1, "y": 1.466}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1.466}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.4167, "x": 1, "y": 1.466}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1.466}, {"time": 1.7167, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 1, "y": 1}, {"time": 2, "x": 1, "y": 0.401, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.401}, {"time": 2.4, "x": 1, "y": 0.312}]}, "mat duoi10": {"translate": [{"time": 0.45, "x": 0, "y": 0}, {"time": 0.65, "x": 0, "y": -107.61}, {"time": 0.6667, "x": 0, "y": -131.1}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.9, "x": 0, "y": -100.71}, {"time": 0.9167, "x": 0, "y": -107.61, "curve": "stepped"}, {"time": 0.9333, "x": 0, "y": -107.61}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.3, "x": 69.53, "y": -156.51}, {"time": 1.3333, "x": 0, "y": -129.56}, {"time": 1.35, "x": -5.74, "y": -137.59}, {"time": 1.3667, "x": -4.79, "y": -96.12}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.5833, "x": 0, "y": -107.61}, {"time": 1.6, "x": 0, "y": -123.27}, {"time": 1.7167, "x": 0, "y": 0}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.65, "x": 1, "y": 0.933}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.9167, "x": 1, "y": 0.933}, {"time": 1.05, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5833, "x": 1, "y": 0.933}, {"time": 1.7167, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 1, "y": 1}, {"time": 2, "x": 1, "y": 0.401, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.401}, {"time": 2.4, "x": 1, "y": 0.312}]}, "bone4": {"rotate": [{"time": 0.1667, "angle": 0}, {"time": 0.3167, "angle": 120}, {"time": 0.4667, "angle": -120}, {"time": 0.6, "angle": 0}, {"time": 0.75, "angle": 120}, {"time": 0.9, "angle": -120}, {"time": 1.0333, "angle": 0, "curve": "stepped"}, {"time": 1.25, "angle": 0}, {"time": 1.3, "angle": 120}, {"time": 1.45, "angle": -120}, {"time": 1.5833, "angle": 0}, {"time": 1.7333, "angle": 120}, {"time": 1.8167, "angle": -120}, {"time": 1.9, "angle": 0}]}, "bone5": {"rotate": [{"time": 0.3333, "angle": 0}, {"time": 0.4833, "angle": 120}, {"time": 0.6333, "angle": -120}, {"time": 0.7833, "angle": 0}, {"time": 0.9167, "angle": 120}, {"time": 1.0667, "angle": -120}, {"time": 1.2167, "angle": 0, "curve": "stepped"}, {"time": 1.4167, "angle": 0}, {"time": 1.4667, "angle": 120}, {"time": 1.5, "angle": 0}, {"time": 1.6167, "angle": -120}, {"time": 1.75, "angle": 0}, {"time": 1.8333, "angle": 120}, {"time": 1.9167, "angle": -120}, {"time": 2.0167, "angle": 0}]}, "bone6": {"rotate": [{"time": 0.2667, "angle": 0}, {"time": 0.4, "angle": 120}, {"time": 0.55, "angle": -120}, {"time": 0.7, "angle": 0}, {"time": 0.8333, "angle": 120}, {"time": 0.9833, "angle": -120}, {"time": 1.1333, "angle": 0, "curve": "stepped"}, {"time": 1.35, "angle": 0}, {"time": 1.3833, "angle": 120}, {"time": 1.4167, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0}, {"time": 1.5333, "angle": -120}, {"time": 1.6833, "angle": 0}, {"time": 1.7667, "angle": 120}, {"time": 1.8667, "angle": -120}, {"time": 1.9833, "angle": 0}]}, "no": {"scale": [{"time": 0, "x": 0.605, "y": 0.605}, {"time": 0.0833, "x": 1, "y": 1}, {"time": 1.9833, "x": 1.052, "y": 1.052}, {"time": 2.1, "x": 1.87, "y": 1.87}, {"time": 2.3167, "x": 1.052, "y": 1.052}, {"time": 2.4, "x": 0.605, "y": 0.605}]}, "no2": {"scale": [{"time": 2.1333, "x": 1, "y": 1}, {"time": 2.2833, "x": 4.931, "y": 4.931}, {"time": 2.4, "x": 1, "y": 1}]}, "ben trai10": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4167, "angle": -5.7}, {"time": 0.45, "angle": -1.2, "curve": "stepped"}, {"time": 1.85, "angle": -1.2}, {"time": 2.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.0667, "x": 34.74, "y": -72.38}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.1667, "x": 34.74, "y": -73.59}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.2833, "x": 34.74, "y": -69.94}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.4, "x": 34.74, "y": -69.94, "curve": [0.049, 0.46, 0.343, 1]}, {"time": 0.45, "x": 0, "y": 0}, {"time": 0.6, "x": 0.3, "y": 14.43, "curve": "stepped"}, {"time": 0.7, "x": 0.3, "y": 14.43}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.8833, "x": 0.3, "y": 14.43}, {"time": 0.9167, "x": 0.3, "y": 10.66}, {"time": 0.9333, "x": 0.3, "y": 29.85}, {"time": 0.9667, "x": 0.3, "y": 14.43}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": 46.73, "y": -72.38}, {"time": 1.1167, "x": 0, "y": 0}, {"time": 1.1333, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 1.15, "x": 34.74, "y": -73.59}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.2, "x": 34.47, "y": 95.69, "curve": "stepped"}, {"time": 1.2167, "x": 34.74, "y": -69.94}, {"time": 1.25, "x": -12.45, "y": 13.15}, {"time": 1.2667, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 1.2833, "x": 25.07, "y": -81.99, "curve": [0.078, 0.35, 0.388, 0.74]}, {"time": 1.3333, "x": 2.37, "y": 28.22, "curve": [0.285, 0.42, 0.618, 0.76]}, {"time": 1.35, "x": 0.74, "y": 0.97, "curve": [0.294, 0.64, 0.626, 1]}, {"time": 1.3667, "x": 0.3, "y": 14.43}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.55, "x": 0.3, "y": 14.43}, {"time": 1.6, "x": 0.3, "y": 24.07}, {"time": 1.6333, "x": 0.3, "y": 14.43}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": 34.74, "y": -72.38}, {"time": 1.75, "x": 0, "y": -12.18}, {"time": 1.7667, "x": 34.74, "y": -73.59}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": 34.74, "y": -69.94}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": 34.74, "y": -69.94, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.8667, "x": 35.32, "y": 55.99, "curve": [0.174, 0.55, 0.493, 1]}, {"time": 1.9, "x": 34.74, "y": -72.38}, {"time": 1.9167, "x": 0, "y": -7.61}, {"time": 1.9333, "x": 35.53, "y": 85.59, "curve": "stepped"}, {"time": 1.9833, "x": 34.74, "y": -73.59}, {"time": 2, "x": 0, "y": 22.33}, {"time": 2.05, "x": 34.74, "y": -69.94}, {"time": 2.1, "x": 0, "y": 30.47}, {"time": 2.1333, "x": 35.53, "y": 139.21, "curve": "stepped"}, {"time": 2.15, "x": 34.74, "y": -69.94, "curve": [0.071, 0.36, 0.379, 0.77]}, {"time": 2.2167, "x": 1.92, "y": 8.74, "curve": [0.264, 0.62, 0.595, 1]}, {"time": 2.2833, "x": 0, "y": 24.85}, {"time": 2.4, "x": 0, "y": 47.74}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 0.783}, {"time": 0.55, "x": 1, "y": 0.978}, {"time": 0.5667, "x": 1, "y": 0.967}, {"time": 0.5833, "x": 1, "y": 0.842}, {"time": 0.6, "x": 1, "y": 0.892}, {"time": 0.6167, "x": 1, "y": 0.881}, {"time": 0.6333, "x": 1, "y": 0.706}, {"time": 0.65, "x": 1, "y": 0.599}, {"time": 0.6667, "x": 1, "y": 0.068}, {"time": 0.7, "x": 1, "y": 0.781}, {"time": 0.7167, "x": 1, "y": 0.95}, {"time": 0.7333, "x": 1, "y": 0.962}, {"time": 0.75, "x": 1, "y": 0.879}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 0.83}, {"time": 0.85, "x": 1, "y": 0.84}, {"time": 0.8667, "x": 1, "y": 0.846}, {"time": 0.8833, "x": 1, "y": 0.856}, {"time": 0.9, "x": 1, "y": 0.814}, {"time": 0.9167, "x": 1, "y": 0.457}, {"time": 0.9333, "x": 1, "y": 0.068}, {"time": 0.9667, "x": 1, "y": 0.81}, {"time": 0.9833, "x": 1, "y": 0.845}, {"time": 1, "x": 1, "y": 0.865}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.2833, "x": 1, "y": 0.269}, {"time": 1.3, "x": 1, "y": 0.346}, {"time": 1.3333, "x": 1, "y": 0.264}, {"time": 1.3667, "x": 1, "y": 0.942}, {"time": 1.3833, "x": 1, "y": 1.013}, {"time": 1.4, "x": 1, "y": 0.916}, {"time": 1.4167, "x": 1, "y": 0.783}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 0.783}, {"time": 1.5167, "x": 1, "y": 0.876}, {"time": 1.5333, "x": 1, "y": 0.902}, {"time": 1.55, "x": 1, "y": 0.89}, {"time": 1.5833, "x": 1, "y": 0.457}, {"time": 1.6, "x": 1, "y": 0.068}, {"time": 1.6333, "x": 1, "y": 0.735}, {"time": 1.65, "x": 1, "y": 0.845}, {"time": 1.6667, "x": 1, "y": 0.865}, {"time": 1.7167, "x": 1, "y": 1.121}, {"time": 1.7333, "x": 1, "y": 1.215}, {"time": 1.8167, "x": 1, "y": 1.138}, {"time": 1.85, "x": 1, "y": 1.161}, {"time": 1.8667, "x": 1, "y": 1.028}, {"time": 1.9, "x": 1, "y": 1.193}, {"time": 1.9333, "x": 1, "y": 1.157}, {"time": 1.9833, "x": 1, "y": 1.028}, {"time": 2, "x": 1, "y": 0.446, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.446}, {"time": 2.4, "x": 1, "y": 0.312}]}, "ben phai10": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4167, "angle": -7.27}, {"time": 0.4333, "angle": -10.19}, {"time": 0.45, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": -29.09, "y": -64.8, "curve": "stepped"}, {"time": 0.0667, "x": -33.86, "y": 90.93}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": -29.09, "y": -63.09, "curve": "stepped"}, {"time": 0.1667, "x": -33.86, "y": 90.93}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": -29.09, "y": -64.3, "curve": "stepped"}, {"time": 0.2833, "x": -33.86, "y": 90.93}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": -29.09, "y": -65.52, "curve": "stepped"}, {"time": 0.4, "x": -33.86, "y": 90.93}, {"time": 0.45, "x": 0, "y": 0}, {"time": 0.6, "x": 0, "y": 12.49, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 12.49}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.8833, "x": 0, "y": 12.49}, {"time": 0.9167, "x": 0, "y": 2.93}, {"time": 0.9333, "x": 0, "y": 19.24}, {"time": 0.9667, "x": 0, "y": 12.49}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": -33.86, "y": 90.93}, {"time": 1.1167, "x": 0, "y": 0}, {"time": 1.1333, "x": -29.09, "y": -63.09, "curve": "stepped"}, {"time": 1.15, "x": -37.95, "y": 83.63}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.2, "x": -29.09, "y": -64.3, "curve": "stepped"}, {"time": 1.2167, "x": -33.86, "y": 90.93}, {"time": 1.25, "x": 0, "y": 0}, {"time": 1.2667, "x": -32.53, "y": -89.86, "curve": "stepped"}, {"time": 1.2833, "x": -23.7, "y": 66.88}, {"time": 1.3, "x": 56.2, "y": 30}, {"time": 1.3333, "x": -2.39, "y": 20.45}, {"time": 1.35, "x": -1.19, "y": 0.21}, {"time": 1.3667, "x": 0, "y": 12.49}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.55, "x": 0, "y": 12.49, "curve": "stepped"}, {"time": 1.6333, "x": 0, "y": 12.49}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": -33.86, "y": 90.93}, {"time": 1.75, "x": 0, "y": 0}, {"time": 1.7667, "x": -33.86, "y": 90.93}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": -33.86, "y": 90.93}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": -23.7, "y": 87.55}, {"time": 1.8667, "x": -16.75, "y": -45.96}, {"time": 1.9, "x": -33.86, "y": 90.93}, {"time": 1.9167, "x": 0, "y": 0}, {"time": 1.9333, "x": -29.09, "y": -63.09, "curve": "stepped"}, {"time": 1.9833, "x": -33.86, "y": 90.93}, {"time": 2, "x": 0, "y": 33.81}, {"time": 2.05, "x": -33.86, "y": 139.34}, {"time": 2.1, "x": 0, "y": 30.47}, {"time": 2.1333, "x": -29.09, "y": -65.52, "curve": "stepped"}, {"time": 2.15, "x": -23.7, "y": 125.57}, {"time": 2.2167, "x": -2.39, "y": 37.31}, {"time": 2.2833, "x": 0, "y": 36.33}, {"time": 2.4, "x": 6.82, "y": 37.51}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 0.791}, {"time": 0.55, "x": 1, "y": 0.957}, {"time": 0.5667, "x": 1, "y": 0.934}, {"time": 0.5833, "x": 1, "y": 0.801}, {"time": 0.6, "x": 1, "y": 0.833}, {"time": 0.6167, "x": 1, "y": 0.831}, {"time": 0.6333, "x": 1, "y": 0.674}, {"time": 0.65, "x": 1, "y": 0.584}, {"time": 0.6667, "x": 1, "y": 0.08}, {"time": 0.7, "x": 1, "y": 0.729}, {"time": 0.7167, "x": 1, "y": 0.913}, {"time": 0.7333, "x": 1, "y": 0.947}, {"time": 0.75, "x": 1, "y": 0.889}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 0.839}, {"time": 0.85, "x": 1, "y": 0.828}, {"time": 0.8667, "x": 1, "y": 0.814}, {"time": 0.8833, "x": 1, "y": 0.799}, {"time": 0.9, "x": 1, "y": 0.773}, {"time": 0.9167, "x": 1, "y": 0.446}, {"time": 0.9333, "x": 1, "y": 0.08}, {"time": 0.9667, "x": 1, "y": 0.756}, {"time": 0.9833, "x": 1, "y": 0.823}, {"time": 1, "x": 1, "y": 0.875}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.2833, "x": 1, "y": 0.284}, {"time": 1.3, "x": 1, "y": 0.367}, {"time": 1.3333, "x": 1, "y": 0.311}, {"time": 1.3667, "x": 1, "y": 0.879}, {"time": 1.3833, "x": 1, "y": 0.975}, {"time": 1.4, "x": 1, "y": 0.904}, {"time": 1.4167, "x": 1, "y": 0.791}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 0.791}, {"time": 1.5167, "x": 1, "y": 0.864}, {"time": 1.5333, "x": 1, "y": 0.868}, {"time": 1.55, "x": 1, "y": 0.831}, {"time": 1.5833, "x": 1, "y": 0.446}, {"time": 1.6, "x": 1, "y": 0.08}, {"time": 1.6333, "x": 1, "y": 0.686}, {"time": 1.65, "x": 1, "y": 0.823}, {"time": 1.6667, "x": 1, "y": 0.875}, {"time": 1.7167, "x": 1, "y": 1.122}, {"time": 1.7333, "x": 1, "y": 1.124}, {"time": 1.85, "x": 1, "y": 1}, {"time": 1.8667, "x": 1, "y": 1.155}, {"time": 1.9, "x": 1, "y": 0.996}, {"time": 1.9333, "x": 1, "y": 1.11}, {"time": 1.9833, "x": 1, "y": 0.983}, {"time": 2, "x": 1, "y": 0.422, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.422}, {"time": 2.4, "x": 1, "y": 0.312}]}, "control13": {"rotate": [{"time": 0, "angle": 0, "curve": [0.019, 0.54, 0.519, 0.83]}, {"time": 0.15, "angle": 120, "curve": [0.629, 0.07, 0.987, 0.73]}, {"time": 0.3167, "angle": -120, "curve": [0.481, 0, 0.448, 0.99]}, {"time": 0.45, "angle": 0}, {"time": 0.6667, "angle": 180}, {"time": 0.7833, "angle": 0}, {"time": 0.9333, "angle": 180}, {"time": 1.05, "angle": 0}, {"time": 1.3333, "angle": 180}, {"time": 1.45, "angle": 0}, {"time": 1.6, "angle": 180}, {"time": 1.7167, "angle": 0}, {"time": 1.95, "angle": 120}, {"time": 2.2, "angle": -120}, {"time": 2.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1, "x": 0, "y": 235.01, "curve": "stepped"}, {"time": 2, "x": 0, "y": 235.01}, {"time": 2.0333, "x": 0, "y": 329.15}, {"time": 2.1333, "x": 0, "y": 0}, {"time": 2.2, "x": 0, "y": 150.21}, {"time": 2.2833, "x": 0, "y": 0}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 0.75, "x": 1, "y": 1.466}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1.466}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.4167, "x": 1, "y": 1.466}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1.466}, {"time": 1.7167, "x": 1, "y": 1}]}, "control5": {"scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.1667, "x": 1.332, "y": 1.332, "curve": "stepped"}, {"time": 2, "x": 1.332, "y": 1.332}, {"time": 2.1333, "x": 1.348, "y": 1.348}, {"time": 2.2833, "x": 1, "y": 1}]}}, "deform": {"default": {"Rectangle 1 copy 20": {"mat tren 5": [{"time": 1.6, "offset": 6, "vertices": [6.24777, -4.31001]}]}}}}, "xi ngau bay 6": {"slots": {"10": {"attachment": [{"time": 0.0667, "name": "2"}, {"time": 0.1667, "name": "3"}, {"time": 0.2833, "name": "4"}, {"time": 0.4, "name": "1"}, {"time": 0.6667, "name": "3"}, {"time": 0.9333, "name": "1"}, {"time": 1.0833, "name": "2"}, {"time": 1.15, "name": "3"}, {"time": 1.2167, "name": "4"}, {"time": 1.2833, "name": "1"}, {"time": 1.3333, "name": "3"}, {"time": 1.6, "name": "1"}, {"time": 1.7333, "name": "2"}, {"time": 1.7667, "name": "3"}, {"time": 1.8167, "name": "4"}, {"time": 1.85, "name": "1"}, {"time": 1.9, "name": "2"}, {"time": 1.9833, "name": "3"}, {"time": 2.05, "name": "4"}, {"time": 2.15, "name": "1"}]}, "Rectangle 1 copy 20": {"attachment": [{"time": 1.65, "name": "mat tren 5"}, {"time": 1.6667, "name": "mat tren 1"}, {"time": 1.85, "name": "mat tren 2"}, {"time": 1.9, "name": "mat tren 1"}, {"time": 1.9333, "name": "mat tren 5"}, {"time": 2.2333, "name": "mat tren 6"}]}, "Rectangle 1 copy 21": {"attachment": [{"time": 1.05, "name": "mat duoi 2"}, {"time": 1.5, "name": "Rectangle 1 copy 3"}, {"time": 1.6667, "name": "mat duoi 2"}]}}, "bones": {"giua10": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4167, "angle": -6.49}, {"time": 0.4333, "angle": -10.43}, {"time": 0.45, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.0667, "x": 130.24, "y": 47.4}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.1667, "x": 130.24, "y": 47.4}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.2833, "x": 130.24, "y": 47.4}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 0.4, "x": 130.24, "y": 47.4}, {"time": 0.45, "x": 0, "y": 0}, {"time": 0.5167, "x": 0, "y": 96.49}, {"time": 0.6, "x": 0, "y": 162.37}, {"time": 0.65, "x": 0, "y": 197.23}, {"time": 0.6667, "x": 0, "y": 231.18}, {"time": 0.7, "x": 0, "y": 162.37}, {"time": 0.75, "x": 0, "y": 96.49}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.8333, "x": 0, "y": 96.49}, {"time": 0.8833, "x": 0, "y": 162.37}, {"time": 0.9167, "x": 0, "y": 197.23}, {"time": 0.9333, "x": 0, "y": 231.18}, {"time": 0.9667, "x": 0, "y": 162.37}, {"time": 1, "x": 0, "y": 96.49}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": 130.24, "y": 47.4}, {"time": 1.1167, "x": 0, "y": 0}, {"time": 1.1333, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 1.15, "x": 130.24, "y": 47.4}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.2, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 1.2167, "x": 130.24, "y": 47.4}, {"time": 1.25, "x": 0, "y": 0}, {"time": 1.2667, "x": -126.68, "y": 22.81, "curve": "stepped"}, {"time": 1.2833, "x": 122.89, "y": 11.33, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.3, "x": 0.96, "y": 212.74, "curve": [0.174, 0.55, 0.493, 1]}, {"time": 1.3333, "x": 0, "y": 231.18}, {"time": 1.3667, "x": 0, "y": 162.37}, {"time": 1.4167, "x": 0, "y": 96.49}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.5, "x": 0, "y": 96.49}, {"time": 1.55, "x": 0, "y": 162.37}, {"time": 1.5833, "x": 0, "y": 197.23}, {"time": 1.6, "x": 0, "y": 231.18}, {"time": 1.6333, "x": 0, "y": 162.37}, {"time": 1.6667, "x": 0, "y": 96.49}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": 130.24, "y": 47.4}, {"time": 1.75, "x": 0, "y": 0}, {"time": 1.7667, "x": 130.24, "y": 47.4}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": 130.24, "y": 47.4}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": 130.24, "y": 47.4, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.8667, "x": -85.63, "y": 48.42, "curve": [0.264, 0.62, 0.595, 1]}, {"time": 1.9, "x": 130.24, "y": 47.4}, {"time": 1.9167, "x": 0, "y": 0}, {"time": 1.9333, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 1.9833, "x": 130.24, "y": 47.4}, {"time": 2, "x": 0, "y": 0}, {"time": 2.05, "x": 130.24, "y": 47.4}, {"time": 2.1, "x": 0, "y": 0}, {"time": 2.1333, "x": -121.7, "y": 48.48, "curve": "stepped"}, {"time": 2.15, "x": 87.25, "y": 41.76, "curve": [0.072, 0.47, 0.371, 1]}, {"time": 2.2833, "x": 0, "y": 0}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 1.232}, {"time": 0.55, "x": 1, "y": 1.374}, {"time": 0.5667, "x": 1, "y": 1.478}, {"time": 0.5833, "x": 1, "y": 1.18}, {"time": 0.6, "x": 1, "y": 1.107}, {"time": 0.6167, "x": 1, "y": 1.066}, {"time": 0.6333, "x": 1, "y": 0.83}, {"time": 0.65, "x": 1, "y": 0.666}, {"time": 0.6667, "x": 1, "y": 0.102}, {"time": 0.7, "x": 1, "y": 0.969}, {"time": 0.7167, "x": 1, "y": 1.357}, {"time": 0.7333, "x": 1, "y": 1.37}, {"time": 0.75, "x": 1, "y": 1.384}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 1.306}, {"time": 0.85, "x": 1, "y": 1.38}, {"time": 0.8667, "x": 1, "y": 1.385}, {"time": 0.8833, "x": 1, "y": 1.062}, {"time": 0.9, "x": 1, "y": 0.879}, {"time": 0.9167, "x": 1, "y": 0.509}, {"time": 0.9333, "x": 1, "y": 0.102}, {"time": 0.9667, "x": 1, "y": 1.005}, {"time": 0.9833, "x": 1, "y": 1.431}, {"time": 1, "x": 1, "y": 1.362}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.2833, "x": 1, "y": 0.31}, {"time": 1.3, "x": 1, "y": 0.406}, {"time": 1.3333, "x": 1, "y": 0.398}, {"time": 1.3667, "x": 1, "y": 1.169}, {"time": 1.3833, "x": 1, "y": 1.463}, {"time": 1.4, "x": 1, "y": 1.474}, {"time": 1.4167, "x": 1, "y": 1.232}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 1.232}, {"time": 1.5167, "x": 1, "y": 1.44}, {"time": 1.5333, "x": 1, "y": 1.443}, {"time": 1.55, "x": 1, "y": 1.104}, {"time": 1.5833, "x": 1, "y": 0.509}, {"time": 1.6, "x": 1, "y": 0.102}, {"time": 1.6333, "x": 1, "y": 0.913}, {"time": 1.65, "x": 1, "y": 1.431}, {"time": 1.6667, "x": 1, "y": 1.362}, {"time": 1.7167, "x": 1, "y": 1.203}, {"time": 1.7333, "x": 1, "y": 1.2}, {"time": 1.85, "x": 1, "y": 1.184}, {"time": 1.8667, "x": 1, "y": 1.203}, {"time": 1.9, "x": 1, "y": 1.199}, {"time": 1.9333, "x": 1, "y": 1.176}, {"time": 2, "x": 1, "y": 0.465, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.465}, {"time": 2.4, "x": 1, "y": 0.312}]}, "mat tren10": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.0667, "x": -115.92, "y": -23.14}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.1667, "x": -115.92, "y": -23.14}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.2833, "x": -115.92, "y": -23.14}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 0.4, "x": -115.92, "y": -23.14}, {"time": 0.45, "x": -1.86, "y": 0}, {"time": 0.4833, "x": -1.86, "y": -80.05}, {"time": 0.5, "x": -1.86, "y": -109.69}, {"time": 0.5167, "x": -1.86, "y": -121.4}, {"time": 0.65, "x": -1.86, "y": -101.97, "curve": "stepped"}, {"time": 0.6667, "x": -1.86, "y": -101.97}, {"time": 0.75, "x": -1.86, "y": -121.4}, {"time": 0.7667, "x": -1.86, "y": -79.61}, {"time": 0.7833, "x": -1.86, "y": 0}, {"time": 0.8167, "x": -1.86, "y": -86.04}, {"time": 0.8333, "x": -1.86, "y": -121.4}, {"time": 0.9167, "x": -1.86, "y": -101.97, "curve": "stepped"}, {"time": 0.9333, "x": -1.86, "y": -101.97}, {"time": 1, "x": -1.86, "y": -121.4}, {"time": 1.0167, "x": -1.12, "y": -93.49}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": -107.63, "y": -43.68}, {"time": 1.1167, "x": 2.04, "y": -24.06}, {"time": 1.1333, "x": 116.49, "y": -55.7, "curve": "stepped"}, {"time": 1.15, "x": -103.59, "y": -28.59}, {"time": 1.1667, "x": -12.33, "y": -37.73}, {"time": 1.1833, "x": 62.07, "y": -66.2}, {"time": 1.2, "x": 121.71, "y": -73.43, "curve": "stepped"}, {"time": 1.2167, "x": -116.33, "y": -67.24}, {"time": 1.2333, "x": -63.06, "y": -55.74}, {"time": 1.25, "x": -9.44, "y": -49.64}, {"time": 1.2667, "x": 110.15, "y": -102.6, "curve": "stepped"}, {"time": 1.2833, "x": -100.12, "y": -98.8, "curve": [0.049, 0.46, 0.343, 1]}, {"time": 1.3333, "x": -1.86, "y": -101.97}, {"time": 1.4167, "x": -1.86, "y": -121.4}, {"time": 1.4333, "x": -1.86, "y": -79.61}, {"time": 1.45, "x": -1.86, "y": 0}, {"time": 1.4833, "x": -1.86, "y": -86.04}, {"time": 1.5, "x": -1.86, "y": -121.4}, {"time": 1.5833, "x": -1.86, "y": -101.97, "curve": "stepped"}, {"time": 1.6, "x": -1.86, "y": -101.97}, {"time": 1.6667, "x": -1.86, "y": -121.4}, {"time": 1.6833, "x": -1.12, "y": -80.61}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": -115.92, "y": -9.43}, {"time": 1.75, "x": 0, "y": 0}, {"time": 1.7667, "x": -115.92, "y": -14}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": -115.92, "y": -14}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": -115.92, "y": -20.84, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.8667, "x": 81.6, "y": -15.26, "curve": [0.264, 0.62, 0.595, 1]}, {"time": 1.9, "x": -115.92, "y": -23.14}, {"time": 1.9167, "x": 0, "y": 0}, {"time": 1.9333, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 1.9833, "x": -115.92, "y": -23.14}, {"time": 2, "x": 0, "y": 0}, {"time": 2.05, "x": -115.92, "y": -23.14}, {"time": 2.1, "x": 0, "y": 0}, {"time": 2.1333, "x": 116.02, "y": -21.82, "curve": "stepped"}, {"time": 2.15, "x": -115.92, "y": -23.14, "curve": [0.184, 0.33, 0.514, 0.66]}, {"time": 2.2, "x": -55.34, "y": -12.43, "curve": [0.251, 0.34, 0.582, 0.68]}, {"time": 2.2167, "x": -32.92, "y": -16.99, "curve": [0.168, 0.55, 0.487, 1]}, {"time": 2.2833, "x": -10.23, "y": 10.23}, {"time": 2.4, "x": -3.41, "y": 20.46}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 0.75, "x": 1, "y": 1.466}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1.466}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.4167, "x": 1, "y": 1.466}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1.466}, {"time": 1.7167, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 1, "y": 1}, {"time": 2, "x": 1, "y": 0.401, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.401}, {"time": 2.4, "x": 1, "y": 0.312}]}, "mat duoi10": {"translate": [{"time": 0.45, "x": 0, "y": 0}, {"time": 0.65, "x": 0, "y": -107.61}, {"time": 0.6667, "x": 0, "y": -131.1}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.9, "x": 0, "y": -100.71}, {"time": 0.9167, "x": 0, "y": -107.61, "curve": "stepped"}, {"time": 0.9333, "x": 0, "y": -107.61}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.3, "x": 69.53, "y": -156.51}, {"time": 1.3333, "x": 0, "y": -129.56}, {"time": 1.35, "x": -5.74, "y": -137.59}, {"time": 1.3667, "x": -4.79, "y": -96.12}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.5833, "x": 0, "y": -107.61}, {"time": 1.6, "x": 0, "y": -123.27}, {"time": 1.7167, "x": 0, "y": 0}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.65, "x": 1, "y": 0.933}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.9167, "x": 1, "y": 0.933}, {"time": 1.05, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5833, "x": 1, "y": 0.933}, {"time": 1.7167, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 1, "y": 1}, {"time": 2, "x": 1, "y": 0.401, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.401}, {"time": 2.4, "x": 1, "y": 0.312}]}, "bone4": {"rotate": [{"time": 0.1667, "angle": 0}, {"time": 0.3167, "angle": 120}, {"time": 0.4667, "angle": -120}, {"time": 0.6, "angle": 0}, {"time": 0.75, "angle": 120}, {"time": 0.9, "angle": -120}, {"time": 1.0333, "angle": 0, "curve": "stepped"}, {"time": 1.25, "angle": 0}, {"time": 1.3, "angle": 120}, {"time": 1.45, "angle": -120}, {"time": 1.5833, "angle": 0}, {"time": 1.7333, "angle": 120}, {"time": 1.8167, "angle": -120}, {"time": 1.9, "angle": 0}]}, "bone5": {"rotate": [{"time": 0.3333, "angle": 0}, {"time": 0.4833, "angle": 120}, {"time": 0.6333, "angle": -120}, {"time": 0.7833, "angle": 0}, {"time": 0.9167, "angle": 120}, {"time": 1.0667, "angle": -120}, {"time": 1.2167, "angle": 0, "curve": "stepped"}, {"time": 1.4167, "angle": 0}, {"time": 1.4667, "angle": 120}, {"time": 1.5, "angle": 0}, {"time": 1.6167, "angle": -120}, {"time": 1.75, "angle": 0}, {"time": 1.8333, "angle": 120}, {"time": 1.9167, "angle": -120}, {"time": 2.0167, "angle": 0}]}, "bone6": {"rotate": [{"time": 0.2667, "angle": 0}, {"time": 0.4, "angle": 120}, {"time": 0.55, "angle": -120}, {"time": 0.7, "angle": 0}, {"time": 0.8333, "angle": 120}, {"time": 0.9833, "angle": -120}, {"time": 1.1333, "angle": 0, "curve": "stepped"}, {"time": 1.35, "angle": 0}, {"time": 1.3833, "angle": 120}, {"time": 1.4167, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0}, {"time": 1.5333, "angle": -120}, {"time": 1.6833, "angle": 0}, {"time": 1.7667, "angle": 120}, {"time": 1.8667, "angle": -120}, {"time": 1.9833, "angle": 0}]}, "no": {"scale": [{"time": 0, "x": 0.605, "y": 0.605}, {"time": 0.0833, "x": 1, "y": 1}, {"time": 1.9833, "x": 1.052, "y": 1.052}, {"time": 2.1, "x": 1.87, "y": 1.87}, {"time": 2.3167, "x": 1.052, "y": 1.052}, {"time": 2.4, "x": 0.605, "y": 0.605}]}, "no2": {"scale": [{"time": 2.1333, "x": 1, "y": 1}, {"time": 2.2833, "x": 4.931, "y": 4.931}, {"time": 2.4, "x": 1, "y": 1}]}, "ben trai10": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4167, "angle": -5.7}, {"time": 0.45, "angle": -1.2, "curve": "stepped"}, {"time": 1.85, "angle": -1.2}, {"time": 2.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.0667, "x": 34.74, "y": -72.38}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.1667, "x": 34.74, "y": -73.59}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.2833, "x": 34.74, "y": -69.94}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 0.4, "x": 34.74, "y": -69.94, "curve": [0.049, 0.46, 0.343, 1]}, {"time": 0.45, "x": 0, "y": 0}, {"time": 0.6, "x": 0.3, "y": 14.43, "curve": "stepped"}, {"time": 0.7, "x": 0.3, "y": 14.43}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.8833, "x": 0.3, "y": 14.43}, {"time": 0.9167, "x": 0.3, "y": 10.66}, {"time": 0.9333, "x": 0.3, "y": 29.85}, {"time": 0.9667, "x": 0.3, "y": 14.43}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": 46.73, "y": -72.38}, {"time": 1.1167, "x": 0, "y": 0}, {"time": 1.1333, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 1.15, "x": 34.74, "y": -73.59}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.2, "x": 34.47, "y": 95.69, "curve": "stepped"}, {"time": 1.2167, "x": 34.74, "y": -69.94}, {"time": 1.25, "x": -12.45, "y": 13.15}, {"time": 1.2667, "x": 35.53, "y": 86.08, "curve": "stepped"}, {"time": 1.2833, "x": 25.07, "y": -81.99, "curve": [0.078, 0.35, 0.388, 0.74]}, {"time": 1.3333, "x": 2.37, "y": 28.22, "curve": [0.285, 0.42, 0.618, 0.76]}, {"time": 1.35, "x": 0.74, "y": 0.97, "curve": [0.294, 0.64, 0.626, 1]}, {"time": 1.3667, "x": 0.3, "y": 14.43}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.55, "x": 0.3, "y": 14.43}, {"time": 1.6, "x": 0.3, "y": 24.07}, {"time": 1.6333, "x": 0.3, "y": 14.43}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": 34.74, "y": -72.38}, {"time": 1.75, "x": 0, "y": -12.18}, {"time": 1.7667, "x": 34.74, "y": -73.59}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": 34.74, "y": -69.94}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": 34.74, "y": -69.94, "curve": [0.121, 0.33, 0.443, 0.68]}, {"time": 1.8667, "x": 35.32, "y": 55.99, "curve": [0.174, 0.55, 0.493, 1]}, {"time": 1.9, "x": 34.74, "y": -72.38}, {"time": 1.9167, "x": 0, "y": -7.61}, {"time": 1.9333, "x": 35.53, "y": 85.59, "curve": "stepped"}, {"time": 1.9833, "x": 34.74, "y": -73.59}, {"time": 2, "x": 0, "y": 22.33}, {"time": 2.05, "x": 34.74, "y": -69.94}, {"time": 2.1, "x": 0, "y": 30.47}, {"time": 2.1333, "x": 35.53, "y": 139.21, "curve": "stepped"}, {"time": 2.15, "x": 34.74, "y": -69.94, "curve": [0.071, 0.36, 0.379, 0.77]}, {"time": 2.2167, "x": 1.92, "y": 8.74, "curve": [0.264, 0.62, 0.595, 1]}, {"time": 2.2833, "x": 0, "y": 24.85}, {"time": 2.4, "x": 0, "y": 47.74}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 0.783}, {"time": 0.55, "x": 1, "y": 0.978}, {"time": 0.5667, "x": 1, "y": 0.967}, {"time": 0.5833, "x": 1, "y": 0.842}, {"time": 0.6, "x": 1, "y": 0.892}, {"time": 0.6167, "x": 1, "y": 0.881}, {"time": 0.6333, "x": 1, "y": 0.706}, {"time": 0.65, "x": 1, "y": 0.599}, {"time": 0.6667, "x": 1, "y": 0.068}, {"time": 0.7, "x": 1, "y": 0.781}, {"time": 0.7167, "x": 1, "y": 0.95}, {"time": 0.7333, "x": 1, "y": 0.962}, {"time": 0.75, "x": 1, "y": 0.879}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 0.83}, {"time": 0.85, "x": 1, "y": 0.84}, {"time": 0.8667, "x": 1, "y": 0.846}, {"time": 0.8833, "x": 1, "y": 0.856}, {"time": 0.9, "x": 1, "y": 0.814}, {"time": 0.9167, "x": 1, "y": 0.457}, {"time": 0.9333, "x": 1, "y": 0.068}, {"time": 0.9667, "x": 1, "y": 0.81}, {"time": 0.9833, "x": 1, "y": 0.845}, {"time": 1, "x": 1, "y": 0.865}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.2833, "x": 1, "y": 0.269}, {"time": 1.3, "x": 1, "y": 0.346}, {"time": 1.3333, "x": 1, "y": 0.264}, {"time": 1.3667, "x": 1, "y": 0.942}, {"time": 1.3833, "x": 1, "y": 1.013}, {"time": 1.4, "x": 1, "y": 0.916}, {"time": 1.4167, "x": 1, "y": 0.783}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 0.783}, {"time": 1.5167, "x": 1, "y": 0.876}, {"time": 1.5333, "x": 1, "y": 0.902}, {"time": 1.55, "x": 1, "y": 0.89}, {"time": 1.5833, "x": 1, "y": 0.457}, {"time": 1.6, "x": 1, "y": 0.068}, {"time": 1.6333, "x": 1, "y": 0.735}, {"time": 1.65, "x": 1, "y": 0.845}, {"time": 1.6667, "x": 1, "y": 0.865}, {"time": 1.7167, "x": 1, "y": 1.121}, {"time": 1.7333, "x": 1, "y": 1.215}, {"time": 1.8167, "x": 1, "y": 1.138}, {"time": 1.85, "x": 1, "y": 1.161}, {"time": 1.8667, "x": 1, "y": 1.028}, {"time": 1.9, "x": 1, "y": 1.193}, {"time": 1.9333, "x": 1, "y": 1.157}, {"time": 1.9833, "x": 1, "y": 1.028}, {"time": 2, "x": 1, "y": 0.446, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.446}, {"time": 2.4, "x": 1, "y": 0.312}]}, "ben phai10": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4167, "angle": -7.27}, {"time": 0.4333, "angle": -10.19}, {"time": 0.45, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.05, "x": -29.09, "y": -64.8, "curve": "stepped"}, {"time": 0.0667, "x": -33.86, "y": 90.93}, {"time": 0.1167, "x": 0, "y": 0}, {"time": 0.15, "x": -29.09, "y": -63.09, "curve": "stepped"}, {"time": 0.1667, "x": -33.86, "y": 90.93}, {"time": 0.2167, "x": 0, "y": 0}, {"time": 0.2667, "x": -29.09, "y": -64.3, "curve": "stepped"}, {"time": 0.2833, "x": -33.86, "y": 90.93}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.3833, "x": -29.09, "y": -65.52, "curve": "stepped"}, {"time": 0.4, "x": -33.86, "y": 90.93}, {"time": 0.45, "x": 0, "y": 0}, {"time": 0.6, "x": 0, "y": 12.49, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 12.49}, {"time": 0.7833, "x": 0, "y": 0}, {"time": 0.8833, "x": 0, "y": 12.49}, {"time": 0.9167, "x": 0, "y": 2.93}, {"time": 0.9333, "x": 0, "y": 19.24}, {"time": 0.9667, "x": 0, "y": 12.49}, {"time": 1.05, "x": 0, "y": 0}, {"time": 1.0833, "x": -33.86, "y": 90.93}, {"time": 1.1167, "x": 0, "y": 0}, {"time": 1.1333, "x": -29.09, "y": -63.09, "curve": "stepped"}, {"time": 1.15, "x": -37.95, "y": 83.63}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.2, "x": -29.09, "y": -64.3, "curve": "stepped"}, {"time": 1.2167, "x": -33.86, "y": 90.93}, {"time": 1.25, "x": 0, "y": 0}, {"time": 1.2667, "x": -32.53, "y": -89.86, "curve": "stepped"}, {"time": 1.2833, "x": -23.7, "y": 66.88}, {"time": 1.3, "x": 56.2, "y": 30}, {"time": 1.3333, "x": -2.39, "y": 20.45}, {"time": 1.35, "x": -1.19, "y": 0.21}, {"time": 1.3667, "x": 0, "y": 12.49}, {"time": 1.45, "x": 0, "y": 0}, {"time": 1.55, "x": 0, "y": 12.49, "curve": "stepped"}, {"time": 1.6333, "x": 0, "y": 12.49}, {"time": 1.7167, "x": 0, "y": 0}, {"time": 1.7333, "x": -33.86, "y": 90.93}, {"time": 1.75, "x": 0, "y": 0}, {"time": 1.7667, "x": -33.86, "y": 90.93}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8167, "x": -33.86, "y": 90.93}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.85, "x": -23.7, "y": 87.55}, {"time": 1.8667, "x": -16.75, "y": -45.96}, {"time": 1.9, "x": -33.86, "y": 90.93}, {"time": 1.9167, "x": 0, "y": 0}, {"time": 1.9333, "x": -29.09, "y": -63.09, "curve": "stepped"}, {"time": 1.9833, "x": -33.86, "y": 90.93}, {"time": 2, "x": 0, "y": 33.81}, {"time": 2.05, "x": -33.86, "y": 139.34}, {"time": 2.1, "x": 0, "y": 30.47}, {"time": 2.1333, "x": -29.09, "y": -65.52, "curve": "stepped"}, {"time": 2.15, "x": -23.7, "y": 125.57}, {"time": 2.2167, "x": -2.39, "y": 37.31}, {"time": 2.2833, "x": 0, "y": 36.33}, {"time": 2.4, "x": 6.82, "y": 37.51}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 0.791}, {"time": 0.55, "x": 1, "y": 0.957}, {"time": 0.5667, "x": 1, "y": 0.934}, {"time": 0.5833, "x": 1, "y": 0.801}, {"time": 0.6, "x": 1, "y": 0.833}, {"time": 0.6167, "x": 1, "y": 0.831}, {"time": 0.6333, "x": 1, "y": 0.674}, {"time": 0.65, "x": 1, "y": 0.584}, {"time": 0.6667, "x": 1, "y": 0.08}, {"time": 0.7, "x": 1, "y": 0.729}, {"time": 0.7167, "x": 1, "y": 0.913}, {"time": 0.7333, "x": 1, "y": 0.947}, {"time": 0.75, "x": 1, "y": 0.889}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 0.839}, {"time": 0.85, "x": 1, "y": 0.828}, {"time": 0.8667, "x": 1, "y": 0.814}, {"time": 0.8833, "x": 1, "y": 0.799}, {"time": 0.9, "x": 1, "y": 0.773}, {"time": 0.9167, "x": 1, "y": 0.446}, {"time": 0.9333, "x": 1, "y": 0.08}, {"time": 0.9667, "x": 1, "y": 0.756}, {"time": 0.9833, "x": 1, "y": 0.823}, {"time": 1, "x": 1, "y": 0.875}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.2833, "x": 1, "y": 0.284}, {"time": 1.3, "x": 1, "y": 0.367}, {"time": 1.3333, "x": 1, "y": 0.311}, {"time": 1.3667, "x": 1, "y": 0.879}, {"time": 1.3833, "x": 1, "y": 0.975}, {"time": 1.4, "x": 1, "y": 0.904}, {"time": 1.4167, "x": 1, "y": 0.791}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 0.791}, {"time": 1.5167, "x": 1, "y": 0.864}, {"time": 1.5333, "x": 1, "y": 0.868}, {"time": 1.55, "x": 1, "y": 0.831}, {"time": 1.5833, "x": 1, "y": 0.446}, {"time": 1.6, "x": 1, "y": 0.08}, {"time": 1.6333, "x": 1, "y": 0.686}, {"time": 1.65, "x": 1, "y": 0.823}, {"time": 1.6667, "x": 1, "y": 0.875}, {"time": 1.7167, "x": 1, "y": 1.122}, {"time": 1.7333, "x": 1, "y": 1.124}, {"time": 1.85, "x": 1, "y": 1}, {"time": 1.8667, "x": 1, "y": 1.155}, {"time": 1.9, "x": 1, "y": 0.996}, {"time": 1.9333, "x": 1, "y": 1.11}, {"time": 1.9833, "x": 1, "y": 0.983}, {"time": 2, "x": 1, "y": 0.422, "curve": "stepped"}, {"time": 2.2833, "x": 1, "y": 0.422}, {"time": 2.4, "x": 1, "y": 0.312}]}, "control13": {"rotate": [{"time": 0, "angle": 0, "curve": [0.019, 0.54, 0.519, 0.83]}, {"time": 0.15, "angle": 120, "curve": [0.629, 0.07, 0.987, 0.73]}, {"time": 0.3167, "angle": -120, "curve": [0.481, 0, 0.448, 0.99]}, {"time": 0.45, "angle": 0}, {"time": 0.6667, "angle": 180}, {"time": 0.7833, "angle": 0}, {"time": 0.9333, "angle": 180}, {"time": 1.05, "angle": 0}, {"time": 1.3333, "angle": 180}, {"time": 1.45, "angle": 0}, {"time": 1.6, "angle": 180}, {"time": 1.7167, "angle": 0}, {"time": 1.95, "angle": 120}, {"time": 2.2, "angle": -120}, {"time": 2.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1, "x": 0, "y": 235.01, "curve": "stepped"}, {"time": 2, "x": 0, "y": 235.01}, {"time": 2.0333, "x": 0, "y": 329.15}, {"time": 2.1333, "x": 0, "y": 0}, {"time": 2.2, "x": 0, "y": 150.21}, {"time": 2.2833, "x": 0, "y": 0}], "scale": [{"time": 0.45, "x": 1, "y": 1}, {"time": 0.5167, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 0.75, "x": 1, "y": 1.466}, {"time": 0.7833, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1.466}, {"time": 1.05, "x": 1, "y": 1}, {"time": 1.4167, "x": 1, "y": 1.466}, {"time": 1.45, "x": 1, "y": 1}, {"time": 1.5, "x": 1, "y": 1.466, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1.466}, {"time": 1.7167, "x": 1, "y": 1}]}, "control5": {"scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.1667, "x": 1.332, "y": 1.332, "curve": "stepped"}, {"time": 2, "x": 1.332, "y": 1.332}, {"time": 2.1333, "x": 1.348, "y": 1.348}, {"time": 2.2833, "x": 1, "y": 1}]}}, "deform": {"default": {"Rectangle 1 copy 20": {"mat tren 5": [{"time": 1.6, "offset": 6, "vertices": [6.24777, -4.31001]}]}}}}}}