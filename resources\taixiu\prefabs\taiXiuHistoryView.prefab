[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "taiXiuHistoryView", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 6}, {"__id__": 13}, {"__id__": 57}, {"__id__": 62}, {"__id__": 67}, {"__id__": 72}], "_active": true, "_components": [{"__id__": 113}, {"__id__": 114}], "_prefab": {"__id__": 115}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "black", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 4}], "_prefab": {"__id__": 5}, "_opacity": 100, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 3000, "height": 3000}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a95690f4-0cfe-4b25-840f-5c84d9abeba3"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "14IYFa3aZNFaHKTHE1Pvh+", "sync": false}, {"__type__": "cc.Node", "_name": "nen popup", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 7}], "_active": true, "_components": [{"__id__": 10}, {"__id__": 11}], "_prefab": {"__id__": 12}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1200, "height": 654}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "Title", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [{"__id__": 8}], "_prefab": {"__id__": 9}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 393.75, "height": 26.25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 308, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "LỊCH SỬ CƯỢC TÀI XỈU", "_N$string": "LỊCH SỬ CƯỢC TÀI XỈU", "_fontSize": 21, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "24c5dd97-97ec-4755-9796-c0f375d16cdf"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "9dedxcf+5L3awufrSvoaaY", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "265c1222-478d-44a4-949f-14afce427aff"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "f75IaZcAlFCqkYqZpdAkDw", "sync": false}, {"__type__": "cc.Node", "_name": "info", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 14}, {"__id__": 17}, {"__id__": 46}, {"__id__": 49}, {"__id__": 52}], "_active": true, "_components": [{"__id__": 55}], "_prefab": {"__id__": 56}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1160, "height": 530}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "789600", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [], "_active": true, "_components": [{"__id__": 15}], "_prefab": {"__id__": 16}, "_opacity": 66, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 600, "height": 270}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "68X3QTi5lNaZa8/TPH07ek", "sync": false}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [{"__id__": 18}, {"__id__": 21}, {"__id__": 24}, {"__id__": 27}, {"__id__": 30}, {"__id__": 33}, {"__id__": 36}, {"__id__": 39}, {"__id__": 42}], "_active": true, "_components": [], "_prefab": {"__id__": 45}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1050, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 233, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_components": [{"__id__": 19}], "_prefab": {"__id__": 20}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 71.5, "height": 27.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-481, 5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON>", "_N$string": "<PERSON><PERSON><PERSON>", "_fontSize": 22, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d8da7e3d-fc85-40bc-812a-b1ad31bef934"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "2b5Ykk9rZES67WXhD3y3y4", "sync": false}, {"__type__": "cc.Node", "_name": "line", "_objFlags": 0, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_components": [{"__id__": 22}], "_prefab": {"__id__": 23}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 25, "height": 530}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-389, -233, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "904acd98-7f59-4b1d-83ef-03d283801684"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "b9K8IfoVxPk5hb9Em9TU3t", "sync": false}, {"__type__": "cc.Node", "_name": "Time", "_objFlags": 0, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_components": [{"__id__": 25}], "_prefab": {"__id__": 26}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 122.38, "height": 27.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-303, 5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON><PERSON> gian", "_N$string": "<PERSON><PERSON><PERSON><PERSON> gian", "_fontSize": 22, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d8da7e3d-fc85-40bc-812a-b1ad31bef934"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "65r2ICCAxLQKLmeBysqfRe", "sync": false}, {"__type__": "cc.Node", "_name": "line", "_objFlags": 0, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_components": [{"__id__": 28}], "_prefab": {"__id__": 29}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 25, "height": 530}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-198, -233, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "904acd98-7f59-4b1d-83ef-03d283801684"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "c6A8uwLwZKXLm0waYjz+fv", "sync": false}, {"__type__": "cc.Node", "_name": "Total", "_objFlags": 0, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_components": [{"__id__": 31}], "_prefab": {"__id__": 32}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 147.13, "height": 27.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-114, 5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON>", "_N$string": "<PERSON><PERSON><PERSON>", "_fontSize": 22, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d8da7e3d-fc85-40bc-812a-b1ad31bef934"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "0cOepVyjlFQ6iEdBwmxW6j", "sync": false}, {"__type__": "cc.Node", "_name": "line", "_objFlags": 0, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_components": [{"__id__": 34}], "_prefab": {"__id__": 35}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 25, "height": 530}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-14, -233, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "904acd98-7f59-4b1d-83ef-03d283801684"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "c02xaV/OFA1piJl5PW2Fsk", "sync": false}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_components": [{"__id__": 37}], "_prefab": {"__id__": 38}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 145.06, "height": 27.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [74, 5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON><PERSON>", "_N$string": "<PERSON><PERSON><PERSON><PERSON>", "_fontSize": 22, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d8da7e3d-fc85-40bc-812a-b1ad31bef934"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "02Y/s9E0BBq78D2XB1n2ed", "sync": false}, {"__type__": "cc.Node", "_name": "line", "_objFlags": 0, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_components": [{"__id__": 40}], "_prefab": {"__id__": 41}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 25, "height": 530}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [176, -233, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 39}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "904acd98-7f59-4b1d-83ef-03d283801684"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "abJeQ0QbVCErDy4xQ9pPzt", "sync": false}, {"__type__": "cc.Node", "_name": "detail", "_objFlags": 0, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_components": [{"__id__": 43}], "_prefab": {"__id__": 44}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 175.31, "height": 27.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [366, 5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 42}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "<PERSON>", "_N$string": "<PERSON>", "_fontSize": 22, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d8da7e3d-fc85-40bc-812a-b1ad31bef934"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "30J9PIzgtJpaksDMMn3Vub", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "10CTtkUqxNs5qkB1/X4xvt", "sync": false}, {"__type__": "cc.Node", "_name": "Betside", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [], "_active": false, "_components": [{"__id__": 47}], "_prefab": {"__id__": 48}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 110, "height": 27.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-217, 249, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 46}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON> đặt", "_N$string": "<PERSON><PERSON><PERSON> đặt", "_fontSize": 22, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d8da7e3d-fc85-40bc-812a-b1ad31bef934"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "f8rCqdLelAfr7RLvQN1Sqx", "sync": false}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [], "_active": false, "_components": [{"__id__": 50}], "_prefab": {"__id__": 51}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 104.5, "height": 27.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-77, 249, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON> qu<PERSON>", "_N$string": "<PERSON><PERSON><PERSON> qu<PERSON>", "_fontSize": 22, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "d8da7e3d-fc85-40bc-812a-b1ad31bef934"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "95Jg7JWGdEurjBt8FGb/cy", "sync": false}, {"__type__": "cc.Node", "_name": "page_index", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [], "_active": true, "_components": [{"__id__": 53}], "_prefab": {"__id__": 54}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 98.5, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [420.025, 302.9, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": false, "_string": "Trang: 1", "_N$string": "Trang: 1", "_fontSize": 24, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "3208deca-08ff-4361-b0c2-5bc37b3d2919"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "32FpEwQQdDn7dQgJeAtZOS", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b0d13b44-76de-4ce1-836c-78cfd8e9da72"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "82H4SrLZtEaZNlwtSHtdUE", "sync": false}, {"__type__": "cc.Node", "_name": "btnClose", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 58}, {"__id__": 59}], "_prefab": {"__id__": 61}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [553, 300, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b9b003eb-4aaf-4186-ae0d-2f4feba1d23f"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 60}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 57}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "bf5abGnZPpOgbJ7CeSflns9", "handler": "closeClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "f0tBL3UKhJqbndztf9vB5r", "sync": false}, {"__type__": "cc.Node", "_name": "btnNext", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 63}, {"__id__": 64}], "_prefab": {"__id__": 66}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 23, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [593.984, -2.65, 0, 0, 0, 0, 1, 2, 2, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 62}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "78b32c40-4f5a-4f63-bb31-216bbf2c4a25"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 62}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 65}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 62}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "bf5abGnZPpOgbJ7CeSflns9", "handler": "pageNextClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "74KXkKipdPRJ0g9yHK08Qg", "sync": false}, {"__type__": "cc.Node", "_name": "btnPrev", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 68}, {"__id__": 69}], "_prefab": {"__id__": 71}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 23, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-591.854, -2.65, 0, 0, 0, 0, 1, 2, 2, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 67}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ff9b21df-4874-4266-be9c-ab954937a2cd"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 67}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 70}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 67}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "bf5abGnZPpOgbJ7CeSflns9", "handler": "pagePreviousClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "25jsx5K2xFIJ4eNnT6+1Cd", "sync": false}, {"__type__": "cc.Node", "_name": "scrollview", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 73}, {"__id__": 105}], "_active": true, "_components": [{"__id__": 110}, {"__id__": 111}], "_prefab": {"__id__": 112}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1160, "height": 450}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -54, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "temp", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [{"__id__": 74}], "_active": true, "_components": [], "_prefab": {"__id__": 104}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 73}, "_children": [{"__id__": 75}, {"__id__": 78}, {"__id__": 81}, {"__id__": 84}, {"__id__": 87}, {"__id__": 90}, {"__id__": 93}, {"__id__": 96}, {"__id__": 99}], "_active": true, "_components": [{"__id__": 102}], "_prefab": {"__id__": 103}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1160, "height": 72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "lbSession", "_objFlags": 0, "_parent": {"__id__": 74}, "_children": [], "_active": true, "_components": [{"__id__": 76}], "_prefab": {"__id__": 77}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 253, "g": 162, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-487, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "PHIÊN", "_N$string": "PHIÊN", "_fontSize": 21, "_lineHeight": 48, "_enableWrapText": false, "_N$file": {"__uuid__": "60942285-d011-4b44-9a5f-19ff3d4c844a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "e2rYZFKCRDZJjYTbBxRT7t", "sync": false}, {"__type__": "cc.Node", "_name": "lbTime", "_objFlags": 0, "_parent": {"__id__": 74}, "_children": [], "_active": true, "_components": [{"__id__": 79}], "_prefab": {"__id__": 80}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 41.58}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-302, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "THỜI GIAN", "_N$string": "THỜI GIAN", "_fontSize": 21, "_lineHeight": 33, "_enableWrapText": false, "_N$file": {"__uuid__": "60942285-d011-4b44-9a5f-19ff3d4c844a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 3, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "7fQqU7LdNCMZIKfRAEsp1k", "sync": false}, {"__type__": "cc.Node", "_name": "lbResult", "_objFlags": 0, "_parent": {"__id__": 74}, "_children": [], "_active": false, "_components": [{"__id__": 82}], "_prefab": {"__id__": 83}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-77.7, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 81}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "KẾT QUẢ", "_N$string": "KẾT QUẢ", "_fontSize": 20, "_lineHeight": 48, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "f8QmVZqhtEz7UAi9CNtj7e", "sync": false}, {"__type__": "cc.Node", "_name": "lbBet", "_objFlags": 0, "_parent": {"__id__": 74}, "_children": [], "_active": true, "_components": [{"__id__": 85}], "_prefab": {"__id__": 86}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 253, "g": 162, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 182, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-114, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 84}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "TIỀN ĐẶT", "_N$string": "TIỀN ĐẶT", "_fontSize": 21, "_lineHeight": 48, "_enableWrapText": false, "_N$file": {"__uuid__": "60942285-d011-4b44-9a5f-19ff3d4c844a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "4e3MJ6YOxMTIKfxd2vILNz", "sync": false}, {"__type__": "cc.Node", "_name": "lbBetside", "_objFlags": 0, "_parent": {"__id__": 74}, "_children": [], "_active": false, "_components": [{"__id__": 88}], "_prefab": {"__id__": 89}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 51.39, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "Label", "_N$string": "Label", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "45c2bac1-33e5-4efc-83ec-e769b152f43a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "74N5AuGBJEqqcqnKM+6Nxh", "sync": false}, {"__type__": "cc.Node", "_name": "lbRefund", "_objFlags": 0, "_parent": {"__id__": 74}, "_children": [], "_active": false, "_components": [{"__id__": 91}], "_prefab": {"__id__": 92}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 243, "b": 112, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 170, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [274, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 90}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "HOÀN TRẢ", "_N$string": "HOÀN TRẢ", "_fontSize": 20, "_lineHeight": 48, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "e38izChKlPOZA/jAYCqsKH", "sync": false}, {"__type__": "cc.Node", "_name": "lbWin", "_objFlags": 0, "_parent": {"__id__": 74}, "_children": [], "_active": true, "_components": [{"__id__": 94}], "_prefab": {"__id__": 95}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 243, "b": 112, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [73, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 93}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "THẮNG", "_N$string": "THẮNG", "_fontSize": 21, "_lineHeight": 48, "_enableWrapText": false, "_N$file": {"__uuid__": "60942285-d011-4b44-9a5f-19ff3d4c844a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "83YYKEvDdFj6rCkmWojtNP", "sync": false}, {"__type__": "cc.Node", "_name": "detail", "_objFlags": 0, "_parent": {"__id__": 74}, "_children": [], "_active": true, "_components": [{"__id__": 97}], "_prefab": {"__id__": 98}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 395, "height": 25.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [371, -2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 96}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_useOriginalSize": true, "_string": "", "_N$string": "", "_fontSize": 18, "_lineHeight": 20, "_enableWrapText": true, "_N$file": {"__uuid__": "60942285-d011-4b44-9a5f-19ff3d4c844a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 0, "_N$verticalAlign": 0, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 3, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "23ZVykF19B5I7olJucO4pB", "sync": false}, {"__type__": "cc.Node", "_name": "line", "_objFlags": 0, "_parent": {"__id__": 74}, "_children": [], "_active": true, "_components": [{"__id__": 100}], "_prefab": {"__id__": 101}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1300, "height": 16}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1, -47.089, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 99}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "c2e4b7ae-d017-4d0e-9860-6ff1e9f63c3f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "0c8JEWk/RMKKvYuHyHdBrK", "sync": false}, {"__type__": "e67a4P0PNtHmbZTElSo0BFt", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "lbSession": {"__id__": 76}, "lbTime": {"__id__": 79}, "lbSide": {"__id__": 88}, "lbResult": {"__id__": 82}, "lbBet": {"__id__": 85}, "lbRefund": {"__id__": 91}, "lbWin": {"__id__": 94}, "lbchitiet": {"__id__": 97}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "e1RO1bFy9GXqoEREZGp73Q", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "bcQyI1jVxLOpTCbyPzYoO/", "sync": false}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [{"__id__": 106}], "_active": true, "_components": [{"__id__": 108}], "_prefab": {"__id__": 109}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1160, "height": 455}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 105}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 107}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1000, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 220, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "6fu3jDAHBLkJ6j5RKi4DF1", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 105}, "_enabled": false, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0.1, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "67khkH9nZLVqtbYtjnBTzu", "sync": false}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 72}, "_enabled": false, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": true, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 106}, "content": {"__id__": 106}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": null, "_id": ""}, {"__type__": "2c74dpzIm1CvrO/23s1je69", "_name": "", "_objFlags": 0, "node": {"__id__": 72}, "_enabled": true, "itemTemplate": {"__id__": 74}, "scrollView": {"__id__": 110}, "spawnCount": 1, "spacing": 4, "bufferZone": 400, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "1epo55TkJFH7Il1JiIxOXI", "sync": false}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_defaultClip": {"__uuid__": "fd368a1d-24a5-4b8f-8745-d532e9a6fda0"}, "_clips": [{"__uuid__": "fd368a1d-24a5-4b8f-8745-d532e9a6fda0"}, {"__uuid__": "2c581fef-58f4-478d-adee-4138a71c7df4"}], "playOnLoad": false, "_id": ""}, {"__type__": "bf5abGnZPpOgbJ7CeSflns9", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "taiXiuHistoryListView": {"__id__": 111}, "nodePageNext": {"__id__": 62}, "nodePagePrevious": {"__id__": 67}, "nodeLBPage": {"__id__": 53}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "f7fddd4f-ee27-4b1a-b38c-bbd70f04e5dd"}, "fileId": "", "sync": false}]