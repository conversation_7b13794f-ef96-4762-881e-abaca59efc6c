{"skeleton": {"hash": "uLslPBsHz8awMWPmSfJJSEL4s4s", "spine": "3.7.93", "width": 219, "height": 118, "images": "", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "scaleY": 1.1}, {"name": "bone2", "parent": "root"}], "slots": [{"name": "VongTron", "bone": "bone2", "attachment": "VongTron"}, {"name": "Text", "bone": "bone", "attachment": "Text"}], "skins": {"default": {"Text": {"Text": {"type": "mesh", "uvs": [0.97817, 0.91111, 0.91211, 0.91111, 0.83009, 0.91111, 0.73268, 0.91111, 0.63271, 0.91111, 0.5148, 0.91111, 0.42508, 0.91111, 0.33793, 0.91111, 0.24052, 0.91111, 0.13287, 0.91111, 0.02183, 0.91111, 0.02183, 0.08889, 0.13287, 0.08889, 0.23796, 0.08889, 0.33537, 0.08889, 0.43277, 0.08889, 0.5148, 0.08889, 0.63528, 0.08889, 0.73268, 0.08889, 0.83521, 0.08889, 0.90699, 0.08889, 0.97817, 0.08889], "triangles": [9, 10, 11, 12, 9, 11, 8, 13, 14, 9, 12, 13, 8, 9, 13, 6, 7, 14, 8, 14, 7, 15, 6, 14, 4, 5, 16, 15, 16, 5, 6, 15, 5, 17, 4, 16, 2, 3, 18, 17, 18, 3, 4, 17, 3, 19, 2, 18, 1, 20, 21, 0, 1, 21, 2, 19, 20, 1, 2, 20], "vertices": [110, -18, 94.87, -18, 76.09, -18, 53.78, -18, 30.89, -18, 3.89, -18, -16.66, -18, -36.61, -18, -58.92, -18, -83.57, -18, -109, -18, -109, 19, -83.57, 19, -59.51, 19, -37.2, 19, -14.89, 19, 3.89, 19, 31.48, 19, 53.78, 19, 77.26, 19, 93.7, 19, 110, 19], "hull": 22, "edges": [20, 22, 0, 42, 22, 24, 18, 20, 24, 26, 16, 18, 26, 28, 14, 16, 28, 30, 12, 14, 30, 32, 10, 12, 32, 34, 8, 10, 34, 36, 6, 8, 36, 38, 4, 6, 38, 40, 40, 42, 0, 2, 2, 4], "width": 229, "height": 45}}, "VongTron": {"VongTron": {"width": 120, "height": 118}}}}, "animations": {"animation": {"bones": {"bone2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6, "angle": -177.9}, {"time": 1.2, "angle": 5.4}]}}, "deform": {"default": {"Text": {"Text": [{"time": 0, "offset": 1, "vertices": [2.781, 0, 2.472, 0, 3.09, 0, 3.09, 0, 3.399, 0, 3.399, 0, 3.399, 0, 3.399, 0, 3.09, 0.30899, 2.781, 0, 2.78099, -2e-05, -3.09, 0, -2.781, -0.30901, -3.09, 0, -3.09, 0, -3.09, 0, -3.39899, 0, -3.399, 0, -3.399, 0, -3.39899, 0, -3.09, 0, -3.09]}, {"time": 0.0667, "offset": 1, "vertices": [2.781, 0, 2.472, 0, 3.09, 0, 3.09, 0, 3.399, 0, 3.399, 0, 3.399, 0, 3.399, 0, 3.09, 0.30899, 2.781, 0, -1.236, -1e-05, 0.618, 0, -2.781, -0.30901, -3.09, 0, -3.09, 0, -3.09, 0, -3.39899, 0, -3.399, 0, -3.399, 0, -3.39899, 0, -3.09, 0, -3.09]}, {"time": 0.1333, "offset": 1, "vertices": [2.781, 0, 2.472, 0, 3.09, 0, 3.09, 0, 3.399, 0, 3.399, 0, 3.399, 0, 3.399, 0, 3.09, 0.30899, -0.927, -0.30899, -4.326, -1e-05, 4.326, 0, 0.618, -0.30901, -3.09, 0, -3.09, 0, -3.09, 0, -3.39899, 0, -3.399, 0, -3.399, 0, -3.39899, 0, -3.09, 0, -3.09]}, {"time": 0.2, "offset": 1, "vertices": [2.781, 0, 2.472, 0, 3.09, 0, 3.09, 0, 3.399, 0, 3.399, 0, 3.399, 0, 3.399, 0, -0.927, 0.30899, -4.326, -0.30899, -9.579, -1e-05, 9.27, 0, 4.94402, -0.30901, 1.23602, 0, -3.09, 0, -3.09, 0, -3.39899, 0, -3.399, 0, -3.399, 0, -3.39899, 0, -3.09, 0, -3.09]}, {"time": 0.2667, "offset": 1, "vertices": [2.781, 0, 2.472, 0, 3.09, 0, 3.09, 0, 3.399, 0, 3.399, 0, 3.399, 0, -0.927, 0, -4.944, 0.30899, -8.96099, -0.30899, -12.66899, -1e-05, 13.596, 0, 9.27002, -0.30901, 5.25303, 0, 1.236, 0, -3.09, 0, -3.39899, 0, -3.399, 0, -3.399, 0, -3.39899, 0, -3.09, 0, -3.09]}, {"time": 0.3333, "offset": 1, "vertices": [2.781, 0, 2.472, 0, 3.09, 0, 3.09, 0, 3.399, 0, 3.399, 0, -0.927, 0, -4.32599, 0, -8.961, 0.30899, -12.97799, -0.30899, -8.961, -0.30902, 9.57899, 0, 13.28702, -0.30901, 9.57903, 0, 4.635, 0, 1.23601, 0, -3.39899, 0, -3.399, 0, -3.399, 0, -3.39899, 0, -3.09, 0, -3.09]}, {"time": 0.4, "offset": 1, "vertices": [2.781, 0, 2.472, 0, 3.09, 0, 3.09, 0, 3.399, 0, -1.236, 0, -4.635, 0, -8.96099, 0, -12.978, 0.30899, -8.65199, -0.30899, -5.253, -0.30902, 5.25299, 0, 8.96101, -0.30901, 13.28703, 0, 9.27, 0, 4.94401, 0, 1.545, 0, -3.399, 0, -3.399, 0, -3.39899, 0, -3.09, 0, -3.09]}, {"time": 0.4667, "offset": 1, "vertices": [2.781, 0, 2.472, 0, 3.09, 0, 3.09, 0, -1.545, 0, -4.635, 0, -9.88799, 0, -12.97799, 0, -9.27, 0.30899, -5.562, -0.30899, -0.618, -0.30902, 0.92697, 0, 5.253, -0.30901, 9.88803, 0, 13.28699, 0, 9.579, 0, 4.63502, 0, 1.23602, 0, -3.399, 0, -3.39899, 0, -3.09, 0, -3.09]}, {"time": 0.5333, "offset": 1, "vertices": [2.781, 0, 2.472, 0, 3.09, 0, -1.236, 0, -4.944, 0, -9.57899, 0, -12.97799, 0, -9.88799, 0, -4.944, 0.30899, -0.927, -0.30899, 3.09, -0.30902, -2.47202, 0, 1.23598, -0.30901, 5.56203, 0, 8.961, 0, 13.28699, 0, 10.19701, 0, 4.94402, 0, 0.618, 0, -3.39899, 0, -3.09, 0, -3.09]}, {"time": 0.6, "offset": 1, "vertices": [2.781, 0, 2.472, 0, -1.06202, 0, -4.34999, 0, -9.09599, 0, -12.34699, 0, -9.172, 0, -4.698, 0, -0.44599, 0.30899, 3.225, -0.30899, 3.09, -0.30902, -2.47202, 0, -3.60802, -0.30901, 1.06404, 0, 5.155, 0, 10.173, 0, 13.65701, 0, 9.44201, 0, 5.11599, 0, 1.099, 0, -3.09, 0, -3.09]}, {"time": 0.6667, "offset": 1, "vertices": [2.781, 0, -0.988, 0, -4.17601, 0, -9.19398, 0, -12.90199, 0, -9.23299, 0, -5.366, 0, -0.54601, 0, 2.66803, 0.30899, 3.225, -0.30899, 3.09, -0.30902, -2.47202, 0, -3.60802, -0.30901, -3.43396, 0, 1.349, 0, 4.98301, 0, 8.81302, 0, 13.59401, 0, 9.26798, 0, 4.55899, 0, 1.408, 0, -3.09]}, {"time": 0.7333, "offset": 1, "vertices": [-0.333, 0, -4.79399, 0, -8.674, 0, -12.65398, 0, -9.44199, 0, -4.735, 0, -0.52201, 0, 2.91399, 0, 2.66803, 0.30899, 3.225, -0.30899, 3.09, -0.30902, -2.47202, 0, -3.60802, -0.30901, -3.43396, 0, -2.80301, 0, 1.52299, 0, 5.00703, 0, 9.44201, 0, 12.72798, 0, 9.05698, 0, 5.55999, 0, 0.37003]}, {"time": 0.8, "offset": 1, "vertices": [-4.48499, 0, -8.59999, 0, -12.826, 0, -9.19398, 0, -5.636, 0, 0.45501, 0, 3.284, 0, 2.91399, 0, 2.66803, 0.30899, 3.225, -0.30899, 3.09, -0.30902, -2.47202, 0, -3.60802, -0.30901, -3.43396, 0, -2.80301, 0, -2.97502, 0, 0.85501, 0, 5.29002, 0, 9.26799, 0, 12.51698, 0, 9.71198, 0, 4.86802]}, {"time": 0.8667, "offset": 1, "vertices": [-7.59899, 0, -13.44398, 0, -8.67401, 0, -5.04199, 0, -0.446, 0, 3.223, 0, 3.284, 0, 2.91399, 0, 2.66803, 0.30899, 3.225, -0.30899, 3.09, -0.30902, -2.47202, 0, -3.60802, -0.30901, -3.43396, 0, -2.80301, 0, -2.97502, 0, -3.297, 0, 0.792, 0, 5.46199, 0, 9.74899, 0, 13.17198, 0, 8.67401]}, {"time": 0.9333, "offset": 1, "vertices": [-12.60398, 0, -8.439, 0, -5.59401, 0, -0.80697, 0, 3.01899, 0, 3.223, 0, 3.284, 0, 2.91399, 0, 2.66803, 0.30899, 3.225, -0.30899, 3.09, -0.30902, -2.47202, 0, -3.60802, -0.30901, -3.43396, 0, -2.80301, 0, -2.97502, 0, -3.297, 0, -3.05799, 0, 1.61197, 0, 5.129, 0, 8.55199, 0, 13.294]}, {"time": 1, "offset": 1, "vertices": [-8.36899, 0, -4.97401, 0, -0.974, 0, 3.42802, 0, 3.01899, 0, 3.223, 0, 3.284, 0, 2.91399, 0, 2.66803, 0.30899, 3.225, -0.30899, 3.09, -0.30902, -2.47202, 0, -3.60802, -0.30901, -3.43396, 0, -2.80301, 0, -2.97502, 0, -3.297, 0, -3.05799, 0, -3.77801, 0, 1.66401, 0, 5.472, 0, 9.05901]}, {"time": 1.0667, "offset": 1, "vertices": [-4.134, 0, 0.03098, 0, 3.26099, 0, 3.42802, 0, 3.01899, 0, 3.223, 0, 3.284, 0, 2.91399, 0, 2.66803, 0.30899, 3.225, -0.30899, 3.09, -0.30902, -2.47202, 0, -3.60802, -0.30901, -3.43396, 0, -2.80301, 0, -2.97502, 0, -3.297, 0, -3.05799, 0, -3.77801, 0, -2.95598, 0, 0.85199, 0, 5.20902]}, {"time": 1.1333, "offset": 1, "vertices": [-1.05401, 0, 3.49597, 0, 3.26099, 0, 3.42802, 0, 3.01899, 0, 3.223, 0, 3.284, 0, 2.91399, 0, 2.66803, 0.30899, 3.225, -0.30899, 3.09, -0.30902, -2.47202, 0, -3.60802, -0.30901, -3.43396, 0, -2.80301, 0, -2.97502, 0, -3.297, 0, -3.05799, 0, -3.77801, 0, -2.95598, 0, -3.383, 0, 1.35901]}, {"time": 1.2, "offset": 1, "vertices": [2.781, 0, 2.472, 0, 3.09, 0, 3.09, 0, 3.399, 0, 3.399, 0, 3.399, 0, 3.399, 0, 3.09, 0.30899, 2.781, 0, 2.78099, -2e-05, -3.09, 0, -2.781, -0.30901, -3.09, 0, -3.09, 0, -3.09, 0, -3.39899, 0, -3.399, 0, -3.399, 0, -3.39899, 0, -3.09, 0, -3.09]}]}}}}}}