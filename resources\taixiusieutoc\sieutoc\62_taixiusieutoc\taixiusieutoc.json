{"skins": {"default": {"sieu": {"sieu": {"x": -6.84, "width": 156, "y": 2.68, "height": 66}}, "xiu": {"xiu": {"x": 1.41, "width": 124, "y": 3.09, "height": 70}}, "toc": {"toc": {"x": -1.8, "width": 137, "y": 3.96, "height": 72}}, "tai": {"tai": {"x": -1.7, "width": 114, "y": 4.19, "height": 65}}}}, "skeleton": {"images": "", "x": -257.86, "width": 573.9, "y": -26.97, "spine": "3.7-from-3.8-from-4.0-from-4.1.17", "audio": "", "hash": "s1+axzNziyo", "height": 72}, "slots": [{"attachment": "sieu", "name": "sieu", "bone": "sieu"}, {"attachment": "tai", "name": "tai", "bone": "tai"}, {"attachment": "toc", "name": "toc", "bone": "toc"}, {"attachment": "xiu", "name": "xiu", "bone": "xiu"}], "bones": [{"name": "root"}, {"parent": "root", "color": "ff0000ff", "name": "bone", "x": -0.13, "y": 0.18}, {"parent": "bone", "name": "tai", "x": -199.03, "y": 1.81}, {"parent": "bone", "name": "xiu", "x": -66.95, "y": 4.82}, {"parent": "bone", "name": "sieu", "x": 94.09, "y": 4.22}, {"parent": "bone", "name": "toc", "x": 249.47, "y": 4.89}], "animations": {"animation": {"slots": {"sieu": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffffff", "curve": "stepped", "time": 0.4333}, {"color": "ffffffff", "time": 4.6667}, {"color": "ffffff00", "time": 4.8333}]}, "xiu": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.1667}, {"color": "ffffffff", "curve": "stepped", "time": 0.2667}, {"color": "ffffffff", "time": 4.5}, {"color": "ffffff00", "time": 4.6667}]}, "toc": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.5}, {"color": "ffffffff", "curve": "stepped", "time": 0.6}, {"color": "ffffffff", "time": 4.8333}, {"color": "ffffff00", "time": 5}]}, "tai": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "curve": "stepped", "time": 0.1}, {"color": "ffffffff", "time": 4.3667}, {"color": "ffffff00", "time": 4.5}]}}, "bones": {"sieu": {"translate": [{"curve": "stepped", "y": 157.51, "time": 0}, {"y": 157.51, "time": 0.3333}, {"y": -10.86, "time": 0.6667}, {"curve": "stepped", "time": 0.7667}, {"time": 2}, {"y": 43.36, "time": 2.1667}, {"curve": "stepped", "time": 2.3333}, {"time": 2.6667}, {"y": 43.36, "time": 2.8333}, {"curve": "stepped", "time": 3}, {"time": 4.4}, {"y": -16.52, "time": 4.5}, {"y": 157.51, "time": 4.8333}]}, "xiu": {"translate": [{"curve": "stepped", "y": 157.51, "time": 0}, {"y": 157.51, "time": 0.1667}, {"y": -10.08, "time": 0.5}, {"curve": "stepped", "time": 0.6}, {"time": 1.8333}, {"y": 43.36, "time": 2}, {"curve": "stepped", "time": 2.1667}, {"time": 2.5}, {"y": 43.36, "time": 2.6667}, {"curve": "stepped", "time": 2.8333}, {"time": 4.2333}, {"y": -16.52, "time": 4.3333}, {"y": 157.51, "time": 4.6667}]}, "toc": {"shear": [{"time": 5.3333}], "translate": [{"curve": "stepped", "y": 157.51, "time": 0}, {"y": 157.51, "time": 0.5}, {"y": -9.06, "time": 0.8333}, {"curve": "stepped", "time": 0.9333}, {"time": 2.1667}, {"y": 43.36, "time": 2.3333}, {"curve": "stepped", "time": 2.5}, {"time": 2.8333}, {"y": 43.36, "time": 3}, {"curve": "stepped", "time": 3.1667}, {"time": 4.5667}, {"y": -16.52, "time": 4.6667}, {"y": 157.51, "time": 5}]}, "tai": {"translate": [{"y": 157.51, "time": 0}, {"y": -16.52, "time": 0.3333}, {"curve": "stepped", "time": 0.4333}, {"time": 1.6667}, {"y": 43.36, "time": 1.8333}, {"curve": "stepped", "time": 2}, {"time": 2.3333}, {"y": 43.36, "time": 2.5}, {"curve": "stepped", "time": 2.6667}, {"time": 4.0667}, {"y": -16.52, "time": 4.1667}, {"y": 157.51, "time": 4.5}]}}}}}