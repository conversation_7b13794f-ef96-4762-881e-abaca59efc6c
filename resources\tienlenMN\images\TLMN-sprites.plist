<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>frames</key>
		<dict>
			<key>CongUng.png</key>
			<dict>
				<key>frame</key>
				<string>{{0,450},{148,50}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{148,50}}</string>
				<key>sourceSize</key>
				<string>{148,50}</string>
			</dict>

			<key>Ung.png</key>
			<dict>
				<key>frame</key>
				<string>{{149,450},{144,50}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{144,50}}</string>
				<key>sourceSize</key>
				<string>{144,50}</string>
			</dict>

			<key>boLuot.png</key>
			<dict>
				<key>frame</key>
				<string>{{151,360},{131,33}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{131,33}}</string>
				<key>sourceSize</key>
				<string>{131,33}</string>
			</dict>

			<key>btn_Xep.png</key>
			<dict>
				<key>frame</key>
				<string>{{0,130},{150,63}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{150,63}}</string>
				<key>sourceSize</key>
				<string>{150,63}</string>
			</dict>

			<key>btn_Xep_off.png</key>
			<dict>
				<key>frame</key>
				<string>{{0,66},{150,63}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{150,63}}</string>
				<key>sourceSize</key>
				<string>{150,63}</string>
			</dict>

			<key>btn_batDau.png</key>
			<dict>
				<key>frame</key>
				<string>{{151,194},{150,63}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{150,63}}</string>
				<key>sourceSize</key>
				<string>{150,63}</string>
			</dict>

			<key>btn_boB.png</key>
			<dict>
				<key>frame</key>
				<string>{{151,130},{150,63}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{150,63}}</string>
				<key>sourceSize</key>
				<string>{150,63}</string>
			</dict>

			<key>btn_boB_off.png</key>
			<dict>
				<key>frame</key>
				<string>{{151,66},{150,63}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{150,63}}</string>
				<key>sourceSize</key>
				<string>{150,63}</string>
			</dict>

			<key>btn_boChon.png</key>
			<dict>
				<key>frame</key>
				<string>{{0,386},{150,63}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{150,63}}</string>
				<key>sourceSize</key>
				<string>{150,63}</string>
			</dict>

			<key>btn_boChon_off.png</key>
			<dict>
				<key>frame</key>
				<string>{{0,322},{150,63}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{150,63}}</string>
				<key>sourceSize</key>
				<string>{150,63}</string>
			</dict>

			<key>btn_danh.png</key>
			<dict>
				<key>frame</key>
				<string>{{0,258},{150,63}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{150,63}}</string>
				<key>sourceSize</key>
				<string>{150,63}</string>
			</dict>

			<key>btn_danh_off.png</key>
			<dict>
				<key>frame</key>
				<string>{{0,194},{150,63}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{150,63}}</string>
				<key>sourceSize</key>
				<string>{150,63}</string>
			</dict>

			<key>cong.png</key>
			<dict>
				<key>frame</key>
				<string>{{151,309},{144,50}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{144,50}}</string>
				<key>sourceSize</key>
				<string>{144,50}</string>
			</dict>

			<key>name.png</key>
			<dict>
				<key>frame</key>
				<string>{{0,0},{442,65}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{442,65}}</string>
				<key>sourceSize</key>
				<string>{442,65}</string>
			</dict>

			<key>thua.png</key>
			<dict>
				<key>frame</key>
				<string>{{151,258},{144,50}}</string>
				<key>offset</key>
				<string>{0,0}</string>
				<key>rotated</key>
				<false/>
				<key>sourceColorRect</key>
				<string>{{0,0},{144,50}}</string>
				<key>sourceSize</key>
				<string>{144,50}</string>
			</dict>

		</dict>

		<key>metadata</key>
		<dict>
			<key>format</key>
			<integer>2</integer>
			<key>size</key>
			<string>{512,512}</string>
			<key>textureFileName</key>
			<string>sprites.png</string>
			</dict>
		</dict>
</plist>