{"skeleton": {"hash": "Qtg7fqwKqlCNb1Utr4WOuSD9WTI", "spine": "3.6.53", "width": 776.34, "height": 485}, "bones": [{"name": "root"}, {"name": "Tam", "parent": "root"}, {"name": "it2", "parent": "Tam", "length": 56.68, "rotation": 93.15, "x": 50.55, "y": 67.37}, {"name": "it1", "parent": "it2", "length": 56.68, "rotation": 53.82, "x": -46.29, "y": 153.26}, {"name": "it3", "parent": "it2", "length": 56.68, "rotation": -52.07, "x": -48.82, "y": -166.26, "scaleX": 1.17, "scaleY": -1.123}, {"name": "k", "parent": "Tam", "length": 161.82, "rotation": 1.09, "x": 0.08, "y": 4.63}, {"name": "text3", "parent": "Tam", "length": 114.69, "rotation": 0.51, "x": 16.46, "y": 94.74}, {"name": "tp", "parent": "Tam", "x": -168.94, "y": 14.33}, {"name": "tp2", "parent": "tp", "x": -46.56, "y": 72.42}, {"name": "tp3", "parent": "tp", "x": 35.47, "y": 79.81}, {"name": "tp4", "parent": "tp", "length": 66.53, "rotation": 91.27, "x": -8.13, "y": 144.84}, {"name": "tp5", "parent": "tp", "length": 85.7, "rotation": -134.3, "x": -89.42, "y": 109.37}, {"name": "tp6", "parent": "tp5", "length": 63.49, "rotation": -95.89, "x": 86.22, "y": -0.53}, {"name": "tp7", "parent": "tp6", "length": 47.99, "rotation": -30.05, "x": 63.96, "y": 0.57}, {"name": "tp8", "parent": "tp", "length": 75.03, "rotation": -72.81, "x": 49.51, "y": 96.81}, {"name": "tp9", "parent": "tp8", "length": 62.12, "rotation": 37.99, "x": 75.03}, {"name": "tp10", "parent": "tp9", "length": 34.16, "rotation": -4.03, "x": 62.12}, {"name": "tp11", "parent": "tp10", "length": 399.49, "rotation": 73.07, "x": -6.19, "y": -81.85}, {"name": "tp12", "parent": "tp", "length": 90.16, "rotation": -112.15, "x": -70.2, "y": -79.07}, {"name": "tp13", "parent": "tp12", "length": 56.38, "rotation": 11.58, "x": 96.19, "y": 0.86}, {"name": "tp14", "parent": "tp", "length": 119.51, "rotation": -49.51, "x": 48.77, "y": -74.64}, {"name": "tp15", "parent": "tp14", "length": 59.17, "rotation": -68.01, "x": 119.51}], "slots": [{"name": "tp", "bone": "tp", "attachment": "tp"}, {"name": "it1", "bone": "it1", "attachment": "it1"}, {"name": "it2", "bone": "it2", "attachment": "it1"}, {"name": "it3", "bone": "it3", "attachment": "it1"}, {"name": "k", "bone": "k", "attachment": "k"}, {"name": "text3", "bone": "text3", "attachment": "text3"}], "skins": {"default": {"it1": {"it1": {"x": 24.74, "y": -6.97, "rotation": -146.98, "width": 212, "height": 217}}, "it2": {"it1": {"x": 24.74, "y": -6.97, "rotation": -146.98, "width": 212, "height": 217}}, "it3": {"it1": {"x": 24.74, "y": -6.97, "rotation": -146.98, "width": 212, "height": 217}}, "k": {"k": {"x": 65.36, "y": -5.87, "rotation": -1.09, "width": 347, "height": 117}}, "text3": {"text3": {"x": 51.58, "y": 7.02, "rotation": -0.51, "width": 231, "height": 107}}, "tp": {"tp": {"type": "mesh", "uvs": [0.40079, 0.07766, 0.40818, 0.14775, 0.45464, 0.16299, 0.5032, 0.23003, 0.51587, 0.3184, 0.51692, 0.37173, 0.50531, 0.39459, 0.54648, 0.45706, 0.66156, 0.3565, 0.66472, 0.31993, 0.68795, 0.28488, 0.73546, 0.25898, 0.79246, 0.22089, 0.88431, 0.12032, 0.89909, 0.05023, 0.92126, 0.03042, 0.94343, 0.03957, 0.95927, 0.07461, 1, 0.20413, 0.98355, 0.23308, 0.9466, 0.2346, 0.91809, 0.23308, 0.81041, 0.35802, 0.80724, 0.39459, 0.7798, 0.43421, 0.79669, 0.52258, 0.79035, 0.57744, 0.72701, 0.614, 0.72384, 0.66886, 0.68056, 0.68105, 0.64783, 0.61858, 0.64678, 0.54239, 0.63622, 0.50582, 0.60666, 0.55001, 0.59399, 0.60334, 0.57077, 0.62315, 0.56232, 0.68714, 0.54437, 0.7039, 0.5676, 0.74352, 0.60138, 0.742, 0.61194, 0.81209, 0.59082, 0.90351, 0.54226, 0.9675, 0.49686, 1, 0.41346, 1, 0.38707, 1, 0.36173, 1, 0.09358, 1, 0.06613, 0.95379, 0.08091, 0.84713, 0.05558, 0.78466, 0.07775, 0.72828, 0.18648, 0.53325, 0.17276, 0.47535, 0.18648, 0.45401, 0.14214, 0.4723, 0.10942, 0.48297, 0.06085, 0.45249, 0.02707, 0.38545, 0.02074, 0.28488, 0, 0.18889, 0.02918, 0.13404, 0.08091, 0.11118, 0.12314, 0.1508, 0.13475, 0.19956, 0.12525, 0.27574, 0.1527, 0.22851, 0.18543, 0.18432, 0.17278, 0.14622, 0.20232, 0.10661, 0.24349, 0.12642, 0.24877, 0.06395, 0.30578, 0, 0.38285, 0, 0.95821, 0.12032, 0.98355, 0.15535, 0.39868, 0.95989, 0.22132, 0.63686, 0.16537, 0.74504, 0.41346, 0.58201, 0.49053, 0.71762, 0.34379, 0.56372, 0.34379, 0.78009, 0.27622, 0.34278, 0.38813, 0.34583, 0.22238, 0.27117, 0.13581, 0.3824, 0.09675, 0.37021, 0.06719, 0.30469, 0.06824, 0.22546, 0.42824, 0.34431, 0.44619, 0.45401, 0.51059, 0.51344, 0.55282, 0.54696, 0.44306, 0.63408, 0.70062, 0.3824, 0.53701, 0.53441, 0.78296, 0.30622, 0.93604, 0.14927], "triangles": [17, 14, 16, 14, 15, 16, 17, 13, 14, 17, 74, 13, 98, 13, 74, 18, 75, 17, 74, 17, 75, 98, 74, 75, 70, 67, 68, 70, 68, 69, 63, 89, 61, 63, 61, 62, 89, 63, 64, 60, 61, 89, 75, 18, 98, 13, 98, 12, 98, 97, 12, 18, 21, 98, 18, 20, 21, 19, 20, 18, 85, 67, 70, 66, 67, 85, 64, 65, 89, 65, 66, 85, 59, 60, 89, 88, 59, 89, 65, 88, 89, 97, 11, 12, 98, 21, 97, 71, 1, 70, 70, 1, 85, 83, 85, 1, 2, 84, 1, 90, 2, 3, 90, 3, 4, 72, 0, 1, 0, 72, 73, 90, 84, 2, 22, 97, 21, 87, 88, 65, 90, 4, 5, 86, 65, 85, 86, 85, 83, 87, 65, 86, 71, 72, 1, 84, 83, 1, 97, 95, 10, 97, 10, 11, 95, 97, 22, 9, 10, 95, 8, 9, 95, 58, 59, 88, 58, 88, 87, 6, 90, 5, 23, 95, 22, 24, 95, 23, 57, 58, 87, 54, 86, 83, 91, 90, 6, 91, 6, 7, 55, 86, 54, 86, 57, 87, 56, 86, 55, 56, 57, 86, 7, 95, 96, 8, 95, 7, 32, 95, 24, 92, 91, 7, 32, 24, 25, 52, 53, 54, 96, 92, 7, 95, 32, 96, 31, 32, 25, 93, 96, 32, 33, 93, 32, 91, 81, 83, 54, 83, 81, 52, 54, 81, 25, 27, 31, 83, 84, 91, 91, 84, 90, 79, 91, 92, 79, 81, 91, 34, 93, 33, 26, 27, 25, 30, 31, 27, 35, 93, 34, 96, 94, 79, 96, 79, 92, 77, 52, 81, 27, 29, 30, 28, 29, 27, 96, 93, 94, 94, 93, 35, 36, 37, 35, 37, 94, 35, 80, 94, 37, 51, 52, 77, 78, 51, 77, 82, 77, 81, 94, 82, 81, 94, 81, 79, 82, 94, 80, 78, 77, 82, 78, 50, 51, 49, 50, 78, 40, 41, 38, 40, 38, 39, 76, 82, 80, 80, 37, 38, 41, 76, 80, 41, 80, 38, 42, 76, 41, 82, 47, 49, 48, 49, 47, 46, 82, 76, 82, 49, 78, 46, 47, 82, 45, 46, 76, 43, 44, 76, 45, 76, 44, 42, 43, 76], "vertices": [2, 10, 55.71, -54.46, 0.89882, 14, -101.56, 26.77, 0.10118, 3, 9, 14.78, 87.94, 0.00356, 10, 21.61, -58.88, 0.58723, 14, -67.56, 21.67, 0.40921, 3, 10, 13.5, -91.22, 0.23154, 14, -50.89, 50.55, 0.7647, 15, -68.13, 117.35, 0.00376, 3, 10, -19.77, -124.48, 0.04141, 14, -9.78, 73.42, 0.90868, 15, -21.65, 110.07, 0.04991, 3, 14, 33.79, 69.22, 0.80789, 15, 10.1, 79.95, 0.19197, 17, 136.16, 95.08, 0.00015, 3, 14, 58.71, 62.29, 0.63657, 15, 25.48, 59.14, 0.3528, 17, 122.22, 73.28, 0.01064, 3, 14, 66.9, 51.25, 0.45505, 15, 25.14, 45.39, 0.50938, 17, 109.27, 68.68, 0.03557, 3, 14, 104.36, 69.83, 0.02206, 15, 66.1, 36.98, 0.31049, 17, 116.06, 27.42, 0.66746, 1, 17, 210.1, 22.45, 1, 1, 17, 221.9, 35.87, 1, 1, 17, 244.91, 40.78, 1, 1, 17, 279.47, 32.47, 1, 1, 17, 322.86, 25.3, 1, 1, 17, 403.45, 29.48, 1, 1, 17, 431.12, 51.77, 1, 1, 17, 449.36, 50.99, 1, 1, 17, 459.69, 38.59, 1, 1, 17, 459.3, 18.31, 1, 1, 17, 447.56, -49.67, 1, 1, 17, 430.14, -54.81, 1, 1, 17, 408.33, -40.87, 1, 1, 17, 392.25, -29.04, 1, 1, 17, 295.84, -36.76, 1, 1, 17, 284.04, -50.18, 1, 1, 17, 257.34, -55.26, 1, 1, 17, 243.02, -97.35, 1, 1, 17, 224.39, -116.86, 1, 1, 17, 177.75, -106.59, 1, 1, 17, 160.96, -127.34, 1, 1, 17, 132.58, -115.19, 1, 1, 17, 130.67, -77.25, 1, 2, 16, 82.01, 48.98, 0.00226, 17, 150.84, -46.28, 0.99774, 2, 16, 65.13, 58.16, 0.02816, 17, 154.7, -27.46, 0.97184, 3, 16, 62.46, 28.49, 0.43728, 17, 125.54, -33.54, 0.56249, 20, 55.19, 137.5, 0.00023, 3, 16, 71.78, 2.78, 0.93293, 17, 103.66, -49.94, 0.03764, 20, 69.11, 113.96, 0.02942, 3, 16, 65.14, -14.9, 0.87425, 17, 84.82, -48.75, 0.01861, 20, 65.86, 95.36, 0.10714, 4, 16, 80.01, -42.78, 0.4843, 17, 62.47, -71.09, 0.14124, 20, 85.62, 70.71, 0.37373, 21, -78.25, -4.95, 0.00073, 4, 16, 75.33, -56.99, 0.297, 17, 47.51, -70.74, 0.14143, 20, 83.65, 55.88, 0.55106, 21, -65.24, -12.33, 0.01052, 4, 16, 100.04, -61.75, 0.09029, 17, 50.15, -95.77, 0.03247, 20, 108.82, 55.77, 0.70763, 21, -55.71, 10.97, 0.16961, 4, 16, 117.99, -46.34, 0.05216, 17, 70.12, -108.46, 0.00681, 20, 123.61, 74.23, 0.66337, 21, -67.3, 31.6, 0.27765, 3, 16, 145.07, -68.18, 0.02484, 20, 154.26, 57.78, 0.49552, 21, -40.57, 53.86, 0.47964, 3, 16, 161.38, -111.98, 0.0016, 20, 178.39, 17.75, 0.11949, 21, 5.58, 61.24, 0.87891, 1, 21, 48.82, 45.44, 1, 1, 21, 77.48, 24.54, 1, 4, 18, 107.42, 177.77, 0.06181, 19, 46.5, 171.06, 0.08834, 20, 133.38, -107.06, 0.11222, 21, 104.46, -27.23, 0.73763, 4, 18, 114.38, 160.66, 0.09755, 19, 49.89, 152.9, 0.14658, 20, 121.38, -121.11, 0.15275, 21, 113, -43.61, 0.60312, 4, 18, 121.07, 144.23, 0.13023, 19, 53.14, 135.46, 0.21509, 20, 109.87, -134.6, 0.16993, 21, 121.2, -59.34, 0.48475, 1, 19, 87.59, -49.05, 1, 2, 18, 178.33, -55.87, 0.02009, 19, 69.08, -72.05, 0.97991, 2, 18, 126.52, -65.79, 0.4097, 19, 16.33, -71.38, 0.5903, 2, 18, 105.14, -93.64, 0.73423, 19, -10.2, -94.37, 0.26577, 3, 11, 223.72, 90.29, 0.0001, 18, 73.96, -89.57, 0.87844, 19, -39.93, -84.13, 0.12146, 4, 7, -104.94, -19.21, 0.17202, 8, -58.38, -91.64, 0.15993, 11, 102.86, 78.7, 0.17524, 18, -42.35, -54.74, 0.49281, 5, 7, -114.54, 8.87, 0.16261, 8, -67.99, -63.55, 0.23116, 11, 89.48, 52.21, 0.35747, 12, -52.8, -2.18, 0.00197, 18, -64.73, -74.22, 0.24678, 5, 7, -104.94, 19.21, 0.12438, 8, -58.38, -53.21, 0.22965, 11, 75.36, 51.86, 0.48062, 12, -51, -16.18, 0.0156, 18, -77.94, -69.23, 0.14976, 5, 7, -135.98, 10.35, 0.0231, 8, -89.42, -62.08, 0.05408, 11, 103.39, 35.84, 0.58898, 12, -37.94, 13.34, 0.30642, 18, -58.02, -94.63, 0.02742, 5, 7, -158.88, 5.17, 0.00436, 8, -112.33, -67.25, 0.01093, 11, 123.09, 23.06, 0.36763, 12, -27.25, 34.25, 0.6114, 18, -44.59, -113.9, 0.00569, 2, 11, 136.25, -11.59, 0.04216, 12, 5.87, 50.91, 0.95784, 2, 12, 45.99, 48.26, 0.95892, 13, -39.44, 32.27, 0.04108, 2, 12, 86.29, 20.44, 0.15914, 13, 9.38, 28.38, 0.84086, 1, 13, 57.72, 34.8, 1, 1, 13, 80.48, 10.16, 1, 2, 11, 7.98, -117.16, 0.00021, 13, 85.27, -27.4, 0.99979, 3, 11, 1.08, -82.58, 0.01795, 12, 90.36, -76.26, 0.00288, 13, 61.33, -53.28, 0.97917, 3, 11, 12.33, -60.25, 0.06144, 12, 66.99, -67.37, 0.03891, 13, 36.64, -57.29, 0.89965, 3, 11, 43.42, -39.2, 0.43026, 12, 42.86, -38.6, 0.29709, 13, 1.35, -44.47, 0.27266, 4, 10, -13.58, 120.79, 0.01754, 11, 13.6, -41.45, 0.89037, 12, 48.16, -68.03, 0.05394, 13, 20.68, -67.29, 0.03816, 4, 10, 7.34, 97.41, 0.18446, 11, -17.73, -40.02, 0.8117, 12, 49.96, -99.35, 0.00259, 13, 37.92, -93.5, 0.00126, 2, 10, 26.01, 105.85, 0.26231, 11, -24.77, -59.26, 0.73769, 2, 10, 44.76, 84.75, 0.33509, 11, -52.96, -57.88, 0.66491, 3, 8, -18.47, 105.68, 0.00372, 10, 34.51, 56.15, 0.60065, 11, -66.22, -30.55, 0.39563, 2, 10, 64.72, 51.79, 0.86568, 11, -90.48, -49.07, 0.13432, 2, 10, 94.84, 11.2, 0.99449, 11, -140.55, -42.17, 0.00551, 2, 10, 93.64, -42.74, 0.9839, 14, -141.26, 25.9, 0.0161, 1, 17, 446.22, 0.39, 1, 1, 17, 451.34, -23.63, 1, 4, 18, 93.3, 160.85, 0.08525, 19, 29.27, 157.32, 0.11526, 20, 111.86, -102.29, 0.16151, 21, 91.99, -45.39, 0.63798, 4, 7, -80.55, -69.47, 0.032, 8, -33.99, -141.89, 0.02002, 11, 121.8, 131.25, 0.01774, 18, -5, -13.2, 0.93024, 2, 11, 186.7, 139.87, 0.00032, 18, 58.37, -29.7, 0.99968, 6, 7, 53.95, -42.86, 0.15206, 14, 134.74, -37.03, 0.00582, 15, 24.27, -65.94, 0.14529, 17, 4.99, 29.67, 0.52518, 18, -80.35, 101.34, 0.00338, 20, -20.81, 24.57, 0.16826, 3, 16, 50.15, -85.81, 0.0807, 17, 12.61, -55.05, 0.17738, 20, 64.24, 22.89, 0.74193, 8, 7, 5.17, -33.99, 0.57133, 8, 51.73, -106.42, 0.00061, 11, 36.54, 167.83, 0.00122, 14, 111.86, -81.01, 0.00089, 15, -20.84, -86.51, 0.02795, 17, -30.36, 64.44, 0.04478, 18, -70.17, 52.82, 0.11267, 20, -59.22, -6.77, 0.24056, 5, 7, 5.17, -138.93, 0.00548, 18, 27.02, 92.38, 0.30665, 19, -49.4, 103.54, 0.06286, 20, 20.59, -74.9, 0.51969, 21, 32.42, -119.77, 0.10532, 4, 7, -42.12, 73.16, 0.00924, 8, 4.43, 0.74, 0.9474, 9, -77.59, -6.65, 0.03318, 10, -70.91, 35.58, 0.01019, 4, 7, 36.21, 71.68, 0.01982, 8, 82.77, -0.74, 0.00781, 9, 0.74, -8.13, 0.90692, 14, 20.07, -20.13, 0.06545, 4, 8, -33.25, 35.47, 0.09133, 9, -115.28, 28.08, 0.0004, 10, -35.35, 72.49, 0.03393, 11, -5.65, 7.91, 0.87434, 4, 7, -140.41, 53.95, 0.00166, 8, -93.85, -18.48, 0.00533, 11, 75.28, 2.22, 0.99124, 18, -96.73, -115.18, 0.00177, 1, 12, 20.44, 6.06, 1, 2, 12, 58.1, 1.61, 0.75794, 13, -5.6, -2.04, 0.24206, 3, 11, 53.84, -84.79, 0.00867, 12, 87.14, -23.56, 0.00141, 13, 32.15, -9.27, 0.98992, 2, 14, 27.67, 6.91, 0.98521, 15, -33.08, 34.61, 0.01479, 5, 7, 76.86, 19.21, 0.00379, 14, 82.21, 3.2, 0.15473, 15, 7.62, -1.9, 0.8354, 17, 58.84, 68.12, 0.00591, 20, -53.15, 82.29, 0.00016, 3, 14, 123.06, 37.75, 0.00011, 15, 61.09, 0.18, 0.53762, 16, -1.04, 0.11, 0.46227, 2, 16, 32.18, 5.99, 0.76734, 17, 95.21, -11.13, 0.23266, 3, 16, -1.15, -75.11, 0.00283, 17, 7.91, -2.86, 0.9769, 20, 11.85, 23.92, 0.02027, 1, 17, 225.64, -3.31, 1, 2, 16, 19.75, 3.79, 0.10066, 17, 89.48, 0.13, 0.89934, 1, 17, 294.08, -5.18, 1, 1, 17, 425.5, -2.49, 1], "hull": 74}}}}, "animations": {"animation": {"bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "Tam": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "it2": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": -1.96}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1, "x": 0.82, "y": -14.86}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "it1": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": -9.78}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "it3": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": 26.67}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "tp": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "tp2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1, "x": -5.28, "y": 6.46}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "tp3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1, "x": 8.22, "y": 4.7}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "tp4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "tp5": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 4.45}, {"time": 1.5, "angle": -4.29}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "tp6": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 4.45}, {"time": 1.5, "angle": -4.29}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "tp7": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 4.45}, {"time": 1.5, "angle": -4.29}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "tp8": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "tp9": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "tp10": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "tp11": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": 6.15}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "tp12": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "tp13": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": -4.96}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "tp14": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.4667, "angle": 2.6}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "tp15": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.4667, "angle": 2.6}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "k": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "text3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1, "x": 0.07, "y": -7.35}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}}}}}