{"skeleton": {"hash": "Sf1WVgjOexMlT7vIvUSH5mPvQCs", "spine": "3.7.94", "width": 160, "height": 141.5, "images": "./images/", "audio": "/Volumes/Lulu/SonTinhThuyTinh/Spine/<PERSON><PERSON><PERSON>-Item"}, "bones": [{"name": "root"}, {"name": "ST", "parent": "root", "x": 37.66, "color": "ff0000ff"}, {"name": "TT", "parent": "root", "x": -29.96, "color": "004dfcff"}, {"name": "text", "parent": "root"}, {"name": "da1", "parent": "root", "x": -60.78, "y": -7.7, "color": "00fc14ff"}, {"name": "da2", "parent": "root", "x": 55.21, "y": -25.68, "color": "04f825ff"}, {"name": "da3", "parent": "root", "x": -54.36, "y": 17.98, "color": "0df107ff"}, {"name": "da4", "parent": "root", "x": 60.78, "y": 2.14, "color": "24f105ff"}, {"name": "da5", "parent": "root", "x": 5.99, "y": 29.96, "color": "37f006ff"}, {"name": "danho1", "parent": "root", "x": -24.4, "y": 39.38, "color": "22f906ff"}, {"name": "danho2", "parent": "root", "x": 59.49, "y": 42.37, "color": "20f707ff"}, {"name": "vetsang", "parent": "root", "rotation": 89.87, "x": 17.27, "y": -74.01, "scaleX": 1.364, "scaleY": 8.081, "color": "e3f404ff"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "root"}, {"name": "da6", "parent": "root", "x": 15.26, "y": -18.34, "color": "24f105ff"}], "slots": [{"name": "Layer 598 copy 3", "bone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attachment": "Layer 598 copy 3"}, {"name": "da5", "bone": "da5", "attachment": "da5"}, {"name": "da2", "bone": "da2", "attachment": "da2"}, {"name": "da4", "bone": "da4", "attachment": "da4"}, {"name": "da6", "bone": "da6", "attachment": "da4"}, {"name": "da3", "bone": "da3", "attachment": "da3"}, {"name": "da1", "bone": "da1", "attachment": "da1"}, {"name": "daNho2", "bone": "danho2", "attachment": "daNho2"}, {"name": "daNho1", "bone": "danho1", "attachment": "daNho1"}, {"name": "Group 13", "bone": "ST", "attachment": "Group 13"}, {"name": "set", "bone": "root", "attachment": "set"}, {"name": "Group 12", "bone": "TT", "attachment": "Group 12"}, {"name": "Text", "bone": "text", "attachment": "Text"}, {"name": "textBright", "bone": "text", "attachment": "textBright"}, {"name": "<PERSON><PERSON>", "bone": "root", "attachment": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "bright", "bone": "vetsang", "attachment": "bright"}], "skins": {"default": {"Group 12": {"Group 12": {"x": 2.8, "y": 10.12, "width": 93, "height": 118}}, "Group 13": {"Group 13": {"x": -13.32, "y": 13.62, "width": 86, "height": 113}}, "Hatsang": {"Hatsang": {"x": 1.34, "y": 11.12, "width": 134, "height": 100}}, "Layer 598 copy 3": {"Layer 598 copy 3": {"x": 0.84, "y": -55.88, "width": 139, "height": 31}}, "Text": {"Text": {"x": 1.34, "y": -45.38, "width": 158, "height": 29}}, "bright": {"bright": {"x": 0.94, "y": 1.84, "width": 5, "height": 23}}, "da1": {"da1": {"x": -0.38, "y": -2.17, "width": 37, "height": 58}}, "da2": {"da2": {"type": "mesh", "uvs": [1, 1, 0.66853, 1, 0.63982, 0, 1, 0], "triangles": [1, 2, 3, 0, 1, 3], "vertices": [20.13, -23.2, -23.29, -23.2, -27.05, 38.8, 20.13, 38.8], "hull": 4, "edges": [0, 6, 0, 2, 4, 6, 4, 2], "width": 131, "height": 62}}, "da3": {"da3": {"x": -4.3, "y": 1.65, "width": 28, "height": 27}}, "da4": {"da4": {"x": -6.93, "y": 9.48, "width": 41, "height": 61}}, "da5": {"da5": {"x": 5.35, "y": 4.66, "width": 92, "height": 53}}, "da6": {"da4": {"x": -6.93, "y": 9.48, "width": 41, "height": 61}}, "daNho1": {"daNho1": {"x": -0.76, "y": 2.75, "width": 11, "height": 16}}, "daNho2": {"daNho2": {"x": 0.85, "y": 2.25, "width": 10, "height": 11}}, "khungduoi": {"khungduoi": {"type": "clipping", "end": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vertexCount": 59, "vertices": [-66.52, -50.03, -65.1, -52.61, -65.07, -56.45, -64.47, -58.86, -62.8, -62.01, -60.12, -63.88, -56.03, -65.16, -53.48, -65.42, -49.13, -64.69, -46.98, -62.95, -45.31, -60.67, -42.7, -57.38, -42.16, -56.25, 41.44, -56.07, 43.69, -58.6, 48.63, -63.54, 52.98, -65.36, 58.64, -65.1, 61.43, -64.06, 64.42, -61.2, 65.4, -59.31, 66.44, -56.13, 66.63, -53.27, 65.92, -50.8, 65.27, -48.85, 63.51, -46.64, 65.33, -45.01, 66.5, -46.64, 68.06, -49.11, 69.1, -51.58, 68.91, -56.84, 66.7, -61.72, 65.66, -63.21, 63.45, -65.88, 61.82, -66.98, 59.81, -67.7, 53.24, -68.54, 49.8, -67.44, 47.78, -66.07, 44.99, -63.8, 43.17, -61.85, 40.83, -59.31, -40.01, -59.31, -41.24, -60.87, -42.87, -62.69, -44.95, -64.71, -47.09, -66.27, -50.28, -67.57, -51.45, -67.76, -54.89, -67.89, -57.75, -67.63, -60.03, -66.66, -62.37, -65.23, -63.93, -63.67, -65.36, -61.85, -65.94, -59.96, -67.11, -57.95, -67.31, -55.28, -67.24, -52.16], "color": "ce3a3aff"}}, "set": {"set": {"x": 3.34, "y": 10.62, "width": 60, "height": 119}}, "textBright": {"textBright": {"x": 0.84, "y": -42.88, "width": 125, "height": 22}}}}, "animations": {"animation": {"slots": {"Group 12": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff"}]}, "Group 13": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff"}]}, "Hatsang": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1667, "color": "ffffff00"}, {"time": 1.2333, "color": "ffffffff"}, {"time": 1.3, "color": "ffffff00"}, {"time": 1.3667, "color": "ffffffff"}, {"time": 1.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5667, "color": "ffffff00"}, {"time": 1.7, "color": "ffffffff"}, {"time": 1.8, "color": "ffffff00"}]}, "da1": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffff00"}, {"time": 0.2667, "color": "ffffffff"}]}, "da2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffff00"}, {"time": 0.2667, "color": "ffffffff"}]}, "da3": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4667, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}]}, "da4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4667, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}]}, "da5": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}, {"time": 0.6333, "color": "ffffffff"}]}, "da6": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4667, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}]}, "daNho1": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.7333, "color": "ffffffff"}]}, "daNho2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff"}]}, "set": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1667, "color": "ffffff00"}, {"time": 1.2333, "color": "ffffffff"}, {"time": 1.3, "color": "ffffff00"}, {"time": 1.3667, "color": "ffffffff"}, {"time": 1.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5667, "color": "ffffff00"}, {"time": 1.7, "color": "ffffffff"}, {"time": 1.8, "color": "ffffff00"}]}, "textBright": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1667, "color": "ffffff00"}, {"time": 1.2333, "color": "ffffffff"}, {"time": 1.3, "color": "ffffff00"}, {"time": 1.3667, "color": "ffffffff"}, {"time": 1.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5667, "color": "ffffff00"}, {"time": 1.7, "color": "ffffffff"}, {"time": 1.8, "color": "ffffff00"}]}}, "bones": {"text": {"translate": [{"time": 0, "x": 0, "y": 13.09}, {"time": 0.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1.563, "y": 1.563}, {"time": 0.1667, "x": 0.984, "y": 0.984}, {"time": 0.2, "x": 1.033, "y": 1.033}, {"time": 0.2667, "x": 1, "y": 1}]}, "vetsang": {"translate": [{"time": 0.8333, "x": 0, "y": 0, "curve": [0.125, 0.2, 0.596, 0.99]}, {"time": 1.3, "x": 0, "y": 32.36}]}, "TT": {"translate": [{"time": 0, "x": 22.19, "y": 0, "curve": "stepped"}, {"time": 0.1, "x": 22.19, "y": 0, "curve": [0.173, 0.16, 0.75, 1]}, {"time": 0.3333, "x": 0, "y": 0}]}, "ST": {"translate": [{"time": 0, "x": -12.52, "y": 0, "curve": "stepped"}, {"time": 0.1, "x": -12.52, "y": 0, "curve": [0.192, 0.17, 0.75, 1]}, {"time": 0.3333, "x": 0, "y": 0}]}, "da1": {"translate": [{"time": 0, "x": 24.47, "y": -19.35, "curve": "stepped"}, {"time": 0.2667, "x": 24.47, "y": -19.35, "curve": [0.284, 0.44, 0.75, 1]}, {"time": 0.5, "x": 0, "y": 0}]}, "da2": {"translate": [{"time": 0, "x": -23.33, "y": -11.38, "curve": "stepped"}, {"time": 0.2667, "x": -23.33, "y": -11.38, "curve": [0.245, 0.42, 0.75, 1]}, {"time": 0.5, "x": 0, "y": 0}]}, "da3": {"translate": [{"time": 0, "x": -2.85, "y": -19.35, "curve": "stepped"}, {"time": 0.5, "x": -2.85, "y": -19.35, "curve": [0.231, 0.4, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 0}]}, "da4": {"translate": [{"time": 0, "x": -2.28, "y": -30.16, "curve": "stepped"}, {"time": 0.5, "x": -2.28, "y": -30.16, "curve": [0.279, 0.49, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 0}]}, "da5": {"translate": [{"time": 0, "x": 0, "y": -23.9, "curve": "stepped"}, {"time": 0.6333, "x": 0, "y": -23.9, "curve": [0.308, 0.5, 0.75, 1]}, {"time": 0.8, "x": 0, "y": 0}]}, "danho1": {"translate": [{"time": 0.7333, "x": 0, "y": 0}, {"time": 2, "x": 0, "y": 8.53}]}, "danho2": {"translate": [{"time": 0, "x": 0, "y": -13.09}, {"time": 0.6667, "x": 0, "y": -6.26}, {"time": 2, "x": 0, "y": 2.84}]}, "da6": {"translate": [{"time": 0, "x": -2.28, "y": -30.16, "curve": "stepped"}, {"time": 0.5, "x": -2.28, "y": -30.16, "curve": [0.279, 0.49, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 0}]}}}}}