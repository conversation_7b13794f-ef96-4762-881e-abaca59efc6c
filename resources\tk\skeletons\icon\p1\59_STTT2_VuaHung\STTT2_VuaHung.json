{"skeleton": {"hash": "Mgn55zxg5jdQ9ck0uzRvfEQEhaI", "spine": "3.7.94", "width": 111.68, "height": 132, "images": "./images/", "audio": "D:/Job/support/anim/Vuahung"}, "bones": [{"name": "root"}, {"name": "VuaHung", "parent": "root", "color": "db07f5ff"}, {"name": "vua hung", "parent": "VuaHung", "x": -3.11, "y": -4.14, "scaleX": 0.8, "scaleY": 0.8}, {"name": "khung", "parent": "VuaHung", "y": -1.39}, {"name": "clipping_BG", "parent": "VuaHung"}, {"name": "effect_trongdong", "parent": "khung", "x": 2.61}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "khung", "y": 1.39, "color": "ffe800ff"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parent": "khung", "x": -53.19, "y": 68.56, "color": "faff00ff"}], "slots": [{"name": "BG", "bone": "VuaHung", "attachment": "BG-New"}, {"name": "clipping_BG", "bone": "clipping_BG", "attachment": "clipping_BG"}, {"name": "trong_dong", "bone": "effect_trongdong", "attachment": "trong_dong"}, {"name": "toc_sau", "bone": "vua hung", "attachment": "toc_sau"}, {"name": "body", "bone": "vua hung", "attachment": "body"}, {"name": "khung<PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attachment": "khung<PERSON><PERSON>"}, {"name": "khungNew4", "bone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attachment": "khung<PERSON><PERSON>"}, {"name": "khungNew2", "bone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attachment": "khung<PERSON><PERSON>"}, {"name": "khungNew3", "bone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attachment": "khung<PERSON><PERSON>"}, {"name": "head", "bone": "vua hung", "attachment": "head"}, {"name": "sang<PERSON>hung-mask", "bone": "khung", "attachment": "sang<PERSON>hung-mask"}, {"name": "Light", "bone": "<PERSON><PERSON><PERSON><PERSON>", "attachment": "Light"}, {"name": "Light2", "bone": "<PERSON><PERSON><PERSON><PERSON>", "attachment": "Light"}], "skins": {"default": {"BG": {"BG-New": {"y": 0.5, "scaleX": 0.789, "scaleY": 0.789, "width": 140, "height": 135}}, "Light": {"Light": {"width": 40, "height": 40}}, "Light2": {"Light": {"x": 109.53, "width": 40, "height": 40}}, "body": {"body": {"type": "mesh", "path": "Group 12 copy 2", "uvs": [0.96961, 0.79969, 0.92812, 0.91158, 0.18004, 0.89793, 0.09171, 0.74511, 0.09439, 0, 0.96961, 0], "triangles": [5, 3, 4, 0, 3, 5, 2, 3, 0, 1, 2, 0], "vertices": [70.76, -54.55, 64.45, -62.91, -49.45, -61.89, -62.9, -50.48, -62.49, 5.15, 70.76, 5.15], "hull": 6, "edges": [8, 10, 6, 8, 4, 6, 2, 4, 10, 0, 0, 2], "width": 143, "height": 72}}, "clipping_BG": {"clipping_BG": {"type": "clipping", "end": "trong_dong", "vertexCount": 8, "vertices": [47.53, 52.04, -47.95, 52.68, -53.32, 47.05, -53.65, -49.89, -47.25, -54.14, 48.2, -54.45, 54.49, -49.78, 53.83, 45.76], "color": "ce3a3aff"}}, "head": {"head": {"type": "mesh", "path": "Group 12 copy", "uvs": [0.94854, 0.3409, 0.94289, 0.42742, 0.94136, 0.45081, 0.93953, 0.47876, 0.93722, 0.61494, 0.99999, 0.75742, 1, 0.89147, 0.98634, 0.95736, 0.96696, 0.958, 0.91087, 0.958, 0.90698, 0.9562, 0.89217, 0.958, 0.62967, 0.958, 0.6021, 0.91019, 0.61025, 0.958, 0.58292, 0.958, 0.56041, 0.958, 0.52119, 0.958, 0.4831, 0.93242, 0.45739, 0.85115, 0.43633, 0.76524, 0.4377, 0.67622, 0.45706, 0.33958, 0.4832, 2e-05, 1, 1e-05, 0.85365, 0.57276, 0.78931, 0.60031, 0.70768, 0.56787, 0.61983, 0.47115, 0.65165, 0.49625, 0.72221, 0.49625, 0.74089, 0.4736, 0.7214, 0.45853, 0.68532, 0.44864, 0.65191, 0.4567, 0.8412, 0.4987, 0.8827, 0.4987, 0.90374, 0.48157, 0.88409, 0.46258, 0.84258, 0.46136, 0.82045, 0.48067, 0.76436, 0.42806, 0.70443, 0.40897, 0.62915, 0.41025, 0.58077, 0.46879, 0.75908, 0.4654, 0.72073, 0.44249, 0.64258, 0.43697, 0.81182, 0.4357, 0.84395, 0.41788, 0.8997, 0.41868, 0.81518, 0.46667, 0.84155, 0.45225, 0.89669, 0.44546, 0.92545, 0.45267, 0.58408, 0.38988, 0.69867, 0.35764, 0.83675, 0.37079, 0.91155, 0.41279, 0.69848, 0.64774, 0.78343, 0.62907, 0.83874, 0.63091, 0.83158, 0.66038, 0.78343, 0.65253, 0.71417, 0.65971, 0.70948, 0.63031, 0.78776, 0.61088, 0.85097, 0.64702, 0.86634, 0.64891, 0.83482, 0.67315, 0.78017, 0.66974, 0.70006, 0.67521, 0.68298, 0.65358, 0.70035, 0.62426, 0.85056, 0.62426, 0.62248, 0.63974, 0.5707, 0.71575, 0.55991, 0.77811, 0.55775, 0.9022, 0.91446, 0.65402, 0.95186, 0.72402, 0.96839, 0.8058, 0.98206, 0.8952, 0.69727, 0.55921, 0.60716, 0.63027, 0.5561, 0.71746, 0.54387, 0.77918, 0.53956, 0.90664, 0.86765, 0.56629, 0.92878, 0.6452, 0.96689, 0.71838, 0.98343, 0.80143, 0.81014, 0.4854, 0.83891, 0.50994, 0.8824, 0.50843, 0.91083, 0.48115, 0.60637, 0.47873, 0.64712, 0.50752, 0.72144, 0.51085, 0.7502, 0.48206, 0.62962, 0.39767, 0.58154, 0.4505, 0.70411, 0.39379, 0.77205, 0.4155, 0.80679, 0.41996, 0.83966, 0.40202, 0.93167, 0.43445, 0.90744, 0.54751, 0.88931, 0.5778, 0.78543, 0.4592, 0.79402, 0.548], "triangles": [24, 56, 23, 56, 22, 23, 56, 24, 57, 0, 57, 24, 55, 22, 56, 103, 102, 56, 100, 55, 56, 102, 100, 56, 58, 105, 57, 57, 103, 56, 103, 42, 102, 100, 102, 42, 100, 101, 55, 43, 100, 42, 0, 58, 57, 103, 57, 104, 50, 49, 105, 58, 50, 105, 105, 104, 57, 104, 105, 49, 1, 58, 0, 41, 42, 103, 106, 58, 1, 50, 58, 106, 48, 104, 49, 47, 43, 42, 46, 42, 41, 47, 42, 46, 53, 49, 50, 53, 50, 106, 33, 47, 46, 101, 22, 55, 43, 101, 100, 44, 101, 43, 2, 106, 1, 52, 48, 49, 52, 49, 53, 54, 53, 106, 54, 106, 2, 34, 47, 33, 45, 32, 46, 33, 46, 32, 104, 41, 103, 48, 41, 104, 48, 109, 41, 41, 45, 46, 38, 39, 52, 53, 38, 52, 109, 45, 41, 51, 48, 52, 51, 52, 39, 109, 48, 51, 44, 22, 101, 47, 44, 43, 28, 44, 47, 28, 47, 34, 31, 32, 45, 96, 44, 28, 3, 54, 2, 40, 51, 39, 95, 53, 54, 95, 54, 3, 37, 38, 53, 95, 37, 53, 99, 31, 45, 92, 109, 51, 92, 51, 40, 30, 32, 31, 30, 31, 99, 29, 28, 34, 29, 34, 33, 30, 29, 33, 30, 33, 32, 36, 39, 38, 36, 38, 37, 35, 40, 39, 35, 39, 36, 97, 28, 29, 94, 35, 36, 35, 92, 40, 93, 35, 94, 98, 29, 30, 37, 94, 36, 37, 95, 94, 107, 94, 95, 109, 99, 45, 27, 99, 109, 109, 25, 110, 93, 92, 35, 92, 25, 109, 98, 97, 29, 83, 97, 98, 88, 93, 94, 88, 94, 107, 93, 25, 92, 27, 83, 98, 98, 30, 99, 27, 98, 99, 109, 110, 27, 88, 25, 93, 108, 88, 107, 26, 27, 110, 26, 110, 25, 26, 73, 27, 3, 107, 95, 4, 107, 3, 108, 107, 4, 27, 75, 83, 26, 65, 73, 74, 26, 25, 61, 66, 26, 26, 66, 65, 97, 84, 96, 97, 96, 28, 84, 97, 83, 75, 84, 83, 60, 65, 66, 74, 61, 26, 60, 66, 61, 27, 73, 75, 89, 108, 4, 88, 108, 89, 68, 67, 74, 61, 74, 67, 25, 79, 74, 60, 61, 67, 73, 72, 75, 59, 73, 65, 59, 65, 60, 79, 25, 88, 68, 74, 79, 60, 64, 59, 62, 63, 60, 59, 72, 73, 88, 89, 79, 63, 64, 60, 67, 62, 60, 70, 64, 63, 69, 62, 67, 71, 59, 64, 72, 59, 71, 21, 22, 44, 21, 84, 85, 44, 84, 21, 84, 44, 96, 76, 84, 75, 76, 85, 84, 90, 4, 5, 89, 4, 90, 80, 79, 89, 90, 80, 89, 20, 21, 85, 77, 85, 76, 86, 20, 85, 77, 86, 85, 90, 91, 80, 5, 91, 90, 81, 80, 91, 19, 20, 86, 91, 5, 6, 81, 91, 6, 82, 81, 6, 78, 86, 77, 87, 19, 86, 71, 76, 75, 71, 75, 72, 77, 76, 71, 78, 87, 86, 71, 64, 70, 13, 77, 71, 78, 77, 13, 70, 63, 62, 70, 62, 69, 18, 19, 87, 80, 81, 69, 79, 69, 68, 79, 80, 69, 10, 81, 82, 7, 82, 6, 9, 10, 82, 82, 8, 9, 10, 11, 81, 7, 8, 82, 17, 18, 87, 70, 13, 71, 12, 13, 11, 13, 70, 11, 69, 67, 68, 11, 69, 81, 11, 70, 69, 15, 16, 78, 13, 15, 78, 87, 78, 16, 17, 87, 16, 14, 15, 13], "vertices": [39.64, 40.58, 38.81, 26.3, 38.59, 22.44, 38.32, 17.83, 37.98, -4.64, 47.15, -28.15, 47.15, -50.27, 45.15, -61.14, 42.33, -61.25, 34.14, -61.25, 33.57, -60.95, 31.41, -61.25, -6.92, -61.25, -10.94, -53.36, -9.75, -61.25, -13.74, -61.25, -17.03, -61.25, -22.76, -61.25, -28.32, -57.03, -32.07, -43.62, -35.15, -29.44, -34.95, -14.75, -32.12, 40.79, -28.3, 96.82, 47.15, 96.82, 25.78, 2.32, 16.39, -2.23, 4.47, 3.12, -8.36, 19.08, -3.71, 14.94, 6.59, 14.94, 9.32, 18.68, 6.47, 21.17, 1.21, 22.8, -3.67, 21.47, 23.96, 14.54, 30.02, 14.54, 33.1, 17.36, 30.23, 20.5, 24.17, 20.7, 20.94, 17.51, 12.75, 26.19, 4, 29.34, -6.99, 29.13, -14.06, 19.47, 11.98, 20.03, 6.38, 23.81, -5.03, 24.72, 19.68, 24.93, 24.37, 27.87, 32.51, 27.74, 20.17, 19.82, 24.02, 22.2, 32.07, 23.32, 36.27, 22.13, -13.57, 32.49, 3.16, 37.81, 23.32, 35.64, 34.24, 28.71, 3.13, -10.05, 15.53, -6.97, 23.61, -7.28, 22.56, -12.14, 15.53, -10.84, 5.42, -12.03, 4.73, -7.18, 16.16, -3.97, 25.39, -9.94, 27.63, -10.25, 23.03, -14.25, 15.05, -13.68, 3.36, -14.59, 0.86, -11.02, 3.4, -6.18, 25.33, -6.18, -7.97, -8.73, -15.53, -21.28, -17.1, -31.57, -17.42, -52.04, 34.66, -11.09, 40.12, -22.64, 42.53, -36.13, 44.53, -50.89, 2.95, 4.55, -10.21, -7.17, -17.66, -21.56, -19.45, -31.74, -20.08, -52.77, 27.83, 3.39, 36.75, -9.63, 42.32, -21.71, 44.73, -35.41, 19.43, 16.73, 23.63, 12.68, 29.98, 12.93, 34.13, 17.43, -10.32, 17.83, -4.37, 13.08, 6.48, 12.53, 10.68, 17.28, -6.93, 31.21, -13.95, 22.49, 3.95, 31.85, 13.87, 28.26, 18.94, 27.53, 23.74, 30.49, 37.17, 25.14, 33.63, 6.48, 30.99, 1.49, 15.82, 21.05, 17.08, 6.4], "hull": 25, "edges": [46, 44, 42, 40, 40, 38, 38, 36, 36, 34, 10, 8, 46, 48, 50, 52, 52, 54, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 56, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 70, 82, 84, 84, 86, 86, 88, 82, 90, 90, 92, 92, 94, 94, 88, 96, 98, 98, 100, 96, 102, 102, 104, 104, 106, 108, 106, 110, 112, 112, 114, 114, 116, 118, 120, 124, 126, 126, 128, 130, 132, 132, 122, 122, 134, 134, 124, 130, 118, 118, 128, 146, 52, 52, 148, 28, 30, 22, 24, 14, 16, 16, 18, 158, 160, 160, 162, 162, 164, 164, 16, 150, 152, 152, 154, 154, 156, 156, 30, 54, 150, 50, 158, 30, 32, 32, 34, 10, 12, 12, 14, 24, 26, 26, 28, 18, 20, 20, 22, 176, 178, 178, 180, 180, 182, 182, 12, 166, 168, 168, 170, 170, 172, 172, 174, 174, 32, 120, 134, 88, 202, 202, 110, 200, 202, 200, 204, 204, 206, 208, 210, 210, 116, 48, 0, 108, 6, 4, 6, 212, 4, 212, 100, 0, 2, 2, 4, 116, 2, 6, 8, 42, 44, 190, 214, 214, 216, 54, 218, 218, 50, 54, 220, 220, 50, 52, 220, 220, 218], "width": 146, "height": 165}}, "khungNew": {"khungNew": {"x": -25.53, "y": 25.51, "scaleX": 0.822, "scaleY": 0.822, "width": 71, "height": 71}}, "khungNew2": {"khungNew": {"x": 27.17, "y": 25.51, "scaleX": -0.82, "scaleY": 0.822, "width": 71, "height": 71}}, "khungNew3": {"khungNew": {"x": 27.17, "y": -26.83, "scaleX": -0.82, "scaleY": -0.82, "width": 71, "height": 71}}, "khungNew4": {"khungNew": {"x": -25.53, "y": -26.83, "scaleX": 0.822, "scaleY": -0.82, "width": 71, "height": 71}}, "sangKhung-mask": {"sangKhung-mask": {"type": "clipping", "end": "sang<PERSON>hung-mask", "vertexCount": 67, "vertices": [-54.67, 50.81, -52.49, 53.81, -49.41, 55.76, -42.2, 56.08, -33.29, 56, -30.54, 53, -43.17, 52.43, -45.12, 53.73, -47.79, 54.22, -50.46, 52.68, -51.52, 50, -50.95, 47.57, -48.92, 46.12, -44.87, 45.55, -44.31, 44.33, -46.17, 43.28, -48.36, 42.63, -48.19, 32.43, -51.27, 29.19, -51.24, -42.99, -49.65, -42.07, -46.83, -41.92, -44.4, -42.45, -42.5, -44.28, -41.74, -45.87, -43.64, -47.85, -44.86, -45.57, -47.97, -44.81, -50.1, -46.25, -50.86, -47.93, -50.1, -50.28, -48.05, -52.18, -46.07, -52.26, 44.9, -51.5, 47.63, -52.26, 52.72, -49.98, 53.48, -47.32, 52.65, -44.28, 52.95, 28.85, 49.99, 31.82, 49.99, 42.91, 48.09, 42.91, 45.58, 44.13, 45.88, 45.73, 49.08, 45.8, 51.81, 46.94, 52.65, 49.68, 52.42, 50.97, 51.36, 52.87, 48.62, 54.01, 51.2, 55.68, 53.56, 55.07, 55.54, 53.25, 56.45, 51.65, 56.3, 49.37, 56.27, -46.71, 55.89, -50.16, 54.84, -52.33, 53.27, -53.46, 51.92, -54.21, 49.82, -54.73, -47.3, -54.58, -50.08, -54.36, -52.18, -53.16, -53.68, -51.21, -54.58, -48.13, -55.03, 46.71], "color": "ce3a3aff"}}, "toc_sau": {"toc_sau": {"path": "Group 12", "x": -2.35, "y": -23.68, "width": 111, "height": 89}}, "trong_dong": {"trong_dong": {"width": 217, "height": 200}}}}, "animations": {"animation": {"slots": {"BG": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.4333, "color": "44488cff", "curve": "stepped"}, {"time": 1.0667, "color": "44488cff"}, {"time": 1.5, "color": "ffffffff"}]}, "trong_dong": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "color": "ffffff00"}]}}, "bones": {"effect_trongdong": {"scale": [{"time": 0, "x": 0.652, "y": 0.652, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 1.222, "y": 1.222}]}, "sangKhung": {"translate": [{"time": 0.5667, "x": 0, "y": 0, "curve": [0.222, 0.41, 0.519, 1]}, {"time": 1.3333, "x": 0, "y": -144.43}]}, "VuaHung": {"translate": [{"time": 0, "x": 0, "y": -8}]}}, "deform": {"default": {"head": {"head": [{"time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "offset": 2, "vertices": [-1.61215, 2.41824, -1.74526, 2.58275, -0.93918, 2.58275, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.54182, 0, -5.82527, 0, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, 0, 2.55134, -1.23943, 2.90106, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -1.23943, 1.44599, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.52915, 1.44599, -1.62387, 2.10325, -2.03877, 1.69897, -2.42994, 3.23176, -2.42994, 3.23176, -2.41824, 4.35283, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.42994, 3.23176, -2.42994, 3.23176, 0, 0, 0, 0, 0, 0, 0.12305, 2.75307, 0, 0, 0, 0, 0, 0, -2.42994, 3.23176, -2.42994, 2.56044, -0.91948, 2.24544, -0.72755, 1.7461, -1.23943, 1.44599, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -1.23943, 1.44599, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -1.46265, 3.23176, 0, 2.47363, 0.31912, 3.19125, -2.42994, 3.23176, -2.42994, 3.23176], "curve": [0.29, 0, 0.629, 0.38]}, {"time": 0.8333, "offset": 2, "vertices": [-1.61215, 2.41824, -1.74526, 2.58275, -0.93918, 2.58275, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.54182, 0, -6.27339, 0, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, 0, 2.55134, -1.23943, 2.90106, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -3.3311, 0.88878, -2.42994, 1.24924, -2.42994, 3.23176, -1.23943, 1.44599, -2.42994, 2.15038, -2.42994, 1.42946, -2.42994, 3.23176, -2.42994, 1.42945, -2.42994, 1.60969, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 2.15037, -2.52915, 3.06806, -1.62387, 2.10325, -2.03877, 1.69897, -2.42994, 3.23176, -2.42994, 3.23176, -2.41824, 4.35283, -2.42994, 3.23176, -2.42994, 3.95268, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 4.31315, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.42994, 3.23176, -2.42994, 3.23176, 0, 0, 0, 0, 0, 0, 0.12306, 2.75308, 0, 0, 0, 0, 0, 0, -2.42994, 3.23176, -2.42994, 2.56044, -0.91948, 2.24544, -0.72755, 1.7461, -1.23943, 1.44599, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -1.23943, 1.44599, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -1.46265, 3.23176, 0, 2.47363, 0.47869, 3.19125, -2.42994, 3.23176, -2.42994, 3.23176], "curve": "stepped"}, {"time": 1.3333, "offset": 2, "vertices": [-1.61215, 2.41824, -1.74526, 2.58275, -0.93918, 2.58275, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.54182, 0, -6.27339, 0, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, 0, 2.55134, -1.23943, 2.90106, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -3.3311, 0.88878, -2.42994, 1.24924, -2.42994, 3.23176, -1.23943, 1.44599, -2.42994, 2.15038, -2.42994, 1.42946, -2.42994, 3.23176, -2.42994, 1.42945, -2.42994, 1.60969, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 2.15037, -2.52915, 3.06806, -1.62387, 2.10325, -2.03877, 1.69897, -2.42994, 3.23176, -2.42994, 3.23176, -2.41824, 4.35283, -2.42994, 3.23176, -2.42994, 3.95268, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 4.31315, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.42994, 3.23176, -2.42994, 3.23176, 0, 0, 0, 0, 0, 0, 0.12306, 2.75308, 0, 0, 0, 0, 0, 0, -2.42994, 3.23176, -2.42994, 2.56044, -0.91948, 2.24544, -0.72755, 1.7461, -1.23943, 1.44599, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -1.23943, 1.44599, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -2.42994, 3.23176, -1.46265, 3.23176, 0, 2.47363, 0.47869, 3.19125, -2.42994, 3.23176, -2.42994, 3.23176], "curve": [0.311, 0.25, 0.757, 1]}, {"time": 2}]}}}}}}