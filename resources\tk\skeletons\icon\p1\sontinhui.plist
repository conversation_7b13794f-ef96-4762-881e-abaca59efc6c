<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,126}</string>
                <key>spriteSourceSize</key>
                <string>{122,126}</string>
                <key>textureRect</key>
                <string>{{420,124},{122,126}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{112,129}</string>
                <key>spriteSourceSize</key>
                <string>{112,129}</string>
                <key>textureRect</key>
                <string>{{790,124},{112,129}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{120,126}</string>
                <key>spriteSourceSize</key>
                <string>{120,126}</string>
                <key>textureRect</key>
                <string>{{542,124},{120,126}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{116,123}</string>
                <key>spriteSourceSize</key>
                <string>{116,123}</string>
                <key>textureRect</key>
                <string>{{1292,0},{116,123}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{113,119}</string>
                <key>spriteSourceSize</key>
                <string>{113,119}</string>
                <key>textureRect</key>
                <string>{{1076,0},{113,119}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>6.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{117,116}</string>
                <key>spriteSourceSize</key>
                <string>{117,116}</string>
                <key>textureRect</key>
                <string>{{847,0},{117,116}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>7.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{128,124}</string>
                <key>spriteSourceSize</key>
                <string>{128,124}</string>
                <key>textureRect</key>
                <string>{{1408,0},{128,124}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>8.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{117,95}</string>
                <key>spriteSourceSize</key>
                <string>{117,95}</string>
                <key>textureRect</key>
                <string>{{406,0},{117,95}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>9.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{121,76}</string>
                <key>spriteSourceSize</key>
                <string>{121,76}</string>
                <key>textureRect</key>
                <string>{{285,0},{121,76}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>A.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{155,138}</string>
                <key>spriteSourceSize</key>
                <string>{155,138}</string>
                <key>textureRect</key>
                <string>{{1172,124},{155,138}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Btn-nhanh-Active.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{107,67}</string>
                <key>spriteSourceSize</key>
                <string>{107,67}</string>
                <key>textureRect</key>
                <string>{{0,0},{107,67}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Icon_BonusG.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{116,100}</string>
                <key>spriteSourceSize</key>
                <string>{116,100}</string>
                <key>textureRect</key>
                <string>{{523,0},{116,100}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Icon_FreeG.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{108,102}</string>
                <key>spriteSourceSize</key>
                <string>{108,102}</string>
                <key>textureRect</key>
                <string>{{639,0},{108,102}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Icon_Jackpot.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{103,120}</string>
                <key>spriteSourceSize</key>
                <string>{103,120}</string>
                <key>textureRect</key>
                <string>{{1189,0},{103,120}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Icon_NormalG.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{100,111}</string>
                <key>spriteSourceSize</key>
                <string>{100,111}</string>
                <key>textureRect</key>
                <string>{{747,0},{100,111}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>IntroFreeGame.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{1030,269}</string>
                <key>spriteSourceSize</key>
                <string>{1030,269}</string>
                <key>textureRect</key>
                <string>{{404,263},{1030,269}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>J.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{128,128}</string>
                <key>spriteSourceSize</key>
                <string>{128,128}</string>
                <key>textureRect</key>
                <string>{{662,124},{128,128}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>KMN.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{134,163}</string>
                <key>spriteSourceSize</key>
                <string>{134,163}</string>
                <key>textureRect</key>
                <string>{{135,263},{134,163}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>KST.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{135,159}</string>
                <key>spriteSourceSize</key>
                <string>{135,159}</string>
                <key>textureRect</key>
                <string>{{0,263},{135,159}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>KTT.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{135,164}</string>
                <key>spriteSourceSize</key>
                <string>{135,164}</string>
                <key>textureRect</key>
                <string>{{269,263},{135,164}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Khung-Bottom.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{420,124}</string>
                <key>spriteSourceSize</key>
                <string>{420,124}</string>
                <key>textureRect</key>
                <string>{{0,124},{420,124}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>Khung_MainScreen-Top.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{270,133}</string>
                <key>spriteSourceSize</key>
                <string>{270,133}</string>
                <key>textureRect</key>
                <string>{{902,124},{270,133}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>R.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{167,139}</string>
                <key>spriteSourceSize</key>
                <string>{167,139}</string>
                <key>textureRect</key>
                <string>{{1327,124},{167,139}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>dong-tong.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{178,69}</string>
                <key>spriteSourceSize</key>
                <string>{178,69}</string>
                <key>textureRect</key>
                <string>{{107,0},{178,69}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>symbol_small_2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{112,116}</string>
                <key>spriteSourceSize</key>
                <string>{112,116}</string>
                <key>textureRect</key>
                <string>{{964,0},{112,116}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA4444</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>sontinhui.png</string>
            <key>size</key>
            <string>{1536,532}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:545c10a515cfef441cace5bbe2cbdd2a:efb7e5f4e94bb5cf396a6dc209f87093:1fd0326e3dab790fcdaccf0de3e8abe7$</string>
            <key>textureFileName</key>
            <string>sontinhui.png</string>
        </dict>
    </dict>
</plist>
