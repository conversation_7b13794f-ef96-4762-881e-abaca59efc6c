{"skeleton": {"hash": "sOlxaLXQ+XZtqsBD7yJx9E2hjuM", "spine": "3.6.53", "width": 0, "height": 0}, "bones": [{"name": "root"}, {"name": "TL", "parent": "root", "length": 157.92, "rotation": 0.66, "x": -1.11, "y": 0.98}, {"name": "Thang-Tex", "parent": "root", "length": 129.72, "rotation": 0.27, "x": 13.2, "y": -125.74}, {"name": "Xe", "parent": "root", "length": 244.45, "rotation": -7.05, "x": 10.31, "y": 8.64}, {"name": "bone", "parent": "root", "length": 197.33, "rotation": -14.77, "x": -8.25, "y": 11.49}, {"name": "bone2", "parent": "root", "length": 235.66, "rotation": 4.68, "x": 88.1, "y": -117.81}, {"name": "bone4", "parent": "root", "length": 229.59, "rotation": -2.53, "x": 178.78, "y": -17.23}, {"name": "bone5", "parent": "root", "length": 51.33, "rotation": -2.01, "x": -1.53, "y": 0.71}, {"name": "bone8", "parent": "root", "length": 235.66, "rotation": 2.66, "x": 166.09, "y": -59.49}, {"name": "bone9", "parent": "root", "length": 235.66, "rotation": -5.2, "x": 171.16, "y": 44.32}], "slots": [{"name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/nohu", "bone": "TL"}, {"name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/thanglon-text", "bone": "Thang-Tex"}, {"name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-3", "bone": "bone4"}, {"name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-2", "bone": "bone"}, {"name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1", "bone": "Xe"}, {"name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-2", "bone": "bone2"}, {"name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-3", "bone": "bone8"}, {"name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-4", "bone": "bone9"}, {"name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-1", "bone": "Xe"}, {"name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-2", "bone": "Xe"}, {"name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/nohu-text", "bone": "Thang-Tex"}, {"name": "root", "bone": "bone5"}], "skins": {"default": {"4.BomTan/Xe dien/effect/fx-2": {"4.BomTan/Xe dien/effect/fx-2": {"x": 4.23, "y": 27.36, "rotation": 1.03, "width": 872, "height": 407}}, "4.BomTan/Xe dien/effect/fx-3": {"4.BomTan/Xe dien/effect/fx-2": {"x": 4.23, "y": 27.36, "scaleX": 0.75, "scaleY": 0.75, "rotation": 1.03, "width": 872, "height": 407}}, "4.BomTan/Xe dien/effect/fx-4": {"4.BomTan/Xe dien/effect/fx-2": {"x": 4.66, "y": 9.64, "scaleX": 0.663, "scaleY": 0.663, "rotation": 1.03, "width": 872, "height": 407}}, "4.BomTan/Xe dien/effect/fx-den-1": {"4.BomTan/Xe dien/effect/fx-den-1": {"x": -250.52, "y": 54.38, "rotation": 7.05, "width": 311, "height": 248}}, "4.BomTan/Xe dien/effect/fx-den-2": {"4.BomTan/Xe dien/effect/fx-den-1": {"x": 56.23, "y": 3.32, "rotation": 7.05, "width": 311, "height": 248}}, "4.BomTan/Xe dien/effect/nohu": {"4.BomTan/Xe dien/effect/nohu": {"x": -4.29, "y": 30.45, "rotation": -0.66, "width": 881, "height": 455}}, "4.BomTan/Xe dien/effect/nohu-text": {"4.BomTan/Xe dien/effect/nohu-text": {"x": -10.81, "y": 5.42, "scaleX": 1.175, "scaleY": 1.175, "rotation": -0.27, "width": 379, "height": 152}}, "4.BomTan/Xe dien/effect/thanglon-text": {"4.BomTan/Xe dien/effect/thanglon-text": {"x": -0.46, "y": 4.31, "rotation": -0.27, "width": 488, "height": 129}}, "4.BomTan/Xe dien/effect/xe-1": {"4.BomTan/Xe dien/effect/xe-1": {"x": -35.19, "y": -3.39, "rotation": 7.05, "width": 556, "height": 354}}, "4.BomTan/Xe dien/effect/xe-2": {"4.BomTan/Xe dien/effect/xe-2": {"x": 36.31, "y": 1.58, "rotation": 11.38, "width": 556, "height": 354}}, "4.BomTan/Xe dien/effect/xe-3": {"4.BomTan/Xe dien/effect/xe-3": {"x": -46.01, "y": 10.2, "rotation": 2.53, "width": 703, "height": 387}}, "root": {"4.BomTan/Xe dien/effect/guong-vo": {"x": 1.56, "y": -0.66, "rotation": 2.01, "width": 1280, "height": 853}}}}, "animations": {"NoHu": {"slots": {"4.BomTan/Xe dien/effect/fx-2": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "color": "ffffffff", "curve": [0.26, 0, 0.618, 0.44]}, {"time": 0.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-2"}, {"time": 2.1333, "name": null}, {"time": 3.0333, "name": null}, {"time": 3.9333, "name": null}, {"time": 4.8333, "name": null}, {"time": 5.7333, "name": null}, {"time": 6.6333, "name": null}, {"time": 7.5333, "name": null}, {"time": 8.4333, "name": null}, {"time": 9.3333, "name": null}, {"time": 10.2333, "name": null}]}, "4.BomTan/Xe dien/effect/fx-3": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1.3, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1, "color": "ffffff00", "curve": "stepped"}, {"time": 4, "color": "ffffff00", "curve": "stepped"}, {"time": 4.9, "color": "ffffff00", "curve": "stepped"}, {"time": 5.8, "color": "ffffff00", "curve": "stepped"}, {"time": 6.7, "color": "ffffff00", "curve": "stepped"}, {"time": 7.6, "color": "ffffff00", "curve": "stepped"}, {"time": 8.5, "color": "ffffff00", "curve": "stepped"}, {"time": 9.4, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-2"}, {"time": 1.0333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-2"}, {"time": 2.1333, "name": null}, {"time": 3.0333, "name": null}, {"time": 3.9333, "name": null}, {"time": 4.8333, "name": null}, {"time": 5.7333, "name": null}, {"time": 6.6333, "name": null}, {"time": 7.5333, "name": null}, {"time": 8.4333, "name": null}, {"time": 9.3333, "name": null}, {"time": 10.2333, "name": null}]}, "4.BomTan/Xe dien/effect/fx-4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "color": "ffffffff", "curve": [0.284, 0, 0.625, 0.38]}, {"time": 0.4667, "color": "ffffff00", "curve": [0.309, 0.25, 0.651, 0.61]}, {"time": 0.7, "color": "ffffff00", "curve": [0.334, 0.34, 0.676, 0.7]}, {"time": 0.9333, "color": "ffffffff", "curve": [0.382, 0.57, 0.735, 1]}, {"time": 1.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 4.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 4.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 5.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 5.7333, "color": "ffffff00", "curve": "stepped"}, {"time": 6.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 6.6333, "color": "ffffff00", "curve": "stepped"}, {"time": 6.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 7.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 7.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 8.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 8.7333, "color": "ffffff00", "curve": "stepped"}, {"time": 9.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 9.6333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-2"}, {"time": 0.1, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-2"}, {"time": 1.2333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-2"}, {"time": 2.1333, "name": null}, {"time": 3.0333, "name": null}, {"time": 3.9333, "name": null}, {"time": 4.8333, "name": null}, {"time": 5.7333, "name": null}, {"time": 6.6333, "name": null}, {"time": 7.5333, "name": null}, {"time": 8.4333, "name": null}, {"time": 9.3333, "name": null}, {"time": 10.2333, "name": null}]}, "4.BomTan/Xe dien/effect/fx-den-1": {"color": [{"time": 0.9, "color": "ffffff00"}, {"time": 1.0667, "color": "ffffffff"}, {"time": 1.2333, "color": "ffffff00"}, {"time": 1.3667, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00"}, {"time": 2, "color": "ffffffff"}, {"time": 2.1333, "color": "ffffff00"}, {"time": 2.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "color": "ffffff00"}, {"time": 3.0667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 3.4, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 3.7333, "color": "ffffff00"}, {"time": 3.9667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 4.1333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 4.3, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 4.4667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6333, "color": "ffffff00"}, {"time": 4.8667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 5.2, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 5.5333, "color": "ffffff00"}, {"time": 5.7667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 5.9333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 6.1, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 6.2667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 6.4333, "color": "ffffff00"}, {"time": 6.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 6.8333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 7, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 7.3333, "color": "ffffff00"}, {"time": 7.5667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 7.7333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 7.9, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 8.0667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 8.2333, "color": "ffffff00"}, {"time": 8.4667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 8.6333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 8.8, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 8.9667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 9.1333, "color": "ffffff00"}, {"time": 9.3667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 9.5333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 9.7, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 9.8667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 10.0333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.1667, "name": null}, {"time": 0.4, "name": null}, {"time": 0.5333, "name": null}, {"time": 0.7, "name": null}, {"time": 0.7333, "name": null}, {"time": 0.9, "name": null}, {"time": 0.9333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-1"}, {"time": 2.1333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-1"}, {"time": 3.0333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-1"}, {"time": 3.9333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-1"}, {"time": 4.8333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-1"}, {"time": 5.7333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-1"}, {"time": 6.6333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-1"}, {"time": 7.5333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-1"}, {"time": 8.4333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-1"}, {"time": 9.3333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-1"}, {"time": 10.2333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-1"}]}, "4.BomTan/Xe dien/effect/fx-den-2": {"color": [{"time": 0.9, "color": "ffffff00"}, {"time": 1.0667, "color": "ffffffff"}, {"time": 1.2333, "color": "ffffff00"}, {"time": 1.3667, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00"}, {"time": 2, "color": "ffffffff"}, {"time": 2.1333, "color": "ffffff00"}, {"time": 2.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "color": "ffffff00"}, {"time": 3.0667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 3.4, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 3.7333, "color": "ffffff00"}, {"time": 3.9667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 4.1333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 4.3, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 4.4667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6333, "color": "ffffff00"}, {"time": 4.8667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 5.2, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 5.5333, "color": "ffffff00"}, {"time": 5.7667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 5.9333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 6.1, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 6.2667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 6.4333, "color": "ffffff00"}, {"time": 6.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 6.8333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 7, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 7.3333, "color": "ffffff00"}, {"time": 7.5667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 7.7333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 7.9, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 8.0667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 8.2333, "color": "ffffff00"}, {"time": 8.4667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 8.6333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 8.8, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 8.9667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 9.1333, "color": "ffffff00"}, {"time": 9.3667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 9.5333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 9.7, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 9.8667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 10.0333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.1667, "name": null}, {"time": 0.4, "name": null}, {"time": 0.5333, "name": null}, {"time": 0.7, "name": null}, {"time": 0.7333, "name": null}, {"time": 0.9, "name": null}, {"time": 0.9333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-1"}, {"time": 2.1333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-1"}, {"time": 3.0333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-1"}, {"time": 3.9333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-1"}, {"time": 4.8333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-1"}, {"time": 5.7333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-1"}, {"time": 6.6333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-1"}, {"time": 7.5333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-1"}, {"time": 8.4333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-1"}, {"time": 9.3333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-1"}, {"time": 10.2333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/fx-den-1"}]}, "4.BomTan/Xe dien/effect/nohu": {"attachment": [{"time": 0, "name": null}, {"time": 2.1333, "name": null}, {"time": 3.0333, "name": null}, {"time": 3.9333, "name": null}, {"time": 4.8333, "name": null}, {"time": 5.7333, "name": null}, {"time": 6.6333, "name": null}, {"time": 7.5333, "name": null}, {"time": 8.4333, "name": null}, {"time": 9.3333, "name": null}, {"time": 10.2333, "name": null}]}, "4.BomTan/Xe dien/effect/nohu-text": {"attachment": [{"time": 0, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/nohu-text"}, {"time": 2.1333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/nohu-text"}, {"time": 3.0333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/nohu-text"}, {"time": 3.9333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/nohu-text"}, {"time": 4.8333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/nohu-text"}, {"time": 5.7333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/nohu-text"}, {"time": 6.6333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/nohu-text"}, {"time": 7.5333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/nohu-text"}, {"time": 8.4333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/nohu-text"}, {"time": 9.3333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/nohu-text"}, {"time": 10.2333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/nohu-text"}]}, "4.BomTan/Xe dien/effect/thanglon-text": {"attachment": [{"time": 0, "name": null}, {"time": 2.1333, "name": null}, {"time": 3.0333, "name": null}, {"time": 3.9333, "name": null}, {"time": 4.8333, "name": null}, {"time": 5.7333, "name": null}, {"time": 6.6333, "name": null}, {"time": 7.5333, "name": null}, {"time": 8.4333, "name": null}, {"time": 9.3333, "name": null}, {"time": 10.2333, "name": null}]}, "4.BomTan/Xe dien/effect/xe-1": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7, "color": "ffffff00"}, {"time": 0.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8, "color": "ffffffff", "curve": "stepped"}, {"time": 1.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.2, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6, "color": "ffffffff", "curve": "stepped"}, {"time": 3.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 4.1, "color": "ffffffff", "curve": "stepped"}, {"time": 4.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 4.5, "color": "ffffffff", "curve": "stepped"}, {"time": 4.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 5, "color": "ffffffff", "curve": "stepped"}, {"time": 5.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 5.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 5.4, "color": "ffffffff", "curve": "stepped"}, {"time": 5.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 5.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 5.9, "color": "ffffffff", "curve": "stepped"}, {"time": 6.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 6.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 6.3, "color": "ffffffff", "curve": "stepped"}, {"time": 6.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 6.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 6.8, "color": "ffffffff", "curve": "stepped"}, {"time": 6.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 7.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 7.2, "color": "ffffffff", "curve": "stepped"}, {"time": 7.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 7.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 7.7, "color": "ffffffff", "curve": "stepped"}, {"time": 7.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 7.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 8.1, "color": "ffffffff", "curve": "stepped"}, {"time": 8.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 8.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 8.6, "color": "ffffffff", "curve": "stepped"}, {"time": 8.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 8.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 9, "color": "ffffffff", "curve": "stepped"}, {"time": 9.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 9.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 9.5, "color": "ffffffff", "curve": "stepped"}, {"time": 9.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 9.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 9.9, "color": "ffffffff", "curve": "stepped"}, {"time": 10.0333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 0.1667, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 0.4, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 0.5333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 0.7, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 0.7333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 0.9, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 1.1333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 1.2667, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 1.4, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 1.5333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 1.6667, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 1.8, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 1.9333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 2.1333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 2.1667, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 2.3, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 2.4333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 2.5667, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 2.7, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 2.8333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 3.0333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 3.0667, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 3.2, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 3.3333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 3.4667, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 3.6, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 3.7333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 3.9333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 3.9667, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 4.1, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 4.2333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 4.3667, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 4.5, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 4.6333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 4.8333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 4.8667, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 5, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 5.1333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 5.2667, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 5.4, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 5.5333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 5.7333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 5.7667, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 5.9, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 6.0333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 6.1667, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 6.3, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 6.4333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 6.6333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 6.6667, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 6.8, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 6.9333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 7.0667, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 7.2, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 7.3333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 7.5333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 7.5667, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 7.7, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 7.8333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 7.9667, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 8.1, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 8.2333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 8.4333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 8.4667, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 8.6, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 8.7333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 8.8667, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 9, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 9.1333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 9.3333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 9.3667, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 9.5, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 9.6333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 9.7667, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 9.9, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 10.0333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}, {"time": 10.2333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-1"}]}, "4.BomTan/Xe dien/effect/xe-2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff"}, {"time": 0.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.8667, "color": "ffffffff"}, {"time": 0.9667, "color": "ffffff00"}], "attachment": [{"time": 2.1333, "name": null}, {"time": 3.0333, "name": null}, {"time": 3.9333, "name": null}, {"time": 4.8333, "name": null}, {"time": 5.7333, "name": null}, {"time": 6.6333, "name": null}, {"time": 7.5333, "name": null}, {"time": 8.4333, "name": null}, {"time": 9.3333, "name": null}, {"time": 10.2333, "name": null}]}, "4.BomTan/Xe dien/effect/xe-3": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.295, 0, 0.633, 0.37]}, {"time": 0.0667, "color": "ffffff32", "curve": [0.311, 0.24, 0.648, 0.59]}, {"time": 0.1333, "color": "ffffffff", "curve": [0.342, 0.36, 0.757, 1]}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7, "color": "ffffff00", "curve": [0.295, 0, 0.633, 0.37]}, {"time": 0.7667, "color": "ffffff32", "curve": [0.311, 0.24, 0.648, 0.59]}, {"time": 0.8333, "color": "ffffffff", "curve": [0.342, 0.36, 0.757, 1]}, {"time": 0.9, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-3"}, {"time": 0.7, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/xe-3"}, {"time": 2.1333, "name": null}, {"time": 3.0333, "name": null}, {"time": 3.9333, "name": null}, {"time": 4.8333, "name": null}, {"time": 5.7333, "name": null}, {"time": 6.6333, "name": null}, {"time": 7.5333, "name": null}, {"time": 8.4333, "name": null}, {"time": 9.3333, "name": null}, {"time": 10.2333, "name": null}]}, "root": {"color": [{"time": 0.3, "color": "ffffffff"}, {"time": 0.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00"}], "attachment": [{"time": 0.1333, "name": null}, {"time": 0.1667, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/guong-vo"}, {"time": 0.8, "name": null}, {"time": 0.8333, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/guong-vo"}, {"time": 2.1333, "name": null}, {"time": 3.0333, "name": null}, {"time": 3.9333, "name": null}, {"time": 4.8333, "name": null}, {"time": 5.7333, "name": null}, {"time": 6.6333, "name": null}, {"time": 7.5333, "name": null}, {"time": 8.4333, "name": null}, {"time": 9.3333, "name": null}, {"time": 10.2333, "name": null}]}}, "bones": {"bone2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5667, "angle": 0, "curve": "stepped"}, {"time": 2.1333, "angle": 0, "curve": "stepped"}, {"time": 3.0333, "angle": 0, "curve": "stepped"}, {"time": 3.9333, "angle": 0, "curve": "stepped"}, {"time": 4.8333, "angle": 0, "curve": "stepped"}, {"time": 5.7333, "angle": 0, "curve": "stepped"}, {"time": 6.6333, "angle": 0, "curve": "stepped"}, {"time": 7.5333, "angle": 0, "curve": "stepped"}, {"time": 8.4333, "angle": 0, "curve": "stepped"}, {"time": 9.3333, "angle": 0, "curve": "stepped"}, {"time": 10.2333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.2333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.1333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.9333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.8333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.7333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6.6333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 7.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8.4333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10.2333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.2333, "x": 0, "y": 0}]}, "Xe": {"rotate": [{"time": 0, "angle": 26.54}, {"time": 0.1667, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0, "curve": "stepped"}, {"time": 0.5333, "angle": 0}, {"time": 0.7, "angle": 26.54, "curve": "stepped"}, {"time": 0.7333, "angle": 26.54}, {"time": 0.9, "angle": 0, "curve": "stepped"}, {"time": 1.1333, "angle": 0, "curve": "stepped"}, {"time": 1.2667, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0, "curve": "stepped"}, {"time": 1.5333, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": "stepped"}, {"time": 1.8, "angle": 0, "curve": "stepped"}, {"time": 1.9333, "angle": 0, "curve": "stepped"}, {"time": 2.0667, "angle": 0, "curve": "stepped"}, {"time": 2.1333, "angle": 0, "curve": "stepped"}, {"time": 2.1667, "angle": 0, "curve": "stepped"}, {"time": 2.3, "angle": 0, "curve": "stepped"}, {"time": 2.4333, "angle": 0, "curve": "stepped"}, {"time": 2.5667, "angle": 0, "curve": "stepped"}, {"time": 2.7, "angle": 0, "curve": "stepped"}, {"time": 2.8333, "angle": 0, "curve": "stepped"}, {"time": 2.9667, "angle": 0, "curve": "stepped"}, {"time": 3.0333, "angle": 0, "curve": "stepped"}, {"time": 3.0667, "angle": 0, "curve": "stepped"}, {"time": 3.2, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0, "curve": "stepped"}, {"time": 3.4667, "angle": 0, "curve": "stepped"}, {"time": 3.6, "angle": 0, "curve": "stepped"}, {"time": 3.7333, "angle": 0, "curve": "stepped"}, {"time": 3.8667, "angle": 0, "curve": "stepped"}, {"time": 3.9333, "angle": 0, "curve": "stepped"}, {"time": 3.9667, "angle": 0, "curve": "stepped"}, {"time": 4.1, "angle": 0, "curve": "stepped"}, {"time": 4.2333, "angle": 0, "curve": "stepped"}, {"time": 4.3667, "angle": 0, "curve": "stepped"}, {"time": 4.5, "angle": 0, "curve": "stepped"}, {"time": 4.6333, "angle": 0, "curve": "stepped"}, {"time": 4.7667, "angle": 0, "curve": "stepped"}, {"time": 4.8333, "angle": 0, "curve": "stepped"}, {"time": 4.8667, "angle": 0, "curve": "stepped"}, {"time": 5, "angle": 0, "curve": "stepped"}, {"time": 5.1333, "angle": 0, "curve": "stepped"}, {"time": 5.2667, "angle": 0, "curve": "stepped"}, {"time": 5.4, "angle": 0, "curve": "stepped"}, {"time": 5.5333, "angle": 0, "curve": "stepped"}, {"time": 5.6667, "angle": 0, "curve": "stepped"}, {"time": 5.7333, "angle": 0, "curve": "stepped"}, {"time": 5.7667, "angle": 0, "curve": "stepped"}, {"time": 5.9, "angle": 0, "curve": "stepped"}, {"time": 6.0333, "angle": 0, "curve": "stepped"}, {"time": 6.1667, "angle": 0, "curve": "stepped"}, {"time": 6.3, "angle": 0, "curve": "stepped"}, {"time": 6.4333, "angle": 0, "curve": "stepped"}, {"time": 6.5667, "angle": 0, "curve": "stepped"}, {"time": 6.6333, "angle": 0, "curve": "stepped"}, {"time": 6.6667, "angle": 0, "curve": "stepped"}, {"time": 6.8, "angle": 0, "curve": "stepped"}, {"time": 6.9333, "angle": 0, "curve": "stepped"}, {"time": 7.0667, "angle": 0, "curve": "stepped"}, {"time": 7.2, "angle": 0, "curve": "stepped"}, {"time": 7.3333, "angle": 0, "curve": "stepped"}, {"time": 7.4667, "angle": 0, "curve": "stepped"}, {"time": 7.5333, "angle": 0, "curve": "stepped"}, {"time": 7.5667, "angle": 0, "curve": "stepped"}, {"time": 7.7, "angle": 0, "curve": "stepped"}, {"time": 7.8333, "angle": 0, "curve": "stepped"}, {"time": 7.9667, "angle": 0, "curve": "stepped"}, {"time": 8.1, "angle": 0, "curve": "stepped"}, {"time": 8.2333, "angle": 0, "curve": "stepped"}, {"time": 8.3667, "angle": 0, "curve": "stepped"}, {"time": 8.4333, "angle": 0, "curve": "stepped"}, {"time": 8.4667, "angle": 0, "curve": "stepped"}, {"time": 8.6, "angle": 0, "curve": "stepped"}, {"time": 8.7333, "angle": 0, "curve": "stepped"}, {"time": 8.8667, "angle": 0, "curve": "stepped"}, {"time": 9, "angle": 0, "curve": "stepped"}, {"time": 9.1333, "angle": 0, "curve": "stepped"}, {"time": 9.2667, "angle": 0, "curve": "stepped"}, {"time": 9.3333, "angle": 0, "curve": "stepped"}, {"time": 9.3667, "angle": 0, "curve": "stepped"}, {"time": 9.5, "angle": 0, "curve": "stepped"}, {"time": 9.6333, "angle": 0, "curve": "stepped"}, {"time": 9.7667, "angle": 0, "curve": "stepped"}, {"time": 9.9, "angle": 0, "curve": "stepped"}, {"time": 10.0333, "angle": 0, "curve": "stepped"}, {"time": 10.1667, "angle": 0, "curve": "stepped"}, {"time": 10.2333, "angle": 0}], "translate": [{"time": 0, "x": 326.86, "y": 35.06}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0}, {"time": 0.5, "x": -32.31, "y": 10.57}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.7, "x": 326.86, "y": 35.06, "curve": "stepped"}, {"time": 0.7333, "x": 326.86, "y": 35.06}, {"time": 0.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1333, "x": 0, "y": 0}, {"time": 1.2, "x": 0, "y": -6.97}, {"time": 1.2667, "x": 0, "y": 0}, {"time": 1.3333, "x": 0, "y": -6.97}, {"time": 1.4, "x": 0, "y": 0}, {"time": 1.4667, "x": 0, "y": -6.97}, {"time": 1.5333, "x": 0, "y": 0}, {"time": 1.6, "x": 0, "y": -6.97}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 1.7333, "x": 0, "y": -6.97}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.8667, "x": 0, "y": -6.97}, {"time": 1.9333, "x": 0, "y": 0}, {"time": 2, "x": 0, "y": -6.97}, {"time": 2.0667, "x": 0, "y": 0}, {"time": 2.1333, "x": 0, "y": -3.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2333, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3667, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.4333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6333, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.7, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.7667, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.9, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.9667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.0333, "x": 0, "y": -3.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.1333, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2667, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.4, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.4667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5333, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.6667, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.7333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.8, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.8667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.9333, "x": 0, "y": -3.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.9667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.0333, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.1667, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.3, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.3667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.4333, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.5, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.5667, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.7, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.7667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.8333, "x": 0, "y": -3.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.8667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.9333, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 5, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0667, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.2, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.4667, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.5333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.6, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.7333, "x": 0, "y": -3.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.7667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8333, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.9, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.9667, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.0333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.1, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.2333, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.3, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.3667, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.4333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.5, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.5667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.6333, "x": 0, "y": -3.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.7333, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.8667, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.0667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1333, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.2667, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.4, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.4667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.5333, "x": 0, "y": -3.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.5667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6333, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.7, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.7667, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.8333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.9, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.9667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.0333, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.1667, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.2333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.3, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.3667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.4333, "x": 0, "y": -3.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.4667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.5333, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.6667, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.7333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.8, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.8667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.9333, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 9, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.0667, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.1333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.2, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.3333, "x": 0, "y": -3.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.3667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.4333, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.5, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.5667, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.6333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.7, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.7667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.8333, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.9, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.9667, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.0333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.1, "x": 0, "y": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.2333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.498, "y": 0.498}, {"time": 0.1667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5333, "x": 1, "y": 1}, {"time": 0.7, "x": 0.498, "y": 0.498, "curve": "stepped"}, {"time": 0.7333, "x": 0.498, "y": 0.498}, {"time": 0.9, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.1333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5667, "x": 1.294, "y": 1.294, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.0333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.4667, "x": 1.294, "y": 1.294, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.9333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.3667, "x": 1.294, "y": 1.294, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.8333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.2667, "x": 1.294, "y": 1.294, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.7333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.1667, "x": 1.294, "y": 1.294, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.6333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.0667, "x": 1.294, "y": 1.294, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.5333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.9667, "x": 1.294, "y": 1.294, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.4333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.8667, "x": 1.294, "y": 1.294, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.7667, "x": 1.294, "y": 1.294, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.2333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.9667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.3667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.9667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.3667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.3667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.2333, "x": 0, "y": 0}]}, "bone8": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.0333, "angle": 0, "curve": "stepped"}, {"time": 2.1333, "angle": 0, "curve": "stepped"}, {"time": 3.0333, "angle": 0, "curve": "stepped"}, {"time": 3.9333, "angle": 0, "curve": "stepped"}, {"time": 4.8333, "angle": 0, "curve": "stepped"}, {"time": 5.7333, "angle": 0, "curve": "stepped"}, {"time": 6.6333, "angle": 0, "curve": "stepped"}, {"time": 7.5333, "angle": 0, "curve": "stepped"}, {"time": 8.4333, "angle": 0, "curve": "stepped"}, {"time": 9.3333, "angle": 0, "curve": "stepped"}, {"time": 10.2333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.2333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.1333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.9333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.8333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.7333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6.6333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 7.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8.4333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10.2333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.2333, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.7, "angle": 0, "curve": "stepped"}, {"time": 2.1333, "angle": 0, "curve": "stepped"}, {"time": 3.0333, "angle": 0, "curve": "stepped"}, {"time": 3.9333, "angle": 0, "curve": "stepped"}, {"time": 4.8333, "angle": 0, "curve": "stepped"}, {"time": 5.7333, "angle": 0, "curve": "stepped"}, {"time": 6.6333, "angle": 0, "curve": "stepped"}, {"time": 7.5333, "angle": 0, "curve": "stepped"}, {"time": 8.4333, "angle": 0, "curve": "stepped"}, {"time": 9.3333, "angle": 0, "curve": "stepped"}, {"time": 10.2333, "angle": 0}], "translate": [{"time": 0, "x": -59.36, "y": 10.48, "curve": "stepped"}, {"time": 0.7, "x": -59.36, "y": 10.48, "curve": "stepped"}, {"time": 2.1333, "x": -59.36, "y": 10.48, "curve": "stepped"}, {"time": 3.0333, "x": -59.36, "y": 10.48, "curve": "stepped"}, {"time": 3.9333, "x": -59.36, "y": 10.48, "curve": "stepped"}, {"time": 4.8333, "x": -59.36, "y": 10.48, "curve": "stepped"}, {"time": 5.7333, "x": -59.36, "y": 10.48, "curve": "stepped"}, {"time": 6.6333, "x": -59.36, "y": 10.48, "curve": "stepped"}, {"time": 7.5333, "x": -59.36, "y": 10.48, "curve": "stepped"}, {"time": 8.4333, "x": -59.36, "y": 10.48, "curve": "stepped"}, {"time": 9.3333, "x": -59.36, "y": 10.48, "curve": "stepped"}, {"time": 10.2333, "x": -59.36, "y": 10.48}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.7, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.1333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.9333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.8333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.7333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6.6333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 7.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8.4333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10.2333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.2333, "x": 0, "y": 0}]}, "bone9": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.2333, "angle": 0, "curve": "stepped"}, {"time": 2.1333, "angle": 0, "curve": "stepped"}, {"time": 3.0333, "angle": 0, "curve": "stepped"}, {"time": 3.9333, "angle": 0, "curve": "stepped"}, {"time": 4.8333, "angle": 0, "curve": "stepped"}, {"time": 5.7333, "angle": 0, "curve": "stepped"}, {"time": 6.6333, "angle": 0, "curve": "stepped"}, {"time": 7.5333, "angle": 0, "curve": "stepped"}, {"time": 8.4333, "angle": 0, "curve": "stepped"}, {"time": 9.3333, "angle": 0, "curve": "stepped"}, {"time": 10.2333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.2333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.1333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.9333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.8333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.7333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6.6333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 7.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8.4333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10.2333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.2333, "x": 0, "y": 0}]}, "Thang-Tex": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1, "angle": 0, "curve": "stepped"}, {"time": 2.1333, "angle": 0, "curve": "stepped"}, {"time": 3.0333, "angle": 0, "curve": "stepped"}, {"time": 3.9333, "angle": 0, "curve": "stepped"}, {"time": 4.8333, "angle": 0, "curve": "stepped"}, {"time": 5.7333, "angle": 0, "curve": "stepped"}, {"time": 6.6333, "angle": 0, "curve": "stepped"}, {"time": 7.5333, "angle": 0, "curve": "stepped"}, {"time": 8.4333, "angle": 0, "curve": "stepped"}, {"time": 9.3333, "angle": 0, "curve": "stepped"}, {"time": 10.2333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.2333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1, "x": 0, "y": 0}, {"time": 0.1667, "x": 1.476, "y": 1.476}, {"time": 0.2667, "x": 0.968, "y": 0.968}, {"time": 0.3667, "x": 1.113, "y": 1.113}, {"time": 0.4667, "x": 0.833, "y": 0.833}, {"time": 0.5667, "x": 1, "y": 1}, {"time": 0.6667, "x": 0.833, "y": 0.833}, {"time": 0.7667, "x": 1, "y": 1}, {"time": 0.8667, "x": 0.833, "y": 0.833}, {"time": 0.9667, "x": 1, "y": 1}, {"time": 1.0667, "x": 0.833, "y": 0.833}, {"time": 1.1667, "x": 1, "y": 1}, {"time": 1.2667, "x": 0.833, "y": 0.833}, {"time": 1.3667, "x": 1, "y": 1}, {"time": 1.4333, "x": 0.833, "y": 0.833}, {"time": 1.5333, "x": 1, "y": 1}, {"time": 1.6333, "x": 0.833, "y": 0.833}, {"time": 1.7333, "x": 1, "y": 1}, {"time": 1.8333, "x": 0.833, "y": 0.833}, {"time": 1.9333, "x": 1, "y": 1}, {"time": 2.0333, "x": 0.833, "y": 0.833}, {"time": 2.1333, "x": 0.889, "y": 0.889, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1667, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.4333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.7333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.9333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.0333, "x": 0.889, "y": 0.889, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.0667, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.1667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.4333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.6333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.7333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.8333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.9333, "x": 0.889, "y": 0.889, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.9667, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.1333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.2333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.3333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.4333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.5333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.7333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.8333, "x": 0.889, "y": 0.889, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.8667, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.9667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.0333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.2333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.4333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.5333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.6333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.7333, "x": 0.889, "y": 0.889, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.7667, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.9333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.0333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.1333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.2333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.3333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.4333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.5333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.6333, "x": 0.889, "y": 0.889, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.6667, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.7667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.8333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.9333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.0333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.2333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.4333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.5333, "x": 0.889, "y": 0.889, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.5667, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.7333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.8333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.9333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.0333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.1333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.2333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.3333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.4333, "x": 0.889, "y": 0.889, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.4667, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.5667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.6333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.7333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.8333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.9333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.0333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.2333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.3333, "x": 0.889, "y": 0.889, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.3667, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.4667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.5333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.6333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.7333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.8333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.9333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.0333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.1333, "x": 0.833, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 10.2333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.2333, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 2.1333, "angle": 0, "curve": "stepped"}, {"time": 3.0333, "angle": 0, "curve": "stepped"}, {"time": 3.9333, "angle": 0, "curve": "stepped"}, {"time": 4.8333, "angle": 0, "curve": "stepped"}, {"time": 5.7333, "angle": 0, "curve": "stepped"}, {"time": 6.6333, "angle": 0, "curve": "stepped"}, {"time": 7.5333, "angle": 0, "curve": "stepped"}, {"time": 8.4333, "angle": 0, "curve": "stepped"}, {"time": 9.3333, "angle": 0, "curve": "stepped"}, {"time": 10.2333, "angle": 0}], "translate": [{"time": 2.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.2333, "x": 0, "y": 0}], "scale": [{"time": 2.1333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.9333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.8333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.7333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6.6333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 7.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8.4333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10.2333, "x": 1, "y": 1}], "shear": [{"time": 2.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.2333, "x": 0, "y": 0}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.1333, "angle": 0, "curve": "stepped"}, {"time": 3.0333, "angle": 0, "curve": "stepped"}, {"time": 3.9333, "angle": 0, "curve": "stepped"}, {"time": 4.8333, "angle": 0, "curve": "stepped"}, {"time": 5.7333, "angle": 0, "curve": "stepped"}, {"time": 6.6333, "angle": 0, "curve": "stepped"}, {"time": 7.5333, "angle": 0, "curve": "stepped"}, {"time": 8.4333, "angle": 0, "curve": "stepped"}, {"time": 9.3333, "angle": 0, "curve": "stepped"}, {"time": 10.2333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.2333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.1333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.9333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.8333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.7333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6.6333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 7.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8.4333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10.2333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.2333, "x": 0, "y": 0}]}, "bone5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.1333, "angle": 0, "curve": "stepped"}, {"time": 3.0333, "angle": 0, "curve": "stepped"}, {"time": 3.9333, "angle": 0, "curve": "stepped"}, {"time": 4.8333, "angle": 0, "curve": "stepped"}, {"time": 5.7333, "angle": 0, "curve": "stepped"}, {"time": 6.6333, "angle": 0, "curve": "stepped"}, {"time": 7.5333, "angle": 0, "curve": "stepped"}, {"time": 8.4333, "angle": 0, "curve": "stepped"}, {"time": 9.3333, "angle": 0, "curve": "stepped"}, {"time": 10.2333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.2333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.2, "x": 1.375, "y": 1.375}, {"time": 0.4333, "x": 1.37, "y": 1.37}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 0.9, "x": 1.375, "y": 1.375, "curve": "stepped"}, {"time": 2.1333, "x": 1.375, "y": 1.375, "curve": "stepped"}, {"time": 3.0333, "x": 1.375, "y": 1.375, "curve": "stepped"}, {"time": 3.9333, "x": 1.375, "y": 1.375, "curve": "stepped"}, {"time": 4.8333, "x": 1.375, "y": 1.375, "curve": "stepped"}, {"time": 5.7333, "x": 1.375, "y": 1.375, "curve": "stepped"}, {"time": 6.6333, "x": 1.375, "y": 1.375, "curve": "stepped"}, {"time": 7.5333, "x": 1.375, "y": 1.375, "curve": "stepped"}, {"time": 8.4333, "x": 1.375, "y": 1.375, "curve": "stepped"}, {"time": 9.3333, "x": 1.375, "y": 1.375, "curve": "stepped"}, {"time": 10.2333, "x": 1.375, "y": 1.375}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.2333, "x": 0, "y": 0}]}, "TL": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.1333, "angle": 0, "curve": "stepped"}, {"time": 3.0333, "angle": 0, "curve": "stepped"}, {"time": 3.9333, "angle": 0, "curve": "stepped"}, {"time": 4.8333, "angle": 0, "curve": "stepped"}, {"time": 5.7333, "angle": 0, "curve": "stepped"}, {"time": 6.6333, "angle": 0, "curve": "stepped"}, {"time": 7.5333, "angle": 0, "curve": "stepped"}, {"time": 8.4333, "angle": 0, "curve": "stepped"}, {"time": 9.3333, "angle": 0, "curve": "stepped"}, {"time": 10.2333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.2333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.1333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.9333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.8333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 5.7333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 6.6333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 7.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 8.4333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 9.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 10.2333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 5.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 6.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 7.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 8.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 9.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 10.2333, "x": 0, "y": 0}]}}}, "Thang-Lon": {"slots": {"4.BomTan/Xe dien/effect/nohu": {"color": [{"time": 0.6333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/nohu"}]}, "4.BomTan/Xe dien/effect/nohu-text": {"attachment": [{"time": 0, "name": null}, {"time": 0.8667, "name": null}, {"time": 1.8, "name": null}]}, "4.BomTan/Xe dien/effect/thanglon-text": {"attachment": [{"time": 0, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/thanglon-text"}, {"time": 0.8667, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/thanglon-text"}, {"time": 1.8, "name": "4.<PERSON><PERSON><PERSON><PERSON>/Xe dien/effect/thanglon-text"}]}}, "bones": {"TL": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.1, "x": 0.873, "y": 0.873}, {"time": 0.2, "x": 1, "y": 1}, {"time": 0.3, "x": 0.873, "y": 0.873}, {"time": 0.4, "x": 1, "y": 1}, {"time": 0.5, "x": 0.873, "y": 0.873}, {"time": 0.6, "x": 1, "y": 1}, {"time": 0.7, "x": 0.873, "y": 0.873}, {"time": 0.8, "x": 1, "y": 1}, {"time": 0.9, "x": 0.873, "y": 0.873}, {"time": 1, "x": 1, "y": 1}, {"time": 1.1, "x": 0.873, "y": 0.873}, {"time": 1.2, "x": 1, "y": 1}, {"time": 1.3, "x": 0.873, "y": 0.873}, {"time": 1.4, "x": 1, "y": 1}, {"time": 1.5, "x": 0.873, "y": 0.873}, {"time": 1.6, "x": 1, "y": 1}, {"time": 1.7, "x": 0.873, "y": 0.873}, {"time": 1.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8, "x": 0, "y": 0}]}, "Thang-Tex": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8667, "angle": 0}, {"time": 1.0333, "angle": 179.52}, {"time": 1.1667, "angle": -84.24}, {"time": 1.3, "angle": 0}, {"time": 1.8, "angle": 0}], "translate": [{"time": 0, "x": -16.2, "y": 121.5, "curve": "stepped"}, {"time": 0.8667, "x": -16.2, "y": 121.5, "curve": "stepped"}, {"time": 1.8, "x": -16.2, "y": 121.5}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8, "x": 0, "y": 0}]}, "bone9": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8, "x": 0, "y": 0}]}, "bone8": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8, "x": 0, "y": 0}]}, "bone5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8, "x": 0, "y": 0}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8, "x": 0, "y": 0}]}, "Xe": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8, "x": 0, "y": 0}]}}}}}