{"skeleton": {"hash": "wV0fbdt70VerMdC/Zb9h+x3vuMQ", "spine": "3.6.52", "width": 134.27, "height": 114.32}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 41.38, "rotation": 0.2, "x": -2.3, "y": 17.34}, {"name": "bone2", "parent": "bone", "length": 20.11, "rotation": 0.31, "x": 3.5, "y": 35.59}, {"name": "bone3", "parent": "bone2", "length": 8.43, "rotation": -83.23, "x": -13.18, "y": 19.34}, {"name": "bone4", "parent": "bone2", "length": 5.63, "rotation": 5.2, "x": 6.76, "y": 15.45}, {"name": "bone5", "parent": "bone2", "length": 3.72, "rotation": 6.4, "x": -17.11, "y": 12.42}], "slots": [{"name": "4.BomTan/Xe dien/minigame/Icon1/banhxe", "bone": "bone", "attachment": "4.BomTan/Xe dien/minigame/Icon1/banhxe"}, {"name": "4.BomTan/Xe dien/minigame/Icon1/than", "bone": "bone2", "attachment": "4.BomTan/Xe dien/minigame/Icon1/than"}, {"name": "4.Bom<PERSON>an/Xe dien/minigame/Icon1/trongmat", "bone": "bone2", "attachment": "4.Bom<PERSON>an/Xe dien/minigame/Icon1/trongmat"}, {"name": "4.Bom<PERSON>an/Xe dien/minigame/Icon1/den", "bone": "bone2", "attachment": "4.Bom<PERSON>an/Xe dien/minigame/Icon1/den"}, {"name": "4.Bo<PERSON><PERSON><PERSON>/Xe dien/minigame/Icon1/mom", "bone": "bone2", "attachment": "4.Bo<PERSON><PERSON><PERSON>/Xe dien/minigame/Icon1/mom"}, {"name": "4.Bom<PERSON>an/Xe dien/minigame/Icon1/nhaymat", "bone": "bone3"}], "skins": {"default": {"4.BomTan/Xe dien/minigame/Icon1/banhxe": {"4.BomTan/Xe dien/minigame/Icon1/banhxe": {"x": -3.54, "y": 4.62, "rotation": -0.06, "width": 123, "height": 46}}, "4.BomTan/Xe dien/minigame/Icon1/den": {"4.BomTan/Xe dien/minigame/Icon1/den": {"x": -14.42, "y": 25.78, "rotation": -1.04, "width": 50, "height": 15}}, "4.BomTan/Xe dien/minigame/Icon1/mom": {"4.BomTan/Xe dien/minigame/Icon1/mom": {"x": -23.52, "y": -11.18, "rotation": -0.37, "width": 42, "height": 23}}, "4.BomTan/Xe dien/minigame/Icon1/nhaymat": {"4.BomTan/Xe dien/minigame/Icon1/nhaymat": {"type": "mesh", "uvs": [0.23222, 0.469, 0.24321, 0.34833, 0.62302, 0.29467, 0.63115, 0.40838], "triangles": [1, 2, 3, 0, 1, 3], "vertices": [11.72, -26.3, -1.75, -23.13, -1.49, 28.13, 11.5, 27.6], "hull": 4}}, "4.BomTan/Xe dien/minigame/Icon1/than": {"4.BomTan/Xe dien/minigame/Icon1/than": {"x": -2.09, "y": 0.81, "rotation": -0.37, "width": 134, "height": 114}}, "4.BomTan/Xe dien/minigame/Icon1/trongmat": {"4.BomTan/Xe dien/minigame/Icon1/trongmat": {"type": "mesh", "uvs": [0.34024, 0.34454, 0.41224, 0.3378, 0.41653, 0.38159, 0.46131, 0.37443, 0.46274, 0.33443, 0.53217, 0.33354, 0.548, 0.41653, 0.46883, 0.42622, 0.46346, 0.38117, 0.41689, 0.38875, 0.41904, 0.43464, 0.35063, 0.44348], "triangles": [9, 3, 8, 8, 5, 6, 5, 3, 4, 8, 3, 5, 7, 8, 6, 0, 9, 11, 2, 0, 1, 11, 9, 10, 9, 0, 2, 9, 2, 3], "vertices": [1, 5, -4.63, 6.38, 1, 1, 5, 5.04, 6, 1, 1, 5, 5.02, 0.98, 1, 1, 4, -3.36, 0.68, 1, 1, 4, -2.72, 5.2, 1, 1, 4, 6.55, 4.4, 1, 1, 4, 7.74, -5.22, 1, 1, 4, -2.93, -5.29, 1, 1, 4, -3.14, -0.11, 1, 1, 5, 4.97, 0.16, 1, 1, 5, 4.64, -5.07, 1, 1, 5, -4.58, -4.99, 1], "hull": 12}}}}, "animations": {"Idle": {"slots": {"4.BomTan/Xe dien/minigame/Icon1/den": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00"}, {"time": 2, "color": "ffffffff"}, {"time": 2.5, "color": "ffffff00"}, {"time": 3, "color": "ffffffff"}]}, "4.BomTan/Xe dien/minigame/Icon1/mom": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7333, "color": "ffffff00"}, {"time": 1.3, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5, "color": "ffffffff"}, {"time": 3, "color": "ffffff00"}]}, "4.BomTan/Xe dien/minigame/Icon1/nhaymat": {"color": [{"time": 1.0333, "color": "ffffff00"}, {"time": 1.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2333, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "4.Bom<PERSON>an/Xe dien/minigame/Icon1/nhaymat"}]}}, "bones": {"bone2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "x": -0.01, "y": -1.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "x": -0.01, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": -0.01, "y": -1.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": -0.01, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": -0.01, "y": -1.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": -0.01, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": -0.01, "y": -1.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": -0.01, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": -0.01, "y": -1.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": -0.01, "y": -1.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "x": -0.01, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": -0.01, "y": -1.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": -0.01, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -0.01, "y": -1.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "x": -0.01, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7, "x": -0.01, "y": -1.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8, "x": -0.01, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9, "x": -0.01, "y": -1.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1, "x": -0.01, "y": -1.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2, "x": -0.01, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3, "x": -0.01, "y": -1.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.4, "x": -0.01, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "x": -0.01, "y": -1.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6, "x": -0.01, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.7, "x": -0.01, "y": -1.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8, "x": -0.01, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.9, "x": -0.01, "y": -1.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": -0.01, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 3.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 5.62, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "angle": 3.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": -2.69, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -2.69, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "x": -2.69, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.2667, "angle": 0, "curve": "stepped"}, {"time": 2.4667, "angle": 0, "curve": "stepped"}, {"time": 2.7, "angle": 0}], "translate": [{"time": 0, "x": -0.17, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": -16.72, "y": -1.48, "curve": "stepped"}, {"time": 2.4667, "x": -16.72, "y": -1.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.7, "x": -0.17, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.4667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.7, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.7, "x": 0, "y": 0}]}, "bone5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.2667, "angle": 0, "curve": "stepped"}, {"time": 2.4667, "angle": 0, "curve": "stepped"}, {"time": 2.7, "angle": 0}], "translate": [{"time": 0, "x": -0.17, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": -16.72, "y": -1.48, "curve": "stepped"}, {"time": 2.4667, "x": -16.72, "y": -1.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.7, "x": -0.17, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.4667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.7, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.7, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 1.0333, "angle": 0, "curve": "stepped"}, {"time": 1.3667, "angle": 0}], "translate": [{"time": 1.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0, "y": 1, "curve": "stepped"}, {"time": 1.0333, "x": 0, "y": 1}, {"time": 1.1333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2333, "x": 1, "y": 1}, {"time": 1.3667, "x": 0, "y": 1}], "shear": [{"time": 1.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3667, "x": 0, "y": 0}]}}}}}