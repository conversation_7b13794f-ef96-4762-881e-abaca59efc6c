{"skeleton": {"hash": "85woQx2KJzLH6rj2RRWSXtgytCk", "spine": "3.6.52", "width": 134, "height": 114}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 28.13, "rotation": 1.33, "x": 2.19, "y": 29.4}, {"name": "bone2", "parent": "bone", "length": 9.93, "rotation": -82.49, "x": -13.83, "y": 62.57}, {"name": "bone3", "parent": "bone", "length": 6.09, "rotation": 3.64, "x": -13.99, "y": 55.86}], "slots": [{"name": "Xe dien/minigame/icon2/than", "bone": "bone", "attachment": "Xe dien/minigame/icon2/than"}, {"name": "Xe dien/minigame/icon2/den", "bone": "bone", "attachment": "Xe dien/minigame/icon2/den"}, {"name": "Xe dien/minigame/icon2/trongmat", "bone": "bone3", "attachment": "Xe dien/minigame/icon2/trongmat"}, {"name": "Xe dien/minigame/icon2/nhaymat", "bone": "bone2"}, {"name": "Xe dien/main/fx-2", "bone": "bone"}, {"name": "Xe dien/main/fx-3", "bone": "bone"}], "skins": {"default": {"Xe dien/main/fx-2": {"Xe dien/main/fx-2": {"x": -47.33, "y": 32.24, "scaleX": 0.645, "scaleY": 0.645, "rotation": -1.33, "width": 100, "height": 100}}, "Xe dien/main/fx-3": {"Xe dien/main/fx-2": {"x": 2.5, "y": 38.61, "scaleX": 0.541, "scaleY": 0.541, "rotation": -1.33, "width": 100, "height": 100}}, "Xe dien/minigame/icon2/den": {"Xe dien/minigame/icon2/den": {"x": -21.91, "y": 36.73, "rotation": -1.33, "width": 69, "height": 22}}, "Xe dien/minigame/icon2/nhaymat": {"Xe dien/minigame/icon2/nhaymat": {"x": 5.61, "y": 4.13, "rotation": 81.16, "width": 50, "height": 19}}, "Xe dien/minigame/icon2/than": {"Xe dien/minigame/icon2/than": {"x": -6.35, "y": 28.09, "rotation": -1.33, "width": 134, "height": 114}}, "Xe dien/minigame/icon2/trongmat": {"Xe dien/minigame/icon2/trongmat": {"x": 1.17, "y": 1.1, "rotation": -4.97, "width": 29, "height": 18}}}}, "animations": {"Idle": {"slots": {"Xe dien/main/fx-2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1, "color": "ffffffff"}, {"time": 1.4667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Xe dien/main/fx-2"}]}, "Xe dien/main/fx-3": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1, "color": "ffffffff"}, {"time": 1.4667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Xe dien/main/fx-2"}]}, "Xe dien/minigame/icon2/den": {"color": [{"time": 0, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.0667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.4667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8667, "color": "ffffffff"}]}, "Xe dien/minigame/icon2/nhaymat": {"attachment": [{"time": 0, "name": "Xe dien/minigame/icon2/nhaymat"}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -9.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "x": 0, "y": -3.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 0, "y": -3.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0, "y": -3.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 0, "y": -3.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 0, "y": -3.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 0, "y": -3.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 0, "y": -3.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0, "y": -3.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7, "x": 0, "y": -3.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9, "x": 0, "y": -3.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1, "x": 0, "y": -3.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3, "x": 0, "y": -3.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.4, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "x": 0, "y": -3.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.7, "x": 0, "y": -3.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.9, "x": 0, "y": -3.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": 0.21}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "x": 6.51, "y": -0.03, "curve": "stepped"}, {"time": 1.9, "x": 6.51, "y": -0.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0.9, "angle": 0}], "translate": [{"time": 0.9, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0, "y": 1, "curve": "stepped"}, {"time": 0.9, "x": 0, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 0, "y": 1}], "shear": [{"time": 0.9, "x": 0, "y": 0}]}}}}}