{"skeleton": {"hash": "L+ce4dSuRkaYsiTYhkAZOu6afD8", "spine": "3.6.52", "width": 134, "height": 114}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 31.23, "rotation": -1.33, "x": -3.17, "y": 46.87}], "slots": [{"name": "Xe dien/minigame/icon3/than", "bone": "bone", "attachment": "Xe dien/minigame/icon3/than"}, {"name": "Xe dien/minigame/icon3/nhaymat", "bone": "bone", "attachment": "Xe dien/minigame/icon3/nhaymat"}, {"name": "Xe dien/minigame/icon3/den", "bone": "bone", "attachment": "Xe dien/minigame/icon3/den"}, {"name": "Xe dien/main/fx-2", "bone": "bone", "attachment": "Xe dien/main/fx-2"}, {"name": "Xe dien/main/fx-3", "bone": "bone", "attachment": "Xe dien/main/fx-2"}], "skins": {"default": {"Xe dien/main/fx-2": {"Xe dien/main/fx-2": {"x": 2.4, "y": 12.72, "scaleX": 0.373, "scaleY": 0.373, "rotation": 1.33, "width": 100, "height": 100}}, "Xe dien/main/fx-3": {"Xe dien/main/fx-2": {"x": 58.63, "y": 6.25, "scaleX": 0.217, "scaleY": 0.217, "rotation": 1.33, "width": 100, "height": 100}}, "Xe dien/minigame/icon3/den": {"Xe dien/minigame/icon3/den": {"x": 29.21, "y": 12.72, "rotation": -0.68, "width": 74, "height": 39}}, "Xe dien/minigame/icon3/nhaymat": {"Xe dien/minigame/icon3/nhaymat": {"x": 2.88, "y": 29.46, "rotation": 1.33, "width": 57, "height": 19}}, "Xe dien/minigame/icon3/than": {"Xe dien/minigame/icon3/than": {"x": 5.35, "y": 10.19, "rotation": 1.33, "width": 134, "height": 114}}}}, "animations": {"Idle": {"slots": {"Xe dien/main/fx-2": {"color": [{"time": 0, "color": "ffffff06", "curve": "stepped"}, {"time": 0.8, "color": "ffffff06"}, {"time": 0.9, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00"}, {"time": 2.2333, "color": "ffffff06"}, {"time": 2.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.5667, "color": "ffffff00"}]}, "Xe dien/main/fx-3": {"color": [{"time": 0, "color": "ffffff06", "curve": "stepped"}, {"time": 0.8, "color": "ffffff06"}, {"time": 0.9, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00"}, {"time": 2.2333, "color": "ffffff06"}, {"time": 2.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4667, "color": "ffffffff"}, {"time": 2.5667, "color": "ffffff00"}]}, "Xe dien/minigame/icon3/den": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff"}, {"time": 0.5333, "color": "ffffff00"}, {"time": 0.7, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.3667, "color": "ffffffff"}, {"time": 1.5333, "color": "ffffff00"}, {"time": 1.7, "color": "ffffffff"}, {"time": 1.9, "color": "ffffff00"}, {"time": 2.0667, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff00"}, {"time": 2.4, "color": "ffffffff"}, {"time": 2.5667, "color": "ffffff00"}, {"time": 2.7333, "color": "ffffffff"}, {"time": 2.8333, "color": "ffffff00"}, {"time": 3, "color": "ffffffff"}]}, "Xe dien/minigame/icon3/nhaymat": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5333, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.8667, "color": "ffffff00"}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1, "angle": -0.51}, {"time": 0.2, "angle": 0}, {"time": 0.3, "angle": -0.51}, {"time": 0.4, "angle": 0}, {"time": 0.5, "angle": -0.51}, {"time": 0.6, "angle": 0}, {"time": 0.7, "angle": -0.51}, {"time": 0.8, "angle": 0}, {"time": 0.9, "angle": -0.51}, {"time": 1, "angle": 0}, {"time": 1.1, "angle": -0.51}, {"time": 1.2, "angle": 0}, {"time": 1.3, "angle": -0.51}, {"time": 1.4, "angle": 0}, {"time": 1.5, "angle": -0.51}, {"time": 1.6, "angle": 0}, {"time": 1.7, "angle": -0.51}, {"time": 1.8, "angle": 0}, {"time": 1.9, "angle": -0.51}, {"time": 2, "angle": 0}, {"time": 2.1, "angle": -0.51}, {"time": 2.2, "angle": 0}, {"time": 2.3, "angle": -0.51}, {"time": 2.4, "angle": 0}, {"time": 2.5, "angle": -0.51}, {"time": 2.6, "angle": 0}, {"time": 2.7, "angle": -0.51}, {"time": 2.8, "angle": 0}, {"time": 2.9, "angle": -0.51}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1, "x": 0, "y": -2.37}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.3, "x": 0, "y": -2.37}, {"time": 0.4, "x": 0, "y": 0}, {"time": 0.5, "x": 0, "y": -2.37}, {"time": 0.6, "x": 0, "y": 0}, {"time": 0.7, "x": 0, "y": -2.37}, {"time": 0.8, "x": 0, "y": 0}, {"time": 0.9, "x": 0, "y": -2.37}, {"time": 1, "x": 0, "y": 0}, {"time": 1.1, "x": 0, "y": -2.37}, {"time": 1.2, "x": 0, "y": 0}, {"time": 1.3, "x": 0, "y": -2.37}, {"time": 1.4, "x": 0, "y": 0}, {"time": 1.5, "x": 0, "y": -2.37}, {"time": 1.6, "x": 0, "y": 0}, {"time": 1.7, "x": 0, "y": -2.37}, {"time": 1.8, "x": 0, "y": 0}, {"time": 1.9, "x": 0, "y": -2.37}, {"time": 2, "x": 0, "y": 0}, {"time": 2.1, "x": 0, "y": -2.37}, {"time": 2.2, "x": 0, "y": 0}, {"time": 2.3, "x": 0, "y": -2.37}, {"time": 2.4, "x": 0, "y": 0}, {"time": 2.5, "x": 0, "y": -2.37}, {"time": 2.6, "x": 0, "y": 0}, {"time": 2.7, "x": 0, "y": -2.37}, {"time": 2.8, "x": 0, "y": 0}, {"time": 2.9, "x": 0, "y": -2.37}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}]}}}}}