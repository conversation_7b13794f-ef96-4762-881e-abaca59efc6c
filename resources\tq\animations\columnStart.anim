{"__type__": "cc.AnimationClip", "_name": "columnStart", "_objFlags": 0, "_native": "", "_duration": 0.6666666666666666, "sample": 60, "speed": 4, "wrapMode": 1, "curveData": {"props": {"y": []}, "paths": {"slot0": {"props": {"y": [{"frame": 0, "value": 220}, {"frame": 0.016666666666666666, "value": -220}, {"frame": 0.5333333333333333, "value": -140}, {"frame": 0.6666666666666666, "value": -220}]}}, "slot1": {"props": {"y": [{"frame": 0, "value": 110}, {"frame": 0.5333333333333333, "value": 190}, {"frame": 0.6666666666666666, "value": 110}]}}, "slot2": {"props": {"y": [{"frame": 0, "value": 0}, {"frame": 0.5333333333333333, "value": 80}, {"frame": 0.6666666666666666, "value": 0}]}}, "slot3": {"props": {"y": [{"frame": 0, "value": -110}, {"frame": 0.5333333333333333, "value": -30}, {"frame": 0.6666666666666666, "value": -110}]}}}}, "events": [{"frame": 0.6666666666666666, "func": "finishStart", "params": []}]}