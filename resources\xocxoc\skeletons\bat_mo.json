{"skeleton": {"hash": "eMcprFu0c1dTmtk8q4Skj9O58lk", "spine": "3.6.53", "width": 195, "height": 151}, "bones": [{"name": "root"}, {"name": "d", "parent": "root", "length": 72.71, "rotation": 90, "x": -0.19, "y": -69.13}, {"name": "b", "parent": "d", "length": 87.61, "x": 46.49}, {"name": "bone", "parent": "root"}], "slots": [{"name": "b1", "bone": "bone", "attachment": "b1"}, {"name": "d", "bone": "d"}, {"name": "b", "bone": "b"}], "skins": {"default": {"b": {"b": {"x": 31.59, "y": -0.65, "rotation": -90, "width": 155, "height": 135}}, "b1": {"b1": {"width": 195, "height": 151}, "b2": {"width": 195, "height": 156}, "b3": {"width": 195, "height": 140}}, "d": {"d": {"x": 52.91, "y": -0.65, "rotation": -90, "width": 195, "height": 148}}}}, "animations": {"IdleDia": {"slots": {"b": {"attachment": [{"time": 0, "name": "b"}]}, "b1": {"attachment": [{"time": 0, "name": null}]}, "d": {"attachment": [{"time": 0, "name": "d"}]}}, "bones": {"b": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -15.45, "y": -0.82}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "bone": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": -8.96}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "d": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -0.51, "y": 5.55}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}}, "drawOrder": [{"time": 0}]}, "Idle_2": {"slots": {"b": {"attachment": [{"time": 0, "name": "b"}]}, "b1": {"attachment": [{"time": 0, "name": "b1"}]}, "d": {"attachment": [{"time": 0, "name": "d"}]}}, "bones": {"b": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -15.45, "y": -0.82}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "bone": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": -8.96}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "d": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -0.51, "y": 5.55}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}}, "drawOrder": [{"time": 0}]}, "lac": {"slots": {"b": {"attachment": [{"time": 0, "name": null}, {"time": 0.2, "name": null}, {"time": 0.4, "name": null}, {"time": 0.6, "name": null}, {"time": 0.8, "name": null}, {"time": 1, "name": null}, {"time": 1.2, "name": null}]}, "b1": {"attachment": [{"time": 0, "name": "b1"}, {"time": 0.0333, "name": "b2"}, {"time": 0.0667, "name": "b3"}, {"time": 0.1333, "name": "b3"}, {"time": 0.1667, "name": "b2"}, {"time": 0.2, "name": "b1"}, {"time": 0.2333, "name": "b2"}, {"time": 0.2667, "name": "b3"}, {"time": 0.3333, "name": "b3"}, {"time": 0.3667, "name": "b2"}, {"time": 0.4, "name": "b1"}, {"time": 0.4333, "name": "b2"}, {"time": 0.4667, "name": "b3"}, {"time": 0.5333, "name": "b3"}, {"time": 0.5667, "name": "b2"}, {"time": 0.6, "name": "b1"}, {"time": 0.6333, "name": "b2"}, {"time": 0.6667, "name": "b3"}, {"time": 0.7333, "name": "b3"}, {"time": 0.7667, "name": "b2"}, {"time": 0.8, "name": "b1"}, {"time": 0.8333, "name": "b2"}, {"time": 0.8667, "name": "b3"}, {"time": 0.9333, "name": "b3"}, {"time": 0.9667, "name": "b2"}, {"time": 1, "name": "b1"}, {"time": 1.0333, "name": "b2"}, {"time": 1.0667, "name": "b3"}, {"time": 1.1333, "name": "b3"}, {"time": 1.1667, "name": "b2"}, {"time": 1.2, "name": "b1"}]}, "d": {"attachment": [{"time": 0, "name": null}, {"time": 0.2, "name": null}, {"time": 0.4, "name": null}, {"time": 0.6, "name": null}, {"time": 0.8, "name": null}, {"time": 1, "name": null}, {"time": 1.2, "name": null}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0, "curve": "stepped"}, {"time": 0.6, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 1.2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2, "x": 1, "y": 1}]}, "d": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0, "curve": "stepped"}, {"time": 0.6, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 1.2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2, "x": 1, "y": 1}]}, "b": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0, "curve": "stepped"}, {"time": 0.6, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 1.2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2, "x": 1, "y": 1}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0, "curve": "stepped"}, {"time": 0.6, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 1.2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": -8.96}, {"time": 0.0333, "x": 0, "y": 8.47}, {"time": 0.0667, "x": 0, "y": 38.33}, {"time": 0.1, "x": 0, "y": 45.55}, {"time": 0.1333, "x": 0, "y": 36.97}, {"time": 0.1667, "x": 0, "y": 8.47}, {"time": 0.2, "x": 0, "y": -8.96}, {"time": 0.2333, "x": 0, "y": 8.47}, {"time": 0.2667, "x": 0, "y": 38.33}, {"time": 0.3, "x": 0, "y": 45.55}, {"time": 0.3333, "x": 0, "y": 36.97}, {"time": 0.3667, "x": 0, "y": 8.47}, {"time": 0.4, "x": 0, "y": -8.96}, {"time": 0.4333, "x": 0, "y": 8.47}, {"time": 0.4667, "x": 0, "y": 38.33}, {"time": 0.5, "x": 0, "y": 45.55}, {"time": 0.5333, "x": 0, "y": 36.97}, {"time": 0.5667, "x": 0, "y": 8.47}, {"time": 0.6, "x": 0, "y": -8.96}, {"time": 0.6333, "x": 0, "y": 8.47}, {"time": 0.6667, "x": 0, "y": 38.33}, {"time": 0.7, "x": 0, "y": 45.55}, {"time": 0.7333, "x": 0, "y": 36.97}, {"time": 0.7667, "x": 0, "y": 8.47}, {"time": 0.8, "x": 0, "y": -8.96}, {"time": 0.8333, "x": 0, "y": 8.47}, {"time": 0.8667, "x": 0, "y": 38.33}, {"time": 0.9, "x": 0, "y": 45.55}, {"time": 0.9333, "x": 0, "y": 36.97}, {"time": 0.9667, "x": 0, "y": 8.47}, {"time": 1, "x": 0, "y": -8.96}, {"time": 1.0333, "x": 0, "y": 8.47}, {"time": 1.0667, "x": 0, "y": 38.33}, {"time": 1.1, "x": 0, "y": 45.55}, {"time": 1.1333, "x": 0, "y": 36.97}, {"time": 1.1667, "x": 0, "y": 8.47}, {"time": 1.2, "x": 0, "y": -8.96}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.0333, "x": 1, "y": 0.846, "curve": "stepped"}, {"time": 0.0667, "x": 1, "y": 0.846}, {"time": 0.1, "x": 1, "y": 0.592}, {"time": 0.1333, "x": 1, "y": 0.846, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 0.846}, {"time": 0.2, "x": 1, "y": 1}, {"time": 0.2333, "x": 1, "y": 0.846, "curve": "stepped"}, {"time": 0.2667, "x": 1, "y": 0.846}, {"time": 0.3, "x": 1, "y": 0.592}, {"time": 0.3333, "x": 1, "y": 0.846, "curve": "stepped"}, {"time": 0.3667, "x": 1, "y": 0.846}, {"time": 0.4, "x": 1, "y": 1}, {"time": 0.4333, "x": 1, "y": 0.846, "curve": "stepped"}, {"time": 0.4667, "x": 1, "y": 0.846}, {"time": 0.5, "x": 1, "y": 0.592}, {"time": 0.5333, "x": 1, "y": 0.846, "curve": "stepped"}, {"time": 0.5667, "x": 1, "y": 0.846}, {"time": 0.6, "x": 1, "y": 1}, {"time": 0.6333, "x": 1, "y": 0.846, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 0.846}, {"time": 0.7, "x": 1, "y": 0.592}, {"time": 0.7333, "x": 1, "y": 0.846, "curve": "stepped"}, {"time": 0.7667, "x": 1, "y": 0.846}, {"time": 0.8, "x": 1, "y": 1}, {"time": 0.8333, "x": 1, "y": 0.846, "curve": "stepped"}, {"time": 0.8667, "x": 1, "y": 0.846}, {"time": 0.9, "x": 1, "y": 0.592}, {"time": 0.9333, "x": 1, "y": 0.846, "curve": "stepped"}, {"time": 0.9667, "x": 1, "y": 0.846}, {"time": 1, "x": 1, "y": 1}, {"time": 1.0333, "x": 1, "y": 0.846, "curve": "stepped"}, {"time": 1.0667, "x": 1, "y": 0.846}, {"time": 1.1, "x": 1, "y": 0.592}, {"time": 1.1333, "x": 1, "y": 0.846, "curve": "stepped"}, {"time": 1.1667, "x": 1, "y": 0.846}, {"time": 1.2, "x": 1, "y": 1}]}}}, "mo": {"slots": {"b": {"attachment": [{"time": 0, "name": "b"}, {"time": 0.6667, "name": "b"}]}, "b1": {"attachment": [{"time": 0, "name": null}, {"time": 0.6667, "name": null}]}, "d": {"attachment": [{"time": 0, "name": null}, {"time": 0.6667, "name": null}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1}]}, "d": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0}], "translate": [{"time": 0, "x": -0.51, "y": 5.55, "curve": "stepped"}, {"time": 0.6667, "x": -0.51, "y": 5.55}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1}]}, "b": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": -20.1}, {"time": 0.6667, "angle": 0.54}], "translate": [{"time": 0, "x": -15.45, "y": -0.82}, {"time": 0.5, "x": 145.95, "y": -127.37}, {"time": 0.6667, "x": 143.14, "y": -142.57}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.5, "x": 0.675, "y": 0.675, "curve": "stepped"}, {"time": 0.6667, "x": 0.675, "y": 0.675}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": -8.96}, {"time": 0.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1}]}}, "drawOrder": [{"time": 0}, {"time": 0.6667}]}}}