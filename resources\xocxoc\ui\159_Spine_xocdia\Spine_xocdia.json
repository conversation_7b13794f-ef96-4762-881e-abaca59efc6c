{"skins": {"default": {"chipdo3": {"chipdo": {"triangles": [3, 0, 1, 3, 1, 2], "uvs": [0, 0, 1, 0, 1, 1, 0, 1], "vertices": [1, 7, -10, 11.35, 1, 1, 7, 9, 11.35, 1, 1, 7, 9, -8.65, 1, 1, 7, -10, -8.65, 1], "width": 19, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 20}}, "chipdo4": {"chipdo": {"triangles": [3, 0, 1, 3, 1, 2], "uvs": [0, 0, 1, 0, 1, 1, 0, 1], "vertices": [1, 8, -10, 11.35, 1, 1, 8, 9, 11.35, 1, 1, 8, 9, -8.65, 1, 1, 8, -10, -8.65, 1], "width": 19, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 20}}, "MoBat": {"MoBat": {"triangles": [0, 3, 2, 0, 2, 1], "uvs": [0, 1, 1, 1, 1, 0, 0, 0], "vertices": [1, 13, -58.15, -13.84, 1, 1, 13, 58.81, -10.68, 1, 1, 13, 57.95, 21.3, 1, 1, 13, -59.01, 18.14, 1], "width": 117, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 32}}, "shadow": {"shadow": {"triangles": [1, 0, 3, 1, 3, 2], "uvs": [0, 1, 1, 1, 1, 0.17759, 0, 0.18545], "vertices": [1, 4, -58.4, -28.04, 1, 1, 4, 60.6, -28.04, 1, 1, 4, 60.6, 44.33, 1, 1, 4, -58.4, 43.64, 1], "width": 119, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 88}}, "chiptrang": {"chiptrang": {"triangles": [3, 0, 1, 3, 1, 2], "uvs": [0, 0, 1, 0, 1, 1, 0, 1], "vertices": [1, 9, -9, 10.35, 1, 1, 9, 10, 10.35, 1, 1, 9, 10, -9.65, 1, 1, 9, -9, -9.65, 1], "width": 19, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 20}}, "chipdo2": {"chipdo": {"triangles": [3, 0, 1, 3, 1, 2], "uvs": [0, 0, 1, 0, 1, 1, 0, 1], "vertices": [1, 5, -9.5, 11.35, 1, 1, 5, 9.5, 11.35, 1, 1, 5, 9.5, -8.65, 1, 1, 5, -9.5, -8.65, 1], "width": 19, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 20}}, "chiptrang4": {"chiptrang": {"triangles": [3, 0, 1, 3, 1, 2], "uvs": [0, 0, 1, 0, 1, 1, 0, 1], "vertices": [1, 12, -10.5, 8.85, 1, 1, 12, 8.5, 8.85, 1, 1, 12, 8.5, -11.15, 1, 1, 12, -10.5, -11.15, 1], "width": 19, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 20}}, "Xocdia01": {"xocdia01": {"triangles": [2, 3, 0, 2, 0, 1], "uvs": [0, 0, 1, 0, 1, 1, 0, 1], "vertices": [1, 1, 39.67, 58.59, 1, 1, 1, 38.33, -60.4, 1, 1, 1, -49.67, -59.4, 1, 1, 1, -48.31, 59.59, 1], "width": 119, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 85}}, "chiptrang3": {"chiptrang": {"triangles": [3, 0, 1, 3, 1, 2], "uvs": [0, 0, 1, 0, 1, 1, 0, 1], "vertices": [1, 11, -10, 10.35, 1, 1, 11, 9, 10.35, 1, 1, 11, 9, -9.65, 1, 1, 11, -10, -9.65, 1], "width": 19, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 20}}, "Xocdia00": {"xocdia00": {"triangles": [3, 20, 21, 3, 21, 22, 4, 3, 22, 22, 5, 4, 18, 19, 0, 14, 13, 18, 13, 17, 18, 17, 12, 16, 17, 13, 12, 21, 20, 26, 26, 20, 27, 20, 19, 27, 18, 25, 19, 19, 25, 27, 21, 23, 22, 23, 26, 24, 23, 21, 26, 18, 17, 25, 25, 17, 15, 22, 6, 5, 22, 23, 6, 17, 16, 15, 12, 11, 16, 16, 11, 15, 6, 23, 7, 23, 24, 7, 25, 15, 10, 24, 26, 7, 10, 15, 11, 26, 8, 7, 25, 10, 27, 8, 26, 9, 10, 9, 27, 26, 27, 9, 2, 19, 20, 2, 20, 3, 19, 2, 1, 0, 14, 18, 19, 1, 0], "uvs": [0, 0.5465, 0, 1, 1, 1, 1, 0.55148, 0.9913, 0.40021, 0.93748, 0.24395, 0.84294, 0.11595, 0.71348, 0.03117, 0.60148, 0, 0.51425, 0, 0.43421, 0, 0.29021, 0.03949, 0.16949, 0.12094, 0.08076, 0.22899, 0.01239, 0.38026, 0.3353, 0.07273, 0.30039, 0.14089, 0.30912, 0.22068, 0.35567, 0.31211, 0.4633, 0.37195, 0.59566, 0.37361, 0.69166, 0.31543, 0.74548, 0.22068, 0.73821, 0.13756, 0.69603, 0.06442, 0.4313, 0.02951, 0.59857, 0.02785, 0.51425, 0.02004], "vertices": [2, 2, -46.39, 9.75, 0.69455, 3, -29.54, 45.41, 0.30545, 2, 2, -46.39, -25.17, 0.96635, 3, -64.46, 45.41, 0.03365, 2, 2, 41.61, -25.17, 0.99119, 3, -64.46, -42.59, 0.00881, 2, 2, 41.61, 9.36, 0.72717, 3, -29.92, -42.59, 0.27283, 2, 2, 40.85, 21.01, 0.55598, 3, -18.27, -41.82, 0.44402, 2, 2, 36.11, 33.04, 0.36865, 3, -6.24, -37.08, 0.63135, 2, 2, 27.79, 42.9, 0.19463, 3, 3.62, -28.76, 0.80537, 2, 2, 16.4, 49.43, 0.05619, 3, 10.14, -17.37, 0.94381, 2, 2, 6.54, 51.83, 0.00454, 3, 12.54, -7.52, 0.99546, 1, 3, 12.54, 0.16, 1, 2, 2, -8.18, 51.83, 0.00341, 3, 12.54, 7.2, 0.99659, 2, 2, -20.85, 48.79, 0.07413, 3, 9.5, 19.88, 0.92587, 2, 2, -31.47, 42.52, 0.20447, 3, 3.23, 30.5, 0.79553, 2, 2, -39.28, 34.2, 0.35039, 3, -5.09, 38.31, 0.64961, 2, 2, -45.3, 22.55, 0.52232, 3, -16.74, 44.32, 0.47768, 2, 2, -16.88, 46.23, 0.05472, 3, 6.94, 15.91, 0.94528, 2, 2, -19.95, 40.98, 0.11493, 3, 1.7, 18.98, 0.88507, 2, 2, -19.18, 34.84, 0.18395, 3, -4.45, 18.21, 0.81605, 2, 2, -15.09, 27.8, 0.28964, 3, -11.49, 14.12, 0.71036, 2, 2, -5.62, 23.19, 0.38163, 3, -16.1, 4.64, 0.61837, 2, 2, 6.03, 23.06, 0.39642, 3, -16.22, -7, 0.60358, 2, 2, 14.48, 27.54, 0.3156, 3, -11.74, -15.45, 0.6844, 2, 2, 19.22, 34.84, 0.21054, 3, -4.45, -20.19, 0.78946, 2, 2, 18.58, 41.24, 0.12454, 3, 1.95, -19.55, 0.87546, 2, 2, 14.86, 46.87, 0.05449, 3, 7.58, -15.84, 0.94551, 2, 2, -8.43, 49.56, 0.00462, 3, 10.27, 7.46, 0.99538, 2, 2, 6.29, 49.68, 0.0051, 3, 10.4, -7.26, 0.9949, 1, 3, 11, 0.16, 1], "width": 88, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28, 16, 18, 18, 20], "type": "mesh", "hull": 15, "height": 77}}, "chipdo": {"chipdo": {"triangles": [3, 0, 1, 3, 1, 2], "uvs": [0, 0, 1, 0, 1, 1, 0, 1], "vertices": [1, 6, -11, 12.35, 1, 1, 6, 8, 12.35, 1, 1, 6, 8, -7.65, 1, 1, 6, -11, -7.65, 1], "width": 19, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 20}}, "chiptrang2": {"chiptrang": {"triangles": [3, 0, 1, 3, 1, 2], "uvs": [0, 0, 1, 0, 1, 1, 0, 1], "vertices": [1, 10, -10, 10.35, 1, 1, 10, 9, 10.35, 1, 1, 10, 9, -9.65, 1, 1, 10, -10, -9.65, 1], "width": 19, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 20}}}}, "skeleton": {"images": "", "width": 249.05, "spine": "3.7.93", "audio": "/Users/<USER>/Desktop/longshin/XocDia/Spine_XocDia", "hash": "CjSH4HDfSU0EH8pNtPJlwR7hw3c", "height": 94.83}, "slots": [{"attachment": "shadow", "name": "shadow", "bone": "root"}, {"attachment": "xocdia01", "name": "Xocdia01", "bone": "root"}, {"attachment": "chipdo", "name": "chipdo", "bone": "root"}, {"attachment": "chipdo", "name": "chipdo3", "bone": "root"}, {"attachment": "chipdo", "name": "chipdo4", "bone": "root"}, {"attachment": "chipdo", "name": "chipdo2", "bone": "root"}, {"attachment": "chiptrang", "name": "chiptrang", "bone": "root"}, {"attachment": "chiptrang", "name": "chiptrang2", "bone": "root"}, {"attachment": "chiptrang", "name": "chiptrang3", "bone": "root"}, {"attachment": "chiptrang", "name": "chiptrang4", "bone": "root"}, {"attachment": "xocdia00", "name": "Xocdia00", "bone": "root"}, {"attachment": "MoBat", "name": "MoBat", "bone": "root"}], "bones": [{"name": "root"}, {"parent": "root", "rotation": 90.65, "name": "bone", "length": 11.44, "x": 0.04, "y": 4.99}, {"parent": "root", "name": "bone2", "x": 2.39, "y": -4.45}, {"parent": "bone2", "rotation": 90, "name": "bone3", "length": 7.92, "x": -0.97, "y": 39.28}, {"parent": "root", "name": "bone4", "x": -0.65, "y": -19.4}, {"parent": "root", "name": "bone5", "x": 116, "y": 23.15}, {"parent": "root", "name": "bone6", "x": 139.5, "y": 22.15}, {"parent": "root", "name": "bone7", "x": 160, "y": 23.15}, {"parent": "root", "name": "bone8", "x": 181, "y": 23.15}, {"parent": "root", "name": "bone9", "x": 115.5, "y": -0.35}, {"parent": "root", "name": "bone10", "x": 138.5, "y": -0.35}, {"parent": "root", "name": "bone11", "x": 159.5, "y": -0.35}, {"parent": "root", "name": "bone12", "x": 181, "y": 1.15}, {"parent": "root", "rotation": -1.55, "name": "bone13", "length": 19.73, "x": 3.55, "y": -4.35}], "animations": {"3 White 1 Red": {"slots": {"chipdo3": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}], "attachment": [{"name": null, "time": 0}, {"name": "chipdo", "time": 6.1667}]}, "chipdo4": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffff00", "time": 0.5}]}, "MoBat": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}, {"color": "ffffff00", "time": 1.3333}]}, "chiptrang": {"color": [{"color": "ffffff00", "time": 0}]}, "chipdo2": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}]}, "chiptrang4": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}]}, "chiptrang3": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}]}, "chipdo": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}], "attachment": [{"name": null, "time": 0}, {"name": "chipdo", "time": 6.1667}]}, "chiptrang2": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}]}}, "bones": {"bone2": {"translate": [{"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": 0, "y": 599.12, "time": 0.6667}, {"x": 0, "y": 599.12, "time": 5.8333}, {"x": 0, "y": 0, "time": 6.1667}]}, "bone5": {"translate": [{"x": -131.5, "y": -12, "time": 0}]}, "bone10": {"translate": [{"x": -121.5, "y": 12.5, "time": 0}]}, "bone7": {"translate": [{"x": -173.5, "y": -33, "time": 0}]}, "bone6": {"translate": [{"x": -122, "y": -10.5, "time": 0}]}, "bone9": {"translate": [{"x": -135.5, "y": 11.5, "time": 0}]}, "bone13": {"scale": [{"x": 0.256, "y": 0.256, "time": 0}, {"x": 1.229, "y": 1.229, "time": 0.5}, {"x": 1.509, "y": 1.509, "time": 0.8333}, {"x": 1.691, "y": 1.691, "time": 1.3333}]}, "bone8": {"translate": [{"x": -165.5, "y": -33, "time": 0}]}, "bone11": {"translate": [{"x": -176, "y": -12.5, "time": 0}]}, "bone12": {"translate": [{"x": -164.5, "y": -12.5, "time": 0}]}}}, "4 White": {"slots": {"chipdo3": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}], "attachment": [{"name": null, "time": 0}, {"name": "chipdo", "time": 6.1667}]}, "chipdo4": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}], "attachment": [{"name": null, "time": 0}, {"name": "chipdo", "time": 6.1667}]}, "MoBat": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}, {"color": "ffffff00", "time": 1.3333}]}, "chiptrang": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}]}, "chipdo2": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}], "attachment": [{"name": null, "time": 0}, {"name": "chipdo", "time": 6.1667}]}, "chiptrang4": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}]}, "chiptrang3": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}]}, "chipdo": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}], "attachment": [{"name": null, "time": 0}, {"name": "chipdo", "time": 6.1667}]}, "chiptrang2": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}]}}, "bones": {"bone2": {"translate": [{"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": 0, "y": 599.12, "time": 0.6667}, {"x": 0, "y": 599.12, "time": 5.8333}, {"x": 0, "y": 0, "time": 6.1667}]}, "bone5": {"translate": [{"x": -131.5, "y": -12, "time": 0}]}, "bone10": {"translate": [{"x": -121.5, "y": 12.5, "time": 0}]}, "bone7": {"translate": [{"x": -173.5, "y": -33, "time": 0}]}, "bone6": {"translate": [{"x": -122, "y": -10.5, "time": 0}]}, "bone9": {"translate": [{"x": -135.5, "y": 11.5, "time": 0}]}, "bone13": {"scale": [{"x": 0.256, "y": 0.256, "time": 0}, {"x": 1.229, "y": 1.229, "time": 0.5}, {"x": 1.509, "y": 1.509, "time": 0.8333}, {"x": 1.691, "y": 1.691, "time": 1.3333}]}, "bone8": {"translate": [{"x": -165.5, "y": -33, "time": 0}]}, "bone11": {"translate": [{"x": -176, "y": -12.5, "time": 0}]}, "bone12": {"translate": [{"x": -164.5, "y": -12.5, "time": 0}]}}}, "XocXoc": {"slots": {"chipdo3": {"color": [{"color": "ffffff00", "time": 0}]}, "chipdo4": {"color": [{"color": "ffffff00", "time": 0}]}, "MoBat": {"attachment": [{"name": null, "time": 0}]}, "shadow": {"color": [{"color": "ffffffac", "time": 0.1333}, {"color": "ffffffdd", "time": 0.2333}, {"color": "ffffffb7", "time": 0.3333}, {"color": "fffffff5", "time": 0.4333}, {"color": "ffffffc4", "time": 0.5333}, {"color": "ffffffec", "time": 0.6333}, {"color": "ffffffa5", "time": 0.7333}, {"color": "ffffffd8", "time": 0.8333}, {"color": "ffffffae", "time": 0.9333}, {"color": "ffffffed", "time": 1.1}, {"color": "ffffffac", "time": 1.3}, {"color": "ffffffdd", "time": 1.4}, {"color": "ffffffb7", "time": 1.5}, {"color": "fffffff5", "time": 1.6}, {"color": "ffffffc4", "time": 1.7}, {"color": "ffffffec", "time": 1.8}, {"color": "ffffffa5", "time": 1.9}, {"color": "ffffffd8", "time": 2}, {"color": "ffffffae", "time": 2.1}, {"color": "ffffffed", "time": 2.2667}]}, "chiptrang": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 2.3}, {"color": "ffffffff", "time": 2.3333}]}, "chipdo2": {"color": [{"color": "ffffff00", "time": 0}]}, "chiptrang4": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffff00", "time": 1.1333}, {"color": "ffffff00", "time": 1.1667}, {"color": "ffffff00", "time": 2.3}, {"color": "ffffffff", "time": 2.3333}]}, "chiptrang3": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 2.3}, {"color": "ffffffff", "time": 2.3333}]}, "chipdo": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffff00", "time": 0.9333}, {"color": "ffffff00", "time": 1.1667}, {"color": "ffffff00", "time": 2.1}, {"color": "ffffff00", "time": 2.3333}]}, "chiptrang2": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 2.3}, {"color": "ffffffff", "time": 2.3333}]}}, "bones": {"bone2": {"rotate": [{"angle": 0, "time": 0}, {"angle": -4.13, "time": 0.1333}, {"angle": 5.31, "time": 0.3333}, {"angle": 0.29, "time": 0.4333}, {"angle": -4.13, "time": 0.5333}, {"angle": 4.12, "time": 0.7333}, {"angle": -1.46, "time": 0.9333}, {"angle": 0, "time": 1.1667}, {"angle": -4.13, "time": 1.3}, {"angle": 5.31, "time": 1.5}, {"angle": 0.29, "time": 1.6}, {"angle": -4.13, "time": 1.7}, {"angle": 4.12, "time": 1.9}, {"angle": -1.46, "time": 2.1}, {"angle": 0, "time": 2.3333}], "scale": [{"x": 1, "y": 1, "time": 0.1333}, {"x": 1, "y": 0.939, "time": 0.3333}, {"x": 1, "y": 0.953, "time": 0.4333}, {"x": 1, "y": 1, "time": 0.5333}, {"x": 1, "y": 0.939, "time": 0.7333}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.9333}, {"x": 1, "y": 1, "time": 1.3}, {"x": 1, "y": 0.939, "time": 1.5}, {"x": 1, "y": 0.953, "time": 1.6}, {"x": 1, "y": 1, "time": 1.7}, {"x": 1, "y": 0.939, "time": 1.9}, {"x": 1, "y": 1, "time": 2.1}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 0, "y": 15.35, "time": 0.1333}, {"x": 0, "y": 7.3, "time": 0.2333}, {"x": 0, "y": 18.41, "time": 0.3333}, {"x": 0, "y": 7.64, "time": 0.4333}, {"x": 0, "y": 15.35, "time": 0.5333}, {"x": 0, "y": 7.3, "time": 0.6333}, {"x": 0, "y": 18.41, "time": 0.7333}, {"x": 0, "y": 7.64, "time": 0.8333}, {"x": 0, "y": 13.17, "time": 0.9333}, {"x": 0, "y": 0, "time": 1.1667}, {"x": 0, "y": 15.35, "time": 1.3}, {"x": 0, "y": 7.3, "time": 1.4}, {"x": 0, "y": 18.41, "time": 1.5}, {"x": 0, "y": 7.64, "time": 1.6}, {"x": 0, "y": 15.35, "time": 1.7}, {"x": 0, "y": 7.3, "time": 1.8}, {"x": 0, "y": 18.41, "time": 1.9}, {"x": 0, "y": 7.64, "time": 2}, {"x": 0, "y": 13.17, "time": 2.1}, {"x": 0, "y": 0, "time": 2.3333}]}, "bone5": {"translate": [{"x": -131.5, "y": -12, "time": 0}]}, "bone4": {"scale": [{"x": 1, "y": 1, "time": 0}, {"x": 0.94, "y": 0.94, "time": 0.0667}, {"x": 0.809, "y": 0.809, "time": 0.1333}, {"x": 0.894, "y": 0.894, "time": 0.2333}, {"x": 0.803, "y": 0.803, "time": 0.3333}, {"x": 0.91, "y": 0.91, "time": 0.4333}, {"x": 0.809, "y": 0.809, "time": 0.5333}, {"x": 0.894, "y": 0.894, "time": 0.6333}, {"x": 0.803, "y": 0.803, "time": 0.7333}, {"x": 0.91, "y": 0.91, "time": 0.8333}, {"x": 0.842, "y": 0.842, "time": 0.9333}, {"x": 1, "y": 1, "time": 1.1667}, {"x": 0.94, "y": 0.94, "time": 1.2333}, {"x": 0.809, "y": 0.809, "time": 1.3}, {"x": 0.894, "y": 0.894, "time": 1.4}, {"x": 0.803, "y": 0.803, "time": 1.5}, {"x": 0.91, "y": 0.91, "time": 1.6}, {"x": 0.809, "y": 0.809, "time": 1.7}, {"x": 0.894, "y": 0.894, "time": 1.8}, {"x": 0.803, "y": 0.803, "time": 1.9}, {"x": 0.91, "y": 0.91, "time": 2}, {"x": 0.842, "y": 0.842, "time": 2.1}, {"x": 1, "y": 1, "time": 2.3333}]}, "bone10": {"translate": [{"x": -121.5, "y": 12.5, "time": 0}]}, "bone": {"rotate": [{"angle": 0, "time": 0}, {"angle": -4, "time": 0.1333}, {"angle": 0, "time": 0.2333}, {"angle": 4.94, "time": 0.3333}, {"angle": 0, "time": 0.4333}, {"angle": -4, "time": 0.5333}, {"angle": 0, "time": 0.6333}, {"angle": 4.94, "time": 0.7333}, {"angle": 0, "time": 0.8333}, {"angle": -5.21, "time": 0.9333}, {"angle": 0, "time": 1.1667}, {"angle": -4, "time": 1.3}, {"angle": 0, "time": 1.4}, {"angle": 4.94, "time": 1.5}, {"angle": 0, "time": 1.6}, {"angle": -4, "time": 1.7}, {"angle": 0, "time": 1.8}, {"angle": 4.94, "time": 1.9}, {"angle": 0, "time": 2}, {"angle": -5.21, "time": 2.1}, {"angle": 0, "time": 2.3333}], "scale": [{"x": 1, "y": 1, "time": 0.2333}, {"x": 0.858, "y": 1, "time": 0.3333}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.4333}, {"x": 1, "y": 1, "time": 0.6333}, {"x": 0.858, "y": 1, "time": 0.7333}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.8333}, {"x": 1, "y": 1, "time": 1.4}, {"x": 0.858, "y": 1, "time": 1.5}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.6}, {"x": 1, "y": 1, "time": 1.8}, {"x": 0.858, "y": 1, "time": 1.9}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 0, "y": 15.75, "time": 0.1333}, {"x": 0, "y": 8.55, "time": 0.2333}, {"x": 0, "y": 14.4, "time": 0.3333}, {"x": 0, "y": 8.55, "time": 0.4333}, {"x": 0, "y": 15.75, "time": 0.5333}, {"x": 0, "y": 8.55, "time": 0.6333}, {"x": 0, "y": 14.4, "time": 0.7333}, {"x": 0, "y": 8.55, "time": 0.8333}, {"x": 0, "y": 14.4, "time": 0.9333}, {"x": 0, "y": 0, "time": 1.1667}, {"x": 0, "y": 15.75, "time": 1.3}, {"x": 0, "y": 8.55, "time": 1.4}, {"x": 0, "y": 14.4, "time": 1.5}, {"x": 0, "y": 8.55, "time": 1.6}, {"x": 0, "y": 15.75, "time": 1.7}, {"x": 0, "y": 8.55, "time": 1.8}, {"x": 0, "y": 14.4, "time": 1.9}, {"x": 0, "y": 8.55, "time": 2}, {"x": 0, "y": 14.4, "time": 2.1}, {"x": 0, "y": 0, "time": 2.3333}]}, "bone7": {"translate": [{"x": -173.5, "y": -33, "time": 0}]}, "bone6": {"translate": [{"x": -122, "y": -10.5, "time": 0}]}, "bone9": {"translate": [{"x": -135.5, "y": 11.5, "time": 0}]}, "bone8": {"translate": [{"x": -165.5, "y": -33, "time": 0}]}, "bone11": {"translate": [{"x": -176, "y": -12.5, "time": 0}]}, "bone12": {"translate": [{"x": -164.5, "y": -12.5, "time": 0}]}}}, "4 Red": {"slots": {"chipdo3": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}]}, "chipdo4": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}]}, "MoBat": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}, {"color": "ffffff00", "time": 1.3333}]}, "chiptrang": {"color": [{"color": "ffffff00", "time": 0}], "attachment": [{"name": null, "time": 0}]}, "chipdo2": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}]}, "chiptrang4": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}], "attachment": [{"name": null, "time": 0}]}, "chiptrang3": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}], "attachment": [{"name": null, "time": 0}]}, "chipdo": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}]}, "chiptrang2": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}], "attachment": [{"name": null, "time": 0}]}}, "bones": {"bone2": {"translate": [{"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": 0, "y": 599.12, "time": 0.6667}, {"x": 0, "y": 599.12, "time": 5.8333}, {"x": 0, "y": 0, "time": 6.1667}]}, "bone5": {"translate": [{"x": -131.5, "y": -12, "time": 0}]}, "bone10": {"translate": [{"x": -121.5, "y": 12.5, "time": 0}]}, "bone7": {"translate": [{"x": -173.5, "y": -33, "time": 0}]}, "bone6": {"translate": [{"x": -122, "y": -10.5, "time": 0}]}, "bone9": {"translate": [{"x": -135.5, "y": 11.5, "time": 0}]}, "bone13": {"scale": [{"x": 0.256, "y": 0.256, "time": 0}, {"x": 1.229, "y": 1.229, "time": 0.5}, {"x": 1.509, "y": 1.509, "time": 0.8333}, {"x": 1.691, "y": 1.691, "time": 1.3333}]}, "bone8": {"translate": [{"x": -165.5, "y": -33, "time": 0}]}, "bone11": {"translate": [{"x": -176, "y": -12.5, "time": 0}]}, "bone12": {"translate": [{"x": -164.5, "y": -12.5, "time": 0}]}}}, "2 Red 2 White": {"slots": {"chipdo3": {"color": [{"color": "ffffff00", "time": 0}]}, "chipdo4": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffff00", "time": 0.5}]}, "MoBat": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}, {"color": "ffffff00", "time": 1.3333}]}, "chiptrang": {"color": [{"color": "ffffff00", "time": 0}]}, "chipdo2": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}]}, "chiptrang4": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}]}, "chiptrang3": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}]}, "chipdo": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}]}, "chiptrang2": {"color": [{"color": "ffffff00", "time": 0}]}}, "bones": {"bone2": {"translate": [{"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": 0, "y": 599.12, "time": 0.6667}, {"x": 0, "y": 599.12, "time": 5.8333}, {"x": 0, "y": 0, "time": 6.1667}]}, "bone5": {"translate": [{"x": -131.5, "y": -12, "time": 0}]}, "bone10": {"translate": [{"x": -121.5, "y": 12.5, "time": 0}]}, "bone7": {"translate": [{"x": -173.5, "y": -33, "time": 0}]}, "bone6": {"translate": [{"x": -122, "y": -10.5, "time": 0}]}, "bone9": {"translate": [{"x": -135.5, "y": 11.5, "time": 0}]}, "bone13": {"scale": [{"x": 0.256, "y": 0.256, "time": 0}, {"x": 1.229, "y": 1.229, "time": 0.5}, {"x": 1.509, "y": 1.509, "time": 0.8333}, {"x": 1.691, "y": 1.691, "time": 1.3333}]}, "bone8": {"translate": [{"x": -165.5, "y": -33, "time": 0}]}, "bone11": {"translate": [{"x": -176, "y": -12.5, "time": 0}]}, "bone12": {"translate": [{"x": -164.5, "y": -12.5, "time": 0}]}}}, "3 Red 1 White": {"slots": {"chipdo3": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}]}, "chipdo4": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffff00", "time": 0.5}]}, "MoBat": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}, {"color": "ffffff00", "time": 1.3333}]}, "chiptrang": {"color": [{"color": "ffffff00", "time": 0}]}, "chipdo2": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}]}, "chiptrang4": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}]}, "chiptrang3": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}], "attachment": [{"name": null, "time": 0}]}, "chipdo": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}]}, "chiptrang2": {"color": [{"color": "ffffff00", "time": 0}]}}, "bones": {"bone2": {"translate": [{"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": 0, "y": 599.12, "time": 0.6667}, {"x": 0, "y": 599.12, "time": 5.8333}, {"x": 0, "y": 0, "time": 6.1667}]}, "bone5": {"translate": [{"x": -131.5, "y": -12, "time": 0}]}, "bone10": {"translate": [{"x": -121.5, "y": 12.5, "time": 0}]}, "bone7": {"translate": [{"x": -173.5, "y": -33, "time": 0}]}, "bone6": {"translate": [{"x": -122, "y": -10.5, "time": 0}]}, "bone9": {"translate": [{"x": -135.5, "y": 11.5, "time": 0}]}, "bone13": {"scale": [{"x": 0.256, "y": 0.256, "time": 0}, {"x": 1.229, "y": 1.229, "time": 0.5}, {"x": 1.509, "y": 1.509, "time": 0.8333}, {"x": 1.691, "y": 1.691, "time": 1.3333}]}, "bone8": {"translate": [{"x": -165.5, "y": -33, "time": 0}]}, "bone11": {"translate": [{"x": -176, "y": -12.5, "time": 0}]}, "bone12": {"translate": [{"x": -164.5, "y": -12.5, "time": 0}]}}}, "Waiting": {"slots": {"chipdo3": {"color": [{"color": "ffffff00", "time": 0}]}, "chipdo4": {"color": [{"color": "ffffff00", "time": 0}]}, "MoBat": {"attachment": [{"name": null, "time": 0}]}, "chiptrang": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 1.1667}]}, "chipdo2": {"color": [{"color": "ffffff00", "time": 0}]}, "chiptrang4": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 1.1667}]}, "chiptrang3": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 1.1667}]}, "chipdo": {"color": [{"color": "ffffff00", "time": 0}]}, "chiptrang2": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 1.1667}]}}, "bones": {"bone5": {"translate": [{"x": -131.5, "y": -12, "time": 0}]}, "bone10": {"translate": [{"x": -121.5, "y": 12.5, "time": 0}]}, "bone7": {"translate": [{"x": -173.5, "y": -33, "time": 0}]}, "bone6": {"translate": [{"x": -122, "y": -10.5, "time": 0}]}, "bone9": {"translate": [{"x": -135.5, "y": 11.5, "time": 0}]}, "bone8": {"translate": [{"x": -165.5, "y": -33, "time": 0}]}, "bone11": {"translate": [{"x": -176, "y": -12.5, "time": 0}]}, "bone12": {"translate": [{"x": -164.5, "y": -12.5, "time": 0}]}}}}}