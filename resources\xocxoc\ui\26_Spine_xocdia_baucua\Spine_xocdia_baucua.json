{"skins": {"default": {"MoBat": {"MoBat": {"triangles": [0, 3, 2, 0, 2, 1], "uvs": [0, 1, 1, 1, 1, 0, 0, 0], "vertices": [1, 5, -58.15, -13.84, 1, 1, 5, 58.81, -10.68, 1, 1, 5, 57.95, 21.3, 1, 1, 5, -59.01, 18.14, 1], "width": 117, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 32}}, "shadow": {"shadow": {"triangles": [3, 0, 1, 3, 1, 2], "uvs": [0, 0.15527, 1, 0.15527, 1, 1, 0, 1], "vertices": [1, 4, -95.46, 62.05, 1, 1, 4, 99.81, 62.05, 1, 1, 4, 99.81, -59.92, 1, 1, 4, -95.46, -59.92, 1], "width": 119, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 88}}, "caidia": {"caidia": {"triangles": [3, 0, 1, 3, 1, 2], "uvs": [0, 0, 1, 0, 1, 1, 0, 1], "vertices": [1, 1, 57.99, 88.36, 1, 1, 1, 55.87, -97.62, 1, 1, 1, -79.12, -96.09, 1, 1, 1, -77.01, 89.9, 1], "width": 186, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 135}}, "caito": {"caito": {"triangles": [0, 1, 2, 0, 2, 3], "uvs": [0, 1, 0, 0, 1, 0, 1, 1], "vertices": [2, 2, -64.79, -40.39, 0.93659, 3, -79.68, 63.82, 0.06341, 2, 2, -64.79, 72.61, 0.07392, 3, 33.32, 63.82, 0.92608, 2, 2, 67.21, 72.61, 0.12612, 3, 33.32, -68.18, 0.87388, 2, 2, 67.21, -40.39, 0.95321, 3, -79.68, -68.18, 0.04679], "width": 132, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "type": "mesh", "hull": 4, "height": 113}}}}, "skeleton": {"images": "", "width": 195.26, "spine": "3.7.93", "audio": "", "hash": "d8Ajj5lIBQYUbbSMNnjDhegnLcg", "height": 147.49}, "slots": [{"attachment": "shadow", "name": "shadow", "bone": "root"}, {"attachment": "caidia", "name": "caidia", "bone": "root"}, {"attachment": "caito", "name": "caito", "bone": "root"}, {"attachment": "MoBat", "name": "MoBat", "bone": "root"}], "bones": [{"name": "root"}, {"parent": "root", "rotation": 90.65, "name": "bone", "length": 11.44, "x": 0.04, "y": 4.99}, {"parent": "root", "name": "bone2", "x": 2.39, "y": -4.45}, {"parent": "bone2", "rotation": 90, "name": "bone3", "length": 7.92, "x": -0.97, "y": 39.28}, {"parent": "root", "name": "bone4", "x": -0.65, "y": -19.4}, {"parent": "root", "rotation": -1.55, "name": "bone13", "length": 19.73, "x": 3.55, "y": -4.35}], "animations": {"mobat": {"slots": {"MoBat": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}, {"color": "ffffff00", "time": 1.3333}]}}, "bones": {"bone2": {"translate": [{"x": 0, "y": 0, "time": 0.5}, {"curve": "stepped", "x": 0, "y": 599.12, "time": 0.6667}, {"x": 0, "y": 599.12, "time": 5.8333}, {"x": 0, "y": 0, "time": 6.1667}]}, "bone4": {"scale": [{"x": 1, "y": 1, "time": 6.6667}]}, "bone13": {"scale": [{"x": 0.256, "y": 0.256, "time": 0}, {"x": 1.229, "y": 1.229, "time": 0.5}, {"x": 1.509, "y": 1.509, "time": 0.8333}, {"x": 1.691, "y": 1.691, "time": 1.3333}]}}}, "XocXoc": {"slots": {"MoBat": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"bone2": {"rotate": [{"angle": 0, "time": 0}, {"angle": -4.13, "time": 0.1333}, {"angle": 5.31, "time": 0.3333}, {"angle": 0.29, "time": 0.4333}, {"angle": -4.13, "time": 0.5333}, {"angle": 4.12, "time": 0.7333}, {"angle": -1.46, "time": 0.9333}, {"angle": 0, "time": 1.1667}, {"angle": -4.13, "time": 1.3}, {"angle": 5.31, "time": 1.5}, {"angle": 0.29, "time": 1.6}, {"angle": -4.13, "time": 1.7}, {"angle": 4.12, "time": 1.9}, {"angle": -1.46, "time": 2.1}, {"angle": 0, "time": 2.3333}], "scale": [{"x": 1, "y": 1, "time": 0.1333}, {"x": 1, "y": 0.939, "time": 0.3333}, {"x": 1, "y": 0.953, "time": 0.4333}, {"x": 1, "y": 1, "time": 0.5333}, {"x": 1, "y": 0.939, "time": 0.7333}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.9333}, {"x": 1, "y": 1, "time": 1.3}, {"x": 1, "y": 0.939, "time": 1.5}, {"x": 1, "y": 0.953, "time": 1.6}, {"x": 1, "y": 1, "time": 1.7}, {"x": 1, "y": 0.939, "time": 1.9}, {"x": 1, "y": 1, "time": 2.1}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 0, "y": 15.35, "time": 0.1333}, {"x": 0, "y": 7.3, "time": 0.2333}, {"x": 0, "y": 18.41, "time": 0.3333}, {"x": 0, "y": 7.64, "time": 0.4333}, {"x": 0, "y": 15.35, "time": 0.5333}, {"x": 0, "y": 7.3, "time": 0.6333}, {"x": 0, "y": 18.41, "time": 0.7333}, {"x": 0, "y": 7.64, "time": 0.8333}, {"x": 0, "y": 13.17, "time": 0.9333}, {"x": 0, "y": 0, "time": 1.1667}, {"x": 0, "y": 15.35, "time": 1.3}, {"x": 0, "y": 7.3, "time": 1.4}, {"x": 0, "y": 18.41, "time": 1.5}, {"x": 0, "y": 7.64, "time": 1.6}, {"x": 0, "y": 15.35, "time": 1.7}, {"x": 0, "y": 7.3, "time": 1.8}, {"x": 0, "y": 18.41, "time": 1.9}, {"x": 0, "y": 7.64, "time": 2}, {"x": 0, "y": 13.17, "time": 2.1}, {"x": 0, "y": 0, "time": 2.3333}]}, "bone4": {"scale": [{"x": 1, "y": 1, "time": 0}, {"x": 0.94, "y": 0.94, "time": 0.0667}, {"x": 0.809, "y": 0.809, "time": 0.1333}, {"x": 0.894, "y": 0.894, "time": 0.2333}, {"x": 0.803, "y": 0.803, "time": 0.3333}, {"x": 0.91, "y": 0.91, "time": 0.4333}, {"x": 0.809, "y": 0.809, "time": 0.5333}, {"x": 0.894, "y": 0.894, "time": 0.6333}, {"x": 0.803, "y": 0.803, "time": 0.7333}, {"x": 0.91, "y": 0.91, "time": 0.8333}, {"x": 0.842, "y": 0.842, "time": 0.9333}, {"x": 1, "y": 1, "time": 1.1667}, {"x": 0.94, "y": 0.94, "time": 1.2333}, {"x": 0.809, "y": 0.809, "time": 1.3}, {"x": 0.894, "y": 0.894, "time": 1.4}, {"x": 0.803, "y": 0.803, "time": 1.5}, {"x": 0.91, "y": 0.91, "time": 1.6}, {"x": 0.809, "y": 0.809, "time": 1.7}, {"x": 0.894, "y": 0.894, "time": 1.8}, {"x": 0.803, "y": 0.803, "time": 1.9}, {"x": 0.91, "y": 0.91, "time": 2}, {"x": 0.842, "y": 0.842, "time": 2.1}, {"x": 1, "y": 1, "time": 2.3333}]}, "bone": {"rotate": [{"angle": 0, "time": 0}, {"angle": -4, "time": 0.1333}, {"angle": 0, "time": 0.2333}, {"angle": 4.94, "time": 0.3333}, {"angle": 0, "time": 0.4333}, {"angle": -4, "time": 0.5333}, {"angle": 0, "time": 0.6333}, {"angle": 4.94, "time": 0.7333}, {"angle": 0, "time": 0.8333}, {"angle": -5.21, "time": 0.9333}, {"angle": 0, "time": 1.1667}, {"angle": -4, "time": 1.3}, {"angle": 0, "time": 1.4}, {"angle": 4.94, "time": 1.5}, {"angle": 0, "time": 1.6}, {"angle": -4, "time": 1.7}, {"angle": 0, "time": 1.8}, {"angle": 4.94, "time": 1.9}, {"angle": 0, "time": 2}, {"angle": -5.21, "time": 2.1}, {"angle": 0, "time": 2.3333}], "scale": [{"x": 1, "y": 1, "time": 0.2333}, {"x": 0.858, "y": 1, "time": 0.3333}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.4333}, {"x": 1, "y": 1, "time": 0.6333}, {"x": 0.858, "y": 1, "time": 0.7333}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.8333}, {"x": 1, "y": 1, "time": 1.4}, {"x": 0.858, "y": 1, "time": 1.5}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.6}, {"x": 1, "y": 1, "time": 1.8}, {"x": 0.858, "y": 1, "time": 1.9}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": 0, "y": 0, "time": 0}, {"x": 0, "y": 15.75, "time": 0.1333}, {"x": 0, "y": 8.55, "time": 0.2333}, {"x": 0, "y": 14.4, "time": 0.3333}, {"x": 0, "y": 8.55, "time": 0.4333}, {"x": 0, "y": 15.75, "time": 0.5333}, {"x": 0, "y": 8.55, "time": 0.6333}, {"x": 0, "y": 14.4, "time": 0.7333}, {"x": 0, "y": 8.55, "time": 0.8333}, {"x": 0, "y": 14.4, "time": 0.9333}, {"x": 0, "y": 0, "time": 1.1667}, {"x": 0, "y": 15.75, "time": 1.3}, {"x": 0, "y": 8.55, "time": 1.4}, {"x": 0, "y": 14.4, "time": 1.5}, {"x": 0, "y": 8.55, "time": 1.6}, {"x": 0, "y": 15.75, "time": 1.7}, {"x": 0, "y": 8.55, "time": 1.8}, {"x": 0, "y": 14.4, "time": 1.9}, {"x": 0, "y": 8.55, "time": 2}, {"x": 0, "y": 14.4, "time": 2.1}, {"x": 0, "y": 0, "time": 2.3333}]}}}, "Waiting": {"slots": {"MoBat": {"attachment": [{"name": null, "time": 0}]}}, "bones": {"bone4": {"scale": [{"x": 1, "y": 1, "time": 1.1667}]}}}}}