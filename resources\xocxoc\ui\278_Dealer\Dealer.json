{"skeleton": {"hash": "KFb8C5LUFAlTNcn6Qe8NAO2L/Mw", "spine": "3.7.93", "width": 364.13, "height": 224.02, "images": "./assest/", "audio": "/Users/<USER>/Desktop/longshin/XocDia/Dealer"}, "bones": [{"name": "Dealer", "scaleX": -0.996}, {"name": "Dealer2", "parent": "Dealer", "x": -81.17, "y": -81.57}, {"name": "Dealer3", "parent": "Dealer", "x": -81.17, "y": -81.57}, {"name": "Dealer4", "parent": "Dealer", "x": -81, "y": -81.57}, {"name": "Dealer5", "parent": "Dealer", "x": -81, "y": -81.57}, {"name": "Dealer6", "parent": "Dealer", "x": -81, "y": -81.4}, {"name": "Dealer7", "parent": "Dealer", "x": -84.15, "y": -81.61}], "slots": [{"name": "Dealer_01", "bone": "Dealer", "attachment": "Dealer_01"}, {"name": "Dealer_02", "bone": "Dealer", "attachment": "Dealer_02"}, {"name": "Dealer_03", "bone": "Dealer", "attachment": "Dealer_03"}, {"name": "Dealer_04", "bone": "Dealer", "attachment": "Dealer_04"}, {"name": "Dealer_05", "bone": "Dealer", "attachment": "Dealer_05"}, {"name": "Dealer_06", "bone": "Dealer", "attachment": "Dealer_06"}, {"name": "Dealer_07", "bone": "Dealer", "attachment": "Dealer_07"}, {"name": "Dealer_08", "bone": "Dealer"}, {"name": "xinmoidatcuoc", "bone": "Dealer", "attachment": "xinmoidatcuoc"}], "skins": {"default": {"Dealer_01": {"Dealer_01": {"type": "mesh", "uvs": [0.55323, 0, 0.61487, 0.04789, 0.66076, 0.09798, 0.73026, 0.31963, 0.73157, 0.33967, 0.65289, 0.40103, 0.63585, 0.41355, 0.72239, 0.42858, 0.77747, 0.45362, 1, 0.84683, 1, 0.89817, 0.91385, 0.94325, 0.6647, 1, 0.55192, 1, 0.47455, 0.95327, 0.47062, 0.8606, 0.33031, 0.85309, 0.26474, 0.73162, 0.20573, 0.81302, 0.15721, 0.87688, 0.07985, 0.88314, 0.01297, 0.86311, 0.02084, 0.81177, 0.11788, 0.65398, 0.23327, 0.46239, 0.30015, 0.42357, 0.38276, 0.41355, 0.39361, 0.40752, 0.37391, 0.39779, 0.30463, 0.34265, 0.32365, 0.28881, 0.32636, 0.16427, 0.36983, 0.05438, 0.46493, 0, 0.49036, 0.30461, 0.50936, 0.31357, 0.52813, 0.30277, 0.47472, 0.33516, 0.51009, 0.33953, 0.54641, 0.33585, 0.49469, 0.35239, 0.52476, 0.35308, 0.48964, 0.33195, 0.50359, 0.33057, 0.51778, 0.3308, 0.53294, 0.33263, 0.49589, 0.33976, 0.52981, 0.33976, 0.42589, 0.24235, 0.43407, 0.23201, 0.44513, 0.22949, 0.45716, 0.23132, 0.46606, 0.23615, 0.47881, 0.24809, 0.43407, 0.24718, 0.4461, 0.25131, 0.45836, 0.25223, 0.46847, 0.25039, 0.54569, 0.24511, 0.55507, 0.2327, 0.56566, 0.22788, 0.57744, 0.22719, 0.58755, 0.22995, 0.59549, 0.23845, 0.55603, 0.25039, 0.56734, 0.25361, 0.57985, 0.25085, 0.59116, 0.24718, 0.53895, 0.21157, 0.56325, 0.20491, 0.58466, 0.20651, 0.6003, 0.22328, 0.60559, 0.23914, 0.60607, 0.25843, 0.59789, 0.27245, 0.57792, 0.28026, 0.5594, 0.27796, 0.54545, 0.2713, 0.53558, 0.26027, 0.52789, 0.24832, 0.5262, 0.23293, 0.49469, 0.23454, 0.48122, 0.21455, 0.46582, 0.2072, 0.44898, 0.20674, 0.4295, 0.21547, 0.41963, 0.23247, 0.41627, 0.24488, 0.41819, 0.2605, 0.43383, 0.27497, 0.45115, 0.28049, 0.46799, 0.28095, 0.48122, 0.27635, 0.49276, 0.26463, 0.49589, 0.25085, 0.4434, 0.34912, 0.46801, 0.37212, 0.49, 0.38762, 0.5078, 0.39462, 0.52246, 0.39412, 0.54655, 0.38212, 0.57063, 0.36162, 0.58634, 0.34212, 0.60467, 0.30562, 0.60938, 0.27962, 0.42246, 0.31462, 0.41565, 0.28712], "triangles": [1, 68, 0, 70, 69, 1, 2, 70, 1, 83, 84, 32, 31, 32, 84, 68, 83, 33, 33, 83, 32, 68, 33, 0, 1, 69, 68, 82, 83, 68, 85, 31, 84, 71, 70, 2, 61, 69, 70, 62, 61, 70, 60, 69, 61, 59, 68, 69, 50, 85, 84, 71, 62, 70, 51, 84, 83, 52, 51, 83, 50, 84, 51, 49, 85, 50, 86, 31, 85, 86, 85, 49, 60, 59, 69, 80, 82, 68, 80, 68, 59, 81, 82, 80, 82, 52, 83, 81, 52, 82, 63, 62, 71, 72, 71, 2, 63, 71, 72, 48, 86, 49, 87, 31, 86, 87, 86, 48, 58, 80, 59, 67, 62, 63, 54, 48, 49, 53, 52, 81, 79, 80, 58, 57, 52, 53, 65, 64, 59, 58, 59, 64, 94, 81, 80, 94, 80, 79, 53, 81, 94, 66, 61, 62, 66, 62, 67, 55, 50, 51, 54, 49, 50, 55, 54, 50, 56, 51, 52, 56, 52, 57, 55, 51, 56, 66, 65, 60, 66, 60, 61, 65, 59, 60, 3, 104, 73, 78, 79, 58, 88, 87, 48, 88, 48, 54, 93, 53, 94, 64, 77, 78, 64, 78, 58, 67, 72, 73, 72, 67, 63, 74, 67, 73, 66, 67, 74, 89, 88, 54, 89, 54, 55, 92, 53, 93, 57, 53, 92, 76, 64, 65, 77, 64, 76, 73, 72, 3, 74, 73, 104, 75, 65, 66, 75, 66, 74, 76, 65, 75, 90, 55, 56, 89, 55, 90, 91, 56, 57, 92, 91, 57, 90, 56, 91, 87, 30, 31, 88, 30, 87, 106, 88, 89, 88, 106, 30, 79, 93, 94, 36, 78, 77, 79, 78, 93, 36, 93, 78, 34, 92, 93, 36, 34, 93, 91, 92, 34, 103, 74, 104, 75, 74, 103, 35, 34, 36, 105, 106, 89, 105, 89, 90, 3, 72, 2, 103, 104, 3, 43, 34, 35, 44, 35, 36, 43, 35, 44, 34, 37, 91, 42, 34, 43, 36, 39, 45, 76, 36, 77, 44, 36, 45, 42, 37, 34, 76, 39, 36, 38, 43, 44, 46, 42, 43, 46, 43, 38, 47, 44, 45, 102, 75, 103, 105, 37, 95, 90, 37, 105, 91, 37, 90, 46, 37, 42, 47, 38, 44, 41, 38, 47, 39, 75, 102, 75, 39, 76, 101, 39, 102, 96, 95, 37, 40, 37, 46, 96, 37, 40, 100, 39, 101, 97, 96, 40, 46, 38, 40, 47, 45, 39, 41, 47, 39, 100, 41, 39, 99, 41, 100, 40, 38, 41, 99, 98, 41, 98, 40, 41, 97, 40, 98, 30, 105, 29, 105, 30, 106, 28, 105, 95, 28, 29, 105, 4, 5, 103, 4, 103, 3, 102, 103, 5, 6, 101, 102, 27, 28, 95, 27, 95, 96, 5, 6, 102, 17, 24, 25, 23, 24, 17, 27, 96, 97, 27, 97, 98, 26, 27, 98, 18, 23, 17, 22, 23, 18, 26, 17, 25, 15, 16, 17, 15, 26, 98, 15, 17, 26, 19, 22, 18, 20, 21, 22, 19, 20, 22, 11, 8, 9, 10, 11, 9, 99, 14, 15, 100, 101, 6, 99, 100, 6, 99, 15, 98, 6, 14, 99, 8, 14, 6, 8, 6, 7, 12, 13, 14, 14, 8, 12, 11, 12, 8], "vertices": [11.29, 111, 24.35, 100.37, 34.08, 89.25, 48.82, 40.04, 49.09, 35.59, 32.41, 21.97, 28.8, 19.19, 47.15, 15.86, 58.82, 10.3, 106, -77, 106, -88.39, 87.74, -98.4, 34.92, -111, 11.01, -111, -5.39, -100.63, -6.23, -80.05, -35.97, -78.39, -49.87, -51.42, -62.38, -69.49, -72.67, -83.67, -89.07, -85.06, -103.25, -80.61, -101.58, -69.21, -81.01, -34.18, -56.55, 8.35, -42.37, 16.97, -24.85, 19.19, -22.56, 20.53, -26.73, 22.69, -41.42, 34.93, -37.39, 46.88, -36.81, 74.53, -27.6, 98.93, -7.44, 111, -2.04, 43.38, 1.99, 41.39, 5.96, 43.79, -5.36, 36.59, 2.14, 35.63, 9.84, 36.44, -1.13, 32.77, 5.25, 32.62, -2.2, 37.31, 0.76, 37.61, 3.77, 37.56, 6.98, 37.16, -0.87, 35.57, 6.32, 35.57, -15.71, 57.2, -13.98, 59.49, -11.63, 60.05, -9.08, 59.65, -7.19, 58.57, -4.49, 55.92, -13.98, 56.13, -11.43, 55.21, -8.83, 55, -6.68, 55.41, 9.69, 56.59, 11.67, 59.34, 13.92, 60.41, 16.42, 60.56, 18.56, 59.95, 20.24, 58.06, 11.88, 55.41, 14.28, 54.7, 16.93, 55.31, 19.32, 56.13, 8.26, 64.03, 13.41, 65.51, 17.95, 65.15, 21.26, 61.43, 22.38, 57.91, 22.49, 53.63, 20.75, 50.52, 16.52, 48.78, 12.59, 49.29, 9.63, 50.77, 7.54, 53.22, 5.91, 55.87, 5.56, 59.29, -1.13, 58.93, -3.98, 63.37, -7.25, 65, -10.82, 65.1, -14.95, 63.16, -17.04, 59.39, -17.75, 56.64, -17.34, 53.17, -14.03, 49.96, -10.36, 48.73, -6.79, 48.63, -3.98, 49.65, -1.53, 52.25, -0.87, 55.31, -12, 33.5, -6.78, 28.39, -2.12, 24.95, 1.65, 23.39, 4.76, 23.51, 9.87, 26.17, 14.97, 30.72, 18.3, 35.05, 22.19, 43.15, 23.19, 48.92, -16.44, 41.15, -17.88, 47.26], "hull": 34, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66], "width": 212, "height": 222}}, "Dealer_02": {"Dealer_02": {"type": "mesh", "uvs": [0.05895, 0, 0, 0.24607, 0, 0.50425, 0.0145, 0.60429, 0.10464, 0.78825, 0.22688, 0.88507, 0.25528, 0.83343, 0.59361, 1, 0.68745, 1, 0.75784, 0.92057, 0.77636, 1, 0.90354, 1, 1, 0.77623, 1, 0.53652, 1, 0.40743, 0.89983, 0.27189, 0.72326, 0.27189, 0.69239, 0.3203, 0.64053, 0.57525, 0.61831, 0.60752, 0.30838, 0.29771, 0.30714, 0.1073, 0.27133, 0], "triangles": [20, 2, 1, 16, 15, 14, 20, 3, 2, 14, 13, 16, 1, 21, 20, 21, 0, 22, 21, 1, 0, 20, 6, 3, 6, 4, 3, 19, 6, 20, 5, 4, 6, 16, 13, 17, 12, 9, 13, 13, 18, 17, 9, 18, 13, 19, 18, 9, 7, 6, 19, 9, 7, 19, 8, 7, 9, 11, 10, 9, 12, 11, 9], "vertices": [1, 1, -12.99, 20.8, 1, 1, 1, -19.77, 9.97, 1, 1, 1, -19.77, -1.39, 1, 1, 1, -18.11, -5.79, 1, 1, 1, -7.74, -13.88, 1, 1, 1, 6.32, -18.14, 1, 1, 1, 9.58, -15.87, 1, 1, 1, 48.49, -23.2, 1, 1, 1, 59.28, -23.2, 1, 1, 1, 67.38, -19.71, 1, 1, 1, 69.51, -23.2, 1, 1, 1, 84.13, -23.2, 1, 1, 1, 95.23, -13.35, 1, 1, 1, 95.23, -2.81, 1, 1, 1, 95.23, 2.87, 1, 1, 1, 83.71, 8.84, 1, 1, 1, 63.4, 8.84, 1, 1, 1, 59.85, 6.71, 1, 1, 1, 53.89, -4.51, 1, 1, 1, 51.33, -5.93, 1, 1, 1, 15.69, 7.7, 1, 1, 1, 15.55, 16.08, 1, 1, 1, 11.43, 20.8, 1], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44], "width": 115, "height": 44}}, "Dealer_03": {"Dealer_03": {"type": "mesh", "uvs": [0, 0.47887, 0.09856, 0.15291, 0.25232, 0.1167, 0.29865, 0.26674, 0.30076, 0.4323, 0.65249, 0.46852, 0.68198, 0.14257, 0.84205, 0, 1, 0.01322, 1, 0.16844, 0.92209, 0.24604, 0.95789, 0.63926, 0.86732, 0.73239, 0.7241, 0.85657, 0.2818, 1, 0.09014, 1, 0, 0.82035], "triangles": [7, 8, 9, 10, 7, 9, 6, 7, 10, 5, 6, 10, 5, 10, 11, 12, 5, 11, 15, 16, 14, 13, 5, 12, 4, 16, 0, 1, 2, 3, 3, 0, 1, 3, 4, 0, 4, 14, 16, 13, 14, 4, 13, 4, 5], "vertices": [1, 2, -20.3, 10.92, 1, 1, 2, -9.16, 25.91, 1, 1, 2, 8.21, 27.58, 1, 1, 2, 13.45, 20.67, 1, 1, 2, 13.69, 13.06, 1, 1, 2, 53.43, 11.39, 1, 1, 2, 56.77, 26.39, 1, 1, 2, 74.85, 32.94, 1, 1, 2, 92.7, 32.34, 1, 1, 2, 92.7, 25.2, 1, 1, 2, 83.9, 21.63, 1, 1, 2, 87.94, 3.54, 1, 1, 2, 77.71, -0.75, 1, 1, 2, 61.53, -6.46, 1, 1, 2, 11.55, -13.06, 1, 1, 2, -10.11, -13.06, 1, 1, 2, -20.3, -4.79, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32], "width": 113, "height": 46}}, "Dealer_04": {"Dealer_04": {"type": "mesh", "uvs": [0, 0.9138, 0.07956, 1, 0.24025, 1, 0.36296, 0.95763, 0.39217, 0.89627, 0.81288, 0.61142, 1, 0.40107, 1, 0.32657, 1, 1e-05, 0.78366, 0, 0.68141, 0.11184, 0.6668, 0.35725, 0.65219, 0.43175, 0.33082, 0.54569, 0.25486, 0.51501, 0.09125, 0.47557, 0, 0.62019], "triangles": [7, 10, 9, 7, 9, 8, 11, 10, 7, 6, 11, 7, 6, 12, 11, 5, 12, 6, 5, 4, 13, 5, 13, 12, 0, 16, 4, 14, 16, 15, 14, 4, 16, 14, 13, 4, 0, 2, 1, 0, 4, 2, 3, 2, 4], "vertices": [1, 3, -19.99, -7.6, 1, 1, 3, -11.88, -13.46, 1, 1, 3, 4.51, -13.46, 1, 1, 3, 17.03, -10.58, 1, 1, 3, 20.01, -6.41, 1, 1, 3, 62.92, 12.96, 1, 1, 3, 82.01, 27.27, 1, 1, 3, 82.01, 32.33, 1, 1, 3, 82.01, 54.54, 1, 1, 3, 59.94, 54.54, 1, 1, 3, 49.51, 46.93, 1, 1, 3, 48.02, 30.25, 1, 1, 3, 46.53, 25.18, 1, 1, 3, 13.75, 17.43, 1, 1, 3, 6, 19.52, 1, 1, 3, -10.69, 22.2, 1, 1, 3, -19.99, 12.37, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32], "width": 102, "height": 68}}, "Dealer_05": {"Dealer_05": {"type": "mesh", "uvs": [1e-05, 0.69577, 0.13064, 0.56727, 0.29654, 0.6004, 0.63624, 0.41182, 0.66046, 0.38919, 0.65286, 0.36304, 0.60726, 0.21431, 0.6081, 0.16283, 0.6993, 0.05087, 0.73561, 0.02227, 0.92413, 0, 0.93043, 0.02908, 0.88423, 0.10971, 0.8513, 0.11788, 1, 0.22739, 1, 0.27115, 0.92175, 0.35629, 0.87407, 0.36663, 0.842, 0.47485, 0.80911, 0.51384, 0.44323, 0.83052, 0.42596, 0.88383, 0.30756, 0.99828, 0.07241, 0.99828, 1e-05, 0.92424], "triangles": [10, 13, 9, 11, 13, 10, 11, 12, 13, 16, 13, 14, 15, 16, 14, 7, 13, 6, 13, 5, 6, 8, 9, 13, 8, 13, 7, 17, 13, 16, 5, 13, 17, 4, 5, 17, 18, 4, 17, 19, 4, 18, 20, 2, 3, 3, 4, 19, 20, 3, 19, 21, 2, 20, 24, 0, 22, 2, 0, 1, 2, 22, 0, 23, 24, 22, 21, 22, 2], "vertices": [1, 4, -19.76, 11.93, 1, 1, 4, -8, 23.88, 1, 1, 4, 6.93, 20.8, 1, 1, 4, 37.5, 38.34, 1, 1, 4, 39.68, 40.45, 1, 1, 4, 39, 42.88, 1, 1, 4, 34.89, 56.71, 1, 1, 4, 34.97, 61.5, 1, 1, 4, 43.18, 71.91, 1, 1, 4, 46.45, 74.57, 1, 1, 4, 63.41, 76.64, 1, 1, 4, 63.98, 73.94, 1, 1, 4, 59.82, 66.44, 1, 1, 4, 56.86, 65.68, 1, 1, 4, 70.24, 55.49, 1, 1, 4, 70.24, 51.42, 1, 1, 4, 63.2, 43.51, 1, 1, 4, 58.91, 42.54, 1, 1, 4, 56.02, 32.48, 1, 1, 4, 53.06, 28.85, 1, 1, 4, 20.13, -0.6, 1, 1, 4, 18.58, -5.56, 1, 1, 4, 7.92, -16.2, 1, 1, 4, -13.24, -16.2, 1, 1, 4, -19.76, -9.31, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48], "width": 90, "height": 93}}, "Dealer_06": {"Dealer_06": {"type": "mesh", "uvs": [0.00491, 0.88345, 0.10226, 0.98054, 0.27207, 0.99115, 0.46653, 0.95289, 0.54599, 0.82968, 0.54202, 0.78097, 0.90359, 0.41993, 0.90359, 0.37122, 0.87975, 0.24801, 1, 0.20503, 1, 0.06176, 0.80426, 0, 0.72082, 0, 0.59367, 0.06749, 0.51818, 0.20503, 0.63341, 0.35116, 0.63341, 0.39987, 0.28375, 0.63483, 0.07317, 0.69787, 0, 0.78383], "triangles": [8, 11, 10, 9, 8, 10, 8, 15, 13, 14, 13, 15, 12, 11, 8, 8, 13, 12, 15, 8, 7, 16, 15, 7, 16, 7, 6, 5, 17, 16, 5, 16, 6, 0, 19, 18, 3, 17, 5, 3, 5, 4, 2, 1, 18, 17, 2, 18, 0, 18, 1, 3, 2, 17], "vertices": [1, 5, -19.62, -3.6, 1, 1, 5, -12.31, -13.7, 1, 1, 5, 0.42, -14.8, 1, 1, 5, 15.01, -10.82, 1, 1, 5, 20.97, 1.99, 1, 1, 5, 20.67, 7.06, 1, 1, 5, 47.79, 44.61, 1, 1, 5, 47.79, 49.67, 1, 1, 5, 46, 62.49, 1, 1, 5, 55.02, 66.96, 1, 1, 5, 55.02, 81.86, 1, 1, 5, 40.34, 88.28, 1, 1, 5, 34.08, 88.28, 1, 1, 5, 24.54, 81.26, 1, 1, 5, 18.88, 66.96, 1, 1, 5, 27.52, 51.76, 1, 1, 5, 27.52, 46.69, 1, 1, 5, 1.3, 22.26, 1, 1, 5, -14.5, 15.7, 1, 1, 5, -19.98, 6.76, 1], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38], "width": 75, "height": 104}}, "Dealer_07": {"Dealer_07": {"type": "mesh", "uvs": [0, 0.91804, 0.2284, 1, 0.60264, 1, 0.86598, 0.94616, 0.90757, 0.82808, 1, 0.45136, 1, 0.3361, 0.93529, 0.23208, 0.54026, 0.05778, 0.17989, 0, 0, 0, 0, 0.07465, 0.26999, 0.25738, 0.47096, 0.35016, 0.60957, 0.39514, 0.57492, 0.44855, 0.36008, 0.64816, 0.22147, 0.68751, 0, 0.81965], "triangles": [11, 10, 9, 12, 9, 8, 11, 9, 12, 13, 12, 8, 14, 13, 8, 7, 14, 8, 14, 7, 6, 5, 14, 6, 5, 15, 14, 4, 15, 5, 2, 16, 15, 1, 0, 18, 15, 4, 2, 1, 17, 16, 2, 1, 16, 1, 18, 17, 4, 3, 2], "vertices": [1, 6, -19.91, -6.34, 1, 1, 6, -10.09, -15.03, 1, 1, 6, 6, -15.03, 1, 1, 6, 17.32, -9.32, 1, 1, 6, 19.11, 3.2, 1, 1, 6, 23.09, 43.13, 1, 1, 6, 23.09, 55.34, 1, 1, 6, 20.3, 66.37, 1, 1, 6, 3.32, 84.85, 1, 1, 6, -12.18, 90.97, 1, 1, 6, -19.91, 90.97, 1, 1, 6, -19.91, 83.06, 1, 1, 6, -8.3, 63.69, 1, 1, 6, 0.34, 53.85, 1, 1, 6, 6.3, 49.09, 1, 1, 6, 4.81, 43.42, 1, 1, 6, -4.43, 22.27, 1, 1, 6, -10.39, 18.09, 1, 1, 6, -19.91, 4.09, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36], "width": 43, "height": 106}}, "Dealer_08": {"Dealer_08": {"type": "mesh", "uvs": [0, 0, 1, 0, 1, 1, 0, 1], "triangles": [3, 0, 1, 3, 1, 2], "vertices": [-32.94, 79.04, 41.06, 79.04, 41.06, 46.04, -32.94, 46.04], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 74, "height": 33}}, "xinmoidatcuoc": {"xinmoidatcuoc": {"type": "mesh", "uvs": [0, 1, 1, 1, 1, 0, 0, 0], "triangles": [0, 3, 2, 0, 2, 1], "vertices": [-274.03, 47.05, -36.03, 47.05, -36.03, 99.05, -274.03, 99.05], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 238, "height": 52}}}}, "animations": {"Chop mat": {"slots": {"Dealer_03": {"attachment": [{"time": 0, "name": null}]}, "Dealer_04": {"attachment": [{"time": 0, "name": null}]}, "Dealer_05": {"attachment": [{"time": 0, "name": null}]}, "Dealer_06": {"attachment": [{"time": 0, "name": null}]}, "Dealer_07": {"attachment": [{"time": 0, "name": null}]}, "Dealer_08": {"attachment": [{"time": 0.1333, "name": "Dealer_08"}, {"time": 0.2667, "name": null}]}, "xinmoidatcuoc": {"attachment": [{"time": 0, "name": null}]}}, "deform": {"default": {"xinmoidatcuoc": {"xinmoidatcuoc": [{"time": 0.6667}]}}}}, "Smile": {"slots": {"Dealer_03": {"attachment": [{"time": 0, "name": null}]}, "Dealer_04": {"attachment": [{"time": 0, "name": null}]}, "Dealer_05": {"attachment": [{"time": 0, "name": null}]}, "Dealer_06": {"attachment": [{"time": 0, "name": null}]}, "Dealer_07": {"attachment": [{"time": 0, "name": null}]}, "Dealer_08": {"attachment": [{"time": 1.4333, "name": "Dealer_08"}, {"time": 1.6, "name": null}]}, "xinmoidatcuoc": {"color": [{"time": 0, "color": "ffffff00"}]}}, "deform": {"default": {"Dealer_01": {"Dealer_01": [{"time": 0.5}, {"time": 0.7333, "offset": 68, "vertices": [-0.334, 0, 0, 0, 0.334, 0, -1.14398, 1.00799, 0, 0, 1.36298, 1.09498, -0.09, 0.27, 0.045, 0.27, -0.68699, 0.31999, -0.18, 0.18, 0, 0.09, 0, 0.18, -1.27698, -0.136, 1.00099, -0.13399, 0, 0, -0.069, -0.82799, 0.069, -0.20699, 0, -0.069, 0, 0, 0, 0, 0, 0.345, 0.069, 0.48299, 0, 0.68999, 0, 0.62099, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.069, 0.621, -0.069, 0.96599, 0, 0.55199, 0, 0.276], "curve": "stepped"}, {"time": 1.2333, "offset": 68, "vertices": [-0.334, 0, 0, 0, 0.334, 0, -1.14398, 1.00799, 0, 0, 1.36298, 1.09498, -0.09, 0.27, 0.045, 0.27, -0.68699, 0.31999, -0.18, 0.18, 0, 0.09, 0, 0.18, -1.27698, -0.136, 1.00099, -0.13399, 0, 0, -0.069, -0.82799, 0.069, -0.20699, 0, -0.069, 0, 0, 0, 0, 0, 0.345, 0.069, 0.48299, 0, 0.68999, 0, 0.62099, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.069, 0.621, -0.069, 0.96599, 0, 0.55199, 0, 0.276]}, {"time": 1.7333, "curve": "stepped"}, {"time": 2}]}}}}, "animation": {"slots": {"Dealer_02": {"color": [{"time": 0.1333, "color": "ffffff00"}, {"time": 2.2, "color": "ffffffff"}], "attachment": [{"time": 0.2333, "name": null}, {"time": 2.2, "name": "Dealer_02"}]}, "Dealer_03": {"color": [{"time": 0.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1, "color": "ffffff00"}, {"time": 2.1333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}, {"time": 0.1333, "name": "Dealer_03"}, {"time": 2.2, "name": null}]}, "Dealer_04": {"color": [{"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.9667, "color": "ffffff00"}, {"time": 2, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}, {"time": 0.2333, "name": "Dealer_04"}, {"time": 2.1333, "name": null}]}, "Dealer_05": {"color": [{"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.8333, "color": "ffffff00"}, {"time": 1.8667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}, {"time": 0.3667, "name": "Dealer_05"}, {"time": 2, "name": null}]}, "Dealer_06": {"color": [{"time": 0.6, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7333, "color": "ffffff00"}, {"time": 1.7667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}, {"time": 0.4667, "name": "Dealer_06"}, {"time": 1.8667, "name": null}]}, "Dealer_07": {"color": [{"time": 2.2, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.6, "name": "Dealer_07"}, {"time": 1.7667, "name": null}]}, "xinmoidatcuoc": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00"}]}}, "bones": {"Dealer2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1333, "angle": 17.57, "curve": "stepped"}, {"time": 1.5333, "angle": 17.57}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1333, "x": 1.68, "y": 0, "curve": "stepped"}, {"time": 1.5333, "x": 1.68, "y": 0}, {"time": 1.6667, "x": 0, "y": 0}]}, "Dealer3": {"rotate": [{"time": 0.1333, "angle": 0}, {"time": 0.2, "angle": 12.91, "curve": "stepped"}, {"time": 1.4667, "angle": 12.91}, {"time": 1.5333, "angle": 0, "curve": "stepped"}, {"time": 2.1333, "angle": 0}, {"time": 2.1667, "angle": -7.2}]}, "Dealer4": {"rotate": [{"time": 0.2333, "angle": 0}, {"time": 0.3333, "angle": 15.96, "curve": "stepped"}, {"time": 1.3333, "angle": 15.96}, {"time": 1.4333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}, {"time": 2.1, "angle": -12.49}], "translate": [{"time": 0.2333, "x": 0, "y": 0}, {"time": 0.3333, "x": 0, "y": 1.01, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 1.01}, {"time": 1.4333, "x": 0, "y": 0}]}, "Dealer5": {"rotate": [{"time": 0.3667, "angle": 0}, {"time": 0.4667, "angle": 10.94, "curve": "stepped"}, {"time": 1.2, "angle": 10.94}, {"time": 1.3, "angle": 0, "curve": "stepped"}, {"time": 1.8667, "angle": 0}, {"time": 1.9667, "angle": -15.44}], "translate": [{"time": 0.3667, "x": -0.5, "y": 0}]}, "Dealer6": {"rotate": [{"time": 0.4667, "angle": 0}, {"time": 0.5667, "angle": 15.44, "curve": "stepped"}, {"time": 1.1, "angle": 15.44}, {"time": 1.2, "angle": 0}, {"time": 1.7667, "angle": 3.86}, {"time": 1.8333, "angle": -8.7}]}, "Dealer7": {"rotate": [{"time": 0.6, "angle": -7.25}, {"time": 0.7, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}, {"time": 1.7333, "angle": -8.18}]}}, "deform": {"default": {"Dealer_03": {"Dealer_03": [{"time": 0.2}, {"time": 2.1333, "vertices": [-0.1233, -0.53806, -0.1233, -0.53806, -0.1233, -0.53806, -0.1233, -0.53806, -0.1233, -0.53806, -0.1233, -0.53806, -0.1233, -0.53806, -0.1233, -0.53806, -0.1233, -0.53806, -0.1233, -0.53806, -0.1233, -0.53806, -0.1233, -0.53806, -0.1233, -0.53806, -0.1233, -0.53806, -0.1233, -0.53806, -0.1233, -0.53806, -0.1233, -0.53806]}, {"time": 2.1667, "vertices": [-0.10339, -0.91418, -0.10339, -0.91418, -0.10339, -0.91418, -0.10339, -0.91418, -0.10339, -0.91418, -0.10339, -0.91418, -0.10339, -0.91418, -0.10339, -0.91418, -0.10339, -0.91418, -0.10339, -0.91418, -0.10339, -0.91418, -0.10339, -0.91418, -0.10339, -0.91418, -0.10339, -0.91418, -0.10339, -0.91418, -0.10339, -0.91418, -0.10339, -0.91418]}, {"time": 2.2, "curve": "stepped"}, {"time": 2.3333}]}, "Dealer_05": {"Dealer_05": [{"time": 0.3667, "vertices": [-0.81001, 1.63199, -0.81001, 1.63199, -0.81001, 1.63199, -0.81001, 1.63199, -0.81001, 1.63199, -0.81001, 1.63199, -0.81001, 1.63199, -0.81001, 1.63199, -0.81001, 1.63199, -0.81001, 1.63199, -0.81001, 1.63199, -0.81001, 1.63199, -0.81001, 1.63199, -0.81001, 1.63199, -0.81001, 1.63199, -0.81001, 1.63199, -0.81001, 1.63199, -0.81001, 1.63199, -0.81001, 1.63199, -0.81001, 1.63199, -0.81001, 1.63199, -0.81001, 1.63199, -0.81001, 1.63199, -0.81001, 1.63199, -0.81001, 1.63199]}, {"time": 0.4333, "vertices": [-0.1532, 1.45653, -0.1532, 1.45653, -0.1532, 1.45653, -0.1532, 1.45653, -0.1532, 1.45653, -0.1532, 1.45653, -0.1532, 1.45653, -0.1532, 1.45653, -0.1532, 1.45653, -0.1532, 1.45653, -0.1532, 1.45653, -0.1532, 1.45653, -0.1532, 1.45653, -0.1532, 1.45653, -0.1532, 1.45653, -0.1532, 1.45653, -0.1532, 1.45653, -0.1532, 1.45653, -0.1532, 1.45653, -0.1532, 1.45653, -0.1532, 1.45653, -0.1532, 1.45653, -0.1532, 1.45653, -0.1532, 1.45653, -0.1532, 1.45653]}, {"time": 0.4667}, {"time": 1.8667, "vertices": [0.2793, 1.44521, 0.2793, 1.44521, 0.2793, 1.44521, 0.2793, 1.44521, 0.2793, 1.44521, 0.2793, 1.44521, 0.2793, 1.44521, 0.2793, 1.44521, 0.2793, 1.44521, 0.2793, 1.44521, 0.2793, 1.44521, 0.2793, 1.44521, 0.2793, 1.44521, 0.2793, 1.44521, 0.2793, 1.44521, 0.2793, 1.44521, 0.2793, 1.44521, 0.2793, 1.44521, 0.2793, 1.44521, 0.2793, 1.44521, 0.2793, 1.44521, 0.2793, 1.44521, 0.2793, 1.44521, 0.2793, 1.44521, 0.2793, 1.44521]}, {"time": 1.9667, "vertices": [-0.81, 1.07999, -0.81, 1.07999, -0.81, 1.07999, -0.81, 1.07999, -0.81, 1.07999, -0.81, 1.07999, -0.81, 1.07999, -0.81, 1.07999, -0.81, 1.07999, -0.81, 1.07999, -0.81, 1.07999, -0.81, 1.07999, -0.81, 1.07999, -0.81, 1.07999, -0.81, 1.07999, -0.81, 1.07999, -0.81, 1.07999, -0.81, 1.07999, -0.81, 1.07999, -0.81, 1.07999, -0.81, 1.07999, -0.81, 1.07999, -0.81, 1.07999, -0.81, 1.07999, -0.81, 1.07999]}]}, "Dealer_07": {"Dealer_07": [{"time": 0.6, "offset": 1, "vertices": [-1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197], "curve": "stepped"}, {"time": 1.7333, "offset": 1, "vertices": [-1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197, 0, -1.75197]}, {"time": 1.7667}]}}}}}}