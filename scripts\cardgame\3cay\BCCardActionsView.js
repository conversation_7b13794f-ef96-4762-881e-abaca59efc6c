/*
 * Generated by BeChicken
 * on 7/25/2019
 * version v1.0
 */
const TWEEN = cc.tween;
(function () {
    cc.BCCardActionsView = cc.Class({
        extends: cc.Component,
        properties: {
            nodeCards: [cc.SpriteFrame],
            groupCardFirst: [cc.Node],
            groupCardSecond: [cc.Node],
            groupCardThird: [cc.Node],
            //cardAtlas: cc.SpriteAtlas,
            //Nan bai
            fatBackCard: cc.Sprite,
            fatFirstCard: cc.Sprite,
            fatSecondCard: cc.Sprite,
            fatThirdCard: cc.Sprite,
            //layout Nan
            layoutNan: cc.Node,
            lbTimeNan: cc.Label,
            //node chicken fet
            nodeChickenFet: cc.Node,
            //nodePlayers: [cc.BCPlayer]
        },
        onLoad: function () {
            cc.BCController.getInstance().setBCCardActionsView(this);
            this.usersCard = this.initUserCard();
            this.currUserCard = null;

            //Map chat bai
            let cardsBich = [];
            let cardsTep = [];
            let cardsCo = [];
            let cardsRo = [];
            for(let i = 0; i < this.nodeCards.length; i++) {
                if(i <= 8 ){
                    cardsBich.push(this.nodeCards[i]);
                }
                if(9 <= i && i <= 17 ) {
                    cardsTep.push(this.nodeCards[i]);
                }
                if(18 <= i && i <= 26) {
                    cardsCo.push(this.nodeCards[i]);
                }
                if(27 <= i && i <= 35) {
                    cardsRo.push(this.nodeCards[i]);
                }
            }
            cc.BCController.getInstance().setCardsSuite(cardsBich, cc.BCCardSuite.BICH);
            cc.BCController.getInstance().setCardsSuite(cardsTep, cc.BCCardSuite.TEP);
            cc.BCController.getInstance().setCardsSuite(cardsCo, cc.BCCardSuite.CO);
            cc.BCController.getInstance().setCardsSuite(cardsRo, cc.BCCardSuite.RO);

            this.cards = [...this.groupCardFirst, ...this.groupCardSecond, ...this.groupCardThird];

            this.nodesCardActive = null;

        },
        getRandomFromTo: function (min, max) {
            return Math.random() * (max - min) + min;
        },
        //Khoi tao bai cua user
        initUserCard: function () {
            let userCard = [];
            for (let i = 0; i < 7; i++) {
                userCard.push({
                    cardFirst: this.groupCardFirst[i],
                    cardSecond: this.groupCardSecond[i],
                    cardThird: this.groupCardThird[i]
                });
            }
            return userCard;
        },

        //Thu bai
        collectCards: function () {
            for (let i = 0; i < this.cards.length; i++) {
                let node = this.cards[i];
                node.opacity = 0;
            }
        },
    })
}).call(this);