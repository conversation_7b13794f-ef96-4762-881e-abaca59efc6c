/*
 * Generated by BeChicken
 * on 10/3/2019
 * version v1.0
 */
cc.Class({
    "extends": cc.Component,
    properties: {
        animation: cc.Animation,
        nodeOffset: cc.Node,

        spriteSound: cc.Sprite,

        sfSounds: [cc.SpriteFrame], //0=on, 1=off
    },

    onLoad: function () {
        this.openPopup = false;
    },

    start: function () {
        //Check Sound
        this.sound = cc.Tool.getInstance().getItem("@Sound").toString() === 'true';
        this.music = cc.Tool.getInstance().getItem("@Music").toString() === 'true';

        this.spriteSound.spriteFrame = this.sound ? this.sfSounds[0] : this.sfSounds[1];

        cc.AudioController.getInstance().enableSound(this.sound);
        cc.AudioController.getInstance().enableMusic(this.music);
    },

    openSettingClicked: function () {
        if (this.openPopup == false) {
            this.openPopup = true;
            this.animation.play('openSettingMenu');
        } else {
            this.openPopup = false;
            this.animation.play('closeSettingMenu');
        }
    },

    closeSettingClicked: function () {
        this.animation.play('closeSettingMenu');
    },

    soundClicked: function () {
        this.sound = !this.sound;
        cc.Tool.getInstance().setItem("@Sound", this.sound);
        this.spriteSound.spriteFrame = this.sound ? this.sfSounds[0] : this.sfSounds[1];
        cc.AudioController.getInstance().enableSound(this.sound);
    },

    musicClicked: function () {
        this.music = !this.music;
        cc.Tool.getInstance().setItem("@Music", this.music);
        this.spriteMusic.spriteFrame = this.music ? this.sfMusics[0] : this.sfMusics[1];
        cc.AudioController.getInstance().enableMusic(this.music);
    },

});