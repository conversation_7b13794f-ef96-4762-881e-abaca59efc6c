/*
 * Generated by BeChicken
 * on 10/2/2019
 * version v1.0
 */
(function () {
    var BacaratPopupController;

    BacaratPopupController = (function () {
        var instance;

        function BacaratPopupController() {

        }

        instance = void 0;

        BacaratPopupController.getInstance = function () {
            if (instance === void 0) {
                instance = this;
            }
            return instance.prototype;
        };

        BacaratPopupController.prototype.setPopupView = function (popupView) {
            return this.bacaratPopupView = popupView;
        };

        BacaratPopupController.prototype.createTopView = function () {
            return this.bacaratPopupView.createTopView();
        };

        BacaratPopupController.prototype.destroyTopView = function () {
            return this.bacaratPopupView.destroyTopView();
        };

        BacaratPopupController.prototype.createHelpView = function () {
            return this.bacaratPopupView.createHelpView();
        };

        BacaratPopupController.prototype.destroyHelpView = function () {
            return this.bacaratPopupView.destroyHelpView();
        };

        BacaratPopupController.prototype.createHistoryView = function () {
            return this.bacaratPopupView.createHistoryView();
        };

        BacaratPopupController.prototype.destroyHistoryView = function () {
            return this.bacaratPopupView.destroyHistoryView();
        };

        BacaratPopupController.prototype.createGroupUserView = function () {
            return this.bacaratPopupView.createGroupUserView();
        };
        BacaratPopupController.prototype.destroyGroupUserView = function () {
            return this.bacaratPopupView.destroyGroupUserView();
        };
        return BacaratPopupController;

    })();

    cc.BacaratPopupController = BacaratPopupController;

}).call(this);