/*
 * Generated by BeChicken
 * on 9/27/2019
 * version v1.0
 */
cc.Class({
    extends: cc.Component,
    properties: {
        itemTemplate: cc.Node,
        layoutParent: cc.Node,
    },
    onLoad: function () {
        cc.BacaratController.getInstance().setCatCauView(this);
        this.nodeRootx = -10;
        this.nodeRooty = -38;
        this.stepX = -19;
        this.stepY = 19;
        this.lengthNode = 5;
        //this.node.active = false;
        this.isActive = false;
        this.itemPool = new cc.NodePool();
        for (let i = 0; i < 100; i++) {
            this.itemPool.put(cc.instantiate(this.itemTemplate));
        }

    },
    activeGraph: function (isActive) {
        this.node.active = isActive;
        this.isActive = isActive;
    },
    initListCatCau: function (data) {
        if (data.length === 0) {
            return;
        }
        try {
            this.clearList();
        } catch (e) {
            console.log(e);
        }

        //convert matrix
        let col = 0;
        let dataCol = [];
        dataCol[col] = [];
        dataCol[col].push(data[0]);
        for (let i = 1; i < data.length; i++) {
            if (dataCol[col][0].BigGateIDWin != data[i].BigGateIDWin || dataCol[col].length === this.lengthNode) {
                col++;
                dataCol[col] = [];
            }
            dataCol[col].push(data[i]);
        }
        this.draw(dataCol);
        this.node.active = this.isActive;
    },
    draw: function (data) {
        let width = data.length * 19;
        width = (width < 420) ? 420 : width;
        this.layoutParent.width = width;
        this.layoutParent.height = 100;
        data.map((dataCol, indexCol) => {
            var nodeRootyTemp = this.nodeRooty + ((5-dataCol.length) * this.stepY);
            dataCol.map((gate, indexNode) => {
                let posX = this.stepX * indexCol + this.nodeRootx;
                let posY = nodeRootyTemp + indexNode * this.stepY;
                let node = null;
                if (this.itemPool.size() > 0) {
                    node = this.itemPool.get();
                } else {
                    node = cc.instantiate(this.itemTemplate);
                }
                let itemSoiCau = node.getComponent(cc.ItemSoiCau);


                itemSoiCau.setSpiteFrameItem(gate.BigGateIDWin, gate.IsPlayerPair, gate.IsBankerPair);

                itemSoiCau.setScore(gate.HandValue);
                node.parent = this.layoutParent;
                node.position = cc.v2(posX, posY);
            }, this);
        }, this);
    },
    // clearList: function () {
    //     this.layoutParent.removeAllChildren(true);
    // }
    clearList: function () {
        let nodes = this.layoutParent.children;
        if (nodes.length > 0) {
            nodes.map(item => {
                this.itemPool.put(item);
            }, this);
        }

        this.layoutParent.removeAllChildren(true);
    },
    onDestroy: function () {
        try {
            this.itemPool.clear();
        } catch (e) {

        }
    }
});