/*
 * Generated by BeChicken
 * on 9/27/2019
 * version v1.0
 */
cc.Class({
    extends: cc.Component,
    properties: {
        itemTemplate: cc.Node,
        layoutParent: cc.Node,
    },
    onLoad: function () {
        cc.BacaratController.getInstance().setSoiCauView(this);
        this.isActive = false;
        this.itemPool = new cc.NodePool();
        for (let i = 0; i < 100; i++) {
            this.itemPool.put(cc.instantiate(this.itemTemplate));
        }
        this.layoutParent.width = 380;
        //this.clearList();
        //this.node.active = false;
    },
    activeGraph: function (isActive) {
        this.node.active = isActive;
        this.isActive = isActive;
    },
    initListSoiCau: function (data) {
        if (data.length === 0) {
            return;
        }
        try {
            this.clearList();
        } catch (e) {
            console.log(e);
        }

        data.map(gate => {
            let item = null;
            if(this.itemPool.size() > 0) {
                item = this.itemPool.get();
            }else {
                item = cc.instantiate(this.itemTemplate);
            }
            item.getComponent(cc.ItemSoiCau).setSpiteFrameItem(gate.BigGateIDWin, gate.IsPlayerPair, gate.IsBankerPair);
            item.parent = this.layoutParent;
        }, this);
        this.node.active = this.isActive;
    },
    clearList: function () {
        let nodes = this.layoutParent.children;
        if(nodes.length > 0) {
            nodes.map(item => {
                this.itemPool.put(item);
            }, this);
        }
        this.layoutParent.removeAllChildren(true);
    },
    onDestroy: function () {
        try {
            this.itemPool.clear();
        } catch (e) {

        }
    }
});