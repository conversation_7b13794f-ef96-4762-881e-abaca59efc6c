/*
 * Generated by BeChicken
 * on 10/11/2019
 * version v1.0
 */
(function () {
    cc.BacaratGroupItem = cc.Class({
        "extends": cc.Component,
        properties: {
            // sprite: cc.Sprite,
            avatar: cc.Avatar,
            lbSId: cc.Label,
            lbNickName: cc.Label,
            lbBalance: cc.Label,
            lbSTT: cc.Label,
        },
        updateItem: function (item, itemID) {
            let accountInfo = item.Account;
            // this.sprite.enabled = itemID % 2 !== 0;
            var avatarID = accountInfo.Avatar;
            if (avatarID <= 0) {
                avatarID = 1;
            }

            //set avatar
            this.lbSTT.string = itemID+1;
            this.lbSId.string = cc.Config.getInstance().getServiceNameNoFormat(accountInfo.ServiceID);
            this.avatar.setAvatar(cc.AccountController.getInstance().getAvatarImage(avatarID));
            this.lbNickName.string = accountInfo.NickName;
            this.lbBalance.string = cc.Tool.getInstance().formatNumber(accountInfo.Balance);
            this.item = item;
            this.itemID = itemID;
        },
    });
}).call(this);
