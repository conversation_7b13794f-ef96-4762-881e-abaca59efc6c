/*
 * Generated by BeChicken
 * on 10/11/2019
 * version v1.0
 */
(function () {
    cc.BacaratGroupUserView = cc.Class({
        "extends": cc.PopupBase,
        properties: {
            groupUserListView: cc.BacaratGroupUserListView,
        },

        onLoad: function () {
            this.animation = this.node.getComponent(cc.Animation);
            this.node.zIndex = cc.NoteDepth.POPUP_TAIXIU;
        },

        onEnable: function () {
            var self = this;
            var delay = 0.2;
            cc.director.getScheduler().schedule(function () {
                self.getGroupUser();
            }, this, 1, 0, delay, false);

            this.animation.play('openPopup');
        },

        getGroupUser: function () {
            var getGroupUserCommand = new cc.BacaratGroupUserCommand;
            getGroupUserCommand.execute(this);
        },

        onGetGroupUserResponse: function (response) {
            var list = response;
            //var list = slotsHistoryListData;
            if (list !== null && list.length > 0) {
                this.groupUserListView.resetList();
                this.groupUserListView.initialize(list);
            }
        },

        closeClicked: function () {
            this.groupUserListView.resetList();
            this.animation.play('closePopup');
            var self = this;
            var delay = 0.12;
            cc.director.getScheduler().schedule(function () {
                self.animation.stop();
                cc.BacaratPopupController.getInstance().destroyGroupUserView();
            }, this, 1, 0, delay, false);
        },
    });
}).call(this);