/*
 * Generated by BeChicken
 * on 9/27/2019
 * version v1.0
 */
(function () {
    cc.BacaratAssetView = cc.Class({
        extends: cc.Component,
        properties: {
            //SF Vien khi chon bai
            sfBorderCard: cc.SpriteFrame,
            sfPrefabChip: [cc.Prefab],//0:1K,1:5K,2:10K,3:50k,4:100K,5:500K,6:1M
            /*
            *   BICH: 0,
                TEP: 13,
                RO: 26,
                CO: 39,
            */
            sfSuitCo: [cc.SpriteFrame],
            sfSuitRo: [cc.SpriteFrame],
            sfSuitTep: [cc.SpriteFrame],
            sfSuitBich: [cc.SpriteFrame],
            sfCardBack: cc.SpriteFrame,

            sfPlayers: [cc.SpriteFrame],
            sfBankers: [cc.SpriteFrame],
            sfTies: [cc.SpriteFrame],

            colorWhite: cc.Color,
            colorYellow: cc.Color,
            colorRed: cc.Color,
        },
        onLoad: function () {
            cc.BacaratController.getInstance().setAssetView(this);
            this.chip1kPool = new cc.NodePool();
            this.chip5kPool = new cc.NodePool();
            this.chip10kPool = new cc.NodePool();
            this.chip100kPool = new cc.NodePool();
            this.chip500kPool = new cc.NodePool();
        },
        onDestroy: function () {
            this.clearPools();
        },
        clearPools: function () {
            try {
                //Clear pool
                this.chip1kPool.clear();
                this.chip5kPool.clear();
                this.chip10kPool.clear();
                this.chip100kPool.clear();
                this.chip500kPool.clear();
            } catch (e) {
                console.log(e);
            }
        },

        getSfBorderCard: function () {
            return this.sfBorderCard;
        },
        //Lay SpriteFrame bai sau
        getSfCardBack: function () {
            return this.sfCardBack;
        },
        //Lay sprite bai
        getSpriteCard: function (number, suite) {
            let getSuit = null;
            number = parseInt(number);
            switch (suite) {
                case cc.MB_Card.CO:
                    getSuit = this.sfSuitCo;
                    break;
                case cc.MB_Card.RO:
                    getSuit = this.sfSuitRo;
                    break;
                case cc.MB_Card.TEP:
                    getSuit = this.sfSuitTep;
                    break;
                case cc.MB_Card.BICH:
                    getSuit = this.sfSuitBich;
                    break;
            }
            if (number === -1) {
                return null;
            }
            return getSuit[number];
        },

        //Tao gia tri bai
        getCardValue: function (cardNumber, suit) {
            // gia tri bai
            // chay tu 3 -> 2
            // (gia tri bai - 3)*4 + suit / 13
            // 8 Bich = (8-3)*4 + 0 = 20
            // 8 tep = (8-3)*4 + 13/13 = 21
            return (cardNumber - 3) * 4 + suit / 13;
        },

        getCardValueByNumber: function (value) {
            // get the card value in the suite
            let cardNumber = (value - 1) % 13 + 1;
            let suite = value - cardNumber;
            return this.getSpriteCard(cardNumber - 1, suite);
        },

        getSpriteByName: function (nameSprite) {
            return this.spriteAtlasNotify.getSpriteFrame(nameSprite);
        },

        createChip: function (type) {
            let nodeChip = null;
            switch (type) {
                case cc.BacaratMapChip['1K']:
                    if (this.chip1kPool.size() > 0) {
                        nodeChip = this.chip1kPool.get();
                    } else {
                        nodeChip = cc.instantiate(this.sfPrefabChip[0]);
                    }
                    break;
                case cc.BacaratMapChip['5K']:
                    if (this.chip5kPool.size() > 0) {
                        nodeChip = this.chip5kPool.get();
                    } else {
                        nodeChip = cc.instantiate(this.sfPrefabChip[1]);
                    }
                    break;
                case cc.BacaratMapChip['10K']:
                    if (this.chip10kPool.size() > 0) {
                        nodeChip = this.chip10kPool.get();
                    } else {
                        nodeChip = cc.instantiate(this.sfPrefabChip[2]);
                    }
                    break;
                case cc.BacaratMapChip['100K']:
                    if (this.chip100kPool.size() > 0) {
                        nodeChip = this.chip100kPool.get();
                    } else {
                        nodeChip = cc.instantiate(this.sfPrefabChip[3]);
                    }
                    break;
                case cc.BacaratMapChip['500K']:
                    if (this.chip500kPool.size() > 0) {
                        nodeChip = this.chip500kPool.get();
                    } else {
                        nodeChip = cc.instantiate(this.sfPrefabChip[4]);
                    }
                    break;
            }

            //BECHICKEN?
            //BacaratAssetView.js:145 Uncaught TypeError: Cannot read property 'setScale' of null
            if (nodeChip)
                nodeChip.setScale(0.5, 0.5);
            return nodeChip;
        },
        putChipToPool: function (nodeChip, betValue) {
            switch (betValue) {
                case 1000:
                    this.chip1kPool.put(nodeChip);
                    break;
                case 5000:
                    this.chip5kPool.put(nodeChip);
                    break;
                case 10000:
                    this.chip10kPool.put(nodeChip);
                    break;
                case 100000:
                    this.chip100kPool.put(nodeChip);
                    break;
                case 500000:
                    this.chip500kPool.put(nodeChip);
                    break;
            }
        },
        getColorType: function (type) {
            switch (type) {
                case cc.BacaratColor.WHITE:
                    return this.colorWhite;
                case cc.BacaratColor.YELLOW:
                    return this.colorYellow;
                case cc.BacaratColor.RED:
                    return this.colorRed;
            }
        },

    });
}).call(this)
