/*
 * Generated by BeChicken
 * on 9/27/2019
 * version v1.0
 */
(function () {
    cc.BacaratCardItem = cc.Class({
        extends: cc.Component,
        properties: {
            //Gia tri quan bai
            ordinalValue: -1,
            cardNumber: -1,
            cardSuite: -1,
            animation: cc.Animation
        },
        onLoad: function () {
            this.duration = 0.5;
            this.spriteNode = this.node.getComponent(cc.Sprite);
            this.nodeBorder = new cc.Node('borderCard');
            this.nodeBorder.setScale(1.5, 1.5);
            this.spriteBorder = this.nodeBorder.addComponent(cc.Sprite);
            this.spriteBorder.spriteFrame = cc.BacaratController.getInstance().getSfBorderCard();
        },
        onDestroy: function () {
            try {
                this.node.stopAllActions();
            } catch (e) {

            }
        },
        //Reset trang thai node chuan bi chia bai
        prepareSlideCard: function () {
            this.node.position = cc.v2(0, 145);
            this.node.setScale(0.3, 0.3);
            this.node.rotation = 0;
            this.node.stopAllActions();
        },
        // Set Spirte frame la bai up
        initCard: function () {
            if (!this.spriteNode) {
                this.spriteNode = this.node.getComponent(cc.Sprite);
            }
            this.spriteNode.spriteFrame = cc.BacaratController.getInstance().getSfCardBack();
        },
        // Hien thi card
        onShowCard: function () {
            this.spriteNode.spriteFrame = cc.BacaratController.getInstance().getCardValueByNumber(this.ordinalValue);
        },
        animationShowCard: function (hasBorder) {
            if (hasBorder) {
                this.addBorderCard();
            }
            this.animation.play('open-card');
        },
        addBorderCard: function () {
            this.nodeBorder.parent = this.node;
            this.nodeBorder.setAnchorPoint(cc.v2(0.7, 0.3));
        },
        removeBorderCard: function () {
            this.node.removeAllChildren();
        },
        forceShowCard: function (position, rotate) {
            if (this.ordinalValue != -1) {
                this.node.active = true;
                this.spriteNode.spriteFrame = cc.BacaratController.getInstance().getCardValueByNumber(this.ordinalValue);
                this.node.position = position;
                this.node.setScale(0.55, 0.55);
                if (rotate) {
                    this.node.rotation = rotate;
                }
            }

        },
        updateOrdinal: function (ordinal) {
            this.ordinalValue = (ordinal != 0) ? ordinal : -1;
        },
        initValue: function (ordinal) {
            this.ordinalValue = (ordinal != 0) ? ordinal : -1;
            this.initCard();
            this.reset();
        },
        // Reset bai ve trang thai ban dau
        reset: function () {
            this.node.active = false;
            this.prepareSlideCard();
        },
        moveTo: function (endPosition) {
            this.node.active = true;
            // this.node.opacity = 200;
            let scaleTo = cc.scaleTo(this.duration, 0.55, 0.55);
            let actionMove = cc.moveTo(this.duration, endPosition);
            this.node.runAction(cc.spawn(actionMove, scaleTo));
        },

        setPosition: function (endPosition) {
            this.node.active = true;
            this.node.setScale(cc.v2(0.55, 0.55));
            this.node.position = endPosition;

        },
        moveToEnd: function (endPosition) {
            if (this.node) {
                this.node.opacity = 100;

                var action = cc.moveTo(0.5, endPosition);
                action.easing(cc.easeOut(1.0));

                var callback = cc.callFunc(this.moveToEndFinished, null, this.node);

                this.node.runAction(cc.sequence(action, callback));
            }
        },
        rotateCard: function (rotate) {
            this.removeBorderCard();
            let action = cc.rotateTo(this.duration, rotate);
            this.node.runAction(action);
        }

    });
}).call(this);
