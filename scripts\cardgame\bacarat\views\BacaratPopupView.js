/*
 * Generated by BeChicken
 * on 10/2/2019
 * version v1.0
 */
/*
 * Generated by BeChicken
 * on 6/10/2019
 * version v1.0
 */
(function () {
    cc.BacaratPopupView = cc.Class({
        "extends": cc.PopupViewBase,
        properties: {
            prefabGroupUser: cc.Prefab
        },
        onLoad: function () {
            cc.BacaratPopupController.getInstance().setPopupView(this);
        },
        createGroupUserView: function () {
            this.nodeGroupUser = this.createView(this.prefabGroupUser);
        },
        destroyGroupUserView: function () {
            if (this.nodeGroupUser)
                this.nodeGroupUser.destroy();
        },
    });
}).call(this);
