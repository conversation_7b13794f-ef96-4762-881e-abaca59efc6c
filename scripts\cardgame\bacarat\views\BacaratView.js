/*
 * Generated by BeChicken
 * on 9/27/2019
 * version v1.0
 */
var netConfig = require('NetConfig');
(function () {
    cc.BacaratView = cc.Class({
        extends: cc.Component,
        properties: {
            //nodeBtnSoiCau
            nodeSoiCau: cc.Node,

            //chat
            nodeParentChat: cc.Node,
            prefabChat: cc.Prefab,
        },

        onLoad: function () {
            cc.BacaratController.getInstance().setBacaratView(this);
            cc.ChatRoomController.getInstance().setHubView(this);
            cc.BacaratController.getInstance().initBetLog();
            cc.BacaratController.getInstance().setBetLogSession(1);

            this.hubName = cc.HubName.BaccaratHub;
            this.subDomainName = cc.SubdomainName.BACCARAT;
            this.interval = null;
            this.isActiveChat = false;
            this.lastTimeReconnect = (new Date()).getTime();
            this.currentState = -1;
            this.currentSessionId = -1;
            this.viewSoiCau = false;
            this.nodeSoiCauAnimation = this.nodeSoiCau.getComponent(cc.Animation);

            //id send playNow
            this.idPlayNow = 0;

            //check jonGame
            this.currAccId = null;

            var nodeChat = cc.instantiate(this.prefabChat);
            this.nodeParentChat.addChild(nodeChat);
            // nodeChat.x = 0;
            // nodeChat.y = 0;

            this.connectHub();
        },

        onEnable: function () {
            //this.updateBalance(cc.BalanceController.getInstance().getBalance());
        },
        updateBalance: function (balance) {
            //this.lbBalance.tweenValueto(balance);
        },
        onDestroy: function () {
            //Exit lobby
            this.sendRequestOnHub(cc.MethodHubName.EXIT_LOBBY);

            cc.LobbyJackpotController.getInstance().pauseUpdateJackpot(false);

            if (this.interval !== null) {
                clearInterval(this.interval);
            }
            if (this.BacaratHub)
                this.BacaratHub.disconnect();
            this.unscheduleAllCallbacks();
            cc.BacaratController.getInstance().setBacaratView(null);

            if (cc.sys.isNative) {
                cc.loader.releaseResDir('bacarat/prefabs');
                cc.loader.releaseResDir('bacarat/images');
            }
        },

        disconnectAndLogout: function () {
            if (this.BacaratHub) {
                this.BacaratHub.disconnect();
            }
            this.lastTimeReconnect = (new Date()).getTime();
        },

        connectHub: function () {
            var negotiateCommand = new cc.NegotiateCommand;
            negotiateCommand.execute(this, this.subDomainName);
        },

        reconnect: function () {
            this.lastTimeReconnect = (new Date()).getTime();
            this.BacaratHub.connect(this, this.hubName, this.connectionToken, true);
        },

        //data1 = amount
        //data2 = gate
        sendRequestOnHub: function (method, data1, data2) {
            switch (method) {
                case cc.MethodHubName.ENTER_LOBBY:
                    this.BacaratHub.enterLobby();
                    break;
                case cc.MethodHubName.EXIT_LOBBY:
                    this.BacaratHub.exitLobby();
                    break;
                case cc.MethodHubName.BET:
                    this.BacaratHub.bet(data1, data2);//betValue, betSide
                    break;
                case cc.MethodHubName.PLAY_NOW:
                    this.BacaratHub.playNow();
                    break;
                case cc.MethodHubName.SEND_MESSAGE:
                    this.BacaratHub.sendRoomMessage(data1);
                    break;
            }
        },

        onSlotsNegotiateResponse: function (response) {
            this.connectionToken = response.ConnectionToken;
            this.BacaratHub = new cc.Hub;
            this.BacaratHub.connect(this, this.hubName, response.ConnectionToken);
        },

        onHubMessage: function (response) {
            if (response.M !== undefined && response.M.length > 0) {
                let res = response.M;
                res.map(m => {
                    switch (m.M) {
                        //Thoat game
                        case cc.MethodHubOnName.PLAYER_LEAVE:
                            this.playerLeave(m.A);
                            break;
                        //Thong tin game
                        case cc.MethodHubOnName.SESSION_INFO:
                            // console.log("SESSION_INFO", m.A);
                            cc.BacaratController.getInstance().updateSessionInfo(m.A[0]);
                            //Cap nhat tong tien bet
                            cc.BacaratController.getInstance().updateTotalBet(m.A[0]);
                            break;
                        //Danh sach nguoi choi
                        case cc.MethodHubOnName.SUMMARY_PLAYER:
                            cc.BacaratController.getInstance().updatePlayersInGame(m.A[0]);
                            break;
                        //Thong tin game
                        case cc.MethodHubOnName.JOIN_GAME:
                            // console.log("JOIN_GAME", m.A);
                            cc.BacaratController.getInstance().updatePlayerInfor(m.A[0]);
                            break;
                        //Cap nhat danh sach player
                        case cc.MethodHubOnName.VIP_PLAYERS:
                            let dataPlayer = m.A[0];
                            if (dataPlayer.length > 0) {
                                cc.BacaratController.getInstance().updatePlayersUI(dataPlayer);
                            }

                            break;
                        //Lich su bet
                        case cc.MethodHubOnName.GAME_HISTORY:
                            // console.log("GAME_HISTORY", m.A);
                            cc.BacaratController.getInstance().initListSoiCau(m.A[0]);
                            cc.BacaratController.getInstance().initListCatCau(m.A[0]);
                            break;
                        //Thong tin bet cua player
                        case cc.MethodHubOnName.BET_OF_ACCOUNT:
                            cc.BacaratController.getInstance().updateBetOfAccount(m.A[0]);
                            break;
                        //Bet thanh cong
                        case cc.MethodHubOnName.BET_SUCCESS:
                            //Push betValue vao betLog
                            let sessionID = cc.BacaratController.getInstance().getBetLogSession();

                            cc.BacaratController.getInstance().setBetLog({
                                sessionID: sessionID,
                                value: m.A[0].BetValue,
                                betSide: m.A[0].BetSide
                            });
                            //Cap nhat balance
                            // cc.BacaratController.getInstance().updateBalance(m.A[1]);
                            cc.BacaratController.getInstance().updateBalanceCurrPlayer(m.A[1]);
                            cc.BalanceController.getInstance().updateRealBalance(m.A[1]);
                            cc.BacaratController.getInstance().updateTotalUserBetSide(m.A[0].BetSide, m.A[0].SumaryBet);
                            //Move Chip
                            //cc.BacaratController.getInstance().moveChipBet(m.A[0].BetValue, m.A[0].BetSide, cc.BacaratChipOf.PLAYER, cc.LoginController.getInstance().getUserId());
                            cc.BacaratController.getInstance().moveChipBet(m.A[0].BetValue, m.A[0].BetSide, cc.BacaratChipOf.PLAYER, m.A[0].AccountID);
                            break;
                        //Thong tin bet cac cua
                        case cc.MethodHubOnName.BET_SESSION:
                            cc.BacaratController.getInstance().updateChipForBetSession(m.A[0]);
                            break;
                        //Nguoi choi bet
                        case cc.MethodHubOnName.BET_USER:
                            if (m.A[0] != cc.LoginController.getInstance().getUserId()) {
                                //Update chip player
                                cc.BacaratController.getInstance().updateBalancePlayer(m.A);
                                //Move Chip
                                cc.BacaratController.getInstance().moveChipBet(m.A[1], m.A[2], cc.BacaratChipOf.USERS, m.A[0]);
                            }
                            break;

                        //Ket qua
                        case cc.MethodHubOnName.WIN_RESULT:
                            //KO hien thi luon khi co winResult
                            cc.BacaratController.getInstance().setWinResult(m.A[0]);
                            // cc.BacaratController.getInstance().winResult(m.A[0]);
                            //cc.BacaratController.getInstance().updateBalance(m.A[0].Balance);
                            cc.BalanceController.getInstance().updateRealBalance(m.A[0].Balance);
                            break;
                        case cc.MethodHubOnName.WIN_RESULT_VIP:
                            if (m.A.length > 0) {
                                cc.BacaratController.getInstance().setWinVipResult(m.A[0]);
                                // cc.BacaratController.getInstance().winResultVip(m.A[0]);
                            }
                            break;
                        //thong bao khi dat cuoc
                        case cc.MethodHubOnName.PLAYER_MESSAGE:
                            cc.PopupController.getInstance().showMessage(m.A[0]);
                            break;
                        //thong bao
                        case cc.MethodHubOnName.MESSAGE:
                            if (!cc.game.isPaused())
                                cc.PopupController.getInstance().showMessage(m.A[0]);
                            break;
                        //nhan message chat
                        case cc.MethodHubOnName.RECEIVE_MESSAGE:
                            cc.ChatRoomController.getInstance().addChatContent(m.A);
                            cc.BacaratController.getInstance().playerShowBubbleChat(m.A);
                            break;

                    }
                });

            } else if (response.R && response.R.AccountID) {
                this.currAccId = response.R.AccountID;
                this.sendRequestOnHub(cc.MethodHubName.PLAY_NOW);
                //sau khi enterLobby
                //cc.PopupController.getInstance().showBusy();
                cc.PopupController.getInstance().hideBusy();

            } else {
                //PING PONG
                if (response.I) {
                    this.BacaratHub.pingPongResponse(response.I);
                }
            }
        },

        onHubOpen: function () {
            cc.PopupController.getInstance().hideBusy();
            this.sendRequestOnHub(cc.MethodHubName.ENTER_LOBBY);
            cc.PopupController.getInstance().showBusy();
        },

        onHubClose: function () {
            //reconnect
            // console.log((new Date()).getTime() - this.lastTimeReconnect);
            if ((new Date()).getTime() - this.lastTimeReconnect >= netConfig.RECONNECT_TIME * 1000) {
                this.reconnect();
            } else {
                cc.director.getScheduler().schedule(this.reconnect, this, netConfig.RECONNECT_TIME, 0, 0, false);
            }
        },

        onHubError: function () {

        },

        backClicked: function () {
            cc.LobbyController.getInstance().destroyDynamicView(null);
			 cc.LobbyController.getInstance().offuserguest(true);
        },

        //huong dan
        helpClicked: function () {
            cc.BCPopupController.getInstance().createHelpView();
        },

        playerLeave: function (info) {
            var accID = info[0];
            if (accID === cc.LoginController.getInstance().getUserId()) {
                var message = info[1];
                cc.LobbyController.getInstance().destroyDynamicView(null);
                cc.PopupController.getInstance().showMessage(message)
            }
        },

        onViewSoiCauClick: function () {

            this.viewSoiCau = !this.viewSoiCau;

            if (this.viewSoiCau) {
                //SlideDown
                this.nodeSoiCauAnimation.play('down');
            } else {
                //SlideUp
                this.nodeSoiCauAnimation.play('up');
            }

        },

        chatClicked: function () {
            cc.ChatRoomController.getInstance().showChat();
        },

    });

}).call(this);
