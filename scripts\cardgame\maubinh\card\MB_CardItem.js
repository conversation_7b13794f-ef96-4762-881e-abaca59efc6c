/*
 * Generated by BeChicken
 * on 9/11/2019
 * version v1.0
 */
(function () {
    cc.MB_CardItem = cc.Class({
        "extends": cc.Component,
        properties: {
            //Gia tri quan bai
            ordinalValue: 0,
            cardNumber: 0,
            cardSuite: 0,
            highLight: false,
            isClose: false //Up bai
        },

        onLoad: function () {
            this.duration = 0.3;
            //Chon bai
            this.selected = false;
            this.bindEvent();
        },
        // let event = cc.event.TOUCH_MOVE;
        bindEvent: function () {
            this.node.on('touchstart', function () {
                // this.eventTouchStart();
            }, this);
            this.node.on('touchmove', function () {
                //this.eventTouchMove();
            }, this);
            this.node.on('touchcancel', function () {
                //this.eventTouchEnd();
            }, this);
            this.node.on('touchend', function () {
                this.eventTouchEnd();
            }, this);
        },
        eventTouchMove: function () {
            this.node.y = 0;
            this.node.x = this.getPositionXMove();
            this.layout.type = cc.Layout.Type.NONE;
            this.node.zIndex = this.getIndexNodeMove();
        },
        eventTouchEnd: function () {
        },
        // Set Spirte frame la bai up
        initCard: function () {
            let sfCard = cc.TLMN_Controller.getInstance().getSfCardBack();
            this.node.getComponent(cc.Sprite).spriteFrame = sfCard;
        },

        //Up bai
        onCloseCard: function () {
            if (!this.isClose) {
                this.initCard();
                this.node.color = cc.TLMN_Controller.getInstance().getColorWhite();
                this.isClose = true;
            }
        },

        // Hien thi card
        onShowCard: function () {
            let sfCard = cc.MB_Controller.getInstance().getSpriteCard(this.cardNumber, this.cardSuite);
            this.node.getComponent(cc.Sprite).spriteFrame = sfCard;
        },

        // Reset bai ve trang thai ban dau
        reset: function () {
            this.selected = false;
            this.node.removeAllChildren();
            let moveBack = cc.moveTo(0.2, cc.v2(0, 0));
            if (!cc.game.isPaused()) {
                this.node.runAction(moveBack);
            } else {
                this.node.position = cc.v2(0, 0);
            }
            this.isClose = false;
        },
        moveTo: function (endPosition) {
            this.node.opacity = 200;
            var action = cc.moveTo(this.duration, endPosition);
            action.easing(cc.easeIn(0.3));

            var callback = cc.callFunc(this.moveFinished, null, this.node);


            this.node.runAction(cc.sequence(action, callback));
        },

        setPosition: function (endPosition) {
            this.node.position = endPosition;
        },

        moveToEnd: function (endPosition) {
            if (this.node) {
                this.node.opacity = 100;

                var action = cc.moveTo(0.5, endPosition);
                action.easing(cc.easeOut(1.0));

                var callback = cc.callFunc(this.moveToEndFinished, null, this.node);

                this.node.runAction(cc.sequence(action, callback));
            }
        },

        moveFinished: function (node) {
            node.opacity = 255;
            node.destroy();
        },

        moveToEndFinished: function (node) {
            // node.stopAllActions();
            cc.TLMN_Controller.getInstance().putToPool(node);
        }
    });
}).call(this);