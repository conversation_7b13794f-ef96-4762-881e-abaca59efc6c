/*
 * Generated by BeChicken
 * on 9/11/2019
 * version v1.0
 */
(function () {
    var MB_Controller;
    MB_Controller = (function () {
        var instance;

        function MB_Controller() {

        }

        instance = void 0;

        MB_Controller.getInstance = function () {
            if (instance === void 0) {
                instance = this;
            }
            return instance.prototype;
        };

        // Set View
        MB_Controller.prototype.setMBView = function (MBView) {
            this.MB_View = MBView;
        };

        MB_Controller.prototype.resetUIBackButton = function () {
            return this.MB_View.resetUIBackButton();
        };


        MB_Controller.prototype.setMBLobbyView = function (MBLobbyView) {
            this.MB_LobbyView = MBLobbyView;
        };

        MB_Controller.prototype.setMBCardView = function (MBCardView) {
            this.MB_CardView = MBCardView;
        };

        MB_Controller.prototype.setMBPlayerInfoView = function (MBPlayerInfoView) {
            this.MB_PlayerInfoView = MBPlayerInfoView;
        };

        MB_Controller.prototype.setMBLayoutCardView = function (layoutCardView) {
            this.MB_LayoutCardView = layoutCardView;
        };

        MB_Controller.prototype.setMBAssetsView = function (assetsView) {
            this.MB_AssetsView = assetsView;
        };
        MB_Controller.prototype.setMBInfoView = function (MBInfoView) {
            this.MB_InfoView = MBInfoView;
        };
        // Tao card pool
        MB_Controller.prototype.setCardPool = function (cardPool) {
            return this.cardPool = cardPool;
        };

        MB_Controller.prototype.putToPool = function (card) {
            return this.cardPool.putToPool(card);
        };
        MB_Controller.prototype.clearPool = function () {
            return this.cardPool.clearPool();
        };

        //Tao bai
        MB_Controller.prototype.createCard = function () {
            return this.cardPool.createCard();
        };

        // Cap nhat game theo HubOn
        //MB_LobbyView
        MB_Controller.prototype.sendRequestOnHub = function (method, data1, data2) {
            if (this.MB_LobbyView)
                return this.MB_LobbyView.sendRequestOnHub(method, data1, data2);
        };


        MB_Controller.prototype.updateInfo = function (info) {
            this.MB_InfoView.updateInfo(info);
        };

        //Reset game
        MB_Controller.prototype.resetGame = function () {
            this.MB_CardView.reset();
            this.MB_View.reset();
            this.MB_PlayerInfoView.reset();
        };

        //HUB ON NAME
        //joinGame
        MB_Controller.prototype.joinGame = function (info, timeInfo) {
            this.MB_InfoView.joinGame(info, timeInfo);
        };

        //playerLeave
        MB_Controller.prototype.playerLeave = function (info) {
            this.MB_LobbyView.playerLeave(info);
            this.MB_InfoView.playerLeave(info);
        };

        //playerJoin
        MB_Controller.prototype.playerJoin = function (info) {
            this.MB_InfoView.playerJoin(info)
        };
        MB_Controller.prototype.updateNotify = function (strNotify) {
            return this.MB_InfoView.updateNotify(strNotify);
        };
        //Reset updateProgress
        MB_Controller.prototype.resetUpdateProgress = function () {
            return this.MB_InfoView.resetUpdateProgress();
        };
        //HubOn haBai
        MB_Controller.prototype.haBai = function (data) {
            return this.MB_InfoView.haBai(data);
        };
        //HubOn updateConnectionStatus
        MB_Controller.prototype.updateConnectionStatus = function (data) {
            return this.MB_InfoView.updateConnectionStatus(data);
        };
        //HubOn showResult
        MB_Controller.prototype.showResult = function (result) {
            return this.MB_InfoView.showResult(result);
        };
        //HubOn updateAccount
        MB_Controller.prototype.updateAccount = function (dataAccounts) {
            return this.MB_InfoView.updateAccount(dataAccounts);
        };
        //Hub checkChi
        MB_Controller.prototype.checkChi = function (listOrdinal) {
            return this.sendRequestOnHub(cc.MethodHubName.CHECK_CHI, listOrdinal);
        };
        //Hub finishGame
        MB_Controller.prototype.finishGame = function (listOrdinal) {
            return this.sendRequestOnHub(cc.MethodHubName.FINISH_GAME, listOrdinal);
        };
        //HubOn onFinishGame
        MB_Controller.prototype.onFinishGame = function (data) {
            return this.MB_InfoView.onFinishGame(data);
        };
        //Hien thi trang thai xep bai
        MB_Controller.prototype.showStateSorting = function (data) {
            return this.MB_InfoView.showStateSorting(data);
        };
        //Set trang thai dang xep bai cho player
        MB_Controller.prototype.setStateSorting = function (accID) {
            return this.MB_InfoView.setStateSorting(accID);
        };

        //HubOn updateGameSession
        MB_Controller.prototype.updateGameSession = function (data) {
            return this.MB_InfoView.updateGameSession(data);
        };

        //Hien thi message chat
        MB_Controller.prototype.playerShowBubbleChat = function (message) {
            return this.MB_InfoView.playerShowBubbleChat(message);
        };
        //Xoa bai con lai tren ban
        MB_Controller.prototype.getListCardCurrentPlayer = function () {
            return this.MB_InfoView.getListCardCurrentPlayer();
        };

        //cap nhat trang thai progress
        MB_Controller.prototype.updateProgressOwner = function (infoTime) {
            this.MB_InfoView.updateProgressOwner(infoTime);
        };
        //Cap nhat so du cua nguoi choi
        MB_Controller.prototype.updateBalancePlayers = function (players) {
            this.MB_InfoView.updateBalancePlayers(players)
        };
        //An/Hien layout bai cua player hien tai
        MB_Controller.prototype.activeLayoutCardPlayer = function (isActive) {
            return this.MB_InfoView.activeLayoutCardPlayer(isActive);
        };
        //Chia bai
        MB_Controller.prototype.moveCards = function (players) {
            return this.MB_InfoView.moveCards(players)
        };
        //Reset resetPlayersResultUI
        MB_Controller.prototype.resetPlayersResultUI = function (isStartGame) {
            return this.MB_InfoView.resetPlayersResultUI(isStartGame);
        };

        MB_Controller.prototype.setOwnerID = function (ownerID) {
            this.ownerID = ownerID;
        };
        MB_Controller.prototype.getOwnerID = function () {
            return this.ownerID;
        };

        MB_Controller.prototype.setCurrTurn = function (isCurrTurn) {
            return this.isCurrTurn = isCurrTurn;
        };
        MB_Controller.prototype.getCurrTurn = function () {
            return this.isCurrTurn;
        };
        //SortCardView
        MB_Controller.prototype.setMBSortCardView = function (MBSortCardView) {
            return this.MB_SortCardView = MBSortCardView;
        };
        //Reset lai trang thai ban dau cua bai
        MB_Controller.prototype.resetSortCards = function () {
            return this.MB_SortCardView.resetSortCards();
        };
        //An btn Xep Lai
        MB_Controller.prototype.hideBtnXepLai = function () {
            return this.MB_SortCardView.hideBtnXepLai();
        };
        //Hien thi layout xep bai
        MB_Controller.prototype.onShowSortCard = function (listCards, timeSort) {
            return this.MB_SortCardView.onShowSortCard(listCards, timeSort);
        };
        //hideSortCard
        MB_Controller.prototype.activeLayoutSortCard = function (isActive) {
            return this.MB_SortCardView.activeLayoutSortCard(isActive);
        };
        //HubOn checkSortChi
        MB_Controller.prototype.checkSortChi = function (data) {
            return this.MB_SortCardView.onCheckSortChi(data);
        };
        //Stop count time
        MB_Controller.prototype.stopCountTime = function () {
            return this.MB_SortCardView.stopCountTime();
        };
        //Assets
        // Lay sprite thong bao
        MB_Controller.prototype.getNotify = function (type) {
            return this.MB_AssetsView.getNotify(type)
        };
        //Hien thi anim notify toi trang
        MB_Controller.prototype.showAllNotify = function (animationName, delayTime) {
            return this.MB_AssetsView.showAllNotify(animationName, delayTime)
        };
        //Lay sprite thua
        MB_Controller.prototype.getLose = function (type) {
            return this.MB_AssetsView.getLose(type)
        };

        //Lay sprite back
        MB_Controller.prototype.getSfBack = function (type) {
            return this.MB_AssetsView.getSfBack(type)
        };
        MB_Controller.prototype.getSfBorderCard = function () {
            return this.MB_AssetsView.getSfBorderCard()
        };
        //Lay spriteFram theo ten
        MB_Controller.prototype.getSpriteByName = function (spriteName) {
            return this.MB_AssetsView.getSpriteByName(spriteName);
        };

        MB_Controller.prototype.getWinFont = function () {
            return this.MB_AssetsView.getWinFont();
        };

        MB_Controller.prototype.getLoseFont = function () {
            return this.MB_AssetsView.getLoseFont();
        };

        MB_Controller.prototype.getAvatarDef = function () {
            return this.MB_AssetsView.getAvatarDef();
        };

        MB_Controller.prototype.getSfCardBack = function () {
            return this.MB_AssetsView.getSfCardBack();
        };

        // lay sprite bai
        MB_Controller.prototype.getSpriteCard = function (cardNunber, suite) {
            return this.MB_AssetsView.getSpriteCard(cardNunber, suite);
        };
        //MB_VIEW
        MB_Controller.prototype.setBetRoom = function (betRoom) {
            return this.betRoom = betRoom;
        };

        MB_Controller.prototype.getBetRoom = function () {
            return this.betRoom;
        };

        //Lay sprite bai theo ket qua tra ve
        MB_Controller.prototype.getSfGameResult = function (type) {
            return this.MB_AssetsView.getSfGameResult(type);
        };

        //Lay sprite bai theo value
        MB_Controller.prototype.getCardValueByNumber = function (value) {
            return this.MB_AssetsView.getCardValueByNumber(value);
        };

        MB_Controller.prototype.getColorDark = function () {
            return this.MB_AssetsView.getColorDark();
        };
        MB_Controller.prototype.getColorWhite = function () {
            return this.MB_AssetsView.getColorWhite();
        };

        //Lay sprite bai theo value
        MB_Controller.prototype.getCardValueByNumber = function (value) {
            return this.MB_AssetsView.getCardValueByNumber(value);
        };
        //Trang thai cua player
        MB_Controller.prototype.setCurrPlayerStatus = function (status) {
            return this.currStatus = status;
        };
        MB_Controller.prototype.getCurrPlayerStatus = function () {
            return this.currStatus;
        };

        return MB_Controller;


    })();
    cc.MB_Controller = MB_Controller;
}).call(this);
