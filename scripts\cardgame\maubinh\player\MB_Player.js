/*
 * Generated by BeChicken
 * on 9/11/2019
 * version v1.0
 */
(function () {
    cc.MB_Player = cc.Class({
        extends: cc.Component,
        properties: {
            layoutCard: cc.Node,
            nickName: ""

        },
        onLoad: function () {
            //animation
            this.animation = this.node.getComponent(cc.Animation);
            //node emotion
            var nodeChat = this.node.getChildByName('chat');
            this.nodeEmotion = nodeChat.getChildByName('emotion');
            this.nodeBubble = nodeChat.getChildByName('bubble');
            //skeleton Emotion
            this.skeEmotion = this.nodeEmotion.getComponent(sp.Skeleton);
            //label Chat
            this.lbBubbleChat = this.nodeBubble.getComponentInChildren(cc.Label);
            nodeChat.active = false;

            //avatar cua player
            this.avatar = this.node.getComponentInChildren(cc.Avatar);

            //node win/lose cua player
            this.nodeWin = this.node.getChildByName('win');
            this.animWin = this.nodeWin.getComponent(sp.Skeleton);

            this.nodeNofity = this.node.getChildByName('notify');

            //lbWin cua player
            this.lbWin = this.node.getChildByName("lbWin").getComponent(cc.Label);
            //anim lbWin cua player
            this.animLbWin = this.node.getComponentInChildren(cc.Animation);

            //node thong tin player (name + chip)
            this.nodeInfo = this.node.getChildByName('ava_money');

            //lbChip cua player
            this.lbChip = this.nodeInfo.getChildByName('lbChip').getComponent(cc.LabelIncrement);

            //lbName cua player
            this.layoutName = this.nodeInfo.getChildByName("layoutName");
            this.lbName = this.layoutName.getChildByName('lbName').getComponent(cc.Label);
            this.lbSID = this.layoutName.getChildByName('lbSID').getComponent(cc.Label);

            //nodeOut cua player
            this.nodeOut = this.node.getChildByName('nodeOut');

            //node time progress
            this.nodeTimeProgress = this.node.getChildByName('timeprogress').getComponent(cc.ProgressBar);

            //label ket qua
            this.lbResult = this.node.getChildByName('lbResult').getComponent(cc.Label);
            //Node resultName
            this.resultName = this.node.getChildByName('resultName');

            //layout bai cua player
            this.interval = null;


            this.nodeChi1 = this.layoutCard.getChildByName('chi1');
            this.nodeChi2 = this.layoutCard.getChildByName('chi2');
            this.nodeChi3 = this.layoutCard.getChildByName('chi3');

            this.nodeCheckChi = this.node.getChildByName('nodeCheckChi');

            this.resetPlayerResultUI(false);
        },
        onDisable: function () {
            try {
                this.resetPlayerResultUI(false);
            } catch (e) {

            }

        },
        onDestroy: function () {
            if (this.interval) {
                clearInterval(this.interval)
            }
        },

        //Cap nhat progress cho truong hop START_ACTION_TIMER
        updateProgressOwner: function (timeInfo) {
            if (!this.nodeTimeProgress)
                return;

            if (this.interval !== null) {
                clearInterval(this.interval);
            }

            let time = timeInfo[1];
            if (time == 0) {
                return;
            }
            this.nodeTimeProgress.node.active = true;
            this.speed = time;
            this.nodeTimeProgress.progress = 1;

            this.interval = setInterval(function () {
                let progress = this.nodeTimeProgress.progress;
                progress -= 0.01 / this.speed;
                if (progress < 0)
                    progress = 0;
                this.nodeTimeProgress.progress = progress;
            }.bind(this), 10);
        },

        //Cap nhat progress cho truong hop joinGame
        updateProgressOwnerJoinGame: function (timeInfo) {
            if (!this.nodeTimeProgress)
                return;

            if (this.interval !== null) {
                clearInterval(this.interval);
            }

            let totalTime = timeInfo[0];
            let time = timeInfo[1];

            if (time == 0) {
                return;
            }

            this.nodeTimeProgress.node.active = true;

            this.speed = totalTime;
            this.nodeTimeProgress.progress = 1 - (totalTime - time) / totalTime;

            this.interval = setInterval(function () {
                let progress = this.nodeTimeProgress.progress;
                progress -= 0.01 / this.speed;
                if (progress < 0)
                    progress = 0;
                this.nodeTimeProgress.progress = progress;
            }.bind(this), 10);
        },
        resetProgressOwner: function () {
            if (this.interval !== null) {
                clearInterval(this.interval);
            }
            this.nodeTimeProgress.node.active = false;
        },
        stopUpdateProgressOwner: function () {
            this.nodeTimeProgress.node.active = false;
        },

        resetPlayerResultUI: function (isStartGame) {
            this.layoutCard.active = false;
            // Anim
            this.nodeWin.active = false;
            this.nodeNofity.active = false;

            this.nodeCheckChi.active = false;
            //Lb Ket qua
            this.lbResult.node.active = false;
            // lb thang/thua
            this.lbWin.node.active = false;
            // icon thoat
            this.nodeOut.active = false;

            this.avatar.node.opacity = 255;

            this.nodeTimeProgress.node.active = false;
            this.resultName.active = false;

            this.nodeInfo.active = isStartGame;
            if (!isStartGame) {
                this.avatar.setAvatar(cc.MB_Controller.getInstance().getAvatarDef());
            }
        },
        showWinNotify: function (animationName, delayTime) {
            this.nodeNofity.active = false;
            this.nodeWin.active = true;
            this.animWin.clearTracks();
            this.animWin.setToSetupPose();
            this.animWin.setAnimation(0, animationName, true);
            if (!cc.game.isPaused()) {
                cc.director.getScheduler().schedule(function () {
                    this.nodeWin.active = false;
                }, this, 0, 0, delayTime, false);
            } else {
                this.nodeWin.active = false;
            }
        },
        setAvatarBlur: function () {
            this.avatar.node.opacity = 150;
        },
        //player vao phong
        registerPlayer: function (accountInfo, status) {
            // hien thi ai la chuong
            let player = accountInfo.Account;
            this.accId = player.AccountID;
            var avatarID = player.Avatar;
            if (avatarID <= 0) {
                avatarID = 1;
            }
            if (status === cc.MB_PlayerStatus.INGAME) {
                this.avatar.node.opacity = 255;
            } else {
                this.avatar.node.opacity = 150;
            }
            this.nodeInfo.active = true;
            this.nickName = player.NickName;
            //set avatar
            this.avatar.setAvatar(cc.AccountController.getInstance().getAvatarImage(avatarID));
            //set name
            if (player.ServiceID) {
                this.lbSID.string = cc.Config.getInstance().getServiceNameNoFormat(player.ServiceID);
                //set name
                this.lbName.string = cc.Config.getInstance().formatName(player.NickName, 7);
            } else {
                this.lbSID.string = '';
                //set name
                this.lbName.string = cc.Config.getInstance().formatName(player.NickName, 10);
            }
            //set chip
            this.lbChip.tweenValueto(player.Star);
            //bat node thong tin
            this.node.opacity = 255;
            //Hien thi trang thai dang ky roi phong
            this.nodeOut.active = accountInfo.RegisterLeaveRoom;

        },
        showEmotion: function (index, message) {
            this.nodeBubble.active = false;
            this.nodeEmotion.active = true;

            this.skeEmotion.clearTracks();
            this.skeEmotion.setToSetupPose();

            //fix loi server cam chat sexy
            if (index === 15) {
                this.skeEmotion.setAnimation(index, '16-extreme-sexy-girl', true);
            } else {
                this.skeEmotion.setAnimation(index, message[1], true);
            }

            this.animation.play('showBubbleChat');
        },
        //player roi phong
        unRegisterPlayer: function () {
            //reset trang thai ban dau
            this.resetPlayerResultUI(false);
            this.avatar.setAvatar(cc.MB_Controller.getInstance().getAvatarDef());
        },

        updateConnectionStatus: function (status) {
            switch (status) {
                case cc.TLMNConnectionStatus.DISCONNECTED:
                    this.node.opacity = 255;
                    break;
                case cc.TLMNConnectionStatus.REGISTER_LEAVE_GAME:
                    this.node.opacity = 255;
                    this.nodeOut.active = true;
                    break;
                case cc.TLMNConnectionStatus.CONNECTED:
                    this.nodeOut.active = false;
                    this.node.opacity = 255;
                    break;
            }
        },

        updatePlayerStatus: function (playerStatus) {
            if (playerStatus == cc.MB_PlayerStatus.INGAME) {
                this.node.opacity = 255;
            } else {
                this.node.opacity = 150;
            }
        },
        //getListCard
        getListCard: function () {
            let listCardChi1 = this.nodeChi1.children;
            let listCardChi2 = this.nodeChi2.children;
            let listCardChi3 = this.nodeChi3.children;
            return [...listCardChi1, ...listCardChi2, ...listCardChi3];
        },
        //Update bai sau khi chia
        updateCardPlayer: function (accountInfo, isHaBai) {
            let dataCard = accountInfo.BaiRac;
            if (dataCard.length === 0) {
                return;
            }
            let joinNodeCard = [];

            let listCardChi1 = this.nodeChi1.children;
            let listCardChi2 = this.nodeChi2.children;
            let listCardChi3 = this.nodeChi3.children;
            joinNodeCard = [...listCardChi1, ...listCardChi2, ...listCardChi3];
            this.layoutCard.active = true;

            dataCard.map((itemCard, idx) => {
                //Lay ten bai
                let cardItem = joinNodeCard[idx].getComponent("MB_CardItem");
                cardItem.cardNumber = itemCard.CardNumber;
                cardItem.cardSuite = itemCard.CardSuite;
                cardItem.ordinalValue = itemCard.OrdinalValue;
                cardItem.highLight = itemCard.HighLight;
                //let spriteFrame = cc.MB_Controller.getInstance().MB_InfoView.spriteCardBack;
                //if (accountInfo.AccountID === cc.LoginController.getInstance().getUserId() || isHaBai) {
                let spriteFrame = cc.MB_Controller.getInstance().getSpriteCard(itemCard.CardNumber, itemCard.CardSuite);
                if (spriteFrame === null) {
                    spriteFrame = cc.MB_Controller.getInstance().MB_InfoView.spriteCardBack;
                }
                //}
                joinNodeCard[idx].getComponent(cc.Sprite).spriteFrame = spriteFrame;
                if (!isHaBai) {
                    joinNodeCard[idx].opacity = 0;
                    joinNodeCard[idx].color = cc.MB_Controller.getInstance().getColorWhite();
                } else {
                    joinNodeCard[idx].color = cc.MB_Controller.getInstance().getColorDark();
                }
            }, this);
            if (!isHaBai) {
                for (let i = 0; i < 13; i++) {
                    cc.director.getScheduler().schedule(function () {
                        joinNodeCard[i].getComponent(cc.Animation).play('show-card');
                    }, this, 0, 0, 0.1 * i, false)
                }
            }

        },
        hideLayoutCardOnHands: function () {
            this.cardOnHand.active = false;
        },
        //Hien thi thong bao thang thua
        showNotifyCheckChi: function (resultFamily) {
            this.nodeCheckChi.active = true;
            let resultName = cc.MB_RESULT_NAME_UI[resultFamily];
            let spriteResult = cc.MB_Controller.getInstance().getSpriteByName(resultName);
            this.nodeCheckChi.getComponent(cc.Sprite).spriteFrame = spriteResult;
        },
        onUpdateResult: function (value, stateName) {
            //Hien thi tien thang/thua
            value = parseInt(value);
            let formatValue = cc.Tool.getInstance().formatNumber(Math.abs(value));
            let font = cc.MB_Controller.getInstance().getWinFont();

            if (value < 0) {
                font = cc.MB_Controller.getInstance().getLoseFont();
                formatValue = "-" + formatValue;
            } else {
                formatValue = "+" + formatValue;
            }
            if (value != 0) {

                this.lbWin.string = formatValue;
                this.lbWin.font = font;
                this.lbWin.node.active = true;
                if (!cc.game.isPaused()) {
                    this.lbWin.node.getComponent(cc.Animation).play('showMoney');
                }
            }


            let spNotify = null;
            if (value > 0) {
                spNotify = cc.MB_Controller.getInstance().getSpriteByName(cc.MB_PLAYER_STATE_UI.THANG);
            } else if (value === 0) {
                spNotify = cc.MB_Controller.getInstance().getSpriteByName(cc.MB_PLAYER_STATE_UI.HOA);
            } else {
                spNotify = cc.MB_Controller.getInstance().getSpriteByName(cc.MB_PLAYER_STATE_UI.THUA);
            }
            if (stateName != "" && stateName != undefined) {
                spNotify = cc.MB_Controller.getInstance().getSpriteByName(stateName);
            }
            if (spNotify == null) {
                return;
            }
            this.nodeNofity.active = true;
            try {
                let originSize = spNotify.getOriginalSize();
                this.nodeNofity.getComponent(cc.Sprite).spriteFrame = spNotify;
                this.nodeNofity.setContentSize(originSize);
            } catch (e) {
                console.log(e);
            }

        },
        showNotifyByStateName: function (stateName) {
            this.nodeNofity.active = true;
            let spNotify = cc.MB_Controller.getInstance().getSpriteByName(stateName);
            try {
                let originSize = spNotify.getOriginalSize();

                this.nodeNofity.getComponent(cc.Sprite).spriteFrame = spNotify;
                this.nodeNofity.setContentSize(originSize);

            } catch (e) {
                console.log(e);
            }

        },
        //HighLight bai cua tung quan bai theo chi
        highLightChi: function (chiValue) {
            let listCardChi1 = this.nodeChi1.children;
            let listCardChi2 = this.nodeChi2.children;
            let listCardChi3 = this.nodeChi3.children;
            let highLightChi1 = false;
            let highLightChi2 = false;
            let highLightChi3 = false;
            switch (chiValue) {
                case 1:
                    highLightChi1 = true;
                    highLightChi2 = false;
                    highLightChi3 = false;
                    break;
                case 2:
                    highLightChi1 = false;
                    highLightChi2 = true;
                    highLightChi3 = false;
                    break;
                case 3:
                    highLightChi1 = false;
                    highLightChi2 = false;
                    highLightChi3 = true;
                    break;
                case 4:
                    highLightChi1 = true;
                    highLightChi2 = true;
                    highLightChi3 = true;
                    break;
                default:
                    highLightChi1 = false;
                    highLightChi2 = false;
                    highLightChi3 = false;
                    break;

            }
            this.hightCard(listCardChi1, highLightChi1);
            this.hightCard(listCardChi2, highLightChi2);
            this.hightCard(listCardChi3, highLightChi3);
        },
        hightCard: function (listCard, isHighLight) {
            listCard.map(card => {
                let color = cc.MB_Controller.getInstance().getColorDark();
                if (isHighLight) {
                    color = cc.MB_Controller.getInstance().getColorWhite();
                }
                card.color = color;
            }, this)
        },
        onUpdateAccount: function (accountInfo) {

            let star = accountInfo.Star;
            let accountID = accountInfo.AccountID;
            //Cap nhat so du UI
            if (!cc.game.isPaused()) {
                this.lbChip.tweenValueto(star);
            }
            else {
                this.lbChip.setValue(star);
            }
            //Cap nhat so du neu la current player
            if (this.isCurrentPlayer(accountID)) {
                cc.BalanceController.getInstance().updateRealBalance(star);
            }


        },
        //Lay danh sach bai con lai cua player hien tai
        getListCurrCardPlayer: function () {
            return this.layoutCard.children;
        },

        isCurrentPlayer: function (accID) {
            return accID === cc.LoginController.getInstance().getUserId();
        },

        showBubbleChat: function (message) {
            this.nodeBubble.active = true;
            this.nodeEmotion.active = false;

            this.lbBubbleChat.string = message[1];

            this.animation.play('showBubbleChat');
        },
    })
}).call(this);