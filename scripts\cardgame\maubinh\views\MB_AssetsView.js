/*
 * Generated by BeChicken
 * on 9/11/2019
 * version v1.0
 */
(function () {
    cc.MB_AssetsView = cc.Class({
        extends: cc.Component,
        properties: {
            sfAvatarDef: cc.SpriteFrame,
            spGameResult: [cc.SpriteFrame],
            //font
            bmfWin: cc.BitmapFont,
            bmfLose: cc.BitmapFont,
            //Color
            colorDark: cc.Color,
            colorWhite: cc.Color,
            //back
            sfBack: [cc.SpriteFrame],//0: huy roi phong, 1: dk roi phong,
            //SF Vien khi chon bai
            sfBorderCard: cc.SpriteFrame,
            /*
            *   BICH: 0,
                TEP: 13,
                RO: 26,
                CO: 39,
            */
            sfSuitCo: [cc.SpriteFrame],
            sfSuitRo: [cc.SpriteFrame],
            sfSuitTep: [cc.SpriteFrame],
            sfSuitBich: [cc.SpriteFrame],
            sfCardBack: cc.SpriteFrame,

            animationNotify: sp.Skeleton,
            spriteAtlasNotify: cc.SpriteAtlas

        },
        onLoad: function () {
            cc.MB_Controller.getInstance().setMBAssetsView(this);
            this.animationNotify.node.parent.active = false;
        },
        onDestroy: function () {
        },
        getWinFont: function () {
            return this.bmfWin;
        },

        getLoseFont: function () {
            return this.bmfLose;
        },
        getAvatarDef: function () {
            return this.sfAvatarDef;
        },
        getNotify: function (type) {
            return this.spGameNotify[type];
        },
        getLose: function (type) {
            return this.spLose[type];
        },

        getSfBack: function (type) {
            return this.sfBack[type];
        },
        //Lay SpriteFrame Ket qua game
        getSfGameResult: function (type) {
            return this.spGameResult[type];
        },
        getSfBorderCard: function () {
            return this.sfBorderCard;
        },
        //Lay SpriteFrame bai sau
        getSfCardBack: function () {
            return this.sfCardBack;
        },
        //Lay sprite bai
        getSpriteCard: function (number, suite) {
            let getSuit = null;
            number = parseInt(number);
            switch (suite) {
                case cc.MB_Card.CO:
                    getSuit = this.sfSuitCo;
                    break;
                case cc.MB_Card.RO:
                    getSuit = this.sfSuitRo;
                    break;
                case cc.MB_Card.TEP:
                    getSuit = this.sfSuitTep;
                    break;
                case cc.MB_Card.BICH:
                    getSuit = this.sfSuitBich;
                    break;
            }
            if (number === -1) {
                return null;
            }
            return getSuit[number];
        },

        //Tao gia tri bai
        getCardValue: function (cardNumber, suit) {
            // gia tri bai
            // chay tu 3 -> 2
            // (gia tri bai - 3)*4 + suit / 13
            // 8 Bich = (8-3)*4 + 0 = 20
            // 8 tep = (8-3)*4 + 13/13 = 21
            return (cardNumber - 3) * 4 + suit / 13;

        },

        getCardValueByNumber: function (value) {
            // get the card value in the suite
            const NUMBER_OF_SUITES = 4;
            const CARDS_IN_SUITE = 13;
            let numericValue = Math.floor(value / NUMBER_OF_SUITES) + 3;
            // get the card suite
            let suiteValue = (value % NUMBER_OF_SUITES) * CARDS_IN_SUITE;
            return this.getSpriteCard(numericValue, suiteValue);

        },

        getColorDark: function () {
            return this.colorDark;
        },

        getColorWhite: function () {
            return this.colorWhite;
        },

        showAllNotify: function (animationName, delayTime) {
            this.animationNotify.node.parent.active = true;
            this.animationNotify.clearTracks();
            this.animationNotify.setToSetupPose();
            this.animationNotify.setAnimation(0, animationName, true);
            if (!cc.game.isPaused()) {
                cc.director.getScheduler().schedule(function () {
                    this.hideNotify();
                }, this, 0, 0, delayTime, false);
            } else {
                this.hideNotify();
            }
        },
        hideNotify: function () {
            this.animationNotify.node.parent.active = false;
        },

        getSpriteByName: function (nameSprite) {
            return this.spriteAtlasNotify.getSpriteFrame(nameSprite);
        }


    })
}).call(this);