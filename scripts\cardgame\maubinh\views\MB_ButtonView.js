/*
 * Generated by BeChicken
 * on 9/11/2019
 * version v1.0
 */
(function () {
    cc.MB_ButtonView = cc.Class({
        extends: cc.Component,
        properties: {
            buttonBatDau: cc.Button,
        },
        onLoad: function () {
            cc.TLMN_Controller.getInstance().setButtonsView(this);
        },
        onDestroy: function () {

        },
        onStartGame: function () {
            cc.MB_Controller.getInstance().sendRequestOnHub(cc.MethodHubName.START_GAME);
            this.buttonBatDau.node.active = false;
        },

        //Hien thi layout button
        showLayoutButton: function (accID, allowActions) {
        },
        enableButtonBatDau: function (enable) {
            if(this.buttonBatDau) {
                this.buttonBatDau.node.active = enable;
            }
        },

    })
}).call(this);