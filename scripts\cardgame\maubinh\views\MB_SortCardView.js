/*
 * Generated by BeChicken
 * on 9/11/2019
 * version v1.0
 */
(function () {
    cc.MB_SortCardView = cc.Class({
        extends: cc.Component,
        properties: {
            nodeSortChi1: cc.Node,
            nodeSortChi2: cc.Node,
            nodeSortChi3: cc.Node,
            nodeBinhLung: cc.Node,
            nodeParentSortCard: cc.Node,
            layoutSortCard: cc.Node,
            lbTimeXep: cc.Label,
            btnXepLai: cc.Node,
        },
        onLoad: function () {
            cc.MB_Controller.getInstance().setMBSortCardView(this);
            this.activeLayoutSortCard(false);
            this.rootPositions = this.rootPositionAllCard();
            this.initCards = this.nodeParentSortCard.children;
            this.indexCardSort = -1;
            this.indexCardMove = -1;
            this.lbTimeXep.string = 0;
            this.btnXepLai.active = false;
            this.initEventTouchMove();

            this.maxY = 337;
            this.minY = -330;
            this.minX = -556;
            this.maxX = 288;
            this.interval = null;

        },
        resetSortCards: function () {
            let listCards = this.nodeParentSortCard.children;
            listCards.map((card, index) => {
                card.stopAllActions();
                card.name = "item" + (index + 1);
                card.zIndex = 0;
                card.position = this.rootPositions[index];
            });
        },
        onEnable: function () {
            //this.activeLayoutSortCard(false);
        },
        hideBtnXepLai: function () {
            this.btnXepLai.active = false;
        },
        onShowSortCard: function (listCards, timeSort) {
            //An/Hien layout bai hien tai cua player
            cc.MB_Controller.getInstance().activeLayoutCardPlayer(false);

            let listNodeCard = this.nodeParentSortCard.children;
            listCards.map((card, idx) => {

                let cardNumber = card.CardNumber;
                let cardSuite = card.CardSuite;
                let highLight = card.HighLight;

                let mbCarItem = listNodeCard[idx].getComponent("MB_CardItem");
                mbCarItem.ordinalValue = card.OrdinalValue;
                mbCarItem.cardNumber = cardNumber;
                mbCarItem.cardSuite = cardSuite;
                mbCarItem.highLight = highLight;

                listNodeCard[idx].getComponent(cc.Sprite).spriteFrame = cc.MB_Controller.getInstance().getSpriteCard(cardNumber, cardSuite);

                if (highLight) {
                    listNodeCard[idx].color = cc.MB_Controller.getInstance().getColorWhite();
                } else {
                    listNodeCard[idx].color = cc.MB_Controller.getInstance().getColorDark();
                }
            });

            //Goi check chi
            this.callCheckChi();
            //Hien thi layout xep bai
            this.activeLayoutSortCard(true);
            //Cap nhat thoi gian
            this.updateCountTime(timeSort);
        },

        updateCountTime: function (time) {
            this.lbTimeXep.string = time;
            this.timer = time;
            //cc.director.getScheduler().schedule(this.startTimer, this, 1, time, 0, false);
            let self = this;
            this.interval = setInterval(function () {
                self.startTimer();
            }, 1000);
        },
        startTimer: function () {
            this.lbTimeXep.node.getComponent(cc.Animation).play('time');
            this.lbTimeXep.string = this.timer;
            this.timer--;
            if (this.timer < 0) {
                this.timer = 0
            }
            if (this.timer === 0) {
                this.layoutSortCard.active = false;
                cc.MB_Controller.getInstance().activeLayoutCardPlayer(true);
            }
        },
        stopCountTime: function () {
            try {
                //cc.director.getScheduler().unschedule(this.startTimer, this)
                clearInterval(this.interval);
            }catch(e) {

            }

        },
        onChangeChi: function () {
            //Doi chi
            // item 1 -> item 6
            // item 2 -> item 7
            // item 3 -> item 8
            // item 4 -> item 9
            // item 5 -> item 10
            let listNodeCard = this.getListCardAfterSort()[0].listCards;
            for (let i = 0; i <= 4; i++) {
                //Doi ten bai sau khi di chuyen
                listNodeCard[i].name = 'item' + (i + 6);
                listNodeCard[i + 5].name = 'item' + (i + 1);
                this.cardMoveTo(listNodeCard[i], this.rootPositions[i + 5], i + 5);
                this.cardMoveTo(listNodeCard[i + 5], this.rootPositions[i], i);
            }
            this.callCheckChi();
        },
        callCheckChi: function () {
            let listOrdinal = this.getListCardAfterSort()[1].listOrdinal;
            cc.MB_Controller.getInstance().checkChi(listOrdinal);
        },
        cardMoveTo: function (node, posEnd, zIndex) {
            let moveTo = cc.moveTo(0.3, posEnd);
            let callFunc = cc.callFunc(function () {
                node.zIndex = zIndex;
            }, this);
            node.runAction(cc.sequence(moveTo, callFunc));
        },
        onSortDone: function () {
            this.activeLayoutSortCard(false);
            //An/Hien layout bai hien tai cua player
            cc.MB_Controller.getInstance().activeLayoutCardPlayer(true);

            this.btnXepLai.active = true;
            //Cap nhat lai bai sau khi sap xep
            let listOrdinal = this.getListCardAfterSort()[1].listOrdinal;
            cc.MB_Controller.getInstance().finishGame(listOrdinal);
        },
        onCheckChi: function () {
            //Lay danh sach bai de gui len kiem tra chi
            let listOrdinal = this.getListOrdinalValueCard();
            cc.MB_Controller.getInstance().checkChi(listOrdinal);
        },
        getListOrdinalValueCard: function () {
            let listNodeCard = this.nodeParentSortCard.children;
            let listOrdinal = [];
            listNodeCard.map(card => {
                let ordinal = card.getComponent("MB_CardItem").ordinalValue;
                if (ordinal !== -1) {
                    listOrdinal.push(ordinal);
                }
            });
            return listOrdinal;
        },
        rootPositionAllCard: function () {
            let rootPos = [];
            let listNodeCard = this.nodeParentSortCard.children;
            listNodeCard.map(card => rootPos.push(card.position));
            return rootPos;
        },
        initEventTouchMove: function () {
            let listNodeCard = this.nodeParentSortCard.children;
            listNodeCard.map((card, idx) => {
                card.on("touchstart", function (event) {
                    let nodeName = event.target._name;
                    this.initIndexCard = parseInt(nodeName.split('item')[1]) - 1;
                    this.indexCardMove = nodeName;
                    card.zIndex = listNodeCard.length + 1;
                }, this);
                card.on("touchmove", function (event) {
                    let delta = event.touch.getDelta();
                    card.x += delta.x;
                    card.y += delta.y;

                    listNodeCard.map(cardDirect => {
                        let colliderCardDirect = cardDirect.getComponent(cc.PolygonCollider);
                        if (cc.Intersection.pointInPolygon(event.touch.getLocation(), colliderCardDirect.world.points)) {
                            cardDirect.color = cc.MB_Controller.getInstance().getColorDark();
                        } else {
                            cardDirect.color = cc.MB_Controller.getInstance().getColorWhite();
                        }
                    }, this);

                }, this);
                card.on('touchcancel', function (event) {
                    card.position = this.rootPositions[this.initIndexCard];
                    card.zIndex = 0;
                }, this);
                card.on("touchend", function (event) {

                    listNodeCard.map(cardDirect => {
                        let colliderCardDirect = cardDirect.getComponent(cc.PolygonCollider);
                        if (cc.Intersection.pointInPolygon(event.touch.getLocation(), colliderCardDirect.world.points)) {
                            if (cardDirect.name !== event.target._name) {
                                this.indexCardSort = cardDirect.name;
                            }
                        }
                    }, this);

                    if (this.indexCardSort === -1) {
                        card.position = this.rootPositions[this.initIndexCard];
                        card.zIndex = 0;
                        this.callCheckChi();
                        return;
                    }
                    let cardGoals = listNodeCard.filter(cardDirect => cardDirect.name === this.indexCardSort);
                    let cardDirect = cardGoals[0];

                    card.name = this.indexCardSort;
                    card.position = cardDirect.position;
                    card.zIndex = 0;
                    card.color = cc.MB_Controller.getInstance().getColorWhite();
                    let zIndexCardDirect = this.indexCardMove.toString().split('item')[1];

                    let convertIndex = parseInt(zIndexCardDirect) - 1;
                    let rootPosCard = this.rootPositions[convertIndex];
                    cardDirect.color = cc.MB_Controller.getInstance().getColorWhite();
                    cardDirect.name = this.indexCardMove;
                    cardDirect.zIndex = 0;
                    cardDirect.position = rootPosCard;

                    //Call hub checkSortChi
                    this.callCheckChi();
                    this.indexCardSort = -1;
                    this.indexCardMove = -1;

                }, this);
            }, this)
        },
        //Lay danh bai + thuoc tinh khoi tao
        listCardInit: function () {
            let listNodeCard = this.nodeParentSortCard.children;
        },
        activeLayoutSortCard: function (isActive) {
            cc.director.getCollisionManager().enabled = true;
            this.layoutSortCard.active = isActive;
            if (!isActive) {
                cc.MB_Controller.getInstance().onFinishGame([cc.LoginController.getInstance().getUserId()]);
            } else {
                cc.MB_Controller.getInstance().setStateSorting([cc.LoginController.getInstance().getUserId()]);
            }
        },
        onDisable: function () {
            try {
                this.unscheduleAllCallbacks();
            } catch (e) {

            }
        },
        onClickXepLai: function () {
            this.activeLayoutSortCard(true);
        },
        //HubOn
        onCheckSortChi: function (data) {
            let lbSortResult = data[2];
            let binhLung = data[3];
            let isToiTrang = binhLung == cc.MB_SORT_CHI.TOI_TRANG;
            this.nodeBinhLung.active = false;
            this.activeNodeSortChi(true);
            if (binhLung != cc.MB_SORT_CHI.BINH_LUNG) {

                this.activeNodeSortChi(true);
                this.setSpriteSortChi(this.nodeSortChi1, lbSortResult[0]);
                this.setSpriteSortChi(this.nodeSortChi2, lbSortResult[1]);
                this.setSpriteSortChi(this.nodeSortChi3, lbSortResult[2]);

                if (isToiTrang) {
                    this.nodeBinhLung.active = true;
                    this.setSpriteBinhLung(binhLung)
                }
            } else {
                this.activeNodeSortChi(false);
                this.nodeBinhLung.active = true;
                this.setSpriteBinhLung(binhLung);
                cc.PopupController.getInstance().showSlotsMessage("Binh Lủng");
            }

            //Cap nhat highLight bai
            let listCards = this.getListCardAfterSort()[0].listCards;
            //Danh sach bai tra ve
            let lstCardSort = data[4];
            let listCardCurrPlayer = cc.MB_Controller.getInstance().getListCardCurrentPlayer();

            lstCardSort.map((cardReturn, index) => {

                //Cap nhat bai hien tai cua player
                let cardItemCurrPlayer = listCardCurrPlayer[index].getComponent("MB_CardItem");
                let cardNodeSprite = listCardCurrPlayer[index].getComponent(cc.Sprite);
                cardItemCurrPlayer.ordinalValue = cardReturn.OrdinalValue;
                cardItemCurrPlayer.cardNumber = cardReturn.CardNumber;
                cardItemCurrPlayer.cardSuite = cardReturn.CardSuite;
                cardNodeSprite.spriteFrame = cc.MB_Controller.getInstance().getSpriteCard(cardReturn.CardNumber, cardReturn.CardSuite);


                let card = listCards[index];
                card.getComponent("MB_CardItem").highLight = cardReturn.HighLight;
                if (cardReturn.HighLight) {
                    card.color = cc.MB_Controller.getInstance().getColorWhite();
                } else {
                    card.color = cc.MB_Controller.getInstance().getColorDark();
                }
            }, this);
        },
        setSpriteSortChi: function(node, result) {
            let spriteNote = node.getChildByName('sprite');
            let spName =  cc.MB_RESULT_NAME_UI[result];
            let sprite = cc.MB_Controller.getInstance().getSpriteByName(spName);
            try {
                let originSize = sprite.getOriginalSize();
                spriteNote.getComponent(cc.Sprite).spriteFrame = sprite;
                spriteNote.setContentSize(originSize);
            } catch (e) {
                console.log(e);
            }

        },
        setSpriteBinhLung: function (binhLung) {
            let spName = cc.MB_SORT_CHI_BINH_LUNG_UI[binhLung];
            let sprite = cc.MB_Controller.getInstance().getSpriteByName(spName);
            try {
                let originSize = sprite.getOriginalSize();
                this.nodeBinhLung.getComponent(cc.Sprite).spriteFrame = sprite;
                this.nodeBinhLung.setContentSize(originSize);
            } catch (e) {
                console.log(e);
            }

        },
        activeNodeSortChi: function (isActive) {
            this.nodeSortChi1.active = isActive;
            this.nodeSortChi2.active = isActive;
            this.nodeSortChi3.active = isActive;
        },
        //Lay danh sach bai sau khi xep
        getListCardAfterSort: function () {
            let listNodeCard = this.nodeParentSortCard.children;
            //Xep lai bai
            let listOrdinal = [];
            let listCards = [];
            listNodeCard
                .sort((a, b) => parseInt(a._name.split('item')[1]) - parseInt(b._name.split('item')[1]))
                .map((card) => {
                    let ordinal = card.getComponent("MB_CardItem").ordinalValue;
                    listCards.push(card);
                    listOrdinal.push(ordinal);
                });
            return [{listCards: listCards}, {listOrdinal: listOrdinal}];
        }
    })
}).call(this);