/*
 * Generated by BeChicken
 * on 9/11/2019
 * version v1.0
 */
(function () {
    cc.MB_VIEW = cc.Class({
        extends: cc.Component,
        properties: {
            sfSounds: [cc.SpriteFrame], //0=on, 1=off
            spriteSound: cc.Sprite,

            spriteBack: cc.Sprite,

            nodeRegisterLeave: cc.Node,
        },
        onLoad: function () {
            cc.MB_Controller.getInstance().setMBView(this);
            this.isRegisterLeaveRoom = false;
        },
        start: function () {
            //Check Sound
            this.sound = cc.Tool.getInstance().getItem("@Sound").toString() === 'true';
            this.spriteSound.spriteFrame = this.sound ? this.sfSounds[0] : this.sfSounds[1];
            cc.AudioController.getInstance().enableSound(this.sound);
        },

        onEnable: function () {

            this.isRegisterLeaveRoom = false;
            this.nodeRegisterLeave.active = false;
            this.spriteBack.spriteFrame =cc.MB_Controller.getInstance().getSfBack(cc.TLMN_BACK.UN_REGIST_LEAVE);

            cc.BalanceController.getInstance().updateBalance(cc.BalanceController.getInstance().getBalance());
        },

        soundClicked: function () {
            this.sound = !this.sound;
            cc.Tool.getInstance().setItem("@Sound", this.sound);
            this.spriteSound.spriteFrame = this.sound ? this.sfSounds[0] : this.sfSounds[1];
            cc.AudioController.getInstance().enableSound(this.sound);
        },

        backClicked: function () {

            this.isRegisterLeaveRoom = !this.isRegisterLeaveRoom;
            if (this.isRegisterLeaveRoom) {
                cc.MB_Controller.getInstance().sendRequestOnHub(cc.MethodHubName.REGISTER_LEAVE_ROOM);
                this.nodeRegisterLeave.active = true;
                this.spriteBack.spriteFrame = cc.MB_Controller.getInstance().getSfBack(cc.TLMN_BACK.REGIST_LEAVE);
            } else {
                cc.MB_Controller.getInstance().sendRequestOnHub(cc.MethodHubName.UNREGISTER_LEAVE_ROOM);
                this.nodeRegisterLeave.active = false;
                this.spriteBack.spriteFrame =cc.MB_Controller.getInstance().getSfBack(cc.TLMN_BACK.UN_REGIST_LEAVE);
            }
        },
        resetUIBackButton: function() {
            this.nodeRegisterLeave.active = false;
            this.spriteBack.spriteFrame =cc.MB_Controller.getInstance().getSfBack(cc.TLMN_BACK.UN_REGIST_LEAVE);
        }

    })
}).call(this);