/*
 * Generated by BeChicken
 * on 8/12/2019
 * version v1.0
 */
(function () {
    var TLMN_Controller;
    TLMN_Controller = (function () {
        var instance;

        function TLMN_Controller() {

        }

        instance = void 0;

        TLMN_Controller.getInstance = function () {
            if (instance === void 0) {
                instance = this;
            }
            return instance.prototype;
        };

        // Set View
        TLMN_Controller.prototype.setTLMNView = function (TLMNView) {
            this.TLMN_View = TLMNView;
        };

        TLMN_Controller.prototype.resetUIBackButton = function () {
            return this.TLMN_View.resetUIBackButton();
        };


        TLMN_Controller.prototype.setTLMNLobbyView = function (TLMNLobbyView) {
            this.TLMN_LobbyView = TLMNLobbyView;
        };

        TLMN_Controller.prototype.setTLMNCardView = function (TLMNCardView) {
            this.TLMN_CardView = TLMNCardView;
        };

        TLMN_Controller.prototype.setTLMNPlayerInfoView = function (TLMNPlayerInfoView) {
            this.TLMN_PlayerInfoView = TLMNPlayerInfoView;
        };

        TLMN_Controller.prototype.setTLMNLayoutCardView = function (layoutCardView) {
            this.TLMN_LayoutCardView = layoutCardView;
        };

        TLMN_Controller.prototype.setTLMNButtonsView = function (buttonsView) {
            this.TLMN_ButtonsView = buttonsView;
        };

        TLMN_Controller.prototype.setTLMNAssetsView = function (assetsView) {
            this.TLMN_AssetsView = assetsView;
        };
        TLMN_Controller.prototype.setTLMNInfoView = function (TLMNInfoView) {
            this.TLMN_InfoView = TLMNInfoView;
        };
        // Tao card pool
        TLMN_Controller.prototype.setCardPool = function (cardPool) {
            return this.cardPool = cardPool;
        };

        TLMN_Controller.prototype.putToPool = function (card) {
            return this.cardPool.putToPool(card);
        };
        TLMN_Controller.prototype.clearPool = function () {
            return this.cardPool.clearPool();
        };

        //Tao bai
        TLMN_Controller.prototype.createCard = function () {
            return this.cardPool.createCard();
        };

        // Cap nhat game theo HubOn
        //TLMN_LobbyView
        TLMN_Controller.prototype.sendRequestOnHub = function (method, data1, data2) {
            if (this.TLMN_LobbyView)
                return this.TLMN_LobbyView.sendRequestOnHub(method, data1, data2);
        };


        TLMN_Controller.prototype.updateInfo = function (info) {
            //this.TLMN_CardView.updateCards(info);
            this.TLMN_InfoView.updateInfo(info);
        };

        //Reset game
        TLMN_Controller.prototype.resetGame = function () {
            this.TLMN_CardView.reset();
            this.TLMN_View.reset();
            this.TLMN_PlayerInfoView.reset();
        };

        //HUB ON NAME
        //joinGame
        TLMN_Controller.prototype.joinGame = function (info, timeInfo) {
            this.TLMN_InfoView.joinGame(info, timeInfo);
        };
        //playerLeave
        TLMN_Controller.prototype.playerLeave = function (info) {
            this.TLMN_LobbyView.playerLeave(info)
            this.TLMN_InfoView.playerLeave(info)
        };
        //playerJoin
        TLMN_Controller.prototype.playerJoin = function (info) {
            this.TLMN_InfoView.playerJoin(info)
        };
        //Envent showcard: danh bai
        TLMN_Controller.prototype.eventShowCard = function (accID, lstCards, currTurnCards) {
            return this.TLMN_InfoView.eventShowCard(accID, lstCards, currTurnCards);
        };
        //endRound
        TLMN_Controller.prototype.onEndRound = function () {
            return this.TLMN_InfoView.onEndRound();
        };
        //HubOn AllowChanNgay
        TLMN_Controller.prototype.onAllowChanNgay = function (lstCard) {
            return this.TLMN_InfoView.onAllowChanNgay(lstCard);
        };
        //HubOn updateConnectionStatus
        TLMN_Controller.prototype.updateConnectionStatus = function (data) {
            return this.TLMN_InfoView.updateConnectionStatus(data);
        };

        //HubOn updateAccount
        TLMN_Controller.prototype.updateAccount = function (dataAccounts) {
            return this.TLMN_InfoView.updateAccount(dataAccounts);
        };

        //HubOn showResult
        TLMN_Controller.prototype.showResult = function (result) {
            return this.TLMN_InfoView.showResult(result);
        };
        //Hien thi message chat
        TLMN_Controller.prototype.playerShowBubbleChat = function (message) {
            return this.TLMN_InfoView.playerShowBubbleChat(message);
        };
        //Xoa bai con lai tren ban
        TLMN_Controller.prototype.clearCardOnTable = function () {
            return this.TLMN_InfoView.clearCardOnTable();
        };
        // Event bo chon
        TLMN_Controller.prototype.onBoChon = function () {
            return this.TLMN_InfoView.onBoChon();
        };

        TLMN_Controller.prototype.updateCardPlayersOnDanhBai = function (info) {
            return this.TLMN_InfoView.updateCardPlayersOnDanhBai(info);
        };

        //cap nhat trang thai progress
        TLMN_Controller.prototype.updateProgressOwner = function (infoTime) {
            this.TLMN_InfoView.updateProgressOwner(infoTime);
        };
        //Cap nhat so du cua nguoi choi
        TLMN_Controller.prototype.updateBalancePlayers = function (players) {
            this.TLMN_InfoView.updateBalancePlayers(players)
        };

        //Chia bai
        TLMN_Controller.prototype.moveCards = function (players) {
            return this.TLMN_InfoView.moveCards(players)
        };
        //Chay hieu ung xep bai
        TLMN_Controller.prototype.runAnimationSortHandCard = function () {
            return this.TLMN_InfoView.runAnimationSortHandCard()
        };

        //Reset resetPlayersResultUI
        TLMN_Controller.prototype.resetPlayersResultUI = function (isStartGame) {
            return this.TLMN_InfoView.resetPlayersResultUI(isStartGame);
        };
        //Update updatePlayerBoLuot
        TLMN_Controller.prototype.updatePlayerBoLuot = function (accID) {
            return this.TLMN_InfoView.updatePlayerBoLuot(accID);
        };

        //Update bai sau khi xep
        TLMN_Controller.prototype.updateCardOnShortHand = function (handCards) {
            return this.TLMN_InfoView.updateCardOnShortHand(handCards);
        };

        TLMN_Controller.prototype.setOwnerID = function (ownerID) {
            this.ownerID = ownerID;
        };
        TLMN_Controller.prototype.getOwnerID = function () {
            return this.ownerID;
        };

        TLMN_Controller.prototype.setCurrTurn = function (isCurrTurn) {
            return this.isCurrTurn = isCurrTurn;
        };
        TLMN_Controller.prototype.getCurrTurn = function () {
            return this.isCurrTurn;
        };

        //Assets
        // Lay sprite thong bao
        TLMN_Controller.prototype.getNotify = function (type) {
            return this.TLMN_AssetsView.getNotify(type)
        };
        //Lay sprite thua
        TLMN_Controller.prototype.getLose = function (type) {
            return this.TLMN_AssetsView.getLose(type)
        };

        //Lay sprite back
        TLMN_Controller.prototype.getSfBack = function (type) {
            return this.TLMN_AssetsView.getSfBack(type)
        };
        TLMN_Controller.prototype.getSfBorderCard = function () {
            return this.TLMN_AssetsView.getSfBorderCard()
        };

        TLMN_Controller.prototype.getWinFont = function () {
            return this.TLMN_AssetsView.getWinFont();
        };

        TLMN_Controller.prototype.getLoseFont = function () {
            return this.TLMN_AssetsView.getLoseFont();
        };

        TLMN_Controller.prototype.getAvatarDef = function () {
            return this.TLMN_AssetsView.getAvatarDef();
        };

        TLMN_Controller.prototype.getSfCardBack = function () {
            return this.TLMN_AssetsView.getSfCardBack();
        };

        //Hien thi notify danh bai
        TLMN_Controller.prototype.showAllNofity = function (animationName, delayTime) {
            return this.TLMN_AssetsView.showAllNofity(animationName, delayTime);
        };
        // lay sprite bai
        TLMN_Controller.prototype.getSpriteCard = function (cardNunber, suite) {
            return this.TLMN_AssetsView.getSpriteCard(cardNunber, suite);
        };
        //TLMN_VIEW
        //Lay random vi tri bai danh trenh ban
        TLMN_Controller.prototype.getPositionCardOnTable = function () {
            return this.TLMN_LayoutCardView.getPositionCardOnTable();
        };

        TLMN_Controller.prototype.setBetRoom = function (betRoom) {
            return this.betRoom = betRoom;
        };

        TLMN_Controller.prototype.getBetRoom = function () {
            return this.betRoom;
        };

        //setButtonsView
        TLMN_Controller.prototype.setButtonsView = function (buttonsView) {
            this.TLMN_ButtonsView = buttonsView;
        };
        TLMN_Controller.prototype.showLayoutButton = function (accID, allowActions) {
            return this.TLMN_ButtonsView.showLayoutButton(accID, allowActions);
        };

        TLMN_Controller.prototype.disableAllButton = function () {
            return this.TLMN_ButtonsView.disableAllButton();
        };

        TLMN_Controller.prototype.enableButtonInturn = function () {
            return this.TLMN_ButtonsView.enableButtonInturn();
        };

        //push gia tri bai duoc chon vao mang
        TLMN_Controller.prototype.pushSelectedCard = function (orinalValue) {
            return this.selectdCard.push(orinalValue)
        };
        //bo gia tri bai duoc chon khoi mang
        TLMN_Controller.prototype.popSelectedCard = function (ordinalValue) {
            let index = -1;
            this.selectdCard.map((card, idx) => {
                if (card.ordinal == ordinalValue) {
                    index = idx;
                }
            }, this);
            this.selectdCard.splice(index, 1);
        };
        // Khoi tao mang bai duoc chon cua player
        TLMN_Controller.prototype.initSelectedCard = function () {
            return this.selectdCard = [];
        };
        // Lay danh sach bai duoc chon
        TLMN_Controller.prototype.getSelectedCard = function () {
            return this.selectdCard;
        };

        //Lay sprite bai theo ket qua tra ve
        TLMN_Controller.prototype.getSfGameResult = function (type) {
            return this.TLMN_AssetsView.getSfGameResult(type);
        };

        //Lay sprite bai theo value
        TLMN_Controller.prototype.getCardValueByNumber = function (value) {
            return this.TLMN_AssetsView.getCardValueByNumber(value);
        };

        TLMN_Controller.prototype.getColorDark = function () {
            return this.TLMN_AssetsView.getColorDark();
        };
        TLMN_Controller.prototype.getColorWhite = function () {
            return this.TLMN_AssetsView.getColorWhite();
        };

        //Lay sprite bai theo value
        TLMN_Controller.prototype.getCardValueByNumber = function (value) {
            return this.TLMN_AssetsView.getCardValueByNumber(value);
        };

        //Danh sach card tren ban
        TLMN_Controller.prototype.initListCardOnTable = function () {
            return this.listCardOnTable = [];
        };

        TLMN_Controller.prototype.getListCardOnTable = function () {
            return this.listCardOnTable;
        };
        TLMN_Controller.prototype.pushListCardOnTable = function (card) {
            return this.listCardOnTable.push(card);
        };

        //Bien kiem tra co cho phep danh chan hay khong
        TLMN_Controller.prototype.setIsAllowDanhChan = function (isAllow) {
            return this.isAllowDanhChan = isAllow;
        };
        TLMN_Controller.prototype.getAllowDanhChan = function () {
            return this.isAllowDanhChan;
        };
        //Trang thai cua player
        TLMN_Controller.prototype.setCurrPlayerStatus = function (status) {
            return this.currStatus = status;
        };
        TLMN_Controller.prototype.getCurrPlayerStatus = function () {
            return this.currStatus;
        };

        //Lay danh sach bai cua player hien tai
        TLMN_Controller.prototype.getListCurrCardPlayer = function () {
            return this.TLMN_InfoView.getListCurrCardPlayer();
        };
        //Bai co gia tri Max
        TLMN_Controller.prototype.setMaxCard = function (card) {
            return this.maxCard = card;
        };
        TLMN_Controller.prototype.getMaxCard = function () {
            return this.maxCard;
        };

        return TLMN_Controller;


    })();
    cc.TLMN_Controller = TLMN_Controller;
}).call(this);