/*
 * Generated by BeChicken
 * on 8/14/2019
 * version v1.0
 */
(function () {
    cc.CardItem = cc.Class({
        "extends": cc.Component,
        properties: {
            //Gia tri quan bai
            ordinalValue: -1,
            cardNumber: -1,
            cardSuite: 0,
            isClose: false //Up bai
        },

        onLoad: function () {
            this.duration = 0.3;
            //Chon bai
            this.selected = false;
            this.nodeBorder = new cc.Node('borderCard');
            this.spriteBorder = this.nodeBorder.addComponent(cc.Sprite);
            this.spriteBorder.spriteFrame = cc.TLMN_Controller.getInstance().getSfBorderCard();
            this.bindEvent();
        },
        // let event = cc.event.TOUCH_MOVE;
        bindEvent: function () {
            this.node.on('touchstart', function () {
                // this.eventTouchStart();
            }, this);
            this.node.on('touchmove', function () {
                //this.eventTouchMove();
            }, this);
            this.node.on('touchcancel', function () {
                //this.eventTouchEnd();
            }, this);
            this.node.on('touchend', function () {
                this.eventTouchEnd();
            }, this);
        },
        eventTouchStart: function () {
            this.mapX = [];
            this.layout = this.node.parent.getComponent(cc.Layout);
            this.lstNode = this.node.parent.children;
            this.lstNode.map((node, index) => {
                this.mapX.push(node.x);
                node.zIndex = index;
            });
            this.widthInit = this.layout.node.width;
        },
        eventTouchMove: function () {
            this.node.y = 0;
            this.node.x = this.getPositionXMove();
            this.layout.type = cc.Layout.Type.NONE;
            this.node.zIndex = this.getIndexNodeMove();
        },
        eventTouchEnd: function () {
            /* this.layout.node.width = this.widthInit;
             this.lstNode.map((node) => {
                 node.position = cc.v2(0, 0);
             });
             this.layout.type = cc.Layout.Type.HORIZONTAL;*/
            this.eventSelected();
        },
        eventSelected: function () {
            let moveBack = cc.moveTo(0.2, cc.v2(0, 0));
            let moveForward = cc.moveTo(0.2, cc.v2(0, 15));
            let move = null;
            move = (this.selected) ? moveBack : moveForward;
            //Neu chon thi push ordinalValue vao mang chon bai
            if (!this.selected) {
                cc.TLMN_Controller.getInstance().pushSelectedCard({node: this.node, ordinal: this.ordinalValue, cardNumber:this.cardNumber});
                this.nodeBorder.parent = this.node;
                if (cc.TLMN_Controller.getInstance().getCurrTurn() == true) {
                    try {
                        this.suggestCard(this.cardNumber, this.ordinalValue);
                    }catch (e) {

                    }

                }
            } else {
                // Bo ordinalValue cua la bai
                cc.TLMN_Controller.getInstance().popSelectedCard(this.ordinalValue);
                this.node.removeAllChildren();
            }
            //Hien thi button danh bai, bo chon
            cc.TLMN_Controller.getInstance().enableButtonInturn();
            this.selected = !this.selected;
            this.node.runAction(move);
        },
        autoSelected: function () {
            if (this.selected) {
                return;
            }
            cc.TLMN_Controller.getInstance().pushSelectedCard({node: this.node, ordinal: this.ordinalValue});
            this.nodeBorder.parent = this.node;
            let moveForward = cc.moveTo(0.2, cc.v2(0, 15));
            this.node.runAction(moveForward);
            this.selected = !this.selected;
        },
        //Goi y quan bai danh
        suggestCard: function (currCardNumber, currOrdinalSelected) {

            let maxCard = cc.TLMN_Controller.getInstance().getMaxCard();
            if (maxCard == null || maxCard.Max == undefined) {
                return;
            }
            //Danh bai con lai cua player
            let listCardsPlayer = cc.TLMN_Controller.getInstance().getListCurrCardPlayer();

            //Gia tri max cua bai
            let maxValue = maxCard.Max.OrdinalValue;
            let maxCardNumber = maxCard.Max.CardNumber;
            //So quan bai
            let count = maxCard.Count;

            //So luong bai con lai nho hon thi return
            if (listCardsPlayer.length < count) {
                return;
            }
            //Loai bai
            let type = maxCard.Type;
            //So luong quan bai can lay
            let remainGetCard = count - 1;
            let listCardSuggest = [];
            //Vong lap
            let directGet = 1;//Lay tu la bai hien tai tro di
            switch (type) {
                case cc.TLMNResultTypes.DOI:
                    if (currCardNumber < maxCardNumber) {
                        return;
                    }
                    //Tim them la bai co numberCard = numberCard chon
                    listCardsPlayer.map(nodeCard => {
                        let numberNodeCard = nodeCard.getComponent(cc.CardItem).cardNumber;
                        let ordinalNodeCard = nodeCard.getComponent(cc.CardItem).ordinalValue;

                        if (numberNodeCard === currCardNumber && remainGetCard > 0 && ordinalNodeCard != currOrdinalSelected) {
                            remainGetCard--;
                            if (numberNodeCard.ordinalValue > maxValue || currOrdinalSelected > maxValue) {
                                listCardSuggest.push(nodeCard);
                            }

                        }
                    }, this);
                    break;
                case cc.TLMNResultTypes.SAM:
                case cc.TLMNResultTypes.TU_QUY:
                    if (currCardNumber < maxCardNumber || listCardsPlayer.length < count) {
                        return;
                    }
                    //Tim them la bai co numberCard = numberCard chon
                    listCardsPlayer.map(nodeCard => {
                        let numberNodeCard = nodeCard.getComponent(cc.CardItem).cardNumber;
                        let ordinalNodeCard = nodeCard.getComponent(cc.CardItem).ordinalValue;
                        if (numberNodeCard === currCardNumber && remainGetCard > 0 && ordinalNodeCard != currOrdinalSelected) {
                            remainGetCard--;
                            listCardSuggest.push(nodeCard);
                        }
                    }, this);
                    break;
                case cc.TLMNResultTypes.DAY:
                    if (maxCardNumber - currCardNumber > remainGetCard || currCardNumber === 15) {
                        return;
                    }
                    if ((maxCardNumber - currCardNumber < remainGetCard) || (maxCardNumber - currCardNumber === 0 && currOrdinalSelected > maxValue)) {
                        directGet = -1;//Lay tu la bai hien tai tro lai
                    }
                    let cardNumberSelected = [];
                    //Neu directGet == 1
                    if (directGet === 1) {
                        for (let i = 1; i <= remainGetCard; i++) {
                            listCardsPlayer.map(nodeCard => {
                                let numberNodeCard = nodeCard.getComponent(cc.CardItem).cardNumber;
                                //Lay tu quan bai hien tai
                                if (numberNodeCard === currCardNumber + i && !cardNumberSelected.includes(numberNodeCard) && numberNodeCard < 15) {
                                    cardNumberSelected.push(numberNodeCard);
                                    listCardSuggest.push(nodeCard);
                                }
                            }, this);
                        }
                        // Lay lui

                        //Kiem tra listCardSuggest co thoa man hay ko
                        if (listCardSuggest.length !== count - 1) {
                            listCardSuggest = [];
                            remainGetCard = count - 1;
                            cardNumberSelected = [];
                        }

                        if (listCardSuggest.length === 0) {
                            //Lay tu bai hien tai tro xuong
                            for (let i = 1; i <= remainGetCard; i++) {
                                listCardsPlayer.map(nodeCard => {
                                    let numberNodeCard = nodeCard.getComponent(cc.CardItem).cardNumber;
                                    //Lay tu quan bai hien tai
                                    if (numberNodeCard === currCardNumber - i && !cardNumberSelected.includes(numberNodeCard)) {
                                        cardNumberSelected.push(numberNodeCard);
                                        listCardSuggest.push(nodeCard);
                                    }
                                }, this);
                            }
                        }

                    }

                    //Neu directGet == -1 Lay tu quan hien tai tro xuong
                    if (directGet === -1) {
                        let loopFirst = currCardNumber - maxCardNumber;
                        if (loopFirst > 0) {
                            for (let i = 1; i <= remainGetCard; i++) {
                                listCardsPlayer.map(nodeCard => {
                                    let numberNodeCard = nodeCard.getComponent(cc.CardItem).cardNumber;
                                    //Lay tu quan bai hien tai
                                    if (numberNodeCard === currCardNumber - i && !cardNumberSelected.includes(numberNodeCard)) {
                                        cardNumberSelected.push(numberNodeCard);
                                        listCardSuggest.push(nodeCard);
                                    }
                                }, this);
                            }//End loop first

                            //Lay tu bai hien tai tro len
                            for (let i = 1; i <= remainGetCard - listCardSuggest.length; i++) {
                                listCardsPlayer.map(nodeCard => {
                                    let numberNodeCard = nodeCard.getComponent(cc.CardItem).cardNumber;
                                    //Lay tu quan bai hien tai
                                    if (numberNodeCard === currCardNumber + i && !cardNumberSelected.includes(numberNodeCard) && numberNodeCard < 15) {
                                        cardNumberSelected.push(numberNodeCard);
                                        listCardSuggest.push(nodeCard);
                                    }
                                }, this);
                            }

                            // Kiem tra day truoc khong hop le  -> reset lai mang
                            if (listCardSuggest.length < count - 1) {
                                listCardSuggest = [];
                                remainGetCard = count - 1;
                                cardNumberSelected = [];
                            } else {
                                //Kiem tra day da hop le hay chua
                                cardNumberSelected.push(currCardNumber);
                                //Xap xep tang dan
                                cardNumberSelected.sort((a, b) => a - b);
                                let checkOk = true;
                                for (let i = 0; i < count - 1; i++) {
                                    if (cardNumberSelected[i] + 1 !== cardNumberSelected[i + 1]) {
                                        checkOk = false;
                                        break;
                                    }
                                }
                                if (checkOk === false) {
                                    listCardSuggest = [];
                                    remainGetCard = count - 1;
                                    cardNumberSelected = [];
                                }
                            }

                            //Lay tu bai hien tai tro di
                            if (currCardNumber < 14 && listCardSuggest.length !== count - 1) { //La bai hien tai khac 1 va 2
                                for (let i = 1; i <= remainGetCard; i++) {
                                    listCardsPlayer.map(nodeCard => {
                                        let numberNodeCard = nodeCard.getComponent(cc.CardItem).cardNumber;
                                        if (numberNodeCard === currCardNumber + i && !cardNumberSelected.includes(numberNodeCard) && numberNodeCard < 15) {
                                            cardNumberSelected.push(numberNodeCard);
                                            listCardSuggest.push(nodeCard);
                                        }

                                    }, this);
                                }
                            }

                        } else {
                            //Lay tu bai hien tai tro di
                            if (currCardNumber <= 14) { //La bai hien tai khac 1 va 2

                                //Lay lui
                                for (let i = 1; i <= remainGetCard; i++) {
                                    listCardsPlayer.map(nodeCard => {
                                        let numberNodeCard = nodeCard.getComponent(cc.CardItem).cardNumber;
                                        if (numberNodeCard === currCardNumber + i && !cardNumberSelected.includes(numberNodeCard) && numberNodeCard < 15) {
                                            cardNumberSelected.push(numberNodeCard);
                                            listCardSuggest.push(nodeCard);
                                        }

                                    }, this);
                                }
                                if (listCardSuggest.length < count - 1) {
                                    for (let i = 1; i <= remainGetCard - listCardSuggest.length; i++) {
                                        listCardsPlayer.map(nodeCard => {
                                            let numberNodeCard = nodeCard.getComponent(cc.CardItem).cardNumber;
                                            if (numberNodeCard === currCardNumber - i && !cardNumberSelected.includes(numberNodeCard)) {
                                                cardNumberSelected.push(numberNodeCard);
                                                listCardSuggest.push(nodeCard);
                                            }

                                        }, this);
                                    }

                                }

                                //Lay tien
                                if (listCardSuggest.length < count - 1) {
                                    listCardSuggest = [];
                                    cardNumberSelected = [];
                                    for (let i = 1; i <= remainGetCard; i++) {
                                        listCardsPlayer.map(nodeCard => {
                                            let numberNodeCard = nodeCard.getComponent(cc.CardItem).cardNumber;
                                            if (numberNodeCard === currCardNumber - i && !cardNumberSelected.includes(numberNodeCard)) {
                                                cardNumberSelected.push(numberNodeCard);
                                                listCardSuggest.push(nodeCard);
                                            }

                                        }, this);
                                    }
                                    if (listCardSuggest.length < count - 1) {
                                        for (let i = 1; i <= remainGetCard - listCardSuggest.length; i++) {
                                            listCardsPlayer.map(nodeCard => {
                                                let numberNodeCard = nodeCard.getComponent(cc.CardItem).cardNumber;
                                                if (numberNodeCard === currCardNumber + i && !cardNumberSelected.includes(numberNodeCard) && numberNodeCard < 15) {
                                                    cardNumberSelected.push(numberNodeCard);
                                                    listCardSuggest.push(nodeCard);
                                                }

                                            }, this);
                                        }

                                    }

                                }

                                //Kiem tra day da hop le hay chua
                                cardNumberSelected.push(currCardNumber);
                                //Xap xep tang dan
                                cardNumberSelected.sort((a, b) => a - b);
                                let checkOk = true;
                                for (let i = 0; i < count - 1; i++) {
                                    if (cardNumberSelected[i] + 1 !== cardNumberSelected[i + 1]) {
                                        checkOk = false;
                                        break;
                                    }
                                }
                                if (checkOk === false) {
                                    listCardSuggest = [];
                                }


                            }
                        }

                    }
                    break;
                case cc.TLMNResultTypes.BA_DOI_THONG:

                    //Goi y 4 doi thong
                    let listBonDoiThong1 = this.getListBonDoiThong(listCardsPlayer);

                    if (listBonDoiThong1.length > 0) {
                        //Kiem tra bai chon co nam trong mang hay ko
                        let checkContainBonDoiThong1 = false;
                        listBonDoiThong1.map(card => {
                            let cardNumber = card.getComponent(cc.CardItem).cardNumber;
                            if (cardNumber === currCardNumber && checkContainBonDoiThong1 === false) {
                                checkContainBonDoiThong1 = true;
                            }
                        }, this);

                        //Neu bai dc chon nam trong danh sach doi thong thi hien thi
                        if (checkContainBonDoiThong1) {
                            listBonDoiThong1.map(card => {
                                let cardItem = card.getComponent(cc.CardItem);
                                if (cardItem.ordinalValue !== currOrdinalSelected) {
                                    card.getComponent(cc.CardItem).autoSelected();
                                }
                            }, this);
                        }

                        return;
                    }


                    //Goi y 3 doi thong
                    let listDoiThong = this.getListDoiThong(listCardsPlayer);

                    if (listDoiThong.length === 0) {
                        return;
                    }

                    //Kiem tra bai chon co nam trong mang hay ko
                    let checkContain = false;
                    let isGreathanCurrMax = false;
                    listDoiThong.map(card => {
                        let cardNumber = card.getComponent(cc.CardItem).cardNumber;
                        let ordinalCard = card.getComponent(cc.CardItem).ordinalValue;
                        if (ordinalCard > maxValue) {
                            isGreathanCurrMax = true;
                        }
                        if (cardNumber === currCardNumber && checkContain === false) {
                            checkContain = true;
                        }
                    }, this);

                    //Neu bai dc chon nam trong danh sach doi thong thi hien thi
                    if (checkContain) {
                        listDoiThong.map(card => {
                            let cardItem = card.getComponent(cc.CardItem);
                            if (isGreathanCurrMax) {
                                if (cardItem.ordinalValue !== currOrdinalSelected) {
                                    card.getComponent(cc.CardItem).autoSelected();
                                }
                            }
                        }, this)
                    }
                    return;
                    break;
                case cc.TLMNResultTypes.BON_DOI_THONG:
                case cc.TLMNResultTypes.DOI_HEO:
                    let listBonDoiThong = this.getListBonDoiThong(listCardsPlayer);

                    if (listBonDoiThong.length === 0) {
                        return;
                    }

                    //Kiem tra bai chon co nam trong mang hay ko
                    let checkContain3 = false;
                    let isGreathanCurrMax3 = false;
                    listBonDoiThong.map(card => {
                        let cardNumber = card.getComponent(cc.CardItem).cardNumber;
                        let ordinalCard = card.getComponent(cc.CardItem).ordinalValue;
                        if (ordinalCard > maxValue) {
                            isGreathanCurrMax3 = true;
                        }
                        if (cardNumber === currCardNumber && checkContain3 === false) {
                            checkContain3 = true;
                        }
                    }, this);

                    //Neu bai dc chon nam trong danh sach doi thong thi hien thi
                    if (checkContain3) {
                        listBonDoiThong.map(card => {
                            let cardItem = card.getComponent(cc.CardItem);
                            if (isGreathanCurrMax3 || type === cc.TLMNResultTypes.DOI_HEO) {
                                if (cardItem.ordinalValue !== currOrdinalSelected) {
                                    card.getComponent(cc.CardItem).autoSelected();
                                }
                            }
                        }, this)
                    }
                    return;
                    break;
                case cc.TLMNResultTypes.HEO:

                    //Goi y 4 doi thong
                    let listBonDoiThong2 = this.getListBonDoiThong(listCardsPlayer);

                    if (listBonDoiThong2.length > 0) {
                        //Kiem tra bai chon co nam trong mang hay ko
                        let checkContainBonDoiThong2 = false;
                        listBonDoiThong2.map(card => {
                            let cardNumber = card.getComponent(cc.CardItem).cardNumber;
                            if (cardNumber === currCardNumber && checkContainBonDoiThong2 === false) {
                                checkContainBonDoiThong2 = true;
                            }
                        }, this);

                        //Neu bai dc chon nam trong danh sach doi thong thi hien thi
                        if (checkContainBonDoiThong2) {
                            listBonDoiThong2.map(card => {
                                let cardItem = card.getComponent(cc.CardItem);
                                if (cardItem.ordinalValue !== currOrdinalSelected) {
                                    card.getComponent(cc.CardItem).autoSelected();
                                }
                            }, this);
                            return;
                        }
                    }

                    //Goi y tu quy
                    let listTuQuy = this.getTuQuy(listCardsPlayer, currCardNumber);
                    if (listTuQuy !== null) {
                        listTuQuy.map(card => {
                            let cardItem = card.getComponent(cc.CardItem);
                            if (cardItem.ordinalValue !== currOrdinalSelected) {
                                cardItem.autoSelected();
                            }
                        });
                        return;
                    }

                    //Goi y 3 doi thong
                    let listBaDoiThong2 = this.getListDoiThong(listCardsPlayer);

                    if (listBaDoiThong2.length > 0) {
                        //Kiem tra bai chon co nam trong mang hay ko
                        let checkContainBaDoiThong2 = false;
                        listBaDoiThong2.map(card => {
                            let cardNumber = card.getComponent(cc.CardItem).cardNumber;
                            if (cardNumber === currCardNumber && checkContainBaDoiThong2 === false) {
                                checkContainBaDoiThong2 = true;
                            }
                        }, this);

                        //Neu bai dc chon nam trong danh sach doi thong thi hien thi
                        if (checkContainBaDoiThong2) {
                            listBaDoiThong2.map(card => {
                                let cardItem = card.getComponent(cc.CardItem);
                                if (cardItem.ordinalValue !== currOrdinalSelected) {
                                    card.getComponent(cc.CardItem).autoSelected();
                                }
                            }, this)
                        }
                        return;
                    }


                    break;
            }

            if (listCardSuggest.length === count - 1) {
                //Kiem tra bai chon co lon hon bai trc hay khog
                let check = listCardSuggest.filter(card => card.getComponent(cc.CardItem).ordinalValue > maxValue);
                if (check.length > 0 || currOrdinalSelected > maxValue) {
                    listCardSuggest.map(card => {
                        card.getComponent(cc.CardItem).autoSelected();
                    });
                }

            }

        },
        getListDoiThong: function (listCardsPlayer) {
            let listDoiThong = [];
            //Kiem tra bai co doi thong hay khong
            let listDouble = [];//Danh sach cac doi
            let tmp = [];
            for (let i = 3; i <= 14; i++) {
                let filterDoubleCard = listCardsPlayer.filter(nodeCard => nodeCard.getComponent(cc.CardItem).cardNumber == i);
                if (filterDoubleCard.length > 1) {
                    //Neu la 3 cay thi chi lay 2 cay
                    if (filterDoubleCard.length > 2) {
                        filterDoubleCard = filterDoubleCard.slice(0, 2);
                    }
                    // tmp[i-3] = filterDoubleCard;
                    listDouble[i - 3] = (filterDoubleCard.length == 0) ? [] : filterDoubleCard;
                }
            }
            if (listDouble.length < 3) {
                return listDoiThong;
            }

            for (let i = 0; i < listDouble.length - 2; i++) {
                if (listDouble[i] && listDouble[i + 1] && listDouble[i + 2]) {
                    listDoiThong = [...listDouble[i], ...listDouble[i + 1], ...listDouble[i + 2]];
                }
            }
            return listDoiThong;
        },
        getListBonDoiThong: function (listCardsPlayer) {
            let listBonDoiThong = [];
            //Kiem tra bai co doi thong hay khong
            let listDouble = [];//Danh sach cac doi
            let tmp = [];
            for (let i = 3; i <= 14; i++) {
                let filterDoubleCard = listCardsPlayer.filter(nodeCard => nodeCard.getComponent(cc.CardItem).cardNumber == i);
                if (filterDoubleCard.length > 1) {
                    //Neu la 3 cay thi chi lay 2 cay
                    if (filterDoubleCard.length > 2) {
                        filterDoubleCard = filterDoubleCard.slice(0, 2);
                    }
                    // tmp[i-3] = filterDoubleCard;
                    listDouble[i - 3] = (filterDoubleCard.length == 0) ? [] : filterDoubleCard;
                }
            }
            if (listDouble.length < 4) {
                return listBonDoiThong;
            }

            for (let i = 0; i < listDouble.length - 3; i++) {
                if (listDouble[i] && listDouble[i + 1] && listDouble[i + 2] && listDouble[i + 3]) {
                    listBonDoiThong = [...listDouble[i], ...listDouble[i + 1], ...listDouble[i + 2], ...listDouble[i + 3]];
                }
            }
            return listBonDoiThong;
        },
        getTuQuy: function (listCardsPlayer, currNumber) {
            let filterDoubleCard = listCardsPlayer.filter(nodeCard => nodeCard.getComponent(cc.CardItem).cardNumber == currNumber);
            if (filterDoubleCard.length == 4) {
                return filterDoubleCard;
            }
            return null;
        },
        getPositionXMove: function () {
            if (this.node.x < this.mapX[0]) {
                return this.mapX[0] - 40;
            }
            if (this.node.x > this.mapX[this.mapX.length - 1]) {
                return this.mapX[this.mapX.length - 1] + 40;
            }
            return this.node.x;
        },
        //lay index cua node khi di chuyen
        getIndexNodeMove: function () {
            for (let x in this.mapX) {
                if (this.mapX[x] > this.node.x) {
                    if (x == 0)
                        return x - 1;
                    return x;
                }
            }
        },
        // Set Spirte frame la bai up
        initCard: function () {
            let sfCard = cc.TLMN_Controller.getInstance().getSfCardBack();
            this.node.getComponent(cc.Sprite).spriteFrame = sfCard;
        },

        //Up bai
        onCloseCard: function () {
            if (!this.isClose) {
                this.initCard();
                this.node.color = cc.TLMN_Controller.getInstance().getColorWhite();
                this.isClose = true;
            }
        },

        // Hien thi card
        onShowCard: function () {
            let sfCard = cc.TLMN_Controller.getInstance().getSpriteCard(this.cardNumber, this.cardSuite);
            this.node.getComponent(cc.Sprite).spriteFrame = sfCard;
        },

        // Reset bai ve trang thai ban dau
        reset: function () {
            this.selected = false;
            this.node.removeAllChildren();
            let moveBack = cc.moveTo(0.2, cc.v2(0, 0));
            if (!cc.game.isPaused()) {
                this.node.runAction(moveBack);
            } else {
                this.node.position = cc.v2(0, 0);
            }
            this.isClose = false;
        },
        moveTo: function (endPosition) {
            this.node.opacity = 200;
            var action = cc.moveTo(this.duration, endPosition);
            action.easing(cc.easeIn(0.3));

            var callback = cc.callFunc(this.moveFinished, null, this.node);


            this.node.runAction(cc.sequence(action, callback));
        },

        setPosition: function (endPosition) {
            this.node.position = endPosition;
        },

        moveToEnd: function (endPosition) {
            if (this.node) {
                this.node.opacity = 100;

                var action = cc.moveTo(0.5, endPosition);
                action.easing(cc.easeOut(1.0));

                var callback = cc.callFunc(this.moveToEndFinished, null, this.node);

                this.node.runAction(cc.sequence(action, callback));
            }
        },

        moveFinished: function (node) {
            node.opacity = 255;
            node.destroy();
        },

        moveToEndFinished: function (node) {
            // node.stopAllActions();
            cc.TLMN_Controller.getInstance().putToPool(node);git
        }
    });
}).call(this);
