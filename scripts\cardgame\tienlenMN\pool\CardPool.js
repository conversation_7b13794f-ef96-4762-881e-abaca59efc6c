/*
 * Generated by BeChicken
 * on 8/12/2019
 * version v1.0
 */
(function () {
    cc.CardPool = cc.Class({
        "extends": cc.Component,
        properties: {
            prefab: cc.Prefab,
        },

        onLoad: function () {
            this.createNodePool();
            cc.TLMN_Controller.getInstance().setCardPool(this);
        },

        createNodePool: function () {
            this.nodePool = new cc.NodePool();
            let initCount = 26;
            for (let i = 0; i < initCount; ++i) {
                this.nodePool.put(cc.instantiate(this.prefab));
            }
        },

        putToPool: function (node) {
            this.nodePool.put(node);
        },

        clearPool: function () {
            if (this.nodePool)
                this.nodePool.clear();
        },

        createCard: function () {
            let node = null;
            if (this.nodePool.size() > 0) {
                node = this.nodePool.get();
            } else {
                node = cc.instantiate(this.prefab);
            }

            return node;
        },
    });
}).call(this);
