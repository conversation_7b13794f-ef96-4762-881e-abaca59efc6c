/*
 * Generated by BeChicken
 * on 8/23/2019
 * version v1.0
 */
(function () {
    cc.TLMNHelpView = cc.Class({
        "extends": cc.PopupBase,
        properties: {},

        onLoad: function () {
            this.node.parent = cc.find('Canvas');
            this.animation = this.node.getComponent(cc.Animation);
            this.node.zIndex = cc.NoteDepth.POPUP_TAIXIU;
        },

        closeFinished: function () {
            cc.TLMN_PopupController.getInstance().destroyHelpView();
            this.node.removeFromParent();
        },
    });
}).call(this);
