/*
 * Generated by Be<PERSON>hicken
 * on 8/23/2019
 * version v1.0
 */
(function () {
    var TLMN_PopupController;

    TLMN_PopupController = (function () {
        var instance;

        function TLMN_PopupController() {
        }

        instance = void 0;

        TLMN_PopupController.getInstance = function () {
            if (instance === void 0) {
                instance = this;
            }
            return instance.prototype;
        };

        TLMN_PopupController.prototype.setTLMN_PopupView = function (TLMN_PopupView) {
            return this.TLMN_PopupView = TLMN_PopupView;
        };
        TLMN_PopupController.prototype.getTLMN_PopupView = function () {
            return this.TLMN_PopupView;
        };

        TLMN_PopupController.prototype.createHelpView = function () {
            return this.TLMN_PopupView.createHelpView();
        };

        TLMN_PopupController.prototype.destroyHelpView = function () {
            return this.TLMN_PopupView.destroyHelpView();
        };

        return TLMN_PopupController;

    })();

    cc.TLMN_PopupController = TLMN_PopupController;

}).call(this);
