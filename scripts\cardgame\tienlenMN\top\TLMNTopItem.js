/*
 * Generated by BeChicken
 * on 8/14/2019
 * version v1.0
 */
(function () {
    cc.TLMNTopItem = cc.Class({
        "extends": cc.Component,
        properties: {
            iconRank: cc.Sprite,
            lbRank: cc.Label,
            lbSID: cc.Label,
            lbNickName: cc.Label,
            lbTotalWin: cc.Label,
            // avatarTop: cc.Avatar,
            spTop: [cc.SpriteFrame]
        },

        updateItem: function(item, itemID) {
            //this.sprite.enabled = itemID % 2 !== 0;
            // if(itemID == 0) {
            //     this.iconRank.spriteFrame = this.spTop[0];
            // }
            // if(itemID == 1) {
            //     this.iconRank.spriteFrame = this.spTop[1];
            // }
            // if(itemID == 2) {
            //     this.iconRank.spriteFrame = this.spTop[2];
            // }
            // this.iconRank.spriteFrame.setOriginalSize(cc.v2(37, 37));
            // if(itemID < 3) {
            //     this.lbRank.node.active = false;
            //     this.iconRank.node.active = true;
            // }else {
            //     this.lbRank.node.active = true;
            //     this.iconRank.node.active = false;
            //     this.lbRank.string = itemID + 1;
            // }

            this.lbRank.string = itemID + 1;

            let avatar = (item.Avatar > 0) ? item.Avatar : 1;
            // this.avatarTop.setAvatar(cc.AccountController.getInstance().getAvatarImage(avatar));
            this.lbSID.string = cc.Config.getInstance().getServiceNameNoFormat(item.ServiceID);
            this.lbNickName.string = item.DisplayName;
            this.lbTotalWin.string = item.AwardFormat;

            this.item = item;
            this.itemID = itemID;
        },
    });
}).call(this);
