/*
 * Generated by BeChicken
 * on 8/14/2019
 * version v1.0
 */
(function () {
    cc.BCTopView = cc.Class({
        "extends": cc.Component,
        properties: {
            TLMNTopListView: cc.TLMNTopListView,
        },

        onEnable: function () {
            var delay = 0.2;
            cc.director.getScheduler().schedule(function () {
                this.getTopSessionWinners();
            }.bind(this), this, 1, 0, delay, false);
        },

        getTopSessionWinners: function () {
            var getWinnersCommand = new cc.TLMNWinnerCommand;
            getWinnersCommand.execute(this);
        },

        onTLMNGetBigWinnerResponse: function (response) {
            let list = response;
            if(list != null && list.length > 0) {
                // var list = this.exData;
                this.TLMNTopListView.resetList();
                this.TLMNTopListView.initialize(response);
            }

        },
    });
}).call(this);