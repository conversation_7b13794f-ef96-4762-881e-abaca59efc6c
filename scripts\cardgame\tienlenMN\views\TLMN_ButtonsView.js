/*
 * Generated by BeChicken
 * on 8/12/2019
 * version v1.0
 */
(function () {
    cc.TLMN_ButtonsView = cc.Class({
        extends: cc.Component,
        properties: {
            layoutButton: cc.Node,
            buttonBatDau: cc.But<PERSON>,
            buttonBoLuot: cc.But<PERSON>,
            buttonChon: cc.Button,
            buttonXepBai: cc.Button,
            buttonDanh: cc.Button,
        },
        onLoad: function () {
            cc.TLMN_Controller.getInstance().setButtonsView(this);
            if(this.buttonBatDau) {
                this.buttonBatDau.node.active = false;
            }
            this.allowDanhBai = false;
        },
        onDestroy: function () {

        },
        //Hien thi button theo trang thai
        enableByState: function (state, playerStatus) {
            if (playerStatus != cc.TLMNPlayerStatus.INGAME) {
                this.layoutButton.active = false;
            } else {
                this.layoutButton.active = true;
            }
        },

        //Xep bai
        onSortCard: function () {
            //Run animation xep bai
            cc.TLMN_Controller.getInstance().runAnimationSortHandCard();
            cc.TLMN_Controller.getInstance().sendRequestOnHub(cc.MethodHubName.SORT_HAND_CARDS);
        },

        //Danh bai
        onBanhBai: function () {
            //1. Lay danh sach bai duoc chon
            //2. Gui len oridinalValue cua bai
            let lstCard = cc.TLMN_Controller.getInstance().getSelectedCard();
            let ordinalValue = [];
            let lstNodeCard = [];
            if (lstCard.length === 0)
                return;
            lstCard.map(card => {
                if(card.ordinal !== -1) {
                    ordinalValue.push(card.ordinal);
                    lstNodeCard.push(card.node);
                }
            });
            cc.TLMN_Controller.getInstance().sendRequestOnHub(cc.MethodHubName.DANH_BAI, ordinalValue);

        },

        //Bo luot
        onBoLuot: function () {
            cc.TLMN_Controller.getInstance().sendRequestOnHub(cc.MethodHubName.BO_LUOT);
            this.buttonBoLuot.interactable = false;
        },

        //Bo chon
        onBoChon: function () {
            cc.TLMN_Controller.getInstance().onBoChon();
            this.enableButtonBoChon(false);
            this.enableButtonDanhBai(false);
        },

        onStartGame: function () {
            cc.TLMN_Controller.getInstance().sendRequestOnHub(cc.MethodHubName.START_GAME);
            this.buttonBatDau.node.active = false;
        },

        //Hien thi layout button
        showLayoutButton: function (accID, allowActions) {
            if (accID == 0) {
                this.disableAllButton();
                this.buttonXepBai.interactable = false;
                return;
            }
            this.allowDanhBai = false;
            this.layoutButton.active = true;
            //Neu cho phep danh chan thi ko an nut
            if (!cc.TLMN_Controller.getInstance().getAllowDanhChan()) {
                //Disable tat ca cac button
                this.disableAllButton();
            }
            //Neu ko phai luot cua player thi disable tat ca cac nut
            if (accID != cc.LoginController.getInstance().getUserId()) {
                //Neu la start game thi an nut xep bai
                if(allowActions.includes(cc.TLMNActionName.START_GAME)) {
                    this.buttonXepBai.interactable = false;
                }
                return;
            }
            //Map action name
            allowActions.map(action => {
                switch (action) {
                    case cc.TLMNActionName.DANH_BAI:
                        this.allowDanhBai = true;
                        //let lstCard = cc.TLMN_Controller.getInstance().getSelectedCard();
                        //let isEnable = lstCard.length > 0;
                        this.enableButtonInturn();
                        // this.enableButtonDanhBai(isEnable);
                        break;
                    case cc.TLMNActionName.BO_LUOT:
                        this.enableButtonBoLuot(true);
                        break;
                    case cc.TLMNActionName.START_GAME:
                        this.enableButtonBatDau(true);
                        this.buttonXepBai.interactable = false;
                        break;
                    case cc.TLMNActionName.WAIT:
                        this.layoutButton.active = false;
                        break;
                }
            }, this);
        },
        enableButtonInturn: function () {
            let lstSelectedCard = cc.TLMN_Controller.getInstance().getSelectedCard();
            let isEnable = lstSelectedCard.length > 0;
            this.enableButtonBoChon(isEnable);
            this.enableButtonDanhBai(isEnable);
        },
        enableButtonBatDau: function (enable) {
            if(this.buttonBatDau) {
                this.buttonBatDau.node.active = enable;
            }
        },
        enableButtonBoChon: function (enable) {
            this.buttonChon.interactable = enable;
        },
        enableButtonDanhBai: function (enable) {
            // Kiem tra phien nguoi choi co dc danh bai hay ko
            // Neu dc danh thi hien thi button DanhBai
            // Ko thi disable
            if (this.allowDanhBai) {
                this.buttonDanh.interactable = enable;
            } else {
                this.buttonDanh.interactable = false;
            }

        },
        enableButtonBoLuot: function () {
            this.buttonBoLuot.interactable = true;
        },
        disableAllButton: function () {
            this.enableButtonBatDau(false);
            this.buttonBoLuot.interactable = false;
            this.buttonChon.interactable = false;
            this.buttonDanh.interactable = false;
            this.buttonXepBai.interactable = true;
        }

    })
}).call(this);