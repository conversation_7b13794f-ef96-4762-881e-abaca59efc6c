/*
 * Generated by BeChicken
 * on 8/14/2019
 * version v1.0
 */
const TWEEN = cc.tween;
(function () {
    cc.TLMN_InfoView = cc.Class({
        extends: cc.Component,
        properties: {
            //phien
            lbSID: cc.Label,
            //roomID
            lbRoomID: cc.Label,
            //roomID
            lbTableId: cc.Label,
            //giai doan (đặt cửa, kết quả...)
            lbInfo: cc.Label,

            lbPlayerStatus: cc.Label,

            //players
            TLMNPlayers: [cc.TLMNPlayer],

            //sprite card back
            spriteCardBack: cc.SpriteFrame,

            dealer: sp.Skeleton,
        },

        onLoad: function () {
            this.interval = null;
            cc.TLMN_Controller.getInstance().setTLMNInfoView(this);

            this.maxPlayer = this.TLMNPlayers.length;

            this.animInfo = this.lbInfo.node.parent.getComponent(cc.Animation);

            this.currentState = null;
            this.currPlayer = null;

        },
        hidePlayerStatus: function () {
            this.lbPlayerStatus.node.active = false;
        },

        //HubOn - joinGame
        joinGame: function (info, timeInfo) {
            // set chuong
            cc.TLMN_Controller.getInstance().setOwnerID(info.OwnerID);
            // update hien thi layout bet
            // cc.TLMN_Controller.getInstance().enableLayoutBet(true);

            //lay ve mang vi tri player
            this.positions = info.Positions;

            this.countPlayer = 0;
            //luu vi tri player tren UI
            this.positionsUI = [0, 0, 0, 0];
            //tim index của owner
            this.onwerIndex = 0;

            if(cc.RoomController.getInstance().getGameId().toString() === cc.GameId.TIEN_LEN_MN) {
                //Vi tri player hien tai trong ban choi
                let posCurrPlayer = this.positions.indexOf(cc.LoginController.getInstance().getUserId());

                //Tach mang tu vi tri nguoi choi den cuoi mang
                let firstArrPosition = this.positions.slice(posCurrPlayer, this.positions.length);
                //lay vi tri tu dau den vi tri nguoi choi
                let lastArrPosition = this.positions.splice(0, posCurrPlayer);
                this.positionsUI = [...firstArrPosition, ...lastArrPosition];
            }

            if(cc.RoomController.getInstance().getGameId().toString() === cc.GameId.TIEN_LEN_MN_SOLO) {
                this.countPlayer = 0;
                this.positionsUI[this.countPlayer] = cc.LoginController.getInstance().getUserId();
                this.countPlayer++;
                for (var i = 0; i < this.maxPlayer; i++) {
                    var accID = this.positions[i];
                    //add vi tri cac accID khac vao position tren UI
                    if (accID > 0 && accID !== cc.LoginController.getInstance().getUserId()) {
                        this.positionsUI[this.countPlayer] = accID;
                        this.countPlayer++;
                    }
                }

            }

            //Cap nhat thong tin bet
            //GameLoop
            let gameLooop = info.GameLoop;
            let pharse = gameLooop.Phrase;
            let timeElapse = gameLooop.Elapsed;

            //lay ve players
            var players = info.Players;

            for (var i = 0; i < this.maxPlayer; i++) {
                var accID = this.positionsUI[i];
                //cac vi tri co nguoi choi: accID > 0
                if (accID > 0) {
                    this.registerPlayer(this.getIndexUIBetByAccID(accID), players[accID], players[accID].Status);
                    //Kiem tra trang thai cua player neu la viewer thi thong bao cho van moi, an layout bet
                    if (accID == cc.LoginController.getInstance().getUserId()) {
                        if (players[accID].Status == 0) {
                            if (this.countPlayer > 1) {
                                this.lbPlayerStatus.node.active = true;
                            }
                            this.lbPlayerStatus.string = "CHỜ VÁN MỚI";
                            let currPlayer = this.TLMNPlayers[this.getIndexUIBetByAccID(accID)];

                            currPlayer.setAvatarBlur();
                        }

                    }
                }
            }

            this.updateRoomId(info.SessionID);


            // Cap nhat trang thai game
            this.updateStatusGame(pharse);
            //get list accID
            //let lstAcc = Object.keys(betLogs);
            let currID = cc.LoginController.getInstance().getUserId();
            let currPlayer = players[currID];
            //Hien thi progress cua nguoi choi ke tiep
            let turnAccount = timeInfo.State.AccountID;
            //Set trang thai cho currPlayer
            cc.TLMN_Controller.getInstance().setCurrPlayerStatus(currPlayer.Status);
            if (!this.isInGame(currPlayer)) {
                turnAccount = 0;
            }
            //Hien thi layoutButton neu turnAccount la current player
            cc.TLMN_Controller.getInstance().showLayoutButton(turnAccount, timeInfo.State.AllowedActions);
            let timeElapsed = [timeInfo.TotalTime, timeInfo.Time];
            let playerTurn = this.TLMNPlayers[this.getIndexUIBetByAccID(timeInfo.State.AccountID)];
            if (playerTurn) {
                playerTurn.updateProgressOwnerJoinGame(timeElapsed);
            }

            //Hien thi bai da dc danh phien truoc
            let currTurnCard = gameLooop.CurrTurnCards;

            if (currTurnCard.length == 0)
                return;

            //Xap xep lai mang
            currTurnCard.reverse();

            currTurnCard.forEach(function (playerCard, index) {
                let cards = playerCard.Value.Cards;

                //Mac dinh vi tri wrap bai la cua player hien tai
                let posEnd = cc.TLMN_Controller.getInstance().getPositionCardOnTable();
                //Random huong xoay cua bai
                let randomRotation = cc.Tool.getInstance().getRandomFromTo(-15, 15);

                if (cards.length == 0)
                    return;

                //Khoi tao bai cua player khac danh
                cards.map((cardValue, idx) => {
                    //Tao card
                    let nodeCard = cc.TLMN_Controller.getInstance().createCard();
                    let spriteCard = cc.TLMN_Controller.getInstance().getCardValueByNumber(cardValue);
                    nodeCard.getComponent(cc.Sprite).spriteFrame = spriteCard;
                    nodeCard.position = posEnd;
                    nodeCard.x = posEnd.x + 30 * idx;
                    nodeCard.setScale(cc.v2(0.5, 0.5));
                    nodeCard.rotation = randomRotation;
                    nodeCard.parent = cc.TLMN_Controller.getInstance().TLMN_LayoutCardView.layoutContentCard;
                    nodeCard.off(cc.Node.EventType.TOUCH_END);
                    cc.TLMN_Controller.getInstance().pushListCardOnTable(nodeCard);
                    //Neu index < currTurnCard.length - 1  thi chuyen mau bai thanh mau xam
                    if (index < currTurnCard.length - 1) {
                        nodeCard.getComponent(cc.CardItem).node.color = cc.TLMN_Controller.getInstance().getColorDark();
                    }
                }, this);

            }, this);


        },

        //HubOn - playerJoin
        playerJoin: function (info) {
            for (var i = 0; i < this.maxPlayer; i++) {
                var accID = this.positionsUI[i];
                if (accID === 0) {
                    this.positionsUI[i] = info.Account.AccountID;
                    let status = info.Status;
                    this.registerPlayer(i, info, status);
                    break;
                }
            }
        },

        //HubOn - playerLeave
        playerLeave: function (info) {
            //dam bao joinGame xong moi xu ly - tranh loi server neu bi
            if (this.positionsUI) {
                var accID = parseInt(info[0]);

                let indexOfAccId = this.positionsUI.indexOf(accID);
                if (indexOfAccId > 0) {
                    this.unRegisterPlayer(this.getIndexUIBetByAccID(accID));
                    this.positionsUI[indexOfAccId] = 0;
                }

            }
        },
        //Hubon onAllowChanNgay
        onAllowChanNgay: function (lstCard) {
            let currAccID = cc.LoginController.getInstance().getUserId();
            let currPlayer = this.TLMNPlayers[this.getIndexUIBetByAccID(currAccID)];
            currPlayer.showCardAllowChanNgay(lstCard);
        },
        isInGame: function (player) {
            return player.Status == cc.TLMNPlayerStatus.INGAME;
        },
        //update balance cho player
        updateBalancePlayers: function (players) {
            let lstAccId = Object.keys(players);
            for (let i = 0; i < lstAccId.length; i++) {
                let acc = players[lstAccId[i]].Account;
                let accId = acc.AccountID;
                let star = acc.Star;
                let player = this.TLMNPlayers[this.getIndexUIBetByAccID(accId)];
                player.updateChip(star);
                if (accId == cc.LoginController.getInstance().getUserId()) {
                    cc.BalanceController.getInstance().updateRealBalance(star)
                }
            }
        },

        // Lay player theo accID
        getHandsCardByAccId: function (accId) {
            return this.TLMNPlayers[this.getIndexUIBetByAccID(accId)];
        },

        // Cap nhat trang thai phong choi
        updateStatusGame: function (state) {
            if (this.currentState == state)
                return;
            this.currentState = state;
            let strState = "";
            switch (state) {
                case cc.BCState.WAITING:
                case cc.BCState.CONFIRM:
                    strState = "CHỜ VÁN MỚI";
                    break;
                case cc.BCState.BETTING:
                    strState = "ĐẶT CƯỢC";
                    break;
                case cc.BCState.DEALER:
                    strState = "CHIA BÀI";
                    break;
                case cc.BCState.FLIP:
                case cc.BCState.FINISH:
                    strState = "KẾT QUẢ";
                    break;
            }
            this.lbInfo.getComponent(cc.Animation).play('change_status');
            this.lbInfo.string = strState;
        },
        // Chia bai
        moveCards: function (players) {
            //Chia cho nhung vi tri active
            let playerIds = Object.keys(players);
            //Lay player id co status là INGAME
            playerIds.filter(accID => {
                players[accID].Status == cc.TLMNPlayerStatus.INGAME
            });
            // playerIds = [0, 2, 3];
            let timeDelay = 0;
            let listPrefab = [];
            playerIds.map(accId => {
                for (let i = 0; i < 13; i++) {
                    timeDelay += i * 0.05;
                    let card = cc.TLMN_Controller.getInstance().createCard();
                    listPrefab.push(card);
                    let posEnd = this.TLMNPlayers[this.getIndexUIBetByAccID(accId)].getComponent(cc.TLMNPlayer).cardOnHand.position;//this.TLMNPlayers[accId].getComponent(cc.TLMNPlayer).cardOnHand.position;//
                    card.position = cc.v2(0, 0);
                    card.parent = cc.TLMN_Controller.getInstance().TLMN_LayoutCardView.layoutCardGame;//this.node;
                    card.setScale(cc.v2(0.45, 0.45));
                    card.opacity = 0;
                    TWEEN(card)
                    // The defference delay should only eval once
                        .delay(i * 0.05)
                        .repeat(1,
                            TWEEN()
                            // first reset node properties
                                .set({opacity: 0, active: true, scale: 0.5, rotation: 90})
                                .to(0.5, {
                                    opacity: 255,
                                    rotation: card.rotation,
                                    scale: card.scale,
                                    x: posEnd.x,
                                    y: posEnd.y,
                                }, {ease: 'quintIn'})
                        ).delay(0.3)
                        .call(() => {
                            card.destroy();
                        }, this)
                        .start()
                }
            }, this);
            return [Math.floor(timeDelay / playerIds.length) - 1.5, listPrefab];

        },

        //HubOn - updateConnectionStatus
        updateConnectionStatus: function (info) {
            if (this.positionsUI) {
                let accID = info[0];
                let status = info[1];
                let player = this.TLMNPlayers[this.getIndexUIBetByAccID(accID)];
                if (player) {
                    player.updateConnectionStatus(status);
                }

            }
        },

        //HubOn - updatePlayerStatus
        updatePlayerStatus: function (playerStatus) {
            if (this.positionsUI) {
                this.TLMNPlayers[0].updatePlayerStatus(playerStatus);
            }
        },

        //HubOn -eventShowCard
        eventShowCard: function (accID, lstCard, currTurnCards) {
            //An sprite bo luot cua user neu co
            this.TLMNPlayers[this.getIndexUIBetByAccID(accID)].showBoLuot(false);

            //Neu la current player - an card
            let listCardUserPop = [];
            if (currTurnCards.length == 0) {
                return;
            }
            //Chuyen mau cac la bai danh truoc
            if (currTurnCards.length > 1) {
                let listCardOnTable = cc.TLMN_Controller.getInstance().getListCardOnTable();
                listCardOnTable.map(card => {
                    //Chi doi mau la bai chua dc up
                    let isClose = card.getComponent(cc.CardItem).isClose;
                    if (!isClose) {
                        card.getComponent(cc.CardItem).node.color = cc.TLMN_Controller.getInstance().getColorDark();
                    }
                });
            }

            //Lay phan tu dau tien cua mang CurrTurnCard la sang
            let currCard = currTurnCards[0];
            //lay danh sach bai trong mang Cards
            let cards = currCard.Value.Cards;

            //Khoi tao layout bai dc danh
            this.layoutAllCard = cc.TLMN_Controller.getInstance().TLMN_LayoutCardView.layoutContentCard;
            //Random huong xoay cua bai
            let randomRotation = cc.Tool.getInstance().getRandomFromTo(-15, 15);
            //Random vi tri bai di chuyen
            let posEnd = cc.TLMN_Controller.getInstance().getPositionCardOnTable();

            //Khoi tao bai cua player hien tai
            if (accID == cc.LoginController.getInstance().getUserId()) {

                cc.TLMN_Controller.getInstance().setMaxCard(null);

                //Lay danh sach bai cua nguoi choi hien tai
                let listCardCurrentPlayer = this.TLMNPlayers[0].getListNodeCard();
                //Danh sach bai da danh
                cards.map(cardPop => {
                    listCardCurrentPlayer.map(nodeCard => {
                        if (cardPop == nodeCard.getComponent(cc.CardItem).ordinalValue) {
                            listCardUserPop.push(nodeCard);
                        }
                    }, this);
                });

                //Animation danh bai
                listCardUserPop.map((card, index) => {
                    // let newLocPos = parentCard.parent.convertToNodeSpaceAR(oldWorPos);
                    card.parent = this.layoutAllCard;
                    card.x = -438 + card.x;
                    card.y = -199 + card.y;
                    let scaleDown = cc.scaleTo(0.3, 0.5, 0.5);
                    let posCardX = posEnd.x + 30 * index;
                    let posCardY = posEnd.y;
                    let actionMoveUp = cc.moveTo(0.5, cc.v2(posCardX, posCardY));
                    // let scaleUp = cc.scaleTo(0.3, 0.5, 0.5);
                    card.removeAllChildren();
                    card.rotation = randomRotation;

                    //Bo event click cho card
                    card.off(cc.Node.EventType.TOUCH_END);
                    card.off(cc.Node.EventType.TOUCH_START);
                    card.off(cc.Node.EventType.TOUCH_MOVE);

                    //Xu ly an tab
                    if (!cc.game.isPaused()) {
                        card.runAction(cc.spawn(scaleDown, actionMoveUp));
                    } else {
                        // card.y = 0;
                        card.rotation = randomRotation;
                        card.setScale(cc.v2(0.5, 0.5));
                        card.parent = this.layoutAllCard;
                        card.position = cc.v2(posCardX, posCardY)
                    }
                    cc.TLMN_Controller.getInstance().pushListCardOnTable(card);
                    card.zIndex = cc.TLMN_Controller.getInstance().getListCardOnTable().length;

                });
                //Reset lai trang thai ban dau cua cac la bai
                listCardCurrentPlayer.map(nodeCard => {
                    nodeCard.getComponent(cc.CardItem).reset();
                }, this);
                //Reset trang thai chon bai
                cc.TLMN_Controller.getInstance().initSelectedCard();
                cc.TLMN_Controller.getInstance().disableAllButton();
            } else {
                let type = currCard.Value.Type;
                if (type >= cc.TLMNResultTypes.HEO) {
                    //Set MaxCard
                    let maxCard = null;
                    if (type === cc.TLMNResultTypes.HEO) {
                        maxCard = currCard.Value.Card;
                    } else {
                        maxCard = currCard.Value.Max;
                    }
                    let count = currCard.Value.Count;
                    cc.TLMN_Controller.getInstance().setMaxCard({Max: maxCard, Type: type, Count: count});
                } else {
                    cc.TLMN_Controller.getInstance().setMaxCard(null);
                }

                //Khoi tao bai cua player khac danh
                cards.map((cardValue, index) => {
                    //Tao card
                    let nodeCard = cc.TLMN_Controller.getInstance().createCard();
                    let spriteCard = cc.TLMN_Controller.getInstance().getCardValueByNumber(cardValue);
                    nodeCard.getComponent(cc.Sprite).spriteFrame = spriteCard;
                    //Vi tri bai cua user
                    nodeCard.position = this.TLMNPlayers[this.getIndexUIBetByAccID(currCard.Key)].cardOnHand.position;
                    nodeCard.x = nodeCard.x + 30 * index;
                    nodeCard.setScale(cc.v2(0.5, 0.5));
                    nodeCard.rotation = randomRotation;
                    nodeCard.parent = this.layoutAllCard;

                    nodeCard.off(cc.Node.EventType.TOUCH_START);
                    nodeCard.off(cc.Node.EventType.TOUCH_MOVE);
                    nodeCard.off(cc.Node.EventType.TOUCH_CANCEL);
                    nodeCard.off(cc.Node.EventType.TOUCH_END);

                    let posCardX = posEnd.x + 30 * index;
                    let posCardY = posEnd.y;
                    let fadeIn = cc.fadeIn(0.5);
                    let actionMoveTo = cc.moveTo(0.3, cc.v2(posCardX, posCardY));
                    actionMoveTo.easing(cc.easeIn(0.3));
                    //Xu ly an tab
                    if (!cc.game.isPaused()) {
                        nodeCard.runAction(cc.spawn(fadeIn, actionMoveTo));
                    } else {
                        nodeCard.position = cc.v2(posCardX, posCardY);
                        nodeCard.rotation = randomRotation;
                        nodeCard.setScale(cc.v2(0.5, 0.5));
                    }


                    cc.TLMN_Controller.getInstance().pushListCardOnTable(nodeCard);
                    nodeCard.zIndex = cc.TLMN_Controller.getInstance().getListCardOnTable().length;
                    //listCardUserPop.push(nodeCard);
                }, this);

            }

            //Hien thi thong bao loai bai danh
            this.showTypeCard(currCard);

        },

        //Lay danh sach bai con lai cua player
        getListCurrCardPlayer: function () {
            return this.TLMNPlayers[0].getListCurrCardPlayer();
        },
        //Hien thi loai bai danh
        showTypeCard: function (currCard) {
            if (currCard) {
                let animationName = null;
                let typeCard = currCard.Value.Type;
                switch (typeCard) {
                    case cc.TLMNResultTypes.HEO:
                        let cardOrdinalValue = currCard.Value.Card.OrdinalValue;
                        if (cardOrdinalValue == 51) {
                            animationName = cc.TLMN_ANIMATION_NAME.HEO;
                        }
                        break;
                    case cc.TLMNResultTypes.BA_HEO:
                        animationName = cc.TLMN_ANIMATION_NAME.BA_HEO;
                        break;
                    case cc.TLMNResultTypes.BA_DOI_THONG:
                        animationName = cc.TLMN_ANIMATION_NAME.BA_DOI_THONG;
                        break;
                    case cc.TLMNResultTypes.BON_DOI_THONG:
                        animationName = cc.TLMN_ANIMATION_NAME.BON_DOI_THONG;
                        break;
                    case cc.TLMNResultTypes.TU_QUY:
                        animationName = cc.TLMN_ANIMATION_NAME.TU_QUY;
                        break;
                }
                if (animationName != null) {
                    cc.TLMN_Controller.getInstance().showAllNofity(animationName, 3);
                }
            }
        },
        //clearCardOnTable
        clearCardOnTable: function () {
            cc.TLMN_Controller.getInstance().TLMN_LayoutCardView.layoutContentCard.removeAllChildren();
            /*let listCardOnTable = cc.TLMN_Controller.getInstance().getListCardOnTable();
            listCardOnTable.map(card => {
                card => card.destroy();
            });*/
        },
        //HubOn - updateAccout
        updateAccount: function (dataAccounts) {
            // console.log({dataAccounts})
            if (dataAccounts.length == 0) {
                return;
            }
            let accountID = dataAccounts[0].AccountID;
            let player = this.TLMNPlayers[this.getIndexUIBetByAccID(accountID)];
            // console.log({player})
            if (player) {
                player.onUpdateAccount(dataAccounts)
            }
        },

        //HubOn - endRound : Het vong up bai
        onEndRound: function () {
            // Up bai da danh
            let listCardOnTable = cc.TLMN_Controller.getInstance().getListCardOnTable();
            listCardOnTable.map(card => {
                let isClose = card.getComponent(cc.CardItem).isClose;
                if (!cc.game.isPaused()) {
                    //Kiem tra bai da up hay chua neu chua thi chay animation upbai
                    if (!isClose) {
                        card.getComponent(cc.Animation).play('close-card');
                    }
                } else {
                    if (!isClose) {
                        card.getComponent(cc.Sprite).spriteFrame = cc.TLMN_Controller.getInstance().getSfCardBack();
                    }
                }
            });

            //An sprite bo luot neu co
            let listPlayerUI = this.positionsUI.filter(accID => accID > 0);
            listPlayerUI.map(accID => {
                let plUI = this.TLMNPlayers[this.getIndexUIBetByAccID(accID)];
                if (plUI) {
                    plUI.showBoLuot(false)
                }

            }, this);

        },
        //HubOn - showResult
        showResult: function (result) {
            let players = result[0].Players;
            let resultList = result[0].GameLoop.SessionResult.ResultList;
            //Lay account id > 0
            let listPlayerUI = this.positionsUI.filter(accID => accID > 0);
            listPlayerUI.map(accID => {
                let resultOfPlayer = resultList.filter(account => account.AccountID == accID);
                if (resultOfPlayer.length == 0) {
                    return;
                }
                let playerUI = this.TLMNPlayers[this.getIndexUIBetByAccID(accID)];
                // Hien thi bai cua player thua
                if (accID != cc.LoginController.getInstance().getUserId()) {
                    playerUI.onShowResult(players[accID], resultOfPlayer[0].BaiThoi);
                }
                //An het bai cua player het bai
                if (players[accID].EmptyHand) {
                    playerUI.hideLayoutCardOnHands();
                }
                //Hien thi thong bao thang thua
                playerUI.showNofity(resultOfPlayer[0].ResultFamily, resultOfPlayer[0].WinType);
                //Hien thi thong bao tien thang/thua, cap nhat lai balance
                let dataAccount = [players[accID].Account, resultOfPlayer[0].Money];
                this.updateAccount(dataAccount);

            }, this);
            //Hien thi progress cua player
            let playerWin = result[1];
            let timeWait = result[2];
            this.updateProgressOwner([playerWin, timeWait]);

            //Disable cac cac button
            cc.TLMN_Controller.getInstance().showLayoutButton(0, null);
        },

        updatePlayerBoLuot: function (accID) {
            //Hien node bo luot cua player
            let player = this.TLMNPlayers[this.getIndexUIBetByAccID(accID)];
            player.showBoLuot(true);
        },
        updateChip: function (accID, chip) {
            this.TLMNPlayers[this.getIndexUIBetByAccID(accID)].updateChip(chip);
        },

        getPositions: function () {
            return this.positionsUI;
        },

        //lay ve index bet theo accID
        getIndexUIBetByAccID: function (accID) {
            accID = parseInt(accID);
            if (accID == 0)
                return -1;
            return this.positionsUI.indexOf(accID);
        },

        //Lay danh sach player active hien tai
        getListPlayerActiveUI: function () {
            return this.positionsUI? this.positionsUI.filter(accID => accID > 0):[];
        },
        // Start progress player cho Chuong
        updateProgressOwner: function (infoTime) {
            let lstPlayerActiveUI = this.getListPlayerActiveUI();
            lstPlayerActiveUI.forEach(accID => {
                let playerUI = this.TLMNPlayers[this.getIndexUIBetByAccID(accID)];
                if (playerUI) {
                    playerUI.resetProgressOwner();
                }

            });
            // lay vi tri player theo Chuong
            let playerID = infoTime[0];
            this.TLMNPlayers[this.getIndexUIBetByAccID(playerID)].updateProgressOwner(infoTime);
        },
        // Stop progress player cho Chuong
        stopUpdateProgressOwner: function () {
            // lay vi tri player theo Chuong
            let ownerId = cc.TLMN_Controller.getInstance().getOwnerID();
            this.TLMNPlayers[this.getIndexUIBetByAccID(ownerId)].stopUpdateProgressOwner();
        },

        //reset UI ket qua (win/lose) sau moi Phien cua tat ca player
        resetPlayersResultUI: function (isStartGame) {
            let lstPlayerActiveUI = this.getListPlayerActiveUI();
            lstPlayerActiveUI.forEach(accID => {
                let playerUI = this.TLMNPlayers[this.getIndexUIBetByAccID(accID)];
                playerUI.resetPlayerResultUI(isStartGame);
            });
        },

        //set ket qua cua player
        playerResultUI: function (playerIndex, isWin, amount) {
            this.TLMNPlayers[playerIndex].playerResultUI(isWin, amount);
        },

        //player vao phong
        registerPlayer: function (playerIndex, info, status) {
            this.TLMNPlayers[playerIndex].registerPlayer(info, status);
        },

        unRegisterPlayer: function (playerIndex) {
            this.TLMNPlayers[playerIndex].unRegisterPlayer();
        },

        playerShowBubbleChat: function (message) {
            if (cc.ChatRoomController.getInstance().checkIsEmotion(message)) {
                this.TLMNPlayers.forEach(function (TLMNPlayer) {
                    let playerNickName = TLMNPlayer.nickName;
                    let nickName = message[0];
                    if (nickName === playerNickName) {
                        TLMNPlayer.showEmotion(cc.ChatRoomController.getInstance().getIndexEmotion(message)
                            , message);
                    }
                });
            } else {
                this.TLMNPlayers.forEach(function (TLMNPlayer) {
                    let playerNickName = TLMNPlayer.nickName;
                    let nickName = message[0];
                    if (nickName === playerNickName) {
                        TLMNPlayer.showBubbleChat(message);
                    }
                });
            }

        },


        updateRoomId: function (roomID) {
            this.lbRoomID.string = ': ' + roomID;
        },
        updateTableId: function (tableID) {
            this.lbTableId.string = ': ' + tableID;
        },
        updateSessionId: function (sID) {
            this.lbSID.string = ': #' + sID;
        },
        //Update bai sau khi xep
        updateCardOnShortHand: function (handCards) {
            return this.TLMNPlayers[0].updateCardOnShortHand(handCards);
        },
        //Hien thi bai, sau khi chia
        updateCardPlayers: function (info) {
            let players = info.Players;
            let playerIds = Object.keys(players);
            playerIds.map(accID => {
                let player = this.TLMNPlayers[this.getIndexUIBetByAccID(accID)];
                if (player) {
                    player.updateCardPlayer(players[accID]);
                }

            }, this);
        },
        //Cap nhat bai sau khi danh
        updateCardPlayersOnDanhBai: function (info) {
            let players = info.Players;
            let playerIds = Object.keys(players);
            playerIds.map(accID => {
                let player = this.TLMNPlayers[this.getIndexUIBetByAccID(accID)];
                if (player) {
                    player.updateCardPlayerOnDanhBai(players[accID]);
                }

            }, this);
        },
        //Cap nhat thong tin game
        updateInfo: function (info) {
            //Cap nhat minbet
            let roomId = cc.Tool.getInstance().formatNumber(info.MinBet);
            //Cap nhat roomID
            this.updateRoomId(roomId);
            //Cap Nhat phong
            this.updateTableId(info.SessionID);
            //Cap nhat phien
            if (info.CurrentGameLoopID != -1) {
                this.updateSessionId(info.CurrentGameLoopID);
            }
            //Cap nhat so bai con lai cua nguoi choi
            //Hien thi bai cua player
            this.updateCardPlayers(info);
        },

        //Bo chon bai
        onBoChon: function () {
            cc.TLMN_Controller.getInstance().initSelectedCard();
            let cardItemofCurrPlayer = this.TLMNPlayers[0].layoutCard.children;
            if (cardItemofCurrPlayer.length == 0)
                return;
            cardItemofCurrPlayer.map(item => {
                item.getComponent(cc.CardItem).reset();
            })
        },

        runAnimationSortHandCard: function () {
            cc.TLMN_Controller.getInstance().initSelectedCard();
            let cardItemofCurrPlayer = this.TLMNPlayers[0].layoutCard.children;
            cardItemofCurrPlayer.map(item => {
                //Bo chon bai
                item.getComponent(cc.CardItem).reset();
                item.getComponent(cc.Animation).play('flip');
            })
        }

    })
    ;
}).call(this);
