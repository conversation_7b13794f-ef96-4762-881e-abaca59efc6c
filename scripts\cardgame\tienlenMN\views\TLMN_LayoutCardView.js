/*
 * Generated by BeChicken
 * on 8/19/2019
 * version v1.0
 */

(function () {
    cc.TLMN_LayoutCardView = cc.Class({
        extends: cc.Component,
        properties: {
            layoutCardGame: cc.Node,
            layoutContentCard: cc.Node,
            prefabLayoutWrapCard: cc.Prefab
        },
        onLoad: function () {
            cc.TLMN_Controller.getInstance().setTLMNLayoutCardView(this);
        },
        //Lay random vi tri bai hien thi tren ban
        getPositionCardOnTable: function () {
            //Random x, y
            let xMax = this.layoutContentCard.width / 2;
            let yMax = this.layoutContentCard.height / 2;

            let xMin = -xMax;
            let yMin = -yMax;

            let xRandom = cc.Tool.getInstance().getRandomFromTo(xMin, xMax);
            let yRandom = cc.Tool.getInstance().getRandomFromTo(yMin, yMax);

            let posRandom = cc.v2(xRandom+this.layoutContentCard.x, yRandom+this.layoutContentCard.y);
            return posRandom;
        },
    })
}).call(this)
