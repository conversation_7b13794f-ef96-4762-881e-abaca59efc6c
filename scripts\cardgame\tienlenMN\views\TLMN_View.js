/*
 * Generated by BeChicken
 * on 8/12/2019
 * version v1.0
 */
(function () {
    cc.TLMN_VIEW = cc.Class({
        extends: cc.Component,
        properties: {
            //Layout chua bai cua game
            layoutCardGame: cc.Node,

            sfSounds: [cc.SpriteFrame], //0=on, 1=off
            spriteSound: cc.Sprite,

            spriteBack: cc.Sprite,

            nodeRegisterLeave: cc.Node,
        },
        onLoad: function () {
            cc.TLMN_Controller.getInstance().setTLMNView(this);
            this.isRegisterLeaveRoom = false;
        },
        start: function () {
            //Check Sound
            this.sound = cc.Tool.getInstance().getItem("@Sound").toString() === 'true';
            this.spriteSound.spriteFrame = this.sound ? this.sfSounds[0] : this.sfSounds[1];
            cc.AudioController.getInstance().enableSound(this.sound);
        },

        onEnable: function () {

            this.isRegisterLeaveRoom = false;
            this.nodeRegisterLeave.active = false;
            this.spriteBack.spriteFrame =cc.TLMN_Controller.getInstance().getSfBack(cc.TLMN_BACK.UN_REGIST_LEAVE);

            cc.BalanceController.getInstance().updateBalance(cc.BalanceController.getInstance().getBalance());
        },

        //Lay random vi tri bai hien thi tren ban
        getPositionCardOnTable: function () {
            //Random x, y
            let xMax = this.layoutCardGame.width / 2;
            let yMax = this.layoutCardGame.height / 2;

            let xMin = -xMax;
            let yMin = -yMax;

            let xRandom = cc.Tool.getInstance().getRandomFromTo(xMin, xMax);
            let yRandom = cc.Tool.getInstance().getRandomFromTo(yMin, yMax);
            return cc.v2(xRandom, yRandom);
        },

        soundClicked: function () {
            this.sound = !this.sound;
            cc.Tool.getInstance().setItem("@Sound", this.sound);
            this.spriteSound.spriteFrame = this.sound ? this.sfSounds[0] : this.sfSounds[1];
            cc.AudioController.getInstance().enableSound(this.sound);
        },

        backClicked: function () {

            this.isRegisterLeaveRoom = !this.isRegisterLeaveRoom;
            if (this.isRegisterLeaveRoom) {
                cc.TLMN_Controller.getInstance().sendRequestOnHub(cc.MethodHubName.REGISTER_LEAVE_ROOM);
                this.nodeRegisterLeave.active = true;
                this.spriteBack.spriteFrame = cc.TLMN_Controller.getInstance().getSfBack(cc.TLMN_BACK.REGIST_LEAVE);
            } else {
                cc.TLMN_Controller.getInstance().sendRequestOnHub(cc.MethodHubName.UNREGISTER_LEAVE_ROOM);
                this.nodeRegisterLeave.active = false;
                this.spriteBack.spriteFrame =cc.TLMN_Controller.getInstance().getSfBack(cc.TLMN_BACK.UN_REGIST_LEAVE);
            }
        },
        resetUIBackButton: function() {
            this.nodeRegisterLeave.active = false;
            this.spriteBack.spriteFrame =cc.TLMN_Controller.getInstance().getSfBack(cc.TLMN_BACK.UN_REGIST_LEAVE);
        }

    })
}).call(this);