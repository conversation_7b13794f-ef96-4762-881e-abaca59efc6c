/*
 * Generated by BeChicken
 * on 11/13/2019
 * version v1.0
 */
(function () {
    var BauCuaGroupUserCommand;

    BauCuaGroupUserCommand = (function () {
        function BauCuaGroupUserCommand() {
        }

        BauCuaGroupUserCommand.prototype.execute = function (controller) {
            let url = 'api/BauCua/GetPlayersNotInGame';
            let subDomainName = cc.SubdomainName.BAUCUA;
            return cc.ServerConnector.getInstance().sendRequest(subDomainName, url, function (response) {
                var obj = JSON.parse(response);
                return controller.onGetGroupUserResponse(obj);
            });
        };

        return BauCuaGroupUserCommand;

    })();

    cc.BauCuaGroupUserCommand = BauCuaGroupUserCommand;

}).call(this);