/*
 * Generated by BeChicken
 * on 11/13/2019
 * version v1.0
 */
(function () {
    var BauCuaHistoryCommand;

    BauCuaHistoryCommand = (function () {
        function BauCuaHistoryCommand() {
        }

        BauCuaHistoryCommand.prototype.execute = function (controller) {
            let url = 'api/BauCua/GetHistory?top=50';
            let subDomainName = cc.SubdomainName.BAUCUA;
            return cc.ServerConnector.getInstance().sendRequest(subDomainName, url, function (response) {
                var obj = JSON.parse(response);
                return controller.onGetHistoryResponse(obj);
            });
        };

        return BauCuaHistoryCommand;

    })();

    cc.BauCuaHistoryCommand = BauCuaHistoryCommand;

}).call(this);