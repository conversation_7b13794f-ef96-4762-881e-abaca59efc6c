/*
 * Generated by BeChicken
 * on 11/13/2019
 * version v1.0
 */
(function () {
    var BauCuaSoiCauCommand;

    BauCuaSoiCauCommand = (function () {
        function BauCuaSoiCauCommand() {
        }

        BauCuaSoiCauCommand.prototype.execute = function (controller) {
            let url = 'api/BauCua/GetSoiCau';
            let subDomainName = cc.SubdomainName.BAUCUA;
            return cc.ServerConnector.getInstance().sendRequest(subDomainName, url, function (response) {
                var obj = JSON.parse(response);
                return controller.onResponse(obj);
            });
        };

        return BauCuaSoiCauCommand;

    })();

    cc.BauCuaSoiCauCommand = BauCuaSoiCauCommand;

}).call(this);