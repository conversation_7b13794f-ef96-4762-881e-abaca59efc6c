/*
 * Generated by BeChicken
 * on 10/11/2019
 * version v1.0
 */
(function () {
    var BacaratGroupUserCommand;

    BacaratGroupUserCommand = (function () {
        function BacaratGroupUserCommand() {
        }

        BacaratGroupUserCommand.prototype.execute = function (controller) {
            let url = 'api/Baccarat/GetPlayersNotInGame';
            let subDomainName = cc.SubdomainName.BACCARAT;
            return cc.ServerConnector.getInstance().sendRequest(subDomainName, url, function (response) {
                var obj = JSON.parse(response);
                return controller.onGetGroupUserResponse(obj);
            });
        };

        return BacaratGroupUserCommand;

    })();

    cc.BacaratGroupUserCommand = BacaratGroupUserCommand;

}).call(this);