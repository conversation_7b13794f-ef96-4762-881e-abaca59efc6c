/*
 * Generated by BeChicken
 * on 9/27/2019
 * version v1.0
 */
(function () {
    var BacaratSoiCauCommand;

    BacaratSoiCauCommand = (function () {
        function BacaratSoiCauCommand() {
        }

        BacaratSoiCauCommand.prototype.execute = function (controller) {
            let url = 'api/Baccarat/GetSoiCau';
            let subDomainName = cc.SubdomainName.BACARAT;
            return cc.ServerConnector.getInstance().sendRequest(subDomainName, url, function (response) {
                var obj = JSON.parse(response);
                return controller.onBacarat_GetSoiCauResponse(obj);
            });
        };

        return BacaratSoiCauCommand;

    })();

    cc.BacaratSoiCauCommand = BacaratSoiCauCommand;

}).call(this);