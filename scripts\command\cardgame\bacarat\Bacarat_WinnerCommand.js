/*
 * Generated by BeChicken
 * on 9/27/2019
 * version v1.0
 */
(function () {
    var BacaratWinnerCommand;

    BacaratWinnerCommand = (function () {
        function BacaratWinnerCommand() {
        }

        BacaratWinnerCommand.prototype.execute = function (controller) {
            let url = 'api/Baccarat/GetBigWinner';
            let subDomainName = cc.SubdomainName.BACCARAT;
            return cc.ServerConnector.getInstance().sendRequest(subDomainName, url, function (response) {
                var obj = JSON.parse(response);
                return controller.onGetBigWinnerResponse(obj);
            });
        };

        return BacaratWinnerCommand;

    })();

    cc.BacaratWinnerCommand = BacaratWinnerCommand;

}).call(this);