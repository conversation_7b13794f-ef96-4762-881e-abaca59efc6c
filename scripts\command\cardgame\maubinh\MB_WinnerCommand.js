/*
 * Generated by BeChicken
 * on 9/11/2019
 * version v1.0
 */
(function () {
    var MB_WinnerCommand;

    MB_WinnerCommand = (function () {
        function MB_WinnerCommand() {
        }

        MB_WinnerCommand.prototype.execute = function (controller) {
            let url = 'api/MauBinh/GetTopRanks';
            let subDomainName = cc.SubdomainName.MAU_BINH;
            return cc.ServerConnector.getInstance().sendRequest(subDomainName, url, function (response) {
                var obj = JSON.parse(response);
                return controller.onMB_GetBigWinnerResponse(obj);
            });
        };

        return MB_WinnerCommand;

    })();

    cc.MB_WinnerCommand = MB_WinnerCommand;

}).call(this);