/*
 * Generated by BeChicken
 * on 8/14/2019
 * version v1.0
 */
(function () {
    var TLMNWinnerCommand;

    TLMNWinnerCommand = (function () {
        function TLMNWinnerCommand() {
        }

        TLMNWinnerCommand.prototype.execute = function (controller) {
            let url = 'api/TLMN/GetTopRanks';
            let subDomainName = cc.SubdomainName.TLMN;
            switch (cc.RoomController.getInstance().getGameId().toString()) {
                case cc.GameId.TIEN_LEN_MN_SOLO:
                    subDomainName = cc.SubdomainName.TLMN_SOLO;
                    break;
            }
            return cc.ServerConnector.getInstance().sendRequest(subDomainName, url, function (response) {
                var obj = JSON.parse(response);
                return controller.onTLMNGetBigWinnerResponse(obj);
            });
        };

        return TLMNWinnerCommand;

    })();

    cc.TLMNWinnerCommand = TLMNWinnerCommand;

}).call(this);