/*
 * Generated by BeChicken
 * on 6/10/2019
 * version v1.0
 */
(function () {
    var DragonTigerGetHistoryCommand;

    DragonTigerGetHistoryCommand = (function () {
        function DragonTigerGetHistoryCommand() {
        }

        DragonTigerGetHistoryCommand.prototype.execute = function (controller) {
            var url = 'api/Game/GetHistory';

            return cc.ServerConnector.getInstance().sendRequest(cc.SubdomainName.DRAGON_TIGER, url, function (response) {
                var obj = JSON.parse(response);
                return controller.onDragonTigerGetHistoryResponse(obj);
            });
        };

        return DragonTigerGetHistoryCommand;

    })();

    cc.DragonTigerGetHistoryCommand = DragonTigerGetHistoryCommand;

}).call(this);
