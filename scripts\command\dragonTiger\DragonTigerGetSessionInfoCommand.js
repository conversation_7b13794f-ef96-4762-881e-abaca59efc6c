/*
 * Generated by BeChicken
 * on 6/10/2019
 * version v1.0
 */
(function () {
    var DragonTigerGetSessionInfoCommand;

    DragonTigerGetSessionInfoCommand = (function () {
        function DragonTigerGetSessionInfoCommand() {

        }

        DragonTigerGetSessionInfoCommand.prototype.execute = function (controller, sessionId) {
            var url = 'api/Game/GetSessionInfo?sessionId=' + sessionId;

            return cc.ServerConnector.getInstance().sendRequest(cc.SubdomainName.DRAGON_TIGER, url, function (response) {
                var obj = JSON.parse(response);
                return controller.onDragonTigerGetSessionInfoResponse(obj);
            });
        };

        return DragonTigerGetSessionInfoCommand;

    })();

    cc.DragonTigerGetSessionInfoCommand = DragonTigerGetSessionInfoCommand;

}).call(this);