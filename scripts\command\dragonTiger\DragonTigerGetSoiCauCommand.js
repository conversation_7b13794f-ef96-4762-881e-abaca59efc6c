/*
 * Generated by BeChicken
 * on 6/10/2019
 * version v1.0
 */
(function () {
    var DragonTigerGetSoiCauCommand;

    DragonTigerGetSoiCauCommand = (function () {
        function DragonTigerGetSoiCauCommand() {
        }

        DragonTigerGetSoiCauCommand.prototype.execute = function (controller) {
            var url = 'api/Game/GetSoiCau';

            return cc.ServerConnector.getInstance().sendRequest(cc.SubdomainName.DRAGON_TIGER, url, function (response) {
                var obj = JSON.parse(response);
                return controller.onDragonTigerGetSoiCauResponse(obj);
            });
        };

        return DragonTigerGetSoiCauCommand;

    })();

    cc.DragonTigerGetSoiCauCommand = DragonTigerGetSoiCauCommand;

}).call(this);
