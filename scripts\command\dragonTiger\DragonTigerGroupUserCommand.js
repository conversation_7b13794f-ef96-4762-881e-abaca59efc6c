/*
 * Generated by BeChicken
 * on 10/11/2019
 * version v1.0
 */
(function () {
    var DragonTigerGroupUserCommand;

    DragonTigerGroupUserCommand = (function () {
        function DragonTigerGroupUserCommand() {
        }

        DragonTigerGroupUserCommand.prototype.execute = function (controller) {
            let url = 'api/Game/GetPlayersNotInGame';
            let subDomainName = cc.SubdomainName.DRAGON_TIGER;
            return cc.ServerConnector.getInstance().sendRequest(subDomainName, url, function (response) {
                var obj = JSON.parse(response);
                return controller.onGetGroupUserResponse(obj);
            });
        };

        return DragonTigerGroupUserCommand;

    })();

    cc.DragonTigerGroupUserCommand = DragonTigerGroupUserCommand;

}).call(this);