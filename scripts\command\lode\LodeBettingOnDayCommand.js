/*******
 * Generated by nemo
 * on 2/21/20
 * version
 ********/
(function () {
    var LodeBettingOnDayCommand;

    LodeBettingOnDayCommand = (function () {
        function LodeBettingOnDayCommand() {
        }

        LodeBettingOnDayCommand.prototype.execute = function (controller, bettingType) {
            let url = '/api/Xoso/GetBettingOnDay?bettingType='+bettingType;
            let subDomainName = cc.SubdomainName.LODE;
            return cc.ServerConnector.getInstance().sendRequest(subDomainName, url, function (response) {
                var obj = JSON.parse(response);
                return controller.onGetBettingOnDayResponse(obj);
            });
        };

        return LodeBettingOnDayCommand;

    })();

    cc.LodeBettingOnDayCommand = LodeBettingOnDayCommand;

}).call(this);
