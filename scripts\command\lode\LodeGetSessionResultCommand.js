/*******
 * Generated by nemo
 * on 2/21/20
 * version
 ********/
(function () {
    var LodeGetSessionResultCommand;

    LodeGetSessionResultCommand = (function () {
        function LodeGetSessionResultCommand() {
        }

        LodeGetSessionResultCommand.prototype.execute = function (controller, openDate) {
            let url = '/api/Account/GetSessionResult?openDate='+openDate;
            let subDomainName = cc.SubdomainName.PORTAL;
            return cc.ServerConnector.getInstance().sendRequest(subDomainName, url, function (response) {
                var obj = JSON.parse(response);
                return controller.onGetHistoryResponse(obj);
            });
        };

        return LodeGetSessionResultCommand;

    })();

    cc.LodeGetSessionResultCommand = LodeGetSessionResultCommand;

}).call(this);
