/**
 * Created by Nofear on 2/27/2019.
 */

(function () {
    var GetCaptchaCommand;

    GetCaptchaCommand = (function () {
        function GetCaptchaCommand() {
        }

        GetCaptchaCommand.prototype.execute = function (controller) {
            var url = 'api/Account/Captcha';

            return cc.ServerConnector.getInstance().sendRequest(cc.SubdomainName.PORTAL, url, function (response) {
                try {
                    var obj = JSON.parse(response);

                    // Kiểm tra nếu có lỗi từ server
                    if (obj.error) {
                        console.error('GetCaptcha: Server error', obj);
                        if (controller.onGetCaptchaError) {
                            controller.onGetCaptchaError('Server error: ' + (obj.message || obj.error));
                        }
                        return;
                    }

                    //[privatekey,binarystring]
                    if (obj && obj.length >= 2) {
                        cc.ServerConnector.getInstance().setCaptchaPrivateKey(obj[0]);
                        return controller.onGetCaptchaResponse(obj);
                    } else {
                        console.error('GetCaptcha: Invalid response format', obj);
                        if (controller.onGetCaptchaError) {
                            controller.onGetCaptchaError('Invalid captcha response format');
                        }
                    }
                } catch (e) {
                    console.error('GetCaptcha: Parse error', e, response);
                    if (controller.onGetCaptchaError) {
                        controller.onGetCaptchaError('Failed to parse captcha response');
                    }
                }
            });
        };

        return GetCaptchaCommand;

    })();

    cc.GetCaptchaCommand = GetCaptchaCommand;

}).call(this);
