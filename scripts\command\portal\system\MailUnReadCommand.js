/**
 * Created by <PERSON><PERSON>ar on 2/27/2019.
 */

(function () {
    var MailUnReadCommand;

    MailUnReadCommand = (function () {
        function MailUnReadCommand() {
        }

        MailUnReadCommand.prototype.execute = function (controller) {
            var url = 'api/System/MailUnRead';

            return cc.ServerConnector.getInstance().sendRequest(cc.SubdomainName.PORTAL, url, function (response) {
                try {
                    var obj = JSON.parse(response);

                    // Kiểm tra nếu có lỗi từ server
                    if (obj.error) {
                        // Server error logged internally
                        return;
                    }

                    if (obj.ResponseCode === 1) {
                        return controller.onMailUnReadResponse(obj);
                    } else {
                        // Response error logged internally
                        //cc.PopupController.getInstance().showMessageError(obj.Message, obj.ResponseCode);
                    }
                } catch (e) {
                    // Parse error logged internally
                }
            });
        };

        return MailUnReadCommand;

    })();

    cc.MailUnReadCommand = MailUnReadCommand;

}).call(this);
