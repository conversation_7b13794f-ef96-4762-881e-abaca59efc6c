/**
 * Created by <PERSON><PERSON>ar on 2/27/2019.
 */

(function () {
    var UpdateStatusMailCommand;

    UpdateStatusMailCommand = (function () {
        function UpdateStatusMailCommand() {
        }

        UpdateStatusMailCommand.prototype.execute = function (controller, status) {
            var url = 'api/System/UpdateStatus';
            var params = JSON.stringify({
                Type: status,
                MailID: controller.item.ID
            });

            return cc.ServerConnector.getInstance().sendRequestPOST(cc.SubdomainName.PORTAL, url, params, function (response) {
                try {
                    var obj = JSON.parse(response);

                    // <PERSON><PERSON><PERSON> tra nếu có lỗi từ server
                    if (obj.error) {
                        console.error('UpdateStatusMail: Server error', obj);
                        cc.PopupController.getInstance().showMessageError('Lỗi server: ' + (obj.message || obj.error), obj.status || -1);
                        return;
                    }

                    if (obj.ResponseCode === 1) {
                        /*
                        {
                            "ResponseCode": 1,
                            "Message": "Cập nhật thành công!"
                        }
                        * */
                        return controller.onUpdateStatusMailResponse(obj, status);
                    } else {
                        console.error('UpdateStatusMail: Response error', obj);
                        cc.PopupController.getInstance().showMessageError(obj.Message || 'Lỗi cập nhật trạng thái thư', obj.ResponseCode);
                    }
                } catch (e) {
                    console.error('UpdateStatusMail: Parse error', e, response);
                    cc.PopupController.getInstance().showMessageError('Lỗi xử lý dữ liệu', -1);
                }
            });
        };

        return UpdateStatusMailCommand;

    })();

    cc.UpdateStatusMailCommand = UpdateStatusMailCommand;

}).call(this);
