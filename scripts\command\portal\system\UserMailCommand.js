/**
 * Created by <PERSON>fear on 2/27/2019.
 */

(function () {
    var UserMailCommand;

    UserMailCommand = (function () {
        function UserMailCommand() {
        }

        UserMailCommand.prototype.execute = function (controller) {
            var url = 'api/System/GetUserMail';
            var params = JSON.stringify({
                MailType: 2, //1=send, 2=receive
                CurrentPage: 1,
                PageSize: 25
            });

            return cc.ServerConnector.getInstance().sendRequestPOST(cc.SubdomainName.PORTAL, url, params, function (response) {
                try {
                    var obj = JSON.parse(response);

                    // <PERSON><PERSON><PERSON> tra nếu có lỗi từ server
                    if (obj.error) {
                        console.error('UserMail: Server error', obj);
                        if (controller.onUserMailResponseError) {
                            controller.onUserMailResponseError(obj);
                        }
                        return;
                    }

                    if (obj.ResponseCode === 1) {
                        return controller.onUserMailResponse(obj);
                    } else {
                        console.error('UserMail: Response error', obj);
                        if (controller.onUserMailResponseError) {
                            controller.onUserMailResponseError(obj);
                        } else {
                            cc.PopupController.getInstance().showMessageError(obj.Message || 'Lỗi tải hòm thư', obj.ResponseCode);
                        }
                    }
                } catch (e) {
                    console.error('UserMail: Parse error', e, response);
                    if (controller.onUserMailResponseError) {
                        controller.onUserMailResponseError({Message: 'Lỗi xử lý dữ liệu hòm thư'});
                    }
                }
            });
        };

        return UserMailCommand;

    })();

    cc.UserMailCommand = UserMailCommand;

}).call(this);
