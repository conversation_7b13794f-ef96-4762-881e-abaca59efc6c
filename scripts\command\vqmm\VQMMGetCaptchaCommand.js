/**
 * Created by <PERSON><PERSON>ar on 2/27/2019.
 */

(function () {
    var VQMMGetCaptchaCommand;

    VQMMGetCaptchaCommand = (function () {
        function VQMMGetCaptchaCommand() {
        }

        VQMMGetCaptchaCommand.prototype.execute = function (controller) {
            var url = 'api/luckyrotation/GetCaptcha';

            return cc.ServerConnector.getInstance().sendRequest(cc.SubdomainName.VQMM, url, function (response) {
                try {
                    var obj = JSON.parse(response);

                    // Ki<PERSON><PERSON> tra nếu có lỗi từ server
                    if (obj.error) {
                        console.error('VQMMGetCaptcha: Server error', obj);
                        if (controller.lbError) {
                            controller.lbError.string = 'Lỗi server: ' + (obj.message || obj.error);
                        }
                        return;
                    }

                    //[privatekey,binarystring]
                    if (obj && obj.length >= 2) {
                        cc.ServerConnector.getInstance().setCaptchaPrivateKey(obj[0]);
                        return controller.oVQMMGetCaptchaResponse(obj);
                    } else {
                        console.error('VQMMGetCaptcha: Invalid response format', obj);
                        if (controller.lbError) {
                            controller.lbError.string = 'Lỗi tải captcha. Vui lòng thử lại.';
                        }
                    }
                } catch (e) {
                    console.error('VQMMGetCaptcha: Parse error', e, response);
                    if (controller.lbError) {
                        controller.lbError.string = 'Lỗi xử lý captcha. Vui lòng thử lại.';
                    }
                }
            });
        };

        return VQMMGetCaptchaCommand;

    })();

    cc.VQMMGetCaptchaCommand = VQMMGetCaptchaCommand;

}).call(this);
