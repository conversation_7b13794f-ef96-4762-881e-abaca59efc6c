/**
 * Disable Console Logs for Production
 * This script completely disables all console output in production
 */

(function () {
    var DisableConsole = (function () {
        function DisableConsole() {
        }

        DisableConsole.prototype.init = function () {
            // Check if we're in production mode
            var isProduction = true; // Set to false for development
            
            // Check hostname to determine environment
            if (typeof window !== 'undefined' && window.location) {
                var hostname = window.location.hostname;
                // Enable console for localhost and development domains
                if (hostname === 'localhost' || 
                    hostname === '127.0.0.1' || 
                    hostname.includes('dev') || 
                    hostname.includes('test')) {
                    isProduction = false;
                }
            }

            if (isProduction) {
                this.disableAllConsole();
            }
        };

        DisableConsole.prototype.disableAllConsole = function () {
            // Store original console methods (in case we need to restore them)
            this.originalConsole = {
                log: console.log,
                error: console.error,
                warn: console.warn,
                info: console.info,
                debug: console.debug,
                trace: console.trace,
                dir: console.dir,
                dirxml: console.dirxml,
                group: console.group,
                groupEnd: console.groupEnd,
                time: console.time,
                timeEnd: console.timeEnd,
                assert: console.assert,
                count: console.count,
                table: console.table
            };

            // Replace all console methods with empty functions
            console.log = function() {};
            console.error = function() {};
            console.warn = function() {};
            console.info = function() {};
            console.debug = function() {};
            console.trace = function() {};
            console.dir = function() {};
            console.dirxml = function() {};
            console.group = function() {};
            console.groupEnd = function() {};
            console.time = function() {};
            console.timeEnd = function() {};
            console.assert = function() {};
            console.count = function() {};
            console.table = function() {};

            // Also disable any potential console.clear calls
            console.clear = function() {};
        };

        DisableConsole.prototype.enableConsole = function () {
            // Restore original console methods if needed
            if (this.originalConsole) {
                console.log = this.originalConsole.log;
                console.error = this.originalConsole.error;
                console.warn = this.originalConsole.warn;
                console.info = this.originalConsole.info;
                console.debug = this.originalConsole.debug;
                console.trace = this.originalConsole.trace;
                console.dir = this.originalConsole.dir;
                console.dirxml = this.originalConsole.dirxml;
                console.group = this.originalConsole.group;
                console.groupEnd = this.originalConsole.groupEnd;
                console.time = this.originalConsole.time;
                console.timeEnd = this.originalConsole.timeEnd;
                console.assert = this.originalConsole.assert;
                console.count = this.originalConsole.count;
                console.table = this.originalConsole.table;
            }
        };

        return DisableConsole;
    })();

    // Create global instance
    cc.DisableConsole = DisableConsole;

    // Auto-initialize when script loads
    var disableConsole = new DisableConsole();
    disableConsole.init();

}).call(this);
