(function () {
    var Tool;

    Tool = (function () {
        var instance;

        function Tool() {

        }

        instance = void 0;

        Tool.getInstance = function () {
            if (instance === void 0) {
                instance = this;
            }
            return instance.prototype;
        };

        Tool.prototype.setItem = function (key, value) {
            if (cc.sys.isBrowser) {
                cc.sys.localStorage.setItem(key, value);
            }
        };

        Tool.prototype.getItem = function (key) {
            if (cc.sys.isBrowser) {
                return cc.sys.localStorage.getItem(key);
            }
            return null;
        };

        Tool.prototype.removeItem = function (key) {
            if (cc.sys.isBrowser) {
                cc.sys.localStorage.removeItem(key);
            }
        };

        Tool.prototype.formatNumber = function (number) {
            if (number === null || number === undefined) return '0';
            return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        };

        Tool.prototype.generateUUID = function () {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        };

        Tool.prototype.getHostName = function () {
            if (cc.sys.isBrowser) {
                return window.location.hostname;
            }
            return '';
        };

        Tool.prototype.removeStr = function (str, removeStr) {
            if (!str || !removeStr) return str;
            return str.replace(new RegExp(removeStr, 'g'), '');
        };

        Tool.prototype.removeDot = function (str) {
            if (!str) return '';
            return str.replace(/\./g, '');
        };

        Tool.prototype.convertUTCTime2 = function (utcTime) {
            if (!utcTime) return '';
            var date = new Date(utcTime);
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
        };

        return Tool;

    })();

    cc.Tool = Tool;

}).call(this);