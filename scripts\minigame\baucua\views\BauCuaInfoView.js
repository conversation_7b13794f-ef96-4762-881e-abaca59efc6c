/*
 * Generated by BeChicken
 * on 11/13/2019
 * version v1.0
 */
(function () {
    cc.BauCuaInfoView = cc.Class({
        extends: cc.Component,
        properties: {
            lbTime: cc.Label,
            lbSessionID: cc.Label,
            lbStatus: cc.Label,
            lbTotalPlayer: cc.Label,
            lstPlayers: [cc.BauCuaPlayer],
            skeletonBat: sp.Skeleton,
            batNan: cc.Node,
            nodeVi: cc.Node,
            nodeDia: cc.Node,
            lbTotalUserWin: cc.Label,
            progressBarTimer: cc.ProgressBar,
        },
        onLoad: function () {
            this.controller = cc.BauCuaController.getInstance();
            this.controller.setInfoView(this);

            this.activeTimer(false);
            this.showStatus(null);
            this.currentState = null;
            this.interval = null;
            this.time = 0;
            this.currPlayer = this.lstPlayers[0];
            this.skeletonBat.node.active = true;
            this.animTotalUserWin = this.lbTotalUserWin.node.getComponent(cc.Animation);
        },
        onEnable: function () {
            this.resetPlate();
        },
        onDestroy: function () {
            try {
                if (this.interval) {
                    clearInterval(this.interval);
                }
            } catch (e) {

            }
        },
        //Active trang thai bat cho phep nan
        activeOpenPlate: function () {
            let isNan = this.controller.getIsNan();
            this.nodeDia.active = true;
            this.batNan.active = isNan;
            this.skeletonBat.node.active = !isNan;
        },
        //Hien thi ket qua
        forceOpenPlate: function (isAuto) {
            this.nodeVi.active = true;
            this.nodeDia.active = true;
            let isNan = this.controller.getIsNan();
            if ((!isNan && isAuto) || (!isAuto && isNan)) {
                this.playAnimBat(cc.BauCuaAnim.OPEN_PLATE);
            } else {
                this.batNan.active = false;
            }
        },
        //reset trang thai bat
        resetPlate: function () {
            this.nodeDia.active = false;
            this.batNan.active = false;
            this.batNan.position = cc.v2(0, 47);
            this.skeletonBat.node.active = true;
        },
        playAnimBat: function (nameAnim) {
            this.skeletonBat.node.active = true;
            this.batNan.active = false;
            this.skeletonBat.clearTracks();
            this.skeletonBat.setToSetupPose();
            this.skeletonBat.setAnimation(1, nameAnim, false);
        },
        //Hub on notifyChangePhrase - Thong bao chuyen phien
        onNotifyChangePhrase: function (data) {
            let state = parseInt(data.Phrase);
            switch (state) {
                case cc.BauCuaPharse.None:
                    this.progressBarTimer.node.parent.active=false;
                    if (this.currentState !== state) {
                        this.controller.enableClickBet(false);
                        this.showStatus(null);
                    }
                    break;
                case cc.BauCuaPharse.Waiting://Cho phien moi
                this.progressBarTimer.node.parent.active=false;
                    if (this.currentState !== state) {
                        this.resetPlate();
                        //Reset trang thai
                        this.controller.enableClickBet(false);
                        //Stop animation
                        this.controller.stopAnimationWin();
                        //Clear chip
                        this.controller.clearAllChips();
                        //Khoi tao lai paramchip
                        this.controller.initParamChips();

                        //Clear session truoc
                        this.controller.clearBetLog(this.controller.getBetLogSession());
                        //Tao session betlog
                        this.controller.setBetLogSession(this.controller.getBetLogSession() + 1);
                        this.resetPlayerUI();
                        this.updateTotalUserWin(null);
                        this.playAnimBat(cc.BauCuaAnim.CLOSE_PLATE);
                        this.showStatus("CHỜ PHIÊN MỚI");
                    }
                    break;
                case cc.BauCuaPharse.Shaking://Xoc
                this.progressBarTimer.node.parent.active=false;
                    if (this.currentState !== state) {
                        this.playAnimBat(cc.BauCuaAnim.SHAKING);
                        this.controller.enableClickBet(false);
                    }
                    break;
                case cc.BauCuaPharse.Betting://Dat Cua
                    if (this.currentState !== state) {
                        this.progressBarTimer.node.parent.active=true;
                        this.resetPlate();
                        this.controller.setWinResult(null);
                        this.controller.setWinVipResult(null);
                        this.controller.setTotalWinResult(null);
                        this.controller.enableClickBet(true);
                        this.activeTimer(true);
                        this.showStatus("ĐẶT CỬA");
                    }
                    break;
                case cc.BauCuaPharse.EndBetting://Dat Cua
                this.progressBarTimer.node.parent.active=false;
                    if (this.currentState !== state) {
                        this.controller.enableClickBet(false);
                        this.showStatus("HẾT THỜI GIAN ĐẶT");
                    }
                    break;
                case cc.BauCuaPharse.OpenPlate://Mo bat
                this.progressBarTimer.node.parent.active=false;
                    if (this.currentState !== state) {
                        this.playAnimBat(cc.BauCuaAnim.CLOSE_PLATE);
                        this.controller.enableClickBet(false);
                        this.controller.setDicesResult(data);
                        this.activeOpenPlate();
                        this.activeTimer(false);
                        if (!this.controller.getIsNan()) {
                            this.forceOpenPlate(true);
                        }
                        this.showStatus("MỞ BÁT");
                    }
                    break;
                case cc.BauCuaPharse.ShowResult://Ket qua
                this.progressBarTimer.node.parent.active=false;
                    if (this.currentState !== state) {
                        this.controller.initChipsWin();
                        this.forceOpenPlate(false);
                        this.controller.enableClickBet(false);
                        this.controller.onShowResult(data);
                        this.activeTimer(true);
                        this.showStatus(null);
                    }
                    break;
            }
            this.controller.setCurrentState(state);
            this.currentState = state;
            this.updateSessionId(data.SessionID);
            //Cap nhat tong tien bet
            this.controller.updateTotalBet(data);
        },
        //Cap nhat thong tin game
        updateSessionInfo: function (data) {
        },
        //Cap nhat so luong nguoi choi ko ngoi trong ban
        updatePlayersInGame: function (totalPlayer) {
            this.lbTotalPlayer.string = totalPlayer;
        },

        resetPlayerUI: function () {
            this.lstPlayers.map(player => {
                player.resetPlayerResultUI();
            }, this)
        },
        //Cap nhat thong tin nguoi choi hien tai
        updatePlayerInfor: function (dataPlayer) {
            this.currPlayer.registerPlayer(dataPlayer.Account);
        },
        //Cap nhat thong tin player
        updatePlayersUI: function (dataPlayers) {
            this.positionsUI = [0, 0, 0, 0, 0, 0, 0];
            let countPlayer = 0;
            this.positionsUI[countPlayer] = cc.LoginController.getInstance().getUserId();
            countPlayer++;
            dataPlayers.map(player => {
                if (player.AccountID != cc.LoginController.getInstance().getUserId()) {
                    if (countPlayer <= 6) {
                        this.positionsUI[countPlayer] = player.AccountID;
                        countPlayer++;
                    }
                }
            }, this);

            //Hien thi player
            this.positionsUI.forEach(function (accID, index) {
                if (accID != 0) {
                    try {
                        let playerInfo = dataPlayers.filter(player => player.AccountID == accID);
                        //Loai tru player hien tai
                        if (playerInfo.length > 0 && index != 0) {
                            this.lstPlayers[index].registerPlayer(playerInfo[0].Account);
                        }
                    } catch (e) {
                        console.log(e);
                    }
                } else {
                    try {
                        //Reset lai vi tri cua player
                        this.lstPlayers[index].unRegisterPlayer();
                    } catch (e) {
                        console.log(e);
                    }
                }
            }, this);
            this.controller.updatePositionPlayerUI(this.positionsUI);
        },
        //Cap nhat balance cua player hien tai
        updateBalanceCurrPlayer: function (balance) {
            this.currPlayer.updateChipNormal(balance);
        },
        //Cap nhat balance player khac
        updateBalancePlayer: function (data) {
            let accID = data[0];
            let balance = data[3];
            if (this.positionsUI) {
                let indexPlayer = this.positionsUI.indexOf(accID);
                if (indexPlayer != -1) {
                    this.lstPlayers[indexPlayer].updateChip(balance);
                }
            }

        },
        //Hien thi ket qua thang
        winResult: function (data) {
            this.currPlayer.playerResultUI(data.Award, data.Balance);
        },
        //Hien thi tien thang cua nguoi choi ngoi trong ban
        winResultVip: function (data) {
            data.map(player => {
                //Kiem tra player co trong mang hay ko
                if (this.positionsUI.includes(player.AccountID) && player.AccountID != cc.LoginController.getInstance().getUserId()) {
                    let indexPlayer = this.positionsUI.indexOf(player.AccountID);
                    this.lstPlayers[indexPlayer].playerResultUI(player.Award, player.Balance);
                }
            }, this);
        },
        showStatus: function (strStatus) {
            if (strStatus != null) {
                this.lbStatus.string = strStatus;
                this.lbStatus.node.parent.active = true;
                this.lbStatus.node.getComponent(cc.Animation).play('notify-checkchi');
            } else {
                this.lbStatus.node.parent.active = false;
            }
        },
        //reset dem nguoc
        activeTimer: function (isActive) {
            this.lbTime.node.parent.active = isActive;
            if (this.interval && !isActive) {
                clearInterval(this.interval);
            }
        },
        //Cap nhat phien
        updateSessionId: function (sID) {
            this.lbSessionID.string = ": #" + sID;
        },
        //Cap nhat thoi gian dem nguoc
        updateTime: function (time) {
            //Clear interval
            if (this.interval) {
                clearInterval(this.interval);
            }
            this.time = parseInt(time);
            this.startTimer();

            this.interval = setInterval(function () {
                this.startTimer();
            }.bind(this), 1000)
        },
        startTimer: function () {
            if (this.time < 0) {
                this.progressBarTimer.progress = 0;
                this.time = 0;
                return;
            }
            this.lbTime.string = this.time;
            console.log(this.time);
            this.time--;
            this.progressBarTimer.progress = this.time / 20;
        },
        updateRoomTimer: function (time) {
            if (this.progressBarTimer) {
                this.progressBarTimer.progress = time / 20;
            }

            if (this.lbTime) {
                var timeInt = time;
                this.timeInt = timeInt;

                if (timeInt > 0) {
                    this.lbTime.string = timeInt;
                    if (this.currentState === cc.BauCuaPharse.EndBetting) {
                        this.lbTime.node.color = cc.Color.RED;
                    } else {
                        this.lbTime.node.color = cc.Color.GREEN;
                    }
                    if ([cc.BauCuaPharse.Waiting, cc.BauCuaPharse.None, cc.BauCuaPharse.Shaking, cc.BauCuaPharse.ShowResult, cc.BauCuaPharse.OpenPlate].includes(this.currentState)) {
                        this.lbTime.node.color = cc.Color.WHITE;
                    }

                }
            }
        },
        //Hien thi tin nhan
        playerShowBubbleChat: function (message) {
            if (message[4] == false && message[3] != cc.LoginController.getInstance().getUserId()) {
                return;
            }
            if (cc.ChatRoomController.getInstance().checkIsEmotion(message)) {
                this.lstPlayers.forEach(function (player) {
                    let playerNickName = player.nickName;
                    let nickName = message[0];
                    if (nickName === playerNickName) {
                        player.showEmotion(cc.ChatRoomController.getInstance().getIndexEmotion(message)
                            , message);
                    }
                });
            } else {
                this.lstPlayers.forEach(function (player) {
                    let playerNickName = player.nickName;
                    let nickName = message[0];
                    if (nickName === playerNickName) {
                        player.showBubbleChat(message);
                    }
                });
            }

        },
        //Hien thi tien thang cua user ko ngoi trong ban
        updateTotalUserWin: function (amount) {
            this.lbTotalUserWin.node.active = false;
            if (amount != null && amount != 0) {
                this.lbTotalUserWin.string = "+" + cc.Tool.getInstance().formatNumber(amount);
                this.lbTotalUserWin.node.active = true;
                this.lbTotalUserWin.node.scaleY = 0;
                this.animTotalUserWin.play('xxWin');
            }
        }
    });
}).call(this);