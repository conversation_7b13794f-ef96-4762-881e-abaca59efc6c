/*
 * Generated by BeChicken
 * on 6/10/2019
 * version v1.0
 */
(function () {
    cc.DragonTigerBetView = cc.Class({
        extends: cc.Component,
        properties: {
            layoutButtonBets: cc.Node,
            lbDragonBet: cc.Label,
            lbTideBet: cc.Label,
            lbTigerBet: cc.Label,
            //Btn Bet again
            nodeBetX2: cc.Node,
            nodeBetAgain: cc.Node,
        },

        onLoad: function () {
            cc.DragonTigerController.getInstance().setDragonTigerBetView(this);
            this.reset();
            this.balanceBet = 5000;
            this.totalBet = 0;
            this.betSide = 0;
            //Btn Bet
            this.btnBetX2 = this.nodeBetX2.getComponent(cc.Button);
            this.btnBetAgain = this.nodeBetAgain.getComponent(cc.Button);
        },

        onDestroy: function () {
            cc.DragonTigerController.getInstance().setDragonTigerBetView(null);
        },

        reset: function () {
            this.lbDragonBet.string = '';
            this.lbTideBet.string = '';
            this.lbTigerBet.string = '';
        },
        setBalanceBet: function (btnBet, value) {
            if (parseInt(value) == this.balanceBet)
                return;
            cc.AudioController.getInstance().playSound(cc.DragonTigerAudioTypes.SELECT_CHIP);
            this.resetStateBtnBet();
            let btnBetName = btnBet.target._name;
            let nodeBtnBet = this.layoutButtonBets.getChildByName(btnBetName);

            let nodeButtons = nodeBtnBet.getComponent(cc.DragonTigerButtonBet);
            let spriteBtn = nodeBtnBet.getChildren()[0];
            spriteBtn.getComponent(cc.Sprite).spriteFrame = nodeButtons.spriteButtons[0];


            let action = cc.moveTo(0.3, cc.v2(nodeBtnBet.x, -308));
            action.easing(cc.easeInOut(1));
            nodeBtnBet.runAction(action);
            this.balanceBet = parseInt(value);
        },
        resetStateBtnBet: function () {
            let listBtns = this.layoutButtonBets.getChildren();
            listBtns.map(btn => {

                let nodeButtons = btn.getComponent(cc.DragonTigerButtonBet);
                let spriteBtn = btn.getChildren()[0];
                spriteBtn.getComponent(cc.Sprite).spriteFrame = nodeButtons.spriteButtons[1];

                let action = cc.moveTo(0.3, cc.v2(btn.x, -334));
                action.easing(cc.easeInOut(1));
                btn.runAction(action);
            });
        },
        //Cap nhat tong tien bet cua player
        updateTotalBetValue: function (betSide, totalBet) {
            totalBet = this.formatNumber(totalBet);
            switch (parseInt(betSide)) {
                case cc.DragonTigerBetSide.RONG:
                    this.lbDragonBet.string = totalBet;
                    break;
                case cc.DragonTigerBetSide.HOA:
                    this.lbTideBet.string = totalBet;
                    break;
                case cc.DragonTigerBetSide.HO:
                    this.lbTigerBet.string = totalBet;
                    break;
            }
        },
        formatNumber: function (strNum) {
            strNum = parseInt(strNum);
            return (strNum == 0) ? "" : cc.Tool.getInstance().formatNumber(strNum);
        },
        updateBetInfoFromServer: function (listBet) {
            this.listBet = listBet;
            var self = this;
            listBet.map(betInfo => {
                self.updateBetInfo(betInfo)
            });

        },
        updateBetInfo: function (betInfo) {
            //this.betSide = betInfo.BetSide;
            let betValue = cc.Tool.getInstance().formatNumber(betInfo.BetValue);
            switch (betInfo.BetSide) {
                case cc.DragonTigerBetSide.RONG:
                    this.lbDragonBet.string = betValue;
                    break;
                case cc.DragonTigerBetSide.HOA:
                    this.lbTideBet.string = betValue;
                    break;
                case cc.DragonTigerBetSide.HO:
                    this.lbTigerBet.string = betValue;
                    break;
            }
        },
        setBetSide: function (element, side) {
            this.betSide = parseInt(side);
            cc.AudioController.getInstance().playSound(cc.DragonTigerAudioTypes.BET);
            if (cc.BalanceController.getInstance().getBalance() < this.balanceBet) {
                cc.PopupController.getInstance().showMessage('Số dư không đủ');
            } else {
                cc.DragonTigerController.getInstance().sendRequestOnHub(cc.MethodHubName.BET, this.balanceBet, this.betSide);
                this.disableBetAgain(true);
            }

        },
        sendRequestBet: function (betValue, betSide) {
            return cc.DragonTigerController.getInstance().sendRequestOnHub(cc.MethodHubName.BET, betValue, betSide);
        },
        //Bet lai
        onBetAgain: function (sender, unit) {
            unit = parseInt(unit); //He so bet
            let logBet = cc.DragonTigerController.getInstance().getBetLogBySessionID(cc.DragonTigerController.getInstance().getBetLogSession());
            // console.log({logBet});

            if (logBet.length === 0) {
                cc.PopupController.getInstance().showSlotsMessage("Chưa có dữ liệu của phiên trước.");
                return;
            }
            for (let i = 1; i <= unit; i++) {
                logBet.map((betData, index) => {
                    let timeOut = setTimeout(function () {
                        if (cc.DragonTigerController.getInstance().getCurrentState() === cc.DragonTigerState.BETTING && betData.sessionID === cc.DragonTigerController.getInstance().getBetLogSession() - 1) {
                            this.sendRequestBet(betData.value, betData.betSide);
                        } else {
                            try {
                                clearTimeout(timeOut);
                            } catch (e) {
                                console.log(e);
                            }
                        }
                    }.bind(this), index * 120 * i);
                }, this);
            }
            this.disableBetAgain(true);
        },
        //Disable button bet lai, bet x2
        disableBetAgain: function (isDisable) {
            this.btnBetAgain.interactable = !isDisable;
            this.btnBetX2.interactable = !isDisable;
            let color = cc.Color.WHITE;
            if (isDisable) {
                color = cc.Color.GRAY;
            }
            this.nodeBetX2.color = color;
            this.nodeBetAgain.color = color;
        },
        getBetSide: function () {
            return this.betSide;
        },
    });
}).call(this);