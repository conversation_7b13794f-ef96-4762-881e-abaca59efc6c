/*
 * Generated by BeChicken
 * on 6/24/2019
 * version v1.0
 */
(function () {
    cc.DragonTigerButtonSideBet = cc.Class({
        extends: cc.Component,
        properties: {
            btnNodeRong: cc.Node,
            btnNodeHoa: cc.Node,
            btnNodeHo: cc.Node
        },
        onLoad: function () {
            cc.DragonTigerController.getInstance().buttonSideBet = this;
        },
        enableButtonBet: function (enable) {
            this.commonActive(this.btnNodeRong, enable);
            this.commonActive(this.btnNodeHoa, enable);
            this.commonActive(this.btnNodeHo, enable);
        },
        commonActive: function(node, enable) {
            node.getComponent(cc.Button).interactable = enable;
        }

    });
}).call(this);
