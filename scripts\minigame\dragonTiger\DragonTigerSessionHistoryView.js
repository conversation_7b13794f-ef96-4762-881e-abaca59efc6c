/*
 * Generated by BeChicken
 * on 6/13/2019
 * version v1.0
 */
(function () {
    cc.DragonTigerSessionHistoryView = cc.Class({
        extends: cc.Component,
        properties: {
            lstSession: [cc.Sprite],
            spSessions: [cc.SpriteFrame]
        },

        onLoad: function () {
            cc.DragonTigerController.getInstance().setDragonTigerSessionHistoryView(this);
        },
        updateGameHistoryUI: function(lstSession) {
            if (lstSession) {
                lstSession = lstSession.reverse();
                this.gameHistory = lstSession;
                lstSession.map((session, index) => {
                    let spSessionId = parseInt(session.Result) - 1;
                    this.lstSession[index].spriteFrame = this.spSessions[spSessionId]
                })
            }
        },
        //Chi tiet 1 phien
        sessionDetailClicked: function sessionDetailClicked(event, index) {
            if (this.gameHistory && this.gameHistory.length > index) {
                cc.DragonTigerController.getInstance().setDetailIndex(index);
                cc.DragonTigerPopupController.getInstance().createSessionDetailView();
            }
        }

    });
}).call(this);
