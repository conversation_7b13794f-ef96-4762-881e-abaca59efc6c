/*
 * Generated by BeChicken
 * on 6/10/2019
 * version v1.0
 */

var netConfig = require('NetConfig');

(function () {
    cc.DragonTigerView = cc.Class({
        "extends": cc.Component,
        properties: {
            lbSID: cc.Label,
            lbTimer: cc.Label,
            lbTimerPrepare: cc.Label,
            lbTotalUser: cc.Label,

            lbTotalUserWin: cc.Label,

            lbTotalBetRong: cc.Label, //tong tien bet Rong

            lbTotalBetHoa: cc.Label, //tong tien bet Hoa

            lbTotalBetHo: cc.Label, //tong tien bet Ho

            bmfWhite: cc.BitmapFont,
            bmfRed: cc.BitmapFont,

            nodeCardRong: cc.Node,
            nodeCardHo: cc.Node,
            nodeCardBurn: cc.Node,
            nodeCardStart: cc.Node,

            // nodeChat: cc.Node,
            prefabCardBack: cc.Prefab,

            nodeBetSides: cc.Node,
            spriteCardDefault: cc.SpriteFrame,

            //chat
            nodeParentChat: cc.Node,
            prefabChat: cc.Prefab,
        },

        onLoad: function () {
            cc.DragonTigerController.getInstance().initBetLog();
            cc.DragonTigerController.getInstance().setBetLogSession(1);

            this.isPlaying = false;
            this.interval = null;
            this.isActiveChat = false;
            cc.DragonTigerController.getInstance().setDragonTigerView(this);
            this.lastTimeReconnect = (new Date()).getTime();
            this.connectHubDragonTiger();

            this.currentState = -1;
            this.currentTimeEllapse = -1;
            cc.ChatRoomController.getInstance().setHubView(this);
            this.dragonTigerController = cc.DragonTigerController.getInstance();

            //this.initCards();
            this.initAnimationWin();
            this.listAnimationOpenCard = this.mapAnimationCardName();
            this.moveCardToBurn = false;
            this.runSuffler = false;
            this.historyResult = "";
            this.accountId = cc.LoginController.getInstance().getUserId();

            var nodeChat = cc.instantiate(this.prefabChat);
            this.nodeParentChat.addChild(nodeChat);
        },
        onEnable: function () {
            cc.BalanceController.getInstance().updateBalance(cc.BalanceController.getInstance().getBalance());
            cc.PopupController.getInstance().showBusy();
        },
        initCards: function () {
            this.spriteCardBurn = this.nodeCardBurn.getChildByName('card');
        },
        initAnimationWin: function () {
            this.winAnimation = this.nodeBetSides.getComponent(cc.Animation);
        },
        playCardEffect: function (result) {
            switch (result) {
                case cc.DragonTigerBetSide.RONG:
                    if (this.cardBackDragon && this.cardBackDragon._name != "")
                        this.cardBackDragon.getComponent(cc.Animation).play('card-scale');
                    break;
                case cc.DragonTigerBetSide.HOA:
                    if (this.cardBackDragon && this.cardBackDragon._name != "")
                        this.cardBackDragon.getComponent(cc.Animation).play('card-scale');

                    if (this.cardBackTiger && this.cardBackTiger._name != "")
                        this.cardBackTiger.getComponent(cc.Animation).play('card-scale');
                    break;
                case cc.DragonTigerBetSide.HO:
                    if (this.cardBackTiger && this.cardBackTiger._name != "")
                        this.cardBackTiger.getComponent(cc.Animation).play('card-scale');
                    break;
            }
        },
        stopResultEffect: function () {
            //this.unscheduleAllCallbacks();
            this.winAnimation.stop();
        },
        onDestroy: function () {

            cc.DragonTigerController.getInstance().moveChipView.clearPools();
            cc.PopupController.getInstance().hideBusy();

            this.sendRequestOnHub(cc.MethodHubName.EXIT_LOBBY);

            cc.LobbyJackpotController.getInstance().pauseUpdateJackpot(false);

            if (this.interval !== null) {
                clearInterval(this.interval);
            }
            if (this.dragonTigerHub)
                this.dragonTigerHub.disconnect();

            this.unscheduleAllCallbacks();
            cc.DragonTigerController.getInstance().setDragonTigerView(null);

            if (cc.sys.isNative) {
                cc.loader.releaseResDir('dragonTiger/prefabs');
                cc.loader.releaseResDir('dragonTiger/images');
            }
        },

        reset: function () {
            //reset thong tin bet
            this.lbTotalBetRong.string = "";
            this.lbTotalBetHo.string = "";
            this.lbTotalBetHoa.string = "";

            this.isTimer = false;
            this.timer = 0;
            this.currentState = 999;
            if (this.interval !== null) {
                clearInterval(this.interval);
            }
            this.stopResultEffect();
        },

        startTimer: function (remaining) {
            if (this.interval !== null) {
                clearInterval(this.interval);
            }

            var self = this;
            this.timer = remaining;
            this.isTimer = true;

            ////update timer UI
            this.updateTimer(remaining);

            this.interval = setInterval(function () {
                if (self.isTimer) {
                    self.timer -= 1;
                    self.updateTimer(Math.round(self.timer));
                }
            }, 1000);
        },

        stopTimer: function () {
            this.isTimer = false;
            if (this.interval !== null) {
                clearInterval(this.interval);
            }
        },
        updateInfo: function (sessionInfo) {
            let isShuffler = sessionInfo.IsShuffler;
            let state = sessionInfo.CurrentState;
            let timeEllapsed = sessionInfo.Ellapsed;
            this.currentTimeEllapse = timeEllapsed;
            switch (sessionInfo.CurrentState) {

                case cc.DragonTigerState.BETTING:
                    //kiem tra card
                    if (this.currentState !== sessionInfo.CurrentState) {

                        cc.DragonTigerController.getInstance().stopResultEffect();
                        //Active bet lai
                        cc.DragonTigerController.getInstance().disableBetAgain(false);

                        cc.DragonTigerController.getInstance().buttonSideBet.enableButtonBet(true);
                        //this.moveCardBurnToBox();
                        // chia bai
                        this.destroyCardBack();
                        if (timeEllapsed > 24) {
                            cc.PopupController.getInstance().showSlotsMessage("Đặt cửa");
                        }
                        if (timeEllapsed > 24 && !cc.game.isPaused()) {
                            this.cardSlide();
                        } else {
                            //if (!this.cardBackDragon || this.cardBackDragon._name == "") {
                            this.moveCardToBurn = false;
                            this.cardBackDragon = this.createNodeCardResult(cc.DragonTigerBetSide.RONG);
                            this.cardBackDragon.position = this.nodeCardRong.position;

                            this.cardBackTiger = this.createNodeCardResult(cc.DragonTigerBetSide.HO);
                            this.cardBackTiger.position = this.nodeCardHo.position;
                            //}
                        }
                        this.lbSID.string = '#' + sessionInfo.SessionID;
                    }
                    break;
                case cc.DragonTigerState.END_BETTING:
                    cc.DragonTigerController.getInstance().disableBetAgain(true);
                    cc.DragonTigerController.getInstance().buttonSideBet.enableButtonBet(false);
                    if (this.currentState !== sessionInfo.CurrentState) {
                        cc.PopupController.getInstance().showSlotsMessage('Hết thời gian đặt cửa');
                        this.lbSID.string = '#' + sessionInfo.SessionID;
                        if (timeEllapsed < 3 && !cc.game.isPaused() && (!this.cardBackDragon || this.cardBackDragon._name == "")) {
                            this.moveCardToBurn = false;
                            this.cardBackDragon = this.createNodeCardResult(cc.DragonTigerBetSide.RONG);
                            this.cardBackDragon.position = this.nodeCardRong.position;

                            this.cardBackTiger = this.createNodeCardResult(cc.DragonTigerBetSide.HO);
                            this.cardBackTiger.position = this.nodeCardHo.position;
                        }
                    }

                    break;
                case cc.DragonTigerState.RESULT:
                    cc.DragonTigerController.getInstance().buttonSideBet.enableButtonBet(false);
                    this.historyResult = sessionInfo;
                    if (this.currentState !== sessionInfo.CurrentState) {
                        cc.DragonTigerController.getInstance().disableBetAgain(true);
                        this.isPlaying = false;
                        cc.AudioController.getInstance().playSound(cc.DragonTigerAudioTypes.CARD_OPEN);
                        this.currentResult = sessionInfo.Result.Result;
                        let self = this;
                        //hien thi ket qua
                        if (timeEllapsed > 6) {
                            this.showCardResult(sessionInfo);
                        }

                        setTimeout(function () {
                            self.playCardEffect(sessionInfo.Result.Result);
                            if (timeEllapsed > 8) {
                                cc.DragonTigerController.getInstance().moveChipView.chipMoveToCoin(self.currentResult, false, 0);
                            } else {
                                try {
                                    //xoa chip
                                    cc.DragonTigerController.getInstance().moveChipView.removeChips(self.currentResult);
                                } catch (e) {
                                    console.log(e);
                                }

                            }
                            try {
                                cc.DragonTigerController.getInstance().dragonTigerResultEffectView.playEffectWin(sessionInfo.Result.Result);

                            } catch (e) {

                            }

                        }, 1000);

                        this.activeNodeCard(true);
                        this.lbSID.string = '#' + sessionInfo.SessionID;
                    }

                    if (sessionInfo.Ellapsed == 6 && !cc.game.isPaused()) {
                        //this.moveCardBackToCardBurn();
                    }
                    if (isShuffler && sessionInfo.Ellapsed == 4 && !cc.game.isPaused()) {
                        this.runSuffler = true;
                        this.moveCardBurnToBox();
                    }


                    break;
                case cc.DragonTigerState.PREPARE_NEW_SESSION:
                    cc.DragonTigerController.getInstance().buttonSideBet.enableButtonBet(false);
                    if (this.currentState !== sessionInfo.CurrentState) {
                        cc.DragonTigerController.getInstance().disableBetAgain(true);
                        cc.DragonTigerController.getInstance().resetPlayerUI();

                        //Clear session truoc
                        cc.DragonTigerController.getInstance().clearBetLog(cc.DragonTigerController.getInstance().getBetLogSession());
                        //Tao session betlog
                        cc.DragonTigerController.getInstance().setBetLogSession(cc.DragonTigerController.getInstance().getBetLogSession() + 1);
                       /* try {
                            //Xoa bai
                            this.cardBackDragon.destroy();
                            this.cardBackTiger.destroy();
                        } catch (e) {
                            console.log(e)
                        }*/
                        if (timeEllapsed == 3 && !cc.game.isPaused()) {
                            //Di chuyen bai ve hop
                            this.moveCardBackToCardBurn();
                        } else {
                            try {
                                //Xoa bai
                                this.cardBackDragon.destroy();
                                this.cardBackTiger.destroy();
                            } catch (e) {
                                console.log(e)
                            }
                        }

                        this.activeNodeCard(false);

                        cc.DragonTigerController.getInstance().moveChipView.clearChips();

                        //reset
                        cc.DragonTigerController.getInstance().reset();
                        this.lbSID.string = '#' + sessionInfo.SessionID;
                    }
                    break;

            }
            //luu lai state hien tai
            this.currentState = sessionInfo.CurrentState;
            cc.DragonTigerController.getInstance().setCurrentState(parseInt(this.currentState));

            this.startTimer(sessionInfo.Ellapsed);

            this.lbTotalBetRong.string = this.formatNumber(sessionInfo.TotalBetDragon);
            this.lbTotalBetHoa.string = this.formatNumber(sessionInfo.TotalBetTie);
            this.lbTotalBetHo.string = this.formatNumber(sessionInfo.TotalBetTiger);

            //this.updateTotalUser(totalUser);

        },
        formatNumber: function (strNumber) {
            return (parseInt(strNumber) == 0) ? "" : cc.Tool.getInstance().formatNumber(strNumber);
        },
        updateTotalUser: function (totalUser) {
            this.lbTotalUser.string = totalUser.toString();
        },
        cardSlide: function () {
            this.destroyCardBack();

            this.moveCardToBurn = false;
            this.cardSlided = true;
            this.cardBackBurn = this.createNodeCardBack(); //new cc.instantiate(this.prefabCardBack);
            this.cardBackDragon = this.createNodeCardBack(); //new cc.instantiate(this.prefabCardBack);
            this.cardBackTiger = this.createNodeCardBack(); //new cc.instantiate(this.prefabCardBack);
            let dragonTigerCardSlide = cc.DragonTigerController.getInstance().dragonTigerCardSlide;

            let self = this;
            cc.director.getScheduler().schedule(function () {
                cc.AudioController.getInstance().playSound(cc.DragonTigerAudioTypes.CARD_SLIDE_BURN);
                self.cardMoveTo(self.cardBackBurn, self.nodeCardBurn.position, 1);
                dragonTigerCardSlide.playBoxAnimation('d1');
                if (!this.runSuffler) {
                    this.runSuffler = false;
                    dragonTigerCardSlide.playBurnAnimation('up2');
                }

            }, this, 0, 0, 0, false);
            cc.director.getScheduler().schedule(function () {
                cc.AudioController.getInstance().playSound(cc.DragonTigerAudioTypes.CARD_SLIDE_RONG);
                self.cardMoveTo(self.cardBackDragon, self.nodeCardRong.position, 1);
                dragonTigerCardSlide.playBoxAnimation('d2');
                dragonTigerCardSlide.playBurnAnimation('up3');

            }, this, 2, 0, 0, false);
            cc.director.getScheduler().schedule(function () {
                cc.AudioController.getInstance().playSound(cc.DragonTigerAudioTypes.CARD_SLIDE_HO);
                self.cardMoveTo(self.cardBackTiger, self.nodeCardHo.position, 1);
                dragonTigerCardSlide.playBoxAnimation('d3');
                let actionScaleFrom = cc.scaleTo(1, 0.5);
                self.cardBackBurn.runAction(actionScaleFrom);
                setTimeout(function () {
                    if (self.cardBackBurn)
                        self.cardBackBurn.opacity = 0;
                }, 1000);
                dragonTigerCardSlide.playBurnAnimation('up4');

            }, this, 2.3, 0, 0, false);
        },
        createNodeCardBack: function () {
            let nodeCardBack = new cc.instantiate(this.prefabCardBack);
            let spCardBackPosition = this.nodeCardStart.position;
            nodeCardBack.position = spCardBackPosition;
            nodeCardBack.parent = this.nodeCardStart.parent;
            nodeCardBack.rotation = -45;
            nodeCardBack.scale = 0.5;
            return nodeCardBack;
        },

        createNodeCardResult: function (type) {
            let pos = "";
            if (type == cc.DragonTigerBetSide.RONG) {
                pos = this.nodeCardRong.position;
            } else {
                pos = this.nodeCardHo.position;
            }
            let nodeCardResult = new cc.instantiate(this.prefabCardBack);
            nodeCardResult.position = pos;
            nodeCardResult.parent = this.nodeCardStart.parent;
            return nodeCardResult;
        },
        updateMoveChip: function (data, isCurrentUser) {
            // cc.DragonTigerController.getInstance().moveChipView.moveChip(data, isCurrentUser);
            cc.DragonTigerController.getInstance().moveChipView.moveChipWithStartPos(data, isCurrentUser);
        },
        initChipsBet: function (data) {
            cc.DragonTigerController.getInstance().moveChipView.initChipsBet(data);

        },
        //hien thi card default cho cardburn
        setDefaultCardBurn: function () {
            this.nodeCardBurn.active = true;
            this.spriteCardBurn.getComponent(cc.Sprite).spriteFrame = this.spriteCardDefault;
        },
        //di chuyen card ve box
        moveCardBurnToBox: function () {
            let self = this;
            let dragonTigerCardSlide = cc.DragonTigerController.getInstance().dragonTigerCardSlide;
            cc.director.getScheduler().schedule(
                function () {
                    dragonTigerCardSlide.playBurnAnimation('down');
                    dragonTigerCardSlide.playBoxAnimation('redeal');
                },
                self, 0, 0, 0, false
            );
            cc.director.getScheduler().schedule(
                function () {
                    dragonTigerCardSlide.playBurnAnimation('empty');
                    dragonTigerCardSlide.burnStopAnimation();
                    try {
                        self.cardBackDragon.destroy();
                        self.cardBackTiger.destroy();
                    } catch (e) {
                        console.log(e)
                    }
                },
                self, 0, 0, 1, false
            );
        },
        //hien thi ket qua
        showCardResult: function (sessionInfo) {
            //this.destroyCardBack();
            //this.activeCardBack(false);
            try {
                let dragonCard = sessionInfo.Result.DragonCard;
                let tigerCard = sessionInfo.Result.TigerCard;

                if (!this.cardBackDragon || this.cardBackDragon._name == "") {
                    this.cardBackDragon = this.createNodeCardResult(cc.DragonTigerBetSide.RONG);
                    this.cardBackTiger = this.createNodeCardResult(cc.DragonTigerBetSide.HO);
                }

                this.runAnimationCard(this.cardBackDragon.getComponent(sp.Skeleton), this.listAnimationOpenCard[dragonCard - 1]);
                this.runAnimationCard(this.cardBackTiger.getComponent(sp.Skeleton), this.listAnimationOpenCard[tigerCard - 1]);
            } catch (e) {

            }


        },
        listCardBack: function () {
            return [this.cardBackBurn, this.cardBackDragon, this.cardBackTiger];
        },
        //destroy card
        destroyCardBack: function () {
            try {
                this.listCardBack().map(node => node.destroy());
                // this.cardBackDragon.destroy();
                // this.cardBackBurn.destroy();
                // this.cardBackTiger.destroy();
            } catch (e) {

            }
        },

        //active cardback
        activeCardBack: function (active) {
            try {
                this.listCardBack().map(node => node.active = active);
            } catch (e) {
            }
        },
        //di chuyen card ve cardBurn
        moveCardBackToCardBurn: function () {
            if (this.moveCardToBurn)
                return;
            this.moveCardToBurn = true;

            if (!this.cardBackDragon)
                return;
            let self = this;
            //this.activeCardBack(true);
            cc.director.getScheduler().schedule(function () {
                if (self.cardBackDragon._name != "") {
                    // chay animation card up
                    self.runAnimationCard(self.cardBackDragon.getComponent(sp.Skeleton), "00back");
                    // di chuyen catd ve hop dung card
                    self.cardMoveTo(self.cardBackDragon, self.nodeCardBurn.position, 1);
                    self.cardBackDragon.getComponent(cc.Animation).stop('card-scale');
                    let actionScaleFrom = cc.scaleTo(1, 0.6);
                    self.cardBackDragon.runAction(actionScaleFrom);
                    self.cardBackDragon.getComponent(cc.Animation).stop('card-scale');
                }

            }, this, 0, 0, 0, false);
            cc.director.getScheduler().schedule(function () {
                if (self.cardBackTiger._name != "") {
                    // chay animation card up
                    self.runAnimationCard(self.cardBackTiger.getComponent(sp.Skeleton), "00back");
                    // di chuyen catd ve hop dung card
                    self.cardMoveTo(self.cardBackTiger, self.nodeCardBurn.position, 1);
                    let actionScaleFrom = cc.scaleTo(1, 0.6);
                    self.cardBackTiger.runAction(actionScaleFrom);
                    self.cardBackTiger.getComponent(cc.Animation).stop('card-scale');
                }
            }, this, 0.3, 0, 0, false);
            cc.director.getScheduler().schedule(function () {
                //chay animation hop dung card
                cc.DragonTigerController.getInstance().dragonTigerCardSlide.playBurnAnimation('up4');
            }, this, 0.6, 0, 0, false);
        },
        //goi animation mo card
        runAnimationCard: function (skeleton, animationName) {
            skeleton.clearTracks();
            skeleton.setAnimation(0, animationName, false);
        },
        //map ten animation cua card
        mapAnimationCardName: function () {
            let bai = ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "J", "Q", "K"];
            let chat = ["b", "t", "r", "c"];
            var listMap = [];
            for (let c = 0; c < chat.length; c++) {
                for (let b = 0; b < bai.length; b++) {
                    listMap.push(bai[b] + chat[c]);
                }
            }
            return listMap;

        },
        activeNodeCard: function (active) {
            this.nodeCardRong.active = active;
            this.nodeCardHo.active = active;

        },
        updateTimer: function (time) {
            if (time < 1) return;
            let strTime = time;
            let color = cc.Color.GREEN;
            switch (this.currentState) {
                case cc.DragonTigerState.BETTING:
                case cc.DragonTigerState.END_BETTING:
                    this.activeNodeTime(true);
                    if (time <= 3) {
                        color = cc.Color.RED;
                    }
                    this.lbTimer.node.color = color;
                    this.lbTimer.string = strTime;

                    this.lbTimer.node.parent.active = true;
                    this.lbTimerPrepare.node.parent.active = false;
                    break;
                case cc.DragonTigerState.RESULT:
                    this.activeNodeTime(false);
                    color = cc.Color.WHITE;
                    this.lbTimer.node.parent.active = false;
                    this.lbTimerPrepare.node.parent.active = true;
                    this.lbTimerPrepare.string = strTime;
                    break;
                case cc.DragonTigerState.PREPARE_NEW_SESSION:
                    this.activeNodeTime(false);
                    color = cc.Color.WHITE;
                    this.lbTimerPrepare.node.parent.active = true;
                    this.lbTimerPrepare.string = strTime;
                    this.lbTimer.node.parent.active = false;
                    break;
            }
        },
        activeNodeTime: function(isActive) {
            this.lbTimer.node.parent.parent.active = isActive;
        },
        disconnectAndLogout: function () {
            if (this.dragonTigerHub) {
                this.dragonTigerHub.disconnect();
            }
            this.lastTimeReconnect = (new Date()).getTime();
        },

        connectHubDragonTiger: function () {
            var negotiateCommand = new cc.NegotiateCommand;
            negotiateCommand.execute(this, cc.SubdomainName.DRAGON_TIGER);
        },

        reconnect: function () {
            // console.log('dragonTigerHub reconnect');
            this.lastTimeReconnect = (new Date()).getTime();
            this.dragonTigerHub.connect(this, cc.HubName.DragonTigerHub, this.connectionToken, true);
        },

        sendRequestOnHub: function (method, data1, data2) {
            switch (method) {
                case cc.MethodHubName.ENTER_LOBBY:
                    this.dragonTigerHub.enterLobby();
                    break;
                case cc.MethodHubName.EXIT_LOBBY:
                    this.dragonTigerHub.exitLobby();
                    break;
                case cc.MethodHubName.PLAY_NOW:
                    this.dragonTigerHub.playNow();
                    break;
                case cc.MethodHubName.BET:
                    this.dragonTigerHub.bet(data1, data2);
                    break;
                case cc.MethodHubName.SEND_MESSAGE:
                    this.dragonTigerHub.sendRoomMessage(data1);
                    break;

            }
        },

        onSlotsNegotiateResponse: function (response) {
            this.connectionToken = response.ConnectionToken;
            this.dragonTigerHub = new cc.Hub;
            this.dragonTigerHub.connect(this, cc.HubName.DragonTigerHub, response.ConnectionToken);
        },

        onHubMessage: function (response) {
            if (response.M !== undefined && response.M.length > 0) {
                let self = this;
                let res = response.M;
                res.map(m => {
                    switch (m.M) {
                        //Thoat game
                        case cc.MethodHubOnName.PLAYER_LEAVE:
                            cc.DragonTigerController.getInstance().unRegisterAllPlayer();
                            this.playerLeave(m.A);
                            break;
                        case cc.MethodHubOnName.JOIN_GAME:
                            // console.log("JOIN_GAME", m.A);
                            cc.DragonTigerController.getInstance().updatePlayerInfor(m.A[0]);
                            break;
                        //vao Phong
                        case cc.MethodHubOnName.SESSION_INFO:
                            var data = m.A[0];
                            this.updateInfo(data);
                            cc.DragonTigerController.getInstance().setSID(data.SessionID);
                            break;
                        //vao Phong
                        case cc.MethodHubOnName.GAME_HISTORY:
                            // update thong tin khi vao lai phong
                            this.dragonTigerController.updateGameHistoryUI(m.A[0]);
                            break;
                        //thong tin bet cu
                        case cc.MethodHubOnName.BET_OF_ACCOUNT:
                            // update thong tin tong bet cua user
                            this.dragonTigerController.updateBetInfoFromServer(m.A[0]);
                            // ko lam
                            // cc.DragonTigerController.getInstance().moveChipView.createListChipOfUser(m.A[0].LstBet);
                            break;

                        //bet thanh cong
                        case cc.MethodHubOnName.BET_SUCCESS:
                            var data = m.A[0];

                            cc.PopupController.getInstance().hideBusy();
                            this.isPlaying = true;
                            //Push betValue vao betLog
                            let sessionID = this.dragonTigerController.getBetLogSession();

                            this.dragonTigerController.setBetLog({
                                sessionID: sessionID,
                                value: m.A[0].BetValue,
                                betSide: m.A[0].BetSide
                            });

                            //update lai balance
                            cc.BalanceController.getInstance().updateRealBalance(m.A[1]);
                            cc.BalanceController.getInstance().updateBalance(m.A[1]);
                            //Cap nhat tong tien bet cua player
                            this.dragonTigerController.updateTotalBetValue(m.A[0].BetSide, m.A[0].SumaryBet);
                            //Cap nhat balance UI cua player
                            this.dragonTigerController.updateBalanceCurrPlayer(m.A[1]);
                            // let currentBetValue = m.A[0].BetValue;//cc.DragonTigerController.getInstance().dragonTigerBetView.balanceBet;
                            // let currentBetSide = cc.DragonTigerController.getInstance().dragonTigerBetView.betSide;
                            // di chuyen chip vao vi tri bet
                            this.updateMoveChip([m.A[0].BetValue, m.A[0].BetSide], true);
                            // cc.DragonTigerController.getInstance().moveChipView.updateListChipOfUser(currentBetSide,currentBetValue);
                            // cc.PopupController.getInstance().showSlotsMessage('Đặt cược thành công');

                            cc.DDNA.getInstance().betSummary(cc.DDNAGame.DRAGON_TIGER, m.A[0].BetValue, cc.DragonTigerController.getInstance().getSID());
                            break;

                        case cc.MethodHubOnName.WIN_RESULT:
                            var data = m.A[0];
                            try {
                                setTimeout(function () {
                                    cc.BalanceController.getInstance().updateRealBalance(data.Balance);//cap nhat lai tong tien user
                                    cc.BalanceController.getInstance().updateBalance(data.Balance);
                                    //reset lai trang thai khoi tao
                                    cc.DragonTigerController.getInstance().resetBetInfo();
                                    //cc.DragonTigerPopupController.getInstance().showPopupWin(data.Award);
                                    cc.DragonTigerController.getInstance().winResult(data);
                                }, 2500);
                            } catch (e) {

                            }


                            break;
                        //Thong tin win cua vip
                        case cc.MethodHubOnName.WIN_RESULT_VIP:
                            if (m.A.length > 0) {
                                try {
                                    setTimeout(function () {
                                        cc.DragonTigerController.getInstance().winResultVip(m.A[0]);
                                    }, 2500);
                                } catch (e) {

                                }

                            }
                            break;
                        case cc.MethodHubOnName.MESSAGE:
                            var data = m.A[0];
                            if (data.Description) {
                                cc.PopupController.getInstance().showMessage(data.Description);
                            } else if (data.Message) {
                                cc.PopupController.getInstance().showMessage(data.Message);
                            } else {
                                cc.PopupController.getInstance().showMessage(data);
                            }
                            break;
                        case cc.MethodHubOnName.TOTAL_WIN_MONEY:
                            let dataTotalWin = m.A[0];
                            try {
                                cc.DragonTigerController.getInstance().moveChipView.chipMoveToSideWin(self.currentResult);
                                setTimeout(function () {
                                    try {
                                        cc.DragonTigerController.getInstance().moveChipView.chipMoveToCoin(self.currentResult, true, 0);
                                    } catch (e) {
                                        console.log(e);
                                    }

                                }, 1500);
                            } catch (e) {
                                console.log(e);
                            }

                            if (parseInt(dataTotalWin) > 0) {
                                setTimeout(function () {
                                    try {
                                        self.lbTotalUserWin.string = "+" + cc.Tool.getInstance().formatNumber(dataTotalWin);
                                        self.lbTotalUserWin.node.parent.getComponent(cc.Animation).play('total-money-animation');
                                    } catch (e) {
                                        console.log(e);
                                    }

                                }, 2000)
                            }
                            break;
                        case cc.MethodHubOnName.REJOIN:
                           
                            if (data.length > 0) {
                                let self = this;
                                cc.AudioController.getInstance().playSound(cc.DragonTigerAudioTypes.BET);
                                data.map(values => {
                                    if (values.length == 0) return;
                                    values.map(bet => {
                                        let betVal = [bet["BetValue"], bet["BetSide"]];
                                        self.initChipsBet(betVal);
                                    });
                                });
                            }
                            break;
                        case cc.MethodHubOnName.BET_SESSION:
                            if (m.A[0].length > 0) {
                                let self = this;
                                cc.AudioController.getInstance().playSound(cc.DragonTigerAudioTypes.BET);
                                m.A[0].map(values => {
                                    if (values.length == 0) return;
                                    values.map(bet => {
                                        let betVal = [bet.BetValue, bet.BetSide, bet.AccountID];
                                        self.initChipsBet(betVal);
                                    });
                                });
                            }
                            break;

                        case cc.MethodHubOnName.BET_USER:
                            var data = m.A;
                            // console.log("BET_USER: ", m.A);
                            if (data[2] != this.accountId) {
                                this.updateMoveChip(data, false);
                                //Câp nhat balance cua nguoi choi
                                cc.DragonTigerController.getInstance().updatePlayerBalance(data);
                            }
                            break;
                        //Tong so nguoi choi
                        case cc.MethodHubOnName.SUMMARY_PLAYER:
                            //var data = m.A;
                            //this.updateMoveChip(data, false);
                            this.updateTotalUser(m.A[0]);
                            break;
                        //Cap nhat danh sach player
                        case cc.MethodHubOnName.VIP_PLAYERS:
                            let dataPlayer = m.A[0];
                            // console.log("VIP_PLAYERS: ", m.A);
                            if (dataPlayer.length > 0) {
                                cc.DragonTigerController.getInstance().updatePlayersUI(dataPlayer);
                            }
                            break;
                        case cc.MethodHubOnName.OTHER_DEVICE:
                            // m.A[0] = ma loi , m.A[1] = message
                            //vao phong choi tren thiet bi khac
                            cc.PopupController.getInstance().showPopupOtherDevice(m.A[1], cc.GameId.DRAGON_TIGER);
                            break;
                        //nhan message chat
                        case cc.MethodHubOnName.RECEIVE_MESSAGE:
                            cc.ChatRoomController.getInstance().addChatContent(m.A);
                            cc.DragonTigerController.getInstance().playerShowBubbleChat(m.A);
                            break;
                    }
                });
            } else if (response.R && response.R.AccountID) {
                this.sendRequestOnHub(cc.MethodHubName.PLAY_NOW);
                //sau khi enterLobby
                //cc.PopupController.getInstance().showBusy();
                cc.PopupController.getInstance().hideBusy();

            } else {
                //PING PONG
                if (response.I) {
                    this.dragonTigerHub.pingPongResponse(response.I);
                }
            }
        },
        playerLeave: function (info) {
            var accID = info[0];
            if (accID === cc.LoginController.getInstance().getUserId()) {
                var message = info[1];
                cc.LobbyController.getInstance().destroyDynamicView(null);
                cc.PopupController.getInstance().showMessage(message)
            }
        },

        onHubOpen: function () {
            cc.PopupController.getInstance().hideBusy();
            this.sendRequestOnHub(cc.MethodHubName.ENTER_LOBBY);
            cc.PopupController.getInstance().showBusy();
        },

        onHubClose: function () {
            //reconnect
            // console.log((new Date()).getTime() - this.lastTimeReconnect);
            if ((new Date()).getTime() - this.lastTimeReconnect >= netConfig.RECONNECT_TIME * 1000) {
                this.reconnect();
            } else {
                cc.director.getScheduler().schedule(this.reconnect, this, netConfig.RECONNECT_TIME, 0, 0, false);
            }
        },

        onHubError: function () {
            cc.PopupController.getInstance().hideBusy();
        },

        //huong dan
        helpClicked: function () {
            cc.DragonTigerPopupController.getInstance().createHelpView();
        },

        //lich su dat cuoc
        historyClicked: function () {
            cc.DragonTigerPopupController.getInstance().createHistoryView();
        },

        //bang xep hang dat cuoc
        topClicked: function () {
            cc.DragonTigerPopupController.getInstance().createTopView();
        },
        //soi cau
        graphClicked: function () {
            cc.DragonTigerPopupController.getInstance().createGraphView();
        },

        backClicked: function () {
            cc.LobbyController.getInstance().destroyDynamicView(null);
			 cc.LobbyController.getInstance().offuserguest(true);

        },

        //move position
        cardMoveTo: function (node, pos, duration) {
            let actionMoveTo = cc.moveTo(duration, pos);
            actionMoveTo.easing(cc.easeInOut(3.0));
            node.runAction(actionMoveTo);

            let actionScaleFrom = cc.scaleTo(duration, 0.5);
            node.runAction(actionScaleFrom);
            let actionScaleTo = cc.scaleTo(duration, 1, 1);
            node.runAction(actionScaleTo);
            let actionRotateTo = cc.rotateTo(duration, 0);
            node.runAction(actionRotateTo);
        },

        chatClicked: function () {
            cc.ChatRoomController.getInstance().showChat();
        },

    });
}).call(this);
