/*
 * Generated by BeChicken
 * on 6/20/2019
 * version v1.0
 */

(function () {
    cc.DragonTigerChatItem = cc.Class({
        "extends": cc.Component,
        properties: {
            nodeUser: cc.Node,
            lbSID: cc.Label,
            lbName: cc.Label,
            lbMessage: cc.Label,

            rtAdmin: cc.RichText,
        },

        updateItem: function(item, itemID) {
            if (item.ad) {
                this.rtAdmin.node.active = true;
                this.nodeUser.active = false;

                this.rtAdmin.string = this.formatChatUser(item);
            } else {
                this.nodeUser.active = true;
                this.rtAdmin.node.active = false;

                this.lbSID.string = cc.Config.getInstance().getServiceNameNoFormat(item.s);
                this.lbName.string = item.n + ':';
                this.lbMessage.string = item.c;


                // this.lbName.node.color = cc.Color.CYAN;
            }

            this.item = item;
            this.itemID = itemID;
        },


        formatChatUser: function (chatItem) {//
            var hubName = cc.Config.getInstance().getServiceName(chatItem.s.toString());
            //chat cua admin
            if (chatItem.ad) {//
                return '<color=#ff6186>' + chatItem.n + ': </color>' + chatItem.c;
            } else {
                return hubName + '<color=#fadb32>' + chatItem.n + ': </color>' + chatItem.c;
            }
        },
    });
}).call(this);
