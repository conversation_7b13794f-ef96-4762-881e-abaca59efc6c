/*
 * Generated by BeChicken
 * on 6/10/2019
 * version v1.0
 */

(function () {
    var DragonTigerPopupController;

    DragonTigerPopupController = (function () {
        var instance;

        function DragonTigerPopupController() {

        }

        instance = void 0;

        DragonTigerPopupController.getInstance = function () {
            if (instance === void 0) {
                instance = this;
            }
            return instance.prototype;
        };

        DragonTigerPopupController.prototype.setDragonTigerPopupView = function (dragonTigerPopupView) {
            return this.dragonTigerPopupView = dragonTigerPopupView;
        };

        DragonTigerPopupController.prototype.createGraphView = function () {
            return this.dragonTigerPopupView.createGraphView();
        };

        DragonTigerPopupController.prototype.destroyGraphView = function () {
            return this.dragonTigerPopupView.destroyGraphView();
        };

        DragonTigerPopupController.prototype.createSessionDetailView = function () {
            return this.dragonTigerPopupView.createSessionDetailView();
        };

        DragonTigerPopupController.prototype.destroySessionDetailView = function () {
            return this.dragonTigerPopupView.destroySessionDetailView();
        };

        DragonTigerPopupController.prototype.createTopView = function () {
            return this.dragonTigerPopupView.createTopView();
        };

        DragonTigerPopupController.prototype.destroyTopView = function () {
            return this.dragonTigerPopupView.destroyTopView();
        };

        DragonTigerPopupController.prototype.createHelpView = function () {
            return this.dragonTigerPopupView.createHelpView();
        };

        DragonTigerPopupController.prototype.destroyHelpView = function () {
            return this.dragonTigerPopupView.destroyHelpView();
        };

        DragonTigerPopupController.prototype.createHistoryView = function () {
            return this.dragonTigerPopupView.createHistoryView();
        };

        DragonTigerPopupController.prototype.destroyHistoryView = function () {
            return this.dragonTigerPopupView.destroyHistoryView();
        };

        DragonTigerPopupController.prototype.showPopupWin = function (amount) {
            return this.dragonTigerPopupView.showPopupWin(amount);
        };
        DragonTigerPopupController.prototype.hidePopupWin = function () {
            return this.dragonTigerPopupView.hidePopupWin();
        };
        DragonTigerPopupController.prototype.createGroupUserView = function () {
            return this.dragonTigerPopupView.createGroupUserView();
        };
        DragonTigerPopupController.prototype.destroyGroupUserView = function () {
            return this.dragonTigerPopupView.destroyGroupUserView();
        };
        return DragonTigerPopupController;

    })();

    cc.DragonTigerPopupController = DragonTigerPopupController;

}).call(this);

