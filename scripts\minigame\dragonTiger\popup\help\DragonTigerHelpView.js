/*
 * Generated by BeChicken
 * on 6/11/2019
 * version v1.0
 */
(function () {
    cc.DragonTigerHelpView = cc.Class({
        "extends": cc.PopupBase,
        properties: {

        },

        onLoad: function () {
            this.animation = this.node.getComponent(cc.Animation);
        },

        closeFinished: function () {
            cc.DragonTigerPopupController.getInstance().destroyHelpView();
        },
    });
}).call(this);
