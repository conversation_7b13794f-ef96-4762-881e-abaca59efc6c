# <PERSON><PERSON> thống Top Sicbo

## Tổng quan
Hệ thống top Sicbo được thiết kế để lưu trữ và quản lý danh sách người chơi thắng lớn, bao gồ<PERSON> cả user thật và bot. <PERSON><PERSON> thống tự động reset vào 00h tối hàng ngày và tạo ra các tên bot mới.

## C<PERSON><PERSON> tính năng chính

### 1. <PERSON><PERSON>u trữ dữ liệu top
- Lưu trữ thông tin user thật và bot winners trong localStorage
- Tự động cập nhật khi có user thắng lớn (>= 1M)
- Sắp xếp theo số tiền thắng giảm dần
- Giới hạn top 50 players, trong đó tối đa 20 user thật

### 2. Reset hàng ngày
- Tự động reset top vào 00h tối (midnight)
- <PERSON><PERSON><PERSON> dữ liệu cũ và tạo session mới
- <PERSON><PERSON><PERSON> tra reset mỗi 30 phút
- <PERSON><PERSON><PERSON> thời gian reset cuối cùng

### 3. Tên bot động
- <PERSON><PERSON><PERSON> 80 prefix names từ anime, gaming, nature, royal themes
- Hơn 60 suffix names đa dạng
- Tạo tên bot unique cho mỗi session
- Hỗ trợ themed bot names (anime, gaming, nature, royal)

### 4. Cập nhật realtime
- Tự động cập nhật top khi user thắng >= 1M
- Hỗ trợ cập nhật từ cả client và server response
- Không duplicate user trong top

## Cấu trúc file

### Core Files
- `TaiXiuSicboTopView.js` - Main top view với logic quản lý dữ liệu
- `SicboBotNames.js` - Module tạo tên bot động
- `TaiXiuSicboResultView.js` - Cập nhật top từ client-side calculation
- `TaiXiuSicboPortalView.js` - Cập nhật top từ server response

### Test Files
- `SicboTopSystemTest.js` - Test suite cho hệ thống
- `README_TOP_SYSTEM.md` - Tài liệu hướng dẫn

## Cách sử dụng

### 1. Khởi tạo hệ thống
Hệ thống tự động khởi tạo khi TaiXiuSicboTopView được load:
```javascript
// Tự động gọi trong onLoad()
this.initTopDataManager();
this.checkDailyReset();
```

### 2. Thêm winner vào top
```javascript
// Từ TaiXiuSicboTopView
topView.addWinnerToTop(userName, winAmount);

// Hoặc trực tiếp vào localStorage
this.saveWinnerToLocalStorage(userName, winAmount);
```

### 3. Tạo bot names
```javascript
// Random bot names
var botNames = cc.SicboBotNames.generateBotNamesForSession(10);

// Themed bot names
var animeNames = cc.SicboBotNames.generateThemedBotNames('anime', 5);

// Bot winners với awards
var botWinners = cc.SicboBotNames.generateBotWinners(8);
```

### 4. Reset manual (for testing)
```javascript
// Từ TaiXiuSicboTopView
topView.performDailyReset();
```

## Cấu hình

### localStorage Keys
- `sicbo_top_data` - Dữ liệu top players
- `sicbo_last_reset` - Thời gian reset cuối cùng

### Thresholds
- Minimum win để vào top: 5,000,000 (5M)
- Bot award range: 10,000,000 - 60,000,000 (10M - 60M)
- Bot bet range: 500,000 - 5,500,000 (500K - 5.5M)
- Max real users in top: 20
- Max total players in top: 50
- Reset time: 00:00 (midnight)
- Reset check interval: 30 minutes
- Number of bot bets per session: 10-30

### Data Structure
```javascript
{
  realUsers: [
    {
      UserName: "player1",
      Award: 15000000,
      IsBot: false
    }
  ],
  bots: [
    {
      UserName: "gintama123pro",
      Award: 25000000,
      IsBot: true
    }
  ],
  lastUpdate: 1640995200000
}
```

## Testing

### Chạy test suite
```javascript
// Trong console hoặc debug mode
cc.SicboTopSystemTest.runAllTests();

// Test specific functions
cc.SicboTopSystemTest.testBotNameGeneration();
cc.SicboTopSystemTest.testLocalStorage();
cc.SicboTopSystemTest.debugTest();
```

### Manual testing
1. Mở game Sicbo
2. Thắng >= 1M để trigger top update
3. Mở top view để xem kết quả
4. Kiểm tra localStorage data
5. Test reset bằng cách thay đổi system time

## Troubleshooting

### Common Issues
1. **Top không cập nhật**: Kiểm tra console logs, đảm bảo win amount >= 1M
2. **Bot names không đổi**: Kiểm tra SicboBotNames module load correctly
3. **Reset không hoạt động**: Kiểm tra system time và reset check interval
4. **localStorage errors**: Kiểm tra browser localStorage permissions

### Debug Commands
```javascript
// Xem dữ liệu hiện tại
console.log(JSON.parse(cc.sys.localStorage.getItem('sicbo_top_data')));

// Force reset
cc.sys.localStorage.removeItem('sicbo_top_data');
cc.sys.localStorage.removeItem('sicbo_last_reset');

// Test bot generation
console.log(cc.SicboBotNames.generateBotWinners(10));
```

## Performance Notes
- localStorage operations are synchronous but fast
- Bot name generation is lightweight
- Reset check runs every 30 minutes (low impact)
- Top data limited to 50 entries to prevent memory issues

## Security Features
- Anti-debug protection (F12, Developer Tools disabled)
- Console methods disabled
- Right-click context menu disabled
- Keyboard shortcuts blocked
- Developer tools detection and prevention

## Future Enhancements
- Server-side top data backup
- More sophisticated bot behavior patterns
- Analytics tracking for top performance
- Custom themes for special events
