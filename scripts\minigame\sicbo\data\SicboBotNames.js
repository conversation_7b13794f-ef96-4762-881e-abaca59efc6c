/**
 * <PERSON><PERSON><PERSON> Bot Names Generator
 * Tạo tên bot động cho hệ thống top Sicbo
 */

(function () {
    var SicboBotNames = {
        // <PERSON>h sách prefix cho tên bot
        prefixes: [
            "long", "phung", "rong", "ho", "su", "bao", "chim", "ca",
            "vua", "hoang", "vuong", "cong", "duc", "bach", "kim", "ngan",
            "thien", "dia", "nhan", "than", "linh", "hon", "ma", "quy",
            "dao", "kiem", "giap", "khien", "cung", "ten", "phao", "dan",
            "lua", "nuoc", "dat", "gio", "sam", "set", "mua", "tuyet",
            "bong", "sang", "toi", "dem", "ngay", "thang", "nam", "gio",
            "manh", "khoe", "nhanh", "chay", "bay", "nhay", "leo", "boi",
            "sieu", "dai", "to", "nho", "cao", "thap", "rong", "hep",
            "thong", "khon", "gioi", "hay", "dep", "xau", "tot", "xau",
            "vang", "bac", "dong", "sat", "thep", "da", "go", "tre",
            "hoa", "co", "la", "cay", "rung", "nui", "song", "bien",
            "troi", "may", "sao", "trang", "mat", "anh", "em", "ban",
            "yeu", "thuong", "ghet", "buon", "vui", "cuoi", "khoc", "hat",
            "nhac", "mua", "xem", "nghe", "noi", "viet", "doc", "hoc",
            "choi", "lam", "an", "ngu", "thuc", "di", "ve", "den"
        ],

        // Danh sách suffix cho tên bot
        suffixes: [
            "pro", "vip", "sieu", "dai", "to", "manh", "khoe", "gioi",
            "thang", "win", "lucky", "may", "tai", "loc", "phat", "dat",
            "88", "99", "168", "888", "999", "68", "86", "89", "98",
            "king", "vuong", "hoang", "chua", "ong", "ba", "anh", "chi",
            "dep", "xinh", "cute", "cool", "hot", "top", "best", "ace",
            "star", "sao", "ngoi", "trang", "vang", "bac", "kim", "ngan",
            "rong", "phung", "long", "ho", "su", "bao", "ca", "chim",
            "nhanh", "chay", "bay", "nhay", "leo", "boi", "lat", "xoay",
            "xanh", "do", "vang", "tim", "hong", "den", "trang", "nau",
            "lon", "nho", "cao", "thap", "day", "mong", "rong", "hep",
            "moi", "cu", "tot", "xau", "dep", "xinh", "sang", "toi",
            "nong", "lanh", "am", "ret", "kho", "uot", "ngot", "chua"
        ],

        // Tạo tên bot ngẫu nhiên
        generateRandomName: function() {
            var prefix = this.prefixes[Math.floor(Math.random() * this.prefixes.length)];
            var suffix = this.suffixes[Math.floor(Math.random() * this.suffixes.length)];
            var number = Math.floor(Math.random() * 999) + 1;
            
            // Có 30% cơ hội không có suffix
            if (Math.random() < 0.3) {
                return prefix + number;
            }
            
            return prefix + number + suffix;
        },

        // Tạo danh sách tên bot cho một session
        generateBotNamesForSession: function(count) {
            var names = [];
            var usedNames = new Set();
            
            count = count || (Math.floor(Math.random() * 8) + 5); // 5-12 bot
            
            while (names.length < count && names.length < 50) { // Giới hạn tối đa 50 để tránh vòng lặp vô hạn
                var name = this.generateRandomName();
                if (!usedNames.has(name)) {
                    names.push(name);
                    usedNames.add(name);
                }
            }
            
            return names;
        },

        // Tạo bot winners với tên động
        generateBotWinners: function(count) {
            try {
                var botNames = this.generateBotNamesForSession(count);
                var botWinners = [];

                for (var i = 0; i < botNames.length; i++) {
                    var randomAward = Math.floor(Math.random() * 50000000) + 10000000; // 10M - 60M

                    botWinners.push({
                        UserName: botNames[i],
                        Award: randomAward,
                        IsBot: true
                    });
                }

                return botWinners;
            } catch (error) {
                console.log("Generate bot winners error:", error);
                return [];
            }
        },

        // Lấy tên bot theo theme cụ thể
        generateThemedBotNames: function(theme, count) {
            var themedPrefixes = [];
            count = count || 10;
            
            switch(theme) {
                case 'anime':
                    themedPrefixes = [
                        "gintama", "naruto", "onepiece", "dragonball", "attacktitan", 
                        "demonslayer", "jujutsu", "myheroacademia", "bleach", "hunterx"
                    ];
                    break;
                case 'gaming':
                    themedPrefixes = [
                        "kingofgame", "luckystar", "cyber", "digital", "matrix",
                        "neon", "laser", "quantum", "turbo", "mega"
                    ];
                    break;
                case 'nature':
                    themedPrefixes = [
                        "dragon", "tiger", "lion", "eagle", "shark", "wolf",
                        "phoenix", "storm", "thunder", "lightning"
                    ];
                    break;
                case 'royal':
                    themedPrefixes = [
                        "king", "emperor", "lord", "duke", "prince",
                        "golden", "diamond", "platinum", "crystal", "emerald"
                    ];
                    break;
                default:
                    return this.generateBotNamesForSession(count);
            }
            
            var names = [];
            var usedNames = new Set();
            
            while (names.length < count && names.length < 30) {
                var prefix = themedPrefixes[Math.floor(Math.random() * themedPrefixes.length)];
                var suffix = this.suffixes[Math.floor(Math.random() * this.suffixes.length)];
                var number = Math.floor(Math.random() * 999) + 1;
                var name = prefix + number + suffix;
                
                if (!usedNames.has(name)) {
                    names.push(name);
                    usedNames.add(name);
                }
            }
            
            return names;
        }
    };

    // Export module
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = SicboBotNames;
    } else {
        cc.SicboBotNames = SicboBotNames;
    }
})();
