/**
 * Created by Nofear on 3/15/2019.
 */

var SicboBotNames = require("../../../data/SicboBotNames");

(function () {
    cc.TaiXiuSicboTopView = cc.Class({
        "extends": cc.PopupBase,
        properties: {
            TaiXiuSicboTopListView: cc.TaiXiuSicboTopListView,
        },

        onLoad: function () {
            this.animation = this.node.getComponent(cc.Animation);
            this.node.zIndex = cc.NoteDepth.POPUP_TAIXIU_SICBO;

            // Khởi tạo hệ thống quản lý top data
            this.initTopDataManager();
        },

        onEnable: function () {
            var self = this;
            var delay = 0.2;
            cc.director.getScheduler().schedule(function () {
                self.getTopSessionWinners();
            }, this, 1, 0, delay, false);

            this.animation.play('openPopup');

            // <PERSON><PERSON><PERSON> tra reset hàng ngày
            this.checkDailyReset();
        },

        // Khởi tạo hệ thống quản lý dữ liệu top
        initTopDataManager: function() {
            this.TOP_DATA_KEY = 'sicbo_top_data';
            this.LAST_RESET_KEY = 'sicbo_last_reset';
            this.RESET_HOUR = 0; // 00h tối
        },

        getTopSessionWinners: function () {
            var txsicboGetBigWinnersCommand = new cc.TXSICBOGetBigWinnersCommand;
            txsicboGetBigWinnersCommand.execute(this);
        },

        onTXGetBigWinnersResponse: function (response) {
            var list = response;

            try {
                // Lấy dữ liệu top đã lưu từ localStorage
                var savedTopData = this.getSavedTopData();

                // Kết hợp dữ liệu từ server và dữ liệu đã lưu
                if (list !== null && Array.isArray(list)) {
                    // Thêm dữ liệu user thật đã lưu
                    if (savedTopData.realUsers && savedTopData.realUsers.length > 0) {
                        list = list.concat(savedTopData.realUsers);
                    }

                    // Thêm bot vào danh sách top winners
                    var botWinners = this.generateBotWinners();
                    if (botWinners && botWinners.length > 0) {
                        list = list.concat(botWinners);
                    }

                    // Sắp xếp lại theo Award giảm dần
                    list.sort(function(a, b) {
                        return (b.Award || 0) - (a.Award || 0);
                    });

                    // Giới hạn top 50
                    if (list.length > 50) {
                        list = list.slice(0, 50);
                    }

                    // Lưu dữ liệu mới vào localStorage
                    this.saveTopData(list);
                }
            } catch (error) {
                console.log("Top list error:", error);
            }

            if (list !== null && list.length > 0) {
                this.TaiXiuSicboTopListView.resetList();
                this.TaiXiuSicboTopListView.initialize(list);
            }
        },

        // Hàm tạo danh sách bot winners với tên động
        generateBotWinners: function() {
            try {
                // Sử dụng SicboBotNames module để tạo bot winners
                var numBots = Math.floor(Math.random() * 8) + 5; // 5-12 bot

                // Chọn theme ngẫu nhiên cho bot names
                var themes = ['anime', 'gaming', 'nature', 'royal', 'random'];
                var selectedTheme = themes[Math.floor(Math.random() * themes.length)];

                var botWinners;
                if (selectedTheme === 'random') {
                    botWinners = SicboBotNames.generateBotWinners(numBots);
                } else {
                    var botNames = SicboBotNames.generateThemedBotNames(selectedTheme, numBots);
                    botWinners = [];

                    for (var i = 0; i < botNames.length; i++) {
                        var randomAward = Math.floor(Math.random() * 50000000) + 10000000; // 10M - 60M
                        botWinners.push({
                            UserName: botNames[i],
                            Award: randomAward,
                            IsBot: true
                        });
                    }
                }

                return botWinners;
            } catch (error) {
                // Fallback to simple bot generation
                return this.generateFallbackBots();
            }
        },

        // Fallback bot generation nếu module SicboBotNames không hoạt động
        generateFallbackBots: function() {
            var botPrefixes = ["dragon", "king", "master", "legend", "hero"];
            var botSuffixes = ["pro", "vip", "winner", "champion", "elite"];
            var botWinners = [];
            var numBots = Math.floor(Math.random() * 5) + 3; // 3-7 bot

            for (var i = 0; i < numBots; i++) {
                var prefix = botPrefixes[Math.floor(Math.random() * botPrefixes.length)];
                var suffix = botSuffixes[Math.floor(Math.random() * botSuffixes.length)];
                var number = Math.floor(Math.random() * 999) + 1;
                var randomAward = Math.floor(Math.random() * 50000000) + 10000000; // 10M - 60M

                botWinners.push({
                    UserName: prefix + number + suffix,
                    Award: randomAward,
                    IsBot: true
                });
            }

            return botWinners;
        },

        // Lấy dữ liệu top đã lưu từ localStorage
        getSavedTopData: function() {
            try {
                var savedData = cc.sys.localStorage.getItem(this.TOP_DATA_KEY);
                if (savedData) {
                    return JSON.parse(savedData);
                }
            } catch (error) {
                console.log("Get saved top data error:", error);
            }
            return {
                realUsers: [],
                bots: [],
                lastUpdate: new Date().getTime()
            };
        },

        // Lưu dữ liệu top vào localStorage
        saveTopData: function(topList) {
            try {
                var realUsers = [];
                var bots = [];

                for (var i = 0; i < topList.length; i++) {
                    var user = topList[i];
                    if (user.IsBot) {
                        bots.push(user);
                    } else {
                        realUsers.push(user);
                    }
                }

                var topData = {
                    realUsers: realUsers,
                    bots: bots,
                    lastUpdate: new Date().getTime()
                };

                cc.sys.localStorage.setItem(this.TOP_DATA_KEY, JSON.stringify(topData));
            } catch (error) {
                console.log("Save top data error:", error);
            }
        },

        // Kiểm tra và thực hiện reset hàng ngày
        checkDailyReset: function() {
            try {
                var now = new Date();
                var lastResetStr = cc.sys.localStorage.getItem(this.LAST_RESET_KEY);
                var lastReset = lastResetStr ? new Date(parseInt(lastResetStr)) : null;

                // Kiểm tra xem đã qua 00h chưa
                var shouldReset = false;
                if (!lastReset) {
                    shouldReset = true;
                } else {
                    var todayMidnight = new Date(now.getFullYear(), now.getMonth(), now.getDate(), this.RESET_HOUR, 0, 0);
                    var lastResetMidnight = new Date(lastReset.getFullYear(), lastReset.getMonth(), lastReset.getDate(), this.RESET_HOUR, 0, 0);

                    if (now >= todayMidnight && lastReset < todayMidnight) {
                        shouldReset = true;
                    }
                }

                if (shouldReset) {
                    this.performDailyReset();
                }

                // Lên lịch kiểm tra reset tiếp theo
                this.scheduleNextResetCheck();
            } catch (error) {
                console.log("Check daily reset error:", error);
            }
        },

        // Thực hiện reset dữ liệu hàng ngày
        performDailyReset: function() {
            try {
                console.log("Performing daily reset for Sicbo top...");

                // Xóa dữ liệu top cũ
                cc.sys.localStorage.removeItem(this.TOP_DATA_KEY);

                // Cập nhật thời gian reset cuối cùng
                cc.sys.localStorage.setItem(this.LAST_RESET_KEY, new Date().getTime().toString());

                console.log("Daily reset completed!");
            } catch (error) {
                console.log("Perform daily reset error:", error);
            }
        },

        // Lên lịch kiểm tra reset tiếp theo
        scheduleNextResetCheck: function() {
            var self = this;

            // Kiểm tra mỗi 30 phút
            if (this.resetCheckInterval) {
                clearInterval(this.resetCheckInterval);
            }

            this.resetCheckInterval = setInterval(function() {
                self.checkDailyReset();
            }, 30 * 60 * 1000); // 30 phút
        },

        // Thêm user thắng lớn vào top
        addWinnerToTop: function(userName, award) {
            try {
                if (!userName || award <= 0) return;

                var topData = this.getSavedTopData();

                // Kiểm tra xem user đã có trong top chưa
                var existingUserIndex = -1;
                for (var i = 0; i < topData.realUsers.length; i++) {
                    if (topData.realUsers[i].UserName === userName) {
                        existingUserIndex = i;
                        break;
                    }
                }

                if (existingUserIndex >= 0) {
                    // Cập nhật award nếu lớn hơn
                    if (award > topData.realUsers[existingUserIndex].Award) {
                        topData.realUsers[existingUserIndex].Award = award;
                    }
                } else {
                    // Thêm user mới
                    topData.realUsers.push({
                        UserName: userName,
                        Award: award,
                        IsBot: false
                    });
                }

                // Sắp xếp lại theo Award
                topData.realUsers.sort(function(a, b) {
                    return (b.Award || 0) - (a.Award || 0);
                });

                // Giới hạn số lượng user thật trong top
                if (topData.realUsers.length > 20) {
                    topData.realUsers = topData.realUsers.slice(0, 20);
                }

                // Lưu lại
                cc.sys.localStorage.setItem(this.TOP_DATA_KEY, JSON.stringify(topData));

            } catch (error) {
                console.log("Add winner to top error:", error);
            }
        },

        closeClicked: function () {
            this.TaiXiuSicboTopListView.resetList();
            this.animation.play('closePopup');
            var self = this;
            var delay = 0.12;
            cc.director.getScheduler().schedule(function () {
                self.animation.stop();
                cc.TaiXiuSicboMainController.getInstance().destroyTopView();
            }, this, 1, 0, delay, false);
        },
    });
}).call(this);
