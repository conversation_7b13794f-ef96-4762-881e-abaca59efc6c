/**
 * Created by Nofear on 3/15/2019.
 */

var SicboBotNames = require("../../../data/SicboBotNames");

(function () {
    cc.TaiXiuSicboTopView = cc.Class({
        "extends": cc.PopupBase,
        properties: {
            TaiXiuSicboTopListView: cc.TaiXiuSicboTopListView,
        },

        onLoad: function () {
            this.animation = this.node.getComponent(cc.Animation);
            this.node.zIndex = cc.NoteDepth.POPUP_TAIXIU_SICBO;

            // Khởi tạo hệ thống quản lý top data
            this.initTopDataManager();
        },

        onEnable: function () {
            var self = this;

            // Khôi phục dữ liệu top ngay lập tức
            this.restoreTopData();

            var delay = 0.2;
            cc.director.getScheduler().schedule(function () {
                self.getTopSessionWinners();
            }, this, 1, 0, delay, false);

            this.animation.play('openPopup');

            // <PERSON><PERSON><PERSON> tra reset hàng ngày
            this.checkDailyReset();
        },

        // Khởi tạo hệ thống quản lý dữ liệu top
        initTopDataManager: function() {
            this.TOP_DATA_KEY = 'sicbo_top_data';
            this.LAST_RESET_KEY = 'sicbo_last_reset';
            this.RESET_HOUR = 0; // 00h tối
            this.MIN_WIN_AMOUNT = 500000; // 500k để vào top
        },

        getTopSessionWinners: function () {
            var txsicboGetBigWinnersCommand = new cc.TXSICBOGetBigWinnersCommand;
            txsicboGetBigWinnersCommand.execute(this);
        },

        onTXGetBigWinnersResponse: function (response) {
            var list = response;

            try {
                // Lấy dữ liệu top đã lưu từ localStorage
                var savedTopData = this.getSavedTopData();

                // Kết hợp dữ liệu từ server và dữ liệu đã lưu
                if (list !== null && Array.isArray(list)) {
                    // Thêm dữ liệu user thật đã lưu
                    if (savedTopData.realUsers && savedTopData.realUsers.length > 0) {
                        list = list.concat(savedTopData.realUsers);
                    }

                    // Thêm bot vào danh sách top winners
                    var botWinners = this.generateBotWinners();
                    if (botWinners && botWinners.length > 0) {
                        list = list.concat(botWinners);
                    }

                    // Sắp xếp lại theo Award giảm dần
                    list.sort(function(a, b) {
                        return (b.Award || 0) - (a.Award || 0);
                    });

                    // Giới hạn top 50
                    if (list.length > 50) {
                        list = list.slice(0, 50);
                    }

                    // Lưu dữ liệu mới vào localStorage
                    this.saveTopData(list);
                }
            } catch (error) {
                console.log("Top list error:", error);
            }

            if (list !== null && list.length > 0) {
                this.TaiXiuSicboTopListView.resetList();
                this.TaiXiuSicboTopListView.initialize(list);
            }
        },

        // Hàm tạo danh sách bot winners với tên động
        generateBotWinners: function() {
            try {
                // Sử dụng SicboBotNames module để tạo bot winners
                var numBots = Math.floor(Math.random() * 8) + 5; // 5-12 bot

                // Chọn theme ngẫu nhiên cho bot names
                var themes = ['anime', 'gaming', 'nature', 'royal', 'random'];
                var selectedTheme = themes[Math.floor(Math.random() * themes.length)];

                var botWinners;
                if (selectedTheme === 'random') {
                    botWinners = SicboBotNames.generateBotWinners(numBots);
                } else {
                    var botNames = SicboBotNames.generateThemedBotNames(selectedTheme, numBots);
                    botWinners = [];

                    for (var i = 0; i < botNames.length; i++) {
                        var randomAward = Math.floor(Math.random() * 50000000) + 10000000; // 10M - 60M
                        botWinners.push({
                            UserName: botNames[i],
                            Award: randomAward,
                            IsBot: true
                        });
                    }
                }

                return botWinners;
            } catch (error) {
                // Fallback to simple bot generation
                return this.generateFallbackBots();
            }
        },

        // Fallback bot generation nếu module SicboBotNames không hoạt động
        generateFallbackBots: function() {
            var botPrefixes = ["dragon", "king", "master", "legend", "hero"];
            var botSuffixes = ["pro", "vip", "winner", "champion", "elite"];
            var botWinners = [];
            var numBots = Math.floor(Math.random() * 5) + 3; // 3-7 bot

            for (var i = 0; i < numBots; i++) {
                var prefix = botPrefixes[Math.floor(Math.random() * botPrefixes.length)];
                var suffix = botSuffixes[Math.floor(Math.random() * botSuffixes.length)];
                var number = Math.floor(Math.random() * 999) + 1;
                var randomAward = Math.floor(Math.random() * 50000000) + 10000000; // 10M - 60M

                botWinners.push({
                    UserName: prefix + number + suffix,
                    Award: randomAward,
                    IsBot: true
                });
            }

            return botWinners;
        },

        // Lấy dữ liệu top đã lưu từ localStorage
        getSavedTopData: function() {
            try {
                var savedData = cc.sys.localStorage.getItem(this.TOP_DATA_KEY);
                if (savedData) {
                    return JSON.parse(savedData);
                }
            } catch (error) {
                console.log("Get saved top data error:", error);
            }
            return {
                realUsers: [],
                bots: [],
                lastUpdate: new Date().getTime()
            };
        },

        // Lưu dữ liệu top vào localStorage
        saveTopData: function(topList) {
            try {
                var realUsers = [];
                var bots = [];

                for (var i = 0; i < topList.length; i++) {
                    var user = topList[i];
                    if (user.IsBot) {
                        bots.push(user);
                    } else {
                        realUsers.push(user);
                    }
                }

                var topData = {
                    realUsers: realUsers,
                    bots: bots,
                    lastUpdate: new Date().getTime()
                };

                cc.sys.localStorage.setItem(this.TOP_DATA_KEY, JSON.stringify(topData));
            } catch (error) {
                console.log("Save top data error:", error);
            }
        },

        // Kiểm tra reset hàng ngày
        checkDailyReset: function() {
            try {
                var now = new Date();
                var lastReset = cc.sys.localStorage.getItem(this.LAST_RESET_KEY);

                if (lastReset) {
                    var lastResetDate = new Date(parseInt(lastReset));

                    // Tính thời điểm reset tiếp theo (00:00 ngày hôm sau)
                    var nextResetTime = new Date(lastResetDate);
                    nextResetTime.setDate(nextResetTime.getDate() + 1);
                    nextResetTime.setHours(this.RESET_HOUR, 0, 0, 0);

                    // Kiểm tra xem đã qua thời điểm reset chưa
                    if (now.getTime() >= nextResetTime.getTime()) {
                        this.performDailyReset();
                        console.log("Sicbo Top reset at:", now.toLocaleString());
                    }
                } else {
                    // Lần đầu tiên, set thời gian reset hiện tại
                    cc.sys.localStorage.setItem(this.LAST_RESET_KEY, now.getTime().toString());
                    console.log("Sicbo Top reset time initialized:", now.toLocaleString());
                }

                // Lên lịch kiểm tra reset tiếp theo
                this.scheduleNextResetCheck();
            } catch (error) {
                console.log("Check Sicbo daily reset error:", error);
            }
        },

        // Thực hiện reset dữ liệu hàng ngày
        performDailyReset: function() {
            try {
                var now = new Date();
                var emptyData = {
                    realUsers: [],
                    bots: [],
                    lastUpdate: now.getTime(),
                    resetTime: now.getTime(),
                    resetDate: now.toLocaleDateString()
                };

                // Xóa dữ liệu cũ và tạo mới
                cc.sys.localStorage.setItem(this.TOP_DATA_KEY, JSON.stringify(emptyData));
                cc.sys.localStorage.setItem(this.TOP_DATA_KEY + '_backup', JSON.stringify(emptyData));
                cc.sys.localStorage.setItem(this.LAST_RESET_KEY, now.getTime().toString());

                // Refresh UI nếu đang hiển thị
                this.refreshTopDisplay();

                console.log("🔄 Sicbo Top data reset successfully at:", now.toLocaleString());
                console.log("📅 Next reset will be at: 00:00 tomorrow");
            } catch (error) {
                console.log("❌ Perform Sicbo daily reset error:", error);
            }
        },

        // Lên lịch kiểm tra reset tiếp theo
        scheduleNextResetCheck: function() {
            var self = this;

            // Kiểm tra mỗi 30 phút
            if (this.resetCheckInterval) {
                clearInterval(this.resetCheckInterval);
            }

            this.resetCheckInterval = setInterval(function() {
                self.checkDailyReset();
            }, 30 * 60 * 1000); // 30 phút
        },

        // Khôi phục dữ liệu top khi mở lại
        restoreTopData: function() {
            try {
                var savedTopData = this.getSavedTopData();

                if (savedTopData.realUsers && savedTopData.realUsers.length > 0) {
                    // Tạo danh sách kết hợp từ dữ liệu đã lưu
                    var combinedList = [];

                    // Thêm user thật
                    combinedList = combinedList.concat(savedTopData.realUsers);

                    // Thêm bot
                    var botWinners = this.generateBotWinners();
                    if (botWinners && botWinners.length > 0) {
                        combinedList = combinedList.concat(botWinners);
                    }

                    // Sắp xếp theo Award
                    combinedList.sort(function(a, b) {
                        return (b.Award || 0) - (a.Award || 0);
                    });

                    // Giới hạn top 50
                    if (combinedList.length > 50) {
                        combinedList = combinedList.slice(0, 50);
                    }

                    // Hiển thị ngay lập tức
                    if (combinedList.length > 0) {
                        this.TaiXiuSicboTopListView.resetList();
                        this.TaiXiuSicboTopListView.initialize(combinedList);
                        console.log("Restored Sicbo top with", savedTopData.realUsers.length, "real users");
                    }
                }
            } catch (error) {
                console.log("Restore Sicbo top data error:", error);
            }
        },

        // Thêm user thắng lớn vào top
        addWinnerToTop: function(userName, award) {
            try {
                if (!userName || award <= 0) return;

                var topData = this.getSavedTopData();

                // Kiểm tra xem user đã có trong top chưa
                var existingUserIndex = -1;
                for (var i = 0; i < topData.realUsers.length; i++) {
                    if (topData.realUsers[i].UserName === userName) {
                        existingUserIndex = i;
                        break;
                    }
                }

                if (existingUserIndex >= 0) {
                    // Cập nhật award nếu lớn hơn
                    if (award > topData.realUsers[existingUserIndex].Award) {
                        topData.realUsers[existingUserIndex].Award = award;
                    }
                } else {
                    // Thêm user mới
                    topData.realUsers.push({
                        UserName: userName,
                        Award: award,
                        IsBot: false
                    });
                }

                // Sắp xếp lại theo Award
                topData.realUsers.sort(function(a, b) {
                    return (b.Award || 0) - (a.Award || 0);
                });

                // Giới hạn số lượng user thật trong top
                if (topData.realUsers.length > 20) {
                    topData.realUsers = topData.realUsers.slice(0, 20);
                }

                // Lưu lại với backup
                cc.sys.localStorage.setItem(this.TOP_DATA_KEY, JSON.stringify(topData));
                cc.sys.localStorage.setItem(this.TOP_DATA_KEY + '_backup', JSON.stringify(topData));

                // Cập nhật UI nếu đang hiển thị
                this.refreshTopDisplay();

                console.log("Updated Sicbo top with winner:", userName, "amount:", award);
            } catch (error) {
                console.log("Add winner to top error:", error);
            }
        },

        // Cập nhật user thắng vào top (hệ thống mới)
        updateUserWin: function(userName, winAmount) {
            try {
                if (!userName || winAmount <= 0) return;

                var topData = this.getSavedTopData();
                var existingUserIndex = -1;

                // Tìm user trong danh sách
                for (var i = 0; i < topData.realUsers.length; i++) {
                    if (topData.realUsers[i].UserName === userName) {
                        existingUserIndex = i;
                        break;
                    }
                }

                if (existingUserIndex >= 0) {
                    // Cộng thêm tiền thắng vào tổng
                    topData.realUsers[existingUserIndex].Award += winAmount;
                } else {
                    // Thêm user mới nếu đủ điều kiện
                    if (winAmount >= this.MIN_WIN_AMOUNT) {
                        topData.realUsers.push({
                            UserName: userName,
                            Award: winAmount,
                            IsBot: false
                        });
                    }
                }

                // Sắp xếp lại theo Award
                topData.realUsers.sort(function(a, b) {
                    return (b.Award || 0) - (a.Award || 0);
                });

                // Giới hạn số lượng user thật trong top
                if (topData.realUsers.length > 20) {
                    topData.realUsers = topData.realUsers.slice(0, 20);
                }

                // Lưu dữ liệu với backup
                cc.sys.localStorage.setItem(this.TOP_DATA_KEY, JSON.stringify(topData));
                cc.sys.localStorage.setItem(this.TOP_DATA_KEY + '_backup', JSON.stringify(topData));

                // Cập nhật UI nếu đang hiển thị
                this.refreshTopDisplay();

                console.log("Updated Sicbo user win:", userName, "amount:", winAmount);
            } catch (error) {
                console.log("Update Sicbo user win error:", error);
            }
        },

        // Cập nhật user thua
        updateUserLoss: function(userName, lossAmount) {
            try {
                if (!userName || lossAmount <= 0) return;

                var topData = this.getSavedTopData();
                var existingUserIndex = -1;

                // Tìm user trong danh sách
                for (var i = 0; i < topData.realUsers.length; i++) {
                    if (topData.realUsers[i].UserName === userName) {
                        existingUserIndex = i;
                        break;
                    }
                }

                if (existingUserIndex >= 0) {
                    // Trừ tiền thua
                    topData.realUsers[existingUserIndex].Award -= lossAmount;

                    // Xóa user khỏi top nếu dưới ngưỡng tối thiểu
                    if (topData.realUsers[existingUserIndex].Award < this.MIN_WIN_AMOUNT) {
                        topData.realUsers.splice(existingUserIndex, 1);
                        console.log("Removed Sicbo user from top:", userName);
                    }

                    // Sắp xếp lại theo Award
                    topData.realUsers.sort(function(a, b) {
                        return (b.Award || 0) - (a.Award || 0);
                    });

                    // Lưu dữ liệu với backup
                    cc.sys.localStorage.setItem(this.TOP_DATA_KEY, JSON.stringify(topData));
                    cc.sys.localStorage.setItem(this.TOP_DATA_KEY + '_backup', JSON.stringify(topData));

                    // Cập nhật UI nếu đang hiển thị
                    this.refreshTopDisplay();

                    console.log("Updated Sicbo user loss:", userName, "amount:", lossAmount);
                }
            } catch (error) {
                console.log("Update Sicbo user loss error:", error);
            }
        },

        closeClicked: function () {
            this.TaiXiuSicboTopListView.resetList();
            this.animation.play('closePopup');
            var self = this;
            var delay = 0.12;
            cc.director.getScheduler().schedule(function () {
                self.animation.stop();
                cc.TaiXiuSicboMainController.getInstance().destroyTopView();
            }, this, 1, 0, delay, false);
        },
    });
}).call(this);
