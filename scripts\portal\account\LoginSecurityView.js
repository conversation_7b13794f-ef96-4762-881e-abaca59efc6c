/**
 * Created by Nofear on 3/15/2019.
 */


(function () {
    cc.LoginSecurityView = cc.Class({
        "extends": cc.Component,
        properties: {
            nodeGroupNoPhoneNumber: cc.Node,

            nodeGroupHavePhoneNumber: cc.Node,
            lbState: cc.Label, //Chua kich hoat / Da kich hoat
        },

        // use this for initialization
        onLoad: function () {

        },

        activateLoginSecurityClicked: function () {

        },

        addPhoneNumberClicked: function () {

        },
    });
}).call(this);
