module.exports =  [
    {
        "SpinId": 5936183,
        "UserName": "soraaoi",
        "RoomID": 3,
        "PrizeValue": 1740000,
        "IsJackpot": false,
        "CreatedDate": "2019-03-23T21:50:45.7"
    },
    {
        "SpinId": 5936166,
        "UserName": "soraaoi",
        "RoomID": 3,
        "PrizeValue": 600000,
        "IsJackpot": false,
        "CreatedDate": "2019-03-23T21:49:37.34"
    },
    {
        "SpinId": 5936146,
        "UserName": "soraaoi",
        "RoomID": 1,
        "PrizeValue": 11000,
        "IsJackpot": false,
        "CreatedDate": "2019-03-23T21:48:00.467"
    },
    {
        "SpinId": 5936145,
        "UserName": "soraaoi",
        "RoomID": 1,
        "PrizeValue": 10100,
        "IsJackpot": false,
        "CreatedDate": "2019-03-23T21:47:30.59"
    },
    {
        "SpinId": 5936127,
        "UserName": "soraaoi",
        "RoomID": 1,
        "PrizeValue": 10400,
        "IsJackpot": false,
        "CreatedDate": "2019-03-23T21:46:11.28"
    },
    {
        "SpinId": 5936089,
        "UserName": "soraaoi",
        "RoomID": 1,
        "PrizeValue": 18400,
        "IsJackpot": false,
        "CreatedDate": "2019-03-23T21:43:35.98"
    },
    {
        "SpinId": 5936041,
        "UserName": "soraaoi",
        "RoomID": 1,
        "PrizeValue": 12000,
        "IsJackpot": false,
        "CreatedDate": "2019-03-23T21:40:20.873"
    },
    {
        "SpinId": 5936040,
        "UserName": "soraaoi",
        "RoomID": 1,
        "PrizeValue": 11000,
        "IsJackpot": false,
        "CreatedDate": "2019-03-23T21:40:13.247"
    },
    {
        "SpinId": 5936022,
        "UserName": "soraaoi",
        "RoomID": 1,
        "PrizeValue": 8000,
        "IsJackpot": false,
        "CreatedDate": "2019-03-23T21:38:56.933"
    },
    {
        "SpinId": 5935982,
        "UserName": "soraaoi",
        "RoomID": 1,
        "PrizeValue": 10800,
        "IsJackpot": false,
        "CreatedDate": "2019-03-23T21:36:17.903"
    },
    {
        "SpinId": 5935962,
        "UserName": "TheWinner",
        "RoomID": 2,
        "PrizeValue": 190000,
        "IsJackpot": false,
        "CreatedDate": "2019-03-23T17:17:10.5"
    },
    {
        "SpinId": 5935945,
        "UserName": "TheWinner",
        "RoomID": 2,
        "PrizeValue": 130000,
        "IsJackpot": false,
        "CreatedDate": "2019-03-23T17:15:46.077"
    },
    {
        "SpinId": 5935931,
        "UserName": "TheWinner",
        "RoomID": 2,
        "PrizeValue": 185000,
        "IsJackpot": false,
        "CreatedDate": "2019-03-23T17:13:16.25"
    },
    {
        "SpinId": 5935895,
        "UserName": "soraaoi",
        "RoomID": 1,
        "PrizeValue": 114000,
        "IsJackpot": false,
        "CreatedDate": "2019-03-23T16:29:12.86"
    },
    {
        "SpinId": 5935787,
        "UserName": "soraaoi",
        "RoomID": 3,
        "PrizeValue": 700000,
        "IsJackpot": false,
        "CreatedDate": "2019-03-23T16:04:01.657"
    },
    {
        "SpinId": 5935785,
        "UserName": "soraaoi",
        "RoomID": 3,
        "PrizeValue": 4800000,
        "IsJackpot": false,
        "CreatedDate": "2019-03-23T16:03:41.313"
    },
    {
        "SpinId": 5935783,
        "UserName": "soraaoi",
        "RoomID": 3,
        "PrizeValue": 540000,
        "IsJackpot": false,
        "CreatedDate": "2019-03-23T16:03:21.08"
    },
    {
        "SpinId": 5935768,
        "UserName": "soraaoi",
        "RoomID": 1,
        "PrizeValue": 10000,
        "IsJackpot": false,
        "CreatedDate": "2019-03-23T16:00:21.407"
    },
    {
        "SpinId": 5935765,
        "UserName": "Hehekakaa",
        "RoomID": 1,
        "PrizeValue": 16200,
        "IsJackpot": false,
        "CreatedDate": "2019-03-22T23:09:50.097"
    },
    {
        "SpinId": 5935754,
        "UserName": "Hehekakaa",
        "RoomID": 1,
        "PrizeValue": 10000,
        "IsJackpot": false,
        "CreatedDate": "2019-03-22T21:36:41.477"
    },
    {
        "SpinId": 5935743,
        "UserName": "Hehekakaa",
        "RoomID": 1,
        "PrizeValue": 14000,
        "IsJackpot": false,
        "CreatedDate": "2019-03-22T21:30:55.943"
    },
    {
        "SpinId": 5935741,
        "UserName": "Hehekakaa",
        "RoomID": 1,
        "PrizeValue": 8000,
        "IsJackpot": false,
        "CreatedDate": "2019-03-22T21:30:44.553"
    },
    {
        "SpinId": 5935739,
        "UserName": "Hehekakaa",
        "RoomID": 1,
        "PrizeValue": 18000,
        "IsJackpot": false,
        "CreatedDate": "2019-03-22T21:30:30.71"
    },
    {
        "SpinId": 66553623,
        "UserName": "soraaoi",
        "RoomID": 1,
        "PrizeValue": 16500,
        "IsJackpot": false,
        "CreatedDate": "2019-03-20T18:30:16.463"
    },
    {
        "SpinId": 66553621,
        "UserName": "soraaoi",
        "RoomID": 1,
        "PrizeValue": 15750,
        "IsJackpot": false,
        "CreatedDate": "2019-03-20T18:29:38.213"
    },
    {
        "SpinId": 5935714,
        "UserName": "Hehekakaa",
        "RoomID": 1,
        "PrizeValue": 11500,
        "IsJackpot": false,
        "CreatedDate": "2019-03-20T16:43:20.073"
    },
    {
        "SpinId": 5935666,
        "UserName": "Hehekakaa",
        "RoomID": 1,
        "PrizeValue": 9200,
        "IsJackpot": false,
        "CreatedDate": "2019-03-19T22:36:50.57"
    },
    {
        "SpinId": 5935640,
        "UserName": "Hehekakaa",
        "RoomID": 1,
        "PrizeValue": 8700,
        "IsJackpot": false,
        "CreatedDate": "2019-03-14T23:17:17.263"
    },
    {
        "SpinId": 69931564,
        "UserName": "Bethomua",
        "RoomID": 1,
        "PrizeValue": 14200,
        "IsJackpot": false,
        "CreatedDate": "2019-02-27T09:12:34.5"
    },
    {
        "SpinId": 69931553,
        "UserName": "phamnhatvy6886",
        "RoomID": 1,
        "PrizeValue": 16800,
        "IsJackpot": false,
        "CreatedDate": "2019-02-27T09:12:33.297"
    },
    {
        "SpinId": 69931501,
        "UserName": "Bethomua",
        "RoomID": 1,
        "PrizeValue": 9200,
        "IsJackpot": false,
        "CreatedDate": "2019-02-27T09:12:26.727"
    },
    {
        "SpinId": 69931475,
        "UserName": "Kukumalu",
        "RoomID": 1,
        "PrizeValue": 23300,
        "IsJackpot": false,
        "CreatedDate": "2019-02-27T09:12:22.19"
    },
    {
        "SpinId": 69931451,
        "UserName": "Van911993",
        "RoomID": 1,
        "PrizeValue": 14000,
        "IsJackpot": false,
        "CreatedDate": "2019-02-27T09:12:18.483"
    },
    {
        "SpinId": 69931432,
        "UserName": "bocau280610",
        "RoomID": 1,
        "PrizeValue": 10500,
        "IsJackpot": false,
        "CreatedDate": "2019-02-27T09:12:15.84"
    },
    {
        "SpinId": 69931417,
        "UserName": "baobaorrt",
        "RoomID": 1,
        "PrizeValue": 83000,
        "IsJackpot": false,
        "CreatedDate": "2019-02-27T09:12:14.103"
    },
    {
        "SpinId": 69931418,
        "UserName": "trunghieu1991",
        "RoomID": 1,
        "PrizeValue": 15000,
        "IsJackpot": false,
        "CreatedDate": "2019-02-27T09:12:14.103"
    },
    {
        "SpinId": 69931385,
        "UserName": "Kemkem2015",
        "RoomID": 1,
        "PrizeValue": 21500,
        "IsJackpot": false,
        "CreatedDate": "2019-02-27T09:12:09.143"
    },
    {
        "SpinId": 69931355,
        "UserName": "Shi13alo",
        "RoomID": 1,
        "PrizeValue": 11400,
        "IsJackpot": false,
        "CreatedDate": "2019-02-27T09:12:04.28"
    },
    {
        "SpinId": 69931351,
        "UserName": "Doconchomayduoc",
        "RoomID": 1,
        "PrizeValue": 10600,
        "IsJackpot": false,
        "CreatedDate": "2019-02-27T09:12:03.263"
    },
    {
        "SpinId": 69931335,
        "UserName": "hien7578",
        "RoomID": 1,
        "PrizeValue": 18000,
        "IsJackpot": false,
        "CreatedDate": "2019-02-27T09:12:00.773"
    },
    {
        "SpinId": 69931328,
        "UserName": "dungnat94",
        "RoomID": 1,
        "PrizeValue": 8000,
        "IsJackpot": false,
        "CreatedDate": "2019-02-27T09:11:59.667"
    },
    {
        "SpinId": 69931323,
        "UserName": "CuWay88",
        "RoomID": 1,
        "PrizeValue": 12000,
        "IsJackpot": false,
        "CreatedDate": "2019-02-27T09:11:59.18"
    },
    {
        "SpinId": 69931291,
        "UserName": "baobaorrt",
        "RoomID": 1,
        "PrizeValue": 12000,
        "IsJackpot": false,
        "CreatedDate": "2019-02-27T09:11:53.813"
    },
    {
        "SpinId": 69931279,
        "UserName": "trangnguyen86",
        "RoomID": 2,
        "PrizeValue": 295000,
        "IsJackpot": false,
        "CreatedDate": "2019-02-27T09:11:52.233"
    },
    {
        "SpinId": 69931276,
        "UserName": "phamnhatvy6886",
        "RoomID": 1,
        "PrizeValue": 8600,
        "IsJackpot": false,
        "CreatedDate": "2019-02-27T09:11:51.717"
    },
    {
        "SpinId": 69931273,
        "UserName": "Kukumalu",
        "RoomID": 1,
        "PrizeValue": 65500,
        "IsJackpot": false,
        "CreatedDate": "2019-02-27T09:11:51.123"
    },
    {
        "SpinId": 69931265,
        "UserName": "keksmizinfoo",
        "RoomID": 1,
        "PrizeValue": 10800,
        "IsJackpot": false,
        "CreatedDate": "2019-02-27T09:11:49.997"
    },
    {
        "SpinId": 69931188,
        "UserName": "denlano",
        "RoomID": 1,
        "PrizeValue": 161500,
        "IsJackpot": false,
        "CreatedDate": "2019-02-27T09:11:38.61"
    },
    {
        "SpinId": 69931182,
        "UserName": "Hungtroc686",
        "RoomID": 1,
        "PrizeValue": 25500,
        "IsJackpot": false,
        "CreatedDate": "2019-02-27T09:11:37.357"
    },
    {
        "SpinId": 69931141,
        "UserName": "baobaorrt",
        "RoomID": 1,
        "PrizeValue": 30000,
        "IsJackpot": false,
        "CreatedDate": "2019-02-27T09:11:31.303"
    }
];
