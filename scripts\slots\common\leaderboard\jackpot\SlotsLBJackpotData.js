module.exports =  [
    {
        "SpinId": 5935908,
        "UserName": "soraaoi",
        "RoomID": 1,
        "PrizeValue": 648320,
        "IsJackpot": true,
        "CreatedDate": "2019-03-23T17:03:33.907"
    },
    {
        "SpinId": 66553620,
        "UserName": "catus6789",
        "RoomID": 2,
        "PrizeValue": 5389740,
        "IsJackpot": true,
        "CreatedDate": "2019-03-06T20:47:06.39"
    },
    {
        "SpinId": 69921656,
        "UserName": "HuyTrung56789",
        "RoomID": 1,
        "PrizeValue": 731726,
        "IsJackpot": true,
        "CreatedDate": "2019-02-27T08:46:02.25"
    },
    {
        "SpinId": 69919124,
        "UserName": "canhsat113",
        "RoomID": 2,
        "PrizeValue": 5652970,
        "IsJackpot": true,
        "CreatedDate": "2019-02-27T08:36:30.94"
    },
    {
        "SpinId": 69910186,
        "UserName": "keksmizinfoo",
        "RoomID": 1,
        "PrizeValue": 552289,
        "IsJackpot": true,
        "CreatedDate": "2019-02-27T07:57:35.923"
    },
    {
        "SpinId": 69907642,
        "UserName": "denlano",
        "RoomID": 1,
        "PrizeValue": 646033,
        "IsJackpot": true,
        "CreatedDate": "2019-02-27T07:45:55.503"
    },
    {
        "SpinId": 69900170,
        "UserName": "Mrminh1996",
        "RoomID": 1,
        "PrizeValue": 2526068,
        "IsJackpot": true,
        "CreatedDate": "2019-02-27T07:16:32.01"
    },
    {
        "SpinId": 69875218,
        "UserName": "chifangtai",
        "RoomID": 1,
        "PrizeValue": 856395,
        "IsJackpot": true,
        "CreatedDate": "2019-02-27T06:22:31.473"
    },
    {
        "SpinId": 69874667,
        "UserName": "Khoa11",
        "RoomID": 2,
        "PrizeValue": 8375580,
        "IsJackpot": true,
        "CreatedDate": "2019-02-27T06:21:04.613"
    },
    {
        "SpinId": 69856295,
        "UserName": "HuyTrung56789",
        "RoomID": 1,
        "PrizeValue": 651843,
        "IsJackpot": true,
        "CreatedDate": "2019-02-27T04:51:04.863"
    },
    {
        "SpinId": 69848253,
        "UserName": "BenAril",
        "RoomID": 1,
        "PrizeValue": 751083,
        "IsJackpot": true,
        "CreatedDate": "2019-02-27T04:24:21.56"
    },
    {
        "SpinId": 69835096,
        "UserName": "vantu29",
        "RoomID": 1,
        "PrizeValue": 2290340,
        "IsJackpot": true,
        "CreatedDate": "2019-02-27T03:43:57.96"
    },
    {
        "SpinId": 69821304,
        "UserName": "1GGMaiThuy",
        "RoomID": 1,
        "PrizeValue": 658167,
        "IsJackpot": true,
        "CreatedDate": "2019-02-27T03:17:19.35"
    },
    {
        "SpinId": 69813204,
        "UserName": "hoaxethai",
        "RoomID": 1,
        "PrizeValue": 779524,
        "IsJackpot": true,
        "CreatedDate": "2019-02-27T02:52:11.873"
    },
    {
        "SpinId": 69799580,
        "UserName": "kinchepquy",
        "RoomID": 1,
        "PrizeValue": 862471,
        "IsJackpot": true,
        "CreatedDate": "2019-02-27T02:17:31.92"
    },
    {
        "SpinId": 69783319,
        "UserName": "mrbin79",
        "RoomID": 1,
        "PrizeValue": 2281699,
        "IsJackpot": true,
        "CreatedDate": "2019-02-27T01:47:22.78"
    },
    {
        "SpinId": 69769975,
        "UserName": "duonggian",
        "RoomID": 1,
        "PrizeValue": 885310,
        "IsJackpot": true,
        "CreatedDate": "2019-02-27T01:27:57.36"
    },
    {
        "SpinId": 69751076,
        "UserName": "xin1landuocno",
        "RoomID": 1,
        "PrizeValue": 668313,
        "IsJackpot": true,
        "CreatedDate": "2019-02-27T00:57:22.477"
    },
    {
        "SpinId": 69743271,
        "UserName": "leechan",
        "RoomID": 1,
        "PrizeValue": 573452,
        "IsJackpot": true,
        "CreatedDate": "2019-02-27T00:43:19.02"
    },
    {
        "SpinId": 69739726,
        "UserName": "thai0512",
        "RoomID": 1,
        "PrizeValue": 2462323,
        "IsJackpot": true,
        "CreatedDate": "2019-02-27T00:38:34.253"
    },
    {
        "SpinId": 69718263,
        "UserName": "Kiepdaphu",
        "RoomID": 1,
        "PrizeValue": 535606,
        "IsJackpot": true,
        "CreatedDate": "2019-02-27T00:09:53.657"
    },
    {
        "SpinId": 69716446,
        "UserName": "Ninhkkt",
        "RoomID": 1,
        "PrizeValue": 521821,
        "IsJackpot": true,
        "CreatedDate": "2019-02-27T00:05:40.283"
    },
    {
        "SpinId": 69715406,
        "UserName": "Huytrung5678",
        "RoomID": 1,
        "PrizeValue": 760902,
        "IsJackpot": true,
        "CreatedDate": "2019-02-27T00:03:37.653"
    },
    {
        "SpinId": 69707540,
        "UserName": "Hamqua123",
        "RoomID": 2,
        "PrizeValue": 5302560,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T23:50:33.003"
    },
    {
        "SpinId": 69702902,
        "UserName": "dieunhiick",
        "RoomID": 1,
        "PrizeValue": 816807,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T23:40:55.917"
    },
    {
        "SpinId": 69687679,
        "UserName": "songvedem789",
        "RoomID": 1,
        "PrizeValue": 943404,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T23:11:13.877"
    },
    {
        "SpinId": 69679607,
        "UserName": "gshoa7007",
        "RoomID": 2,
        "PrizeValue": 8243520,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T22:56:42.047"
    },
    {
        "SpinId": 69665667,
        "UserName": "ahuhuconcu",
        "RoomID": 1,
        "PrizeValue": 832945,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T22:30:25.573"
    },
    {
        "SpinId": 69648567,
        "UserName": "ngocanhmmx",
        "RoomID": 1,
        "PrizeValue": 653957,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T21:53:09.537"
    },
    {
        "SpinId": 69641043,
        "UserName": "pehotluu1",
        "RoomID": 1,
        "PrizeValue": 547083,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T21:38:55.58"
    },
    {
        "SpinId": 69638787,
        "UserName": "hoiken",
        "RoomID": 1,
        "PrizeValue": 531695,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T21:34:08.433"
    },
    {
        "SpinId": 69637067,
        "UserName": "Taokhongthich",
        "RoomID": 1,
        "PrizeValue": 808153,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T21:31:19.553"
    },
    {
        "SpinId": 69620128,
        "UserName": "linhak2019",
        "RoomID": 1,
        "PrizeValue": 898720,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T20:57:10.637"
    },
    {
        "SpinId": 69600202,
        "UserName": "trungbeo9339",
        "RoomID": 1,
        "PrizeValue": 1101394,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T20:19:39.07"
    },
    {
        "SpinId": 69571186,
        "UserName": "NUPLUM444",
        "RoomID": 1,
        "PrizeValue": 532665,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T19:20:18.537"
    },
    {
        "SpinId": 69569719,
        "UserName": "sieunohu91",
        "RoomID": 1,
        "PrizeValue": 620136,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T19:16:37.68"
    },
    {
        "SpinId": 69563858,
        "UserName": "heroskiss91",
        "RoomID": 1,
        "PrizeValue": 710913,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T18:59:42.337"
    },
    {
        "SpinId": 69553235,
        "UserName": "pehotluu1",
        "RoomID": 1,
        "PrizeValue": 500417,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T18:28:39.223"
    },
    {
        "SpinId": 69553216,
        "UserName": "beko1111",
        "RoomID": 1,
        "PrizeValue": 587188,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T18:28:36.25"
    },
    {
        "SpinId": 69548753,
        "UserName": "trungbeo9339",
        "RoomID": 1,
        "PrizeValue": 543441,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T18:15:37.523"
    },
    {
        "SpinId": 69546741,
        "UserName": "Bomhune",
        "RoomID": 1,
        "PrizeValue": 711467,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T18:11:45.06"
    },
    {
        "SpinId": 69537796,
        "UserName": "Cobaccuuho",
        "RoomID": 2,
        "PrizeValue": 7440040,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T17:49:14.533"
    },
    {
        "SpinId": 69536411,
        "UserName": "Zcuchanhz",
        "RoomID": 1,
        "PrizeValue": 655472,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T17:45:39.573"
    },
    {
        "SpinId": 69528450,
        "UserName": "keksmizinfoo",
        "RoomID": 1,
        "PrizeValue": 641455,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T17:21:43.267"
    },
    {
        "SpinId": 69520725,
        "UserName": "Emlama9",
        "RoomID": 1,
        "PrizeValue": 521280,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T16:55:00.09"
    },
    {
        "SpinId": 69519529,
        "UserName": "nam87788",
        "RoomID": 1,
        "PrizeValue": 811966,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T16:50:11.51"
    },
    {
        "SpinId": 69502446,
        "UserName": "Nocairam999",
        "RoomID": 1,
        "PrizeValue": 721195,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T16:04:58.113"
    },
    {
        "SpinId": 69489883,
        "UserName": "dinhphu2001",
        "RoomID": 1,
        "PrizeValue": 529584,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T15:38:28.117"
    },
    {
        "SpinId": 69488244,
        "UserName": "sadsadassa",
        "RoomID": 1,
        "PrizeValue": 792205,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T15:34:50.303"
    },
    {
        "SpinId": 69472105,
        "UserName": "hoiken",
        "RoomID": 1,
        "PrizeValue": 516448,
        "IsJackpot": true,
        "CreatedDate": "2019-02-26T14:50:29.173"
    }
];
