/*
 * Generated by BeChicken
 * on 10/30/2019
 * version v1.0
 */
(function () {
    cc.CBAccumulateView = cc.Class({
        extends: cc.Component,
        properties: {
            prgAccumulate: cc.ProgressBar,
            lbTime: cc.Label,
            lbTotalPrize: cc.LabelIncrement,
            nodeTichLuy: cc.Node,
            nodeFx: cc.Node,
            lbKernel: cc.Label,
            listPiceNV: [cc.Node],
            sfPiceNV: [cc.SpriteFrame],
            nodePice: cc.Node,
            nodeNVWin: cc.Node,
            particleWin: cc.ParticleSystem,
            particleCoin: cc.ParticleSystem
        },
        onLoad: function () {
            cc.CBAccumulateController.getInstance().setAccumulateView(this);
            this.maxAntiquity = 9;
            this.nodeTichLuy.active = false;
            this.rootPosTichLuy = cc.v2(515, -226);
            this.coinPosition = cc.v2(-428, 4);
            this.nodeFx.active = false;
            this.animNodeFx = this.nodeFx.getComponent(cc.Animation);
            //Vi tri ban dau cua FX NV
            this.rootXFX = -6;
            this.rootYFX = -7;
            this.lbTotalPrize.setValue(0);
            this.scheduleTime = null;

            //Vi tri goc cua manh ghep nv
            this.rootPositionPiceNV = this.nodePice.position;
            //Lay vi tri cua tung manh ghep
            this.lstPositionPiceNV = [];
            this.activeNodeNVWin(false);

        },
        onEnable: function () {
            this.clearTimer();
            this.currentAntiquity = 0;
            this.startUpdateTime = false;
            this.nodePice.active = false;
            this.listPiceNV.map(node => {
                this.lstPositionPiceNV.push(node.position);
                node.active = false;
            }, this);
        },
        activeNodeNVWin: function (isActive) {
            this.nodeNVWin.active = isActive;
        },
        clearTimer: function () {
            try {
                if (this.scheduleTime)
                    clearInterval(this.scheduleTime);
            } catch (e) {

            }
        },
        onUpdateAccumulate: function (accumulateGame) {
            this.lbKernel.string = "x" + accumulateGame.Kernel;
            //Kiem tra co tien thuong -> chay anim
            let prizeValue = parseInt(accumulateGame.PrizeValue);
            //active di chuyen manh ghep
            let antiquity = parseInt(accumulateGame.Antiquity);
            if (prizeValue > 0) {
                antiquity = this.maxAntiquity;
            }
            if (this.startUpdateTime) {
                this.activeMovePice(antiquity);
            }
            //Cap nhat dem thoi gian tich luy
            if (!this.startUpdateTime && accumulateGame.EndTime != 0) {
                this.currentAntiquity = antiquity;
                this.startUpdateTime = true;
                this.updateTime(accumulateGame.EndTime);
                if (antiquity > 0) {
                    this.listPiceNV.map((node, index) => {
                        if (index + 1 <= antiquity) {
                            node.active = true;
                        }
                    }, this);
                }
            }
            if (prizeValue > 0 && accumulateGame.SpinID > 0) {
                this.startUpdateTime = false;
                try {
                    clearInterval(this.scheduleTime);
                } catch (e) {
                    console.log(e)
                }

                this.lbTotalPrize.tweenValueto(prizeValue);
                setTimeout(function () {
                    this.moveTichLuy();
                    setTimeout(function () {
                        //Chay hieu ung tien thuong
                        cc.EffectController.getInstance().playEffect(cc.EffectType.SUPER_WIN, prizeValue, 1);
                        cc.AudioController.getInstance().playSound(cc.AudioTypes.BIG_WIN);
                    }.bind(this), 500);
                }.bind(this), 2500)
            } else {
                let prize = parseInt(accumulateGame.Accumulate);
                this.lbTotalPrize.tweenValueto(prize);
            }
        },
        //Cap nhat diem tich luy
        activeMovePice: function (antiquity) {
            antiquity = parseInt(antiquity);
            if (this.currentAntiquity < antiquity) {
                this.currentAntiquity = antiquity;
                this.movePice(antiquity - 1);
            }
            //Reset currentProgressVal
            if (antiquity == this.maxAntiquity) {
                this.currentAntiquity = 0;
            }

        },
        //Cap nhat thoi gian con lai
        updateTime: function (time) {
            time = parseInt(time);
            let strTime = cc.Tool.getInstance().convertSecondToTime(time);
            this.lbTime.string = strTime;

            this.scheduleTime = setInterval(function () {
                let strTime = cc.Tool.getInstance().convertSecondToTime(time--);
                this.lbTime.string = strTime;
                if (time === 0) {
                    this.startUpdateTime = false;
                    clearInterval(this.scheduleTime);
                }
            }.bind(this), 1000);
        },
        onDestroy: function () {
            this.clearTimer();
        },
        //Di chuyen tich luy den vi tri dong tien tren balance
        moveTichLuy: function () {
            //An het cac manh ghp
            this.listPiceNV.map(node => {
                node.active = false;
            }, this);
            this.activeNodeNVWin(true);
            try {
                setTimeout(function () {
                    this.lbTotalPrize.setValue(0);
                    this.activeNodeNVWin(false);
                    this.listPiceNV.map(node => {
                        node.active = false;
                    }, this);
                }.bind(this), 2000)

            } catch (e) {

            }

        },
        getRandomPos: function () {
            let arrayMoveBy = [
                cc.v2(-100, 60),
                cc.v2(100, 60), cc.v2(20, 30),
                cc.v2(50, 20), cc.v2(-80, 50),
                cc.v2(-50, 40), cc.v2(-90, 60),
                cc.v2(50, 40), cc.v2(90, 60),
                cc.v2(-40, 0), cc.v2(-30, 30),
                cc.v2(40, 0), cc.v2(30, 30),
                cc.v2(-10, 10)];
            return arrayMoveBy[Math.floor(Math.random() * arrayMoveBy.length)];
        },
        //Di chuyen manh ghep den bang ghep
        movePice: function (indexPice) {
            this.nodePice.stopAllActions();
            cc.AudioController.getInstance().playSound(cc.AudioTypes.MINI_GAME_ACTIVE);
            this.nodePice.position = this.rootPositionPiceNV;
            this.nodePice.setScale(cc.v2(1, 1));
            this.particleWin.resetSystem();
            let sprite = this.nodePice.getChildByName("pice").getComponent(cc.Sprite);
            sprite.spriteFrame = this.sfPiceNV[indexPice];
            this.nodePice.active = true;

            var seq = cc.sequence(
                cc.moveBy(0.2, this.getRandomPos(), 0.3),//cc.v2(50, 20)
                cc.moveBy(0.2, this.getRandomPos(), 0.3),
                cc.moveBy(0.2, this.getRandomPos(), 0.3),
                cc.moveBy(0.2, this.getRandomPos(), 0.3),
                cc.moveBy(0.2, cc.v2(-10, 30), 0.3),
            );

            let scaleUp = cc.scaleTo(0.3, 4, 4);
            let scaleDown = cc.scaleTo(0.5, 1.2, 1.2);
            let action = cc.moveTo(0.5, this.lstPositionPiceNV[indexPice]);
            action.easing(cc.easeSineInOut(0.3));
            let scale = cc.scaleTo(0.3, 0.65, 0.65);
            let callFunc = cc.callFunc(function () {

                //An cho truong hop manh ghep cuoi cung
                this.listPiceNV[indexPice].active = (this.currentAntiquity == 0) ? false : true;

                setTimeout(function () {
                    this.nodePice.active = false;
                }.bind(this), 300);
            }, this);
            this.nodePice.runAction(cc.sequence(scaleUp, scaleDown, seq, action, scale, callFunc));
        }
    })
}).call(this);