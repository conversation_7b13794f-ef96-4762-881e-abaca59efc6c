/*
 * Generated by BeChicken
 * on 11/6/2019
 * version v1.0
 */
(function () {
    cc.CBEffectView = cc.Class({
        extends: cc.EffectView,
        properties: {
            nodeReward: cc.Node,
            particleReward: cc.ParticleSystem,
            lbReward: cc.LabelIncrement,
            animationReward: cc.Animation
        },
        playEffect: function (effectType, totalWin, tweenTime) {
            this.nodeJackpot.active = false;
            this.nodeBigWin.active = false;
            this.nodeNormalWin.active = false;
            this.nodeReward.active = false;
            switch (effectType) {
                case cc.EffectType.JACKPOT:
                    this.nodeJackpot.active = true;
                    this.animationJackpot.play('openWinFx');
                    this.particleJackpot.resetSystem();
                    this.lbiTotalWins[0].setValue(0);
                    this.lbiTotalWins[0].tweenValueto(totalWin, tweenTime);
                    break;
                case cc.EffectType.BIG_WIN:
                    this.nodeBigWin.active = true;
                    this.animationBigWin.play('openWinFx');
                    this.particleBigWin.resetSystem();
                    this.lbiTotalWins[1].setValue(0);
                    this.lbiTotalWins[1].tweenValueto(totalWin, tweenTime);
                    break;
                case cc.EffectType.NORMAL_WIN:
                    this.nodeNormalWin.active = true;
                    this.animationWin.play('openWinFx');
                    this.particleWin.resetSystem();
                    this.lbiTotalWins[2].setValue(0);
                    this.lbiTotalWins[2].tweenValueto(totalWin, tweenTime);
                    break;
                case cc.EffectType.SUPER_WIN:
                    this.nodeReward.active = true;
                    this.animationReward.play('openWinFx');
                    this.particleReward.resetSystem();
                    this.lbReward.setValue(0);
                    this.lbReward.tweenValueto(totalWin, tweenTime);
                    break;
            }
            this.nodeEffect.active = true;
        },

    })
}).call(this);