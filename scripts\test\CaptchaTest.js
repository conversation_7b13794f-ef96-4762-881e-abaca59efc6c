/**
 * Test script for Captcha functionality
 */

(function () {
    cc.CaptchaTest = cc.Class({
        "extends": cc.Component,
        properties: {
            btnTestPortalCaptcha: cc.Button,
            btnTestVQMMCaptcha: cc.Button,
            imageUrlCaptcha: cc.ImageUrl,
            lbResult: cc.Label,
        },

        onLoad: function () {
            console.log('CaptchaTest: onLoad');
        },

        testPortalCaptcha: function () {
            console.log('CaptchaTest: Testing Portal Captcha');
            this.lbResult.string = 'Testing Portal Captcha...';
            
            var getCaptchaCommand = new cc.GetCaptchaCommand;
            getCaptchaCommand.execute(this);
        },

        testVQMMCaptcha: function () {
            console.log('CaptchaTest: Testing VQMM Captcha');
            this.lbResult.string = 'Testing VQMM Captcha...';
            
            var vqmmGetCaptchaCommand = new cc.VQMMGetCaptchaCommand;
            vqmmGetCaptchaCommand.execute(this);
        },

        onGetCaptchaResponse: function (response) {
            console.log('CaptchaTest: Portal Captcha Response', response);
            this.lbResult.string = 'Portal Captcha Success!';
            
            if (response && response.length >= 2 && response[1]) {
                var base64Data = cc.Tool.getInstance().removeStr(response[1], '\r\n');
                this.imageUrlCaptcha.get('data:image/png;base64,' + base64Data);
            }
        },

        onGetCaptchaError: function (errorMessage) {
            console.error('CaptchaTest: Portal Captcha Error:', errorMessage);
            this.lbResult.string = 'Portal Captcha Error: ' + errorMessage;
        },

        oVQMMGetCaptchaResponse: function (response) {
            console.log('CaptchaTest: VQMM Captcha Response', response);
            this.lbResult.string = 'VQMM Captcha Success!';
            
            if (response && response.length >= 2 && response[1]) {
                var base64Data = cc.Tool.getInstance().removeStr(response[1], '\r\n');
                this.imageUrlCaptcha.get('data:image/png;base64,' + base64Data);
            }
        },

        lbError: {
            string: ''
        }
    });

}).call(this);
