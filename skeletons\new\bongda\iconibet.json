{"skeleton": {"hash": "DTnMUgJHbY4DBBWLXlAe+VUeiQo", "spine": "3.6.53", "width": 352.51, "height": 447.62, "images": "./images/"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "rotation": -40.61, "x": -67.42, "y": -139.49}, {"name": "nv_all", "parent": "root", "x": 7.1, "y": 14.8, "scaleX": 1.15, "scaleY": 1.15}, {"name": "bong", "parent": "nv_all", "x": 10.38, "y": -54.28, "scaleX": 0.93, "scaleY": 0.93}, {"name": "nv2", "parent": "nv_all", "x": -39.68, "y": 31.03}, {"name": "nv3", "parent": "nv2", "length": 67.53, "rotation": 73.78, "x": 0.39, "y": 0.79}, {"name": "hand_nv2", "parent": "nv3", "length": 47.09, "rotation": -170.49, "x": 64.43, "y": 28.97}, {"name": "hand_nv3", "parent": "hand_nv2", "length": 43.45, "rotation": 102.42, "x": 47.09}, {"name": "text_all", "parent": "root", "x": 0.79, "y": -211.78, "scaleX": 1.04, "scaleY": 1.04}, {"name": "ibet", "parent": "text_all", "x": -12.45, "y": 43.86}, {"name": "nv1", "parent": "nv_all", "x": 22.42, "y": 21.2}, {"name": "leg_nv1", "parent": "nv1", "length": 82.25, "rotation": -86.99, "x": 14.93, "y": -7.47}, {"name": "nv4", "parent": "nv3", "length": 44.19, "rotation": 11.12, "x": 67.53}, {"name": "nv5", "parent": "nv3", "length": 37.17, "rotation": -93.56, "x": 79.14, "y": -21.59}, {"name": "nv8", "parent": "nv_all", "length": 26.03, "rotation": -118.89, "x": -86.55, "y": -87.66, "color": "ff3f00ff"}, {"name": "nv6", "parent": "nv2", "length": 77.1, "rotation": -113.43, "x": -13.36, "y": -8.65}, {"name": "nv7", "parent": "nv6", "length": 40.08, "rotation": 12.12, "x": 77.1}, {"name": "nv11", "parent": "nv_all", "length": 24.53, "rotation": -125.22, "x": -34.57, "y": -73.51, "color": "ff3f00ff"}, {"name": "nv9", "parent": "nv2", "length": 36.56, "rotation": -88.77, "x": 15.72, "y": -8.65}, {"name": "nv10", "parent": "nv9", "length": 59.66, "rotation": -12.25, "x": 37.34, "y": -0.02}, {"name": "nv12", "parent": "nv1", "length": 58.32, "rotation": 67.41, "x": 1.18, "y": 7.86}, {"name": "nv13", "parent": "nv12", "length": 44.66, "rotation": -5.78, "x": 58.32}, {"name": "nv14", "parent": "nv12", "length": 33.26, "rotation": -122.03, "x": 19.22, "y": -22.65}, {"name": "nv15", "parent": "nv14", "length": 24.06, "rotation": 2.99, "x": 33.26}, {"name": "nv18", "parent": "nv_all", "length": 28.01, "rotation": -55.86, "x": 65.65, "y": -94.73, "color": "ff3f00ff"}, {"name": "nv16", "parent": "nv1", "length": 58.93, "rotation": -70.11, "x": -17.29, "y": -20.04}, {"name": "nv17", "parent": "nv16", "length": 57.53, "rotation": 24.28, "x": 58.93}, {"name": "text", "parent": "text_all", "x": -5.49, "y": 30.52}], "slots": [{"name": "BG", "bone": "root", "attachment": "BG2"}, {"name": "nv2", "bone": "nv2", "attachment": "nv2"}, {"name": "nv1", "bone": "nv1", "attachment": "nv1"}, {"name": "hand_nv2", "bone": "hand_nv2", "attachment": "hand_nv2"}, {"name": "leg_nv1", "bone": "leg_nv1", "attachment": "leg_nv1"}, {"name": "bong", "bone": "bong", "attachment": "bong"}, {"name": "bong_lua", "bone": "root", "attachment": "bong_lua"}, {"name": "fireball/fireball_00000", "bone": "bone", "attachment": "fireball/fireball_00022", "blend": "additive"}, {"name": "ibet", "bone": "ibet", "attachment": "ibet"}, {"name": "ibet2", "bone": "ibet", "color": "ffffff00", "attachment": "ibet", "blend": "additive"}, {"name": "khungtext", "bone": "text_all", "attachment": "khungtext"}, {"name": "text", "bone": "text", "attachment": "text"}, {"name": "text2", "bone": "text", "color": "ffffff00", "attachment": "text", "blend": "additive"}], "ik": [{"name": "nv8", "order": 0, "bones": ["nv6", "nv7"], "target": "nv8"}, {"name": "nv11", "order": 1, "bones": ["nv9", "nv10"], "target": "nv11", "bendPositive": false}, {"name": "nv18", "order": 2, "bones": ["nv16", "nv17"], "target": "nv18"}], "skins": {"default": {"BG": {"BG2": {"width": 220, "height": 392}}, "bong": {"bong": {"x": 1.62, "y": -0.72, "width": 49, "height": 49}}, "bong_lua": {"bong_lua": {"x": -36, "y": -95, "width": 163, "height": 193}}, "fireball/fireball_00000": {"fireball/fireball_00000": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}, "fireball/fireball_00001": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}, "fireball/fireball_00002": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}, "fireball/fireball_00003": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}, "fireball/fireball_00004": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}, "fireball/fireball_00005": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}, "fireball/fireball_00006": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}, "fireball/fireball_00007": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}, "fireball/fireball_00008": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}, "fireball/fireball_00009": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}, "fireball/fireball_00010": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}, "fireball/fireball_00011": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}, "fireball/fireball_00012": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}, "fireball/fireball_00013": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}, "fireball/fireball_00014": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}, "fireball/fireball_00015": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}, "fireball/fireball_00016": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}, "fireball/fireball_00017": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}, "fireball/fireball_00018": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}, "fireball/fireball_00019": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}, "fireball/fireball_00020": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}, "fireball/fireball_00021": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}, "fireball/fireball_00022": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}, "fireball/fireball_00023": {"x": -5.98, "y": 79.51, "width": 250, "height": 250}}, "hand_nv2": {"hand_nv2": {"type": "mesh", "uvs": [0.38731, 0.25629, 0.37828, 0.55109, 0.58958, 0.55769, 0.88937, 0.57749, 1, 0.69189, 1, 1, 0.89659, 1, 0.54624, 1, 0.28257, 1, 0, 1, 0, 0.81729, 0, 0.55769, 0, 0.23869, 0, 0, 0.39454, 0, 0.2013, 0.72489], "triangles": [6, 4, 5, 7, 2, 6, 2, 3, 6, 6, 3, 4, 7, 15, 1, 7, 1, 2, 7, 8, 15, 8, 10, 15, 8, 9, 10, 10, 11, 15, 15, 11, 1, 11, 12, 1, 1, 12, 0, 0, 12, 14, 12, 13, 14], "vertices": [2, 6, 13.15, 11.17, 0.99698, 7, 18.21, 30.74, 0.00302, 2, 6, 29.32, 12.46, 0.56744, 7, 15.99, 14.67, 0.43256, 2, 6, 28.03, 26.57, 0.02977, 7, 30.05, 12.9, 0.97023, 1, 7, 49.92, 9.82, 1, 1, 7, 56.67, 2.82, 1, 1, 7, 54.99, -14.04, 1, 1, 7, 48.09, -13.35, 1, 1, 7, 24.73, -11.02, 1, 1, 7, 7.16, -9.26, 1, 2, 6, 56.8, -9.82, 0.62286, 7, -11.68, -7.37, 0.37714, 2, 6, 46.82, -11, 0.94874, 7, -10.68, 2.63, 0.05126, 1, 6, 32.64, -12.66, 1, 1, 6, 15.22, -14.71, 1, 1, 6, 2.18, -16.25, 1, 1, 6, -0.91, 10, 1, 2, 6, 40.2, 1.8, 0.87549, 7, 3.24, 6.34, 0.12451], "hull": 15, "edges": [26, 28, 28, 0, 0, 2, 2, 4, 4, 6, 10, 8, 6, 8, 10, 12, 12, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26], "width": 67, "height": 55}}, "ibet": {"ibet": {"x": 18.66, "y": 33.78, "width": 191, "height": 70}}, "ibet2": {"ibet": {"x": 18.66, "y": 33.78, "width": 191, "height": 70}}, "khungtext": {"khungtext": {"x": 1.21, "y": 31.64, "width": 185, "height": 36}}, "leg_nv1": {"leg_nv1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [1, 11, 91.12, 14.38, 1, 1, 11, 89.17, -22.57, 1, 2, 10, -2.92, -0.7, 0.536, 11, -7.69, -17.47, 0.464, 2, 10, 34.08, -0.7, 0.872, 11, -5.75, 19.48, 0.128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 37, "height": 97}}, "nv1": {"nv1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0, 0.70875, 0.96174, 0.60712, 0.8928, 0.48428, 0.84126, 0.35253, 0.76001, 0.23325, 0.68925, 0.14958, 0.59316, 0.11753, 0.51977, 0.06591, 0.46561, 0.11575, 0.4062, 0.14323, 0.33237, 0.20096, 0.22567, 0.27985, 0.12463, 0.40004, 0.10992, 0.48947, 0.12511, 0.61329, 0.03313, 0.7801, 0.04241, 0.86264, 0.11245, 0.70787, 0.21878, 0.71303, 0.28629, 0.68207, 0.33101, 0.71992, 0.39687, 0.80933, 0.45084, 0.95722, 0.48797, 0.9641, 0.5867, 0.82653, 0.59345, 0.75946, 0.53945, 0.68207, 0.50485, 0.63048, 0.4635, 0.63048, 0.50738, 0.47571, 0.49894, 0.45335, 0.53185, 0.5187, 0.58839, 0.53338, 0.65638, 0.57096, 0.70801, 0.67053, 0.78453, 0.76535, 0.84874, 0.92717, 0.92318, 0.91705, 0.97281, 0.25239, 0.46127, 0.45142, 0.45507, 0.40696, 0.40418, 0.44219, 0.33965, 0.53378, 0.24746, 0.48212, 0.1979, 0.57048, 0.40526, 0.55878, 0.35647], "triangles": [33, 43, 31, 34, 33, 35, 35, 33, 32, 30, 37, 32, 32, 33, 31, 34, 43, 33, 3, 0, 27, 40, 39, 27, 41, 39, 40, 4, 39, 41, 40, 27, 0, 41, 40, 0, 4, 41, 0, 1, 4, 0, 36, 7, 34, 29, 38, 37, 6, 7, 36, 6, 36, 37, 5, 6, 37, 39, 38, 28, 38, 5, 37, 5, 38, 39, 4, 5, 39, 11, 1, 2, 1, 6, 5, 1, 8, 7, 6, 1, 7, 4, 1, 5, 37, 36, 32, 11, 12, 42, 10, 11, 42, 9, 10, 42, 36, 35, 32, 8, 9, 42, 8, 42, 34, 7, 8, 34, 36, 34, 35, 9, 1, 11, 9, 11, 10, 1, 9, 8, 25, 20, 26, 3, 27, 26, 29, 30, 25, 28, 25, 26, 28, 26, 27, 29, 25, 28, 29, 37, 30, 38, 29, 28, 27, 39, 28, 22, 23, 21, 25, 24, 22, 23, 22, 24, 48, 49, 23, 31, 48, 23, 43, 49, 48, 24, 31, 23, 43, 48, 31, 30, 31, 24, 30, 24, 25, 32, 31, 30, 18, 2, 3, 19, 18, 3, 16, 2, 18, 20, 19, 3, 15, 2, 16, 17, 16, 18, 47, 16, 17, 21, 18, 19, 21, 19, 20, 46, 17, 18, 18, 21, 46, 22, 21, 20, 47, 15, 16, 20, 25, 22, 26, 20, 3, 46, 47, 17, 14, 2, 15, 23, 46, 21, 13, 2, 14, 45, 47, 46, 49, 46, 23, 45, 46, 49, 47, 45, 15, 45, 14, 15, 44, 14, 45, 42, 13, 14, 13, 11, 2, 43, 45, 49, 44, 45, 43, 44, 42, 14, 12, 13, 42, 42, 44, 43, 12, 11, 13, 42, 43, 34], "vertices": [2, 21, -158.69, -151.04, 0, 24, 49.19, 12.38, 1, 2, 25, 110.49, -75.72, 0.06152, 26, 15.86, -90.23, 0.93848, 2, 20, 82.88, 90.91, 0.46176, 21, 15.28, 92.92, 0.53824, 2, 21, 78, -23.23, 0.99649, 22, -53.61, 101.37, 0.00351, 2, 26, 73.67, -15.95, 0.45902, 24, 19.1, -13.66, 0.54098, 1, 26, 51.02, -12.65, 1, 1, 26, 29.77, -14.62, 1, 3, 21, -142.5, -45.17, 0, 25, 65.61, -10, 0.23742, 26, 1.98, -11.87, 0.76258, 3, 21, -133.23, -22.27, 0, 25, 42.36, -18.34, 0.93246, 26, -22.65, -9.9, 0.06754, 4, 20, -56.86, 11.38, 0.00187, 21, -115.73, -0.27, 0, 25, 14.29, -19.93, 0.99745, 26, -48.88, 0.19, 0.00068, 4, 10, -35.4, -20.52, 0.00171, 20, -40.26, 22.87, 0.07552, 21, -100.37, 12.83, 0, 25, -5.71, -17.19, 0.92277, 3, 10, -42.22, -5.95, 0.01481, 20, -29.42, 34.76, 0.27402, 25, -21.73, -18.65, 0.71116, 4, 10, -35.64, 10.03, 0.03283, 20, -12.14, 34.83, 0.55789, 21, -73.6, 27.55, 0.00226, 25, -34.52, -7.02, 0.40702, 4, 10, -32.01, 29.89, 0.00207, 20, 7.59, 39.11, 0.8174, 21, -54.4, 33.8, 0.02659, 25, -51.96, 3.14, 0.15394, 3, 20, 37.02, 43.1, 0.79546, 21, -25.53, 40.73, 0.18227, 25, -76.36, 20.07, 0.02227, 3, 20, 66.11, 43.92, 0.47532, 21, 3.33, 44.48, 0.52417, 25, -98.38, 39.11, 0.00051, 2, 20, 75.86, 30.79, 0.28008, 21, 14.36, 32.4, 0.71992, 2, 20, 76.62, 18.33, 0.14887, 21, 16.37, 20.08, 0.85113, 2, 20, 105.75, 12.74, 0.01622, 21, 45.91, 17.45, 0.98378, 2, 21, 54.17, -3.11, 0.99851, 22, -61.11, 71.09, 0.00149, 2, 21, 42.77, -21.65, 0.94338, 22, -39.44, 69.07, 0.05662, 4, 20, 64.43, -17.97, 0.1435, 21, 7.9, -17.26, 0.68869, 22, -27.95, 35.85, 0.16761, 23, -59.26, 38.99, 0.0002, 4, 20, 47.93, -25.57, 0.27177, 21, -7.76, -26.49, 0.25049, 22, -12.75, 25.89, 0.47337, 23, -44.6, 28.25, 0.00437, 4, 20, 35.25, -26.42, 0.18624, 21, -20.29, -28.61, 0.08475, 22, -5.31, 15.59, 0.72521, 23, -37.7, 17.58, 0.0038, 3, 21, -33.5, -41.43, 0.02356, 22, 12.03, 9.41, 0.95702, 23, -20.71, 10.5, 0.01942, 3, 21, -40.67, -58.71, 0.01268, 22, 30.7, 10.63, 0.62211, 23, -2, 10.75, 0.36521, 3, 21, -40.18, -80.63, 0.01046, 22, 50.15, 20.76, 0.17207, 23, 17.95, 19.85, 0.81747, 5, 10, 76.34, -38.52, 4e-05, 23, 39.33, 4.08, 0.92467, 25, 49.23, 81.77, 0.00047, 26, 24.79, 78.52, 0.04069, 24, -45.49, 70.85, 0.03413, 6, 10, 58.18, -40.34, 0.01269, 21, -73.34, -78.93, 0, 23, 29.48, -11.29, 0.84594, 25, 44.76, 64.07, 0.0362, 26, 13.44, 64.23, 0.07742, 24, -54.18, 54.8, 0.02776, 6, 10, 49.33, -25.81, 0.03274, 22, 46.32, -8.54, 0.02929, 23, 12.6, -9.21, 0.87425, 25, 28.09, 60.69, 0.04247, 26, -3.15, 68, 0.01969, 24, -71.17, 55.62, 0.00156, 5, 10, 39.12, -16.51, 0.10021, 22, 32.82, -11.48, 0.43066, 23, -1.04, -11.44, 0.39988, 25, 15.86, 54.25, 0.06121, 26, -16.95, 67.16, 0.00804, 7, 10, 32.31, -5.38, 0.14147, 20, -0.27, -33.83, 0.0163, 21, -54.88, -39.55, 0, 22, 19.81, -10.59, 0.77893, 23, -13.99, -9.88, 0.02955, 25, 3.08, 51.63, 0.03289, 26, -29.67, 70.03, 0.00086, 1, 11, 10.62, 16.84, 1, 1, 11, 7.28, -3.45, 1, 4, 10, 8.93, -23.77, 0.17541, 25, 12.42, 23.39, 0.18488, 26, -32.77, 40.44, 0.0026, 11, 15.96, -6.86, 0.63711, 5, 10, 17.55, -38.98, 0.23415, 21, -91.45, -42.53, 0, 25, 29.66, 26.32, 0.68313, 26, -15.85, 36.03, 0.08174, 24, -78.11, 21.93, 0.00098, 5, 10, 19.49, -57.27, 0.0579, 21, -106.63, -52.93, 0, 25, 47.52, 21.93, 0.61318, 26, -1.38, 24.68, 0.32189, 24, -61.88, 13.27, 0.00702, 5, 10, 24.45, -71.16, 0.01605, 21, -116.49, -63.89, 0, 25, 62.26, 21.87, 0.22215, 26, 12.03, 18.56, 0.74176, 24, -47.6, 9.58, 0.02004, 5, 10, 37.59, -91.74, 0.00082, 21, -128.35, -85.24, 0, 25, 86.09, 27.23, 0.006, 26, 35.96, 13.65, 0.8809, 24, -23.19, 8.91, 0.11228, 3, 21, -137.61, -104.46, 0, 26, 57.07, 10.59, 0.23041, 24, -1.87, 9.58, 0.76959, 2, 21, -145.08, -132.77, 0, 24, 26.69, 16.02, 1, 2, 21, -157.46, -137.94, 0, 24, 36.99, 7.42, 1, 3, 10, -17.6, -4.78, 0.30076, 20, -18.89, 12.48, 0.19252, 25, -14.45, 4.9, 0.50672, 5, 10, 8.67, -3.12, 0.6436, 20, -7.26, -11.13, 0.13234, 22, 4.27, -28.55, 0.15977, 23, -30.44, -27, 0.00709, 25, -7.09, 30.17, 0.05719, 4, 10, 2.8, 10.57, 0.00257, 20, 3.13, -0.46, 0.99329, 21, -54.86, -6.01, 0, 22, -10.29, -25.41, 0.00414, 2, 20, 20.94, 1.92, 0.99934, 25, -36.7, 39.59, 0.00066, 1, 20, 48.48, 0.28, 1, 2, 20, 58.17, 11.7, 0.56595, 21, -1.32, 11.63, 0.43405, 4, 10, 24.39, 10.28, 0.07725, 20, 11.15, -20.5, 0.16077, 22, 2.45, -7.98, 0.76087, 25, -14.34, 49.51, 0.00111, 4, 10, 22.84, 23.41, 0.00236, 20, 22.68, -14.03, 0.42062, 21, -34.05, -17.55, 0.00088, 22, -9.15, -1.64, 0.57614], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 8, 22, 84, 84, 86, 86, 62, 34, 94, 94, 92, 92, 42, 62, 96, 96, 98], "width": 132, "height": 269}}, "nv2": {"nv2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0, 0.46592, 0.58633, 0.42643, 0.63655, 0.35072, 0.67242, 0.29806, 0.73391, 0.28654, 0.78413, 0.21084, 0.86817, 0.17628, 0.96452, 0.0331, 0.96554, 0.1088, 0.87637, 0.12691, 0.80668, 0.14995, 0.73391, 0.1878, 0.69292, 0.19603, 0.62117, 0.24704, 0.5361, 0.27173, 0.47256, 0.24375, 0.40902, 0.33097, 0.35572, 0.38364, 0.30038, 0.38857, 0.25221, 0.30464, 0.23376, 0.39351, 0.17329, 0.47251, 0.14459, 0.50218, 0.04013, 0.65096, 0.02402, 0.71565, 0.08545, 0.70271, 0.14991, 0.81591, 0.14991, 0.92749, 0.19422, 0.96145, 0.25062, 0.92426, 0.30097, 0.79974, 0.29594, 0.80135, 0.24256, 0.76416, 0.24155, 0.70433, 0.2909, 0.66875, 0.34529, 0.65743, 0.38859, 0.68977, 0.45003, 0.71079, 0.5165, 0.6833, 0.56987, 0.64773, 0.60915, 0.63155, 0.6867, 0.59921, 0.7572, 0.57377, 0.80631, 0.53574, 0.87281, 0.4348, 0.95481, 0.31631, 0.94205, 0.35288, 0.87373, 0.44504, 0.79264, 0.46552, 0.72796, 0.48307, 0.6651, 0.50063, 0.61864, 0.41333, 0.47208, 0.55343, 0.46251, 0.49021, 0.4242, 0.51242, 0.35823, 0.55172, 0.288, 0.57564, 0.23799, 0.5073, 0.19862, 0.65424, 0.2199], "triangles": [22, 23, 24, 19, 2, 23, 21, 23, 22, 41, 33, 0, 33, 32, 0, 41, 34, 33, 37, 34, 38, 0, 32, 3, 34, 40, 38, 34, 35, 33, 33, 31, 32, 33, 35, 31, 37, 36, 34, 34, 36, 35, 37, 62, 36, 32, 31, 3, 35, 30, 31, 35, 36, 30, 62, 29, 36, 36, 29, 30, 31, 30, 3, 29, 28, 30, 30, 28, 3, 61, 24, 25, 62, 61, 29, 25, 26, 61, 61, 27, 29, 61, 26, 27, 25, 24, 26, 29, 27, 28, 26, 24, 2, 28, 27, 3, 26, 2, 27, 27, 2, 3, 23, 2, 24, 55, 57, 56, 55, 20, 57, 57, 58, 56, 56, 58, 39, 39, 38, 40, 20, 21, 57, 57, 21, 58, 58, 21, 22, 58, 22, 24, 24, 61, 58, 19, 23, 20, 58, 59, 39, 39, 59, 38, 58, 61, 59, 20, 23, 21, 59, 60, 38, 60, 62, 38, 38, 62, 37, 59, 61, 60, 60, 61, 62, 41, 56, 40, 41, 42, 56, 41, 40, 34, 56, 39, 40, 46, 45, 0, 45, 44, 0, 0, 44, 42, 44, 43, 42, 42, 41, 0, 47, 51, 46, 51, 52, 46, 46, 52, 45, 51, 6, 52, 52, 53, 45, 45, 53, 44, 52, 5, 53, 53, 54, 44, 44, 54, 43, 53, 5, 54, 43, 54, 56, 42, 43, 56, 48, 0, 1, 10, 49, 48, 48, 47, 0, 47, 46, 0, 49, 50, 48, 48, 51, 47, 48, 50, 51, 49, 9, 50, 1, 11, 10, 1, 10, 48, 11, 1, 2, 11, 12, 10, 13, 12, 11, 10, 9, 49, 10, 12, 9, 14, 11, 2, 12, 13, 9, 11, 14, 13, 9, 8, 50, 8, 9, 14, 9, 13, 14, 8, 7, 51, 14, 15, 8, 50, 8, 51, 5, 4, 54, 7, 6, 51, 8, 15, 7, 15, 14, 16, 6, 7, 16, 7, 15, 16, 16, 14, 2, 16, 17, 6, 5, 6, 17, 5, 17, 55, 55, 17, 18, 5, 55, 4, 16, 19, 17, 16, 2, 19, 4, 55, 56, 17, 19, 18, 18, 20, 55, 18, 19, 20, 6, 5, 52, 56, 54, 4], "vertices": [2, 17, -9.37, 99.96, 0.47266, 19, 92.09, 95.01, 0.52734, 2, 14, 46.33, -9.78, 1, 17, 91.55, -43.01, 0, 3, 15, -96.36, -117.46, 0.11218, 5, 97.44, 115.01, 0.38047, 12, 51.52, 107.09, 0.50735, 2, 12, 67.09, -67.22, 0.25838, 13, 27.2, 69.01, 0.74162, 2, 15, 22.39, 22.86, 0.90413, 4, -1.29, -38.28, 0.09587, 2, 15, 38.09, 22.13, 0.99322, 4, -8.2, -52.4, 0.00678, 2, 15, 52.61, 13.98, 0.99202, 16, -21.01, 18.81, 0.00798, 2, 15, 72.13, 12.4, 0.69041, 16, -2.26, 13.17, 0.30959, 2, 15, 85.88, 16.16, 0.13086, 16, 11.98, 13.96, 0.86914, 2, 16, 37.73, 5.6, 0.82038, 14, -3.93, 4.63, 0.17962, 1, 14, 22.7, 12.41, 1, 2, 16, 70.66, -19.54, 0.00567, 14, 35.06, -9.39, 0.99433, 2, 16, 43.49, -11.46, 0.25309, 14, 6.72, -9.89, 0.74691, 3, 15, 102.8, -6.95, 0.0156, 16, 23.67, -12.19, 0.89952, 14, -11.96, -16.58, 0.08488, 3, 15, 82.43, -11.38, 0.35017, 16, 2.83, -12.25, 0.64704, 14, -31.81, -22.93, 0.00279, 3, 15, 69.23, -9.89, 0.88069, 16, -9.77, -8.01, 0.11923, 5, -80.34, 29.07, 8e-05, 4, 15, 50.16, -16.58, 0.97962, 16, -29.82, -10.56, 0.01262, 5, -60.58, 33.32, 0.00759, 12, -119.28, 57.4, 0.00017, 3, 15, 24.68, -17.89, 0.94774, 5, -35.13, 31.42, 0.04897, 12, -94.68, 50.63, 0.00329, 3, 15, 6.57, -21.03, 0.80626, 5, -16.78, 32.26, 0.18286, 12, -76.51, 47.91, 0.01088, 3, 15, -7.86, -32.62, 0.5647, 5, -1, 41.95, 0.39767, 12, -59.16, 54.38, 0.03762, 3, 15, -27.67, -24.57, 0.30363, 5, 17.64, 31.48, 0.64463, 12, -42.89, 40.51, 0.05174, 3, 15, -45.61, -22.3, 0.14335, 5, 35.15, 26.97, 0.75913, 12, -26.58, 32.71, 0.09752, 4, 15, -58.37, -26.89, 0.05647, 5, 48.39, 29.92, 0.38457, 12, -13.02, 33.05, 0.11896, 6, 15.66, -3.59, 0.44, 1, 6, 12.23, -18.78, 1, 3, 15, -79.06, -34.91, 0.06222, 5, 69.92, 35.29, 0.42393, 12, 9.15, 34.16, 0.51385, 3, 15, -91.96, -25.43, 0.02334, 5, 81.52, 24.27, 0.22848, 12, 18.41, 21.11, 0.74817, 3, 15, -120.96, -32.34, 0.01173, 5, 111.16, 27.48, 0.06534, 12, 48.11, 18.55, 0.92293, 2, 12, 54.93, -6.98, 0.91512, 13, -27.99, 41.99, 0.08488, 2, 12, 38.74, -19.79, 0.64321, 13, -11.5, 29.58, 0.35679, 2, 12, 20.5, -19.15, 0.42525, 13, -7.5, 11.77, 0.57475, 2, 12, 22.26, -38.88, 0.13929, 13, 11.14, 18.47, 0.86071, 2, 12, 11.6, -59.43, 0.0213, 13, 33.73, 13.36, 0.9787, 5, 19, -113.32, 48.16, 0.00437, 18, -63.18, 71.09, 0.00959, 5, 76.82, -66.22, 0.0017, 12, -3.66, -66.76, 0.00011, 13, 44.68, 0.45, 0.98424, 4, 19, -98.19, 44.48, 0.03789, 18, -49.18, 64.27, 0.08754, 5, 61.41, -63.92, 0.04107, 13, 43.34, -15.06, 0.8335, 4, 19, -95.41, 22.82, 0.02228, 18, -51.06, 42.52, 0.09248, 5, 56.68, -42.6, 0.1823, 13, 22.36, -21.1, 0.70295, 4, 19, -110.19, 20.23, 0.00251, 18, -66.05, 43.12, 0.01425, 5, 71.17, -38.68, 0.05695, 13, 17.55, -6.89, 0.9263, 4, 19, -109.22, 13.79, 0.00231, 18, -66.47, 36.62, 0.0167, 5, 69.62, -32.35, 0.10635, 13, 11.33, -8.83, 0.87464, 4, 19, -93.61, 6.16, 0.00948, 18, -52.83, 25.86, 0.07148, 5, 53.38, -26.17, 0.45012, 13, 6.17, -25.42, 0.46892, 4, 19, -77.42, 2.96, 0.02016, 18, -37.69, 19.3, 0.17519, 5, 36.97, -24.46, 0.57401, 13, 5.48, -41.91, 0.23064, 4, 19, -65.1, 3.35, 0.03307, 18, -25.56, 17.06, 0.32324, 5, 24.73, -25.96, 0.49524, 13, 7.73, -54.03, 0.14845, 4, 19, -49.23, 12.2, 0.10353, 18, -8.18, 22.35, 0.59081, 5, 9.73, -36.22, 0.18501, 13, 18.9, -68.36, 0.12065, 4, 19, -31.6, 19.38, 0.28883, 18, 10.57, 25.62, 0.60104, 5, -7.17, -44.97, 0.02917, 13, 28.68, -84.69, 0.08097, 4, 19, -15.96, 17.52, 0.5211, 18, 25.46, 20.49, 0.44111, 5, -22.92, -44.54, 0.00081, 13, 29.23, -100.43, 0.03698, 3, 19, -3.94, 13.52, 0.84606, 18, 36.36, 14.03, 0.13938, 13, 27.11, -112.93, 0.01456, 3, 17, -44.11, -3.49, 0.00085, 19, 17.99, 14.9, 0.99478, 13, 31.82, -134.39, 0.00437, 3, 17, -24.66, 3.31, 0.01411, 19, 38.52, 13.13, 0.98525, 13, 33.19, -154.95, 0.00063, 3, 17, -10.82, 7.63, 0.14539, 19, 52.92, 11.4, 0.85458, 13, 33.67, -169.44, 3e-05, 2, 17, 8.28, 12.97, 0.90025, 19, 72.53, 8.44, 0.09975, 2, 14, -1.55, 50.7, 0.08499, 17, 37.29, 11.83, 0.91501, 3, 16, 54.47, 27.77, 0.05488, 14, 5.33, 30.82, 0.37269, 17, 46.32, -7.18, 0.57243, 5, 15, 104.36, 36.82, 0.00225, 16, 34.39, 30.28, 0.22423, 14, -14.57, 27.15, 0.06319, 17, 26.95, -13.02, 0.69739, 19, 78.9, -22.93, 0.01295, 4, 15, 77.04, 42.56, 0.04827, 16, 8.88, 41.62, 0.08248, 17, -0.97, -12.98, 0.24831, 19, 53.45, -11.45, 0.62094, 4, 15, 58.94, 38.62, 0.12423, 16, -9.64, 41.57, 0.02494, 17, -17.88, -20.54, 0.01101, 19, 34.92, -11.4, 0.83981, 4, 15, 41.51, 34.42, 0.16421, 16, -27.57, 41.12, 0.00044, 19, 17, -11.76, 0.83504, 4, 1.71, -60.42, 0.0003, 3, 15, 28.31, 32.05, 0.1567, 19, 3.6, -11.24, 0.83667, 4, 4.79, -47.36, 0.00663, 5, 15, -3.4, 1.65, 0.73616, 18, -3.03, -26.15, 0.00839, 5, -9.73, 8.5, 0.08467, 4, -10.49, -6.18, 0.17078, 12, -74.17, 23.24, 2e-05, 4, 18, -5.19, -1.58, 0.72397, 5, -0.3, -14.29, 0.13529, 4, 14.03, -3.49, 0.13829, 13, -2.37, -79.73, 0.00244, 5, 19, -49.69, -23.47, 0, 18, -16.19, -12.41, 0.01124, 5, 6.95, -0.66, 0.9842, 4, 2.96, 7.27, 0.00435, 13, -16.42, -73.35, 0.00021, 3, 15, -39.65, 4.85, 0.00201, 5, 25.83, 0.79, 0.99763, 12, -40.77, 8.82, 0.00036, 4, 19, -89.31, -20.21, 3e-05, 18, -54.22, -0.83, 0.00048, 5, 46.7, -0.3, 0.99777, 13, -19.24, -33.69, 0.00172, 4, 19, -103.9, -18.79, 0, 18, -68.18, 3.66, 0.00013, 5, 61.37, -0.4, 0.99515, 13, -20.05, -19.05, 0.00471, 3, 15, -80.45, -13.81, 0.01503, 5, 68.65, 14.18, 0.44362, 12, 3.83, 13.7, 0.54135, 5, 19, -111.52, -6.26, 4e-05, 18, -72.97, 17.52, 0.00329, 5, 70.09, -12.18, 0.2823, 12, 0.16, -12.45, 0.26813, 13, -8.83, -9.61, 0.44624], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 8, 36, 110, 110, 112, 112, 80, 50, 122, 122, 120, 120, 124, 124, 58], "width": 175, "height": 281}}, "text": {"text": {"x": 4.7, "y": 1.62, "width": 163, "height": 27}}, "text2": {"text": {"x": 4.7, "y": 1.62, "width": 163, "height": 27}}}}, "animations": {"animation": {"slots": {"fireball/fireball_00000": {"attachment": [{"time": 0, "name": "fireball/fireball_00000"}, {"time": 0.0333, "name": "fireball/fireball_00001"}, {"time": 0.0667, "name": "fireball/fireball_00002"}, {"time": 0.1, "name": "fireball/fireball_00004"}, {"time": 0.1333, "name": "fireball/fireball_00005"}, {"time": 0.1667, "name": "fireball/fireball_00006"}, {"time": 0.2, "name": "fireball/fireball_00007"}, {"time": 0.2333, "name": "fireball/fireball_00008"}, {"time": 0.2667, "name": "fireball/fireball_00010"}, {"time": 0.3, "name": "fireball/fireball_00011"}, {"time": 0.3333, "name": "fireball/fireball_00012"}, {"time": 0.3667, "name": "fireball/fireball_00013"}, {"time": 0.4, "name": "fireball/fireball_00014"}, {"time": 0.4333, "name": "fireball/fireball_00016"}, {"time": 0.4667, "name": "fireball/fireball_00017"}, {"time": 0.5, "name": "fireball/fireball_00018"}, {"time": 0.5333, "name": "fireball/fireball_00019"}, {"time": 0.5667, "name": "fireball/fireball_00020"}, {"time": 0.6, "name": "fireball/fireball_00022"}, {"time": 0.6333, "name": "fireball/fireball_00023"}, {"time": 0.6667, "name": "fireball/fireball_00000"}, {"time": 0.7, "name": "fireball/fireball_00001"}, {"time": 0.7333, "name": "fireball/fireball_00002"}, {"time": 0.7667, "name": "fireball/fireball_00003"}, {"time": 0.8, "name": "fireball/fireball_00004"}, {"time": 0.8333, "name": "fireball/fireball_00006"}, {"time": 0.8667, "name": "fireball/fireball_00007"}, {"time": 0.9, "name": "fireball/fireball_00008"}, {"time": 0.9333, "name": "fireball/fireball_00009"}, {"time": 0.9667, "name": "fireball/fireball_00010"}, {"time": 1, "name": "fireball/fireball_00012"}, {"time": 1.0333, "name": "fireball/fireball_00013"}, {"time": 1.0667, "name": "fireball/fireball_00014"}, {"time": 1.1, "name": "fireball/fireball_00015"}, {"time": 1.1333, "name": "fireball/fireball_00016"}, {"time": 1.1667, "name": "fireball/fireball_00018"}, {"time": 1.2, "name": "fireball/fireball_00019"}, {"time": 1.2333, "name": "fireball/fireball_00020"}, {"time": 1.2667, "name": "fireball/fireball_00021"}, {"time": 1.3, "name": "fireball/fireball_00022"}, {"time": 1.3333, "name": "fireball/fireball_00000"}, {"time": 1.3667, "name": "fireball/fireball_00001"}, {"time": 1.4, "name": "fireball/fireball_00002"}, {"time": 1.4333, "name": "fireball/fireball_00003"}, {"time": 1.4667, "name": "fireball/fireball_00004"}, {"time": 1.5, "name": "fireball/fireball_00006"}, {"time": 1.5333, "name": "fireball/fireball_00007"}, {"time": 1.5667, "name": "fireball/fireball_00008"}, {"time": 1.6, "name": "fireball/fireball_00009"}, {"time": 1.6333, "name": "fireball/fireball_00010"}, {"time": 1.6667, "name": "fireball/fireball_00012"}, {"time": 1.7, "name": "fireball/fireball_00013"}, {"time": 1.7333, "name": "fireball/fireball_00014"}, {"time": 1.7667, "name": "fireball/fireball_00015"}, {"time": 1.8, "name": "fireball/fireball_00016"}, {"time": 1.8333, "name": "fireball/fireball_00018"}, {"time": 1.8667, "name": "fireball/fireball_00019"}, {"time": 1.9, "name": "fireball/fireball_00020"}, {"time": 1.9333, "name": "fireball/fireball_00021"}, {"time": 1.9667, "name": "fireball/fireball_00022"}, {"time": 2, "name": "fireball/fireball_00023"}, {"time": 2.0333, "name": "fireball/fireball_00001"}, {"time": 2.0667, "name": "fireball/fireball_00002"}, {"time": 2.1, "name": "fireball/fireball_00003"}, {"time": 2.1333, "name": "fireball/fireball_00004"}, {"time": 2.1667, "name": "fireball/fireball_00005"}, {"time": 2.2, "name": "fireball/fireball_00006"}, {"time": 2.2333, "name": "fireball/fireball_00008"}, {"time": 2.2667, "name": "fireball/fireball_00009"}, {"time": 2.3, "name": "fireball/fireball_00010"}, {"time": 2.3333, "name": "fireball/fireball_00011"}, {"time": 2.3667, "name": "fireball/fireball_00012"}, {"time": 2.4, "name": "fireball/fireball_00014"}, {"time": 2.4333, "name": "fireball/fireball_00015"}, {"time": 2.4667, "name": "fireball/fireball_00016"}, {"time": 2.5, "name": "fireball/fireball_00017"}, {"time": 2.5333, "name": "fireball/fireball_00018"}, {"time": 2.5667, "name": "fireball/fireball_00020"}, {"time": 2.6, "name": "fireball/fireball_00021"}, {"time": 2.6333, "name": "fireball/fireball_00022"}, {"time": 2.6667, "name": "fireball/fireball_00023"}]}, "ibet2": {"color": [{"time": 0.4, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "color": "ffffff97", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "color": "ffffff97", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "color": "ffffff97", "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "color": "ffffff97", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "color": "ffffff97", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "color": "ffffff97", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "color": "ffffff97", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9333, "color": "ffffff97", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.0333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1333, "color": "ffffff97", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3667, "color": "ffffff97", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.4667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5667, "color": "ffffff97", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "color": "ffffff00"}]}, "text2": {"color": [{"time": 0, "color": "ffffff7f", "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "color": "ffffffff", "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2.6667, "color": "ffffff7f"}]}}, "bones": {"ibet": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 0, "curve": [0.168, 0.43, 0.732, 1]}, {"time": 0.2, "x": 1, "y": 1.1, "curve": [0.3, 0, 0.8, 0.69]}, {"time": 0.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.2333, "x": 1, "y": 1, "curve": [0.168, 0.43, 0.75, 1]}, {"time": 2.3667, "x": 1, "y": 1.1, "curve": [0.3, 0, 0.8, 0.69]}, {"time": 2.4667, "x": 1, "y": 0}]}, "nv2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 7.15, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 7.15, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "nv3": {"rotate": [{"time": 0, "angle": -0.08, "curve": [0.348, 0.66, 0.682, 1]}, {"time": 0.0333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -4.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.0333, "angle": -4.55, "curve": [0.247, 0, 0.729, 0.91]}, {"time": 2.6667, "angle": -0.08}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 1.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 1.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "nv4": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 5.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 5.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "hand_nv2": {"rotate": [{"time": 0, "angle": 0.44, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 0.0667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 10.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.0667, "angle": 10.45, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 2.6667, "angle": 0.44}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "hand_nv3": {"rotate": [{"time": 0, "angle": -0.32, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 0.0667, "angle": 0}, {"time": 0.7333, "angle": -7.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.0667, "angle": -7.58, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 2.6667, "angle": -0.32}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "nv5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "nv6": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "nv7": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "nv8": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 0.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "nv9": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "nv10": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "nv11": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 12.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 12.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 20.74, "y": -0.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 7.31, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 20.74, "y": -0.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "nv1": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 7.51, "y": 2.51, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "x": 2.2, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 5.41, "y": 1.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "nv12": {"rotate": [{"time": 0, "angle": -0.08, "curve": [0.36, 0.64, 0.695, 1]}, {"time": 0.0667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -1.94, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.0667, "angle": -1.94, "curve": [0.245, 0, 0.711, 0.83]}, {"time": 2.6667, "angle": -0.08}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.6667, "x": 2.46, "y": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2, "x": 2.46, "y": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "nv13": {"rotate": [{"time": 0, "angle": 4.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -2.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 4.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -2.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "angle": 4.97}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "nv14": {"rotate": [{"time": 0, "angle": 0.47, "curve": [0.369, 0.63, 0.706, 1]}, {"time": 0.1, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 5.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1, "angle": 5.72, "curve": [0.244, 0, 0.694, 0.77]}, {"time": 2.6667, "angle": 0.47}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "nv15": {"rotate": [{"time": 0, "angle": 1.78, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": 9.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1667, "angle": 9.66, "curve": [0.242, 0, 0.667, 0.67]}, {"time": 2.6667, "angle": 1.78}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}]}, "nv16": {"rotate": [{"time": 0, "angle": -0.64}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "nv17": {"rotate": [{"time": 0, "angle": 2.05}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "nv18": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "leg_nv1": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 17.06, "curve": [0.207, 0.64, 0.607, 1]}, {"time": 1.3333, "angle": -22.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 4.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "text": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "x": 1.1, "y": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 1, "y": 1}]}, "bong": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 5.35, "y": 0, "curve": [0.25, 0.46, 0.621, 1]}, {"time": 1.3333, "x": -41.48, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 5.35, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}}}}}