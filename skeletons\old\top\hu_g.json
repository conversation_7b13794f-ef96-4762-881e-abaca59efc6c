{"skeleton": {"hash": "Cu/74GG5qV9B+NaxnrKi6dJ2Qbs", "spine": "3.6.53", "width": 197.73, "height": 126.03}, "bones": [{"name": "root"}, {"name": "b", "parent": "root", "length": 41.93, "rotation": -157.71, "x": -12.42, "y": 6.14}, {"name": "chest", "parent": "root", "length": 25.62, "rotation": 83.99, "x": -7.59, "y": -5.98}, {"name": "g", "parent": "root", "x": -36.3, "y": 38.91}, {"name": "g1", "parent": "g", "x": 123.08, "y": 28.13}, {"name": "g2", "parent": "g", "rotation": -52.51, "x": 107.23, "y": -62.53, "scaleX": 0.823, "scaleY": 0.823}, {"name": "g3", "parent": "g", "rotation": -171.88, "x": -53.81, "y": -1.35, "scaleX": 0.655, "scaleY": -0.684}, {"name": "g4", "parent": "g", "rotation": 166.97, "x": -13.87, "y": 37, "scaleX": 0.511, "scaleY": -0.533}, {"name": "g5", "parent": "g", "rotation": -124.74, "x": -24.96, "y": -48.59, "scaleX": 0.422, "scaleY": -0.44}, {"name": "g6", "parent": "g", "rotation": -107.9, "x": 8.01, "y": -57.14, "scaleX": 0.262, "scaleY": -0.274}, {"name": "g7", "parent": "g", "rotation": -16.1, "x": 90.11, "y": -21.64, "scaleX": 0.332, "scaleY": 0.332}, {"name": "star1", "parent": "chest", "x": 54.18, "y": -2.31}, {"name": "und", "parent": "root", "length": 30.08, "rotation": 149.8, "x": -11.64, "y": -5.89}], "slots": [{"name": "und", "bone": "und", "attachment": "und"}, {"name": "chest", "bone": "chest", "attachment": "chest"}, {"name": "b", "bone": "b", "attachment": "b"}, {"name": "g1", "bone": "g1", "attachment": "g1"}, {"name": "g2", "bone": "g2", "attachment": "g1"}, {"name": "g7", "bone": "g7", "attachment": "g1"}, {"name": "g3", "bone": "g3", "attachment": "g1"}, {"name": "g4", "bone": "g4", "attachment": "g1"}, {"name": "g5", "bone": "g5", "attachment": "g1"}, {"name": "g6", "bone": "g6", "attachment": "g1"}, {"name": "star1", "bone": "star1", "attachment": "star1"}, {"name": "star", "bone": "star1", "color": "ffffff00", "attachment": "star"}], "skins": {"default": {"b": {"b": {"x": -12.34, "y": 8.65, "rotation": 157.71, "width": 123, "height": 65}}, "chest": {"chest": {"x": 35.64, "y": -13.94, "rotation": -83.99, "width": 94, "height": 86}}, "g1": {"g1": {"x": -7.92, "y": -7.29, "width": 40, "height": 34}}, "g2": {"g1": {"x": -7.92, "y": -7.29, "width": 40, "height": 34}}, "g3": {"g1": {"x": -7.92, "y": -7.29, "width": 40, "height": 34}}, "g4": {"g1": {"x": -7.92, "y": -7.29, "width": 40, "height": 34}}, "g5": {"g1": {"x": -7.92, "y": -7.29, "width": 40, "height": 34}}, "g6": {"g1": {"x": -7.92, "y": -7.29, "width": 40, "height": 34}}, "g7": {"g1": {"x": -7.92, "y": -7.29, "width": 40, "height": 34}}, "star": {"star": {"x": 1.04, "y": -0.38, "rotation": -83.99, "width": 43, "height": 40}}, "star1": {"star1": {"x": 1.04, "y": -0.38, "rotation": -83.99, "width": 43, "height": 40}}, "und": {"und": {"x": -7.1, "y": -10.95, "rotation": -149.8, "width": 107, "height": 45}}}}, "animations": {"animation": {"slots": {"g1": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 2.0333, "color": "ffffff00"}, {"time": 2.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5667, "color": "ffffffff"}, {"time": 3.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0667, "color": "ffffff00"}]}, "g2": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 2.0333, "color": "ffffff00"}, {"time": 2.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5667, "color": "ffffffff"}, {"time": 3.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0667, "color": "ffffff00"}]}, "g3": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 2.0333, "color": "ffffff00"}, {"time": 2.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5667, "color": "ffffffff"}, {"time": 3.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0667, "color": "ffffff00"}]}, "g4": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 2.0333, "color": "ffffff00"}, {"time": 2.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5667, "color": "ffffffff"}, {"time": 3.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0667, "color": "ffffff00"}]}, "g5": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 2.0333, "color": "ffffff00"}, {"time": 2.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5667, "color": "ffffffff"}, {"time": 3.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0667, "color": "ffffff00"}]}, "g6": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 2.0333, "color": "ffffff00"}, {"time": 2.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5667, "color": "ffffffff"}, {"time": 3.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0667, "color": "ffffff00"}]}, "g7": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 2.0333, "color": "ffffff00"}, {"time": 2.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5667, "color": "ffffffff"}, {"time": 3.0333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0667, "color": "ffffff00"}]}, "star": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 3, "color": "ffffffff"}, {"time": 3.0667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "star"}, {"time": 1.5, "name": "star"}, {"time": 3, "name": "star"}, {"time": 3.0667, "name": "star"}]}, "star1": {"attachment": [{"time": 0, "name": "star1"}, {"time": 1.5, "name": "star1"}, {"time": 3, "name": "star1"}, {"time": 3.0667, "name": "star1"}]}, "und": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 1.5, "color": "ffffffff"}, {"time": 2.2667, "color": "ffffff00"}, {"time": 3.0667, "color": "ffffffff"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "g": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "g1": {"rotate": [{"time": 0, "angle": 16.83}, {"time": 0.5, "angle": 0}, {"time": 1, "angle": -86.71}, {"time": 1.0333, "angle": 0, "curve": "stepped"}, {"time": 1.5333, "angle": 0}, {"time": 2.0333, "angle": -86.71}, {"time": 2.0667, "angle": 0, "curve": "stepped"}, {"time": 2.5667, "angle": 0}, {"time": 3.0333, "angle": -86.71}, {"time": 3.0667, "angle": 0}], "translate": [{"time": 0, "x": -84.24, "y": -21.52}, {"time": 0.5, "x": 0, "y": 0}, {"time": 1, "x": -24.37, "y": -30.15}, {"time": 1.0333, "x": -84.24, "y": -21.52}, {"time": 1.5333, "x": 0, "y": 0}, {"time": 2.0333, "x": -24.37, "y": -30.15}, {"time": 2.0667, "x": -84.24, "y": -21.52}, {"time": 2.5667, "x": 0, "y": 0}, {"time": 3.0333, "x": -24.37, "y": -30.15, "curve": "stepped"}, {"time": 3.0667, "x": -24.37, "y": -30.15}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.5667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0667, "x": 1, "y": 1}]}, "g2": {"rotate": [{"time": 0, "angle": 16.83}, {"time": 0.5, "angle": 0}, {"time": 1, "angle": -86.71}, {"time": 1.0333, "angle": 0, "curve": "stepped"}, {"time": 1.5333, "angle": 0}, {"time": 2.0333, "angle": -86.71}, {"time": 2.0667, "angle": 0, "curve": "stepped"}, {"time": 2.5667, "angle": 0}, {"time": 3.0333, "angle": -86.71}, {"time": 3.0667, "angle": 0}], "translate": [{"time": 0, "x": -58.7, "y": 65.28}, {"time": 0.5, "x": 0, "y": 0}, {"time": 1, "x": -31.91, "y": 0.81}, {"time": 1.0333, "x": -58.7, "y": 65.28}, {"time": 1.5333, "x": 0, "y": 0}, {"time": 2.0333, "x": -31.91, "y": 0.81}, {"time": 2.0667, "x": -58.7, "y": 65.28}, {"time": 2.5667, "x": 0, "y": 0}, {"time": 3.0333, "x": -31.91, "y": 0.81, "curve": "stepped"}, {"time": 3.0667, "x": -31.91, "y": 0.81}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.5667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0667, "x": 1, "y": 1}]}, "g3": {"rotate": [{"time": 0, "angle": 16.83}, {"time": 0.5, "angle": 0}, {"time": 1, "angle": 121.26}, {"time": 1.0333, "angle": 0, "curve": "stepped"}, {"time": 1.5333, "angle": 0}, {"time": 2.0333, "angle": 121.26}, {"time": 2.0667, "angle": 0, "curve": "stepped"}, {"time": 2.5667, "angle": 0}, {"time": 3.0333, "angle": 121.26}, {"time": 3.0667, "angle": 0}], "translate": [{"time": 0, "x": 89.01, "y": 0.72}, {"time": 0.5, "x": 0, "y": 0}, {"time": 1, "x": 18.72, "y": -18.16}, {"time": 1.0333, "x": 89.01, "y": 0.72}, {"time": 1.5333, "x": 0, "y": 0}, {"time": 2.0333, "x": 18.72, "y": -18.16}, {"time": 2.0667, "x": 89.01, "y": 0.72}, {"time": 2.5667, "x": 0, "y": 0}, {"time": 3.0333, "x": 18.72, "y": -18.16, "curve": "stepped"}, {"time": 3.0667, "x": 18.72, "y": -18.16}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.5667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0667, "x": 1, "y": 1}]}, "g4": {"rotate": [{"time": 0, "angle": -343.17}, {"time": 0.5, "angle": 0}, {"time": 1, "angle": -238.74}, {"time": 1.0333, "angle": 0, "curve": "stepped"}, {"time": 1.5333, "angle": 0}, {"time": 2.0333, "angle": -238.74}, {"time": 2.0667, "angle": 0, "curve": "stepped"}, {"time": 2.5667, "angle": 0}, {"time": 3.0333, "angle": -238.74}, {"time": 3.0667, "angle": 0}], "translate": [{"time": 0, "x": 64.97, "y": -14.44}, {"time": 0.5, "x": 0, "y": 0}, {"time": 1, "x": 8.51, "y": -18.47}, {"time": 1.0333, "x": 64.97, "y": -14.44}, {"time": 1.5333, "x": 0, "y": 0}, {"time": 2.0333, "x": 8.51, "y": -18.47}, {"time": 2.0667, "x": 64.97, "y": -14.44}, {"time": 2.5667, "x": 0, "y": 0}, {"time": 3.0333, "x": 8.51, "y": -18.47, "curve": "stepped"}, {"time": 3.0667, "x": 8.51, "y": -18.47}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.5667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0667, "x": 1, "y": 1}]}, "g5": {"rotate": [{"time": 0, "angle": 16.83}, {"time": 0.5, "angle": 0}, {"time": 1, "angle": 121.26}, {"time": 1.0333, "angle": 0, "curve": "stepped"}, {"time": 1.5333, "angle": 0}, {"time": 2.0333, "angle": 121.26}, {"time": 2.0667, "angle": 0, "curve": "stepped"}, {"time": 2.5667, "angle": 0}, {"time": 3.0333, "angle": 121.26}, {"time": 3.0667, "angle": 0}], "translate": [{"time": 0, "x": 57.47, "y": 43.6}, {"time": 0.5, "x": 0, "y": 0}, {"time": 1, "x": 16.76, "y": 0.88}, {"time": 1.0333, "x": 57.47, "y": 43.6}, {"time": 1.5333, "x": 0, "y": 0}, {"time": 2.0333, "x": 16.76, "y": 0.88}, {"time": 2.0667, "x": 57.47, "y": 43.6}, {"time": 2.5667, "x": 0, "y": 0}, {"time": 3.0333, "x": 16.76, "y": 0.88, "curve": "stepped"}, {"time": 3.0667, "x": 16.76, "y": 0.88}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.5667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0667, "x": 1, "y": 1}]}, "g6": {"rotate": [{"time": 0, "angle": 16.83}, {"time": 0.5, "angle": 0}, {"time": 1, "angle": 121.26}, {"time": 1.0333, "angle": 0, "curve": "stepped"}, {"time": 1.5333, "angle": 0}, {"time": 2.0333, "angle": 121.26}, {"time": 2.0667, "angle": 0, "curve": "stepped"}, {"time": 2.5667, "angle": 0}, {"time": 3.0333, "angle": 121.26}, {"time": 3.0667, "angle": 0}], "translate": [{"time": 0, "x": 37.09, "y": 40.23}, {"time": 0.5, "x": 0, "y": 0}, {"time": 1, "x": 9.82, "y": 3.55}, {"time": 1.0333, "x": 37.09, "y": 40.23}, {"time": 1.5333, "x": 0, "y": 0}, {"time": 2.0333, "x": 9.82, "y": 3.55}, {"time": 2.0667, "x": 37.09, "y": 40.23}, {"time": 2.5667, "x": 0, "y": 0}, {"time": 3.0333, "x": 9.82, "y": 3.55, "curve": "stepped"}, {"time": 3.0667, "x": 9.82, "y": 3.55}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.5667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0667, "x": 1, "y": 1}]}, "g7": {"rotate": [{"time": 0, "angle": 16.83}, {"time": 0.5, "angle": 0}, {"time": 1, "angle": -86.71}, {"time": 1.0333, "angle": 0, "curve": "stepped"}, {"time": 1.5333, "angle": 0}, {"time": 2.0333, "angle": -86.71}, {"time": 2.0667, "angle": 0, "curve": "stepped"}, {"time": 2.5667, "angle": 0}, {"time": 3.0333, "angle": -86.71}, {"time": 3.0667, "angle": 0}], "translate": [{"time": 0, "x": -17.92, "y": 14.77}, {"time": 0.5, "x": 0, "y": 0}, {"time": 1, "x": -10.56, "y": -7.38}, {"time": 1.0333, "x": -17.92, "y": 14.77}, {"time": 1.5333, "x": 0, "y": 0}, {"time": 2.0333, "x": -10.56, "y": -7.38}, {"time": 2.0667, "x": -17.92, "y": 14.77}, {"time": 2.5667, "x": 0, "y": 0}, {"time": 3.0333, "x": -10.56, "y": -7.38, "curve": "stepped"}, {"time": 3.0667, "x": -10.56, "y": -7.38}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.5667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0667, "x": 1, "y": 1}]}, "chest": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.8333, "angle": 0, "curve": "stepped"}, {"time": 2.1667, "angle": 0, "curve": "stepped"}, {"time": 3.0667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 2.0333, "x": -0.32, "y": -3.02}, {"time": 2.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.1667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0667, "x": 1, "y": 1}]}, "star1": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.2667, "angle": -2.9}, {"time": 1.5, "angle": -26.1}, {"time": 2.1667, "angle": 14.16}, {"time": 2.7333, "angle": -12.11}, {"time": 3, "angle": 0, "curve": "stepped"}, {"time": 3.0667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.2, "x": 13.89, "y": 0}, {"time": 0.4333, "x": -3, "y": 0}, {"time": 0.6333, "x": 13.89, "y": 0}, {"time": 0.8333, "x": -3, "y": 0}, {"time": 1.0333, "x": 13.89, "y": 0}, {"time": 1.2667, "x": -3.39, "y": 0.13}, {"time": 1.5, "x": 17.58, "y": -4.59}, {"time": 1.8333, "x": -3, "y": 0}, {"time": 2.1667, "x": -5.12, "y": 13.9}, {"time": 2.5, "x": -3, "y": 0}, {"time": 2.7333, "x": -0.09, "y": -5.45}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0667, "x": 1, "y": 1}]}, "b": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.5, "x": 1, "y": 0.844}, {"time": 1, "x": 1.064, "y": 1.141}, {"time": 1.5, "x": 1, "y": 1}, {"time": 2, "x": 1, "y": 0.844}, {"time": 2.5, "x": 1.064, "y": 1.141}, {"time": 3, "x": 1, "y": 1}]}, "und": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0, "curve": "stepped"}, {"time": 3.0667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0667, "x": 1, "y": 1}]}}}}}