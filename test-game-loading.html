<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Test Game Loading</title>
    <meta name="viewport" content="width=device-width,user-scalable=no,initial-scale=1, minimum-scale=1,maximum-scale=1"/>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        #gameCanvas {
            border: 1px solid #ccc;
            margin: 10px 0;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 Test Game Loading</h1>
        <p>Kiểm tra game có thể load và chạy bình thường sau khi xóa anti-debug code</p>

        <div class="test-section info">
            <h3>📋 Test Status</h3>
            <div id="testStatus">Đang khởi tạo...</div>
        </div>

        <div class="test-section">
            <h3>🎯 Game Canvas</h3>
            <canvas id="gameCanvas" width="800" height="600"></canvas>
        </div>

        <div class="test-section">
            <h3>📝 Console Logs</h3>
            <div id="consoleLogs" class="log"></div>
        </div>

        <div class="test-section">
            <h3>🔧 Debug Info</h3>
            <div id="debugInfo"></div>
        </div>
    </div>

    <script>
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const logContainer = document.getElementById('consoleLogs');
        
        function addLog(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : 'black';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addLog(args.join(' '), 'error');
        };

        // Test status updates
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('testStatus');
            statusDiv.textContent = message;
            statusDiv.className = type;
        }

        // Debug info
        function updateDebugInfo() {
            const debugDiv = document.getElementById('debugInfo');
            debugDiv.innerHTML = `
                <p><strong>User Agent:</strong> ${navigator.userAgent}</p>
                <p><strong>Platform:</strong> ${navigator.platform}</p>
                <p><strong>URL:</strong> ${window.location.href}</p>
                <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
            `;
        }

        // Initialize test
        updateStatus('Bắt đầu test...', 'info');
        updateDebugInfo();
        
        console.log('=== Test Game Loading Started ===');
        console.log('Anti-debug code đã được xóa');
        console.log('Kiểm tra F12 có hoạt động không...');
        
        // Test F12 functionality
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F12') {
                console.log('F12 pressed - Developer Tools should open normally');
                updateStatus('F12 hoạt động bình thường - Developer Tools có thể mở', 'success');
            }
        });

        // Test basic functionality
        setTimeout(() => {
            console.log('Test completed - No anti-debug interference detected');
            updateStatus('✅ Test hoàn thành - Không có can thiệp anti-debug', 'success');
        }, 2000);
    </script>
</body>
</html>
